# Theme Toggle Implementation

This document explains the theme toggle functionality that has been implemented in your project.

## 🌟 Features

- **Automatic theme detection** - Uses system preference by default
- **Persistent storage** - Remembers user's choice in localStorage
- **Smooth transitions** - CSS transitions for seamless theme switching
- **Keyboard shortcut** - `Ctrl/Cmd + Shift + T` to toggle theme
- **HTMX compatibility** - Works with dynamic content loading
- **Accessibility** - Proper ARIA labels and semantic markup
- **Mobile support** - Updates meta theme-color for mobile browsers

## 🚀 How to Use

### For Users

1. **Click the theme toggle button** in the top navigation bar (moon/sun icon)
2. **Use keyboard shortcut**: `Ctrl + Shift + T` (or `Cmd + Shift + T` on Mac)
3. **System preference**: If no theme is set, it follows your system's dark/light mode

### For Developers

#### Files Added/Modified

1. **`src/assets/js/layout/themeToggle.js`** - Main theme management logic
2. **`src/Components/layout/header/themeToggle.php`** - Theme toggle button component
3. **`src/Views/head.php`** - Added theme initialization script and theme toggle JS
4. **`src/Views/layout.php`** - Integrated theme toggle button into layout
5. **`src/assets/styles/index.css`** - Added theme transition styles
6. **`src/Views/theme-demo.php`** - Demo page showcasing theme functionality
7. **`index.php`** - Added route for theme demo page

#### Theme Toggle Button Usage

The theme toggle button is automatically included in the main layout. To add it elsewhere:

```php
<?php include "/path/to/src/Components/layout/header/themeToggle.php"; ?>
```

#### JavaScript API

```javascript
// Access the theme manager
window.themeManager.toggleTheme();           // Toggle between light/dark
window.themeManager.setTheme('dark');        // Set specific theme
window.themeManager.getCurrentTheme();       // Get current theme
window.themeManager.refresh();               // Refresh theme state
```

#### Listen to Theme Changes

```javascript
window.addEventListener('themeChanged', (event) => {
    console.log('Theme changed to:', event.detail.theme);
    // Your custom logic here
});
```

## 🎨 Styling Guidelines

### Dark Mode Classes

The implementation uses Tailwind's dark mode classes. Examples:

```html
<!-- Background colors -->
<div class="bg-white dark:bg-gray-800">Content</div>

<!-- Text colors -->
<p class="text-gray-900 dark:text-white">Text</p>

<!-- Borders -->
<div class="border-gray-200 dark:border-gray-700">Content</div>

<!-- Buttons -->
<button class="bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600">
    Button
</button>
```

### Custom CSS

For custom components, use CSS custom properties or class-based approach:

```css
/* Using CSS custom properties */
:root {
    --bg-primary: #ffffff;
    --text-primary: #1f2937;
}

.dark {
    --bg-primary: #1f2937;
    --text-primary: #ffffff;
}

.my-component {
    background-color: var(--bg-primary);
    color: var(--text-primary);
}

/* Or using class-based approach */
.my-component {
    @apply bg-white text-gray-900 dark:bg-gray-800 dark:text-white;
}
```

## 🔧 Configuration

### Theme Initialization

The theme is initialized before page render to prevent FOUC (Flash of Unstyled Content):

```javascript
// In head.php - runs immediately
(function() {
    const theme = localStorage.getItem('theme') || 
                 (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
    if (theme === 'dark') {
        document.documentElement.classList.add('dark');
    }
})();
```

### Tailwind Configuration

Ensure your `tailwind.config.js` has dark mode enabled:

```javascript
module.exports = {
    darkMode: 'class', // Enable class-based dark mode
    // ... rest of your config
}
```

## 🧪 Testing

Visit `/theme-demo` to see the theme toggle in action with various UI components.

## 🐛 Troubleshooting

### Theme not persisting
- Check if localStorage is available in the browser
- Ensure the theme toggle script is loaded before other scripts

### HTMX content not updating theme
- The theme manager automatically refreshes after HTMX content swaps
- For manual refresh, call `window.themeManager.refresh()`

### Styles not applying
- Ensure Tailwind's dark mode is configured correctly
- Check that the `dark` class is being added to the `<html>` element
- Verify that your CSS includes the necessary dark mode variants

## 🎯 Demo

Visit `/theme-demo` in your application to see:
- Theme toggle functionality
- Various UI components in both themes
- Current theme display
- Usage instructions

## 📱 Mobile Considerations

The theme toggle automatically updates the meta theme-color for mobile browsers:
- Light theme: `#ffffff`
- Dark theme: `#1f2937`

This ensures the browser UI matches your app's theme on mobile devices.

## ♿ Accessibility

- Proper ARIA labels for screen readers
- Keyboard navigation support
- High contrast maintained in both themes
- Semantic HTML structure
- Focus indicators preserved

## 🔄 Future Enhancements

Potential improvements you could add:
- System theme change detection and auto-switching
- Multiple theme options (not just light/dark)
- Theme-specific animations
- Per-user theme preferences stored in database
- Theme scheduling (auto dark mode at night)

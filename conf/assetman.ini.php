;<?php /* DO NOT REMOVE THIS LINE 

;Konfiguracny subor.

;===========================================
; Logovanie neopravnenych pristupov do aplikacie
[Main]
Spolocnost = "Sympatia Financie, o.c.p., a.s."

[Unauthorized]

; Logovat (ano/nie).
DoLogging = true

; Kam logovat (meno suboru);
LogFile = "C:\inetpub\logs\Goldmann\auth_error.log"

;===========================================
; Nastavenia tykajuce sa databazy.

[Database]

; Databazovy pouzivatel.
;User = "sympatia"
User = "lukas"
; Heslo databazoveho pouzivatela. 
; Pri jeho zmene zmen aj heslo .bat suboru na backup!!!
Password = "xxxxxxxx"

; Pripojovaci retazec k Oracle servru.
Connection = "ORCL"

; Zapne/vypne debugovaci mod pripojenia k databaze (vypisuje kazde query, navratove 
; hodnoty atd.).
DebugMode = false

; Logovat (ano/nie).
DoLogging = true

; Kam logovat (meno suboru);
LogFile = "C:\inetpub\logs\Goldmann\ORA_error.log"

;===========================================
; Nastavenia tykajuce sa importu kurzov do ocenenia.

[Kurzy]

; Oddelovac zaznamov v CSV subore.
Delimiter = ";"

; Format datumu
DateFormat = "j.n.Y"

; Cislo stlpca, v ktorom je ulozena informacia o datume.
DatePosition = 3

; Cislo stlpca, v ktorom je ulozena informacia o ISIN-e daneho aktiva.
IsinPosition = 6

; Cislo stlpca, v ktorom je ulozena informacia o kurze aktiva.
KurzPosition = 23

;===========================================
; Monitoring rizika

[Monitoring]

; Zapina/vypina pocitanie s debetnymi polozkami (NAV/HM).
ExcludeDebets = true

;===========================================
; Zoznam jazykov a jazykovych suborov.

[Languages]

; Informacie su ulozene vo formate Jazyk = subor.
; Prvy par je predvoleny.
Slovensky = "conf/slovak.lang.php"
English = "conf/english.lang.php"
Deutsch = "conf/german.lang.php"

[Podielnik]
; Parameter Zmena urcuje, ktore agentury/org.jednotky maju moznost
; menit udaje podielnika priamo cez interface (agentury su od seba
; oddelene ciarkou.
Zmena = 9000
PodielnikID = podielnikid
GenerujPodielnikID = true
MinDlzkaPodielnikID = 1

;Urcuje, ci bude mozne robit upravy v rezime investovania
RezimInvestovania = true

; V pripade, ze je true, je spristupnena moznost pridavat spoludisponujucich
; priamo v konte podielnika
SpravaSpoludisponujucich = true

;Frekvencia v�pisov podielnika
VypisFrekvencia = false

;Zobrazit poistenie v menu
Poistenie = true

;Zobrazenie Vyhlasenia o ochrane udajov
VyhlasenieOOU = true

;===========================================
;Nastavenia tykajuce sa organizacnej struktury.

[Struktura]
; Trieda pre organizacnu strukturu typu IAM :
Class = org_unit
; Trieda pre organizacnu strukturu typu SPOROAM :
;Class = agentura

;===========================================
[Gridkarta]
Povolene = false
VazbaNaZiadost = false

;===========================================
;Nastavenia tykajuce sa emisii.

[Emisie]
;Relevatny datum pre matching
;IAM - kp.obratdatatimezauctovanie
;SLSP - ob.obratdatetime 
RelevantnyDatum = ob.obratdatetime

;Zoradovanie poloziek v zozname pripravenych emisii
; IAM - ziadostid, kp.pocet, po.rcico
; SLSP - ziadostid,kp.obratdatetimereal
Zoradovanie = ziadostid, kp.pocet, po.rcico

; Rezim vyhladavania kurzu :
; 1 - kurz typu SPORO
; 2 - kurz typu IAM
Rezim = 2

; Paramter "PevnePasmaPoplatkov" urcuje, akym sposobom sa pocita poplatok v pripade,
; ze sumarna hodnota doterajsich investicii a novej investicie meni pasmo poplatku
; (prechadza z vyssej sadzby poplatku do nizsej).
; true = Investicia sa rozdeli na casti, ktore patria do vyssej, resp. nizsej sadzby
;		 a potom sa z nich zaplati poplatok podla danej sadzby.
; false = Ak sumarna hodnota patri do nizsej sadzby, na celu novu investiciu sa
;		 vztahuje nizsi poplatok. 
RozdeleniePoplatku = true

[Redemacie]
; Rezim vyhladavania kurzu :
; 1 - kurz typu SPORO
; 2 - kurz typu IAM

Rezim = 2

; Spravanie sa voci relevantnej sume
; false - relevantne, resp. najblizsia vyssia suma
; true - presna suma, zbytok nad relevantnou sumou je hal. vyrovnanie
StrictRelevant = true

;Zobrazit symboly (ss, vs) ��tu 
UcetSymboly = false

[Presun]
; Urcuje, aka schema sa pouzije pri vypocte poplatkov : 
; false = x SKK < y%
; true = x SKK < y% < z SKK 
DetailnePoplatky = true


;===========================================
;Nastavenia tykajuce sa rezimu sporenia.

[Sporenie]

; Parameter "PravidelneStrhnutiePoplatku" urcuje, kedy sa plati poplatok
; true = poplatok sa pravidelne strhava pri kazdom vklade zo sporenia.
; false = poplatok je zaplateny pri nultej platbe.
MesacnyPoplatok = false

; Maximalny doba meskania (nezaplatenych splatok) v mesiacoch.
MaxMeskanie = 3

; Moznost sparovania viacerych vkladov voci sporeniu v jednom mesiaci
ViacVkladovVMesiaci = true 

; Urcuje, ci sa pri preruseni sporenia pokutuje podielnik.
Pokutovanie = true

; Suma a mena pripadnej pokuty v pripade prerusenia sporenia (relevantne iba ak je 
; parameter Pokutovanie nastaveny na true).
SumaPokuty = 3000
MenaPokuty = "SKK"

; Zobrazenie cielovej sumy
CielovaSuma = false

; Zobrazenie vstupnej investicie
VstupnaInvesticia = true

; Nasledovne polozky urcuju, ci je mozne redemovat/presuvat/prevadzat PL zo sporenia
; bez zrusenia samotneho sporenia. 
; 0 = sporenie sa nezrusi, mozu sa presunut len cele sporenie na novy fond
; 1 = sporenie sa zrusi, moze akciu rusiacu sporenie vykonat
; 2 = akcia s PL zo sporenia je zakazana
RedemovanieSporenia = 1
PresunSporenia = 1
PrevodSporenia = 1

[Uzavierka]
; Ma sa generovat zavazok za spravu CP (specifikum IAM) ?
ZavazokSpravaCP = true
; Ma sa generovat zavazok dane z btto vynosu Eurobondov (specifikum IAM) ?
ZavazokDaneEB = true
; Na kolko desatinnych miest sa ma zaokruhlovat vypocitana cena PL ?
CenaPLDesatiny = 5
; Skontrolovat, ci nie su v systeme na danom fonde este neukoncene operacie za dany den ?
Kontrola = true
; Urcuje, ci ma byt pri oceneni uctovneho kodu 325300 pouzity predposledny kurz namiesto posledneho
NIPPredposlednyKurz = true
; Urok BU v dni rekonfirmacie uroku ?
UrokBU = false

[Ziadost]
; Tlac ziadosti - ak nie je nic zadane, nezobrazi tlac, inak tlaci
; subor print_ziadost_??.php, kde ?? je nizsieuvedeny parameter
EmisiaTlac = "vydanie" 
RedemaciaTlac = "vyplata"
PresunTlac = "presun"
PrevodTlac = "prevod"
DSTlac = "ds"

EmisiaPoplatok = false
RedemaciaPoplatok = false
PresunPoplatok = false
PrevodPoplatok = false
DSPoplatok = false

;Mod potvrdenia DS
PotvrdenieDS = 2

[PL]
; Defaultny user pre statsdata v pripade nenajdenia predchadzajuceho pouzivatela
DefaultUser = 3
; Urcuje, ci aky oddelovac sa pouzije (a aky znak to ma byt) medzi seriou a cislom emisie
; Napriklad v IAM nie je tam medzera, takze cislo PL je A000001, v SLSP je tam
; medzera, takze tam je nazov A 000001
Oddelovac = ""

[Refresh]
; Zobrazovat alarm ?
Alarm = false
; Automaticky verifikovat aktiva pri vstupe do dialogu pokynu ? 
AutoVerifikacia = true

[PodielnikPrehlady]
; Povolene menu prehlady
Povolene = true
PrikazUhrada = true

[FondFilter]
; fondID fondov, pre ktore sa budu generovat vypisy z IAM Konta
VypisKonta=5,2
; fondID pre prikaz na uhradu
PrikazUhrada=3
; fond id na autentifikaciu
Autentifikacia=5
;Na ktorych fondoch je poistenie
Poistenie = 2,3,4

[Poplatok]
Autentifikacia = 20
PrikazUhrada = 50

[Konverzia]
;Urcuje, ci su v systeme dostupne forwardove konverzie
Forwardy = false

[CP]
;Zobrazovanie atributov pri konfirmacii na hromadny nakup CP
RozsireneAtributy = true

[Pooling]
;Zobrazovanie mien v poolingu, zobrazia sa iba meny v zozname
;Meny musia byt v jednoduchych uvodzovkach, oddelene ciarkou!
Meny = 'EUR','SKK','USD'

[Verifikacia]
;Nastavenie verifikacia

;automaticka verifikacia inv. zameru
AutoVerifikacia = true

;povolenie verifikacie tomu istemu pouzivatelovi, co zadal zamer
TheSameUser = true

[Kontrola]
PocetDni = 7

[Ucty]
; povolenie/zakazanie vedenia beznych uctov v rovnakej mene
BURovnakaMena = true

[Prehlady]
;povolenie zobrazenia prehladu poplatkov pred rekonfirmaciou
PredRekonfirmaciou = true

[Dan]
;Platca dane
Platca=true

[AUV]
; Splacanie kuponu a istiny je dane kalendarom
KalendarSplacania = true

; nestandardne obdobia splacania kuponu
NestandardnyKupon = true

[EURConversionSettings]

DUAL_PRICING_START = "2008-07-01"
DUAL_PRICING_START_TIMESTAMP = 1214870400.5
DUAL_PRICING_END = "2009-12-31"
DUAL_PRICING_END_TIMESTAMP = 1262217600.5
CONVERSION_DATE = "2009-01-01"
CONVERSION_DATE_TIMESTAMP = 1230768000.5
BASE_CURRENCY_BC = "SKK"
BASE_CURRENCY_AC = "EUR"
CONVERSION_RATE = 30.126

; DO NOT REMOVE THIS LINE */ ?>

<?php
/*
 * $Id: settings.php,v 1.2 2008/10/27 15:04:15 theinz Exp $
 *
 * Copyright (C) 2008 Goldmann Systems
 *
 * Maintainer: Goldmann Systems
 *
 * Description:
 *
 */


$config = parse_ini_file($ROOT . "conf/assetman.ini.php", true);

$settings = parse_ini_file("appConfig.ini");

define("defaultDB", $settings['DEFAULT_DB_HOST']);
define("clickupDB", $settings['CLICKUP_DB_HOST']);
define("onboardingDB", $settings['ONBOARDING_DB_HOST']);
define("onboardingMDB", $settings['ONBOARDING_MDB_HOST']);
define('oracleDB', $settings['ORACLE_DB_HOST']);

/* The refresh interval for menu_status.php and refresh.php */
    define('REFRESH_INTERVAL', 20);

/* Settings for unauthorized access logging */
define('UNAUTH_LOG', $config["Unauthorized"]["DoLogging"]);
define('UNAUTH_LOG_FILE', $config["Unauthorized"]["LogFile"]);

/* Settings for dbconnect.php */
define('DB_USER', $config["Database"]["User"]);
define('DB_PASS', $config["Database"]["Password"]);
define('DB_NAME', $config["Database"]["Connection"]);

define('DB_QS', ";\n");
define('DB_DEBUG', $config["Database"]["DebugMode"]);
define('DB_LOG', $config["Database"]["DoLogging"]);
define('DB_LOG_FILE', $config["Database"]["LogFile"]);

/* Strings so enclose a SQL date expression in oracle in to get a date in seconds from 1970/1/1 */
const DATE_EPOCH1 = 'to_number(';
define('DATE_EPOCH2', " - to_date('01-01-1970 00:00','MM-DD-YYYY HH24:MI')) * 86400 + 0.5");

/* Cesta pre upload kurzov */
define('L_KURZY_PATH', '/tmp/kurzy.dat');

/* Nastavenie parametrov importovaneho csv suboru s kurzami */
define('L_KURZY_DELIMITER', $config["Kurzy"]["Delimiter"]);
define('L_KURZY_DATEFORMAT', $config["Kurzy"]["DateFormat"]);
define('L_KURZY_DATUM_POS', $config["Kurzy"]["DatePosition"]);
define('L_KURZY_ISIN_POS', $config["Kurzy"]["IsinPosition"]);
define('L_KURZY_KURZ_POS', $config["Kurzy"]["KurzPosition"]);

/* Settings for SPIN connectivity */
define('SPIN_IMPORT_EXPORT_PATH', '/home/<USER>/spin/samples');
define('IMPORT_SPIN_COMMAND', '/bin/sh /home/<USER>/spin/run.sh');

define('EXCLUDE_DEBETS', true);

/* Localization settings */
global $langNames, $langFiles;
//session_register('langNames');
//session_register('langFiles');

foreach ($config["Languages"] as $name => $file) {
    if (!defined("DEFAULT_LANGUAGE_FILE")) {
        define('DEFAULT_LANGUAGE_FILE', $file);
    }
    $langNames[$name] = $name;
    $langFiles[$name] = $file;
}


/*	$langNames['slovak'] = 'Slovensky';
	$langFiles['slovak'] = 'conf/slovak.lang.php';
	$langNames['english'] = 'English';
	$langFiles['english'] = $ROOT.'conf/english.lang.php';
	$langNames['german'] = 'Deutsch';
	$langFiles['german'] = $ROOT.'conf/german.lang.php';
*/
session_start();

if ($sess_language_file) {
    require_once($ROOT . "conf/slovak.lang.php");
}
?>

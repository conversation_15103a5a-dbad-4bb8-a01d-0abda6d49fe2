<?php
/*
 * $Id: slovak.lang.php,v 1.2 2008/10/27 15:04:15 theinz Exp $
 *
 * Copyright (C) 2008 Goldmann Systems
 *
 * Maintainer: Goldmann Systems
 *
 * Description:
 *
 */

	/* Charset of this lokalization file */
const CHARSET = 'utf-8';
	define('HTTPEQUIV', 'X-UA-Compatible');
	define('HTTPCONTENT', 'IE=9');
	define('IT_PER_PG', '15');
	define('HTTPMETA', '<meta http-equiv="X-UA-Compatible" content="IE=8; charset=windows-1250" />');
	define('DOCTYPE', '');
	define('L_SPOLOCNOST_NAZOV', $config["Main"]["Spolocnost"]);

	/* Application name */
	($sess_depo == true) ? define('L_APP_NAME', 'Depo Manager') : define('L_APP_NAME', 'Private Banking');

	/* Short month names */
	define('L_JAN',					'Jan');
	define('L_FEB',					'Feb');
	define('L_MAR',					'Mar');
	define('L_APR',					'Apr');
	define('L_JUN',					'Jun');
	define('L_JUL',					'Jul');
	define('L_AUG',					'Aug');
	define('L_SEP',					'Sep');
	define('L_OCT',					'Oct');
	define('L_NOV',					'Nov');
	define('L_DEC',					'Dec');

	/* Long month names */
	define('L_JANUARY',				'Janu�r');
	define('L_FEBRUARY',				'Febru�r');
	define('L_MARCH',				'Marec');
	define('L_APRIL',				'Apr�l');
	define('L_MAY',					'M�j');
	define('L_JUNE',				'J�n');
	define('L_JULY',				'J�l');
	define('L_AUGUST',				'August');
	define('L_SEPTEMBER',				'September');
	define('L_OCTOBER',				'Okt�ber');
	define('L_NOVEMBER',				'November');
	define('L_DECEMBER',				'December');

	/* Short day names */
	define('L_SUN',					'Ne');
	define('L_MON',					'Po');
	define('L_TUE',					'Ut');
	define('L_WED',					'St');
	define('L_THU',					'�t');
	define('L_FRI',					'Pi');
	define('L_SAT',					'So');

	/* Long day names */
	define('L_MONDAY',				'Pondelok');
	define('L_TUESDAY',				'Utorok');
	define('L_WEDNESDAY',				'Streda');
	define('L_THURSDAY',				'�tvrtok');
	define('L_FRIDAY',				'Piatok');
	define('L_SATURDAY',				'Sobota');
	define('L_SUNDAY',				'Nede�a');

	/* Number formatting attributes */
	define('L_DECIMAL_SEPARATOR',			',');
	define('L_THOUSAND_SEPARATOR',			' ');
	define('L_DATE_SEPARATOR',			'.');

	/* Misc */
	define('L_LOGIN_NAME',				'Prihlasovacie meno');
	define('L_PASSWORD',				'Heslo');
	define('L_ABBREV',				'Skratka');
	define('L_ABBREVIATION',			'Skratka');
	define('L_ABS_NARAST_SPRAV_PROSTR',	'Abs. n�rast sprav. prostr.');
	define('L_ACCOUNT_NUMBER_INCORRECT',		'Zadan� ��slo ��tu nie je spr�vne.');
	define('L_ACTIVE',				'Akt�vny');
	define('L_ACTIVE_TYPES',			'Atrib�ty');
	define('L_ACTUAL_KURZ',				'Aktu�lny kurz');
	define('L_ACTUAL_SERIE',			'Aktu�lna s�ria');
	define('L_ADD',					'Prida�');
	define('L_ADDRESS',				'Adresa');
	define('L_ADDRESSES',				'Adresy');
	define('L_ADD_NEW_BANKA',			'Prida� nov� banku');
	define('L_ADD_NEW_BASE',			'Prida� nov� z�klad');
	define('L_ADD_NEW_DEST',			'Prida� nov�ho custodi�na');
	define('L_ADD_NEW_PODIELNIK_Q',			'Chcete zaregistrova� klienta ?');
	define('L_ADD_NEW_STATE',			'Prida� nov� �t�t');
	define('L_ADD_PARTNER', 			'Prida� partnera');
	define('L_ADD_STATE',				'Prida� �t�t');
	define('L_ADD_UCET', 				'Prida� ��et');
	define('L_ADD_UROK', 'Pridanie �rokovej sadzby');
	define('L_ADD_USER',				'Prida� u��vate�a');
	define('L_AGENT',				'Pracovn�k');
	define('L_AGENTID_UZ_EXISTUJE',			'Agent s t�mto ��slom je pre t�to agent�ru u� zadan�');
	define('L_AGENTS',				'Agenti');
	define('L_AGENTURA',				'Pracovisko');
	define('L_AGENTURY',				'Agent�ry');
	define('L_AGENT_ID',				'��slo agenta');
	define('L_AKTUAL_KURZ',				'Aktu�lny kurz');
	define('L_AKCIA',				'Akcia');
	define('L_AKCIE',				'Akcie');
	define('L_AKTIVA',				'Akt�va');
	define('L_AKTIVA_FONDU',			'Akt�va portf�lia');
	define('L_AKTIVITA',				'Aktivita');
	define('L_AKTIVNA',				'Akt�vna');
	define('L_AKTIVNE',				'Akt�vne');
	define('L_AKTIVOVAT',				'Aktivova�');
	define('L_AKTIVUM',				'Akt�vum');
	define('L_AKTUALIZOVAT',			'Aktualizova�');
	define('L_AKTUALIZACIA_SA_NEPODARILA',	'Aktualiz�cia sa nepodarila');
	define('L_AKTUALNA_HODNOTA',			'Aktu�lna hodnota PL');
	define('L_AKTUALNA_TRHOVA_HODNOTA',	'Aktu�lna trhov� hodnota');
	define('L_AKTUALNY_EVIDENCNY_DEN',			'Aktu�lny eviden�n� de�');
	define('L_AKTUALNY_KURZ',			'Aktu�lny index');
	define('L_AKTUALNY_MESIAC',			'Aktu�lny mesiac');
	define('L_AKT_DATE_SPLATNOST',			'Akt.d�tum splatnosti');
	define('L_ALERT_DOBA_VYSP_5',			'Upozornenie: Doba vysporiadania je vy��ia ako 5 dn�');
	define('L_ALERT_KS_REZERVOVANE',		'Kusy s� rezervovan�');
	define('L_ALERT_KS_VOLNE',			' kusov je vo�n�ch');
	define('L_ALERT_KS_V_MAJETKU',			' kusov je v majetku');
	define('L_ALERT_MALO_KUSOV',			'M�lo kusov!');
	define('L_ALERT_VELA_KUSOV',			'Ve�a kusov!');
	define('L_ALL',					'V�etky');
	define('L_ALTERNATIVE_AKTIVUM',			'Alternat�vne akt�vum');
	define('L_ALTERNATIVE_OPERATOR',		'Oper�tor alternat�vy');
	define('L_AND',							'A');
	define('L_ARCHIVE',				'Arch�vny');
	define('L_ARCHIVNE',				'Arch�vne');
	define('L_ARCHIV_DATE',				'D�tum archiv�cie');
	define('L_AS',						'a.s.');
	define('L_ASSETID_NOT_UNIQUE',			'ID atrib�tu mus� by� jedine�n�');
	//define('L_ASSET_CANNOT_BE_CHANGED',		'Atrib�t je fixn�, nie je povolen� ho aktualizova�.');
	define('L_ASSET_CANNOT_BE_CHANGED',		'Nie je povolen� aktualizova� fixn� atrib�t !');
	define('L_ASSET_INCORRECT_COMBINATION',		'Nespr�vna kombin�cia');
	define('L_ASSIGNED_ATTRIBUTES',			'Priraden� atrib�ty');
	define('L_ATRIBUT',					'Atrib�t');
	define('L_ATRIBUTY_PRESUNU',		'Atrib�ty presunu');
	define('L_ATTRIBUTE',				'Atrib�t');
	define('L_ATTRIBUTES',				'Atrib�ty');
	define('L_ATTRIBUTES_FOR',			'Atrib�ty pre');
	define('L_AUDIO_OVERENIA',			'Audio overenia');
const L_AUTH_ERROR = 'Chybné údaje !!!';
	define('L_AUDIT',				'Audit');
	define('L_AUTO',				'Automaticky');
	define('L_AUTO_EMITUJ',				'Auto. emituj');
	define('L_AUTO_ZAUCTUJ',				'Auto. zaev.');
	define('L_AUTO_NATIPUJ',				'Auto. natip.');
	define('L_AUTO_SPAROVANIE',			'Automatick� sp�rovanie');
	define('L_AUTO_SPAROVANIE_DOSLYCH_PLATIEB',	'Automatick� sp�rovanie do�l�ch platieb');
	define('L_AUTOMATIC',				'Automatick�');
	define('L_AUTOPROLONGACIA',			'Autoprolong�cia');
	define('L_AUTORIZACIA_PODPISU',		'Autoriz�cia podpisu');
	define('L_AUTORIZOVANY',		'Autorizovan� podpis');
	define('L_AUV',					'AUV');
	define('L_AUVREAL',				'AUV');
	define('L_AVAILABLE_ATTRIBUTES',		'Dostupn� atrib�ty');
	define('L_AVIZO',				'Av�zo');
	define('L_AZIO', 				'Zvy�ok');
	define('L_BACK',				'&lt;&lt; Sp�');
	define('L_BAD_ASSETID',				'Nespr�vne ID akt�va');
	define('L_BAD_TYPID',				'Nespr�vne ID pokynu');
	define('L_BAD_DATE',				'Zl� d�tum');
	define('L_BAD_DATEFROM',			'Zl� d�tum za�iatku platnosti');
	define('L_BAD_DATEPOKYN',			'Zl� d�tum pokynu');
	define('L_BAD_DATETILL',			'Zl� d�tum konca platnosti');
	define('L_BAD_DATE_KONF',			'Zl� d�tum konfirm�cie');
	define('L_BAD_DATE_REAL',			'Zl� d�tum realiz�cie');
	define('L_BAD_SIGNATURE_FILE',		'S�bor nevyhovuje po�adovan�m krit�ri�m :<br>1. s�bor mus� by� vo form�te GIF,JPEG alebo PNG.<br>2. s�bor nesmie ma� ve�kos� v��iu ako 50 kB.');
	define('L_BANKA',				'Banka');
	define('L_BANKA_ADD',				'Prida� nov� banku');
	define('L_BANK_ACCOUNT',			'BU');
	define('L_BASE',				'B�za');
	define('L_BASE_ADD',				'Pridav�nie z�kladu');
	define('L_BASE_MENA',			'Zakladna mena');
	define('L_BASIC_DATA',				'Z�kladn� �daje');
	define('L_BCPB',				'BCPB');
	define('L_BEG_DATE',				'D�tum za�iatku');
	define('L_BEZNY_UCET',				'Be�n� ��et');
	define('L_BEZNY_UCET_PREHLAD',				'Stav na be�nom ��te');
	define('L_BLOKOVANIE_PRIHLASENIA',	'Blokovanie prihlasovania');
	define('L_BLOCK_ALL',				'Blokova� v�etk�ch u��vate�ov');
	define('L_BLOCKEDLOGIN',			'Login zablokovan�');
	define('L_BLOKOVANA',				'Blokovan�');
	define('L_BLOKOVANE',				'Blokovan�');
	define('L_BLOKOVAT',				'Blokova�');
	define('L_BLOKOVANIE',				'Blokovanie u��vate�a');
	define('L_BOLI_UZ_REKONFIRMOVANE',		'boli rekonfirmovan� in�m u��vate�om.');
	define('L_BOLI_ZMENENE_INYM',			'boli zmenen� in�m u��vate�om.');
	define('L_BOND',				'Dlhopis');
	define('L_BONDS',				'Dlhopisy');
	define('L_BOTH',				'Obe');
	define('L_BROWSER_UNSUPPORTED',	'Pou��vate nepodporovan� browser. Pre pou��vanie Asset Managera si nain�talujte Internet Explorer 5.5 alebo vy��ie.');
	define('L_BURZOVY',				'Burzov�');
	define('L_BUSINESS_DAY',			'obchodn�ho d�a');
	define('L_BU_ATTRIBUTES',			'Atrib�ty be�n�ch ��tov');
	define('L_CANCEL',				'Zru�i�');
	define('L_CAS',					'�as');
	define('L_CASOVE_ROZLISENIE',		'�asov� rozl�enie');
	define('L_CAS_PODANIA',				'�as podania');
	define('L_CELKOVY_POCET',			'Celkov� po�et');
	define('L_CENA_OBCHODU',			'Cena obchodu');
	define('L_CENA_ZA_KUS',			'Cena za kus');
	define('L_CENNE_PAPIERE',			'Cenn� papiere');
	define('L_CENNY_PAPIER',			'Cenn� papier');
	define('L_CERTIFICATES',			'Certifik�ty');
	define('L_CERTIFIKATY',				'Certifik�ty');
	define('L_CERTIFIKAT',				'Certifik�t');
	define('L_CHANGE_PWD',				'Zmeni� heslo');
	define('L_CHANGE_REKLAMACIA_STATUS','Zmeni� stav reklam�cie?');
	define('L_CHOOSE',				'Vyberte');
	define('L_CIELOVY_FOND',			'Cie�ov� portf�lio');
	define('L_CIELOVA_SUMA',			'Cie�ov� suma');
	define('L_CIPHER',				'Cifra');
	define('L_CISELNIKY', 				'��seln�ky');
	define('L_CISLO_BEZNEHO_UCTU',			'��slo be�n�ho ��tu');
	define('L_CISLO_DIVIDENDY',			'��slo dividendy');
	define('L_CISLO_DOKLAD',			'��slo dokladu');
	define('L_CISLO_EMISIA_NAVRH',		'Navrhovan� ��slo vkladu');
	define('L_CISLO_EMISIE',			'��slo emisie');
	define('L_CISLO_KARTY',			'��slo karty');
	define('L_CISLO_KONF',				'��slo konfirm�cie');
	define('L_CISLO_KONFIRMACIE',		'��slo konfirm�cie');
	define('L_CISLO_LISTU',				'��slo listu');
	define('L_CISLO_MAJETK_UCTU',		'��slo majetk.��tu');
	define('L_CISLO_OBCHODU',			'��slo obchodu');
	define('L_CISLO_PARTNERA', 			'��slo partnera');
	define('L_CISLO_REKLAMACIE',		'��slo reklam�cie');
	define('L_CISLO_REKONF',			'��slo rekonfirm�cie');
	define('L_CISLO_TV',				'��slo TV');
	define('L_CISLO_UCTU',				'��slo ��tu');
	define('L_CISLO_UCTU_SPRAVCU',			'��slo ��tu spr�vcu');
	define('L_CISLO_VKLADU',			'��slo KTV');
	define('L_CISLO_ZIADOSTI',			'��slo �iadosti');
	define('L_CISLO_ZIADOSTI_SHORT',		'��s.�iad.');
	define('L_CISLO_PORTFOLIA', '��slo portf�lia');
	define('L_CISTE_PREDAJE',			'�ist� vklady');
	define('L_CIS_KONF',				'��slo');
	define('L_CIS_PARTN', 				'��s.part.');
	define('L_CITY',				'Mesto');
	define('L_CLEAR',				'Zmaza�');
	define('L_CLEARING_CODE',			'K�d');
	define('L_CLEARING_CODES',			'Clearingov� k�dy');
	define('L_CLIENT_CERT',				'Klientov certif');
	define('L_CLIENT_S_DN_O',			'CLIENT_S_DN_O');
	define('L_CLIENT_S_DN_OU',			'CLIENT_S_DN_OU');
	define('L_CLOSE',				'Zavrie�');
	define('L_CLOSED',				'Uzavret�');
	define('L_CLOSE_FOND',				'D�tum uzavretia portf�lia');
	define('L_CL_CODE',				'Clearing. k�d');
	define('L_CODE_TYPE',				'Duh k�du');
	define('L_COMMIT',					'Potvrdi�');
	define('L_COMMIT_PRINT',			'Potvrdi� + tla�');
	define('L_COMPUTED_FIELD',			'Vyr�ta� toto pole');
	define('L_CONFIRM',					'Potvrdi�');
	define('L_CONFIRMATION_DATE',			'D�tum konfirm�cie');
	define('L_CONFIRM_NEW_PASSWORD',		'Potvrdi� heslo');
	define('L_CONF_CP_CC_DEL',			'Skuto�ne chcete zmaza� tento clearingov� k�d?');
	define('L_CONF_CP_ICR_DEL',			'Skuto�ne chcete zmaza� toto RIC?');
	define('L_CONF_CP_ICR_GENERATE_RIC',		'Skuto�ne chcete prida� do�asn� nezn�me RIC?');
	define('L_CONF_CP_RIC_DEL',			'Skuto�ne chcete zmaza� t�to obchodn� menu a RIC?');
	define('L_CONSTANT_SYMBOL',			'Kon�tantn� symbol');
	define('L_CONTACT',				'Kontakt');
	define('L_CONTACT_ADDRESS',			'Kontaktn� adresa');	
	define('L_CONTACT_DATA',			'Kontaktn� �daje');
	define('L_CONTACT_NAME',			'Meno');
	define('L_CONTINUE',				'Pokra�ova�');
	define('L_COUNTRY',				'Krajina');
	define('L_COUPON',				'Kup�n');
	define('L_COUPON_FREQ',				'FQ kup�nu');
	define('L_CP_CONF_ATTRIBUTES_CHANGED',		'Atrib�ty CP boli zmenen�');
	define('L_CP_NAMONITORING',			'Zahrn�� vybran� CP do monitoringu rizika?');
	define('L_CP_NAVERIFIKACIU',			'Skuto�ne chcete verifikova� vybran� CP?');
	define('L_CP_SUBTYPE',				'Poddruh akt�va');
	define('L_CP_TYPE',				'Druh akt�va');
	define('L_CROSSCHARGE',				'Prestupn� poplatok');
	define('L_CURRENCY',				'Mena');
	define('L_CURRENCY_CODE',			'K�d meny');
	define('L_CUSTODIAN',				'Custodian');
	define('L_CUSTODIANFEE',			'Odplata depozit�ra');
	define('L_CU_PARTNERA',			'��slo ��tu partnera');
	define('L_CU_USED',				'Toto ��slo ��tu je u� pou�it� !');
	define('L_CUB',					'��slo ��tu');
	define('L_DAN',					'Da�');
	define('L_DATABAZA',				'Datab�za');
	define('L_DATE',				'D�tum');
	define('L_DATEFORMAT',				'j.n.Y');		// Default language date format
	define('L_DATEFORMATZERO',			'd.m.Y');		// Default language date format
	define('L_DATEORDER',				'D.M.Y');		// Default date order - D.M.Y or M/D/Y ...
	define('L_DATETIMEFORMAT',			'j.n.Y G:i');		// Default language date and time format
	define('L_DATETIMEORACLEFORMAT',		'Y-m-d G:i:s');		// Default language date and time format
	define('L_DATETIMEORACLEFORMAT_24',		'Y-m-d H:i:s');		// Default language date and time format
	define('L_DATETIMEORACLEFORMATZERO_24',	'd.m.Y H:i:s');
	define('L_DATE_AGREEMENT',			'D�tum a �as podania');
	define('L_DATE_ARCHIVE',			'D�tum archiv�cie');
	define('L_DATE_BEGIN',				'D�tum za�iatku');
	define('L_DATE_EMISIE',				'D�tum emisie');
	define('L_DATE_END',				'D�tum konca');
	define('L_DATE_ESTABLISH',			'D�tum zalo�enia');
	define('L_DATE_KONFIRMACIA',			'D�tum konfirm�cie');
	define('L_DATE_MATCHING',			'Matching');
	define('L_DATE_NOT_CONFIRMED',			'Eviden�n� d�tum e�te nie je potvrden�');
	define('L_DATE_PODANIE_ZIADOSTI',		'D�tum podania �iadosti');
	define('L_DATE_POKYN_FROM_TO',			'Platnos� pokynu<br>od - do');
	define('L_DATE_REDEMACIE',			'D�tum v�beru');
	define('L_DATE_REGISTRACIA',			'D�tum registr�cie');
	define('L_DATE_REGISTRACIA_ZIADOSTI',		'D�t.registr�cie');
	define('L_DATE_REGISTRATION',			'D�tum registr�cie');
	define('L_DATE_REKONFIRMACIA',			'D�tum rekonfirm�cie');
	define('L_DATE_SPLATNOST',			'D�tum splatnosti');
	define('L_DATE_SPLATNOSTI',			'D�tum splatnosti');
	define('L_DATE_TRANSAKCIE',			'D�t. transakcie');
	define('L_DATE_UBYTKU',				'D�tum �bytku');
	define('L_DATE_UZAVRETIA',			'D�tum uzavretia');
	define('L_DATE_VYSPORIADANIABU',		'D�t. fin. vysp.');
	define('L_DATE_VYSPORIADANIAMU',		'D�t. maj. vysp.');
	define('L_DATE_ZUCTOVANIE',			'D�tum z��tovania');
	define('L_DATE_ZUCTOVANIE_NEXT',		'Nov� d�tum z��tovania');
	define('L_DATUM',				'D�tum');
	define('L_DATUM_1',				'D�tum 1');
	define('L_DATUM_2',				'D�tum 2');
	define('L_DATUM_AKTIVACIE',				'D�tum aktiv�cie');
	define('L_DATUM_BLOKOVANIA',		'D�tum blokovania');
	define('L_DATUM_CAS_OBCHODU','D�tum/�as<br>obchodu');	
	define('L_DATUM_CONFIRMED',			'D�tum je potvrden�');
	define('L_DATUM_DO',				'D�tum do');
	define('L_DATUM_EXPIRACIE',			'D�tum expir�cie');
	define('L_DATUM_ISTINA_KUPON',			'Splatenie');
	define('L_DATUM_KONFIRMACIE',			'D�tum konfirm�cie');
	define('L_DATUM_MV',					'D�tum maj. vyspor.');
	define('L_DATUM_FV',					'D�tum fin. vyspor.');
	define('L_DATUM_NEMOZE_BYT_NIZSI_NEZ',		'D�tum nem��e by� ni��� ne�');
	define('L_DATUM_OBCHODU',			'D�tum obchodu');
	define('L_DATUMCAS_OBCHODU',		'D�tum/�as obchodu');
	define('L_DATUM_OCENENIA',			'D�tum ocenenia');
	define('L_DATUM_OD',				'D�tum od');
	define('L_DATUM_PLATNOSTI_DO',			'D�tum platnosti do');
	define('L_DATUM_PLATNOSTI_OD',			'D�tum platnosti od');
	define('L_DATUM_PODANIA',			'D�tum podania');
	define('L_DATUM_POKYNU',			'D�tum pokynu');
	define('L_DATUM_POSL_EXP',			'D�tum posl. exportu');
	define('L_DATUM_PRIRASTKU',			'D�tum pr�rastku');
	define('L_DATUM_PRIZNANIA', 'D�tum priznania');
	define('L_DATUM_PRIZNANIA_VYSSI',		'D�tum priznania je vy��� ako aktu�lny eviden�n� d�tum. V�nosy nie je mo�n� prizna�');
	define('L_DATUM_REALIZACIE',			'D�tum realiz�cie');
	define('L_DATUM_REEMISIE',			'D�tum reemisie');
	define('L_DATUM_REGISTRACIE',			'D�tum registr�cie');
	define('L_DATUM_SPAROVANIA',		'D�tum sp�rovania');
	define('L_DATUM_SPLATNOSTI', 'D�tum splatnosti');
	define('L_DATUM_TRANSAKCIE',		'D�tum transakcie');
	define('L_DATUM_UZAVIERKY',			'D�tum uz�vierky');
	define('L_DATUM_UZ_JE_POTVRDENY', 		'D�tum u� je potvrden�');
	define('L_DATUM_VALUTY',			'D�tum valuty');
	define('L_DATUM_VSTUP_MAJETOK',			'D�tum vstupu do majetku');
	define('L_DATUM_VYSTUP_MAJETOK',			'D�tum v�stupu z majetku');
	define('L_DATUM_VYBAVENIA',			'D�tum vybavenia');
	define('L_DATUM_VYDANIA',			'D�tum vydania');
	define('L_DATUM_VYPISU_K',			'D�tum v�pisu k');
	define('L_DATUM_VYSP',				'D�tum vysp.');
	define('L_DATUM_VYSPOR',			'D�tum vysporiadania');
	define('L_DATUM_ZMENY', 			'D�tum zmeny');
	define('L_DATUM_ZOSTAVY', 			'D�tum zostavy');
	define('L_DATUM_ZRIADENIA',			'D�tum zriadenia');
	define('L_DATUM_ZRUSENIA',			'D�tum zru�enia');
	define('L_DAT_FAIL_UZAV',			'D�tum poslednej ne�spe�nej uz�vierky');
	define('L_DAT_LAST_UZAV',			'D�tum poslednej uz�vierky');
	define('L_DAT_NEXT_UZAV',			'D�tum nasleduj�cej uz�vierky');
	define('L_DAT_POKYNU',				'D�t.pokynu');
	define('L_DAT_TRANS',				'D�t.trans.');
	define('L_DAT_ZAC',				'D�t.za�.');
	define('L_DATUM_EMISIE_MUSI_BYT_VACSI_DEN',			'D�tum splatnosti mus� by� va�� ako d�tum emisie.');
	define('L_DATUM_EMISIE_MUSI_BYT_VACSI_ROK',			'D�tum splatnosti mus� by� va�� ako d�tum emisie a rozdiel mus� by� cel� rok alebo roky.');
	define('L_DAY',					'De�');
	define('L_DAYS',				'dn�');
	define('L_DEALER',				'Dealer');
	define('L_DEBET',				'Debet');
	define('L_DEFAULT',				'P�vodn�');
	define('L_DEFINICIA_VKLADU',	'Defin�cia vkladu');
	define('L_DELETE',				'Zmaza�');
	define('L_DELETE_ALT',			'Odstr�ni�');
	define('L_DELETE_BLOCK',			'Odstr�ni� blokovanie prihlasovania ?');
	define('L_DELETE_SPOLUPODIELNIK',	'Odstr�nit spolupodielnika?');
	define('L_DELETE_UHRADA',			'Zmaza� �hradu');
	define('L_DEMOGRAFICKE_UDAJE',	'Demografick� �daje');
	define('L_DENNA_UZAVIERKA',		'Denn� uz�vierka');
	define('L_DENNE',				'Denne');
	define('L_STVRTROCNE',			'�tvr�ro�ne');
	define('L_POLROCNE',			'Polro�ne');
	define('L_DENNIK_PM',			'Denn�k portf�liomanagera');
	define('L_DEPOZITAR',				'Depozit�r');
	define('L_DEPOZITARA',				'Mana��rsky / za riadenie');
	define('L_DEPOZITNY_CERTIFIKAT',		'Depozitn� certifik�t');
	define('L_DESTINATION',				'V�platn� miesto');
	define('L_DEST_ADD',				'Prida� nov�ho custodi�na');
	define('L_DETAIL',				'Detail');
	define('L_DETAILY',				'Detaily');
	define('L_DIC',					'DI�');
	define('L_DIFFERENCE',				'Rozdiel');
	define('L_DISTRIB_DIVIDENDU',			'Distribuuj dividendu');
	define('L_DIVIDEND',				'Dividenda');
	define('L_DIVIDENDA',				'Dividenda');
	define('L_DIVIDENDA_K_REINVEST', 		'Dividenda k reinvest�cii');
	define('L_DIVIDENDA_K_VYPLATE',			'Dividenda k v�plate');
	define('L_DIVIDENDA_NA_UCET',			'V�nosy na ��et');
	define('L_DIVIDENDA_PODIEL_NETTO',		'Dividenda/Podiel netto');
	define('L_DLHOPIS',				'Dlhopis');
	define('L_DLHOPISY',				'Dlhopisy');
	define('L_DO',					'Do');
	define('L_DO_18',						'0-18 rokov');
	define('L_DO_25',						'19-25 rokov');
	define('L_DO_35',						'26-35 rokov');
	define('L_DO_45',						'36-45 rokov');
	define('L_DO_55',						'46-55 rokov');
	define('L_DO_65',						'56-65 rokov');
	define('L_DO_15000',					'0 - 15 000');
	define('L_DO_25000',					'15 001 - 25 000');
	define('L_DO_50000',					'25 001 - 50 000');
	define('L_DO_100000',					'50 001 - 100 000');
	define('L_DO_200000',					'100 001 - 200 000');
	define('L_DO_500000',					'200 001 - 500 000');
	define('L_DO_1000000',					'500 001 - 1 mil.');
	define('L_DO_5000000',					'1 mil - 5 mil.');
	define('L_DOBA_VIAZ',				'Doba viaz.');
	define('L_DOBA_VIAZANOSTI',			'Doba viazanosti');
	define('L_DOBA_VYSPOR',				'Doba vysp.');
	define('L_DOCUMENTS', 				'Dokumenty');
	define('L_DOCUMENTS_TO_DOWNLOAD',		'Dokumenty na stiahnutie');
	define('L_DOKLAD',				'Doklad');
	define('L_DOKUMENT',				'Dokument');
	define('L_DOKUMENTY',				'Dokumenty');
	define('L_DOPLNKOVE_SLUZBY',			'Doplnkov� slu�by');
	define('L_DOPORUC_HODNOTA',			'Doporu�en� hodnota');
	define('L_DOSLA_PLATBA',			'Do�l� platba');
	define('L_DOSLE_AKTIVA',			'Do�l� akt�va');
	define('L_DOSLE_PAPIERE',			'Do�l� papiere');
	define('L_DOSLE_PLATBY',			'Do�l� platby');
	define('L_DOWNLOAD',				'Download');
	define('L_DRUH',				'Druh');
	define('L_DRUHCPID_NOT_UNIQUE',			'ID druhu CP nie je jedine�n�');
	define('L_DRUHY_AKTIV',				'Druhy akt�v');
	define('L_DRUH_AKTIVA',				'Druh Akt�va');
	define('L_DRUH_CP',				'Druh CP');
	define('L_DRUH_KODU',				'Druh k�du');
	define('L_DRUH_OBCHODU',			'Druh obchodu');
	define('L_DS_CHNG_PD',				'Zmena os. �dajov');
	define('L_DS_DOKLAD',				'Pripojen� doklad');
	define('L_DS_NAZOV',				'N�zov slu�by');
	define('L_DS_NOTNULL',				'Musite zadat hodnotu');
	define('L_DS_NUMBER_SHORT',			'��s.DS');
	define('L_DS_OUT_RANGE',			'�daj nie je v pr�pustnom rozmedz�!');
	define('L_DS_POPIS',				'Popis');
	define('L_DS_POPLATOK',				'Poplatok');
	define('L_DS_TYP',				'Typ slu�by');
	define('L_EDIT',				'Zmeni�');
	define('L_EDIT_PARTNER', 			'Upravi� partnera');
	define('L_EDIT_PODIELNIK',			'Upravi�');
	define('L_EDIT_UCTY', 				'Upravi� ��ty');
	define('L_EDIT_UROK', 'Edit�cia �rokovej sadzby');
	define('L_EDIT_USER',				'Upravi� u��vate�a');
	define('L_EMAIL',				'Email');
	define('L_EMID_SAVED_AS',			'Zaevidovanie vkladu prebehlo �spe�ne. Vklad bol zaevidovan� pod �islom %s .');
	define('L_EMISIA',					'Vklad');
	define('L_EMISIA_CP',			'Emisia');
	define('L_EMISIA_ATRIB',			'Atrib�ty vkladu');
	define('L_EMISIA_ID', '��slo emisie');
	define('L_EMISIA_OBMEDZ',			'Emisn� obmedzenie');
	define('L_EMISIA_PODIELOVEHO_LISTU',		'Zaevidovanie vkladu klienta');
	define('L_EMISIA_REINVESTICIE',			'Emisia reinvest�cie');
	define('L_EMISIE',				'Emisie');
	define('L_EMISIE_A_REDEMACIE',			'Predan� a sp�tne odk�pen� podiely');
	define('L_EMISIE_A_REDEMACIE_V_BUDUCNOSTI',	'Nie je mo�ne rozdeli� v�nos, nako�ko v syst�me existuj� emisie emitovan� alebo redemovan� po d�tume v�platy v�nosu.');
	define('L_EMISIE_NEVYBRATE',		'Neboli vybrat� �iadne emisie!');
	define('L_EMISION_TIME',			'Emisn� �as');
	define('L_EMISSION_DATA',			'�daje o emisii');
	define('L_EMITENT',					'Emitent');
	define('L_EMITENTS',				'Emitenti');
	define('L_EMITENTS_DATA',			'�daje o emitentovi');
	define('L_EMITENT_NAZOV',			'N�zov emitenta');
	define('L_EMITENT_SKRATKA',			'Skratka');
	define('L_EMITOVANE',				'Emitovan�');
	define('L_EMITOVANE_PODIELOVE_LISTY',		'Zoznam vyemitovan�ch podielov�ch listov');
	define('L_EMITUJ',			'Emituj');
	define('L_ENTER_MAXIMUM_SUMA',		'Mus�te zada� doporu�ovan� hodnotu.');
	define('L_ENTER_UCET_SUMA',			'Mus�te zada� cel� hodnotu na ��te.');
	define('L_Encode',				'Encode');
	define('L_EQUITY',				'Equity');
	define('L_EQID',				'Eqid');
	define('L_ERR_ROVNAKY_CL_CODE',			'Clearingov� k�d mus� by� jedine�n� pre ISIN a druh k�du.');
	define('L_ERROR',				'Chyba');
	define('L_ERROR_CHOOSE_CURRENCYNOM',		'Vyberte nomin�l meny');
	define('L_ERROR_CHOOSE_CURR_CURRENCY',		'Zadajte pros�m menu!');
	define('L_ERROR_CHOOSE_CURR_CURRENCY_EXISTS',	'Nem��te prida� menu,ktor� u� existuje!');
	define('L_ERROR_CHOOSE_DRUH',			'Zadajte druh CP!');
	define('L_ERROR_CHOOSE_EMITENT',		'Vyberte emitenta');
	define('L_ERROR_DATE_NO_KURZ',			'�iaden index k dan�mu d�tumu');
	define('L_ERROR_DATE_TIME',			'Zadajte d�tum a �as.');
	define('L_ERROR_ID_ALREADY_EXISTS',		'�iados� s t�mto ��slom u� existuje !');
	define('L_ERROR_INVALID_RC',			'Nespr�vne Rodn� ��slo !!!');
	define('L_ERROR_LIMIT_FIRST_VKLAD',		'Nie je splnen� v��ka prvej invest�cie.');
	define('L_ERROR_LIMIT_NEXT_VKLAD',		'Nie je splnen� v��ka �al�ej invest�cie.');
	define('L_ERROR_MANDATORY_FIELD',		'Vypl�te pros�m v�etky polia formul�ra.');
	define('L_ERROR_MISSING_POCET',			'Zadajte redemovan� po�et podielov.');
	define('L_ERROR_MISSING_SUMA',			'Zadajte vypl�can� sumu.');
	define('L_ERROR_NACITANIE_SUBORU',		'Chyba pri na��tan� s�boru.');
	define('L_ERROR_NADOBUDATEL_DATA',		'Nezadali ste �daje o nadob�date�ovi.');
	define('L_ERROR_NAZOV',					'Zadajte n�zov.');
	define('L_ERROR_NIE_JE_KURZ',			'K dan�mu d�tumu nie je zn�my aktu�lny index podielu.');
	define('L_ERROR_NO_BANKACCOUNT',		'�iadne bankov� konto');
	define('L_ERROR_NO_PERMISSION',			'Nem�te dostato�n� pr�va');
	define('L_ERROR_NO_PODIEL',			'Vyberte pros�m porf�lio.');
	define('L_ERROR_NO_VALUE',			'Nezadali ste krit�rium !!!');
	define('L_ERROR_NUMBER_EXPECTED',		'O�ak�van� ��slo');
	define('L_ERROR_PODIELNIK_NEVLASTNI_PL',	'Podielnik nevlastn� �iadne podielov� listy.');
	define('L_ERROR_POPIS',					'Zadajte popis.');	
	define('L_ERROR_POHLAVIE',				'Zadajte pohlavie.');	
	define('L_ERROR_SAME_FOND',				'Nem��ete vybra� rovnak� portf�lio.');
	define('L_ERROR_SAME_FOND_MENA',		'ERROR_SAME_FOND_MENA');
	define('L_ERROR_SELECT_AGENT',			'Vyberte agenta.');
	define('L_ERROR_SELECT_AGENTAGENTURA',		'Vyberte agent�ru');
	define('L_ERROR_SELECT_AGENTURA',		'Vyberte agent�ru.');
	define('L_ERROR_SELECT_FOND',			'Mus�te vybra� aspo� 1 portf�lio.');
	define('L_ERROR_SELECT_FOND_ACCOUNT',		'Vyberte konto portf�lio');
	define('L_ERROR_SELECT_PODIEL',			'Vyberte podielov� listy ur�en� na presun.');
	define('L_ERROR_SET_BANKACCOUNT',		'Zadajte ��slo bankov�ho ��tu.');
	define('L_ERROR_SET_BANK_ACCOUNT',		'Nezadali ste ��slo bankov�ho ��tu.');
	define('L_ERROR_SET_CPNAZ',			'Zadajte meno CP');
	define('L_ERROR_SET_CPNAZSKRATKA',		'Zadajte skratku CP');
	define('L_ERROR_SET_DAN',			'Zadajte da�');
	define('L_ERROR_SET_DATEEMISIE',		'Zadajte d�tum emisie');
	define('L_ERROR_SET_EXFREK',			'Zadajte ex-frekvenciu');
	define('L_ERROR_SET_ISIN',			'Zadajte ISIN');
	define('L_ERROR_SET_ISTFREK',			'Zadajte frekvenciu istiny');
	define('L_ERROR_SET_KUPFREK',			'Zadajte frekvenciu kup�nu');
	define('L_ERROR_SET_KUPON',			'Zadajte kup�n');
	define('L_ERROR_SET_MATURITYDATE',		'Zadajte d�tum splatnosti');
	define('L_ERROR_SET_NOMINALEMISIE',		'Zadajte nomin�l emisie');
	define('L_ERROR_SET_PRIEZNAZ',			'Zadajte pros�m Priezvisko/N�zov.');
	define('L_ERROR_SET_RCICO',			'Zadajte pros�m rodn� ��slo/I�O klienta.');
	define('L_ERROR_SET_STATE',			'Zadajte �t�t');
	define('L_ERROR_SET_ZAKLAD',			'Zadajte z�klad');
	define('L_ERROR_VYBERTE_PODIELOVE_LISTY',	'Vyberte podielov� listy.');
	define('L_ERROR_VYBER_AKTIVUM',			'Nevybrali ste akt�vum !!!');
	define('L_ERROR_ZIADATEL_DATA',			'Nezadali ste �daje o �iadate�ovi.');
	define('L_ERR_CP_01',				'ERR_CP_01');
	define('L_ERR_CP_02',				'ERR_CP_02');
	define('L_ERR_CP_CC_01',			'Nezadali ste clearingov� k�d cenn�ho papiera!');
	define('L_ERR_CP_CC_02',			'Cenn� papier s tak�mto clearingov�m k�dom sa u� v datab�ze nach�dza!');
	define('L_ERR_CP_ICR_01',			'Nezadali ste RIC!');
	define('L_ERR_CP_ICR_02',			'Toto RIC sa u� v datab�ze nach�dza!');
	define('L_ERR_CP_ICR_03',			'Mena mus� obsahova� aspo� jeden RIC!');
	define('L_ERR_PP_NOT_UNIQUE',			'PP nie je jedine�n�');
	define('L_ERR_PP_SET_ASSET',			'Zadajte akt�vum');
	define('L_ESTABLISH_FOND',			'D�tum zalo�enia portf�lia');
	define('L_EVIDENCNY_DATUM',			'Eviden�n� d�tum');
	define('L_EVID_NUMBER',				'Eviden�n� ��slo');
	define('L_EVID_NUMBER_SHORT',		'E�P');
	define('L_EVIDENCIA_VYPISOV',		'Evidencia v�pisov');
	define('L_EXCEL_CSV',				'Excel - CSV');
	define('L_EXCOUPON',				'Exkup�n');
	define('L_EXISTINA',				'Existina');
	define('L_EXPORT',				'Export');
	define('L_EXPORT_ALL',				'Export v�etk�ch');
	define('L_EXPORT_SELECTED',			'Export vybran�ch');
	define('L_EXPORT_HB',				'Export HB');
	define('L_EXPORTOVAT_ZAZNAMY',			'Exportova� z�znamy');
	define('L_EX_ISTINA_KUPON',			'Re�lny d�tum');
	define('L_FAIL_COUNT',				'!! Po�et ne�spe�ne rekonfirmovan�ch �hrad !! :');
	define('L_FAX_NUMBER',				'Fax');
	define('L_FILE',				'S�bor');
	define('L_FILL_DATES',				'Zadajte d�tumy');
	define('L_FILL_ITEMS',				'Vypl�te polo�ky:');
	define('L_FILL_THIS_ITEMS',			'Nasledovn� polo�ky s� povinn� :');
	define('L_FILL_tv_ITEMS',			'Vypl�te polo�ky');
	define('L_FILTER',				'Filter');
	define('L_FIND',				'H�adaj');
	define('L_FINISH_TRANSACTION',	'Ukon�i� transakciu');
	define('L_FIRM_DATA',				'Firemn� �daje');
	define('L_FIRSTNAME',				'Meno');
	define('L_FIRSTWEEKDAY',			1);			// Default language first week day 0 - wednesday 1 - monday a.s.o.
	define('L_FIX_RESTRICTIONS',			'Fixn� obmedzenia');
	define('L_FLOAT_KUPON',			'Float kup�n');	
	define('L_FOND',				'Portf�lio');
	define('L_FONDS',				'Podielov� listy');
	define('L_FONDY_SPOLU',				'Portf�li� spolu');
	define('L_FOND_FORM',				'Forma portf�lia');
	define('L_FOND_FROM',				'Z fondu');
	define('L_FOND_ID_USED',			'ID portf�lia je u� pou��van�');
	define('L_FOND_MANAGER',			'Mana��ri portf�lia');
	define('L_FOND_MANAGERS_CHANGED',		'Mana��ri portf�lia zmenen�');
	define('L_FOND_NAME',				'N�zov portf�lia');
	define('L_FOND_NEMA_DATUM_INKASA',		'Portf�lio nem� zadan� d�tum inkasa');
	define('L_FOND_POCET_DNI_PLATBA',		'Po�et dn� na pouk�zanie platby klienta');
	define('L_FOND_RATIO_NOT_FOUND',		'Syst�m nena�iel percento poplatku pre dan� hodnotu emisie.');
	define('L_FOND_SELECT',				'Portf�lio');
	define('L_FOND_TO',				'Do fondu');
	define('L_FORMA',				'Forma');
	define('L_FORMAT',				'Form�t');
	define('L_FORM_NOT_FILLED',			'Nem�te vyplnen� v�etky polo�ky !');
	define('L_FORM_PL',				'Forma PL');
	define('L_FORMULAR_CHYBY',			'Pri kontrole formul�ra sa objavili nasledovn� chyby :');
	define('L_FORWARD_RATE',			'Forward rate');
	define('L_FPO',					'F/P osoba');
	define('L_FREK_IST',				'Frek. istiny');
	define('L_FREK_KUPON',				'Frek. kup�nu');
	define('L_FREQUENCY',				'Frekvencia');
	define('L_FREQ_2YEARLY',			'dvojro�ne');
	define('L_FREQ_MATURITY',			'Frekvencia z��tovania');
	define('L_FREQ_MONTHLY',			'mesa�ne');
	define('L_FREQ_NEVER',				'nikdy');
	define('L_FREQ_QUARTERLY',			'�tvr�ro�ne');
	define('L_FREQ_SPLATNOSTI',			'pri splatnosti');
	define('L_FREQ_TERMLY',				'polro�ne');
	define('L_FREQ_YEARLY',				'ro�ne');
	define('L_FREQ_PO_OBRATE',			'po obrate');
	define('L_FROM',				'od');
	define('L_FROM_ARCHIVE',			'Z arch�vu');
	define('L_FUNKCIA',				'Funkcia');
	define('L_FYZ_PERSON',				'Fyzick� osoba');
	define('L_FYZ_PERSON_PODN',			'Fyz. osoba - podnikate�');
	define('L_FYZICKE_OSOBY',			'Fyzick� osoby');
	define('L_GENERATE',			'Generova�');
	define('L_GLOBAL',					'Hromadn�');
	define('L_GRIDCARD',				'Grid karta');
	define('L_GRIDCARD_BLOKOVANIE',		'Blokovanie karty');
	define('L_GRIDCARDS',				'Grid karty');
	define('L_FAKTOR',					'Faktor');
	define('L_GOTOPAGE',			'Cho� na');		
	define('L_HALIER_VYROVNANIE',			'Halierov� vyrovnanie');
	define('L_HELP',				'Pomocn�k');
	define('L_HISTORY',				'Hist�ria');
	define('L_HISTORIA_KLIENTA',	'Hist�ria klienta');
	define('L_HLADAJ',				'H�adaj');
	define('L_HLAVNE_MENU',				'Hlavn� menu');
	define('L_HLV',					'Hal. vyrov.');
	define('L_HODNOTA',				'Hodnota');
	define('L_HODNOTA_AKTIV_NA_ZACIATKU_RIADENIA',	'Hodnota akt�v na za�iatku riadenia');
	define('L_HODNOTA_MAJETKU',			'Hodnota majetku');
	define('L_HODNOTA_POCIATOCNEHO_VKLADU',	'Hodnota po�iato�n�ho vkladu');
	define('L_HODNOTA_PORTFOLIOVYCH_AKTIV',	'Hodnota portf�liov�ch akt�v');
	define('L_HODNOTA_KUPONU_BUDE_ZAPISANA_DO_AKT_OBDOBIA',	'Hodnota kup�nu bude zap�san� do aktu�lneho obdobia!');
	define('L_HODNOTA_VKLADOV',	'Hodnota vkladov');
	define('L_HODNOTA_VYBEROV',	'Hodnota v�berov');
	define('L_HOLDER_TYPE',				'Typ klienta');
	define('L_HOTOVOST',				'Hotovos�');
	define('L_HRAN_TERMIN_DOSIAH_POZAD_NAV',	'D�tum dosiahnutia minim�lnej invest�cie');
	define('L_HROMADNA',			'Hromadn�');
	define('L_HROMADNA_TLAC',		'Hromadn� tla�');
	define('L_IC_DPH','I� DPH');
	define('L_ICO',					'I�O');
	define('L_ID',					'Id');
	define('L_IDAGENT',				'��slo pracovn�ka');
	define('L_IDENTIFICATION_NUMBER',		'Identifika�n� ��slo');
	define('L_IDENTIFIKOVANE',			'Identifikovan�');
	define('L_IDENT_NUMBER',			'��slo dokladu');
	define('L_IDENT_TYPE',				'Druh dokladu');
	define('L_ID_NOT_UNIQUE',			'ID nie je jedine�n�');
	define('L_ID_NUMBER',				'Identifika�n� ��slo');
	define('L_ID_PORTFOLIO',					'Id portf�lia');
	define('L_ID_REQUIRED',				'Mus�te zada� svoje ID !');
	define('L_ID_USED',				'Zadan� eviden�n� �islo je u� obsaden� !');
/*	define('L_IMPORT_SPIN_FAILED_1',		'S�bor');
	define('L_IMPORT_SPIN_FAILED_2',		'sa nepodarilo naimportova�');
	define('L_IMPORT_SPIN_SUCCEEDED_1',		'S�bor');
	define('L_IMPORT_SPIN_SUCCEEDED_2',		'bol uspe�ne naimportovan�');
	define('L_IMPORT_SPIN_VYBER',			'Vyberte subor, ktory ma byt importovan�:');*/
	define('L_IMPORT',					'Importuj');
	define('L_IMPORT_BELOW_EVID_DATE',	'V importovanom s�bore je platba, ktorej d�tum prip�sania je ni��� ako aktu�lny eviden�n� d�tum.');
	define('L_IMPORT_HB',					'Import');
	define('L_IMPORT_DEBET_SA_NEIMPORTUJE','Debetn� z�znamy sa neimportuj�.');
	define('L_IMPORT_KREDIT_SA_NEIMPORTUJE','Kreditn� z�znamy sa neimportuj�.');
	define('L_IMPORT_ERROR',			'Pri importe tejto polo�ky nastali nasledovn� chyby:');
	define('L_IMPORT_NEEXISTUJUCI_FOND','Zadan� portf�lio v syst�me neexistuje.');
	define('L_IMPORT_NEEXISTUJUCI_UCET','Zadan� ��et pre portf�lio v zadanej mene neexistuje.');
	define('L_IMPORT_NO_IMPORTABLE_RECORDS','V s�bore sa nenach�dzaj� �iadne platby, ktor� by bolo mo�n� importova� do syst�mu.');
	define('L_IMPORT_NOT_IMPORTED',		'Nenaimp. platby');
	define('L_IMPORT_NOT_IMPORTED_ITEMS','Nenaimportovan� platby');
	define('L_IMPORT_OTHER_FONDS',		'Importuj aj z�znamy pre ostatn� porf�li�');
	define('L_IMPORT_RECORDS_WRITTEN',	'Po�et �spe�ne naimportovan�ch z�znamov : %s.');
	define('L_IMPORT_SELECT_FILE',		'Vyberte si s�bor, ktor� chcete naimportova�.');
	define('L_IMPORT_SPIN',				'Import platieb z HB');
	define('L_IMPORT_SPIN_FILE',		'S�bor platieb z HB');
	define('L_IMPORT_SPIN_FILE_OPEN_ERROR','S�bor sa nepodarilo otvori�.');
	define('L_IMPORT_SPIN_INCORRECT_FILE',	'S�bor nem� spr�vny form�t.');
	define('L_IMPORT_VIACNASOBNA_PLATBA',	'Rovnak� platba u� je v syst�me zaevidovan�.');
	define('L_IMPORTED',					'Naimportovan�');
	define('L_INDUSTRY',				'Odvetvie');
	define('L_IND_COLUMNS',				'IND_COLUMNS');
	define('L_INFO_WINDOW',				'Alarm');
	define('L_INITIALCHARGE',			'Vstupn� poplatok');
	define('L_INKASO',				'Inkaso');
	define('L_INKASO_DATE',				'D�tum inkasa');
	define('L_INSERT',				'Prida�');
	define('L_INTEREST',				'�rok');
	define('L_INTERVAL_JE',				'Interval je');
	define('L_INVALID_OPERATION',			'Neplatn� oper�cia');
	define('L_INVENTARIZACIA',				'Inventariz�cia');
	define('L_INVENTARIZACIA_EMISIE',		'Emisie');
	define('L_INVENTARIZACIA_NESPAROVANE',	'Nesp�rovan� platby');
	define('L_INVENTARIZACIA_REDEMACIE',	'Redem�cie');

	define('L_INVENTURA',				'Invent�ra');
	define('L_INVESTICIA',				'Invest�cia');
	define('L_INVESTICIA_FROM',			'Hodnota invest�cie od');
	define('L_INVESTICIA_TO',			'Hodnota invest�cie do');
	define('L_INVESTOVANA_SUMA',		'Investovan� suma');
	define('L_INVESTICNY_ZAMER',		'Investi�n� z�mer');
	define('L_INV_JEDNOTKA',			'Investi�n� jednotka');
	define('L_ISIN',				'ISIN');
	define('L_ISIN_UNIQUE',			'ISIN mus� by� jedine�n�.');
	define('L_ISINCURRRICS',			'ISIN-CURR-RIC');
	define('L_ISTINA',				'Istina');
	define('L_ISTINA_FREQ',				'FQ istiny');
	define('L_ISTINA_NA_UCET',			'Istina na ��et');
	define('L_ISTINA_UROK_NETTO',			'Istina + �rok netto');
	define('L_IS_DAYFIRST',				'true');
	define('L_JEDNOTKA',				'Jednotka');
	define('L_K_DATUMU',				'K d�tumu');
	define('L_KARTA_PODIELNIK',			'Karta klienta');
	define('L_KATEGORIA',				'Kateg�ria');
	define('L_KIND',				'Druh');
	define('L_KLIENT',				'Klient');
	define('L_KLIENT_NAKUPUJE',			'Klient nakupuje');
	define('L_KLIENT_PREDAVA',			'Klient pred�va');
	define('L_KOD',					'K�d');
	define('L_KOD_FONDU',				'K�d fondu');
	define('L_KODOBRATU_NOT_IN_DB',			'Zadan� k�d obratu nie je v datab�ze');
	define('L_KOD_AKTIVA',				'K�d akt�va');
	define('L_KOLATERAL',			'Kolater�l');
	define('L_KOMPLET',				'Komplet');
	define('L_KONFIRMACIA',				'Konfirm�cia');
	define('L_KONFIRMACIA_BONDS',			'Konfirm�cia dlhopisov');
	define('L_KONFIRMACIA_CAS',			'�as konf.');
	define('L_KONFIRMACIA_DEPO',			'Konfirm�cia depozitn�ch certifik�tov');
	define('L_KONFIRMACIA_FONDS',			'Konfirm�cia fondov');
	define('L_KONFIRMACIA_ID',			'��slo konfirm�cie');
	define('L_KONFIRMACIA_KONV',			'Zadanie konverzie');
	define('L_KONFIRMACIA_KTV',			'Konfirm�cia term�novan�ho vkladu');
	define('L_KONFIRMACIA_MENOVEJ_KONV',		'Konfirm�cia menovej konverzie');
	define('L_KONFIRMACIA_SHARES',			'Konfirm�cia akci�');
	define('L_KONFIRMACIA_SPORENIA',		'Konfirm�cia sporenia');
	define('L_KONFIRMACIA_T',			'KONFIRM�CIA');
	define('L_KONFIRMACIA_TLAC_NADPIS',		'Na obstaranie k�py alebo predaja<br>cenn�ch papierov');
	define('L_KONFIRMACIE',				'Konfirm�cie');
	define('L_KONFIRMACIE_KONTOLINE',	'Konfirm�cie KONTO Line');
	define('L_KONFIRMED_BY',			'Konfirmoval');
	define('L_KONFIRMOVAL',				'Konfirmoval');
	define('L_KONFKTV_OPRAVA',			'Oprava - Term�novan� vklad - Konfirm�cia');
	define('L_KONFKTV_STORNO',			'Storno - Term�novan� vklad - Konfirm�cia');
	define('L_KONF_CP',				'Konfirm�cia CP');
	define('L_KONF_KONV',				'Konfirm�cia konverzie');
	define('L_KONF_KTV',				'Konfirm�cia KTV');
	define('L_KONF_NAKUP',				'Zadanie konfirm�cie CP - N�KUP');
	define('L_KONF_PREDAJ',				'Zadanie konfirm�cie CP - PREDAJ');
	define('L_KONF_UHRADA',				'Konfirm�cia �hrady');
	define('L_KONF_ZMENENA_RELOAD',			'Konfirm�cia bola zmenen� in�m u��vate�om. Zadajte rekonfirm�ciu znova.');
	define('L_KONIEC',				'Koniec');
	define('L_KONTO_PODIELNIKA',			'Konto klienta');
	define('L_KONTOLINE',				'KONTO Line');
	define('L_KONTROLA',				'Kontrola');
	define('L_KONTROLA_PRED_UZAVIERKOU','Kontrola pred uz�vierkou');
	define('L_KONVENCIA',				'Konvencia');
	define('L_KONVERZIA',				'Konverzia');
	define('L_KONVERZIE',				'Konverzie');
	define('L_KREDIT',				'Kredit');
	define('L_KS',					'KS');
	define('L_KS_MAJETOK_VS_VOLNE',			'Kusov v majetku / vo�n�ch kusov (mimo rezerv�cie)');
	define('L_KTV',					'Term�novan� vklady');
	define('L_KTV_ATTRIBUTES',			'Atrib�ty term�nov�ch ��tov');
	define('L_KTV_NO',				'��slo term�nov�ho vkladu');
	define('L_KUPON',				'Kup�n');
	define('L_KUPON_SPL',			'Spl. kup�n');
	define('L_KURZ',				'Index');
	define('L_KURZ_KURZ',			'Kurz');
	define('L_KURZREAL',			'Index');
	define('L_KURZ_REAL',			'Kurz');
	define('L_KURZY',				'Indexy');
	define('L_KURZY_BCPB',				'Indexy z BCPB');
	define('L_KURZY_K',				'Indexy k');
	define('L_KURZY_KURZY_K',		'Kurzy k');
	define('L_KURZ_AKTIVA',				'Kurz akt�va');
	define('L_KURZ_MENA',				'Menov� kurz');
	define('L_KURZ_MENA_OLD_DATE',			'Menov� kurz k zadan�mu d�tumu nie je zn�my. Bol pou�it� kurz zo d�a %s !');
	define('L_KURZ_MENA_OLD_DATE_JS1',		'Menov� kurz k zadan�mu d�tumu nie je zn�my. Bol pou�it� kurz zo d�a');
	define('L_KURZ_MENA_OLD_DATE_JS2',		' !');
	define('L_KURZ_NESMIE_BYT_NULOVY',		'Zadan� index mus� by� va�� ako 0');
	define('L_KURZ_PL',				'Index');
	define('L_KURZ_PL_NOT_FOUND',			'Nie je zn�my index.');
	define('L_KURZ_PL_UNKNOWN',				'Nezn�my index.');
	define('L_KURZ_REDEMACIE_KU',			'Index k');
	define('L_KURZ_TO',				'Index k');
	define('L_KUSOV',				'Kusov');
	define('L_KUSOVREAL',				'Po�et');
	define('L_KUSOV_ZREALIZ',			'Kusy zreal.');
	define('L_KUSY',				'Kusy');
	define('L_KU_DNU',				'Ku d�u');
	define('L_LANGUAGE',				'Jazyk');
	define('L_LAST_LOGOUT_IMPROPER',		'U��vate� s t�mto ID sa nekorektne odhl�sil');
	define('L_LEI_KOD','LEI k�d');
	define('L_LEN_NOVE_PL_PO_REDEMACII',		'Nov� PL po v�bere');
	define('L_LIMITNY_KURZ',			'Limitn� kurz');
	define('L_LIMITY',				'Zmluvn� vz�ah');
	define('L_LIMIT_BU_PREKROCENY',			'Na ��te nem�te dostatok prostriedkov! Pokra�ova�?');
	define('L_LIMIT_CENA',				'Limitn� cena');
	define('L_LIMIT_KURZ',				'Limitn� kurz');
	define('L_LIM_KURZ',				'Lim.index');
	define('L_LIST_PODIELNIK',			'Zoznam klientov');
	define('L_LOGGED_USERS',			'Prihl�sen� u��vatelia');
	define('L_LOG_IN',				'Prihlásiť sa');
	define('L_LOT',					'Lot');
	define('L_LOAD',				'Na��ta�');
	define('L_MAINMENU',				'Hlavn� menu');
	define('L_MAIN_MENU',				'Hlavn� menu');
	define('L_MAJETKOVY',				'Majetkov�');
	define('L_MAJETKOVY_PODUCET',			'Majetkov� pod��et');
	define('L_MAJETOK',				'Majetok');
	define('L_MAJETOK_ACCOUNT',			'MU');
	define('L_MAJETOK_FONDU',			'Majetok portf�lia');
	define('L_MAJETOK_KOMPLET',			'Majetok komplet');
	define('L_MAJETOK_STRUKTURA',			'Majetok �trukt�ra');
	define('L_MAJETOK_TOTAL',			'Hodnota majetku protf�li� a spr�vcu');
	define('L_MALO_KUSOV',				'Po�et kusov nesmie by� nulov�');
	define('L_MANAGEMENTFEE',			'Odplata spr�vcu');
	define('L_MANAGER', 				'Mana��r');
	define('L_MANAGERS',				'Mana��ri');
	define('L_MANAZERSKY_POPLATOK',		'Mana��rsky polatok');
	define('L_MANUALY_ADDED',			'Ru�ne pridan�');
	define('L_WORD_MANUAL',				'Manu�l pre pobo�ky vo form�te Microsoft Word');
	define('L_WORD_MANUAL_DOWNLOAD',	'Stiahnite si manu�l pre pobo�ky');
	define('L_MARKET',				'Trh');
	define('L_MATURITY',				'Splatnos�');
	define('L_MAX_FILE_SIZE',		'200000');
	define('L_MAX_VSTUP_INVEST_SPORENIE',		'Max. vstupn� invest�cia (v %)');
	define('L_MAX_DOBA_SPORENIA',		'Max. doba sporenia (v mesiacoch)');
	define('L_MEDIAN',				'Medi�n');
	define('L_MENA',				'Mena');
	define('L_MENA_DEBET',			'Mena debet');
	define('L_MENA_KREDIT',			'Mena kredit');
	define('L_MENA_OBCHODU',		'Mena obchodu');
	define('L_MENO',				'Meno');
	define('L_MENOVA_KONVERZIA',			'Menov� konverzia');
	define('L_MENOVY_KURZ',			'Menov� kurz');
	define('L_MENOVY_KURZ_K',				'Menov� kurz k');	
	define('L_MENOVYPAR',			'Menov� p�r');
	define('L_MENOVYPARNOTEXISTS',	'Menov� p�r neexistuje !!!');
	define('L_MENOVY_PAR',			'Menovy p�r');
	define('L_MESACNA_SPLATKA',			'Mesa�n� spl�tka');
	define('L_MESACNE',				'Mesa�ne');
	define('L_MESSAGE',				'Spr�va');
	define('L_MIMORIADNY_VYNOS',	'Mimoriadny v�nos');
	define('L_MIMORIADNY_VYPIS',	'Mimoriadny v�pis');
	define('L_MIN_CIELOVA_SUMA',		'Min. cie�ov� suma');
	define('L_MIN_DOBA_SPORENIA',		'Min. doba sporenia (v mesiacoch)');
	define('L_MIN_SUM_VYPLATA',		'Min. sum. na v�pl.');
	define('L_MIN_MES_SPLATKA',		'Min. mesa�n� spl�tka (v SKK)');
	define('L_MIN_VSTUP_INVEST_SPORENIE',		'Min. vstupn� invest�cia (v SKK)');
	define('L_MINIMALNA_HODNOTA_REDEMACIE',		'Minim�lna hodnota v�beru');
	define('L_MINIMALNY_ZOSTATOK',	'Minim�lny zostatok');
	define('L_MINIMUM',				'Minimum');
	define('L_MIN_MAX_EQUAL',		'Oper�tor');
	define('L_MIN_NEXT_INVEST',		'Minim�lna �al�ia');
	define('L_MIN_SPOR_INVEST',		'Min. invest�cia sporenia');
	define('L_MIN_VSTUP_INVEST',	'Minim�lna vstupn�');
	define('L_MIN_ZOSTATOK',		'Min. zostatok konta');
	define('L_MISSING',				'Ch�ba');
	define('L_MISSING_REQUIRED_ATTRIBUTE',				'Mus�te si vybra� aspo� jeden z povinn�ch atrib�tov ozna�en�ch �ervenou farbou.');
	define('L_MOBIL_NUMBER',		'Mobil');
	define('L_MODUS',				'Modus');
	define('L_MODIFIED_BUSINESS_DATE',	'Modifikovan� obchodn� de�');
	define('L_MONITORING',			'Monitoring');
	define('L_MONITORING_RIZIKA',	'Monitoring rizika');
	define('L_MONITORING_OBMEDZENI',	'Monitoring obmedzen�');
	define('L_MONTH',				'Mesiac');
	define('L_MSG0_DATA_CHANGED',			'Oper�cia neprebehla, �daje boli zmenen� in�m u��vate�om');
	define('L_MSG_FROM',			'Spr�va od');
	define('L_MSG_ODHLASENIE',		'Boli ste odhl�sen� zo syst�mu.');
	define('L_MSG_USER_KILLED',		'Va�e pripojenie bolo odhl�sen� administr�torom!');
	define('L_MSG_VELA_ZAZNAMOV_MTOTAL',		'Ve�a z�znamov v majetku total');
	define('L_MSP',					'MsP');
	define('L_MUZ',					'Mu�');
	define('L_NA',					'Na');
	define('L_NEDOSTATOCNA_SUMA_NA_UCTE',	'Na ��te je nedostato�n� suma');
	define('L_NA_UCTE_JE',			'Na ��te je: ');
	define('L_NACITANIE',			'Na��tanie');
	define('L_NACITANIE_PLATIEB',	'Na��tanie platieb');
	define('L_NACITANIE_UDAJOV',	'Na��ta� �daje?');
	define('L_NACITAT_KURZY',		'Na��ta� kurzy');
	define('L_NACITAT_PREDCHADZAJUCE_KURZY',		'Predch. kurzy');
	define('L_NAD_5000000',			'nad 5 mil.');
	define('L_NAD_65',				'66 a viac rokov');
	define('L_NAKLAD',				'N�klad');
	define('L_NAKLADY',				'N�klady');
	define('L_NAKL_NADOBUD_SPOLU', 			'N�kl. nadobud. spolu ');
	define('L_NAKUP',				'N�kup');
	define('L_NAME',				'N�zov');
	define('L_NAMONITORING',			'Zahrn�� vybran� polo�ky do monitoringu rizika?');	
	define('L_NAOZAJ_CHCETE_VYMAZAT_AUTENTIFIKACIU',	'Naozaj chcete vymaza� autentifik�ciu?');
	define('L_NAOZAJ_CHCETE_VYMAZAT_DOKUMENT', 	'Naozaj chcete vymaza� dokument?');
	define('L_NAOZAJ_CHCETE_VYMAZAT_PARTNERA', 	'Naozaj chcete zmaza� partnera?');
	define('L_NAOZAJ_CHCETE_VYMAZAT_UCET', 		'Naozaj chcete vymaza� ��et?');
	define('L_NAOZAJ_CHCETE_VYMAZAT_UROKOVU_SADZBU','Naozaj chcete vymaza� �rokov� sadzbu ?');
	define('L_NAOZAJ_CHCETE_VYMAZAT_UZIVATELA',	'Naozaj chcete vymaza� u��vate�a?');
	define('L_NAOZAJ_CHCETE_VYMAZAT_ZAZNAM',	'Naozaj chcete vymaza� z�znam?');
	define('L_NAOZAJ_CHCETE_ZMENIT_STAV_PODIELKNIKA',	'Naozaj chcete zmeni� stav podielnika');
	define('L_NAROK_NA_VYPLATU_VYNOSU',		'D�tum vzniku v�nosu');
	define('L_NAROK_NA_KUPON',				'N�rok na kup�n');
	define('L_NAPOVEDA',		'N�poveda');
	define('L_NASPORENA_SUMA',				'Nasporen� suma');
	define('L_NATIPOVANE',			'Natipovan�');
	define('L_NATIPUJ',				'Natipuj');
	define('L_NATIPOVANIE',			'Natipovanie');
	define('L_NATIPOVANIE_DOSLYCH_PLATIEB',		'Natipovanie do�l�ch platieb');
	define('L_NATIPOVAT',			'Natipova�');
	define('L_NAV',					'NAV');
	define('L_NAZOV',				'N�zov');
	define('L_NAZOV_CENNEHO_PAPIERA',		'N�zov cenn�ho papiera');
	define('L_NAZOV_CP',			'N�zov CP');
	define('L_NAZOV_FONDU',			'N�zov portf�lia');
	define('L_NAZOV_FONDU_',		'N�zov fondu');
	define('L_NAZOV_PARTNERA',		'N�zov partnera');
	define('L_NAZOV_SUBJEKTU',		'N�zov subjektu');
	define('L_NA_DORUCITELA',		'Na doru�ite�a');
	define('L_NA_MENO',				'Na meno');
	define('L_NA_OVERENIE',			'Na overenie');
	define('L_NA_VERIFIKACIU',		'Na verifik�ciu');
	define('L_NDF',					'NDF');
	define('L_NEAKCEPTUJEM_CP',		'Syst�m neakceptuje zvolen� CP');
	define('L_NEAUTORIZOVANY',		'Neautorizovan� podpis');
	define('L_NECHAT_MIN_ZOSTATOK',	'Necha� minim�lny zostatok');
	define('L_NECHAT_MIN_ZOSTATOK_SHORT',		'Min. zostatok');
	define('L_NEDEFINOVANE',		'Nedefinovane');
	define('L_NEGATIVE',			'Negat�vna');
	define('L_NEIDENTIFIKOVANE',	'Neidentifikovan�');
	define('L_NEOCENENA_UZAVIERKA', 		'Neocenen� uz�vierka');
	define('L_NEPATRIACE_K_POBOCKAM',					'Nepatriace k pobo�k�m');
	define('L_NEPOTVRDENY_DATUM',			'Nepotvrden� d�tum');
	define('L_NEPODARILO_SA_ZAPISAT_AUTENTIFIKACNE_UDAJE',	'Nepodarilo sa zap�sa� autentifika�n� �daje!');
	define('L_NESPAROVANE',			'Nesp�rovan�');
	define('L_NESPAROVATELNA',		'Nesp�rovate�n�');
	define('L_NESPAROVATELNA_PLATBA','Nespr�rovate�n� platba');
	define('L_NESPAROVATELNE',		'Nespr�rovate�n�');
	define('L_NESPAROVATELNE_PLATBY','Nespr�rovate�n� platby');
	define('L_NESPEC_PLATBY_POHLADAVKA','Ne�pecifikovan� platby - poh�ad�vka');
	define('L_NESPEC_PLATBY_ZAVAZOK','Ne�pecifikovan� platby - z�v�zok');
	define('L_NESPEC_POHLADAVKA','Ne�pecifikovan� poh�ad�vka');
	define('L_NESPEC_ZAVAZOK','Ne�pecifikovan� z�v�zok');
	define('L_NESPRAVNY_POCET_SPLATOK',		'Po�et spl�tok mus� by� cel� ��slo v rozsahu stanovenom parametrami sporenia dan�ho fondu.');
	define('L_NESPRAVNA_VSTUP_INVEST',		'Vstupn� invest�cia mus� by� v rozsahu stanovenom parametrami sporenia dan�ho fondu.');
	define('L_NESPRAVNA_MES_SPLATKA',			'Mesa�n� spl�tka mus� dosahova� minim�lnu po�adovan� hodnotu.');	
	define('L_NESPRAVNA_CIELOVA_SUMA',			'Cie�ov� suma mus� dosahova� minim�lnu po�adovan� hodnotu.');	
	define('L_NESTANDARDNY',		'Ne�tandardn�');
	define('L_NEVYBAVENA',			'Nevybaven�');
	define('L_NEZASIELAT',			'Nezasiela�');
	define('L_NEW',					'Nov�');
	define('L_NEW_ACTIVE_DEPOZITAR','Nov� akt�vny depozit�r');
	define('L_NEW_PASSWORD',		'Nov� heslo');
	define('L_NEXT_PAGE',			'�al�ia str�nka');
	define('L_NEZREALIZOVANA_KONF',	'Nezrealizovan�');
	define('L_NEZREALIZOVANY_POKYN',	'Nezrealizovan� pokyn');
	define('L_NEUDANE',				'Neudan�');
	define('L_NIEKTORE_POLOZKY',	'Niektor� polo�ky');
	define('L_NIZKY_DAT_OBCH',		'N�zky d�tum obchodu!');
	define('L_NIZKY_KURZ',			'N�zky kurz');
	define('L_NO',					'Nie');
	define('L_NO_USER',			'Ani jeden pou��vate� nevyhovuje zadan�m krit�ri�m.');
	define('L_NO_UHRADA_RECORDS',	'Niet viacej z�znamov na �hradu.');
	define('L_NOMINAL',				'Nomin�l');
	define('L_NOMINAL_EUR',				'Nomin�l EUR');
	define('L_NOMINALNA_HODNOTA',	'Nomin�lna hodnota');
	define('L_NOMINAL_DEN',			'Nomin�l v mene denomin�cie');
	define('L_NOMINAL_TRANS',		'Nomin�l v transak�nej mene');
	define('L_NONE',				'�iadna');
	define('L_NOTE',				'Pozn�mka');
	define('L_NOT_CONFIRMED',		'Nepotvrden�');
	define('L_NOT_FOND_MANAGER',		'Dostupn� mana��ri');
	define('L_NOT_ORIGINAL_EMID_USED',		'Syst�m neakceptoval navrhovan� ��slo vkladu. Pre ��slo vkladu syst�m pou�il nasleduj�ce vo�n�.');
	define('L_NOVA',					'Nov�');
	define('L_NOVE',					'Nov�');
	define('L_NOVE_AKTIVUM',			'Nov� akt�vum');
	define('L_NOVE_PODIELY',			'Nov� podiely');
	define('L_NO_ATTRIBUTES_E',			'�iadne atrib�ty');
	define('L_NO_CHANGE',				'Nebola zmenen� �iadna hodnota!');
	define('L_NO_CLEARING_CODES',		'�iadne clearingov� k�dy');
	define('L_NO_EMISIA_RECORDS',		'V syst�me nie s� evidovan� �iadne z�znamy pripraven� na emisiu.');
	define('L_NO_ISINCURRRICS',			'�iadne zadan� RIC');
	define('L_NO_ITEM_CHECKED',			'Ni� nebolo vybrat�');
	define('L_NO_PODIELNIK',			'Klient v syst�me neexistuje.');
	define('L_NO_RECORD',				'�iadny z�znam');
	define('L_NO_RECORDS',				'�iadne z�znamy');
	define('L_NO_RECORDS_IN_SEKTOR',	'Neexistuj� �iadne z�znamy o sektoroch');
	define('L_NO_RECORDS_FOUND',		'�iadny z�znam nevyhovuje zadan�m krit�ri�m.');
	define('L_NO_REEMISIA_RECORDS',		'V syst�me nie s� evidovan� �iadne z�znamy pripraven� na v�ber.');
	define('L_NO_RIGHT',				'Nem�te opr�vnenie');
	define('L_NO_SIGNATURE',			'Podpis nie je zadan�.');
	define('L_NULOVY_OBJEM_EMISIE',			'Nulov� objem emisie');
	define('L_NUMBER',				'��slo');
	define('L_OBCHODNIK_CP', 			'Obchodn�k CP');
	define('L_OBCHODNY_DENNIK', 			'Obchodn� denn�k');
	define('L_OBDOBIE',				'Obdobie');
	define('L_OBJEM',				'Objem');
	define('L_OBJEMY_A_POCTY_PODIELOV',		'Po�ty vkladov');
	define('L_OBJEMY_POPLATKOV',			'Objemy poplatkov');
	define('L_OBJEM_EMISIE',						'Objem emisie');
	define('L_OBJEM_PODIELOV',			'Objemy podielov');
	define('L_OBJEM_VYBERU',			'Objem v�beru');
	define('L_OBMEDZENIE_P33_7B1',			'V majetku spr�vcovskej spolo�nosti a v�etk�ch podielov�ch fondov m��e by� max. ');
	define('L_OBMEDZENIE_P33_7B2',			'% s��tu menovit�ch hodn�t akci� vydan�ch jedn�m eminentom.');
	define('L_OBRATY_PO_UZAVIERKE', 		'Obraty po uz�vierke');
	define('L_OBSTARANIE_TRANSAKCIE_CP', 		'Obstaranie transakcie s CP');
	define('L_OCAKAVANA_PLATBA',			'O�ak�van� platba');
	define('L_OCAKAVANE_PLATBY',			'O�ak�van� platby');
	define('L_OCAKAVANE_REKONF_KONV',		'O�ak�van� rekonfirm�cie konverzi�');
	define('L_OCAKAVANE_UHRADY',			'O�ak�van� �hrady');
	define('L_OCAKAVANY_NAV',			'O�ak�van� NAV');
	define('L_OCENENIE',				'Ocenenie');
	define('L_OCENENIE_ESTE_NEPREBEHLO',		'Ocenenie e�te neprebehlo');
	define('L_OCENENIE_K',				'Ocenenie k');
	define('L_OCENENY_FOND',			'Ocenen� portf�lio');
	define('L_OD',					'Od');
	define('L_OD_ZACIATKU_RIADENIA',		'Od za�iatku riadenia');
	define('L_ODBLOKOVANIE_PRIHLASENIA',	'Odblokovanie prihlasovania');
	define('L_ODCHADZAJUCE',			'Odchadzaj�ce');
	define('L_ODOSLANE',				'Odoslan�');
	define('L_ODOSLANIE_CP_VERIF',			'Odoslanie konfirm�cie CP na verifikaciu depozit�rovi');
	define('L_ODOSLANIE_KONV_VERIF',		'Odoslanie konfirm�cie konverzie na verifikaciu depozit�rovi');
	define('L_ODOSLAT_VERIF_BONDS',			'Odoslanie dlhopisov na verifik�ciu');
	define('L_ODOSLAT_VERIF_DEPO',			'Odoslanie depozitn�ch certifik�tov na verifik�ciu');
	define('L_ODOSLAT_VERIF_FONDS',			'Odoslanie fondov na verifik�ciu');
	define('L_ODOSLAT_VERIF_KONV',			'Odoslanie konverzi� na verifik�ciu');
	define('L_ODOSLAT_VERIF_SHARES',		'Odoslanie akci� na verifik�ciu');
	define('L_ODOVZDANE',					'Odovzdan�');
	define('L_ODPLATA',						'Odplata');
	define('L_ODPLATA_CELKOM',				'Odplata celkom');
	define('L_ODPLATA_DEPOZITAR',			'Odplata za v�kon �innosti depozit�ra');
	define('L_ODPLATA_DEPOZITARA',			'Poplatok za riadenie');
	define('L_ODPLATA_DEPOZITARA_DB',		'odplata depozit�ra');
	define('L_ODPLATA_DEPOZITARA_SHORT',	'Odpl. za spr�vu');
	define('L_ODPLATA_DEPOZITARA_DPH_ERROR','Nie je mozne zobrazit odplatu depozitara pre dane obdobie, nakolko zasahuje do obdobi s roznymi urovnami DPH. Nova uroven DPH je platna od %s.');
	define('L_ODPLATA_SPOLU',				'Odplata spolu');
	define('L_ODPLATA_SPRAVCU',				'Poplatok za spr�vu');
	define('L_ODPLATA_SPRAVCU_DB',			'odplata spr�vcu');
	define('L_ODPLATA_SPRAVCU_SHORT',		'Odpl. za riadenie');
	define('L_ODPLATA_ZAKLAD',				'Odplata z�klad');
	define('L_ODPLATY',						'Odplaty');
	define('L_ODVETVIE',					'Odvetvie');
	define('L_OK',							'OK');
	define('L_OLD_KURZ_USED',				'Kurz na dne�n� de� e�te nebol zadan�. Bol pou�it� kurz zo d�a %s !');
	define('L_OMESKANIE',					'Ome�kanie');
	define('L_OMESKANE_SPORENIA',			'Sporenie - zoznam');
	define('L_ONE_DAY',						'1 de�');
	define('L_ONE_MONTH',					'1 mesiac');
	define('L_ONE_WEEK',					'1 t��de�');
	define('L_ONE_YEAR',					'12 mesiacov');
	define('L_ONLY_ONE_DEPOZITAR_ACTIVE',	'Iba jeden depozit�r m��e by� akt�vny !!!');
	define('L_OP',							'Ob�iansky preukaz');
	define('L_OOU_AOK',						'AOK');
	define('L_OOU_NEW_CONDITIONS',			'Nov� obchodn� podmienky');
	define('L_OOU_NIE',						'NEPOTVRDEN�');
	define('L_OOU_OK',						'OK');
	define('L_OOU_TO_DATE',					'k d�tumu');
	define('L_OPENED',						'Otvoren�');
	define('L_OPERACIA_PREBEHLA_OK',		'Oper�cia prebehla v poriadku');
	define('L_OPERACIA_NEPREBEHLA',			'Oper�cia neprebehla');
	define('L_OPERACIA_PENAZNY',			'Oper�cia na pe�. trhu');
	define('L_OPERATOR',					'Oper�tor');
	define('L_OPRAV',						'Oprav');
	define('L_OPRAVA',						'Oprava');
	define('L_OPRAVA_DOSLYCH_PLATIEB',		'Oprava do�l�ch platieb');
	define('L_OPRAVA_KURZOV',			'Oprava kurzov');
	define('L_OPRAVA_REKONF',			'Oprava rekonfirm�cie term�nov�ho vkladu');
	define('L_OPRAVA_REKONFIRMACIE_POPLATKU',	'Oprava');
	define('L_OPRAVA_REKONF_CP',			'Oprava rekonfirm�cie CP');
	define('L_OPRAVA_UHRADA',			'Oprava pr�kazov na �hradu');
	define('L_OPRAVIT_ZVOLENE_POPLATKY',		'Opravi� zvolen� poplatky ?');
	define('L_OPRAVNE',				'Opravn�');
	define('L_OPRAVOVANE',				'Opravovan�');
	define('L_OPRAV_KONF_BONDS',			'Oprava konfirm�cie dlhopisu');
	define('L_OPRAV_KONF_CP',			'Oprava konfirm�cie CP');
	define('L_OPRAV_KONF_DEPO',			'Oprava konfirm�cie depozitn�ho certifik�tu');
	define('L_OPRAV_KONF_FONDS',			'Oprava konfirm�cie fondu');
	define('L_OPRAV_KONF_KONV',			'Oprava konfirm�cie konverzie');
	define('L_OPRAV_KONF_SHARES',			'Oprava konfirm�cie akcie');
	define('L_OPRAV_REKONF_BONDS',			'Oprava rekonfirm�cie dlhopisu');
	define('L_OPRAV_REKONF_CP',			'Oprava rekonfirm�cie CP');
	define('L_OPRAV_REKONF_DEPO',			'Oprava rekonfirm�cie depozitn�ho certifik�tu');
	define('L_OPRAV_REKONF_FONDS',			'Oprava rekonfirm�cie fondu');
	define('L_OPRAV_REKONF_KONV',			'Oprava rekonfirm�cie konverzie');
	define('L_OPRAV_REKONF_SHARES',			'Oprava rekonfirm�cie akcie');
	define('L_OPRAVNENA',					'Opr�vnen� osoba');
	define('L_ORG_UNIT',					'Organiza�n� jednotka');
	define('L_ORG_UNIT_DATA',				'�daje organiza�nej jednotky');
	define('L_ORG_UNIT_SUPER',				'Nadrad.org. jednotka');
	define('L_ORG_UNIT_PERCENTAGE',			'Rozdelenie poplatkov');
	define('L_ORG_UNIT_TYPE',				'Typ');
	define('L_ORG_UNIT_INTEREST',			'Interest');
	define('L_ORG_UNIT_UNDER',				'Podriaden�');
	define('L_OR',							'Alebo');
	define('L_ORADFCOMP',				'YYYYMMDD');		// Default date format for DATE compare - iba pre porovnanie dni
	define('L_ORADFLOCALE',				'DD.MM.YYYY');		// Default LOCALE date format for ORACLE
	define('L_OSOBNE',					'Osobne');
	define('L_OSTATNE',				'Ostatn�');
	define('L_OSTATNE_UDAJE',				'Ostatn� �daje');
	define('L_OTHER',				'In�');
	define('L_OUTCHARGE',				'V�stupn� poplatok');
	define('L_OVERIT',					'Overi�');
	define('L_OVERENIA',				'Overenia');
	define('L_PARAGRAPH331A1',			'� 33 ods.2 - hodnota CP emitovan�ch jedn�m �t�tom alebo so z�rukou jedn�ho �t�tu nesmie tvori� viac ako ');
	define('L_PARAGRAPH331A2',			'% hodnoty majetku vo fonde.');
	define('L_PARAGRAPH331B1',			'� 33 ods.1 - celkov� hodnota CP emitentov s viac ako 5-mi % na hodnote majetku vo fonde nesmie prekro�i� ');
	define('L_PARAGRAPH331B2',			'% hodnoty majetku fondu.');
	define('L_PARAGRAPH3321',			'� 33 ods.1 - hodnota CP rovnak�ho emitenta nesmie tvori� viac ako ');
	define('L_PARAGRAPH3322',			'% na hodnote majetku vo fonde.');
	define('L_PARAGRAPH3331',			'� 33 ods.3 - hodnota HZL vydan�ch jednou bankov v SR nesmie tvori� viac ako ');
	define('L_PARAGRAPH3332',			'; na hodnote majetku fondu.');
	define('L_PARTNER', 				'Partner');
	define('L_PARTNERID_UZ_EXISTUJE', 		'Toto ��slo partnera je u� pou�it�');
	define('L_PARTNERS', 				'Partneri');
	define('L_PAS',					'Pas');
	define('L_PASMO_PODLA_KLIENTOV',	'P�smo pod�a klientov');
	define('L_PASMO_PODLA_VKLADOV',	'P�smo pod�a vkladov');
	define('L_PASSPORT',				'Pas');
	define('L_PAYMENTS',				'Poplatky');
	define('L_PCT',					'ks');
	define('L_PENAZNY',				'Pe�a�n�');
	define('L_PENAZNY_PODUCET',			'Pe�a�n� pod��et');
	define('L_PENIAZE',				'Peniaze');
	define('L_PENIAZE_NA_CESTE',			'Peniaze na ceste');
	define('L_PEN_UCET_PARTNERA',			'Pe�a�n� ��et partnera');
	define('L_PERCENTO',				'Percento');
	define('L_PERCENTO_POPLATKU',			'Percento poplatku');
	define('L_PERCENTO_INVESTICIE',			'Percento invest�cie');
	define('L_PERCENTUAL_RESTRICTION',		'Percentu�lne obmedzenie');
	define('L_PERCENTUALNY_NARAST_TRHOVEJ_HODNOTY',	'Percentu�lny n�rast trhovej hodnoty');
	define('L_PERSONAL_DATA',			'Osobn� �daje');
	define('L_PHONENUMBER',				'Telef�n');
	define('L_PHONE_NUMBER',			'Telef�nne ��slo');
	define('L_PIECES',				'ks');
	define('L_PL',					'PL');
	define('L_PLACE',				'N�zov');
	define('L_PLATBA',				'Platba');
	define('L_PLATBA_FINANCNE_VYNOSY',		'Platba finan�n�ch v�nosov');
	define('L_PLATBA_TILL',				'Platba do');
	define('L_PLATBA_VKLADU_AKCIONARA',		'Platba vkladu akcion�ra');
	define('L_PLATBY',				'Platby');
	define('L_PLATBY_ALL',				'V�etky');
	define('L_PLATBY_FONDU',			'Platby portf�liu');
	define('L_PLATBY_IDENTIFIKOVANE',		'Identifikovan�');
	define('L_PLATBY_NA_OVERENIE',			'Identifikovan� s nutn�m overen�m');
	define('L_PLATBY_NEIDENTIFIKOVANE',		'Neidentifikovan�');
	define('L_PLATI_PRE',					'Plat� pre');
	define('L_PLATI_PRE_DALSIE_EMISIE',		'Plat� pre dal�ie emisie');
	define('L_PLATNOST_DO',					'Platnos� do');
	define('L_PLATNOST_OD',					'Platnos� od');
	define('L_PLATNOST_POKYNU',				'Platnos� pokynu');
	define('L_PLEASE_SET_MENOVY_PAR',		'Zadajte kurz menov�ho p�ru :');
	define('L_PLNA_MOC',					'Doklad odovzdan�');
	define('L_PLNENIE',					'Plnenie');
	define('L_POBOCKA',				'Pobo�ka');
	define('L_POBOCKY',				'Pobo�ky');
	define('L_POBOCKA_BOLA_ZMAZANA',		'Pobo�ka bola zmazan�');
	define('L_POBOCKA_NEMOZE_BYT_ZMAZANA',		'Pobo�ka nem��e by� zmazan� - obsahuje agent�ry');
	define('L_POCET',						'Po�et');
	define('L_POCET_EMISII',						'Po�et emisi�');
	define('L_POCET_EMITOVANYCH_CELKOM',	'Po�et emitovan�ch celkom');
	define('L_POCET_EXPORTOV',						'Po�et exportov');
	define('L_POCET_KLIENTOV',				'Po�et klientov');
	define('L_POCET_KS',					'Po�et kusov');
	define('L_POCET_KS_3',					'Po�et kusov<br>pokyn - zreal.- zost.');
	define('L_POCET_KUSOV',					'Po�et kusov');
	define('L_POCET_LISTOV',				'Po�et listov');
	define('L_POCET_MUZOV',					'Po�et mu�ov');
	define('L_POCET_PODIELOVYCH_LISTOV',	'Po�et podiel. listov');
	define('L_POCET_POZASTAVENYCH_SPORENI',	'Po�et pozastaven�ch sporen�');
	define('L_POCET_PRIJATYCH_PLATIEB',		'Po�et prijat�ch platieb');
	define('L_POCET_REDEMOVANYCH_CELKOM',	'Po�et redemovan�ch celkom');
	define('L_POCET_SPLATENYCH_ISTIN', 		'Po�et splaten�ch ist�n');
	define('L_POCET_SPLATENYCH_UROKOV',		'Po�et splaten�ch �rokov');
	define('L_POCET_SPLATOK',				'Po�et spl�tok');
	define('L_POCET_SPORENI_S_JEDNYM_ONESKORENIM',	'Po�et sporen� s jedn�m oneskoren�m');
	define('L_POCET_SPORENI_S_DVOMA_ONESKORENIAMI',	'Po�et sporen� s dvoma oneskoreniami');
	define('L_POCET_SPORENI_S_TROMA_ONESKORENIAMI',	'Po�et sporen� s troma oneskoreniami');
	define('L_POCET_UKONCENYCH_SPORENI',	'Po�et ukon�en�ch sporen�');
	define('L_POCET_V_OBEHU',				'Po�et v obehu');
	define('L_POCET_ZIEN',					'Po�et �ien');
	define('L_POCET_ZOSTAVAJUCICH_PLATIEB',	'Po�et zost�vaj�cich platieb');
	define('L_POCET_ZOSTAV_PLATIEB',	'Po�et zost�v. platieb');
	define('L_POCET_ZIVYCH_SPORENI',		'Po�et �iv�ch sporen�');
	define('L_POCITAT',						'Po��ta�');
	define('L_PODIELNICI',					'Klient');
	define('L_PODIELNICI_FONDU',			'Podielnici fondu');
	define('L_PODIELNIK',					'Klient');
	define('L_PODIELNIK_DATA',				'�daje o klientovi');
	define('L_PODIELNIK_FORM',				'Karta klienta');
	define('L_PODIELNIK_MAIN_TITLE',		'Klient');
	define('L_PODIELNIK_NAME',				'Meno/N�zov');
	define('L_PODIELNIK_TO_ARCHIVE_ERROR',	'Klienta nie je mo�n� presun�� do arch�vu. Na konte klienta s� evidovan� akt�va.');
	define('L_PODIELNIK_VKLAD',				'Vklad klienta');
	define('L_PODIELNIK_VYDANIE_OPRAVA',	'Oprava vydania');
	define('L_PODIELNIK_ZMENY',				'Zmeny klienta');
	define('L_PODIELOVE_FONDY',				'Podielov� fondy');
	define('L_PODIELOVE_LISTY',				'Podielov� listy');
	define('L_PODIELOVE_LISTY_DO_USCHOVY',	'Podielov� listy mus�te najsk�r vlo�i� do �schovy!');
	define('L_PODIELOVY_FOND',				'Podielov� fond');
	define('L_PODIELOVY_LIST',				'Invest�cie');
	define('L_PODIELOVY_LIST_JE_BLOKOVANY',	'Podielov� list je blokovan�.');
	define('L_PODIELOVY_LIST_JE_BLOKOVANY_ZIADOSTOU_O_PRESUN',	'Podielov� list je blokovan� �iados�ou o presun.');
	define('L_PODIELOVY_LIST_JE_BLOKOVANY_ZIADOSTOU_O_PREVOD',	'Podielov� list je blokovan� �iados�ou o prevod.');
	define('L_PODIELOVY_LIST_JE_BLOKOVANY_ZIADOSTOU_O_REDEMACIU',	'Podielov� list je blokovan� �iados�ou o v�ber.');
	define('L_PODIELOV_NIZSIA',			'Po�adovan� po�et podielov je ni��� ako je aktu�lny po�et vybran� na redem�ciu.');
	define('L_PODIELOV_VYSSIA',			'Po�adovan� po�et podielov je vy��� ako je aktu�lny po�et vybran� na redem�ciu.');
	define('L_PODIELY_V_OBEHU', 'Podiely v obehu');
	define('L_POISTENIE',			'Poistenie');
	define('L_PODLA_A',				'atrib�tov � 32');
	define('L_PODLA_E',				'emitentov � 33, ods.1');
	define('L_PODLA_FILIALKY',				'Pod�a fili�lky');
	define('L_PODLA_FONDU',				'Pod�a portf�lia');
	define('L_PODLA_H',				'HZL � 33, ods.2');
	define('L_PODLA_K',				'kumulat�vne � 33, ods.7b');
	define('L_PODLA_OPERATORA',			'Pod�a oper�tora');
	define('L_PODLA_PASMA_JEDNOTKOVO',				'Pod�a p�sma - jednotkovo');
	define('L_PODLA_PASMA_KUMULATIVNE',				'Pod�a p�sma - kumulat�vne');
	define('L_PODLA_POBOCKY',			'Pod�a pobo�ky');
	define('L_PODLA_S',				'�t�tne CP � 33, ods.2');
	define('L_PODMIENKY_NA_NAKUP_PREDAJ',		'Podmienky na n�kup alebo predaj CP');
	define('L_PODPIS_AUTORIZOVANY',		'Podpis autorizovan�');
	define('L_PODPIS_NEAUTORIZOVANY',		'Podpis neautorizovan�');
	define('L_PODPIS_ZADANY',			'Podpis zadan�');
	define('L_POHLADAVKY',				'Poh�ad�vky');
	define('L_POHLAVIE',				'Pohlavie');
	define('L_POKLES_FOND_MSG1', 			'Cena portf�lia klesla o viac ako jednu tretinu oproti priemernej cene za posledn�ch 6 mesiacov');
	define('L_POKYN',				'Pokyn');
	define('L_POKYNY',				'Pokyny');
	define('L_POKYN_BONDS',				'Pokyn na n�kup/predaj dlhopisov');
	define('L_POKYN_CP_TLAC',			'Tla� pokynu CP');
	define('L_POKYN_DEPO',				'Pokyn na n�kup/predaj depozitn�ch certifik�tov');
	define('L_POKYN_FONDS',				'Pokyn na n�kup/predaj fondov');
	define('L_POKYN_KONV',				'Pokyn na konverziu');
	define('L_POKYN_NO',				'��slo pokynu');
	define('L_POKYN_SHARES',			'Pokyn na n�kup/predaj akci�');
	define('L_POLOZKY_HVUCTOVANIA',			'Polo�ky ��tovania');
	define('L_POOL',				'Pool');
	define('L_POOLING',				'Pooling');
	define('L_POOL_PREHLAD',		'Pool - preh�ad');
	define('L_POPIS',				'Popis');
	define('L_POPLATKY',				'Poplatky');
	define('L_POPLATKY_TRANSAKCIE_SPOLU', 'Poplatky za tran. spolu');
	define('L_POPLATKY_VYSPORIADANIE_SPOLU','Poplatky za vysp. spolu');
	define('L_POPLATKY_A_ODPLATY',			'Poplatky a odplaty');
	define('L_POPLATKY_FONDU',			'Poplatky portf�liu');
	define('L_POPLATKY_FOND',			'Odplata spr�vcu - fond');
	define('L_POPLATKY_SPRAVCA',		'Odpl. spr�vcu a vst.popl. - spr�vca');
	
	define('L_POPLATOK',				'Poplatok');
	define('L_MAX_POPLATOK',			'Max. poplatok');
	define('L_POPLATOK_FROM',			'Rozp�tie poplatku od');
	define('L_POPLATOK_FROM_IS_LESS',		'Horna hranica percenta poplatku nem��e by� men�ia ako doln�');
	define('L_POPLATOK_NIE_JE_ZAPLATENY',	'Poplatok nie je zaplaten�!');
	define('L_POPLATOK_PERCENT',			'Poplatok v %');
	define('L_POPLATOK_PRESTUPNY',			'Prestupn� poplatok');
	define('L_POPLATOK_TO',				'Rozp�tie poplatku do');
	define('L_POPLATOK_VSTUPNY',			'Vstupn� poplatok');
	define('L_POPLATOK_VYSTUPNY',			'V�stupn� poplatok');
	define('L_POPLATOK_ZA_OPER_PENAZNY_TRH',			'Poplatok za oper�ciu na pe�a�nom trhu');
	define('L_POPLATOK_ZA_PREVOD_ZAPLATENY',		'Poplatok za prevod PL zaplaten�');
	define('L_POPLATOK_ZA_PREVZATIE_ZAPLATENY',		'Poplatok za prevzatie PL zaplaten�');
	define('L_POPLATOK_ZA_VRATENIE_ZAPLATENY',		'Poplatok za vr�tenie PL zaplaten�');
	define('L_POPLATOK_ZA_VYPIS_IAM',			'Poplatok za v�pis z IAM Konta');
	define('L_POPLATOK_VYNOS',				'Poplatok za v�nos');
	define('L_POPLATOK_VYNOS_CHYBA_MIERA',				'Pre uplynul� rok nie je zadan� ref.miera v�nosnosti');

	define('L_POPLATOK_OPERACIA_PENAZNY_DB',			'poplatok operacia pen.trh');
	define('L_POPLATOK_MENOVA_KONVERZIA_DB',			'poplatok menova konverzia');
	define('L_POPLATOK_OBSTARANIE_TRANSAKCIE_CP_DB',			'poplatok obstaranie transakcie cp');
	define('L_POPLATOK_VYSPORIADANIE_TRANSAKCIE_CP_DB',			'poplatok vysporiadanie transakcie cp');
	define('L_POPLATOK_MIMORIADNY_VYPIS_DB',			'poplatok mimoriadny vypis');
	define('L_POPLATOK_VYNOS_DB',			'poplatok vynos');

	define('L_POPLATOK_OPERACIA_PENAZNY_LONG',			'Poplatok za oper�ciu na pe�.trhu');
	define('L_POPLATOK_MENOVA_KONVERZIA_LONG',			'Poplatok za menov� konverziu');
	define('L_POPLATOK_OBSTARANIE_TRANSAKCIE_CP_LONG',			'Poplatok za obstaranie transakcie CP');
	define('L_POPLATOK_VYSPORIADANIE_TRANSAKCIE_CP_LONG',			'Poplatok za vysporiadanie transakcie CP');
	define('L_POPLATOK_MIMORIADNY_VYPIS_LONG',			'Poplatok za mimoriadny v�pis');
	define('L_POPLATOK_VYNOS_LONG',			'Poplatok z v�nosu');

	define('L_POPLATOK_OPERACIA_PENAZNY_SHORT',			'KTV');
	define('L_POPLATOK_MENOVA_KONVERZIA_SHORT',			'Konverzia');
	define('L_POPLATOK_OBSTARANIE_TRANSAKCIE_CP_SHORT',			'Obst. CP');
	define('L_POPLATOK_VYSPORIADANIE_TRANSAKCIE_CP_SHORT',			'Vysp. CP');
	define('L_POPLATOK_MIMORIADNY_VYPIS_SHORT',			'Mim. vypis');
	define('L_POPLATOK_VYNOS_SHORT',			'Vynos');
	define('L_POPLATOK_ZA_TRAN','Popl. za trans.');
	define('L_POPLATOK_ZA_VYSP', 'Popl. za vysp.');

	define('L_POPLATOK_ZAUCTOVANY',			'Poplatok bol za��tovan�.');
	define('L_POPLSPRAVARIADENIE', 'Poplatky za riadenie a spr�vu');
	define('L_POP_SADZBA',				'Sadzba');
	define('L_PORADOVE_CISLO', 			'P�.');
	define('L_PORT_VKLAD_CP', 			'Vklad CP');
	define('L_PORT_VYBER_CP', 			'V�ber CP');
	define('L_PORTFOLIO_MANAGER',		'Portf�lio manager');
	define('L_PORTFOLIO_POKYNY',		'Pokyny za obch.de�');
	define('L_POSITIVE',				'Pozit�vna');
	define('L_POSLAT_ALERT_ODHLASENIE',		'Posla� ozn�menie o uz�vierke');
	define('L_POSLAT_POSTOU',			'Posla� po�tou');
	define('L_POSLEDNA_SPLATKA',			'Posledn� spl�tka');
	define('L_POSLEDNA_PRIJATA_PLATBA',		'Posledn� prijat� platba');
	define('L_POSLEDNY_POKLES_33', 			'D�tum posledn�ho poklesu o 1/3 je: ');
	define('L_POSLEDNY_POKLES_NO_33',		'V hist�rii cien sa pokles o 1/3 pod�a �37 Z�k.o kolekt�vnom investovan� nenach�dza');
	define('L_POSLI_VERIFIKACIA',			'Odosla� na verifik�ciu');
	define('L_POSTAL_CODE',				'PS�');
	define('L_POSTOU',				'Po�tou');
	define('L_POTVRD_DATUM',			'Potvrdi� eviden�n� d�tum');
	define('L_POVODNY_ISIN',			'P�vodn� ISIN');
	define('L_POVODNY_FOND',			'P�vodn� fond');
	define('L_POVODNE_PORTFOLIO',		'P�vodn� portfolio');
	define('L_POVODNY_PODIELNIK',			'P�vodn� klient');
	define('L_POZADOVANY_DATUM_SPLATNOSTI',	'Po�adovan� d�tum splatnosti');
 	define('L_POZASTAVENE_OD',			'Pozastaven� od');
	define('L_POZNAMKA',				'Pozn�mka');
	define('L_PO',						'Po');
	define('L_PO_UZAVIERKE',				'po uz�vierke');
	define('L_POZASTAVENE',				'Pozastaven�');
	define('L_PRAV_PERSON',				'Pr�vnick� osoba');
	define('L_PRAVIDELNY_VYPIS',		'Pravideln� v�pis');
	define('L_PRAVNICKE_OSOBY',			'Pr�vnick� osoby');
	define('L_PRE_PREDAJCOV',				'Pre predajcov');
	define('L_PRE_STATISTIKU',				'Pre �tatist. ��ely');
	define('L_PREBYTOK',				'Prebytok');
	define('L_PRECHOD',				'Prechod');
	define('L_PRED',				'Pred');
	define('L_PREDAJ',					'Predaj');
	define('L_PREDANA_AKCIA',			'Predan� akcia');
	define('L_PREDANY_PODIELOVY_FOND',	'Predan� podielov� fond');
	define('L_PREDCASNY_VYBER',			'Pred�asn� v�ber');
	define('L_PREDCASNE_UKONCENE',			'Pred�asne ukon�en�');
	define('L_PRED_UZAVIERKOU',			'pred uz�vierkou');
	define('L_PREDMET_REKL',					'Predment reklam�cie');
	define('L_PREHLAD',					'Preh�ad');
	define('L_PREHLAD_POISTENIA',		'Preh�ad poistenia');
	define('L_PREHLADNE_INFORMACIE_O_KLIENTOVI',				'Preh�adn� inform�cie o klientovi');
	define('L_PREHLADY',				'Preh�ady');
	define('L_PREHLAD_EMISIE_PL',		'Emisie');
	define('L_PREHLAD_KNIHA_PL',		'Kniha PL');
	define('L_PREHLAD_PRIHLASENYCH_UZIVATELOV',	'Preh�ad prihl�sen�ch u��vate�ov');
	define('L_PREHLAD_TRANSAKCII',			'Preh�ad transakci�');
	define('L_PREHLAD_VYPISOV_IAM_KONTA',	'Preh�ad v�pisov IAM Konta');
	define('L_PREHLAD_ZIADOSTI',			'Preh�ad �iadost�');
	define('L_PREKROCENY_LIMIT_KUSOV_CP',		'Prekro�en� po�et kusov CP');
	define('L_PREPAROVANIE',			'Prep�rovanie');
	define('L_PREPAROVAT_NESPAROVANE_PLATBY',	'Prep�rova� nesp�rovan� platby ?');
	define('L_PREPAROVANIE_UKONCENE',	'Prep�rovanie ukon�en�.');
	define('L_PREPOCITAT',				'Prepo��ta�');
	define('L_PRESTUPNY',				'Prestupn�');
	define('L_PRESTUPNY_POPLATOK',			'Prestupn� poplatok');
	define('L_PRESTUPNY_POPLATOK_DB',		'prestupn� poplatok');
	define('L_PRESTUPNY_POPLATOK_SHORT',		'Prest. poplat.');
	define('L_PRESUN',				'Presun');
	define('L_PRESUN_EMISIA_POPLATOK_SMALL',				'Hodnota poplatku odpovedaj�ca zvolen�mu percento poplatku je men�ia ako minim�lny poplatok !');
	define('L_PRESUN_OPERACII',				'Presun oper�ci�');
	define('L_PRESUN_USPESNY',		'Presun oper�ci� bol �spe�n�.');
	define('L_PRESUN_NEUSPESNY',		'Presun oper�ci� bol ne�spe�n�!');
	define('L_PRESUNY_PENAZNYCH_PROSTRIEDKOV',		'Presuny pe�a�n�ch prostriedkov');
	define('L_PRESUNUT',			'Presun��');
	define('L_PREVADZKOVE_VYNOSY',			'In� prev�dzkov� v�nosy');
	define('L_PREVIEST_NA_UCET_PODIELNIKA',		'Previes� na ��et klienta');
	define('L_PREVIOUS_PAGE',			'Predch�dzaj�ca str�nka');
	define('L_PREVOD',				'Prevod');
	define('L_PREVOD_ISTINY',			'Prevod istiny');
	define('L_PREVOD_POPLATKU',			'Prevod poplatku');
	define('L_PREVOD_UROKU',			'Prevod �roku');
	define('L_PREVOD_CP',			'Prevod CP');
	define('L_PREVOD_PP',			'Prevod pe�a�n�ch prostriedkov');
	define('L_PREVODY_CENNYCH_PAPIEROV',			'Prevody cenn�ch papierov');
	define('L_PREVODY_PENAZNYCH_PROSTRIEDKOV',			'Prevody pe�a�n�ch prostriedkov');
	define('L_PREZERAT',				'Prezera�');
	define('L_PRE_VYPOCET_KURZU_PL',	'Pre v�po�et kurzu PL sa pou�ije po�iato�n� hodnota');
	define('L_PRICE',					'Kurz investi�nej jednotky');
	define('L_INDEX_ZHODNOTENIA',		'Index zhodnotenia');
	define('L_PRIVAT',					'Privat');
	define('L_PB',						'Priv�tny bank�r');
	define('L_PRICHADZAJUCE',			'Prich�dzaj�ce');
	define('L_PRIDAJ',					'Pridaj');
	define('L_PRIDAJ_NOVU_UHRADU',		'Prida� nov� �hradu');
	define('L_PRIDAL',				'Pridal');
	define('L_PRIDANE',				'Pridan�');
	define('L_PRIDANIE',				'Pridanie');
	define('L_PRIDAT',					'Prida�');
	define('L_PRIDAT_SLUZBU',			'Prida� slu�bu');
	define('L_PRIEMERNA_PLATBA',		'Priemern� platba');
	define('L_PRIEM_AUV',				'Priem. AUV');
	define('L_MAX_AUV',					'Max. AUV');
	define('L_PRIEMER', 				'Priemer');
	define('L_PRIEM_CENA', 				'6M priem.cena');
	define('L_PRIEM_KURZ',				'Priem. kurz');
	define('L_PRIEM_NAKL_NADOBUD', 			'Priem. n�kl. nadobud.');
	define('L_PRIEMER_CIELOVA_SUMA', 			'Priemern� investi�n� cie�');
	define('L_PRIEMER_MESACNA_SPLATKA', 			'Priemern� mesa�n� platba');
	define('L_PRIEMER_POPLATOK', 			'Priemern� vstupn� poplatok ');
	define('L_PRIEMER_POPLATOK_PERC', 			'Priemern� vstupn� poplatok v %');
	define('L_PRIEMERNA_DOBA_VO_FONDE',	'Priemern� doba v portf�liu');
	define('L_PRIEMERNA_HODNOTA_SPRAV_PROSTR',	'Priem. hodnota sprav. prostr.');
	define('L_PRIEMERNA_INVESTICIA',	'Priemern� invest�cia na 1 klienta');
	define('L_PRIEMERNY_VKLAD',			'Priemern� vklad');
	define('L_PRIEMERNY_VSTUPNY_POPLATOK','Priemern� vstupn� poplatok');
	define('L_PRIEZNAZ',				'Priezvisko/N�zov');
	define('L_PRIJATE_POPLATKY',		'Prijat� poplatky');
	define('L_PRIJATE_VKLADY',			'Prijat� vklady');
	define('L_PRIKAZ_UHRADA',			'Pr�kaz na �hradu');
	define('L_PRIKAZUHRADA_OBMEDZENIE',			'Pr�kaz na �hradu m��e zada� iba podielnik, ktor� m� v danom fonde PL.');
	define('L_PRINT',				'Vytla�i�');
	define('L_PRINT_CONF',		'Vytla�i� konfirm.');
	define('L_PRINT_CONFIRM',			'Vytla�i� konfirm�ciu');
	define('L_PRINT_DETAIL',			'Detailn� tla�');
	define('L_PRINT_GRIDCARD',			'Vytla�i� gridkarty');
	define('L_PRINT_KONFIRMACIA',		'Vytla�i� konfirm�ciu');
	define('L_PRINT_OPRAVA_CONFIRM',	'Vytla�i� opravu konfirm�cie');
	define('L_PRINT_UHRADA',			'Tla�i�');
	define('L_PRIPISAT_PLATBU',			'Prip�sa� platbu');
	define('L_PRIPRAVENE_UHRADY',		'Pripraven� �hrady');
	define('L_PRIRADENI_UZIVATELIA',	'Priraden� u��vatelia');
	define('L_PRIZNANIE',			'Priznanie');
	define('L_PROGNOZA',			'Progn�za');
	define('L_PROMPT',				'Zadajte text');
	define('L_PROGRAM_SPORENIA',		'Program sporenia');
	define('L_PROTOCOL',			'Protokol');
	define('L_PRVA_SPLATKA',			'Prv� spl�tka');
	define('L_PRVY_KUPON',			'Prv� kup�n');
	define('L_POSLEDNY_KUPON',		'Posledn� kup�n');
	define('L_QUANTITY',			'Objem');
	define('L_QUARTER',				'�tvr�rok');	
	define('L_QUERY_CONTINUE',		'Chcete pokra�ova�?');
	define('L_RATIO',				'Poplatok (%)');
	define('L_RCICO',				'Rodn� ��slo/I�O');
	define('L_RCICO_SHORT',			'R�/I�O');
	define('L_READ',				'Na��taj');
	define('L_REASON',				'D�vod');
	define('L_REALY_CONFIRM_ACCOUNT','Skuto�ne chcete uzavrie� hospod�rsky v�sledok za predch�dzaj�ce obdobie ?');
	define('L_RELEVANTNA_SUMA_POPLATKOV_V_STANDARDNOM_REZIME',	'Relevantn� suma poplatkov v �tandardnom re�ime');
	define('L_RECORD_DEPP_NO_DEL',	'Z�znam nemo�no presun�� do arch�vu,lebo je akt�vny!');
	define('L_REDEMACIA_ATRIB',		'Atrib�ty v�beru');
	define('L_REDEMACIA',		'V�ber');
	define('L_REDEMACIA_DATE',		'Vybra� ku');
	define('L_REDEMACIA_PL',		'V�ber');
	define('L_REDEMACIA_PODIELOVEHO_LISTU',		'Zaevidovanie v�beru klienta');
	define('L_REDEMACIA_TILL',			'Po�et dn� na pouk�zanie platby klientovi');
	define('L_REDEMOVANE',				'Vybrat�');
	define('L_REDEMOVANE_PL',			'Vybran� PL');
	define('L_REDEMOVAT',				'Vybra�');
	define('L_REFERENCE_CURRENCY',			'Referen�n� mena');
	define('L_REFERENCNA_MENA', 'Referen�na mena');
	define('L_REFKURZ',				'Ref. kurz');
	define('L_REFMENA',				'Ref. mena');
	define('L_REF_MIERA_VYNOS',				'Ref. miera v�nosnosti');
	define('L_REF_MIERA_VYNOS_DEF',				'Ref. miera v�nosnosti pre dan� obdobie u� bola definovan�');
	define('L_REGION',				'Regi�n');
	define('L_REGISTRATION_DATE',			'D�tum registr�cie');
	define('L_REGISTRATION_NUMBER',			'Registra�n� ��slo');
	define('L_REGISTRATION_TIME',			'�as registr�cie');
	define('L_REEMISIA',				'Reemisia');
	define('L_REINVESTICIA',				'Reinvest�cia');
	define('L_REINVESTICIA_NEPREBEHLA',		'Reinvest�cia a vyplatenie v�nosov neprebehli');
	define('L_REINVESTICIA_PREBEHLA_OK',		'Reinvest�cia a vyplatenie v�nosov prebehli v poriadku');
	define('L_REINVESTICIA_VYNOSOV',		'Reinvest�cia v�nosov');
	define('L_REINVESTICIA_VYNOSU',			'Reinvest�cia v�nosu');
	define('L_REINVESTICIE',			'Reinvest�cie');
	define('L_REINVESTICIE_VYNOSOV',	'Reinvest�cie v�nosov');
	define('L_REINVESTICNE_PODIELY', 'Reinvesti�n� podiely');
	define('L_REINVESTICNY_KOEFICIENT', 'Reinvesti�n� koeficient');
	define('L_REINVESTOVAT',			'Reinvestova�');
	define('L_REINVEST_DIVIDENDA',			'Reinvesti�n� dividenda');
	define('L_REINVEST_PODIELY', 			'Reinvesti�n� podiely');
	define('L_REKLAMACIA',				'Reklam�cia');
	define('L_REKLAMACIE',				'Reklam�cie');
	define('L_REKONFIRM',				'Rekonfirmova�');
	define('L_REKONFIRMACIA',			'Rekonfirm�cia');
	define('L_REKONFIRMACIA_POPLATKOV',		'Rekonfirm�cia poplatkov');
	define('L_REKONFIRMACIA_POPLATKU',		'Rekonfirm�cia');
	define('L_REKONFIRMACIA_T',			'REKONFIRM�CIA');
	define('L_REKONFIRMACIA_UHRAD',			'Rekonfirm�cia �hrad');
	define('L_REKONFIRMACIA_CP',			'Rekonfirm�cia CP');
	define('L_REKONFIRMACIA_UROKU_Z_BU',		'Rekonfirm�cia �roku z BU');
	define('L_REKONFIRMACIA_VYNOSOV',		'Rekonfirm�cia v�nosov');
	define('L_REKONFIRMACIE',			'Rekonfirm�cie');
	define('L_REKONFIRMACIE_KONV',			'Rekonfirm�cie konverzi�');
	define('L_REKONFIRMED_BY',			'Rekonfirmoval');
	define('L_REKONFIRMOVAT_VYBRANE_VYNOSY',	'Rekonfirmova� vybran� v�nosy?');
	define('L_REKONFIRMOVAT_ZVOLENE_POPLATKY',	'Rekonfirmova� zvolen� poplatky a odplaty ?');
	define('L_REKONFIRM_UHRADA',			'Rekonfirm�cia �hrady');
	define('L_REKONF_BONDS',			'Rekonfirm�cia dlhopisu');
	define('L_REKONF_CP',				'Rekonfirm�cia CP');
	define('L_REKONF_DEPO',				'Rekonfirm�cia depozitn�ch certifik�tov');
	define('L_REKONF_FONDS',			'Rekonfirm�cia fondu');
	define('L_REKONF_KONV',				'Rekonfirm�cia konverzi�');
	define('L_REKONF_SHARES',			'Rekonfirm�cia akcie');
	define('L_RELEVANT',				'Relevantn�');
	define('L_RELEVANTNE',				'Relevantn�');
	define('L_REMOVE',				'Odstr�ni�');
	define('L_REPAYMENT_CONDITIONS',		'Podmienky spl�cania');
	define('L_REPORT_POPLATKOV','Report poplatkov');
	define('L_REQUIRED_ATTRIBUTES_MISSING',		'Nevyplnili ste nasledovn� po�adovan� polo�ky formul�ra');
	define('L_RESERVED_FOR_SPRAVCA',		'Eviden�n� �islo 1 je rezervovan� pre spr�vcu');
	define('L_RESET',				'Reset');
	define('L_RESPONSIBLE_PERSON',			'Zodpovedn� osoba');
	define('L_RESTRICTIONS',			'Obmedzenia');
	define('L_RESTRICTION_NAME',			'N�zov obmedzenia');
	define('L_RESTRICT_DATES',			'D�tumov� filter');
	define('L_REVENUES',				'V�nosy');
	define('L_REZERVNY',				'Rez.');
	define('L_REZERVOVANE',				'Rezervovan�');
	define('L_REZIM',				'Re�im');
	define('L_REZIM_FOND',				'Portf�lio');
	define('L_REZIM_INVESTOVANIA',			'Re�im investovania');
	define('L_REZIM_SPRAVCA',			'Spr�vca');
	define('L_RIADNE',				'Riadne');
	define('L_RIC',					'RIC');
	define('L_RIC_AKTIVA',				'RIC akt�va');
	define('L_RIC_FOR_CURRENCY',			'RIC pre menu');
	define('L_RIC_REUTERS',				'RIC Reuters');
	define('L_ROCNE',					'Ro�ne');
	define('L_RODNE_CISLO',				'Rodn� ��slo');
	define('L_ROZDELENIE_VYNOSU',			'Rozdelenie v�nosu');
	define('L_ROZDEL_VYNOS', 			'Rozdeli� v�nos');
	define('L_ROZDIEL_VIAC_AKO_ROK',	'Rozdiel medzi d�tumami nesmie by� viac ako 1 rok.');
	define('L_ROZPRACOVANA',			'Rozpracovan�');
	define('L_ROZPRACOVANE',			'Rozpracovan�');
	define('L_SADZBA',				'Sadzba');
	define('L_SADZBA_DANE',				'Sadzba dane');
	define('L_SANKCIA',				'Sankcia');
	define('L_SAVE',				'Ulo�i�');
	define('L_SAVE_CHANGES',			'Ulo�i� zmeny?');
	define('L_SAVE_CURRENT_RESTRICTION',		'Ulo�i� aktu�lne obmedzenie');
	define('L_SAVE_CURRENT_RESTRICTIONS',		'Ulo�i� zmeny aktu�lneho obmedzenia ?');
	define('L_SAVE_ERROR', 'Nastala chyba v syst�me. Akciu sa nepodarilo vykona�.');
	define('L_SAVE_UCET_CHANGES',			'Ulo�i� zmeny ��tu');
	define('L_SECTOR',				'Sektor');
	define('L_SEKTOR',				'Sektor');
	define('L_SELECT',				'Vybra�');
	define('L_SELECT_ALL',				'Vybra� v�etko');
	define('L_SELECT_GRIDCARDS',		'Vybra� gridkarty');
	define('L_SELECT_KONFIRM',		'Vybra� konfirm.');
	define('L_SELECT_BANK',				"'Vyberte si banku zo zoznamu !'");
	define('L_SELECT_FOND',				'V�ber portf�lia');
	define('L_SELECT_ITEMS',			'Nevybrali ste si �iadnu polo�ku !');
	define('L_SELECT_OBRAT',			'Vyber obrat');
	define('L_SELECT_PODIELNIK',			'V�ber klienta');
	define('L_SELECT_USER',				'V�ber pou��vate�a');
	define('L_SELECT_TITLE',			'Vybra� titul');
	define('L_SELL_PLACE',				'Predajn� miesto');
	define('L_SEL_CHOOSE',				' ---');
	define('L_SEND',				'Posla�');
	define('L_SERIALIZED_ACCEPT',			'SERIALIZED_ACCEPT');
	define('L_SERIA_NUMBER',			'��slo s�rie');
	define('L_SERIE_NUMBER',			'��slo listu');
	define('L_SERVER_CERT',				'SERVER_CERT');
	define('L_SET',					'Nastav');
	define('L_SET_CURRENCY_KURZ',			'Menov� p�r');
	define('L_SHARES',				'Akcie');
	define('L_SHOW',				'Zobrazi�');
	define('L_SHOWALL',				'SHOWALL');
	define('L_SHOWRESTRICTED',			'SHOWRESTRICTED');
	define('L_SIDLO',				'S�dlo');
	define('L_SIGNATURE',				'Podpis');
	define('L_SIGNATURE_DESCRIPTION',				'Koment�r k podpisu');
	define('L_SIGNATURE_FILE',			'Obr�zok podpisu :');
	define('L_SIX_MONTHS',				'6 mesiacov');
	define('L_SKONTROLUJ_DATUM',			'Skontroluj d�tum');
	define('L_SKRATKA',				'Skratka');
	define('L_SKRATKA_AKTIVA',			'Skratka akt�va');
	define('L_SLUZBY',				'Slu�by');
	define('L_SPAROVANE',				'Sp�rovan�');
	define('L_SPAROVANIE',				'Sp�rovanie');
	define('L_SPAROVANIE_DOSLYCH_PLATIEB',		'Sp�rovanie do�l�ch platieb');
	define('L_SPAROVAT',				'Sp�r.');
	define('L_SPATNE_ODOSLANIE',		'Sp�tn� odoslanie');
	define('L_SPATNY_ODKUP',			'V�ber');
	define('L_SPATNY_ODKUP_PL',			'Sp�tn� odkup podielov�ch listov');
	define('L_SPECIAL',					'�peci�lny');
	define('L_SPECIFY_REASON',		'Zadajte d�vod blokovania prihlasovania.');
	define('L_SPLATENE_AUV', 			'Splaten� AUV');
	define('L_SPLATENIE',				'Splatenie');
	define('L_SPLATENIE_D',				'Splatenie dlhopisu');
	define('L_SPLATENIE_FORWARD',		'Splatenie NDF');
	define('L_SPLATENIE_TV',			'Splatenie term�novan�ho vkladu');
	define('L_SPLATNA_ISTINA_KUPON',	'Istina/Kup�n');
	define('L_SPLATNA_ISTINA_TV',		'Splatn� istina TV');
	define('L_SPLATNA_POHLADAVKA',		'Splatn� poh�ad�vka');
	define('L_SPLATNE_KTV',				'Splatn� KTV');
	define('L_SPLATNOST',				'Splatnos�');
	define('L_SPLATNY_DLHOPIS',			'Splatn� dlhopis');
	define('L_SPLATNY_KUPON',			'Splatn� kup�n dlhopisu');
	define('L_SPLATNY_NOMINAL',			'Splatn� nomin�l dlhopisu');
	define('L_SPLATNY_NOMINAL_DEP_CERT','Splatn� nomin�l z depozitn�ho certifik�tu');
	define('L_SPLATNY_OBJEM',			'Splatn� objem');
	define('L_SPLATNY_UROK_BU',			'Splatn� �rok � be�n�ho ��tu');
	define('L_SPLATNY_UROK_DEP_CERT',	'Splatn� �rok z depozitn�ho certifik�tu');
	define('L_SPLATNY_UROK_TV',			'Splatn� �rok TV');
	define('L_SPLNOMOCNENA_OSOBA',		'Splnomocnen� osoba');
	define('L_SPLNOMOCNENE_OSOBY',		'Splnomocnen� osoby');
	define('L_SPOLU',				'Spolu');
	define('L_SPOLUDISPONENT',			'Spoludisponuj�ci');
	define('L_SPORENIE',				'Sporenie');
	define('L_SPORENIE_AKT',				'Sporenie AKT');
	define('L_SPORENIE_AKTIV',				'Aktivova� sporenie');
	define('L_SPORENIE_DS_OBMEDZENIE',		'Sporenie pomocou doplnkovej slu�by mo�no zavies� iba ak dan� podielnik vlastn� podielov� listy');
	define('L_SPORENIE_KOMPLET',		'Sporenie komplet');
	define('L_SPORENIE_VYDANIE_PL',		'Program sporenia - Vydanie PL');
	define('L_SPORENIE_ZIADOSTID',				'��slo �iadosti o sporenie');
	define('L_SPORENIE_NAOZAJ_POKRACOVAT',				'M�te vybrat� PL zo sporenia. Danou oper�ciou s PL zo sporenia sa pozastav� dan� program sporenia.');
	define('L_SPORENIE_ZRUSENIE',				'Zru�enie programu sporenia');
	define('L_SPOT_RATE',				'Spot rate');

	define('L_SPP', 					'�PP');
	define('L_SPRACOVANE', 				'Spracovan�');
	define('L_SPRACOVANIE_DOSLYCH_PLATIEB',		'Spracovanie do�l�ch platieb');
	define('L_SPRAVA_O_TRHOVEJ_HODNOTE_A_ZHODNOTENI',	'Spr�va o trhovej hodnote a zhodnoten�');
	define('L_SPRAVA_UZIVATELOV',			'Spr�va u��vate�ov');
	define('L_SPRAVA_UZIVATELSKYCH_SKUPIN',		'Spr�va u��vate�sk�ch skup�n');
	define('L_SPRAVCA',				'Spr�vca');
	define('L_SPRAVCA_NEMA_UCET_V_TEJTO_MENE',	'Spr�vca nem� ��et v tejto mene.');
	define('L_SPRAV_SPOL',			'spr�v. spol.');
	define('L_SPRAVCU',				'Za spr�vu portf�lia');
	define('L_SPUSTIT',				'Spusti�');
	define('L_SPUSTIT_OCENENIE',			'Spusti� ocenenie');
	define('L_SPUSTIT_OCENENIE_ZA',			'Spusti� ocenenie za');
	define('L_SPUSTIT_UZAVIERKU', 			'Spusti� uz�vierku');
	define('L_SPUSTIT_UZAVIERKU_ZA_TENTO_FOND', 	'Spusti� uz�vierku za toto portf�lio ?');
	define('L_SPUSTIT_UZAV_ZA',			'Spusti� uz�vierku za');
	define('L_SS',					'�S');
	define('L_STANDARDNA',				'�tandardn�');
	define('L_STANDARDNY',				'�tandardn�');
	define('L_STANOVENIE_POPLATKU',			'Konfirm�cia poplatku');
	define('L_STANOVENIE_VYNOSU',			'Stanovenie v�nosu');
	define('L_STARTING_VALUE_SHARE',		'Po�iato�n� hodnota PL');
	define('L_START_EMMIT_PL',			'D�tum za�iatku vyd�vania PL');
	define('L_STATE',				'�t�t');
	define('L_STATEALL',				'�t�t');
	define('L_STATESHORT',				'Skratka');
	define('L_STATE_CANNOT_BE_REMOVED',		'�t�t nie je mo�n� odstr�ni�.');
	define('L_STATISTIKA',			'�tatistika');
	define('L_STATUS',				'Status');
	define('L_STAV',				'Stav');
	define('L_STAV_1',				'Stav 1');
	define('L_STAV_2',				'Stav 2');
	define('L_STAV_PL_FOOTLINES',	'Emitovan� suma vyjadruje kumulovan� hodnotu vkladov podielnikov, o�isten� o vstupn� poplatok a halierov� vyrovnanie, za ktor� boli vyemitovan� podielov� listy.
	Redemovan� suma vyjadruje hodnotu pe�a�n�ch prostriedkov o ktor� bolo zn�en� NAV z d�vodu redem�ci�.
	Investovan� suma predstavuje rozdiel emitovanej a redemovanej sumy.');
	define('L_STAVY',				'Stavy');
	define('L_STAV_K',				'Stav k');
	define('L_STAV_PODIELOVYCH_LISTOV',		'Stav podielov�ch listov');
	define('L_STAV_PORTFOLIA_CENN_PAP',		'Stav portf�lia cenn�ch papierov');
	define('L_STAV_PORTFOLIA_CP',			'Stav portf�lia CP');
	define('L_STAV_UCTU',				'Stav ��tu');
	define('L_STOP',						'Ukon�i�');
 define('L_STOP_BLOCK',				'Ukon�i� platnos� blokovania prihlasovania ?');
	define('L_STORNO',				'Storno');
	define('L_STORNO_NEMOZE_PREBEHNUT',		'Storno nem��e prebehn��');
	define('L_STORNO_OCENENIA',			'Storno ocenenia');
	define('L_STORNO_POPLATKOV',			'Storno poplatkov');
	define('L_STORNO_UZ_PREBEHLO',			'Storno u� prebehlo');
	define('L_STORNO_UZAVIERKY',			'Storno uz�vierky');
	define('L_STORNOVANE',				'Stornovan�');
	define('L_STORNOVAT',				'Stornova�');
	define('L_STORNOVAT_REKONFIRMACIU',		'Stornova� rekonfirm�ciu');
	define('L_STORNOVAT_UZAVIERKU_ZA_TENTO_FOND',	'Stornova� uz�vierku za toto portf�lio ?');
	define('L_STORNOVAT_UZAVIERKU_ZA',			'Stornova� uz�vierku za');
	define('L_STORNOVAT_ZIADOST',			'Stornova� �iados�?');
	define('L_STORNOVAT_ZVOLENE_POPLATKY',		'Stornova� zvolen� poplatky ?');
	define('L_STORNO_KONF_BONDS',			'Storno konfirm�cie dlhopisu');
	define('L_STORNO_KONF_CP',			'Storno konfirm�cie CP');
	define('L_STORNO_KONF_DEPO',			'Storno konfirm�cie depozitn�ho certifik�tu');
	define('L_STORNO_KONF_FONDS',			'Storno konfirm�cie fondu');
	define('L_STORNO_KONF_KONV',			'Storno konfirm�cie konverzie');
	define('L_STORNO_KONF_SHARES',			'Storno konfirm�cie akcie');
	define('L_STORNO_REKONFIRMACIE_POPLATKU',	'Storno');
	define('L_STORNO_REKONF_BONDS',			'Storno rekonfirm�cie dlhopisu');
	define('L_STORNO_REKONF_DEPO',			'Storno rekonfirm�cie depozitn�ho certifik�tu');
	define('L_STORNO_REKONF_FONDS',			'Storno rekonfirm�cie fondu');
	define('L_STORNO_REKONF_KONV',			'Storno rekonfirm�cie konverzie');
	define('L_STORNO_REKONF_SHARES',		'Storno rekonfirm�cie akcie');
	define('L_STORNO_VKLAD_CP',		'Storno vkladu CP');
	define('L_STORNO_MALO_CP',		'Na maj. ��te nie je dostatok CP na storno');
	define('L_STREET',				'Ulica');
	define('L_STRUKTURA',				'�trukt�ra');
	define('L_STRUKTURA_AKTIV',			'�trukt�ra akt�v');
	define('L_STRUKTURA_MAJETKU',			'�trukt�ra majetku');
	define('L_SUBJECT_ID',				'ID subjektu');
	define('L_SUBJEKT',				'Subjekt');
	define('L_SUBOR_MUSI_BYT_V_TVARE',		'S�bor mus� by� v tvare');
	define('L_SUCCES_COUNT',			'Po�et �spe�ne rekonfirmovan�ch �hrad:');
	define('L_SUMA',				'Suma');
	define('L_SUMA_CM',				'Suma v CM');
	define('L_SUMA_CELKOVA',			'Celkov� suma');
	define('L_SUMA_DANE', 				'Suma dane');
	define('L_SUMA_DEBET',				'Suma debet');
	define('L_SUMA_DO',				'Suma do');
	define('L_SUMA_DO_MUSI_BYT_NIZSIA', 'Horn� hranica mus� by� ni��ia');
	define('L_SUMA_DO_MUSI_BYT_VYSSIA', 'Horn� hranica mus� by� vy��ia');
	define('L_SUMA_EMISIE',				'Suma emisie');
	define('L_SUMA_EMITOVANA',			'Emitovan� suma');
	define('L_SUMA_KREDIT',				'Suma kredit');
	define('L_SUMA_NA_BU',				'Suma na BU');
	define('L_SUMA_NA_UCTE',			'Suma na ��te');
	define('L_SUMA_NIZSIA',				'Po�adovan� suma v�beru je ni��ia ako je aktu�lna suma vybran� na v�ber.');
	define('L_SUMA_OD',				'Suma od');
	define('L_SUMA_PLATIEB',			'Suma platieb');
	define('L_SUMA_PODIEL', 			'Suma/podiel');
	define('L_SUMA_POPLATKOV',			'Suma poplatkov');
	define('L_SUMA_PRESUNU',			'Suma presunu');
	define('L_SUMA_REDEMOVANA',			'Redemovan� suma');
	define('L_SUMA_VYBERU',				'Suma v�beru');
	define('L_SUMA_VYPLATA',			'Suma k v�plate');
	define('L_SUMA_VYSSIA',				'Po�adovan� suma v�beru je vy��a ako je aktu�lna suma vybran� na v�ber.');
	define('L_SUMAR_SPORENIA',			'Sum�r sporenia');
	define('L_SUPERVISOR',				'Supervisor');
	define('L_SURNAME',				'Priezvisko');
	define('L_SURNAME_AND_NAME',			'Priezvisko a meno');
	define('L_SURNAME_TITLE',			'Priezvisko/N�zov');
	define('L_SYMBOL',				'Symbol');
	define('L_SYSTEM',				'Syst�m');
	define('L_TAB_COLUMNS',				'TAB_COLUMNS');
	define('L_TAX',					'Da�');
	define('L_TENTO_PL_UZ_BOL_VYEMITOVANY',	'Tento vklad u� bol vyemitovan�.');
	define('L_TERM',				'Polrok');
	define('L_TERMINOVANY_VKLAD',			'Term�novan� vklad');
	define('L_THANKS_SHEET',			'�akovn� list');
	define('L_THREE_MONTHS',			'3 mesiace');
	define('L_TICKET',				'Ticket');
	define('L_TIME',				'�as');
	define('L_TIMEFORMAT',				'G:i');			// Default language time format
	define('L_TIMEFORMATSEC',			'G:i.s');		// Default language time format
	define('L_TIME_KONFIRMACIA',			'�as konfirm.');
	define('L_TIME_PODANIE_ZIADOSTI',		'�as podania �iadosti');
	define('L_TIME_REGISTRACIA_ZIADOSTI',		'�as registr�cie');
	define('L_TITLE_AFTER',				'Titul za');
	define('L_TITLE_BEFORE',			'Titul pred');
	define('L_TLAC',				'Tla�');
	define('L_TLACIT_KONF',				'Vytla�i� konfirm�ciu');
	define('L_TLACIT_OZNAM',		'Tla�i� oznam');
	define('L_TO',					'do');
	define('L_TOTAL',				'Spolu');
	define('L_TO_ACTIVE',				'Akt�vne');
	define('L_TO_ARCHIVE',				'Presun�� do arch�vu');
	define('L_TOO_MANY_RECORDS',			'Pr�li� ve�a z�znamov');
	define('L_TRADE_DATA',				'Obchodn� �daje');
	define('L_TRANSAKCIA', 'Transakcia');
	define('L_TRANSAKCIE',				'Transakcie');
	define('L_TRANSAKCIE_FONDU',			'Transakcie portf�lia');
	define('L_TRANSFER',				'Presun podielnikov');
	define('L_TRANSSUM',				'Trans.suma');
	define('L_TRANSSUMA',				'Transak�n� suma');
	define('L_TRANS_SUMA',				'Transak�n� suma');
	define('L_TRANZA',					'Tran�a');
	define('L_TRH',						'Trh');
	define('L_TRHOVA_HODNOTA',			'Trhov� hodnota');
	define('L_TRHOVA_CENA',				'Trhov� cena');
	define('L_TRH_HODNOTA',				'Trh.hodnota');
	define('L_TRH_HODNOTA_SPOLU', 			'Trh. hodnota spolu');
	define('L_TRH_KURZ',				'Trh.kurz');
	define('L_TRIEDA',					'Trieda');
	define('L_TRZBA_DLHOPIS',			'Platba za predan� dlhopis');
	define('L_TRVALY_PRIKAZ',			'Trval� pr�kaz');
	define('L_TRVALY_PRIKAZ_OD',		'Trval� pr�kaz - d�tum prvej platby');
	define('L_TRVALY_PRIKAZ_DO',		'Platnos� trval�ho pr�kazu - do');
	
	define('L_TV',						'Term�nov� vklad');
	define('L_TV_NAMONITORING',			'Zahrn�� vybran� TV do monitoringu rizika?');
	define('L_TV_NAVERIFIKACIU',		'Skuto�ne chcete odosla� TV na verifik�ciu?');
	define('L_TWO_WEEKS',				'2 t��ne');
	define('L_TYP',						'Typ');
	define('L_TYP_KONVERZIE',			'Typ konverzie');
	define('L_TYP_POKYNU','Typ pokynu');
	define('L_TYPY_POKYNOV','Typy pokynov');
	define('L_TYP_PARTNERA', 			'Typ partnera');
	define('L_TYP_PLATBY',				'Typ platby');
	define('L_TYP_POPLATKU',			'Typ poplatku');
	define('L_TYP_PORTFOLIA',			'Typ portf�lia');
	define('L_TYP_PREDLOHY',			'Typ predlohy');
	define('L_TYP_SUBORU',				'Typ s�boru');
	define('L_TYP_TRANSAKCIE',			'Typ transakcie');
	define('L_TYP_VYNOSU', 				'Typ v�nosu');
	define('L_TYP_ZIADOSTI', 			'Typ �iadosti');
	define('L_UCET',					'��et');
	define('L_UCET_DEBET',				'��et debet');
	define('L_UCET_KREDIT',				'��et kredit');
	define('L_UCET_LABEL',				'��et');
	define('L_UCET_NUMBER',				'��slo ��tu');
	define('L_UCET_PARTNER',			'��et partnera');
	define('L_UCET_PARTNERA',			'��et partnera');
	define('L_UCET_SPRAVCU',			'��et spr�vcu');
	define('L_UCET_UZ_EXISTUJE', 			'Zadan� ��et u� existuje');
	define('L_UCET_VYSPOR',				'��et vysporiadania');
	define('L_UCET_VYSPOR_DANE',				'��et vysporiadania dane');
	define('L_UCTY',				'��ty');
	define('L_UHRADA',				'�hrada');
	define('L_UHRADA_BOLA_ZMENENA_ALEBO_ZAUCT',	'�hrada bola zmenen�,alebo za��tovan� in�m u��vate�om.');
	define('L_UHRADA_CONFIRM_MSG1',			'�hrada e�te nie je verifikovan�. Prajete si ju skuto�ne za��tova�?');
	define('L_UHRADA_CONFIRM_MSG2',			'�hrada bola verifikovan� negat�vne. Prajete si ju napriek tomu za��tova�?');
	define('L_UHRADA_NAVERIFIKACIU',		'Skuto�ne chcete odosla� vybran� �hrady na verifik�ciu?');
	define('L_UHRADA_UZ_BOLA_NATIPOVANA',		'�hrada u� bola natipovan� in�m u��vate�om.');
	define('L_UHRADA_VERIFIKACIA',			'Verifik�cia �hrady');
	define('L_UHRADY',				'�hrady');
	define('L_UKONCENE',				'Ukon�en�');
	define('L_UKON_VYDAV_PL_ZA_POC_HODNOTU',	'D�tum ukon�enia vyd�vania PL');
	define('L_ULOZIT',				'Ulo�i�');
	define('L_ULOZIT_ZMENY_OR_CANCEL',		'Ulo�te zmeny,	alebo ich stornujte');
	define('L_UNKNOWN_RIC',				'Nezn�me RIC');
	define('L_UPDATE',				'Opravi�');
	define('L_UPDATE_PORTFOLIO',		'Aktualizova� portf�lia');
	define('L_UPDATE_PORTFOLIO_Q',		'Aktualizova� v�etky portf�lia typu ');
	define('L_POCET_PORTFOLII',			'Po�et aktualizovan�ch portf�li�: ');
	define('L_UPOZORNENIE',				'Upozornenie');
	define('L_UROCENIE',				'�ro�enie');
	define('L_UROK', 					'�rok');
	define('L_UROKY', 				'�roky');
	define('L_UROK_BRUTTO',				'�rok brutto');
	define('L_UROK_KAPITALIZOVANIE',		'Kapitalizovanie');
	define('L_UROK_NETTO',				'�rok netto');
	define('L_UROK_PRIPISANIE',			'Prip�sanie �roku');
	define('L_UROK_SADZBA',				'Sadzba');
	define('L_UROK_Z_BU',				'�roku z BU');
	define('L_UR_SADZBA',				'�rok. sadzba p.a.');
	define('L_USCHOVAT',				'Uschova�');
	define('L_USER',				'U��vate�');
	define('L_USERLOGIN',				'Prihlasovacie meno');
	define('L_USERNAME_UZ_EXISTUJE',		'U��vate� s t�mto menom u� v datab�ze existuje');
	define('L_USER_ALREADY_LOGGED',			'U��vate� u� je prihl�sen�');
	define('L_UZATVORENA',				'Uzartvoren�');
	define('L_UZATVORENE',				'Uzartvoren�');
	define('L_UZAVIERKA',				'Uz�vierka');
	define('L_UZAVIERKA_DATUM_PROBLEM', 		'Probl�m s d�tumami, uz�vierka nem��e prebehn��');
	define('L_UZAVIERKA_FAIL',			'Uz�vierka neprebehla');
	define('L_UZAVIERKA_KURZY',			'Kurzy');
	define('L_UZAVIERKA_OK',			'Uz�vierka prebehla v poriadku');
	define('L_UZIVATEL',				'U��vatel');
	define('L_UZIVATELIA',				'U��vatelia');
	define('L_UZIVATELSKA_SKUPINA',			'U��vate�sk� skupina');
	define('L_VALID',				'Platn�');
	define('L_VALID_FROM',				'Datum platnosti od');
	define('L_VALID_TO',				'Datum platnosti do');
	define('L_VARIABLE_RESTRICTIONS',		'Variabiln� obmedzenia');
	define('L_VARIAB_SYMBOL',			'Variabiln� symbol');
	define('L_VARIAB_SYMBOL_SHORT',		'Var. symbol');
	define('L_VEK',						'Vek');
	define('L_VELA_KUSOV',				'Pr�li� ve�a kusov');
	define('L_VELKY_LIMIT_KURZ',			'Pr�li� vysok� limitn� kurz');
	define('L_VERIFIKACIA',				'Verifik�cia');
	define('L_VERIFIKACIA_TERMINOVANEHO_VKLADU',	'Verifik�cia term�novan�ho vkladu');
	define('L_VERIFIKACIA_UHRAD',			'Verifik�cia �hrad');
	define('L_VERIFIKACIE',				'Verifik�cie');
	define('L_VERIFIKOVANE',			'Verifikovan�');
	define('L_VERIFIKOVAT',				'Verifikova�');
	define('L_VERIFY_KONFIRMACIA',			'Odosla� na verifik�ciu');
	define('L_VERIFY_TIME_LIMIT',			'�asov� limit verifik�cie [min]');
	define('L_VERIF_AUTOMATICKY',			'Verif. automaticky');
	define('L_VERIF_BONDS',				'Verifik�cia dlhopisov');
	define('L_VERIF_DEPO',				'Verifik�cia depozitn�ch certifik�tov');
	define('L_VERIF_FONDS',				'Verifik�cia fondov');
	define('L_VERIF_KLADNE',			'Verif. kladne');
	define('L_VERIF_KONV',				'Verifik�cia konverzi�');
	define('L_VERIF_SHARES',			'Verifik�cia akci�');
	define('L_VERIF_ZAPORNE',			'Verif. z�porne');
	define('L_VIAC_AKO_5P',				'Viac ako 5%');
	define('L_VIEW_PODIELNIK',			'Zobrazi� inform�cie o klientovi');
	define('L_VIEW_TRANS_NOTHING_CHOOSEN',		'Ni� nie je zobrazen�!');
	define('L_VKLAD',							'Vklad');
	define('L_VKLADY',							'Vklady');
	define('L_VKLAD_PODIELNIKA',			'Vklad klienta');
	define('L_VKLAD_PODIELNIKA_PRESUN',		'Vklad klienta - presun');
	define('L_VKLADY_CENNYCH_PAPIEROV',				'Vklady cenn�ch papierov');
	define('L_VKLADY_PENAZNYCH_PROSTRIEDKOV',				'Vklady pe�a�n�ch prostriedkov');
	define('L_VKLADY_A_VYBERY',				'Vklady a v�bery');
	define('L_VLASTNA',			'Vlastn�');
	define('L_VLASTNE_VYKONY',			'Tr�ba za vlastn� v�kony');
	define('L_VOLNE',				'Vo�n�');
	define('L_VS',					'VS');
	define('L_VSETKY',				'V�etky');
 define('L_VSETKY_UCTY',			'V�etky ��ty');
	define('L_VSTUPNY',				'Vstupn�');
	define('L_VSTUPNA_INVESTICIA',			'Vstupn� invest�cia');
	define('L_VSTUPNY_POPLATOK',			'Vstupn� poplatok');
	define('L_VSTUPNY_POPLATOK_DB',			'vstupn� poplatok');
	define('L_VSTUPNY_POPLATOK_SHORT',		'Vst. poplat.');
	define('L_VSTUPNY_POPLATOK_SPORENIE',		'Vstupn� poplatok (v percent�ch)');
	define('L_VSYMBOL',				'Variabiln� symbol');
	define('L_VYBAVENA',				'Vybaven�');
	define('L_VYBAVUJE',				'Vybavuje');
	define('L_VYBAVUJE_CENTRALA',				'Centr�la');
	define('L_VYBAVUJE_POBOCKA',				'Pobo�ka');
	define('L_VYBAV_KLADNE',			'Vybavi� kladne');
	define('L_VYBAV_ZAPORNE',			'Vybavi� z�porne');
	define('L_VYBER',				'V�ber');
	define('L_VYBERY',			'V�bery');
	define('L_VYBERY_CENNYCH_PAPIEROV',			'V�bery cenn�ch papierov');
	define('L_VYBERY_PENAZNYCH_PROSTRIEDKOV',			'V�bery pe�a�n�ch prostriedkov');
	define('L_VYBER_AKTIVA',			'V�ber akt�va');
	define('L_VYBER_ASPON_JEDNU_POLOZKU',		'Vyber aspo� jednu polo�ku');
	define('L_VYBER_CP',				'Mus�te vybra� cenn� papier');
	define('L_VYBER_FILTER',			'Vyberte filter');
	define('L_VYBER_FOND',				'Vyberte portf�lio');
	define('L_VYBER_FONDU',				'V�ber portf�lia');
	define('L_VYBER_MENA',				'Mus�te vybra� menu.');
	define('L_VYBER_MENOVEHO_PARU',		'V�ber menov�ho p�ru');
	define('L_VYBER_PODIELOV',			'V�ber podielov');
	define('L_VYBER_POLOZKU',			'Vyberte polo�ku');
	define('L_VYBER_POVODNY_FOND',		'Mus�te vybra� p�vodn� portf�lio.');
	define('L_VYBER_ZAUCTOVANIE',		'Vyberte typ za��tovania');
	define('L_VYBERTE_NOVEHO_PODIELNIKA',		'Vyberte nov�ho podielnika');
	define('L_VYBRANE_EMISIE',			'Vybran� emisie');
	define('L_VYBRANE_POLOZKY_HVUCTOVANIA',		'Vybran� polo�ky ��tovania');
	define('L_VYBRANY_FOND',			'Vybran� portf�lio');
	define('L_VYBRAT_FOND',				'Vybra� portf�lio');
	define('L_VYBRAT_POZICIU',			'Vybra� poz�ciu');
	define('L_VYDANIE',					'Vydanie');
	define('L_VYDANIE_PL',				'�iados� o vklad');
	define('L_VYDAV_PL',				'Vyd�vanie podiel.listov za po�. hodnotu');
	define('L_VYHLASENIEOOU',			'Vyhl�senie o ochrane �dajov');
	define('L_VYHODNOTENIE',			'Vyhodnotenie zadefinovan�ch obmedzen�');
	define('L_VYHLASENIE_OOU',			'Vyhl�senie OOU');
	define('L_VYKAZY',					'V�kazy');
	define('L_VYKONAL',					'Vykonal');
	define('L_VYKONAVAM_AKTUALIZACIU',	'Vykon�vam aktualiz�ciu');
	define('L_VYMAZTE_NAJSKOR_VYSSIU_SADZBU', 'Vyma�te najsk�r vy��iu sadzbu');
	define('L_VYNOS',					'V�nos');
	define('L_VYNOS_AD_MIERA',			'Korekcia miery');
	define('L_VYNOSY',					'V�nosy');
	define('L_VYNOSY_FONDU',			'V�nosy portf�lia');
	define('L_VYNOSY_Z_DS',				'V�nosy z doplnkov�ch slu�ieb');
	define('L_VYNOS_BRUTTO', 			'V�nos');
	define('L_VYNOS_NETTO', 			'V�nos netto');
	define('L_VYNOS_PODIEL_BRUTTO', 		'V�nos / 1 podiel');
	define('L_VYNOS_PODIEL_NETTO',			'V�nos / podiel netto');
	define('L_VYPIS',						'V�pisy');
	define('L_VYPIS_FREKVENCIA',			'Frekvencia v�pisov');
	define('L_VYPISY_ZAEVIDOVANE',			'V�pisy boli zaevidovan�');
	define('L_VYPISY_NEZAEVIDOVANE',		'Nepodarilo sa zaevidova� v�pisy');
	define('L_VYPISY_NEVYTLACENE',			'Vypisy neboli vytla�en�');
	define('L_VYPLATA',				'V�plata');
	define('L_VYPLATA_PL',				'�iados� o v�ber');
	define('L_VYPLATA_PODIELNIKA',			'V�plata klienta');
	define('L_VYPLATA_VYNOSU',			'V�plata v�nosu');
	define('L_VYPLATA_VYNOSOV',					'V�plata v�nosov');
	define('L_VYPLATIT',				'Vyplati�');
	define('L_VYPLATIT_VSETKO',			'Vyplati� v�etko');
	define('L_VYPLATIT_VSETKO_SHORT',		'Vypl. v�etko');
	define('L_VYPLATNA_DIVIDENDA', 			'V�platn� dividenda');
	define('L_VYPLATNE_PODIELY', 			'V�platn� podiely');
	define('L_VYPLATY_VYNOSU',			'D�tum priznania v�nosu');
	define('L_VYPLN_UCTY',				'Vypl�te v�etky ��ty');
	define('L_VYPLN_VSETKY_POLIA',			'Vypl�te v�etky polo�ky');
	define('L_VYPOCET_NAV_K',			'V�po�et NAV ku');
	define('L_VYPOCITANE',			'V�po��tan�');
	define('L_VYPRACOVAL',				'Vypracoval');
	define('L_VYSLEDOVKA',				'V�sledovka');
	define('L_VYSOKA_DAN',				'Vysok� da�');
	define('L_VYSOKY_DATUM', 			'Vysok� d�tum');
	define('L_VYSOKY_DAT_OBCH',			'Vysok� d�tum obchodu!');
	define('L_VYSOKY_KURZ',				'Vysok� kurz');
	define('L_VYSOKY_POPLATOK',			'Vysok� poplatok');
	define('L_VYSPORIADANIA',			'Vysporiadania');
	define('L_VYSPORIADANIA_FONDU',			'Vysporiadania portf�lia');
	define('L_VYSPORIADANIE',			'Vysporiadanie');
	define('L_VYSPORIADANIE_TRANSAKCIE_CP',			'Vysporiadanie transakcie s CP');
	define('L_VYSTAVIL',				'Vystavil');
	define('L_VYSTUPNY',				'V�stupn�');
	define('L_VYSTUPNY_POPLATOK',			'V�stupn� poplatok');
	define('L_VYSTUPNY_POPLATOK_DB',		'v�stupn� poplatok');
	define('L_VYSTUPNY_POPLATOK_SHORT',		'V�st. poplat.');
	define('L_WAIT',						'�ak�');
	define('L_WAIT_EMISIA',						'Pros�m �akajte, emitujem podielov� listy.');
	define('L_WAIT_PLEASE',						'Pros�m �akajte');
	define('L_WRITE',							'Zap�sa�');
	define('L_WRITE_XLS',						'Zap�sa� do XLS');
	define('L_YEAR',							'Rok');
	define('L_YEAR_ODPLATA_SPRAVCA',			'Poplatok za spr�vu portf�lia (PK)');
	define('L_YES',								'�no');
	define('L_YOU_ARE_BEFORE_DATE',				'D�tum z��tovania je v bud�cnosti.');
	define('L_Z_VYNOSU',				'Z v�nosu');
	define('L_ZACIATOK_RIADENIA',				'Za�iatok riadenia');
	define('L_ZADAJTE_INV_SUMU',				'Zadajte investovan� sumu');
	define('L_ZADAJTE_AGENTID',					'Zadajte ��slo agenta');
	define('L_ZADAJTE_HESLO',					'Zadajte heslo');
	define('L_ZADAJTE_MENU', 					'Zadajte menu');
	define('L_ZADAJTE_NAZOV', 					'Zadajte n�zov');
	define('L_ZADAJTE_PARTNERID', 				'Zadajte ��slo partnera');
	define('L_ZADAJTE_SADZBU', 'Zadajte sadzbu');
	define('L_ZADAJTE_SKRATKU', 				'Zadajte skratku');
	define('L_ZADAJTE_SPRAVNU_SADZBU', 'Zadajte spr�vnu sadzbu');
	define('L_ZADAJTE_SUMU', 'Zadajte sumu');
	define('L_ZADAJTE_UCET', 					'Zadajte ��et');
	define('L_ZADAJTE_USERNAME',				'Zadajte u��vate�sk� meno');
	define('L_ZADAJ_KURZ',						'Zadajte kurz');
 define('L_ZADAJ_KS',						'Zadajte kon�tantn� symbol');
	define('L_ZADAJ_LIMIT_KURZ',				'Zadajte limitn� kurz');
	define('L_ZADAJ_MENOVY_PAR',				'Zadaj ch�baj�ci menov� p�r');
	define('L_ZADAJ_SPLATNOST',					'Zadajte d�tum splatnosti');
	define('L_ZADAJ_DATUM_M_VYSPORIADANIA',		'Zadajte d�tum majetkov�ho vysporiadania');
	define('L_ZADAJ_DATUM_F_VYSPORIADANIA',		'Zadajte d�tum finan�n�ho vysporiadania');
	define('L_VELKE_PERCENTO_POPLATKU',			'Percento poplatku mus� by� men�ie ako 100');
	define('L_ZADAJ_SUMU',						'Zadajte sumu');
	define('L_ZADAJ_PERCENTO_POPLATKU',			'Zadajte perceno poplatku');
 define('L_ZADAJ_UCET_PARTNERA',			'Zadajte ��et partnera');
	define('L_ZADAJ_VS',						'Zadajte variabiln� symbol');
	define('L_ZADANIE_KONF_CP',					'Zadanie konfirm�cie CP');
	define('L_ZADANIE_PLATBY',					'Zadanie platby');
	define('L_ZADAT',							'Zada�');
	define('L_ZADAT_KURZY',						'Zada� kurzy');
	define('L_ZADAT_Z_TEMPLATE',				'Zada� z preddefinovan�ho');
	define('L_ZAEVIDOVANIE_NEPREBEHLO', 		'Zaevidovanie neprebehlo');
	define('L_ZAEVIDOVANIE_PREBEHLO_OK', 		'Zaevidovanie prebehlo v poriadku');
	define('L_ZAEVIDOVANIE_STRATY_NEPREBEHLO', 	'Zaevidovanie straty neprebehlo');
	define('L_ZAEVIDOVANIE_STRATY_PREBEHLO_OK', 	'Zaevidovanie straty prebehlo v poriadku');
	define('L_ZAEVIDOVAT',				'Zaevidova�');
	define('L_ZAEVIDOVAT_STRATU',			'Zaevidova� stratu');
	define('L_ZAEVIDOVAT_ZISK',			'Zaevidova� v�nos');
	define('L_ZAEVIDOVAT_VYTLACENE_VYPISY',		'Zaevidova� vytla�en� v�pisy');
	define('L_ZAEVIDUJ_AKO',			'Zaeviduj ako');
	define('L_ZAKLAD',				'Z�klad');
	define('L_ZAKLAD_DANE',				'Z�klad dane');
	define('L_ZAKON',				'Z�kon');
	define('L_ZAPIS',				'Zap�');
	define('L_ZAPLATENE',				'Zaplaten�');
	define('L_ZASLAT_POSTOU',			'Zasla� po�tou');
	define('L_ZASTUPCA_KONATEL',		'Z�konn� z�stupca alebo konate� v mene pr�vnickej osoby');
	define('L_ZAUCTOVANE',				'Za��tovan�');
	define('L_ZAUCTOVANIE_DOSLYCH_AKTIV',		'Za��tovanie do�l�ch akt�v');
	define('L_ZAUCTOVANIE_DOSLYCH_PLATIEB',		'Za��tovanie do�l�ch platieb');
	define('L_ZAUCTOVAT',				'Za��tova�');
	define('L_ZAUCTUJ',				'Zaevidova�');
	define('L_ZAUCTUJ_AKO',				'Zaevidova� ako');
	define('L_ZAVAZKY',				'Z�v�zky');
	define('L_ZA_DANYCH_PODMIENOK',			'Kv�li podmienke min. zostatku je mo�n� vybra� len ');
	define('L_ZAZNAM_PRE_DANY_FOND_UZ_EXISTUJE',	'Z�znam pre dan� fond u� existuje!');
	define('L_ZENA',				'�ena');
	define('L_ZHODNOTENIE',				'Zhodnotenie');
	define('L_ZHODNOTENIE_PORTFOLIOVYCH_AKTIV',	'Zhodnotenie portf�liov�ch akt�v');
	define('L_ZHODNOTENIE_FLAT',		'Zhodnotenie Flat');
	define('L_ZHODNOTENIE_PA',			'Zhodnotenie p.a.');
	define('L_ZIADATEL',				'�iadate�');
	define('L_ZIADNE_ZIADOSTI',			'Za dan� obchodn� de� neboli prijat� �iadne �iadosti.');
	define('L_ZIADNY_CP_VYBER',			'Na danom portf�liu nie je cenn� papier na v�ber.');
	define('L_ZIADOSTEMISIA',			'�iados� emisia');
	define('L_ZIADOSTI',				'�iadosti');
	define('L_ZIADOSTI_DS',				'�iadosti o DS');
	define('L_ZIADOST_BOLA_OPRAVENA',	'�iados� bola opraven�.');
	define('L_ZIADOST_BOLA_PRIDANA',	'�iados� bola pridan�.');
	define('L_ZIADOST_DS',				'�iados� o doplnkov� slu�by');
	define('L_ZIADOST_EMISIA',			'�iados� o vklad');
	define('L_ZIADOST_EMISIA_NO',		'��slo �iadosti');
	define('L_ZIADOST_PRESUN',			'�iados� o presun');
	define('L_ZIADOST_PREVOD',			'�iados� o prevod/prechod');
	define('L_ZIADOST_PRIDELENE_CISLO',	'�iadosti bolo pridelen� ��slo');
	define('L_ZIADOST_REEMISIA',		'�iados� o v�ber');
	define('L_ZIAD_REDEMACIA_PL',		'V�ber');
	define('L_ZIAD_VYDANIE_PL',			'Vklad');
	define('L_ZISK_STRATA',				'Zisk/strata');
	define('L_ZLIKVIDNIT_VSETKO',		'Kv�li podmienke \"vyplati� v�etko\" je nutn� zlikvidni� v�etky prostriedky v portf�liu.');
	define('L_ZISKAVAM_UDAJE',			'Z�skavam �daje');
	define('L_ZIVE',					'�iv�');
	define('L_ZMENA',				'Zmena');
	define('L_ZMENIL',					'Zmenil');
	define('L_ZMENIT_STAV',				'Zmeni� stav');
	define('L_ZMENY_BOLI_ZAPISANE',			'Zmeny boli zap�san�');
	define('L_ZMENY_NEBOLI_ZAPISANE',		'Zmeny neboli zap�san�');
	define('L_ZOBRAZIT_AJ_VYBAVENE',		'Zobrazi� aj vybaven�');
	define('L_ZOBRAZIT_NAV',			'Zobrazi� trhov� hodnotu');
	define('L_ZOBRAZIT_VSETKY',			'Zobrazi� v�etky');
	define('L_ZOSKUPENE_PODLA',			'Zobrazi� akt�va zoskupen� pod�a');
	define('L_ZOST',				'Zost.');
	define('L_ZOSTATOK',				'Disponibiln� zostatok');
	define('L_ZOSTATOK2',				'Zostatok');
	define('L_ZOSTATOK_JE_MENSI',			'Zostatok je men�� ako minim�lny povolen� zostatok.');
	define('L_ZOSTAVA_VYTLACENA',			'Zostava vytla�en�');
	define('L_ZOZNAM_EXPORTOV',			'Zoznam exportov');
	define('L_ZOZNAM_UZIVATELOV',			'Zoznam u��vate�ov');
	define('L_ZOZNAM_CP',			'Zoznam dne�n�ch transakci� CP');
	define('L_ZREAL',				'Zreal.');
	define('L_ZREALIZ',				'Zrealizovan�');
	define('L_ZREALIZOVANE',			'Zrealizovan�');
	define('L_ZRIADENIE_TERMINOVANEHO_VKLADU_T',	'ZRIADENIE TERM�NOVAN�HO VKLADU');
	define('L_ZUCTOVACI_KURZ',			'Z��tovac� kurz');
	define('L_ZVOLTE_TITUL',			'Zvo�te titul');
	define('L_ZVYSNE_PODIELY',			'Zvy�n� podiely');
	define('L_EXPORT_KURZY_AKTIV',			'Export kurzov akt�v');
	define('L_UROK_KTV',				'�roky KTV');
	
	define('L_STAV_PORTFOLIA',			'�trukt�ra portf�lia');
	define('L_STAV_PORTFOLIA_KLIENT',		'�trukt�ra portf�lia pre klienta');
	define('L_SUMA_SKK',				'Suma (SKK)');
	define('L_ZACIATOK_SPRAVY',				'Za�iatok spr�vy');
	define('L_VLOZENE_PROSTRIEDKY',				'Vlo�en� prostriedky');
	define('L_STAV_MAJETKU_K',				'Stav majetku k');

	define('FUNKCIA_EMISIA',			1);
	define('FUNKCIA_REDEMACIA',			2);
	define('FUNKCIA_ZAVAZOK',			3);
	
	define('L_OBJEMY_PRED_REKONF',			'Objemy poplatkov<br>pred rekonfirm�ciou');
	define('L_PRED_REKONFIRMACIOU',			'Pred rekonfirm�ciou');
	define('L_REKONFIRMOVANE',			'Rekonfirmovan�');
	
	//lang pre upgrade2006
	define('L_JE_SUCASTOU_GFI','Je s��as�ou GFI');
	define('L_TYP_ZDANENIA','Typ zdanenia');
	define('L_TYP_UCTU','Typ ��tu');
	define('L_SPOSOB_UHRADY','Sp�sob �hrady');
	define('L_FAKTURA','Fakt�ra');
	define('L_POVODNE_ZOSTAVY','P�vodn� zostavy');
	define('L_ZA_SPRAVU_A_RIADENIE','Za spr�vu a riadenie');
	define('L_OBCHODY_VYSPORIADANIE','Obchody a vysporiadanie');
	define('L_RATING_CP','Rating CP');
	define('L_RA_AGENCY','Agent�ra');
	define('L_ACTUAL','Aktu�lne');
	define('L_RATING_DATE','D�tum ratingu');
	define('L_RATING','Rating');
	define('L_INVEST_RANGE_SHORT','Inv. p�smo');
	define('L_INVEST_RANGE','Investi�n� p�smo');
	define('L_MAJ_UCET','Maj. ��et');
	define('L_TYP_POD_LISTU','Typ pod.listu');
	define('L_Z_PORTFOLIA','Z portf�lia');
	define('L_NA_PORTFOLIO','Na portf�lio');
	define('L_DATUM_ZADANIA','D�tum zadania');
	define('L_ZRUSENIE','Zru�enie');
	define('L_POTVRDENIE','Potvrdenie');
	define('L_VYBER_PP','V�ber pe�a�n�ch prostriedkov');
	define('L_VKLAD_PP','Vklad pe�a�n�ch prostriedkov');
	define('L_VKLAD_PEN_PROSTR','Vklad pe�.prostr.');
	define('L_PODLA_UCTU','Pod�a ��tu');
	define('L_PODLA_MENY','Pod�a meny');
	define('L_S_PRESUNOM','S presunom');
	define('L_VYPLATA_HOTOVOSTI','V�plata hotovosti');
	define('L_C_ZMLUVY','�. zmluvy');
	define('L_POHLADAVKA','Poh�ad�vka');
	define('L_OPCNA_DIVIDENDA','Op�n� dividenda');
	define('L_MIMORIADNA_DIVIDENDA','Mimoriadna dividenda');
	define('L_FRAKCIE','Frakcie');
	define('L_ZNIZ_ZAKLAD_IMANIA','Zn�enie z�kladn�ho imania');
	define('L_BONUS','Bonus');
	define('L_FUZIA','F�zia');
	define('L_AKVIZICIA','Akviz�cia');
	define('L_ROZDELENIE_SPOL','Rozdelenie spolo�nosti');
	define('L_OPCNA_DIVIDENDA_AKC','Op�n� dividenda v akci�ch');
	define('L_DIVIDENDA_AKC','Dividenda v akci�ch');
	define('L_PRIPISANIE_AKCII','Prip�sanie akci�');
	define('L_SPLIT','Split');
	define('L_ODPISANIE_AKCII','Odp�sanie akci�');
	define('L_ZAVAZOK','Z�v�zok');
	define('L_SPLATENIE_AKCIA','Splatenie akcie');
	define('L_REALNE','Re�lne');
	define('L_FIKTIVNE','Fikt�vne');
	define('L_PREHLAD_OCAK_FIN_TOK','Preh�ad o�ak�van�ch fin. tokov');
	define('L_DAN_Z_KTV','Da� z KTV');
	define('L_DAN_Z_CP','Da� z CP');
	define('L_PREHLAD_TRAN_HROMADNE','Preh�ad transakci� hromadne');
	define('L_PREHLAD_TRAN_PORTFOLIO','Preh�ad transakci� po portf�li�ch');
	define('L_TERMINOVANY_UCET','Term�novan� ��et');
	define('L_MAJETKOVY_UCET','Majetkov� ��et');
	define('L_POPLATKYTRANSAKCIE','Poplatky za transakcie');
	define('L_POPLATKYOSTATNE','Ostatn� poplatky');
	define('L_LONG_VKLAD_CP','Vklad cenn�ch papierov');
	define('L_LONG_VYBER_CP','V�ber cenn�ch papierov');
	define('L_LONG_PREVOD_CP','Prevod cenn�ch papierov');
	define('L_LONG_PRESUN_CP','Presun pe�a�n�ch prostr');
	define('L_SPOSOB_UHRADY_POPLATKOV','Sp�sob �hrady poplatkov');
	define('L_Z_UCTU','Z ��tu');
	define('L_NA_UCET','Na ��et');
	define('L_ZMENA_INV_ZAMERU','Zmena inv. z�meru');
	define('L_ZMENA_SUMY','Zmena sumy');
	define('L_ZMENA_MENY','Zmena meny');
	define('L_UKONCENIE_ZMLUVY','Ukon�enie zmluvy');
	define('L_ZADANIE_POPLATKU','Zadanie poplatku');
	define('L_OPRAVA_POPLATKOV','Oprava poplatku');
	define('L_INVESTICNY_NASTROJ','Investi�n� n�stroj');
	define('L_POTVRDENIE_TRANSAKCIE','Potvrdenie transakcie');
	define('L_PRESUN_PP','Presun pe�a�n�ch prostriedkov');
	define('L_POPLATOK_DOBROPIS','Poplatok dobropis');
	define('L_DATUM_PREVODU','D�tum prevodu');
	define('L_SUHRNNE','S�hrnne');
	define('L_PO_VYNOSOCH','Po v�nosoch');
	define('L_JEDNOTLIVO','Jednotlivo');
  	define('L_RISK_MANAZMENT',				'Risk mana�ment');
	define('L_DURACIA_DLHOPISOVEJ_CASTI_PORTFOLII',		'Dur�cia dlhopisovej �asti portf�li�');
	define('L_DURACIA_PORTFOLII',			'Dur�cia portf�li�');
	define('L_VYVOJ_AKCII_A_PODIELOVYCH_LISTOV',	'V�voj akci� a podielov�ch listov');
  	define('L_VYVOJ_DLHOPISOV',	'V�voj dlhopisov');	
	define('L_DEVIZIOVA_POZICIA_PORTFOLII',		'Dev�zov� poz�cia portf�li�');
	define('L_USPORIADANIE', 'Usporiadanie:');
	define('L_VYVOJ_KURZOV', 'V�voj kurzov');
	define('L_SUHRN', 'S�hrn');
	define('L_PLATBA_ZA_PREDANE_PRAVA', 'Platba za predan� pr�va');
	define('L_PRIPISANIE_PRAV_ZVYSENIE_ZI', 'Prip�sanie pr�v - zv��enie ZI');
	define('L_ODPISANIE_PRAV_PREDAJ', 'Odp�sanie pr�v - predaj');
	define('L_ODPISANIE_PRAV_UPIS_AKCII', 'Odp�sanie pr�v - �pis akci�');
	define('L_UPIS_AKCII_ZVYSENIE_ZI', '�pis akci� - zv��enie ZI');
	define('L_PRIPISANIE_PRAV_DIVIDENDA_STOCK', 'Prip�sanie pr�v - dividenda stock');
	define('L_PLATBA_ZA_PREDAJ_AKCII_TENDER', 'Platba za predaj akci� - tender');
	define('L_ODPISANIE_AKCII_TENDER', 'Odp�sanie akci� - tender');
	define('L_POZICIA_V_PENAZNYCH_PROSTRIEDKOCH','Poz�cia v pe�a�n�ch prostriedkoch');
	define('L_VYSPORIADANE','Vysporiadan�');
	define('L_NEVYSPORIADANE','Nevysporiadan�');
	define('L_POPLATKY_EXPORT','Export poplatkov');
	define('L_POPLATKY_INTERNE','Intern� v�po�et poplatkov');
	define('L_POPLATKY_MESACNE','Pr�jmy bud�cich obdob�');
	define('L_POPLATKY_PROGNOZA','Progn�za poplatkov');
	define('L_POPLATKY_TEST','Testovanie poplatkov');
	define('L_SUMARNA_DEVIZOVA_POZICIA','Sum�rna dev�zov� poz�cia');
	define('L_VYVOJ_DLHOPISOV','V�voj dlhopisov');
	define('L_VYVOJ_AKCII_HISTORY','V�voj akci� (vr�tane u� predan�ch)');
	define('L_VYVOJ_AKCII','V�voj akci�');
	define('L_VYVOJ_AKCII_NO_HISTORY','V�voj akci� (aktu�lnych)');
	define('L_DODATOCNE_VYROVNANIE_EMITENTA', 'Dodato�n� vyrovnanie emitenta');
	define('L_ODPISANIE_AKCII_ZMENA_ZI', 'Odp�sanie akci� - zmena ZI');
	define('L_REVERSE_STOCK_SPLIT', 'Reverse stock split');
	define('L_SQUEEZE_OUT', 'Squeeze-out');
        define('L_VYMENA_AKCII', 'V�mena akci�');
	define('L_VYBER_MENOVY_PAR', 'Vyber menov� p�r');
        define('L_VYPLATA_EMISNEHO_AZIA', 'V�plata emisn�ho �ia');
  ?>

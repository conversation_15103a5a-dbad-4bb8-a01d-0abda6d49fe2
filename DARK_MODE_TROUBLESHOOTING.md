# Dark Mode Troubleshooting Guide

If only some elements are changing to dark mode while others remain light, here's how to diagnose and fix the issues:

## 🔍 Common Issues & Solutions

### 1. **Elements with Hardcoded Colors**

**Problem**: Elements with inline styles or CSS classes that override Tailwind's dark mode.

**How to identify**:
```bash
# Search for hardcoded background colors
grep -r "background.*#" src/
grep -r "bg.*white" src/
grep -r "color.*#" src/

# Search for inline styles
grep -r "style=" src/
```

**Solution**: Replace hardcoded colors with Tailwind dark mode classes:
```html
<!-- Before -->
<div style="background: white; color: black;">Content</div>
<div class="bg-white text-black">Content</div>

<!-- After -->
<div class="bg-white dark:bg-gray-800 text-black dark:text-white">Content</div>
```

### 2. **Missing Dark Mode Classes**

**Problem**: Elements using only light mode Tailwind classes.

**Common patterns to fix**:
```html
<!-- Backgrounds -->
bg-white → bg-white dark:bg-gray-800
bg-gray-100 → bg-gray-100 dark:bg-gray-700
bg-gray-200 → bg-gray-200 dark:bg-gray-600

<!-- Text Colors -->
text-gray-900 → text-gray-900 dark:text-white
text-gray-600 → text-gray-600 dark:text-gray-300
text-black → text-black dark:text-white

<!-- Borders -->
border-gray-300 → border-gray-300 dark:border-gray-600
border-gray-200 → border-gray-200 dark:border-gray-700

<!-- Form Elements -->
bg-gray-50 → bg-gray-50 dark:bg-gray-700
placeholder-gray-500 → placeholder-gray-500 dark:placeholder-gray-400
```

### 3. **CSS Specificity Issues**

**Problem**: Custom CSS overriding Tailwind's dark mode classes.

**Solution**: Add dark mode variants to your custom CSS:
```css
/* Before */
.my-element {
    background-color: white;
    color: black;
}

/* After */
.my-element {
    background-color: white;
    color: black;
}

.dark .my-element {
    background-color: #1f2937;
    color: #f3f4f6;
}
```

### 4. **JavaScript-Generated Content**

**Problem**: Content added via JavaScript doesn't have dark mode classes.

**Solution**: Update JavaScript to include dark mode classes:
```javascript
// Before
element.innerHTML = '<div class="bg-white text-black">Content</div>';

// After
element.innerHTML = '<div class="bg-white dark:bg-gray-800 text-black dark:text-white">Content</div>';
```

### 5. **Third-Party Components**

**Problem**: External libraries or components don't support dark mode.

**Solution**: Override with custom CSS:
```css
/* Target third-party components in dark mode */
.dark .external-component {
    background-color: #1f2937 !important;
    color: #f3f4f6 !important;
}

.dark .external-component input {
    background-color: #374151 !important;
    color: #f3f4f6 !important;
    border-color: #4b5563 !important;
}
```

## 🛠️ Debugging Tools

### 1. **Browser Developer Tools**

1. Open DevTools (F12)
2. Check if `dark` class is on `<html>` element
3. Inspect elements that aren't changing
4. Look for overriding CSS rules

### 2. **CSS Inspection**

```javascript
// Check if dark mode is active
console.log(document.documentElement.classList.contains('dark'));

// Check theme manager status
console.log(window.themeManager?.getCurrentTheme());

// Find elements without dark mode classes
document.querySelectorAll('[class*="bg-white"]:not([class*="dark:"])').forEach(el => {
    console.log('Missing dark mode:', el);
});
```

### 3. **Automated Detection Script**

Add this to your page to find elements missing dark mode:

```javascript
function findMissingDarkModeElements() {
    const lightPatterns = [
        'bg-white', 'bg-gray-50', 'bg-gray-100', 'bg-gray-200',
        'text-black', 'text-gray-900', 'text-gray-800',
        'border-gray-200', 'border-gray-300'
    ];
    
    const issues = [];
    
    lightPatterns.forEach(pattern => {
        const elements = document.querySelectorAll(`[class*="${pattern}"]:not([class*="dark:"])`);
        elements.forEach(el => {
            issues.push({
                element: el,
                issue: `Missing dark mode for ${pattern}`,
                classes: el.className
            });
        });
    });
    
    console.table(issues);
    return issues;
}

// Run the check
findMissingDarkModeElements();
```

## 🎯 Quick Fixes for Common Elements

### Forms
```html
<input class="border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg px-3 py-2">
<select class="border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg px-3 py-2">
<textarea class="border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg px-3 py-2"></textarea>
```

### Cards
```html
<div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg">
    <div class="p-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Title</h3>
        <p class="text-gray-600 dark:text-gray-300">Content</p>
    </div>
</div>
```

### Tables
```html
<table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
    <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
        <tr>
            <th class="px-6 py-3">Header</th>
        </tr>
    </thead>
    <tbody>
        <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
            <td class="px-6 py-4">Data</td>
        </tr>
    </tbody>
</table>
```

### Buttons
```html
<!-- Primary Button -->
<button class="bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 text-white px-4 py-2 rounded-lg">
    Primary
</button>

<!-- Secondary Button -->
<button class="bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-900 dark:text-white px-4 py-2 rounded-lg">
    Secondary
</button>
```

## 🔧 Systematic Approach

1. **Start with the main layout**: Ensure `<html>`, `<body>`, and main containers have dark mode classes
2. **Fix navigation**: Update menu, header, and sidebar components
3. **Update content areas**: Fix main content, cards, and forms
4. **Handle dynamic content**: Update JavaScript-generated elements
5. **Test thoroughly**: Check all pages and interactive elements

## 📝 Checklist

- [ ] HTML element has `dark` class when theme is dark
- [ ] Main layout containers have dark mode backgrounds
- [ ] Navigation elements support dark mode
- [ ] Forms and inputs have dark mode styles
- [ ] Tables and data displays work in dark mode
- [ ] Buttons and interactive elements support dark mode
- [ ] Text colors are readable in both themes
- [ ] Borders and dividers are visible in both themes
- [ ] Dynamic/HTMX content includes dark mode classes
- [ ] Third-party components are styled for dark mode

## 🚀 Pro Tips

1. **Use CSS custom properties** for consistent theming:
```css
:root {
    --bg-primary: #ffffff;
    --text-primary: #1f2937;
}

.dark {
    --bg-primary: #1f2937;
    --text-primary: #ffffff;
}

.my-component {
    background-color: var(--bg-primary);
    color: var(--text-primary);
}
```

2. **Create utility classes** for common patterns:
```css
.theme-bg-primary {
    @apply bg-white dark:bg-gray-800;
}

.theme-text-primary {
    @apply text-gray-900 dark:text-white;
}

.theme-border {
    @apply border-gray-200 dark:border-gray-700;
}
```

3. **Use the theme manager events** to update complex components:
```javascript
window.addEventListener('themeChanged', (event) => {
    // Update charts, maps, or other complex components
    updateChartTheme(event.detail.theme);
});
```

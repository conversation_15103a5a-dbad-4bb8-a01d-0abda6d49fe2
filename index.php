<?php
session_start();
$ds = DIRECTORY_SEPARATOR;
$base_dir = realpath(dirname(__FILE__)) . $ds;
$rootDir = 'src/';
$viewDir = '/src/Views/';
$apirDir = '/src/Controllers/';
$componentsDir = '/src/Components/';
$logDir = '/temp';
$route = $_SERVER['REQUEST_URI'];
$route = parse_url($route, PHP_URL_PATH);
$hxRequest = isset($_SERVER['HTTP_HX_REQUEST']) && $_SERVER['HTTP_HX_REQUEST'] == 'true';
$isLoggedIn = isset($_SESSION["user"]);

register_shutdown_function(function () use ($isLoggedIn) {
    global $content, $hxRequest, $viewDir, $breadcrumb, $menu, $notifBtn;
    if ($hxRequest) {
        echo $content;
    } else {
        if ($isLoggedIn) {
            require __DIR__ . $viewDir . 'layout.php';
        } else {
            require __DIR__ . $viewDir . 'layout_guest.php';
        }
    }
});


function exception_handler($throwable): void
{
    global $content, $logDir;
    $logFile = __DIR__ . $logDir . '/error_log.txt';
    $message = date('Y-m-d H:i:s') . ' - Error: ' . $throwable->getMessage() . ' in ' . $throwable->getFile() . ' on line ' . $throwable->getLine() . PHP_EOL;
    file_put_contents($logFile, $message, FILE_APPEND);
    $content = "An unexpected error occurred. Please check the logs for more information." . $message;
}

set_exception_handler('exception_handler');

if ($isLoggedIn) {
    $routes = [
        '/' => $viewDir . 'dashboard.php',
        '/dashboard' => $viewDir . 'dashboard.php',
        '/clickup' => $viewDir . 'clickup.php',
        '/breadcrumb' => $componentsDir . 'layout/header/breadcrumb.php',
        '/admin' => $viewDir . 'Spravca/spravcaLayout.php',
        //KLIENTI ROUTES
        '/klienti' => $viewDir . 'klient/layout.php',
        '/klienti/detail/(\d+)/?' => $viewDir . 'klient/clientDetailPage.php',
        '/klienti/edit/(\d+)/?' => $viewDir . 'klient/clientEditPage.php',
        '/klienti/create-new' => $viewDir . 'klient/createClientPage.php',
        '/klienti/create-new/step-2/(\d+)/?' => $viewDir . 'klient/createClientStep2.php',
        '/klienti/create-new/final-step/(\d+)/?' => $viewDir . "klient/createClientFinalStep.php",

        '/portfolia/detail/([a-zA-Z0-9]+)/?' => $viewDir . "portfolio/portfolioPage.php",
        '/demo' => $viewDir . "theme-demo.php",


        '/aktiva/cenne-papiere' => $viewDir . "aktiva/cp/index.php",
        '/aktiva/create-new' => $viewDir . "aktiva/cp/createCPForm.php",
        '/aktiva/cenne-papiere/detail/([a-zA-Z0-9]+)/?' => $viewDir . "aktiva/cp/detail.php",
        '/aktiva/cenne-papiere/edit/([a-zA-Z0-9]+)/?' => $viewDir . "aktiva/cp/edit.php",
        '/aktiva/sankcny-zoznam' => $viewDir . "default/sanctionList.php",
        '/aktiva/sankcny-zoznam/historia' => $viewDir . "default/sanctionHistory.php",
        '/hromadna-tlac' => $viewDir . 'HromadnaTlac/printToPDF.php',
        //PREHLADY
        '/prehlady/ucty/bezny-ucet' => $viewDir . "prehlady/ucty/bezny-ucet/index.php",
        '/prehlady/ucty/majetkovy-ucet' => $viewDir . "prehlady/ucty/majetkovy-ucet/index.php",
        '/prehlady/ucty/majetkovy-ucet/([a-zA-Z0-9]+)/?' => $viewDir . "prehlady/ucty/majetkovy-ucet/detail.php",
        '/prehlady/ucty/terminovany-ucet' => $viewDir . "prehlady/ucty/terminovany-ucet/index.php",
        '/prehlady/klientsky-vypis' => $viewDir . "prehlady/vypis_o_stave_majetku/index.php",
        '/prehlady/transakcie' => $viewDir . "prehlady/prehlad_transakcii/index.php",
        '/prehlady/vypis-o-stave-majetku' => $viewDir . "prehlady/financne-toky/index.php",
        '/prehlady/poplatky' => $viewDir . "prehlady/poplatky/index.php",
        '/prehlady/vyplatene-vynosy' => $viewDir . "prehlady/vyplatene-vynosy/index.php",
        '/prehlady/nbs-gfi' => $viewDir . "prehlady/nbs-gfi/index.php",
        '/prehlady/zmluvy-klienti' => $viewDir . "prehlady/zmluvy-klienti/index.php",

        '/menove-pary' => $viewDir . "menove-pary/index.php",
        '/api/menove-pary/create' => $apirDir . "menove-pary/create.php",
        '/api/menove-pary/edit' => $apirDir . "menove-pary/edit.php",
        '/api/menove-pary/delete' => $apirDir . "menove-pary/delete.php",
        '/api/menove-pary/update' => $apirDir . "menove-pary/update.php",

        //UZAVIERKA
        '/uzavierka' => $viewDir . "uzavierka/index.php",


        // OBCHODNY DENNIK ROUTES
        '/obchodny-dennik' => $viewDir . "dennik/index.php",
        '/obchodny-dennik/terminovane-vklady' => $viewDir . "dennik/terminovane-vklady.php",
        '/obchodny-dennik/dlhopisy' => $viewDir . "dennik/dlhopisy.php",
        '/obchodny-dennik/akcie' => $viewDir . "dennik/akcie.php",
        '/obchodny-dennik/konverzie' => $viewDir . "dennik/konverzie.php",
        '/obchodny-dennik/vklady-penaznych-prostriedkov' => $viewDir . "dennik/penazne-prostriedky-vklady.php",
        '/obchodny-dennik/vybery-penaznych-prostriedkov/vysporiadane' => $viewDir . "dennik/vybery-prostriedkov/vysporiadane.php",
        '/obchodny-dennik/vybery-penaznych-prostriedkov/nevysporiadane' => $viewDir . "dennik/vybery-prostriedkov/nevysporiadane.php",
        '/obchodny-dennik/presuny-penaznych-prostriedkov/vysporiadane' => $viewDir . "dennik/presuny-prostriedkov/vysporiadane.php",
        '/obchodny-dennik/presuny-penaznych-prostriedkov/nevysporiadane' => $viewDir . "dennik/presuny-prostriedkov/nevysporiadane.php",
        '/obchodny-dennik/prevody-penaznych-prostriedkov/vysporiadane' => $viewDir . "dennik/prevody-prostriedkov/vysporiadane.php",
        '/obchodny-dennik/prevody-penaznych-prostriedkov/nevysporiadane' => $viewDir . "dennik/prevody-prostriedkov/nevysporiadane.php",
        '/obchodny-dennik/vklady-cennych-papierov' => $viewDir . "dennik/vklady-cennych-papierov.php",
        '/obchodny-dennik/vybery-cennych-papierov' => $viewDir . "dennik/vybery-cennych-papierov.php",
        '/obchodny-dennik/prevody-cennych-papierov' => $viewDir . "dennik/prevody-cennych-papierov.php",
        '/obchodny-dennik/platby-poplatkov' => $viewDir . "dennik/platby-poplatkov.php",
        '/obchodny-dennik/podielove-fondy' => $viewDir . "dennik/podielove-fondy.php",
        // OBCHODNY DENNIK API
        '/api/obchodny-dennik/terminovane-vklady' => $apirDir . "dennik/terminovane-vklady.php",
        '/api/obchodny-dennik/dlhopisy' => $apirDir . "dennik/dlhopisy.php",
        '/api/obchodny-dennik/akcie' => $apirDir . "dennik/akcie.php",
        '/api/obchodny-dennik/konverzie' => $apirDir . "dennik/konverzie.php",
        '/api/obchodny-dennik/vklady-penaznych-prostriedkov' => $apirDir . "dennik/vklady-penaznych-prostriedkov.php",
        '/api/obchodny-dennik/vybery-penaznych-prostriedkov/vysporiadane' => $apirDir . "dennik/vybery-vysporiadane.php",
        '/api/obchodny-dennik/presuny-penaznych-prostriedkov/vysporiadane' => $apirDir . "dennik/presuny-vysporiadane.php",
        '/api/obchodny-dennik/prevody-penaznych-prostriedkov/vysporiadane' => $apirDir . "dennik/prevody-vysporiadane.php",
        '/api/obchodny-dennik/vklady-cennych-papierov' => $apirDir . "dennik/vklady-cennych-papierov.php",
        '/api/obchodny-dennik/vybery-cennych-papierov' => $apirDir . "dennik/vybery-cennych-papierov.php",
        '/api/obchodny-dennik/prevody-cennych-papierov' => $apirDir . "dennik/prevody-cennych-papierov.php",
        '/api/obchodny-dennik/platby-poplatkov' => $apirDir . "dennik/platby-poplatkov.php",
        '/api/obchodny-dennik/podielove-fondy' => $apirDir . "dennik/podielove-fondy.php",
        '/api/obchodny-dennik/platby-poplatkov/data/?' => $apirDir . "dennik/platby-poplatkov-data.php",


        //HROMADNY REZIM -----------------------------------------------------------------------------------------------------------------
        '/investicne-zamery' => $viewDir . "global/investicne-zamery/index.php",
        '/investicne-zamery/pridat/terminovany-vklad' => $viewDir . "global/investicne-zamery/add/terminovany-vklad.php",
        '/investicne-zamery/pridat/terminovany-vklad/vyber-uctu/?' => $viewDir . "global/investicne-zamery/add/TV-vyber-uctu.php",
        '/investicne-zamery/pridat/terminovany-vklad/zakladne-informacie' => $viewDir . "global/investicne-zamery/add/TV-create.php",
        '/investicne-zamery/pridat/dlhopisy' => $viewDir . "global/investicne-zamery/add/cp.php",
        '/investicne-zamery/pridat/dlhopisy/new/([a-zA-Z0-9]+)/?' => $viewDir . "global/investicne-zamery/add/createDlhopis.php",
        '/investicne-zamery/pridat/akcie/new/([a-zA-Z0-9]+)/?' => $viewDir . "global/investicne-zamery/add/createShare.php",
        '/investicne-zamery/pridat/akcie' => $viewDir . "global/investicne-zamery/add/cp.php",
        '/investicne-zamery/pridat/podielove-fondy' => $viewDir . "global/investicne-zamery/add/cp.php",
        '/investicne-zamery/pridat/podielove-fondy/new/([a-zA-Z0-9]+)/?' => $viewDir . "global/investicne-zamery/add/createFond.php",
        '/investicne-zamery/pridat/konverzie' => $viewDir . "global/investicne-zamery/add/konverzie.php",
        '/investicne-zamery/konverzia/edit/([a-zA-Z0-9]+)/?' => $viewDir . "global/investicne-zamery/edit/konv.php",
        '/investicne-zamery/terminovany-vklad/edit/([a-zA-Z0-9]+)/?' => $viewDir . "global/investicne-zamery/edit/terminovany-vklad.php",
        '/investicne-zamery/akcia/edit/([a-zA-Z0-9]+)/?' => $viewDir . "global/investicne-zamery/edit/cp.php",
        '/investicne-zamery/dlhopis/edit/([a-zA-Z0-9]+)/?' => $viewDir . "global/investicne-zamery/edit/cp.php",
        '/investicne-zamery/fond/edit/([a-zA-Z0-9]+)/?' => $viewDir . "global/investicne-zamery/edit/cp.php",
        '/dlhopisy/kupon/potvrdenie' => $viewDir . "global/dlhopisy/zoznam.php",
        '/dlhopisy/kupon/splatenie' => $viewDir . "global/dlhopisy/zoznam.php",
        '/dlhopisy/kupon/potvrdenie/([^/]+)/?' => $viewDir . "global/dlhopisy/dlhopisDetail.php",
        '/dlhopisy/kupon/splatenie/([^/]+)/?' => $viewDir . "global/dlhopisy/dlhopisDetail.php",
        '/dlhopisy/istina/potvrdenie' => $viewDir . "global/dlhopisy/zoznam.php",
        '/dlhopisy/istina/splatenie' => $viewDir . "global/dlhopisy/zoznam.php",
        '/dlhopisy/istina/potvrdenie/([^/]+)/?' => $viewDir . "global/dlhopisy/dlhopisDetail.php",
        '/dlhopisy/istina/splatenie/([^/]+)/?' => $viewDir . "global/dlhopisy/dlhopisDetail.php",
        '/terminovany-vklad/potvrdenie' => $viewDir . "global/terminovany-vklad/index.php",

        // VYSPORIDANIE
        '/vysporiadanie/penazne/odchazajuce/natipovanie' => $viewDir . "global/vysporiadanie/uhrady.php",
        '/vysporiadanie/majetkove/odchadzajuce' => $viewDir . "global/vysporiadanie/majetkove/odchadzajuce/index.php",
        '/vysporiadanie/penazne/odchadzajuce/natipovanie/uhrada/?' => $viewDir . "global/vysporiadanie/penazne/odchadzajuce/uhradyDetail.php",
        '/vysporiadanie/penazne/prichadzajuce/natipovanie' => $viewDir . "global/vysporiadanie/uhrady.php",
        '/vysporiadanie/majetkove/prichadzajuce' => $viewDir . "global/vysporiadanie/majetkove/prichadzajuce/index.php",
        '/vysporiadanie/penazne/prichadzajuce/natipovanie/uhrada/?' => $viewDir . "global/vysporiadanie/penazne/prichadzajuce/natipovanie.php",
        '/vysporiadanie/penazne/prichadzajuce/sparovanie' => $viewDir . "global/vysporiadanie/uhrady.php",
        '/vysporiadanie/penazne/prichadzajuce/sparovanie/uhrada/?' => $viewDir . "global/vysporiadanie/penazne/prichadzajuce/sparovanie.php",

        '/poplatky' => $viewDir . "global/poplatky/zoznam.php",


        '/presun-prostriedkov' => $viewDir . "global/presuny/index.php",
        '/presun/toAcc' => $viewDir . "global/presuny/fromAcc.php",

        '/vklad-prostriedkov' => $viewDir . "global/prostriedky/vkladProstriedkov.php",
        '/api/vklad-prostriedkov/vykonat' => $apirDir . "global/prostriedky/vlozitProstriedky.php",

        '/vyber-prostriedkov' => $viewDir . "global/prostriedky/vyberProstriedkov.php",
        '/api/vyber-prostriedkov/vykonat' => $apirDir . "global/prostriedky/vybratProstriedky.php",
        '/api/vyber-prostriedkov/delete' => $apirDir . "global/prostriedky/deleteVyber.php",
        '/api/vyber-prostriedkov/upravit' => $apirDir . "global/prostriedky/updateVyber.php",
        '/api/vyber-prostriedkov/potvrdit' => $apirDir . "global/prostriedky/potvrditVyber.php",

        '/prevod-prostriedkov' => $viewDir . "global/prostriedky/prevodProstriedkov.php",
        '/api/prevod-prostriedkov/vykonat' => $apirDir . "global/prostriedky/prevodProstriedkov.php",
        '/api/prevod-prostriedkov/potvrdit' => $apirDir . "global/prostriedky/potvrdPrevod.php",
        '/api/prevod-prostriedkov/upravit' => $apirDir . "global/prostriedky/updatePrevod.php",
        '/api/prevod-prostriedkov/delete' => $apirDir . "global/prostriedky/deletePrevod.php",

        '/transakcie' => $viewDir . "global/transakcie/index.php",
        '/zoznam-transakcii' => $viewDir . "global/zoznam-transakcii/index.php",
        '/api/transakcie/potvrditCP' => $apirDir . "global/transakcie/potvrditCP.php",
        '/api/transakcie/potvrditTV' => $apirDir . "global/transakcie/potvrditTV.php",
        '/api/transakcie/potvrditKonv' => $apirDir . "global/transakcie/potvrditKonv.php",

        '/nastavenia' => $viewDir . "settings/index.php",
        '/theme-demo' => $viewDir . "theme-demo.php",
        '/nastavenia/konstanty' => $viewDir . "settings/konstanty/konstanty.php",
        '/nastavenia/konstanty/staty' => $viewDir . "settings/konstanty/index.php",
        '/nastavenia/konstanty/emitenti/?' => $viewDir . "settings/konstanty/index.php",
        '/nastavenia/konstanty/odvetvia' => $viewDir . "settings/konstanty/index.php",
        '/nastavenia/konstanty/sektor' => $viewDir . "settings/konstanty/index.php",
        '/nastavenia/konstanty/ucet-partnera' => $viewDir . "settings/konstanty/index.php",
        '/nastavenia/konstanty/konstantny-symbol' => $viewDir . "settings/konstanty/index.php",
        '/nastavenia/konstanty/druhy-aktiv' => $viewDir . "settings/konstanty/index.php",
        '/nastavenia/konstanty/doplnkove-sluzby' => $viewDir . "settings/konstanty/index.php",
        '/nastavenia/konstanty/atributy' => $viewDir . "settings/konstanty/index.php",
        '/nastavenia/konstanty/sektory-esa95' => $viewDir . "settings/konstanty/index.php",
        '/nastavenia/konstanty/typy-pokynov' => $viewDir . "settings/konstanty/index.php",

        '/nastavenia/uzivatelia' => $viewDir . "settings/uzivatelia/index.php",

        //API ROUTES DEFAULT
        '/api/cp/archive' => $apirDir . "prehlady/cp/archive.php",
        '/api/cp/getFilteredData' => $apirDir . "aktiva/filteredData.php",
        '/api/cp/createSanction' => $apirDir . "default/sanctionList/createSanction.php",
        '/api/cp/updateSanction' => $apirDir . "default/sanctionList/updateSanction.php",
        '/api/cp/deleteSanction' => $apirDir . "default/sanctionList/deleteSanction.php",
        '/api/cp/getFilteredCPListData' => $apirDir . "default/sanctionList/cpListFilteredData.php",
        '/api/cp/getFilteredSanctions' => $apirDir . "default/sanctionList/filteredData.php",
        '/api/cp/getFilteredSanctionHistory' => $apirDir . "default/sanctionList/filteredHistory.php",
        '/api/update-spravca' => $apirDir . "spravca/php/updateZakladneUdaje.php",
        '/prehlady/poplatky/report' => $apirDir . "prehlady/poplatky/reportIndex.php",
        '/prehlady/vyplatene-vynosy/report' => $apirDir . "prehlady/vyplatene-vynosy/reportIndex.php",
        '/prehlady/nbs-gfi/nbs-report' => $apirDir . "prehlady/nbs-gfi/nbsMajetokIndex.php",
        '/prehlady/nbs-gfi/statistika-report' => $apirDir . "prehlady/nbs-gfi/statistickyReport.php",
        '/prehlady/nbs-gfi/nbs-hlasenie' => $apirDir . "prehlady/nbs-gfi/nbsHlasenie.php",
        '/prehlady/nbs-gfi/gfi-hlasenie' => $apirDir . "prehlady/nbs-gfi/gfiHlasenie.php",
        '/prehlady/nbs-gfi/mifir-hlasenie' => $apirDir . "prehlady/nbs-gfi/mifir.php",
        '/prehlady/zmluvy-klienti/report' => $apirDir . "prehlady/zmluvy-klienti/reportIndex.php",
        '/api/pdf-generator' => $apirDir . "hromadnaTlac/hromadne_transackie_do_pdf.php",

        '/api/notifications/markAllRead' => $apirDir . "notifications/markAllRead.php",

        // GLOBAL MODE API ROUTES ****************************************************** // 
        '/api/presuny/vykonat' => $apirDir . "global/presuny/doMigration.php",
        '/api/presuny/deletePrevod' => $apirDir . "global/presuny/deletePrevod.php",
        '/api/presuny/potvrdPrevod' => $apirDir . "global/presuny/potvrdPrevod.php",
        '/api/investicne-zamery/terminovany-vklad/get/accounts' => $apirDir . "global/investicne-zamery/terminovany-vklad/get/accounts.php",
        '/api/investicne-zamery/terminovany-vklad/get/creation' => $apirDir . "global/investicne-zamery/terminovany-vklad/get/creation.php",
        '/api/investicne-zamery/terminovany-vklad/get/generatePool' => $apirDir . "global/investicne-zamery/terminovany-vklad/get/generatePool.php",
        '/api/investicne-zamery/terminovany-vklad/get/filteredData' => $apirDir . "global/investicne-zamery/terminovany-vklad/get/getFilteredData.php",
        '/api/investicne-zamery/terminovany-vklad/get/createTVZamer' => $apirDir . "global/investicne-zamery/terminovany-vklad/get/createTVZamer.php",
        '/api/investicne-zamery/terminovany-vklad/update/KTV' => $apirDir . "global/investicne-zamery/terminovany-vklad/update/updateTVZamer.php",
        '/api/investicne-zamery/terminovany-vklad/confirm/KTV' => $apirDir . "global/investicne-zamery/terminovany-vklad/update/konfTVZamer.php",
        '/api/investicne-zamery/terminovany-vklad/delete/delete' => $apirDir . "global/investicne-zamery/terminovany-vklad/get/delete/deleteInvesticnyZamer.php",

        '/api/investicne-zamery/cp/get/initialData' => $apirDir . "global/investicne-zamery/cp/get/initialData.php",
        '/api/investicne-zamery/konverzie/get/initialData' => $apirDir . "global/investicne-zamery/konverzie/form.php",
        '/api/investicne-zamery/generatePool' => $apirDir . "global/investicne-zamery/generatePool.php",
        '/api/investicne-zamery/dlhopisy/add' => $apirDir . "global/investicne-zamery/dlhopisy/create.php",
        '/api/penazne-fondy/get/account' => $apirDir . "global/investicne-zamery/konverzie/getAccount.php",

        '/api/investicne-zamery/cp/create' => $apirDir . "global/investicne-zamery/createCPZamer.php",
        '/api/investicne-zamery/cp/update' => $apirDir . "global/investicne-zamery/cp/update/updateCPZamer.php",
        '/api/investicne-zamery/konverzie/create' => $apirDir . "global/investicne-zamery/konverzie/createKonvZamer.php",
        '/api/investicne-zamery/konverzie/update' => $apirDir . "global/investicne-zamery/konverzie/updateKonvZamer.php",

        '/api/aktiva/get/cpList' => $viewDir . "default/cpList.php",
        '/api/get/notificationUsers' => $apirDir . "general/get/notificationUsers.php",
        '/api/mentionUserToAction' => $apirDir . "general/get/createMention.php",
        '/api/get/clientModeClients' => $apirDir . "client/filteredData.php",
        '/api/setClientObject' => $apirDir . "client/setClientObject.php",
        '/api/unsetClient' => $apirDir . "client/unsetClient.php",

        '/api/dlhopisy/potvrdenie' => $apirDir . "global/dlhopisy/operation.php",
        '/api/dlhopisy/splatenie' => $apirDir . "global/dlhopisy/operation.php",
        '/api/dlhopisy/filteredData' => $apirDir . "global/dlhopisy/filteredData.php",
        '/api/terminovany-vklad/potvrdenie' => $apirDir . "global/terminovany-vklad/operation.php",
        '/api/get/newRowToUhrady' => $viewDir . "global/vysporiadanie/penazne/odchadzajuce/newRow.php",
        '/api/get/newFilledRowToUhrady' => $viewDir . "global/vysporiadanie/penazne/odchadzajuce/newFilledRow.php",
        '/api/vysporiadanie/pridatUhradyBulk' => $apirDir . "global/vysporiadanie/penazne/odchadzajuce/pridatUhradyBulk.php",
        '/api/vysporiadanie/readyPayments' => $apirDir . "global/vysporiadanie/majetkove/readyPaymentsTableMove.php",
        '/api/vysporiadanie/refreshOcakavane' => $apirDir . "global/vysporiadanie/penazne/odchadzajuce/refreshOcakavane.php",
        '/api/vysporiadanie/deleteUhradaFromList' => $apirDir . "global/vysporiadanie/majetkove/deleteUhradaFromList.php",
        '/api/vysporiadanie/filterCubs' => $apirDir . "global/vysporiadanie/penazne/filterCubs.php",
        '/api/vysporiadanie/majetkove/zauctovatPlatbu' => $apirDir . "global/vysporiadanie/majetkove/zauctovatPlatbu.php",
        '/api/poplatky/get/typy' => $apirDir . "global/poplatky/typyPoplatkov.php",
        '/api/poplatky/get/zmluvy' => $apirDir . "global/poplatky/zmluvy.php",
        '/api/poplatky/get/dateRange' => $apirDir . "global/poplatky/dateRange.php",
        '/api/poplatky/get/isin' => $apirDir . "global/poplatky/isin.php",
        '/api/poplatky/get/meny' => $apirDir . "global/poplatky/meny.php",
        '/api/poplatky/get/filteredData' => $apirDir . "global/poplatky/filterTable.php",
        '/api/poplatky/get/displayFilteredData' => $apirDir . "global/poplatky/displayFilter.php",
        '/api/poplatky/get/paginationData' => $apirDir . "global/poplatky/pagination.php",
        '/api/poplatky/change/global/sumTable' => $apirDir . "global/poplatky/actions/changeSumTable.php",
        '/api/poplatky/change/global/sum' => $apirDir . "global/poplatky/actions/changeSum.php",
        '/api/poplatky/change/global/currTable' => $apirDir . "global/poplatky/actions/changeCurrTable.php",
        '/api/poplatky/change/global/curr' => $apirDir . "global/poplatky/actions/changeCurr.php",
        '/api/poplatky/change/global/rekonfTable' => $apirDir . "global/poplatky/actions/rekonfTable.php",
        '/api/poplatky/change/global/rekonf' => $apirDir . "global/poplatky/actions/rekonf.php",
        '/api/poplatky/create' => $apirDir . "global/poplatky/actions/createFreeDB.php",
        '/api/poplatky/get/rekonfirmed' => $apirDir . "global/poplatky/rekonfirmed.php",
        '/api/poplatky/change/global/payment' => $apirDir . "global/poplatky/actions/splatenie.php",
        '/api/poplatky/change/global/terminationQuestion' => $apirDir . "global/poplatky/actions/terminationQuestion.php",
        '/api/poplatky/change/global/termination' => $apirDir . "global/poplatky/actions/termination.php",

        '/api/vysporiadanie/process-payment' => $apirDir . "global/vysporiadanie/processPayment.php",
        '/api/vysporiadanie/penazne/deleteUhradaFromList' => $apirDir . "global/vysporiadanie/penazne/deleteUhrada.php",
        '/api/vysporiadanie/penazne/sparovanie/zauctovatPlatbu' => $apirDir . "global/vysporiadanie/penazne/sparovanie/zauctovatPlatbu.php",
        '/api/vysporiadanie/majetkove/sparovanie/natipovatPlatbu' => $apirDir . "global/vysporiadanie/majetkove/majetkoveNatipovanie.php",

        '/api/get-transactions-another/?' => $apirDir . "global/indexView/transactions-data.php",
        '/api/get-druhy-aktiv' => $apirDir . "global/indexView/getDruhy.php",
        '/api/get/aktiva' => $apirDir . "global/indexView/getAktiva.php",
        '/api/index-view/get/filteredData' => $apirDir . "global/indexView/filterIndexViewTable.php",
        '/api/global/data' => $apirDir . "client/viewData.php",

        //KLIENTI
        '/api/klienti/get/portfolio/?' => $apirDir . "klienti/detail/getPortfolio.php",
        '/api/klienti/get/transactions/?' => $apirDir . "klienti/detail/getTransactions.php",
        '/api/klienti/get/documents/?' => $apirDir . "klienti/detail/getDocuments.php",
        '/api/klienti/get/risk-profile/?' => $apirDir . "klienti/detail/getRiskProfile.php",
        '/api/klienti/get/notes/?' => $apirDir . "klienti/detail/getNotes.php",
        '/api/klienti/get/acitivity/?' => $apirDir . "klienti/detail/getActivity.php",
        '/api/transakcie-klienta/get/datumy/?' => $apirDir . "klienti/detail/filters/transactionsDates.php",
        '/api/transakcie-klienta/get/portfolia/?' => $apirDir . "klienti/detail/filters/portfolios.php",
        '/api/transakcie-klienta/get/filteredData' => $apirDir . "klienti/detail/detailClientFilteredData.php",


        //UTILITIES
        "/refreshMenu" => $componentsDir . "layout/menus/menuLayout.php",
        "/refreshTopBar" => $componentsDir . "layout/header/topBar.php",
        "/force-logout" => $apirDir . "uzavierka/process/force-logout.php",
        "/unlock-login" => $apirDir . "uzavierka/process/unlock-login.php",

        //UZAVIERKA
        "/uzavierka/process/menove-pary" => $apirDir . "uzavierka/process/menove-pary.php",
        "/uzavierka/process/main-process" => $apirDir . "uzavierka/process/main-process.php",
        "/uzavierka/process/kurzy" => $apirDir . "uzavierka/process/kurzy.php",
        '/uzavierka/process/kurzyCPupload' => $apirDir . "uzavierka/process/uploadCPkurzy.php",
        '/uzavierka/process/kurzyAll' => $apirDir . "uzavierka/process/kurzyAll.php",
        '/uzavierka/process/potvrditKurzy' => $apirDir . "uzavierka/process/potvrditKurzy.php",
        '/uzavierka/process/ocenenie' => $apirDir . "uzavierka/process/ocenenie.php",
        '/uzavierka/process/hotovo' => $apirDir . "uzavierka/process/hotovo.php",
        '/uzavierka/storno' => $apirDir . "uzavierka/process/storno-uzavierky.php",
        '/uzavierka/process/report-pokles-nav' => $apirDir . "uzavierka/process/report-pokles-nav.php",
        '/uzavierka/process/denny-pokles' => $apirDir . "uzavierka/process/denna-zmena-aktiv.php",
        '/uzavierka/process/narodeniny' => $apirDir . "uzavierka/process/narodeniny.php",

        //AKCIE
        '/akcie/dividendy/natipovanie' => $viewDir . "akcie/dividendy.php",
        '/akcie/dividendy/splatenie' => $viewDir . "akcie/dividendy_splatenie.php",
        '/api/akcie/dividendy/get' => $apirDir . "akcie/dividendy/get.php",
        '/api/akcie/dividendy/natipovat' => $apirDir . "akcie/dividendy/naitpovanieDividendy.php",
        '/api/akcie/dividendy/splatit' => $apirDir . "akcie/dividendy/splatenieDividendy.php",
        '/api/akcie/dividendy/getCharakter/?' => $apirDir . "akcie/dividendy/getCharakter.php",
        '/api/readPDF' => $apirDir . "akcie/dividendy/getDataFromPDF.php",
        '/api/aktiva/editInfo/([^/]+)/?' => $apirDir . "aktiva/cpInfoEdit.php",
        '/api/aktiva/updateInfo' => $apirDir . "aktiva/cpInfoUpdate.php",
        '/api/klienti/detail/addNote' => $apirDir . "klienti/detail/actions/addNote.php",
        '/api/klienti/detail/deleteNote' => $apirDir . "klienti/detail/actions/deleteNote.php",
        '/api/klienti/detail/filterActivity' => $apirDir . "klienti/detail/actions/filterActivity.php",

        '/api/exportToExcel' => $apirDir . "global/exportToExcel.php",
        '/api/obchodnny-dennik/generatePDF' => $apirDir . "dennik/createPDF.php",
    ];
} else {
    $routes = [
        '/' => $viewDir . 'Login/loginForm.php',
        '/zabudnute-heslo' => $viewDir . 'Login/forgot-password.php',
        '/reset-hesla' => $viewDir . 'Login/reset-password.php',
    ];
}

$match = false;


foreach ($routes as $pattern => $file) {
    if (preg_match("#^$pattern/?$#", $route, $matches)) {
        $match = true;

        if (!str_contains($pattern, "/api") && !str_contains($pattern, "pridat/terminovany-vklad")) {
            ob_start();
            $path = $_SERVER['REQUEST_URI'];
            require "/home/<USER>/www/src/Components/layout/header/breadcrumb.php";
            $breadcrumb = ob_get_clean();
        }
        if (file_exists(__DIR__ . $file)) {
            ob_start();
            require __DIR__ . $file;
            $content = ob_get_clean();
            if (!str_contains($pattern, "/api") && isset($_SESSION["user"])) {
                ob_start();
                $path = $_SERVER['REQUEST_URI'];
                require "/home/<USER>/www/src/Components/layout/menus/menuLayout.php";
                $menu = ob_get_clean();
                ob_start();
                require "/home/<USER>/www/src/Components/layout/header/notificationsTrigger.php";
                $notifBtn = ob_get_clean();
            }
        } else {
            http_response_code(404);
            if (!$isLoggedIn) {
                header("Location: /");
            }
        }

        break;
    }
}

// If no route matched, return 404
if (!$match) {
    http_response_code(404);
    if (!$isLoggedIn) {
        header("Location: /");
    }
}


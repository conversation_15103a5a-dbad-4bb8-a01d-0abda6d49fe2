{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "17a217b2ad4b45c2c4a5bb43470d32ab", "packages": [{"name": "shuchkin/simplexlsxgen", "version": "1.4.12", "source": {"type": "git", "url": "https://github.com/shuchkin/simplexlsxgen.git", "reference": "4dffbd72ffaaa57022a50d770f30fa1efe3500a0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/shuchkin/simplexlsxgen/zipball/4dffbd72ffaaa57022a50d770f30fa1efe3500a0", "reference": "4dffbd72ffaaa57022a50d770f30fa1efe3500a0", "shasum": ""}, "require": {"ext-mbstring": "*", "ext-zlib": "*", "php": ">=5.4"}, "type": "library", "autoload": {"classmap": ["src/SimpleXLSXGen.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON> (SMSPILOT)", "email": "<EMAIL>", "homepage": "https://shuchkin.ru/"}], "description": "Export data to Excel XLSx file. PHP XLSX generator.", "homepage": "https://github.com/shuchkin/simplexlsxgen", "keywords": ["backend", "creator", "excel", "generator", "php", "writer", "xlsx"], "support": {"issues": "https://github.com/shuchkin/simplexlsxgen/issues", "source": "https://github.com/shuchkin/simplexlsxgen/tree/1.4.12"}, "time": "2024-07-28T10:07:02+00:00"}, {"name": "smalot/pdfparser", "version": "v2.12.0", "source": {"type": "git", "url": "https://github.com/smalot/pdfparser.git", "reference": "8440edbf58c8596074e78ada38dcb0bd041a5948"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/smalot/pdfparser/zipball/8440edbf58c8596074e78ada38dcb0bd041a5948", "reference": "8440edbf58c8596074e78ada38dcb0bd041a5948", "shasum": ""}, "require": {"ext-iconv": "*", "ext-zlib": "*", "php": ">=7.1", "symfony/polyfill-mbstring": "^1.18"}, "type": "library", "autoload": {"psr-0": {"Smalot\\PdfParser\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Pdf parser library. Can read and extract information from pdf file.", "homepage": "https://www.pdfparser.org", "keywords": ["extract", "parse", "parser", "pdf", "text"], "support": {"issues": "https://github.com/smalot/pdfparser/issues", "source": "https://github.com/smalot/pdfparser/tree/v2.12.0"}, "time": "2025-03-31T13:16:09+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "6d857f4d76bd4b343eac26d6b539585d2bc56493"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/6d857f4d76bd4b343eac26d6b539585d2bc56493", "reference": "6d857f4d76bd4b343eac26d6b539585d2bc56493", "shasum": ""}, "require": {"ext-iconv": "*", "php": ">=7.2"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-12-23T08:48:59+00:00"}], "packages-dev": [], "aliases": [], "minimum-stability": "stable", "stability-flags": [], "prefer-stable": false, "prefer-lowest": false, "platform": {"ext-iconv": "*"}, "platform-dev": [], "plugin-api-version": "2.3.0"}
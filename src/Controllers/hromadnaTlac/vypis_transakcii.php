<?php
$vypisyspolu = 0;

$vypis_obsah = '<div class="block p-6 bg-white border border-gray-200 rounded-lg shadow hover:bg-gray-100 dark:bg-gray-800 dark:border-gray-700 dark:hover:bg-gray-700"><h5 class="mb-2 text-2xl font-bold tracking-tight text-gray-900 dark:text-white">Prehľad transakcií</h5><p class="font-normal mb-2 text-gray-700 dark:text-gray-400"><span><b>od ' . $fromdate . ' do ' . $todate . '</b></span></p><div class="flex justify-between"><span>Klient: <strong>' . $klient->fondid . '</strong></span><span>Klient: <strong>' . $klient->cislozmluvy . '</strong></span></div></div>';

$getMaxDateForKlient = "SELECT max(datum) FROM majetoktotal WHERE subjektid = 1752";
$clientToDate = Connection::getDataFromDatabase($getMaxDateForKlient, defaultDB)[1][0]['max'];

$fondid_add = "subjektid in ($fondid) ";

$query = "select to_char(datum, 'YYYY-MM-DD') as mydatum,
       t.*
from (	select 	-1 as poradie,'Počiatočný stav účtu' as popis, menadenom as mena1,
				coalesce( sum(((0.5-md_d)/0.5) * pocet),0) as transsuma, 
					'$fromdate'::date as datum,
					'' as poznamka,
					0 as md_d
			from	majetoktotal mt
			where	$fondid_add
					and datum = '$fromdate'::date
					and eqid = 'BU'
					and uctovnykod = 221110					
			group by menadenom, datum,subjektid
	
      union all
      SELECT 0                                                                       AS poradie,
             'Automatická konverzia SKK na EUR - konverzný kurz 1 EUR = 30,1260 SKK' AS popis,
             mena                                                                    AS mena1,
             SUM(CASE WHEN md_d = 0 THEN 1 ELSE -1 END * pocet)                      AS transsuma,
             MAX(e.conversion_date)                                                  AS datum,
             ''                                                                      AS poznamka,
             0                                                                       AS md_d
      FROM majetokarchiv mt
               JOIN
           eurconversionsettings e
           ON DATE_TRUNC('day', mt.obratdatatimezauctovane) = e.conversion_date
      WHERE $fondid_add
        AND eqid = 'BU'
        AND uctovnykod = 221110
        AND destinacia = 'nauctovanieEUR'
        AND e.conversion_date = '$fromdate'::date
      GROUP BY mena, subjektid
      union all
      SELECT 100000              AS poradie,
             'Konečný stav účtu' AS popis,
             menadenom           AS mena1,
             (
                 CASE
                     WHEN EXISTS (SELECT 1
                                  FROM majetoktotal
                                  WHERE subjektid = mt.subjektid
                                    AND eqid = 'BU'
                                    AND uctovnykod = 221110
                                    AND menadenom = mt.menadenom
                                    AND DATE_TRUNC('day', datum) = DATE_TRUNC('day', mt.datum)) THEN
                         SUM(CASE WHEN md_d = 0 THEN 1 ELSE -1 END * pocet)
                     ELSE 0
                     END
                 )               AS transsuma,
             '$clientToDate'::date AS datum,
             ''                  AS poznamka,
             0                   AS md_d
      FROM majetoktotal mt
      WHERE $fondid_add
        AND DATE_TRUNC('day'
                , mt.datum) = (SELECT DATE_TRUNC('day'
                                          , MAX(datum))
                               FROM majetoktotal
                               WHERE DATE_TRUNC('day'
                                         , datum) = '$clientToDate'
                                         ::date)
        AND eqid = 'BU'
        AND uctovnykod = 221110
      GROUP BY menadenom, subjektid, DATE_TRUNC('day', mt.datum), mt.datum
      union all
      SELECT 99999                                                                   AS poradie,
             'Automatická konverzia SKK na EUR - konverzný kurz 1 EUR = 30,1260 SKK' AS popis,
             mena                                                                    AS mena1,
             SUM(CASE WHEN md_d = 0 THEN 1 ELSE -1 END * pocet)                      AS transsuma,
             MAX(e.conversion_date)                                                  AS datum,
             ''                                                                      AS poznamka,
             0                                                                       AS md_d
      FROM majetokarchiv mt
               JOIN
           eurconversionsettings e
           ON DATE_TRUNC('day', mt.obratdatatimezauctovane) = e.conversion_date
      WHERE $fondid_add
        AND eqid = 'BU'
        AND uctovnykod = 221110
        AND destinacia = 'oductovanieSKK'
        AND e.conversion_date = '$fromdate'::date
      GROUP BY mena,
               subjektid
      union all
      select 120              as poradie,
             'Vklad klienta'  as popis,
             ob.mena          as mena1,
             ob.suma          as transsuma,
             ob.obratdatetime as datum,
             ''               as poznamka,
             ma.md_d
      from obratybu ob,
           obratybuobratid obo,
           majetokarchiv ma
      where ob.id = obo.id
        and ma.obratid = obo.obratid
        and ma.$fondid_add
        and ma.kodobratu = 201
        and ma.uctovnykod = 325300
        and ob.logactivityid = 15
        and ob.obratdatetime >= to_date('$fromdate'
          , 'YYYY-MM-DD')
        and ob.obratdatetime <= to_date('$clientToDate'
          , 'YYYY-MM-DD')
        and ob.krdb = 1
      union all
      select 420             as poradie,
             'Výber klienta' as popis,
             u.mena          as mena1,
             coalesce(sum(((0.5 - 1) / 0.5) * u.suma), 0) as transsuma,
             u.datesplatnost as datum,
             ''              as poznamka,
             ma.md_d
      from uhrada u,
           uhradaobratid ui,
           majetokarchiv ma
      where u.kodobratu = 303
        and u.id = ui.id
        and ma.obratid = ui.obratid
        and ma.$fondid_add
        and ma.kodobratu = 303
        and ma.uctovnykod = 261930
        and logactivityid = 15
        and datesplatnost >= to_date('$fromdate'
          , 'YYYY-MM-DD')
        and datesplatnost <= to_date('$clientToDate'
          , 'YYYY-MM-DD')
      group by ma.md_d, u.datesplatnost, u.mena
      union all
      SELECT 220                        as poradie,
             'Nákup akcie'              as popis,
             u.mena                     as mena1,
             COALESCE(
                     (SELECT pd.transsumareal
                      FROM pool p,
                           pooldetailreal pd
                      WHERE p.dealid = r.dealid
                        AND pd.poolid = p.poolid
                        AND pd.subjektid = ma.subjektid), r.transsuma
             )                          as transsuma,
             ma.obratdatatimezauctovane as datum,
             (SELECT d.cpnaz || '; ISIN ' || d.isinreal
              FROM konfirmaciacp k,
                   dbequity d
              WHERE k.dealid = r.dealid
                AND d.isin = k.isin)    as poznamka,
             ma.md_d
      FROM uhrada u
               JOIN rekonfirmaciacp r
                    ON u.dealid = r.dealid
                        AND u.tranza = r.tranza
               JOIN uhradaobratid ui ON u.id = ui.id
               JOIN majetokarchiv ma ON ma.obratid = ui.obratid
      WHERE u.kodobratu = 331
        AND ma.kodobratu = 331
        AND ma.uctovnykod = 261911
        AND ma.$fondid_add
        AND u.logactivityid = 15
        AND ma.obratdatatimezauctovane >= '$fromdate' ::date
        AND ma.obratdatatimezauctovane <= '$clientToDate' ::date
      UNION ALL
      SELECT 70                          AS poradie,
             'Platba za predaj akcie'    AS popis,
             ma.jednotka                 AS mena1,
             COALESCE(
                     (SELECT pd.transsumareal
                      FROM pool p
                               JOIN pooldetailreal pd ON p.poolid = pd.poolid
                      WHERE p.dealid = r.dealid
                        AND pd.subjektid = ma.subjektid), r.transsuma
             )                           AS transsuma,
             ma.obratdatatimezauctovane  AS datum,
             --r.datvysporiadaniabureal    AS datum_vysporiadania,
             (SELECT d.cpnaz || '; ISIN ' || d.isinreal
              FROM konfirmaciacp k
                       JOIN dbequity d ON k.isin = d.isin
              WHERE k.dealid = r.dealid) AS poznamka,
             0
      FROM rekonfirmaciacpobratid ro
               JOIN
           rekonfirmaciacp r
           ON r.dealid = ro.dealid AND r.tranza = ro.tranza
               JOIN
           konfirmaciacp kcp ON r.dealid = kcp.dealid
               JOIN
           majetokarchiv ma ON ro.obratid = ma.obratid
      WHERE ma.$fondid_add
        AND ma.kodobratu = 231
        AND ma.uctovnykod = 261710
        AND r.datvysporiadaniabureal >= '$fromdate'::date
        AND r.datvysporiadaniabureal <= '$clientToDate'::date
      UNION ALL
      SELECT 210                         AS poradie,
             'Nákup dlhopisu'            AS popis,
             u.mena                      AS mena1,
             COALESCE(
                     (SELECT transsumareal
                      FROM pooldetailreal pd
                               JOIN pool p ON pd.poolid = p.poolid
                      WHERE p.dealid = r.dealid
                        AND pd.subjektid = ma.subjektid), r.transsuma
             )                           AS transsuma,
             ma.obratdatatimezauctovane  AS datum,
             (SELECT d.cpnaz || '; ISIN ' || d.isinreal
              FROM konfirmaciacp k
                       JOIN dbequity d ON k.isin = d.isin
              WHERE k.dealid = r.dealid) AS poznamka,
             ma.md_d
      FROM uhrada u
               JOIN rekonfirmaciacp r
                    ON u.dealid = r.dealid
                        AND u.tranza = r.tranza
               JOIN uhradaobratid ui ON u.id = ui.id
               JOIN majetokarchiv ma ON ma.obratid = ui.obratid
               LEFT JOIN konfirmaciacp kcp ON r.dealid = kcp.dealid
      WHERE u.kodobratu = 302
        AND ma.$fondid_add
        AND ma.kodobratu = 302
        AND ma.uctovnykod = 261920
        AND u.logactivityid = 15
        AND ma.obratdatatimezauctovane >= '$fromdate' ::date
        AND ma.obratdatatimezauctovane <= '$clientToDate' ::date
      union all
      SELECT 60                          AS poradie,
             CASE
                 WHEN r.dealid IN (46663904, 47124904) THEN 'Predčasné splatenie dlhopisu na výzvu emitenta'
                 ELSE 'Platba za predaj dlhopisu'
                 END                     AS popis,
             ma.jednotka                 AS mena1,
             COALESCE(
                     (SELECT pd.transsumareal
                      FROM pool p
                               JOIN pooldetailreal pd ON p.poolid = pd.poolid
                      WHERE p.dealid = r.dealid
                        AND pd.subjektid = ma.subjektid), r.transsuma
             )                           AS transsuma,
             ma.obratdatatimezauctovane  AS datum,
             --r.datvysporiadaniabureal    AS datum_vysporiadania,
             (SELECT d.cpnaz || '; ISIN ' || d.isinreal
              FROM konfirmaciacp k
                       JOIN dbequity d ON k.isin = d.isin
              WHERE k.dealid = r.dealid) AS poznamka,
             ma.md_d
      FROM rekonfirmaciacpobratid ro
               JOIN
           rekonfirmaciacp r ON r.dealid = ro.dealid AND r.tranza = ro.tranza
               JOIN
           konfirmaciacp kcp ON r.dealid = kcp.dealid
               JOIN
           majetokarchiv ma ON ro.obratid = ma.obratid
      WHERE ma.$fondid_add
        AND ma.kodobratu = 134
        AND ma.uctovnykod = 261210
        AND r.datvysporiadaniabureal >= '$fromdate'::date
        AND r.datvysporiadaniabureal <= '$clientToDate'::date
      UNION ALL
      SELECT 230                                                                   AS poradie,
             'Nákup fondu'                                                         AS popis,
             u.mena                                                                AS mena1,
             COALESCE(
                     (SELECT pd.transsumareal
                      FROM pool p
                               JOIN pooldetailreal pd ON p.poolid = pd.poolid
                      WHERE p.dealid = r.dealid
                        AND pd.subjektid = ma.subjektid), r.transsuma
             )                                                                     AS transsuma,
             r.dattransakcie                                                       AS datum,
             (SELECT d.cpnaz || '; ISIN ' || d.isinreal
              FROM konfirmaciacp k
                       JOIN dbequity d ON k.dealid = r.dealid AND d.isin = k.isin) AS poznamka,
             ma.md_d
      FROM uhrada u
               JOIN
           rekonfirmaciacp r ON u.dealid = r.dealid AND u.tranza = r.tranza
               JOIN
           uhradaobratid ui ON u.id = ui.id
               JOIN
           majetokarchiv ma ON ui.obratid = ma.obratid
      WHERE u.kodobratu = 332
        AND ma.$fondid_add
        AND ma.kodobratu = 332
        AND ma.uctovnykod = 261912
        AND u.logactivityid = 15
        AND r.dattransakcie >= '$fromdate'::date
        AND r.dattransakcie <= '$clientToDate'::date
      union all
      SELECT 80                                                                    AS poradie,
             'Platba za predaj fondu'                                              AS popis,
             ma.jednotka                                                           AS mena1,
             COALESCE(
                     (SELECT pd.transsumareal
                      FROM pool p
                               JOIN pooldetailreal pd ON p.poolid = pd.poolid
                      WHERE p.dealid = r.dealid
                        AND pd.subjektid = ma.subjektid), r.transsuma
             )                                                                     AS transsuma,
             ma.obratdatatimezauctovane                                            AS datum,
             (SELECT d.cpnaz || '; ISIN ' || d.isinreal
              FROM konfirmaciacp k
                       JOIN dbequity d ON k.dealid = r.dealid AND d.isin = k.isin) AS poznamka,
             ma.md_d
      FROM rekonfirmaciacpobratid ro
               JOIN
           rekonfirmaciacp r ON r.dealid = ro.dealid AND r.tranza = ro.tranza
               JOIN
           majetokarchiv ma ON ro.obratid = ma.obratid
      WHERE ma.$fondid_add
        AND ma.kodobratu = 233
        AND ma.uctovnykod = 325300 --261810
        AND ma.obratdatatimezauctovane >= '$fromdate'::date
        AND ma.obratdatatimezauctovane <= '$clientToDate'::date

      union all
      SELECT 430                              AS poradie,
             'Zriadenie termínovaného vkladu' AS popis,
             u.mena                           AS mena1,
             COALESCE(
                     (SELECT coalesce(transsumareal_pov, transsumareal)
                      FROM pooldetailreal pd
                               JOIN pool p ON pd.poolid = p.poolid
                      WHERE p.dealid = r.dealid
                        AND pd.subjektid = ma.subjektid), r.sum_td
             )                                AS transsuma,
             ma.obratdatatimezauctovane       AS datum,
             ''                               AS poznamka,
             ma.md_d
      FROM uhrada u
               JOIN konfirmaciaktv r
                    ON u.dealid = r.dealid
               JOIN uhradaobratid ui ON u.id = ui.id
               JOIN majetokarchiv ma ON ma.obratid = ui.obratid
      WHERE u.kodobratu = 301
        AND ma.$fondid_add
        AND ma.kodobratu = 301
        AND ma.uctovnykod = 2619
        AND u.logactivityid = 15
        AND ma.obratdatatimezauctovane >= '$fromdate' ::date
        AND ma.obratdatatimezauctovane <= '$clientToDate' ::date
        AND u.dealid = r.dealid
      union all
      select 10                               as poradie,
             'Splatenie termínovaného vkladu' as popis,
             ob.mena                          as mena1,
             ma.pocet                         as transsuma,
             ma.obratdatatimezauctovane       as datum,
             ''                               as poznamka,
             ma.md_d
      from obratybu ob,
           obratybuobratid obo,
           majetokarchiv ma
      where ob.id = obo.id
        and ma.obratid = obo.obratid
        and ma.$fondid_add
        and ma.kodobratu = 203
        and ma.uctovnykod = 325300
        and ob.logactivityid = 15
        and ob.obratdatetime >= to_date('$fromdate'
          , 'YYYY-MM-DD')
        and ob.obratdatetime <= to_date('$clientToDate'
          , 'YYYY-MM-DD')
        and ob.krdb = 1
      union all
      SELECT 20                                       AS poradie,
             'Splatenie úroku z termínovaného vkladu' AS popis,
             ob.mena                                  AS mena1,
             ma.pocet                                 AS transsuma,
             ma.obratdatatimezauctovane               AS datum,
             'Úrok brutto = ' || replace(
                     replace(
                             to_char(k.iv_b, '9,999,999,990.90'), ',', ' '
                     ), '.', ','
                                 ) || '; Daň = ' || replace(
                     replace(
                             to_char(k.suma_dane, '9,999,999,990.90'), ',', ' '
                     ), '.', ','
                                                    ) AS poznamka,
             ma.md_d
      FROM obratybu ob
               JOIN
           obratybuobratid obo ON ob.id = obo.id
               JOIN
           majetokarchiv ma ON obo.obratid = ma.obratid
               JOIN
           konfirmaciaktv k ON k.dealid::text = ob.vs AND k.$fondid_add
      WHERE ma.$fondid_add
        AND ma.kodobratu = 204
        AND ma.uctovnykod = 325300
        AND ob.logactivityid = 15
        AND ma.obratdatatimezauctovane >= '$fromdate'::date
        AND ma.obratdatatimezauctovane <= '$clientToDate'::date
        AND ob.krdb = 1
      union all
      select 20                                       as poradie,
             'Splatenie úroku z termínovaného vkladu' as popis,
             ob.mena                                  as mena1,
             ma.pocet                                 as transsuma,
             ma.obratdatatimezauctovane               as datum,
             'rok brutto = ' || replace(
                     replace(
                             to_char(pdr.auvreal, '9,999,999,990.90'), ',', ' '
                     ), '.', ','
                                 ) || '; Daň = ' || replace(
                     replace(
                             to_char(pdr.dan, '9,999,999,990.90'), ',', ' '
                     ), '.', ','
                                                    ) as poznamka,
             ma.md_d
      from obratybu ob,
           obratybuobratid obo,
           majetokarchiv ma,
           konfirmaciaktv k,
           pool po,
           pooldetailreal pdr
      where ob.id = obo.id
        and ma.obratid = obo.obratid
        and ma.$fondid_add
        and ma.kodobratu = 204
        and ma.uctovnykod = 325300
        and ob.logactivityid = 15
        and ma.obratdatatimezauctovane >= to_date('$fromdate'
          , 'YYYY-MM-DD')
        and ma.obratdatatimezauctovane <= to_date('$clientToDate'
          , 'YYYY-MM-DD')
        and ob.krdb = 1
        and k.subjektid = 0
        and pdr.poolid = po.poolid
        and pdr.subjektid = ma.SUBJEKTID
        and k.dealid = po.dealid
        and k.dealid::text = ob.vs
      union all
      select 40                         as poradie,
             'Splatenie kupónu'         as popis,
             ma.mena                    as mena1,
             s.suma                     as transsuma,
             ma.OBRATDATATIMEZAUCTOVANE as datum,
             (select e.cpnaz || '; ISIN ' || e.isinreal
              from dbequity e,
                   dbequitycurr c,
                   dbequitycurrric r
              where r.isincurrric = ma.kodaktiva
                and r.isincurr = c.isincurr
                and c.isin = e.isin)    as poznamka,
             0                          as md_d
      from splatenie s,
           splatenieobratid so,
           majetokarchiv ma
      where s.DEALID = so.DEALID
        and so.OBRATID = ma.obratid
        and so.tranza = s.tranza
        and s.suma <> 0
        and ma.$fondid_add
        and ma.kodobratu = 137
        and ma.uctovnykod = 315124
        and ma.destinacia = 'dbequity'
        and ma.obratdatatimezauctovane >= to_date('$fromdate'
          , 'YYYY-MM-DD')
        and ma.obratdatatimezauctovane <= to_date('$clientToDate'
          , 'YYYY-MM-DD')
      union all
      select 35                                 as poradie,
             case
                 when d.isin in ('SK4000016598') then 'Splatenie investičného certifikátu'
                 else 'Splatenie dlhopisu' end  as popis,
             s.mena                             as mena1,
             s.suma                             as transsuma,
             s.datumvyplaty                     as datum,
             d.cpnaz || '; ISIN ' || d.isinreal as poznamka,
             0                                  as md_d
      from splatenie s,
           dbequity d,
           dbequitycurr dc,
           dbequitycurrric dcr,
           portfolio p
      where s.uctovnykod = 251110
        and s.datumvyplaty >= to_date('$fromdate'
          , 'YYYY-MM-DD')
        and s.datumvyplaty <= to_date('$clientToDate'
          , 'YYYY-MM-DD')
        and s.$fondid_add
        and s.datum_naroku = (select max(datesplatnost)
                              from floatkupon
                              where s.kodaktiva = isincurrric
                                and istina >= 100)
        and dcr.isincurrric = s.kodaktiva
        and dcr.isincurr = dc.isincurr
        and dc.isin = d.isin
        and p.fondid = s.subjektid
      union all
      select 35                                     as poradie,
             'Čiastočné splatenie istiny - konečné' as popis,
             s.mena                                 as mena1,
             s.suma                                 as transsuma,
             s.datumvyplaty                         as datum,
             d.cpnaz || '; ISIN ' || d.isinreal     as poznamka,
             0                                      as md_d
      from splatenie s,
           dbequity d,
           dbequitycurr dc,
           dbequitycurrric dcr,
           portfolio p
      where s.uctovnykod = 251110
        and s.datumvyplaty >= to_date('$fromdate'
          , 'YYYY-MM-DD')
        and s.datumvyplaty <= to_date('$clientToDate'
          , 'YYYY-MM-DD')
        and s.$fondid_add
        and s.datum_naroku = (select max(datesplatnost)
                              from floatkupon
                              where s.kodaktiva = isincurrric
                                and istina
                                  < 100)
        and dcr.isincurrric = s.kodaktiva
        and dcr.isincurr = dc.isincurr
        and dc.isin = d.isin
        and p.fondid = s.subjektid
      union all
      select 35                                       as poradie,
             'Čiastočné splatenie istiny - priebežné' as popis,
             s.mena                                   as mena1,
             s.suma                                   as transsuma,
             s.datumvyplaty                           as datum,
             d.cpnaz || '; ISIN ' || d.isinreal       as poznamka,
             0                                        as md_d
      from splatenie s,
           dbequity d,
           dbequitycurr dc,
           dbequitycurrric dcr,
           portfolio p
      where s.uctovnykod = 251110
        and s.datumvyplaty >= to_date('$fromdate'
          , 'YYYY-MM-DD')
        and s.datumvyplaty <= to_date('$clientToDate'
          , 'YYYY-MM-DD')
        and s.$fondid_add
        and s.datum_naroku
          < (select max(datesplatnost)
             from floatkupon
             where s.kodaktiva = isincurrric
               and istina
                 < 100)
        and dcr.isincurrric = s.kodaktiva
        and dcr.isincurr = dc.isincurr
        and dc.isin = d.isin
        and p.fondid = s.subjektid
      union all
      select 50                                   as poradie,
             d.nazov                              as popis,
             sa.mena                              as mena1,
             sa.suma                              as transsuma,
             sa.datumvyplaty                      as datum,
             de.cpnaz || '; ISIN ' || de.isinreal as poznamka,
             0
      from splatenieakcia sa,
           dbequity de,
           dbequitycurr dr,
           dbequitycurrric drr,
           dividendaakciatyp d
      where sa.$fondid_add
        and sa.datumvyplaty >= to_date('$fromdate'
          , 'YYYY-MM-DD')
        and sa.datumvyplaty <= to_date('$clientToDate'
          , 'YYYY-MM-DD')
        and d.subkodobratu = sa.subkodobratu
        and sa.subkodobratu in (select subkodobratu
                                from dividendaakciatyp
                                where hotovost = 1)
        and sa.kodaktiva = drr.isincurrric
        and dr.isincurr = drr.isincurr
        and de.isin = dr.isin
      union all
      select 400              as poradie,
             'Ostatné platby' as popis,
             ob.mena          as mena1,
             ob.suma          as transsuma,
             ob.obratdatetime as datum,
             ob.nazpartnera   as poznamka,
             ma.md_d
      from obratybu ob,
           obratybuobratid obo,
           majetokarchiv ma
      where ob.id = obo.id
        and ma.obratid = obo.obratid
        and ma.$fondid_add
        and ma.uctovnykod in (221110, 325300)
        and ma.kodobratu not in (
                                 201, 203, 204, 205, 206, 207, 208, 209, 210, 213, 215, 214, 274, 279, 231, 232, 233,
                                 234, 235, 236, 237, 238, 260, 261, 262, 263, 285, 401, 402, 403, 601, 602, 603, 613,
                                 614, 631, 632, 633, 634, 644, 645, 646, 647, 648, 650
          )
        and ma.kodobratu in (
                             219, 226, 228, 266, 287, 288, 610, 620, 621, 622, 623, 624, 625, 626
          )
        and ob.logactivityid = 15
        and ob.obratdatetime >= to_date('$fromdate'
          , 'YYYY-MM-DD')
        and ob.obratdatetime <= to_date('$clientToDate'
          , 'YYYY-MM-DD')
      union all
      select 320                                                                          as poradie,
             'Poplatok za transakciu s CP'                                                as popis,
             pr.mena                                                                      as mena1,
             pr.suma                                                                      as transsuma,
             u.datesplatnost                                                              as datum,
             'Dátum obchodu ' || to_char(pr.datum, 'DD.MM.YYYY') || '; ISIN ' || kcp.ISIN as poznamka,
             ma.md_d
      from uhrada u,
           uhradaobratid ui,
           majetokarchiv ma,
           poplatok_register_links prl,
           poplatok_register pr,
           konfirmaciacp kcp
      where u.kodobratu = 350
        and u.id = ui.id
        and ma.obratid = ui.obratid
        and ma.$fondid_add
        and ma.uctovnykod = 221110
        and u.logactivityid = 15
        and datesplatnost >= to_date('$fromdate'
          , 'YYYY-MM-DD')
        and datesplatnost <= to_date('$clientToDate'
          , 'YYYY-MM-DD')
        and prl.uhrada_id = u.id
        and pr.id = prl.poplatok_register_id
        and pr.typ = 'TRAN'
        and pr.fondid = ma.subjektid
        and pr.dealid = kcp.dealid
      union all
      select 330                                                                          as poradie,
             'Poplatok za vysporiadanie transakcie s CP'                                  as popis,
             pr.mena                                                                      as mena1,
             pr.suma                                                                      as transsuma,
             u.datesplatnost                                                              as datum,
             'Dátum obchodu ' || to_char(pr.datum, 'DD.MM.YYYY') || '; ISIN ' || kcp.ISIN as poznamka,
             ma.md_d
      from uhrada u,
           uhradaobratid ui,
           majetokarchiv ma,
           poplatok_register_links prl,
           poplatok_register pr,
           konfirmaciacp kcp
      where u.kodobratu = 350
        and u.id = ui.id
        and ma.obratid = ui.obratid
        and ma.$fondid_add
        and ma.uctovnykod = 221110
        and u.logactivityid = 15
        and datesplatnost >= to_date('$fromdate'
          , 'YYYY-MM-DD')
        and datesplatnost <= to_date('$clientToDate'
          , 'YYYY-MM-DD')
        and prl.uhrada_id = u.id
        and pr.id = prl.poplatok_register_id
        and pr.typ = 'VYROV'
        and pr.fondid = ma.subjektid
        and pr.dealid = kcp.dealid
      union all
      select 350                        as poradie,
             'Poplatok za konverziu'    as popis,
             ma.mena                    as mena1,
             ma.pocet                   as transsuma,
             ma.obratdatatimezauctovane as datum,
             ''                         as poznamka,
             ma.md_d
      from majetokarchiv ma
      where ma.$fondid_add
        and ma.kodobratu = 346
        and ma.uctovnykod = 261992
        and ma.obratdatatimezauctovane >= to_date('$fromdate'
          , 'YYYY-MM-DD')
        and ma.obratdatatimezauctovane <= to_date('$clientToDate'
          , 'YYYY-MM-DD')
      union all
      select 340                                   as poradie,
             'Poplatok za obchod na peňažnom trhu' as popis,
             mena                                  as mena1,
             ma.pocet                              as transsuma,
             ma.obratdatatimezauctovane            as datum,
             ''                                    as poznamka,
             ma.md_d
      from majetokarchiv ma
      where ma.$fondid_add
        and ma.kodobratu = 345
        and ma.uctovnykod = 261991
        and ma.obratdatatimezauctovane >= to_date('$fromdate'
          , 'YYYY-MM-DD')
        and ma.obratdatatimezauctovane <= to_date('$clientToDate'
          , 'YYYY-MM-DD')
      union all
      select 310                                                                           as poradie,
             'Poplatok za správu portfólia'                                                as popis,
             pr.mena                                                                       as mena1,
             pr.suma                                                                       as transsuma,
             u.datesplatnost                                                               as datum,
             to_char(pr.datumod, 'DD.MM.YYYY') || ' - ' || to_char(pr.datum, 'DD.MM.YYYY') as poznamka,
             md_d
      from uhrada u,
           uhradaobratid ui,
           majetokarchiv ma,
           poplatok_register_links prl,
           poplatok_register pr
      where u.kodobratu = 350
        and u.id = ui.id
        and ma.obratid = ui.obratid
        and ma.$fondid_add
        and ma.uctovnykod = 221110
        and logactivityid = 15
        and datesplatnost >= to_date('$fromdate'
          , 'YYYY-MM-DD')
        and datesplatnost <= to_date('$clientToDate'
          , 'YYYY-MM-DD')
        and prl.uhrada_id = u.id
        and pr.id = prl.poplatok_register_id
        and pr.typ = 'SPRAVA'
        and pr.fondid = ma.subjektid
      union all
      select 300                                                                           as poradie,
             'Poplatok za riadenie portfólia'                                              as popis,
             ma.mena                                                                       as mena1,
             coalesce(sum(((0.5 - ma.md_d) / 0.5) * pr.suma), 0)                           as transsuma,
             u.datesplatnost                                                               as datum,
             to_char(pr.datumod, 'DD.MM.YYYY') || ' - ' || to_char(pr.datum, 'DD.MM.YYYY') as poznamka,
             ma.md_d
      from uhrada u,
           uhradaobratid ui,
           majetokarchiv ma,
           poplatok_register_links prl,
           poplatok_register pr
      where u.kodobratu = 350
        and u.id = ui.id
        and ma.obratid = ui.obratid
        and ma.$fondid_add
        and ma.uctovnykod = 221110
        and logactivityid = 15
        and datesplatnost >= to_date('$fromdate'
          , 'YYYY-MM-DD')
        and datesplatnost <= to_date('$clientToDate'
          , 'YYYY-MM-DD')
        and prl.uhrada_id = u.id
        and pr.id = prl.poplatok_register_id
        and pr.typ = 'MANAZ'
        and pr.fondid = ma.subjektid
      group by ma.mena, u.datesplatnost, ma.md_d, pr.datumod, pr.datum
      union all
      select 360             as poradie,
             'Poplatok'      as popis,
             ma.mena         as mena1,
             pr.suma         as transsuma,
             u.datesplatnost as datum,
             pr.dovod        as poznamka,
             ma.md_d
      from uhrada u,
           uhradaobratid ui,
           majetokarchiv ma,
           poplatok_register_links prl,
           poplatok_register pr
      where u.kodobratu = 350
        and u.id = ui.id
        and ma.obratid = ui.obratid
        and ma.$fondid_add
        and ma.uctovnykod = 221110
        and logactivityid = 15
        and datesplatnost >= to_date('$fromdate'
          , 'YYYY-MM-DD')
        and datesplatnost <= to_date('$clientToDate'
          , 'YYYY-MM-DD')
        and prl.uhrada_id = u.id
        and pr.id = prl.poplatok_register_id
        and pr.typ = 'OSTATNE'
        and pr.fondid = ma.subjektid
      union all
      select 370                     as poradie,
             'Vstupný poplatok'      as popis,
             mena                    as mena1,
             pocet                   as transsuma,
             obratdatatimezauctovane as datum,
             ''                      as poznamka,
             md_d
      from majetokarchiv
      where $fondid_add
        and kodobratu = 310
        and uctovnykod = 261961
        and obratdatatimezauctovane >= to_date('$fromdate'
          , 'YYYY-MM-DD')
        and obratdatatimezauctovane <= to_date('$clientToDate'
          , 'YYYY-MM-DD')
      union all
      select 380                            as poradie,
             'Poplatok za mimoriadny výpis' as popis,
             mena                           as mena1,
             pocet                          as transsuma,
             obratdatatimezauctovane        as datum,
             ''                             as poznamka,
             md_d
      from majetokarchiv
      where $fondid_add
        and kodobratu = 344
        and uctovnykod = 261995
        and obratdatatimezauctovane >= to_date('$fromdate'
          , 'YYYY-MM-DD')
        and obratdatatimezauctovane <= to_date('$clientToDate'
          , 'YYYY-MM-DD')
      union all
      SELECT 200                  AS poradie,
             'Konverzia - Šhrada' AS popis,
             k.menadebet          AS mena1,
             COALESCE(
                     (SELECT pd.transsumareal
                      FROM pool p
                               JOIN pooldetailreal pd ON p.poolid = pd.poolid
                      WHERE p.dealid = k.dealid
                        AND pd.subjektid = ma.subjektid), u.suma
             )                    AS transsuma,
             u.datesplatnost      AS datum,
             (
                 CASE
                     WHEN menadebet || menakredit = menovypar THEN menadebet || '/' || menakredit
                     ELSE menakredit || '/' || menadebet
                     END
                 ) || ' kurz = ' || (
                 CASE
                     WHEN k.kurz < 1 THEN replace(('0' || k.kurz::text), '.', ',')
                     ELSE replace(k.kurz::text, '.', ',')
                     END
                 ) || ''          AS poznamka,
             ma.md_d
      FROM uhrada u
               JOIN
           uhradaobratid ui ON u.id = ui.id
               JOIN
           majetokarchiv ma ON ui.obratid = ma.obratid
               JOIN
           konverzia k ON u.dealid = k.dealid
      WHERE u.kodobratu = 334
        AND ma.$fondid_add
        AND ma.kodobratu = 334
        AND ma.uctovnykod = 261914
        AND u.logactivityid = 15
        AND u.datesplatnost >= '$fromdate'::date
        AND u.datesplatnost <= '$clientToDate'::date
      union all
      SELECT 90                         AS poradie,
             'Konverzia - došlá platba' AS popis,
             ob.mena                    AS mena1,
             ma.pocet                   AS transsuma,
             ma.obratdatatimezauctovane AS datum,
             foo.popis                  AS poznamka,
             ma.md_d
      FROM obratybu ob
               JOIN obratybuobratid obo
                    ON ob.id = obo.id
               JOIN majetokarchiv ma ON ma.obratid = obo.obratid
               LEFT JOIN (SELECT (
                                     CASE
                                         WHEN menadebet || menakredit = menovypar THEN menadebet || '/' || menakredit
                                         ELSE menakredit || '/' || menadebet END
                                     ) || ' kurz = ' || (
                                     CASE
                                         WHEN k.kurz < 1 THEN replace(
                                                 (
                                                     '0' || k.kurz::text
                                                     ),
                                                 '.',
                                                 ','
                                                              )
                                         ELSE replace(
                                                 k.kurz::text,
                                                 '.',
                                                 ','
                                              ) END
                                     ) AS popis,
                                 mak.obratdatatimezauctovane,
                                 mak.pocet,
                                 mak.mena,
                                 mak.subjektid
                          FROM konverzia k
                                   JOIN konverziaobratid ko ON k.dealid = ko.dealid
                                   JOIN majetokarchiv mak ON ko.obratid = mak.obratid
                          WHERE mak.kodobratu = 237
                            AND mak.uctovnykod IN (315160, 315161)) foo ON ma.pocet = foo.pocet
          AND ma.mena = foo.mena
          AND ma.obratdatatimezauctovane = foo.obratdatatimezauctovane
          AND ma.subjektid = foo.subjektid
      WHERE ma.$fondid_add
        AND ma.kodobratu = 237
        AND ma.uctovnykod = 325300
        AND ob.logactivityid = 15
        AND ob.obratdatetime >= '$fromdate' ::date
        AND ob.obratdatetime <= '$clientToDate' ::date
        AND ob.krdb = 1
      union all
      select 410                                                          as poradie,
             (
                 case when k1.druhobchodu = 'prevod' then 'Prevod' end
                 ) || ' na portfólio č.' || (select cislozmluvy
                                             from portfolio
                                             where fondid = k2.subjektid) as popis,
             k1.mena                                                      as mena1,
             k1.suma                                                      as transsuma,
             k1.datum_zauctovania                                         as datum,
             ''                                                           as poznamka,
             1
      from konfirmaciapp k1,
           konfirmaciapp k2
      where k1.dealid_related is not null
        and k1.$fondid_add
        and k1.logactivityid = 12
        and k1.druhobchodu in ('prevod')
        and k1.subjektid != 0
        and k2.dealid = k1.dealid_related
        and k1.datum_zauctovania >= to_date('$fromdate'
          , 'YYYY-MM-DD')
        and k1.datum_zauctovania <= to_date('$clientToDate'
          , 'YYYY-MM-DD')
      union all
      select 100                                                         as poradie,
             (
                 case when k1.druhobchodu = 'prevod' then 'Prevod' end
                 ) || ' z portfólia č.' || (select cislozmluvy
                                            from portfolio
                                            where fondid = k2.subjektid) as popis,
             k1.mena                                                     as mena1,
             k1.suma                                                     as transsuma,
             k1.datum_zauctovania                                        as datum,
             ''                                                          as poznamka,
             0
      from konfirmaciapp k1,
           konfirmaciapp k2
      where k1.dealid_related is null
        and k1.$fondid_add
        and k1.logactivityid = 12
        and k1.druhobchodu in ('prevod')
        and k1.subjektid != 0
        and k2.dealid_related = k1.dealid
        and k1.datum_zauctovania >= to_date('$fromdate'
          , 'YYYY-MM-DD')
        and k1.datum_zauctovania <= to_date('$clientToDate'
          , 'YYYY-MM-DD')
      union all
      select 101                             as poradie,
             (
                 case when k1.druhobchodu = 'presun' then 'Presun' end
                 ) || ' z ��tu: ' || k1.ucet as popis,
             k1.mena                         as mena1,
             k1.suma                         as transsuma,
             k1.datum_zauctovania            as datum,
             ''                              as poznamka,
             ma.md_d
      from konfirmaciapp k1,
           konfirmaciappobratid ko,
           majetokarchiv ma
      where k1.subjektid = 1
        and k1.dealid_related is not null
        and k1.$fondid_add
        and k1.dealid = ko.dealid
        and ko.obratid = ma.obratid
        and k1.ucet = ma.ucetaktiva
        and ma.uctovnykod = 221110
        and k1.logactivityid = 12
        and k1.druhobchodu in ('presun')
        and k1.datum_zauctovania >= to_date('$fromdate'
          , 'YYYY-MM-DD')
        and k1.datum_zauctovania <= to_date('$clientToDate'
          , 'YYYY-MM-DD')
      union all
      select 102                              as poradie,
             (
                 case when k1.druhobchodu = 'presun' then 'Presun' end
                 ) || ' na ��et: ' || k1.ucet as popis,
             k1.mena                          as mena1,
             k1.suma                          as transsuma,
             k1.datum_zauctovania             as datum,
             ''                               as poznamka,
             ma.md_d
      from konfirmaciapp k1,
           konfirmaciapp k2,
           konfirmaciappobratid ko,
           majetokarchiv ma
      where k1.subjektid = 1
        and k1.$fondid_add
        and k1.dealid_related is null
        and k1.dealid = k2.dealid_related
        and k2.dealid = ko.dealid
        and ko.obratid = ma.obratid
        and k1.ucet = ma.ucetaktiva
        and ma.uctovnykod = 221110
        and k1.logactivityid = 12
        and k1.druhobchodu in ('presun')
        and k1.datum_zauctovania >= to_date('$fromdate'
          , 'YYYY-MM-DD')
        and k1.datum_zauctovania <= to_date('$clientToDate'
          , 'YYYY-MM-DD')
      union all
      select 110                       as poradie,
             'Ostatné platby'          as popis,
             m.mena                    as mena1,
             m.pocet                   as transsuma,
             m.obratdatatimezauctovane as datum,
             ob.nazpartnera            as poznamka,
             0
      from majetokarchiv m,
           obratybu ob,
           obratybuobratid obo
      where m.obratdatatimezauctovane >= to_date('$fromdate'
          , 'YYYY-MM-DD')
        and m.obratdatatimezauctovane <= to_date('$clientToDate'
          , 'YYYY-MM-DD')
        and m.$fondid_add
        and m.uctovnykod = 668000
        and m.md_d = 1
        and m.KODOBRATU = 214
        and ob.id = obo.id
        and m.obratid = obo.obratid --neberiem vkladypp ani opravy vkladov
        and not exists (select 1
                        from konfirmaciapp k5
                           , obratybu o5
                           , obratybuobratid oo5
                        where k5.subjektid = m.subjektid
                          and k5.logactivityid = 12
                          and o5.subjektid = k5.subjektid
                          and o5.ss = k5.dealid::text
                          and o5.suma = k5.suma
                          and o5.mena = k5.mena
                          and o5.cub = k5.ucet
                          and oo5.id = o5.id
                          and oo5.obratid = m.obratid
                        union all
                        select 1
                        from majetokarchiv ma5
                        where ma5.OBRATDATATIMEZAUCTOVANE = m.OBRATDATATIMEZAUCTOVANE
                          and ma5.$fondid_add
                          and ma5.uctovnykod = 668000
                          and ma5.md_d = 0
                          and ma5.KODOBRATU = 214)
      union all
      select 420                 as poradie,
             'Výber klienta'     as popis,
             k.mena              as mena1,
             coalesce(sum(((0.5 - 1) / 0.5) * k.suma), 0) as transsuma,
             k.datum_zauctovania as datum,
             k.externy_ucet      as poznamka,
             1
      from konfirmaciapp k
      where k.druhobchodu = 'vyber'
        and k.$fondid_add
        and k.logactivityid = 12
        and k.datum_zauctovania >= to_date('$fromdate'
          , 'YYYY-MM-DD')
        and k.datum_zauctovania <= to_date('$clientToDate'
          , 'YYYY-MM-DD')
      group by k.mena, k.datum_zauctovania, k.externy_ucet
      union all
      SELECT
          120 AS poradie,
          'Vklad klienta' AS popis,
          k.mena AS mena1,
          k.suma AS transsuma,
          k.datum_zauctovania AS datum,
          (
              SELECT cubpartnera
              FROM obratybu o
              WHERE o.ss::text = k.dealid::text
          ) AS poznamka,
          0
      FROM
          konfirmaciapp k
      WHERE
          k.druhobchodu = 'vklad'
        AND k.$fondid_add
        AND k.logactivityid = 12
        AND k.datum_zauctovania >= '$fromdate'::date
        AND k.datum_zauctovania <= '$clientToDate'::date) t
order by mena1,
         mydatum,
         poradie
";

$data = Connection::getDataFromDatabase($query, defaultDB);
$dataLen = $data[0];
$data = $data[1][0];
$cnt = 0;
$mena_foo = "";
$sekcia_ukoncena = true;
$sumacheck = 0;
$sumaAlert = "";

for ($i = 0; $i < $dataLen; $i++) {
    $datum = ($data['datum'] !== '') ? $data['datum'] : '';
    $poznamka = ($data['poznamka'] !== '') ? " (" . $data['poznamka'] . ")" : '';
    $popis = $data['popis'];
    $suma = $data['transsuma'];
    $mena = $data['mena1'];
    $sumacheck = ($mena_foo != $mena) ? 0 : $sumacheck;
    $poradie = $data['poradie'];
    if (intval($poradie) == 100000) {
        $sekcia_ukoncena = true;
        if (round($sumacheck, 2) != round($suma, 2)) {
            $sumaAlert = $sumaAlertBottom = "Neplatná zostava !";
        }
    } else {
        $sumacheck += $data['md_d'] ? -$suma : $suma;
    }
    if ($mena_foo != $mena) {
        if ($mena_foo != "") {
            if (!$sekcia_ukoncena) {
                //pokial prebehli transakcie na ucte v jednom dni a v majetoktotal neexistuje zaznam, zostatok je nulovy
                //teda v poslednom dni prebehli transakcie a zostatok na ucte je nulovy, do majetoktotal sa nezapise ...
                $vypis_obsah = $vypis_obsah . '
					<tr>
						<td style="width:20%;text-align:left;font-size:8;"><b>' . $todate . '</b></td>
						<td style="width:50%;text-align:left;font-size:8;"><b>Konečný stav účtu</b></td>
						<td style="width:30%;text-align:right;font-size:8;"><b>' . bcdiv(0, 1, 2) . '</b></td>
					</tr>
				';
                if (isset($predoslasumacheck)) {
                    if (round($predoslasumacheck, 2) != round(0, 2)) {
                        $sumaAlert = $sumaAlertBottom = "Neplatná zostava !";
                    }
                }
            }
            $vypis_obsah = $vypis_obsah . '	
						<tr style="width:100%;line-height:1pt;">
							<td colspan="3" style="height:1pt;min-width:100%;width:100%;"></td>
						</tr>	
					</tbody>
				</table>
			';
            if ($sumaAlert != "") {
                $vypis_obsah = $vypis_obsah . '
					<table>
						<tr>
							<td style="font-size:4.5mm;">' . $sumaAlert . '</td>
						</tr>
					</table>
				';
                $sumaAlert = "";
            }
        }
        $sekcia_ukoncena = false;
        $vypisyspolu++;
        $vypis_obsah = $vypis_obsah . '<div class="inline-flex mt-2 items-center px-4 py-3 text-white bg-green-500 rounded-lg active w-full dark:bg-blue-600" aria-current="page">Výpis z bežného účtu (' . $mena . ')</div>
            <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
                <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                <tr>
                    <th scope="col" class="px-6 py-3">
                        Dátum
                    </th>
                    <th scope="col" class="px-6 py-3">
                        Transakcia
                    </th>
                    <th scope="col" class="px-6 py-3">
                        Čiastka
                    </th>
                </tr>
            </thead>
				<tbody>
			';
        if ($poradie != -1) {
            $vypis_obsah = $vypis_obsah . '
				<tr class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b dark:border-gray-700">
					<td class="px-6 py-4">' . $before_fromdate . '</td>
					<td class="px-6 py-4">Počiatočný stav účtu</td>
					<td class="px-6 py-4">' . bcdiv(0, 1, 2) . '</td>
				</tr>
				';
        }
    }
    $vypis_obsah = $vypis_obsah . '
			<tr class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b dark:border-gray-700">
				<td class="px-6 py-4">' . $datum . '</td>
				<td class="px-6 py-4">' . $popis . $poznamka . '</td>
				<td class="px-6 py-4">' . ($data['md_d'] == 0 ? '' : '-') . bcdiv($suma, 1, 2) . '</td>
			</tr>
		';
    $mena_foo = $mena;
    $predoslasuma = $suma;
    $predoslasumacheck = $sumacheck;
    $cnt = $i;
}

/**
 * pokial prebehli transakcie na ucte v jednom dni a v majetoktotal neexistuje zaznam, zostatok je nulovy
 * teda v poslednom dni prebehli transakcie a zostatok na ucte je nulovy, do majetoktotal sa nezapise ...
 */
if (!$sekcia_ukoncena) {
    $vypis_obsah = $vypis_obsah . '
				<tr class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 border-b">
					<td class="px-6 py-4">' . $todate . '</td>
					<td class="px-6 py-4">Konečný stav účtu</td>
					<td class="px-6 py-4">' . bcdiv(0, 1, 2) . '</td>
				</tr>
			</tbody>
		</table>';
    /*if (round($predoslasumacheck, 2) !== round(0, 2)) {
        $sumaAlert = $sumaAlertBottom = "Neplatná zostava !";
    }*/
}
if ($sekcia_ukoncena and $cnt > 0) {
    $vypis_obsah = $vypis_obsah . '				
				<tr style="width:100%;line-height:1pt;">
					<td colspan="3" style="height:1pt;min-width:100%;width:100%;"></td>
				</tr>		
			</tbody>
		</table>
	';
    /*if (round($sumacheck, 2) != round($suma, 2)) {
        $sumaAlert = "Neplatná zostava !";
        $sumaAlertBottom = "Neplatná zostava !";

    }*/
}
if ($sumaAlert != "") {
    $vypis_obsah = $vypis_obsah . '			
		<table>
			<tr>
				<td style="font-size:4.5mm;">' . $sumaAlert . '</td>
			</tr>
		</table>
	';
    $sumaAlert = "";
}


//------------------Terminovane vklady-----------------------------
$fondid_add = "subjektid in ($fondid) ";
$query = "SELECT *
FROM (SELECT CASE
                 WHEN z_td < to_date('$fromdate', 'YYYY-MM-DD') THEN 1
                 WHEN k_td > to_date('$todate', 'YYYY-MM-DD') THEN 3
                 ELSE 2 END                  AS trieda,
             k.z_td                          AS zaciatok,
             k.k_td                          AS koniec,
             k.sum_td                        AS suma,
             k.ir_td                         AS sadzba,
             k.iv_b                          AS brutto,
             k.iv_n                          AS netto_old,
             k.iv_n                          AS netto,
             k.mena,
             k.suma_dane                     AS dan,
             k.datvysporiadaniarealntd       AS vysporiadanie,
             k.cutd,
             k.subjektid,
             z_td,
             k_td,
             p.nazovpartnera                 AS miesto,
             k.datum_cas_obchodu             AS datum_cas,
             (SELECT typ_pokynu
              FROM typy_pokynov tp
              WHERE tp.typid = k.typ_pokynu) AS typPokynu,
             ''                              AS detailKTV
      FROM konfirmaciaktv k
               JOIN partner p ON k.partnerid = p.partnerid
      WHERE (
          (
              z_td >= to_date('2023-01-01', 'YYYY-MM-DD')
                  AND z_td <= to_date('2024-05-01', 'YYYY-MM-DD')
              )
              OR (
              k_td >= to_date('2023-01-01', 'YYYY-MM-DD')
                  AND k_td <= to_date('2024-05-01', 'YYYY-MM-DD')
              )
          )
        AND logactivityid IN (17, 25)
        AND k.$fondid_add
      UNION ALL
      SELECT CASE
                 WHEN z_td < to_date('2023-01-01', 'YYYY-MM-DD') THEN 1
                 WHEN k_td > to_date('2024-05-01', 'YYYY-MM-DD') THEN 3
                 ELSE 2 END                                              AS trieda,
             k.z_td                                                      AS zaciatok,
             k.k_td                                                      AS koniec,
             pdr.transsumareal                                           AS suma,
             k.ir_td                                                     AS sadzba,
             pdr.auvreal                                                 AS brutto,
             k.iv_n * pdr.transsumareal / (SELECT sum(x.transsumareal)
                                           FROM pooldetailreal x
                                           WHERE x.poolid = po.poolid)   AS netto_old,
             (pdr.auvreal - pdr.dan)                                     AS netto,
             k.mena,
             pdr.dan                                                     AS dan,
             k.datvysporiadaniarealntd                                   AS vysporiadanie,
             k.cutd,
             k.subjektid,
             z_td,
             k_td,
             p.nazovpartnera                                             AS miesto,
             k.datum_cas_obchodu                                         AS datum_cas,
             (SELECT typ_pokynu
              FROM typy_pokynov tp
              WHERE tp.typid = k.typ_pokynu)                             AS typPokynu,
             CASE WHEN sum_td_pov IS NULL THEN '' ELSE 'uzavreteSKK' END AS detailKTV
      FROM konfirmaciaktv k
               JOIN pool po ON k.dealid = po.dealid
               JOIN pooldetailreal pdr ON pdr.poolid = po.poolid
          AND pdr.subjektid in (1752)
               JOIN partner p ON k.partnerid = p.partnerid
      WHERE (
          (
              z_td >= to_date('$fromdate', 'YYYY-MM-DD')
                  AND z_td <= to_date('$todate', 'YYYY-MM-DD')
              )
              OR (
              k_td >= to_date('$fromdate', 'YYYY-MM-DD')
                  AND k_td <= to_date('$todate', 'YYYY-MM-DD')
              )
          )
        AND k.subjektid = 0
        AND k.logactivityid IN (17, 25)
      UNION ALL
      SELECT CASE
                 WHEN z_td < to_date('$fromdate', 'YYYY-MM-DD') THEN 1
                 WHEN k_td > to_date('$todate', 'YYYY-MM-DD') THEN 3
                 ELSE 2 END                  AS trieda,
             k.z_td                          AS zaciatok,
             k.k_td                          AS koniec,
             pdr.transsumareal_pov           AS suma,
             k.ir_td                         AS sadzba,
             0                               AS brutto,
             0                               AS netto_old,
             0                               AS netto,
             'SKK'                           AS mena,
             0                               AS dan,
             k.datvysporiadaniarealntd       AS vysporiadanie,
             k.cutd,
             k.subjektid,
             z_td,
             k_td,
             p.nazovpartnera                 AS miesto,
             k.datum_cas_obchodu             AS datum_cas,
             (SELECT typ_pokynu
              FROM typy_pokynov tp
              WHERE tp.typid = k.typ_pokynu) AS typPokynu,
             'vyplateneEUR'                  AS detailKTV
      FROM konfirmaciaktv k
               JOIN pool po ON k.dealid = po.dealid
               JOIN pooldetailreal pdr ON pdr.poolid = po.poolid
          AND pdr.$fondid_add
               JOIN partner p ON k.partnerid = p.partnerid
      WHERE (
          (
              z_td >= to_date('$fromdate', 'YYYY-MM-DD')
                  AND z_td <= to_date('$todate', 'YYYY-MM-DD')
              )
              OR (
              k_td >= to_date('$fromdate', 'YYYY-MM-DD')
                  AND k_td <= to_date('$todate', 'YYYY-MM-DD')
              )
          )
        AND k.subjektid = 0
        AND k.logactivityid IN (17, 25)
        AND k.sum_td_pov IS NOT NULL -- ktv po konverzii preratane do EUR )
     ) result
ORDER BY mena, trieda, zaciatok, koniec;
";
$terminovaneVklady = Connection::getDataFromDatabase($query, defaultDB);
$terminovaneVklady = $terminovaneVklady[1][0];

$cnt = 0;
$mena_foo = "";
$trieda_foo = '';

for ($i = 0; $i < $terminovaneVklady[0]; $i++) {
    $cnt++;
    echo $cnt;
    $mena = $terminovaneVklady['mena'];
    echo "Mena: " . $mena;
    $suma = $terminovaneVklady['suma'];
    $sadzba = $terminovaneVklady['sadzba'];
    $brutto = $terminovaneVklady['brutto'];
    $dan = $terminovaneVklady['dan'];
    $netto = $terminovaneVklady['netto'];
    $zaciatok = $terminovaneVklady['zaciatok'];
    $koniec = $terminovaneVklady['koniec'];
    $cisloKTV = $terminovaneVklady['cutd'];
    $trieda = $terminovaneVklady['trieda'];
    $miesto = $terminovaneVklady['miesto'];
    $datum_cas = $terminovaneVklady['datum_cas'];
    $typ_pokynu = $terminovaneVklady['typPokynu'];
    $detailKTV = $terminovaneVklady['detailKTV'];

    $style_hr = "";

    if ($mena_foo != $mena) {
        if ($mena_foo != "") {
            $vypis_obsah = $vypis_obsah . '						
				</tbody>
				</table>
			';
        }
        $vypisyspolu++;
        $vypis_obsah = $vypis_obsah . '	
			<br><br><br>
			<table style="width:100%;border-collapse:collapse;">				
				<tr>
					<td>
						<h3style="color:' . $farbaciara2 . '">Prehľad termínovaných vkladov</h3>
		';
        if (!isset($mena) or $mena == '') $vypis_obsah . ' č ';
        $vypis_obsah = $vypis_obsah . $mena . '
					</td>
				</tr>	
			</table>
			<br><br>
			<table style="width:100%;border-collapse:collapse;border-top:1pt solid ' . $farbaciara3 . ';border-bottom:1pt solid ' . $farbaciara3 . ';">
				<thead>
					<tr>
						<th style="text-align:left;font-size:8;"><b>Uzavretie TV</b></th>
						<th style="text-align:left;font-size:8;"<b>Zriadenie TV</b></th>
						<th style="text-align:left;font-size:8;"><b>Splatnosť TV</b></th>
						<th style="text-align:left;font-size:8;"><b>Miesto<BR>výkonu</b></th>
						<th style="text-align:left;font-size:8;"><b>Typ<BR>pokynu</b></th>		
						<th style="text-align:right;font-size:8;"><b>Suma</b></th>
						<th style="text-align:right;font-size:8;"><b>Sadzba</b></th>
						<th style="text-align:right;font-size:8;"><b>rok brutto</b></th>
						<th style="text-align:right;font-size:8;"><b>Daň</b></th>
						<th style="text-align:right;font-size:8;"><b>rok netto</b></th>
					</tr>
				</thead>
				<tbody>
					<tr style="width:100%;line-height:1pt;">
						<td colspan="10" style="border-bottom:0.1mm solid ' . $farbaciara1 . ';height:1pt;min-width:100%;width:100%;"></td>
					</tr>
					<tr style="width:100%;line-height:1pt;">
						<td colspan="10" style="height:1pt;min-width:100%;width:100%;"></td>
					</tr>
		';

    } elseif ($trieda_foo != $trieda) {
        $style_hr = 'style="border-top: 0.25mm dashed #aaaaaa"';
    }

    $vypis_obsah = $vypis_obsah . '		
		<tr>
	';
    $vypis_obsah = $vypis_obsah . '		
			<td ' . $style_hr . '>' . $datum_cas . '</td>
		';
    $vypis_obsah = $vypis_obsah . '		
			<td  style="font-size:8;" ' . $style_hr . '>' . $zaciatok . '</td>
			<td  style="font-size:8;" ' . $style_hr . '>' . $koniec . '</td>
			<td  style="font-size:8;" ' . $style_hr . '>' . $miesto . '</td>
			<td  style="font-size:8;" ' . $style_hr . '>' . $typ_pokynu . '</td>		
			<td  style="text-align:right;font-size:8;" ' . $style_hr . '>' . bcdiv($suma, 1, 2) . '</td>
			<td  style="text-align:right;font-size:8;" ' . $style_hr . '>' . bcdiv($sadzba, 1, 4) . '</td>
	';
    $vypis_obsah = $vypis_obsah . '		
			<td colspan="3" style="text-align:center;font-size:8;" ' . $style_hr . '>Vyplatené v EUR</td>
		';
    $vypis_obsah = $vypis_obsah . '			
			</tr>
			<tr style="width:100%;line-height:1pt;">
				<td colspan="10" style="height:1pt;min-width:100%;width:100%;"></td>
			</tr>
		</tbody>
	</table>
	';

    $mena_foo = $mena;
    $trieda_foo = $trieda;
}

//------------------konverzie-----------------------------
$query = "SELECT *
FROM (
    SELECT
        k.datum_cas_obchodu AS datum_cas,
        p.nazovpartnera AS miesto,
        (SELECT typ_pokynu FROM typy_pokynov tp WHERE tp.typid = k.typ_pokynu) AS typPokynu,
        k.kurz,
        k.sumakredit,
        k.sumadebet,
        k.menakredit,
        k.menadebet,
        CASE WHEN k.typ_konverzie = 0 THEN 'Spot' WHEN k.typ_konverzie = 2 THEN 'Forward' END AS druhObchodu,
        TO_CHAR(k.dat_vysporiadaniecukredit, 'DD.MM.YYYY') AS datum
    FROM
        konverzia k,
        partner p
    WHERE
        k.logactivityid >= 12 AND
        k.logactivityid != 14 AND
        p.partnerid = k.partnerid AND
        k.$fondid_add AND
        k.dat_realizacia >= '$fromdate'::DATE AND
        k.dat_realizacia <= '$todate'::DATE

    UNION ALL

    SELECT
        k.datum_cas_obchodu AS datum_cas,
        p.nazovpartnera AS miesto,
        (SELECT typ_pokynu FROM typy_pokynov tp WHERE tp.typid = k.typ_pokynu) AS typPokynu,
        k.kurz,
        m.pocet AS sumakredit,
        pdr.transsumareal AS sumadebet,
        k.menakredit,
        k.menadebet,
        CASE WHEN k.typ_konverzie = 0 THEN 'Spot' WHEN k.typ_konverzie = 2 THEN 'Forward' END AS druhObchodu,
        TO_CHAR(k.dat_vysporiadaniecukredit, 'DD.MM.YYYY') AS datum
    FROM
        konverzia k,
        konverziaobratid ko,
        partner p,
        pool po,
        pooldetailreal pdr,
        majetokarchiv m
    WHERE
        k.logactivityid >= 12 AND
        k.logactivityid != 14 AND
        p.partnerid = k.partnerid AND
        k.subjektid = 0 AND
        pdr.$fondid_add AND
        po.poolid = pdr.poolid AND
        k.dealid = po.dealid AND
        ko.dealid = k.dealid AND
        m.subjektid = pdr.subjektid AND 
        m.obratid = ko.obratid AND 
        uctovnykod IN (315160, 315161) AND 
        m.mena = k.menakredit AND 
        m.destinacia = 'konverziaobratid' AND
        k.dat_realizacia >= '$fromdate'::DATE AND
        k.dat_realizacia <= '$todate'::DATE
) result
ORDER BY datum;	
";

$cnt = 0;
$konverzie = Connection::getDataFromDatabase($query, defaultDB);
$konverzieCount = $konverzie[0];
$konverzie = $konverzie[1][0];

for ($i = 0; $i < $konverzieCount; $i++) {
    $datum = $konverzie['datum'];
    $datum_cas = $konverzie['datum_cas'];
    $miesto = $konverzie['miesto'];
    $typPokynu = $konverzie['typPokynu'];
    $kurz = $konverzie['kurz'];
    $sumakredit = bcdiv($konverzie['sumakredit'], 1, 2);
    $sumadebet = bcdiv($konverzie['sumadebet'], 1, 2);
    $menakredit = $konverzie['menakredit'];
    $menadebet = $konverzie['menadebet'];
    $druhObchodu = $konverzie['druhObchodu'];
    if ($cnt == 0) {
        $vypisyspolu++;
        $vypis_obsah = $vypis_obsah . '			
			<br>
			<br>
			<br>
			<table style="width:100%;border-collapse:collapse;">				
				<tr>
					<td>
						<h3 style="color:' . $farbaciara2 . '">Prehľad menových konverzií</h3>
					</td>
				</tr>				
			</table>
			<br><br>
			<table style="width:100%;border-collapse:collapse;border-top:1pt solid ' . $farbaciara3 . ';border-bottom:1pt solid ' . $farbaciara3 . ';">
				<thead>
					<tr >
						<th style="width:13%;text-align:left;font-size:8;"><b>Typ konverzie</b></th>
						<th style="width:15%;text-align:center;font-size:8;"><b>Uzavretie<br>konverzie</b></th>
						<th style="width:15%;text-align:center;font-size:8;"><b>Vyrovnanie<br>konverzie</b></th>
						<th style="width:14%;text-align:center;font-size:8;"><b>Menový pár</b></th>
						<th style="width:7%;text-align:center;font-size:8;"><b>Kurz</b></th>
						<th style="width:16%;text-align:right;font-size:8;"><b>Objem nakupo-<BR>vanej meny</b></th>
						<th style="width:16%;text-align:right;font-size:8;"><b>Objem predá-<BR>vanej meny</b></th>
					</tr>
				</thead>
				<tbody>
					<tr style="width:100%;line-height:1pt;">
						<td colspan="7" style="border-bottom:0.1mm solid ' . $farbaciara1 . ';height:1pt;min-width:100%;width:100%;"></td>
					</tr>
					<tr style="width:100%;line-height:1pt;">
						<td colspan="7" style="height:1pt;min-width:100%;width:100%;"></td>
					</tr>
		';
    }

    $vypis_obsah = $vypis_obsah . '		
			<tr valign="center">			
				<td style="width:13%;text-align:left;font-size:8;"><div>' . $druhObchodu . '</div></td>
				<td style="width:15%;text-align:center;font-size:8;"><div>' . $datum_cas . '</div></td>			
				<td style="width:15%;text-align:center;font-size:8;"><div>' . $datum . '</div></td>
				<td style="width:14%;text-align:center;font-size:8;"><div>' . $menakredit . ' / ' . $menadebet . '</div></td>
				<td style="width:7%;text-align:right;font-size:8;"><div>' . bcdiv($kurz_mena, 1, 4) . '</div></td>
				<td style="width:16%;text-align:right;font-size:8;"><div>' . $sumakredit . '</div></td> 
				<td style="width:16%;text-align:right;font-size:8;"><div>' . $sumadebet . '</div></td>
			</tr>
	';
    $cnt++;
}
$vypis_obsah = $vypis_obsah . '
				
				<tr style="width:100%;line-height:1pt;">
					<td colspan="7" style="height:1pt;min-width:100%;width:100%;"></td>
				</tr>
			</tbody>
		</table>
	';


//------------------------------------pohyby na majetkovom ucte----------------------------------		
$pomerNominalu = "(CASE WHEN kcp.eqid='Bonds' THEN (select faktor from floatkupon where isincurrric = maj.kodaktiva and (maj.obratdatatimezauctovane - e.exfrekist)  >= datefrom and (maj.obratdatatimezauctovane - e.exfrekist) <= datetill  ) ELSE 1 END)";
$query = "
		select * 
		from (
			select	CASE
    WHEN kcp.dealid IN (46663904, 47124904) THEN 'Predčasné splatenie dlhopisu na výzvu emitenta'
    ELSE
        CASE
            WHEN kcp.druhobchodu = 'nakup' THEN 'Nákup'
            WHEN kcp.druhobchodu = 'predaj' THEN 'Predaj'
            WHEN kcp.druhobchodu = 'vklad' THEN 'Vklad'
            WHEN kcp.druhobchodu = 'vyber' THEN 'Výber'
            WHEN kcp.druhobchodu = 'vklad-pr' THEN 'Prevod z port.<br>č.' || (
                SELECT cislozmluvy
                FROM portfolio f
                WHERE fondid = (
                    SELECT fondid
                    FROM konfirmaciacp k2
                    WHERE k2.dealid = (kcp.dealid) - 1000
                    AND f.fondid = k2.subjektid
                )
            )
            WHEN kcp.druhobchodu = 'vyber-pr' THEN 'Prevod na port.<br>č.' || (
                SELECT cislozmluvy
                FROM portfolio f
                WHERE fondid = (
                    SELECT fondid
                    FROM konfirmaciacp k2
                    WHERE k2.dealid = (kcp.dealid) + 1000
                    AND f.fondid = k2.subjektid
                )
            )
            WHEN kcp.druhobchodu = 'vklad-vy' THEN 'Výmena CP - pripísanie CP'
            WHEN kcp.druhobchodu = 'vyber-vy' THEN 'Výmena CP - odpísanie CP'
        END
END AS popis,
					rcp.datvysporiadaniamureal,
					e.isin,
					e.isinreal,
					e.eqid,
					edruh.poddruheq as poddruheq,
					e.cpnaz,e.cpnazskratka,
					rcp.kusovreal as pocet,
					kcp.currencyidtrade,
					rcp.kurzreal as kurz,
					rcp.auvreal as auv,
					rcp.transsuma as spolu,
					round((rcp.kurzreal * rcp.kusovreal * coalesce(e.nominalemisie, 1)  * $pomerNominalu / 
					        (CASE WHEN kcp.eqid='Bonds' THEN 100 ELSE 1 END)),2) as bezAUV,
					maj.obratdatatimezauctovane as datum,
					currencyidtrade as mena,					
					case when kcp.dealid in (46663904,47124904) then '--'
						else p.nazovpartnera end as miesto,
					kcp.datum_cas_obchodu as datum_cas,					
					case when kcp.dealid in (46663904,47124904) then '--'
						else (select typ_pokynu from typy_pokynov tp where tp.typid = kcp.typ_pokynu) end as typPokynu,
					CASE
                        WHEN kcp.druhobchodu = 'nakup' THEN 0
                        WHEN kcp.druhobchodu = 'predaj' THEN 1
                        WHEN kcp.druhobchodu = 'vklad' THEN 0
                        WHEN kcp.druhobchodu = 'vyber' THEN 1
                        WHEN kcp.druhobchodu = 'vklad-pr' THEN 0
                        WHEN kcp.druhobchodu = 'vyber-pr' THEN 1
                        ELSE NULL
                    END AS MD_D
			from	konfirmaciacp kcp,
					rekonfirmaciacp rcp,
					rekonfirmaciacpobratid rcpo,
					dbequity e,
					equityid eid,
					equitydruh edruh,
					(select obratid, obratdatatimezauctovane, kodaktiva
					 from 	majetoktoday
					 where	obratid > 0
							and uctovnykod in (251110,251200,251300)
							and obratdatatimezauctovane >= to_date('$fromdate','YYYY-MM-DD')
							and obratdatatimezauctovane <= to_date('$todate','YYYY-MM-DD')
							and destinacia not like 'muprevod'
					 union all
					 select obratid, obratdatatimezauctovane, kodaktiva
					 from majetokarchiv
					 where	obratid > 0
							and uctovnykod in (251110,251200,251300)
							and obratdatatimezauctovane >= to_date('$fromdate','YYYY-MM-DD')
							and obratdatatimezauctovane <= to_date('$todate','YYYY-MM-DD')
							and destinacia not like 'muprevod'
					) maj,
					partner p
			where	kcp.dealid = rcp.dealid
					and $fondid_add
					and e.isin = kcp.isin
					and e.eqid = eid.eqid
					and rcpo.dealid=rcp.dealid
					and rcpo.tranza=rcp.tranza
					and maj.obratid=rcpo.obratid
					and kcp.partnerid = p.partnerid
					and edruh.druheqid = e.druheqid
						
			union all
				
			select	CASE
    WHEN kcp.dealid = 46663904 THEN 'Predčasné splatenie dlhopisu na výzvu emitenta'
                    ELSE
                        CASE kcp.druhobchodu
                            WHEN 'nakup' THEN 'Nákup'
                            WHEN 'predaj' THEN 'Predaj'
                            WHEN 'vklad' THEN 'Vklad'
                            WHEN 'vyber' THEN 'Výber'
                            WHEN 'vklad-vy' THEN 'Výmena CP - pripísanie CP'
                            WHEN 'vyber-vy' THEN 'Výmena CP - odpísanie CP'
                        END
                END AS popis,
                rcp.datvysporiadaniamureal,
                e.isin,
                e.isinreal,
                e.eqid,
                edruh.poddruheq AS poddruheq,
                e.cpnaz,
                e.cpnazskratka,
                pdr.ksreal AS pocet,
                kcp.currencyidtrade,
                rcp.kurzreal AS kurz,
                pdr.auvreal AS auv,
                pdr.transsumareal AS spolu,
                ROUND((rcp.kurzreal * pdr.ksreal * coalesce(e.nominalemisie, 1) * $pomerNominalu /
                    (CASE WHEN kcp.eqid='Bonds' THEN 100 ELSE 1 END)), 2) AS bezAUV,
                maj.obratdatatimezauctovane AS datum,
                currencyidtrade AS mena,
                CASE
                    WHEN kcp.dealid = 46663904 THEN '--'
                    ELSE p.nazovpartnera
                END AS miesto,
                kcp.datum_cas_obchodu AS datum_cas,
                CASE
                    WHEN kcp.dealid = 46663904 THEN '--'
                    ELSE (SELECT typ_pokynu FROM typy_pokynov tp WHERE tp.typid = kcp.typ_pokynu)
                END AS typPokynu,
                CASE kcp.druhobchodu
                    WHEN 'nakup' THEN 0
                    WHEN 'predaj' THEN 1
                    WHEN 'vklad' THEN 0
                    WHEN 'vyber' THEN 1
                END AS MD_D
			from	konfirmaciacp kcp,
					rekonfirmaciacp rcp,
					rekonfirmaciacpobratid rcpo,
					dbequity e,
					equityid eid,
					equitydruh edruh,
					(select obratid, obratdatatimezauctovane, subjektid, kodaktiva
					 from 	majetoktoday
					 where	obratid > 0
							and uctovnykod in (251110,251200,251300)
							and obratdatatimezauctovane >= to_date('$fromdate','YYYY-MM-DD')
							and obratdatatimezauctovane <= to_date('$todate','YYYY-MM-DD')
							and destinacia not like 'muprevod'
					 union all
					 select obratid, obratdatatimezauctovane, subjektid, kodaktiva
					 from majetokarchiv
					 where	obratid > 0
							and uctovnykod in (251110,251200,251300)
							and obratdatatimezauctovane >= to_date('$fromdate','YYYY-MM-DD')
							and obratdatatimezauctovane <= to_date('$todate','YYYY-MM-DD')
							and destinacia not like 'muprevod'
					) maj,
					pool po,
					pooldetailreal pdr,
					partner p
			where	kcp.dealid = rcp.dealid
					and kcp.subjektid =0
					and e.eqid = eid.eqid
					and pdr.poolid = po.poolid 
					and pdr.subjektid = maj.subjektid
					and pdr.$fondid_add
					and kcp.dealid = po.dealid
					and rcp.tranza = pdr.tranza
					and e.isin = kcp.isin
					and rcpo.dealid=rcp.dealid
					and rcpo.tranza=rcp.tranza
					and maj.obratid=rcpo.obratid
					and kcp.partnerid = p.partnerid
					and edruh.druheqid = e.druheqid
													
			union all						
			
			select	'Splatenie' as popis,
					s.datumvyplaty as datvysporiadaniamureal,
					e.isin,
					e.isinreal,
					e.eqid,
					edruh.poddruheq as poddruheq,
					e.cpnaz,e.cpnazskratka,
					s.pocet,
					s.mena as currencyidtrade,
					/*100 as kurz,*/
					s.suma*100/(e.nominalemisie*s.pocet) as kurz, 
					0 as auv,
					s.suma as spolu,
					s.suma as bezAUV,
					s.datum as datum,
					s.mena as mena,
					null as miesto,
					'Aktivita emitenta' as datum_cas,
					null as typPokynu,
					1 as MD_D
			from
					splatenie s,
					dbequity e,
					dbequitycurr c,
					dbequitycurrric r,
					equitydruh edruh
			where
					s.kodaktiva = r.isincurrric
					and r.isincurr = c.isincurr
					and c.isin = e.isin
					and e.druheqid = edruh.druheqid
					and s.uctovnykod = 251110
					and s.datum >= to_date('$fromdate','YYYY-MM-DD')
					and s.datum <= to_date('$todate','YYYY-MM-DD')
					and s.$fondid_add
					and s.datum_naroku = (select max(datesplatnost) from floatkupon where s.kodaktiva = isincurrric and istina >= 100)
					
			union all

			select	'Čiastočné splatenie priebežné' as popis,
					s.datumvyplaty as datvysporiadaniamureal,
					e.isin,
					e.isinreal,
					e.eqid,
					edruh.poddruheq as poddruheq,
					e.cpnaz,e.cpnazskratka,
					s.pocet,
					s.mena as currencyidtrade,
					100 as kurz,
					0 as auv,
					s.suma as spolu,
					s.suma as bezAUV,
					s.datum as datum,
					s.mena as mena,
					null as miesto,
					'Aktivita emitenta' as datum_cas,
					null as typPokynu,
					1 as MD_D
			from
					splatenie s,
					dbequity e,
					dbequitycurr c,
					dbequitycurrric r,
					equitydruh edruh
			where
					s.kodaktiva = r.isincurrric
					and r.isincurr = c.isincurr
					and c.isin = e.isin
					and e.druheqid = edruh.druheqid
					and s.uctovnykod = 251110
					and s.datum >= to_date('$fromdate','YYYY-MM-DD')
					and s.datum <= to_date('$todate','YYYY-MM-DD')
					and s.$fondid_add
					and s.datum_naroku < (select max(datesplatnost) from floatkupon where s.kodaktiva = isincurrric and istina < 100)
					
			union all

			select	'Čiastočné splatenie konečné' as popis,
					s.datumvyplaty as datvysporiadaniamureal,
					e.isin,
					e.isinreal,
					e.eqid,
					edruh.poddruheq as poddruheq,
					e.cpnaz,e.cpnazskratka,
					s.pocet,
					s.mena as currencyidtrade,
					100 as kurz,
					0 as auv,
					s.suma as spolu,
					s.suma as bezAUV,
					s.datum as datum,
					s.mena as mena,
					null as miesto,
					'Aktivita emitenta' as datum_cas,
					null as typPokynu,
					1 as MD_D
			from
					splatenie s,
					dbequity e,
					dbequitycurr c,
					dbequitycurrric r,
					equitydruh edruh
			where
					s.kodaktiva = r.isincurrric
					and r.isincurr = c.isincurr
					and c.isin = e.isin
					and e.druheqid = edruh.druheqid
					and s.uctovnykod = 251110
					and s.datum >= to_date('$fromdate','YYYY-MM-DD')
					and s.datum <= to_date('$todate','YYYY-MM-DD')
					and s.$fondid_add
					and s.datum_naroku = (select max(datesplatnost) from floatkupon where s.kodaktiva = isincurrric and istina < 100)
					
			union all

			select	da.nazov  as popis,
					s.datumvyplaty as datvysporiadaniamureal,
					e.isin,
					e.isinreal,
					e.eqid,
					edruh.poddruheq as poddruheq,
					e.cpnaz,e.cpnazskratka,
					s.pocet,
					s.mena as currencyidtrade,
					null as kurz,
					null as auv,
					null as spolu,
					s.pocet as bezAUV,
					s.datumvyplaty as datum,
					s.mena as mena,
					null as miesto,
					'Aktivita emitenta' as datum_cas,
					null as typPokynu,
					da.MD_D  as MD_D
			from
					splatenieakcia s,
					dividendaakciatyp da,
					dbequity e,
					dbequitycurr c,
					dbequitycurrric r,
					equitydruh edruh
			where
					s.kodaktiva = r.isincurrric
					and r.isincurr = c.isincurr
					and c.isin = e.isin
					and e.druheqid = edruh.druheqid
					and s.datumvyplaty >= to_date('$fromdate','YYYY-MM-DD')
					and s.datumvyplaty <= to_date('$todate','YYYY-MM-DD')
					and s.$fondid_add
					and s.subkodobratu in (select subkodobratu from dividendaakciatyp where hotovost = 0)
					and da.subkodobratu = s.subkodobratu
		) as kr
		order by datum";

$pohyby = Connection::getDataFromDatabase($query, defaultDB);
$pohybyCount = $pohyby[0];
$pohyby = $pohyby[1][0];

for ($i = 0; $i < $pohybyCount; $i++) {
    $nazov = $pohyby['nazov'];
    $isin = $pohyby['isin'];
    $isinreal = $pohyby['isinreal'];
    $pocet = $pohyby['pocet'];
    $mena = $pohyby['mena'];
    $kurz = $pohyby['kurz'];
    $auv = $pohyby['auv'];
    $suma = $pohyby['spolu'];
    $bezAUV = $pohyby['bezAUV'];
    $popis = $pohyby['popis'];
    $typ = $pohyby['eqid'];
    $druh = $pohyby['poddruheq'];
    $datum = $pohyby['datum'];
    $datum = substr($datum, 8, 2) . '.' . substr($datum, 5, 2) . '.' . substr($datum, 0, 4);
    $datum_cas = $pohyby['datum_cas'];
    $miesto = $pohyby['miesto'];
    $typPokynu = $pohyby['typPokynu'];
    $MD_D = $pohyby['MD_D'];

    if ($i == 1) {
        $vypisyspolu++;
        $vypis_obsah = $vypis_obsah . '					
			<br><br><br>
			<table style="width:100%;border-collapse:collapse;">				
				<tr>
					<td>
						<h3 style="color:' . $farbaciara2 . '">Pohyby na majetkovom účte</h3>
					</td>
				</tr>
			</table>
			<br><br>
			<table style="width:100%;border-collapse:collapse;border-top:1pt solid ' . $farbaciara3 . ';border-bottom:1pt solid ' . $farbaciara3 . ';">
				<thead border-collapse:collapse;>
					<tr>				
						<th style="width:11%;text-align:left;font-size:8;"><b>Uzavretie obchodu</b></th>
						<th style="width:11%;text-align:left;font-size:8;"><b>Transakcia</b></th>
						<th style="width:10%;text-align:left;font-size:8;"><b>Názov CP</b></th>
						<th style="width:12%;text-align:left;font-size:8;"><b>ISIN</b></th>
						<th style="width:10%;text-align:right;font-size:8;"><b>Počet CP</b></th>
						<th style="width:6%;font-size:8;"><b>Mena</b></th>
						<th style="width:8%;text-align:right;font-size:8;"><b>Cena CP</b></th>
						<th style="width:11%;text-align:right;font-size:8;"><b>Cena bez AUV</b></th>
						<th style="width:10%;text-align:rightfont-size:8;"><b>AUV</b></th>
						<th style="width:11%;text-align:right;font-size:8;"><b>Cena spolu</b></th>
					</tr>
					<tr>
						<th style="width:11%;text-align:left;font-size:8;"><b>Vysporiadanie</b></th>
						<th style="width:11%;text-align:left;font-size:8;"><b>Typ pokynu</b></th>		
						<th style="width:10%;text-align:left;font-size:8;"><b>Miesto výkonu</b></th>
						<th colspan="3" style="width:12%;text-align:left;font-size:8;"><b>Druh CP</b></th>
						<th colspan="2" ></th>
						<th colspan="2" ></th>
					</tr>
				</thead>
				<tbody>
					<tr style="width:100%;line-height:1pt;">
						<td colspan="10" style="border-bottom:0.1mm solid ' . $farbaciara1 . ';height:1pt;min-width:100%;width:100%;"></td>
					</tr>
					<tr style="width:100%;line-height:1pt;">
						<td colspan="10" style="height:1pt;min-width:100%;width:100%;"></td>
					</tr>
		';
    }

    if ($i > 1) {
        $vypis_obsah = $vypis_obsah . '	
			<tr style="width:100%;line-height:1pt;">
				<td colspan="10" style="border-bottom:0.1mm dashed ' . $farbaciara1 . ';height:1pt;min-width:100%;width:100%;"></td>
			</tr>
			<tr style="width:100%;line-height:1pt;">
				<td colspan="10" style="height:1pt;min-width:100%;width:100%;"></td>
			</tr>
		';
    }

    $vypis_obsah = $vypis_obsah . '		
		<tr style="page-break-after:avoid;">
			<td style="width:11%;font-size:8;">' . $datum_cas . '</td>
			<td style="width:11%;font-size:8;">' . $popis . '</td>
			<td style="width:10%;font-size:8;">' . $nazov . '</td>
			<td style="width:12%;font-size:8;">' . $isinreal . '</td>
			<td style="width:10%;text-align:right;font-size:8;">' . ($pohyby['MD_D'] == 0 ? '' : '-') . bcdiv($pocet, 1, 0) . '</td>
			<td style="width:6%;text-align:center;font-size:8;">' . $mena . '</td>
			<td style="width:8%;text-align:right;font-size:8;">' . ($pohyby['kurz'] == null ? '' : bcdiv($kurz, 1, 4)) . '</td>
			<td style="width:11%;text-align:right;font-size:8;">' . ($typ == 'Bonds' && $popis != 'Splatenie' ? bcdiv($bezAUV, 1, 2) : "") . '</td>
			<td style="width:10%;text-align:right;font-size:8;">' . ($typ == 'Bonds' && $popis != 'Splatenie' ? bcdiv($auv, 1, 2) : "") . '</td>
			<td style="width:11%;text-align:right;font-size:8;">' . ($pohyby['spolu'] == null ? '' : bcdiv($suma, 1, 2)) . '</td>
		</tr>
		<tr>
			<td style="width:11%;font-size:8;">' . $datum . '</td>
			<td style="width:11%;font-size:8;">' . $typPokynu . '</td>		
			<td style="width:10%;font-size:8;">' . $miesto . '</td>
			<td colspan=3 style="width:11%;font-size:8;">' . $druh . '</td>
			<td style="font-size:8;"></td>
			<td style="font-size:8;"></td>
			<td style="font-size:8;"></td>
			<td style="font-size:8;"></td>
		</tr>
	';
}

$vypis_obsah = $vypis_obsah . '	
				<tr style="width:100%;line-height:1pt;">
					<td colspan="10" style="height:1pt;min-width:100%;width:100%;"></td>
				</tr>	
			</tbody>
		</table>		
	';


if ($vypisyspolu == 0) {
    $vypis_obsah = $vypis_obsah . '		
		<br><br>
		<table style="width:100%;border-collapse:collapse;">	
			<tr>
				<td colspan="4"><h4>Pre zvolené obdobie neprebehli žiadne transakcie</h4></tD>
			</tr>
		</table>
	';
}

$vypis_transakcii = iconv("WINDOWS-1250", "UTF-8", $vypis_obsah);


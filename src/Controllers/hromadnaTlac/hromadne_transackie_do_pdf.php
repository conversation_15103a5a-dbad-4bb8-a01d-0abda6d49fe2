<?php
/*
 *  Maintainer: Miso
 *
 * Description:testovanie hoci<PERSON>ho 
 *
 */

//phpinfo ();


require("lib/dbconnect.php");
require("lib/functions.php");
require("lib/controls.php");
require_once('/home/<USER>/www/lib/tcpdf/tcpdf.php');
require($ROOT . 'lib/phpmailer/Exception.php');
require($ROOT . 'lib/phpmailer/PHPMailer.php');
require($ROOT . 'lib/phpmailer/SMTP.php');

$dba=new DB_SQL;

//error_reporting(E_ALL);

if(!isset($showit))
{
	$showit='';
}
if ($showit=="spusti")
{	
	$casovac=true;
	if($cassovac) echo date("Y-m-d H:i:s")."->";
	
	// hlavicka vypisov - momentalne sa nepouziva
	$hlavicka1=iconv("WINDOWS-1250", "UTF-8",'Sympatia Financie o.c.p., a.s.');
	$hlavicka2=iconv("WINDOWS-1250", "UTF-8",'Vajnorsk� 21 A, 831 03');
	$hlavicka3=iconv("WINDOWS-1250", "UTF-8",'Bratislava');
	
	$zobrazHlavickuPage1=0;
	
	// tato premenna nastavuje ci ma byt na prvej strane v paticke cislovanie stran, zobrazuje cislovanie na poradovej strane vyssej ako je dane cislo
	$zobrazPatickuPage1=0;	//upraveny aj subor tcpdf/tcpdf.php na riadku 3591 a 3592
	
	include("mypdf_class.php");
		
	$db=new	DB_Sql;
	$db2=new DB_Sql;
	$db3=new DB_Sql;

	set_time_limit(3600);
	
	if(!isset($fromdate) or $fromdate == '')
	{
		if(substr($todate,3,2)<=3)
		{
			$rok=substr($todate,6,4)-1;			
		}
		else
		{
			$rok=substr($todate,6,4);
		}
		if(fmod(substr($todate,3,2),3)==0)
		{
			$fromdate=date_create_from_format('d.m.Y',"01.".sprintf("%'.02d",(floor(substr($todate,3,2)/3)-1)*3+1).".".$rok);
		}
		else
		{
			$fromdate=date_create_from_format('d.m.Y',"01.".sprintf("%'.02d",floor(substr($todate,3,2)/3)*3+1).".".$rok);
		}

		$odpocet=DateInterval::createFromDateString('-1 day');
		$fromdate=date_add($fromdate,$odpocet);
		$fromdate=date_format($fromdate,'d.m.Y');
	}	
	
	
	$fromdateread=$fromdate;
	$todateread=$todate;
/*			//dotaz vsetkych okrem vybranych 
	$query="select * from (
				select 	m.subjektid as fondid,
						case when po.meno is null then po.prieznaz else po.meno||' '||po.prieznaz end as klient,
						f.fondnameall,p.cislozmluvy,po.vypisy_heslo,f.refmena,po.podielnikid_polaris,f.fondnameshort
				from majetoktotal m
				left join portfolio p on m.subjektid=p.fondid
				left join podielnik po on p.podielnikid=po.podielnikid	
				left join fonds f on p.fondid=f.fondid
				where m.datum>to_date('$fromdate','dd.mm.yyyy') and m.datum<=to_date('$todate','dd.mm.yyyy') and m.subjektid>1 and p.cislozmluvy not in ('2231180011','2231080329','2231080332','2231020004','2231080331','2111080249','2171080305')
				group by m.subjektid,case when po.meno is null then po.prieznaz else po.meno||' '||po.prieznaz end,f.fondnameall,p.cislozmluvy,po.vypisy_heslo,f.refmena,
							po.podielnikid_polaris,f.fondnameshort
			) a			
			
			order by a.fondid";
	*/
			//dotaz na pevne vybraneho klienta
			/*
	$query="select * from (
				select 	m.subjektid as fondid,
						case when po.meno is null then po.prieznaz else po.meno||' '||po.prieznaz end as klient,
						f.fondnameall,p.cislozmluvy,po.vypisy_heslo,f.refmena,po.podielnikid_polaris,f.fondnameshort
				from majetoktotal m
				left join portfolio p on m.subjektid=p.fondid
				left join podielnik po on p.podielnikid=po.podielnikid	
				left join fonds f on p.fondid=f.fondid
				where m.datum>to_date('$fromdate','dd.mm.yyyy') and m.datum<=to_date('$todate','dd.mm.yyyy') and m.subjektid>1 and po.podielnikid in (4313900)
				group by m.subjektid,case when po.meno is null then po.prieznaz else po.meno||' '||po.prieznaz end,f.fondnameall,p.cislozmluvy,po.vypisy_heslo,f.refmena,
							po.podielnikid_polaris,f.fondnameshort
			) a
			where ROWNUM<10
			order by a.fondid";
	*/
			//dotaz na vsetkych klientov
	$query="select * from (
				select 	m.subjektid as fondid,
						case when po.meno is null then po.prieznaz else po.meno||' '||po.prieznaz end as klient,
						f.fondnameall,p.cislozmluvy,po.vypisy_heslo,f.refmena,po.podielnikid_polaris,f.fondnameshort
				from majetoktotal m
				left join portfolio p on m.subjektid=p.fondid
				left join podielnik po on p.podielnikid=po.podielnikid	
				left join fonds f on p.fondid=f.fondid
				where m.datum>to_date('$fromdate','dd.mm.yyyy') and m.datum<=to_date('$todate','dd.mm.yyyy') and m.subjektid>1 
				group by m.subjektid,case when po.meno is null then po.prieznaz else po.meno||' '||po.prieznaz end,f.fondnameall,p.cislozmluvy,po.vypisy_heslo,f.refmena,
							po.podielnikid_polaris,f.fondnameshort
			) a			
			order by a.fondid";
			
	$dba->query($query);

	if ($fromdate!="")  $fromdate=toDBDate($fromdate);
	if ($todate!="") $todate=toDBDate($todate);
			
	while ($dba->next_record())
	{		
				
		$fondid=$dba->f('fondid');
		$cislozmluvy=$dba->f('cislozmluvy');
		$klient=$dba->f('klient');
		//$klient='XXXXXXXXXX';
		$klientdb=str_replace(" ","_",$klient);
		$vypisy_heslo=$dba->f('vypisy_heslo');
		$refmena=$dba->f("refmena");
		$polarisid=$dba->f("podielnikid_polaris");
		$typ_portfolia=$dba->f("fondnameall");
		$fnshort=$dba->f("fondnameshort");
		$cub="";
		$ucetaktiva="";
		$k1ucet="";
		$sumaAlertBottom="";
		
		$farbaciara1='black';	//tenka ciara pod hlavickou tabulky 
		$farbaciara2='#1782c5';	//nazov tabulky
		$farbaciara3='#DCE2EA';	//hruba ciara okolo tabulky	
		
		
		//'#1782c5' - modra
		//'#DCE2EA' - oranzova
				
		//-------------------generovanie html kodu vypisu transakcii----------------------
		include("vypis_transakcii.php");
		$vypis_obsah="";
		include("vypis_portfolia.php");		
		$vypis_obsah="";
		include("vypis_zhodnotenie.php");			
		$vypis_obsah="";
		//echo $vypis_transakcii."<br><br>";
		//file_put_contents('c:/vypisy/test.txt',$vypis_transakcii.":::::",FILE_APPEND);
		
		if($sumaAlertBottom=="")
		{	
							
			$dir = "c:/vypisy/01-Summary/01_vypisy_podladatumu/".$todate."/";
			if (!file_exists($dir)) mkdir($dir, 0777, true);
						
			//generovanie heslovaneho pdf do spolocneho adresara aby sa to z neho dalo lahko naraz kopirovat na mailovu kampan
			$myfile = $dir . "V�pis_portf�lia_".$klientdb."_".$cislozmluvy."_od_".$fromdateread."_do_".$todateread.".pdf";

			include("pdfko_heslovane.php");
			$image_file = 'obrazky/raster1.jpg';
			$pdf->Image($image_file, 10, 75.5,190 , '', 'JPG', '', 'T', false, 300, '', false, false, 0, false, false, false);
			$pdf->SetXY ( 0, 40);
			$pdf->writeHTML($vypis_zhodnotenie, true, false, true, false, '');
			$pdf->AddPage();
			$pdf->setPage($pdf->getPage()); 
			$pdf->Image($image_file, 10, 75.5,190 , '', 'JPG', '', 'T', false, 300, '', false, false, 0, false, false, false);
			$pdf->SetXY ( 0, 40);
			$pdf->writeHTML($vypis_portfolia, true, false, true, false, '');
			$pdf->AddPage();
			$pdf->setPage($pdf->getPage()); 
			$pdf->Image($image_file, 10, 75.5,190 , '', 'JPG', '', 'T', false, 300, '', false, false, 0, false, false, false);
			$pdf->SetXY ( 0, 40);
			$pdf->writeHTML($vypis_transakcii, true, false, true, false, '');
			$pdf->SetDisplayMode('fullpage','OneColumn','UseNone');
			$pdf->Output($myfile, 'F');		
			$pdf=null;
			
			$dir = "c:/vypisy/".$klientdb."/Vypisy/";			
			if (!file_exists($dir)) mkdir($dir, 0777, true);	
			//file_put_contents( "\\\\DC1\\GroupShares\\Trading\\Traderi\\Miso\\vypisy\\".$klientdb.".txt",$vypis_transakcii . $vypis_portfolia);
			
			//generovanie heslovaneho pdf
			$myfile = $dir . "V�pis_portf�lia_".$klientdb."_".$cislozmluvy."_od_".$fromdateread."_do_".$todateread.".pdf";

			include("pdfko_heslovane.php");
			$image_file = 'obrazky/raster1.jpg';
			$pdf->Image($image_file, 10, 75.5,190 , '', 'JPG', '', 'T', false, 300, '', false, false, 0, false, false, false);
			$pdf->SetXY ( 0, 40);
			$pdf->writeHTML($vypis_zhodnotenie, true, false, true, false, '');
			$pdf->AddPage();
			$pdf->setPage($pdf->getPage()); 
			$pdf->Image($image_file, 10, 75.5,190 , '', 'JPG', '', 'T', false, 300, '', false, false, 0, false, false, false);
			$pdf->SetXY ( 0, 40);
			$pdf->writeHTML($vypis_portfolia, true, false, true, false, '');
			$pdf->AddPage();
			$pdf->setPage($pdf->getPage()); 
			$pdf->Image($image_file, 10, 75.5,190 , '', 'JPG', '', 'T', false, 300, '', false, false, 0, false, false, false);
			$pdf->SetXY ( 0, 40);
			$pdf->writeHTML($vypis_transakcii, true, false, true, false, '');
			$pdf->SetDisplayMode('fullpage','OneColumn','UseNone');
			$pdf->Output($myfile, 'F');		
			$pdf=null;	
			

			//generovanie neheslovaneho pdf
			$myfile = $dir . "V�pis_portf�lia_".$klientdb."_".$cislozmluvy."_od_".$fromdateread."_do_".$todateread."_neheslovane.pdf";
			include("pdfko_neheslovane.php");	
			$image_file = 'obrazky/raster1.jpg';
			$pdf->Image($image_file, 10, 75.5,190 , '', 'JPG', '', 'T', false, 300, '', false, false, 0, false, false, false);
			$pdf->SetXY ( 0, 40);
			$pdf->writeHTML($vypis_zhodnotenie, true, false, true, false, '');
			$pdf->AddPage();
			$pdf->setPage($pdf->getPage()); 
			$pdf->Image($image_file, 10, 75.5,190 , '', 'JPG', '', 'T', false, 300, '', false, false, 0, false, false, false);
			$pdf->SetXY ( 0, 40);
			$pdf->writeHTML($vypis_portfolia, true, false, true, false, '');
			$pdf->AddPage();
			$pdf->setPage($pdf->getPage()); 
			$pdf->Image($image_file, 10, 75.5,190 , '', 'JPG', '', 'T', false, 300, '', false, false, 0, false, false, false);
			$pdf->SetXY ( 0, 40);
			$pdf->writeHTML($vypis_transakcii, true, false, true, false, '');
			$pdf->SetDisplayMode('fullpage','OneColumn','UseNone');
			$pdf->Output($myfile, 'F');	
			//$pdf->Output('Local Expedition', 'I');		
			$pdf=null;

		}
		else
		{			
			echo PosliMail('<EMAIL>',"Neplatna zostava transakcii - " . $cislozmluvy,iconv("UTF-8","WINDOWS-1250", $vypis_transakcii));	
		}		
	}	
	//toto kopiruje vypisy z goldmann servera C disku na sietovy M disk	
	//je to presunute sem aby to nebezalo stale v cykle
	exec("robocopy c:\\vypisy\\ \\\DC1\\GroupShares\\Backoffice\\Klienti\\ /E");
	exec("rmdir /s /q c:\\vypisy");
	
	unset($hlavicka1);
	unset($hlavicka2);
	unset($hlavicka3);
	
if($cassovac) echo date("Y-m-d H:i:s")."<br><br><br>";	
echo '<script>alert("Vypisy su ulozene")</script>';
}
?>
<html>
<head>
	<!--<meta http-equiv="Content-Type" content="text/html; charset=<?php //echo CHARSET ?>">-->
	<meta http-equiv="Content-Type" content="text/html">
	
	<link rel="stylesheet" type="text/css" href="../style/menu.css">
	<script src="../lib/js_functions.php" language="javascript">
	</script>
	<script language="Javascript">
	var pobockaObj=new Array();
	
		
	function ShowReport()
	{
		
			if(IsDate(null,document.forms['data']['todate'].value,null))
			{								
				if(checkPrintDateValidity(document.forms['data']['todate'].value,'new'))
				{
					document.forms['data']['showit'].value='spusti';
					document.forms['data'].submit();
				}
			}			
		
		
	}

	
	</script>
</head>
<body>

<form name="data" method="post">
	<input type="hidden" name="showit" value="">
	<span id="spdate">	
		<input id="fromdate" class="clsDate" name="fromdate" type="text" maxlength="10" onblur="OnBlurDate(this,true);">
		<input id="todate" class="clsDate" name="todate" type="text" maxlength="10" onblur="OnBlurDate(this,true);">
		<!--
		
		sem treba pridat vyber klienta a az potom sa povoli button dole
		
		-->
		<input disabled name="btn_ok" type="button" class="butonik_login" value="OK" onclick="javascript:ShowReport()">
	</span>
</form>


<script language="javascript">
	centerWindow(parent.window,900,500);	
		document.forms['data']['btn_ok'].disabled=false;
</script>
</body>
</html>



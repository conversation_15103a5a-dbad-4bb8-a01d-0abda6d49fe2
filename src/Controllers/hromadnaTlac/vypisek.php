<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

$fromdate = $_POST['datefrom'];
$todate = $_POST['dateto'];
$query = "select * from (
				select 	m.subjektid as fondid,
						case when po.meno is null then po.prieznaz else po.meno||' '||po.prieznaz end as klient,
						f.fondnameall,p.cislozmluvy,/*po.vypisy_heslo*/1 as vypisy_heslo,f.refmena,po.podielnikid_polaris,f.fondnameshort
				from majetoktotal m
				left join portfolio p on m.subjektid=p.fondid
				left join podielnik po on p.podielnikid=po.podielnikid	
				left join fonds f on p.fondid=f.fondid
				where m.datum>to_date('$fromdate','yyyy-mm-dd') and m.datum<=to_date('$todate','yyyy-mm-dd') and m.subjektid>1 
				group by m.subjektid,case when po.meno is null then po.prieznaz else po.meno||' '||po.prieznaz end,f.fondnameall,p.cislozmluvy,/*po.vypisy_heslo,*/f.refmena,
							po.podielnikid_polaris,f.fondnameshort
			) a			
			order by a.fondid";
$result = Connection::getDataFromDatabase($query, defaultDB);
?>
<div class="relative bg-gray-100 shadow-md">
    <div class="flex flex-col items-center justify-between p-4 space-y-3 md:flex-row md:space-y-0 md:space-x-4">
        <div class="w-full md:w-1/2">
            <label for="simple-search" class="sr-only">Vyhľadávanie</label>
            <div class="relative w-full">
                <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                    <svg aria-hidden="true" class="w-5 h-5 text-gray-500 dark:text-gray-400"
                         fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd"
                              d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                              clip-rule="evenodd"/>
                    </svg>
                </div>
                <input type="text" id="simple-search"
                       class="block w-full p-2 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                       placeholder="Vyhľadávanie" required="">
            </div>
        </div>
        <div class="flex flex-col items-stretch justify-end flex-shrink-0 w-full space-y-2 md:w-auto md:flex-row md:space-y-0 md:items-center md:space-x-3">
            <div class="flex items-center w-full space-x-3 md:w-auto">
                <button id="actionsDropdownButton" data-dropdown-toggle="actionsDropdown"
                        class="flex items-center justify-center w-full px-4 py-2 text-sm font-medium text-gray-900 bg-white border border-gray-200 rounded-lg md:w-auto focus:outline-none hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                        type="button">
                    <svg class="-ml-1 mr-1.5 w-5 h-5" fill="currentColor" viewbox="0 0 20 20"
                         xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                        <path clip-rule="evenodd" fill-rule="evenodd"
                              d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"/>
                    </svg>
                    Actions
                </button>
                <div id="actionsDropdown"
                     class="z-10 hidden bg-white divide-y divide-gray-100 rounded shadow w-44 dark:bg-gray-700 dark:divide-gray-600">
                    <ul class="py-1 text-sm text-gray-700 dark:text-gray-200"
                        aria-labelledby="actionsDropdownButton">
                        <li>
                            <a href="#"
                               class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">Mass
                                Edit</a>
                        </li>
                    </ul>
                    <div class="py-1">
                        <a href="#"
                           class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 dark:text-gray-200 dark:hover:text-white">Delete
                            all</a>
                    </div>
                </div>
                <button id="filterDropdownButton" data-dropdown-toggle="filterDropdown"
                        class="flex items-center justify-center w-full px-4 py-2 text-sm font-medium text-gray-900 bg-white border border-gray-200 rounded-lg md:w-auto focus:outline-none hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                        type="button">
                    <svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true"
                         class="w-4 h-4 mr-2 text-gray-400" viewbox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd"
                              d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z"
                              clip-rule="evenodd"/>
                    </svg>
                    Filter
                    <svg class="-mr-1 ml-1.5 w-5 h-5" fill="currentColor" viewbox="0 0 20 20"
                         xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                        <path clip-rule="evenodd" fill-rule="evenodd"
                              d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"/>
                    </svg>
                </button>
                <!-- Dropdown menu -->
                <div id="filterDropdown"
                     class="z-10 hidden w-48 p-3 bg-white rounded-lg shadow dark:bg-gray-700">
                    <h6 class="mb-3 text-sm font-medium text-gray-900 dark:text-white">
                        Category
                    </h6>
                    <ul class="space-y-2 text-sm" aria-labelledby="dropdownDefault">

                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="relative overflow-x-auto shadow-md mb-10">
    <table class="w-full text-sm text-left rtl:text-right">
        <thead class="text-xs text-gray-700 uppercase bg-gray-100">
        <tr>
            <th scope="col" class="p-4">
                <div class="flex items-center">
                    <input id="checkbox-all" type="checkbox"
                           class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                    <label for="checkbox-all" class="sr-only">checkbox</label>
                </div>
            </th>
            <th scope="col" class="px-6 py-3">
                Klient
            </th>
            <th scope="col" class="px-6 py-3">
                Číslo zmluvy
            </th>
            <th scope="col" class="px-6 py-3">
                Názov fondu
            </th>
            <th scope="col" class="px-6 py-3">
                Polaris ID
            </th>
            <th scope="col" class="px-6 py-3">
                Akcia v Lidli
            </th>
        </tr>
        </thead>
        <tbody>
        <?php
        for ($i = 0; $i < sizeof($result[1]); $i++) { ?>
            <tr class="bg-white border-b dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-600">
                <td class="w-4 p-4">
                    <div class="flex items-center">
                        <input id="checkbox-<?php echo $i; ?>" type="checkbox"
                               class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                        <label for="checkbox-<?php echo $i; ?>" class="sr-only">checkbox</label>
                    </div>
                </td>
                <td class="px-6 py-4">
                    <?php echo $result[1][$i]['klient']; ?>
                </td>
                <td class="px-6 py-4 cislozmluvy">
                    <?php echo $result[1][$i]['cislozmluvy']; ?>
                </td>
                <td class="px-6 py-4">
                    <?php echo $result[1][$i]['fondnameall']; ?>
                </td>
                <td class="px-6 py-4">
                    <?php echo $result[1][$i]['podielnikid_polaris']; ?>
                </td>
                <td class="px-6 py-4 flex gap-4">
                    <form id="generate-form" hx-post="/api/pdfGenerate" hx-target="#modal-body" hx-swap="innerHTML"
                          hx-vals='{"fromdate": "<?php echo $fromdate ?>", "todate": "<?php echo $todate; ?>", "klient": <?php echo json_encode($result[1][$i]) ?>}'>
                        <button type="submit" id="view-pdf" data-tooltip-target="tooltip-view-<?php echo $i ?>"
                                data-modal-target="default-modal" data-modal-toggle="default-modal">
                            <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true"
                                 xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 14">
                                <g stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                   stroke-width="2">
                                    <path d="M10 10a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z"/>
                                    <path d="M10 13c4.97 0 9-2.686 9-6s-4.03-6-9-6-9 2.686-9 6 4.03 6 9 6Z"/>
                                </g>
                            </svg>
                        </button>
                    </form>
                    <div id="tooltip-view-<?php echo $i ?>" role="tooltip"
                         class="absolute z-10 invisible inline-block px-3 py-2 text-sm font-medium text-white bg-gray-900 rounded-lg shadow-sm opacity-0 tooltip dark:bg-gray-700">
                        Náhľad výpisu
                        <div class="tooltip-arrow" data-popper-arrow></div>
                    </div>
                    <button type="button" data-tooltip-target="tooltip-generate-<?php echo $i ?>">
                        <svg class="w-6 h-6 text-gray-800" aria-hidden="true"
                             xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 16 20">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                  stroke-width="2"
                                  d="M1 18a.969.969 0 0 0 .933 1h12.134A.97.97 0 0 0 15 18M1 7V5.828a2 2 0 0 1 .586-1.414l2.828-2.828A2 2 0 0 1 5.828 1h8.239A.97.97 0 0 1 15 2v5M6 1v4a1 1 0 0 1-1 1H1m0 9v-5h1.5a1.5 1.5 0 1 1 0 3H1m12 2v-5h2m-2 3h2m-8-3v5h1.375A1.626 1.626 0 0 0 10 13.375v-1.75A1.626 1.626 0 0 0 8.375 10H7Z"/>
                        </svg>
                    </button>
                    <div id="tooltip-generate-<?php echo $i ?>" role="tooltip"
                         class="absolute z-10 invisible inline-block px-3 py-2 text-sm font-medium text-white bg-gray-900 rounded-lg shadow-sm opacity-0 tooltip dark:bg-gray-700">
                        Generovať výpis
                        <div class="tooltip-arrow" data-popper-arrow></div>
                    </div>
                </td>
            </tr>
            <?php
        } ?>
        </tbody>
    </table>
</div>
<script>
    let checkbox = document.getElementById("checkbox-all");
    let toggle = false;
    checkbox.addEventListener("click", () => {
        if (toggle) {
            toggle = false;
            for (let i = 0; i < document.querySelectorAll("tbody input").length; i++) {
                document.querySelectorAll("tbody input")[i].checked = toggle;
            }
        } else {
            toggle = true;
            for (let i = 0; i < document.querySelectorAll("tbody input").length; i++) {
                document.querySelectorAll("tbody input")[i].checked = toggle;
            }
        }
    })
    let searchBar = document.getElementById("simple-search");
    searchBar.addEventListener("onchange", () => {
        let tableTr = document.querySelectorAll(".cislozmluvy");
        for (let i = 0; i < tableTr.length; i++) {
            tableTr[i].style.display = "none";
        }
    })
    let generateForm = document.getElementById("generate-form");
    searchBar.addEventListener("submit", () => {

    })
</script>

<?php

	class MYPDF_POLARIS extends TCPDF {

		public function Header() {
			$image_file = 'obrazky/pf_logo_full.jpg';
			$this->Image($image_file, 12, 5, 55, '', 'JPG', '', 'T', false, 300, '', false, false, 0, false, false, false);
			$this->Cell(110, 5, '', 0, false, 'R', 0, '', 0, false, 'M', 'M');	
			$image_file = 'obrazky/sf_logo_full.jpg';
			$this->Image($image_file, 152, 5, 43, '', 'JPG', '', 'T', false, 300, '', false, false, 0, false, false, false);	

/*
			$this->SetAutoPageBreak(false, 0);
			$image_file = 'obrazky/girlanda.jpg';
			$this->Image($img_file, 0, 0, '', '', '', '', 'T', false, 300, '', false, false, 0);
			$this->SetAutoPageBreak(true, 30);
*/
		}

		public function Footer() {
			
			$image_file = 'obrazky/polaris_paticka.jpg';
			$this->Image($image_file, 0, 274, 210, '', 'JPG', '', 'T', false, 300, '', false, false, 0, false, false, false);
			
			//girlanda
			//potrebujem rozbehat obrazok png s transparentnym pozadim!!!
			//$image_file = 'obrazky/girlanda.jpg';
			//$image_file = 'obrazky/girlanda.png';
			//$this->Image($image_file, 15, 60,180, '', '', '', 'T', false, 300, '', false, false, 0, false, false, false);
			//$this->Image($img_file, 15, 60, 180, '', '', '', '', false, 300, '', false, false, 0);
			
			
			
			
			/*// Position at 30 mm from bottom
			//$this->SetY(-25);
			//$this->SetX(20);
			//$this->SetFont('robotocondensed', '', 9);	
			//$image_file = 'obrazky/polaris_logo.jpg';
			//$this->Image($image_file, 0, 0, 20, '', 'JPG', '', 'T', false, 300, '', false, false, 0, false, false, false);
			$this->Cell(50, 5, '', 0, false, 'R', 0, '', 0, false, 'M', 'M');			
			$this->Cell(45, 5, 'INVESTMENT COMPANY', 0, false, 'L', 0, '', 0, false, 'M', 'M');	
			$this->Cell(60, 5, '', 0, false, 'R', 0, '', 0, false, 'M', 'M');
			$image_file = 'obrazky/polaris_logo.jpg';
			$this->Image($image_file, 10, 10, 20, '', 'JPG', '', 'T', false, 300, '', false, false, 0, false, false, false);
			$this->LN(3);	
			$this->Cell(155, 5, '', 0, false, 'R', 0, '', 0, false, 'M', 'M');
			$this->Cell(45, 5, 'Polaris Finance SICAV p.l.c.', 0, false, 'L', 0, '', 0, false, 'M', 'M');	
			$this->LN(3);	
			$this->Cell(155, 5, '', 0, false, 'R', 0, '', 0, false, 'M', 'M');
			$this->Cell(45, 5, '475, Triq il-Kbira San Guzepp', 0, false, 'L', 0, '', 0, false, 'M', 'M');
			$this->LN(3);*/			
		}
	}


/*
$image_file = 'logo_SF.jpg';
			//$image_file = 'loader.gif';
			$this->Image($image_file, 10, 10, 45, '', 'JPG', '', 'T', false, 300, '', false, false, 0, false, false, false);
			$this->SetFont('robotocondensed', 'B',6);		
			$this->Cell(110, 5, '', 0, false, 'R', 0, '', 0, false, 'M', 'M');			
			$this->Cell(45, 5, $hlavicka1, 0, false, 'L', 0, '', 0, false, 'M', 'M');		
			$this->LN(3);	
			$this->Cell(155, 5, '', 0, false, 'R', 0, '', 0, false, 'M', 'M');
			$this->Cell(45, 5, $hlavicka2, 0, false, 'L', 0, '', 0, false, 'M', 'M');	
			$this->LN(3);	
			$this->Cell(155, 5, '', 0, false, 'R', 0, '', 0, false, 'M', 'M');
			$this->Cell(45, 5, $hlavicka3, 0, false, 'L', 0, '', 0, false, 'M', 'M');
			$this->LN(3);				


<div class="Aac3db8b49e3f469c871ed9c2a5ab9dc339">
<span class="Aac3db8b49e3f469c871ed9c2a5ab9dc338">INVESTMENT COMPANY</span>
</div>
<div class="Aac3db8b49e3f469c871ed9c2a5ab9dc341">
<span class="Aac3db8b49e3f469c871ed9c2a5ab9dc340">&nbsp;</span>
</div>
<div class="Aac3db8b49e3f469c871ed9c2a5ab9dc343">
<span class="Aac3db8b49e3f469c871ed9c2a5ab9dc342">Polaris Finance SICAV p.l.c.</span>
</div>
<div class="Aac3db8b49e3f469c871ed9c2a5ab9dc345">
<span class="Aac3db8b49e3f469c871ed9c2a5ab9dc344">475, Triq il-Kbira San Guzepp</span>
</div>
<div class="Aac3db8b49e3f469c871ed9c2a5ab9dc347">
<span class="Aac3db8b49e3f469c871ed9c2a5ab9dc346">Santa Venera SVR 1011, Malta</span>
</div>
<div class="Aac3db8b49e3f469c871ed9c2a5ab9dc349">
<span class="Aac3db8b49e3f469c871ed9c2a5ab9dc348">www.polarisfinance.eu  <EMAIL></span>
</div>
</div>


<div style="width:53.62mm;">
<div class="Aac3db8b49e3f469c871ed9c2a5ab9dc322">
	<span class="Aac3db8b49e3f469c871ed9c2a5ab9dc321">PORTFOLIO MANAGER</span>
</div>
<div class="Aac3db8b49e3f469c871ed9c2a5ab9dc324">
	<span class="Aac3db8b49e3f469c871ed9c2a5ab9dc323">&nbsp;</span>
</div>
<div class="Aac3db8b49e3f469c871ed9c2a5ab9dc326">
	<span class="Aac3db8b49e3f469c871ed9c2a5ab9dc325">Sympatia Financie, o.c.p.,  a.s.</span>
</div>
<div class="Aac3db8b49e3f469c871ed9c2a5ab9dc328">
	<span class="Aac3db8b49e3f469c871ed9c2a5ab9dc327">Vajnorska 21 A, 83103 Bratislava, Slovakia</span>
</div>
<div class="Aac3db8b49e3f469c871ed9c2a5ab9dc330">
	<span class="Aac3db8b49e3f469c871ed9c2a5ab9dc329">www.sympatia.sk             <EMAIL></span>
</div>
<div class="Aac3db8b49e3f469c871ed9c2a5ab9dc332">
	<span class="Aac3db8b49e3f469c871ed9c2a5ab9dc331">T: +421 2 3263 0700        F: +421 2 3263 0799</span>
</div>
</div>
*/

?>

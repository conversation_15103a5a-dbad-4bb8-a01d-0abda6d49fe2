﻿<?php

	//$pdf = new TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);
	if(isset($pdf)) unset($pdf);
	$pdf = new MYPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);

	// set document information
	$pdf->SetCreator(PDF_CREATOR);
	$pdf->Set<PERSON>uthor("Sympatia Financie, o.c.p., a.s.");
	$pdf->SetTitle('Výpis');
	//$pdf->SetSubject('Predmet');
	//$pdf->SetKeywords('Nejake, slova');

	// set default header data
	$pdf->SetHeaderData(PDF_HEADER_LOGO, PDF_HEADER_LOGO_WIDTH, PDF_HEADER_TITLE, PDF_HEADER_STRING);

	// set header and footer fonts
	$pdf->setHeaderFont(Array('roboto', '', 8));
	$pdf->setFooterFont(Array('roboto', '', 8));

	// set default monospaced font
	//$pdf->SetDefaultMonospacedFont(PDF_FONT_MONOSPACED);

	// set margins
	//$pdf->SetMargins(PDF_MARGIN_LEFT, PDF_MARGIN_TOP, PDF_MARGIN_RIGHT);
	$pdf->SetTopMargin(40);
	$pdf->SetHeaderMargin(20);
	$pdf->SetFooterMargin(20);

	// set auto page breaks
	//$pdf->SetAutoPageBreak(TRUE, PDF_MARGIN_BOTTOM);
	$pdf->SetAutoPageBreak(TRUE, 40);

	// set image scale factor
	//$pdf->setImageScale(PDF_IMAGE_SCALE_RATIO);

	// set default header data
	// disable header/footer line
	//$pdf->setPrintHeader(false);
	//$pdf->setPrintFooter(false);
	// set password protection
	/*
	  The permission array is composed of values taken from the following ones (specify the ones you want to block):
	    - print : Print the document;
	    - modify : Modify the contents of the document by operations other than those controlled by 'fill-forms', 'extract' and 'assemble';
	    - copy : Copy or otherwise extract text and graphics from the document;
	    - annot-forms : Add or modify text annotations, fill in interactive form fields, and, if 'modify' is also set, create or modify interactive form fields (including signature fields);
	    - fill-forms : Fill in existing interactive form fields (including signature fields), even if 'annot-forms' is not specified;
	    - extract : Extract text and graphics (in support of accessibility to users with disabilities or for other purposes);
	    - assemble : Assemble the document (insert, rotate, or delete pages and create bookmarks or thumbnail images), even if 'modify' is not set;
	    - print-high : Print the document to a representation from which a faithful digital copy of the PDF content could be generated. When this is not set, printing is limited to a low-level representation of the appearance, possibly of degraded quality.
	    - owner : (inverted logic - only for public-key) when set permits change of encryption and enables all other permissions.

	 If you don't set any password, the document will open as usual.
	 If you set a user password, the PDF viewer will ask for it before displaying the document.
	 The master (owner) password, if different from the user one, can be used to get full document access.

	 Possible encryption modes are:
	     0 = RSA 40 bit
	     1 = RSA 128 bit
	     2 = AES 128 bit
	     3 = AES 256 bit
	*/

	//$pdf->SetProtection(array('modify','copy','annot-forms','fill-forms','extract','assemble'),'aaa53g54jh567',null,3);
	//$pdf->SetProtection(array('modify','copy','annot-forms','fill-forms','extract','assemble'),null,null,3);
	//$pdf->SetProtection(array(''),null,null,3);

	// set font
	// treba nejaky font co obsahuje ľščťžýáíéúäôňď	
//	$pdf->SetFont('dejavusans', '', 10);
//	$pdf->SetFont('gtwalsheimpro', '', 10);
	$pdf->SetFont('roboto', '', 10);

	// add a page
	$pdf->AddPage();
		
		
?>
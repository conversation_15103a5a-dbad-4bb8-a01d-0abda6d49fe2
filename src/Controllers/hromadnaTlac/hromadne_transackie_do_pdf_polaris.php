<?php
/*
 *  Maintainer: Miso
 *
 * Description:testovanie hoci<PERSON>ho 
 *
 */



require("lib/dbconnect.php");
require("lib/functions.php");
require("lib/controls.php");
require_once('tcpdf/tcpdf.php');
require($ROOT . 'lib/phpmailer/Exception.php');
require($ROOT . 'lib/phpmailer/PHPMailer.php');
require($ROOT . 'lib/phpmailer/SMTP.php');

$dba=new DB_SQL;

//error_reporting(E_ALL);

if(!isset($showit))
{
	$showit='';
}
if ($showit=="spusti")
{	
	// hlavicka vypisov
	$hlavicka1=iconv("WINDOWS-1250", "UTF-8",'Sympatia Financie o.c.p., a.s.');
	$hlavicka2=iconv("WINDOWS-1250", "UTF-8",'Vajnorsk� 21 A, 831 03');
	$hlavicka3=iconv("WINDOWS-1250", "UTF-8",'Bratislava');
	
	// tato premenna nastavuje ci ma byt na prvej strane v paticke cislovanie stran, zobrazuje cislovanie na poradovej strane vyssej ako je dane cislo
	$zobrazPatickuPage1=0;	//upraveny aj subor tcpdf/tcpdf.php na riadku 3591 a 3592
	$zobrazHlavickuPage1=0;
	
	
	include("mypdf_polaris_class.php");
		
	$db=new	DB_Sql;
	$db2=new DB_Sql;
	$db3=new DB_Sql;

	set_time_limit(3600);
	
	if(!isset($fromdate) or $fromdate == '')
	{
		if(substr($todate,3,2)<=3)
		{
			$rok=substr($todate,6,4)-1;			
		}
		else
		{
			$rok=substr($todate,6,4);
		}
		if(fmod(substr($todate,3,2),3)==0)
		{
			$fromdate=date_create_from_format('d.m.Y',"01.".sprintf("%'.02d",(floor(substr($todate,3,2)/3)-1)*3+1).".".$rok);
		}
		else
		{
			$fromdate=date_create_from_format('d.m.Y',"01.".sprintf("%'.02d",floor(substr($todate,3,2)/3)*3+1).".".$rok);
		}

		$odpocet=DateInterval::createFromDateString('-1 day');
		$fromdate=date_add($fromdate,$odpocet);
		$fromdate=date_format($fromdate,'d.m.Y');
	}	
		
	$fromdateread=$fromdate;
	$todateread=$todate;
	
	//kontrola, ci boli nove vkldy po skonceni kvartalu aby som doticnych klientov dal urobit az do tohto datumu
	$query="
		select  max(dealdate) as datum,klient from tbpodiely
		where dealdate>@datum and dealdate<=dateadd(day,20,@datum) and pocet>0
		group by klient	
	";	
	
	/*$mssql_data = sqlsrv_query($dbMssql,$query);
	while($mssql_riadok = sqlsrv_fetch_array($mssql_data,SQLSRV_FETCH_ASSOC))
	{
		//to treba nastavit zmenu datumu pre vybranych klientov a neskor sa urobi vypis ostatnych k povodnemu datumu
		//toto rozdelenie je potrebne pre klientov ktory robili nove vklady, aby sa im na vypise zobrazili,
		//pre starsich klientov bez novych vkladov je dobre im dat vypis z katumu NAV,
	//pre vyberajucich je dobre dat im poslednu hodnotu ich investicie pred vyberom (dane a pod.)
		//$todate 
	}*/
	
	$fromdateread=$fromdate;
	$todateread=$todate;	
	
	/*
	while($mssql_riadok = sqlsrv_fetch_array($mssql_data,SQLSRV_FETCH_ASSOC))
				{
					$querypostgre=$querypostgre."('".$mssql_riadok[PrimaryKey]."','".$mssql_riadok[Klient]."','".$mssql_riadok[DruhPodielu]."','".$mssql_riadok[Datum]."','".$mssql_riadok[Suma]."','".
	*/

	$query="select * from (
				select 	m.subjektid as fondid,
						case when po.meno is null then po.prieznaz else po.meno||' '||po.prieznaz end as klient,
						f.fondnameall,p.cislozmluvy,po.rcico,f.refmena,po.podielnikid_polaris
				from majetoktotal m
				left join portfolio p on m.subjektid=p.fondid
				left join podielnik po on p.podielnikid=po.podielnikid	
				left join fonds f on p.fondid=f.fondid
				where m.datum>to_date('$fromdate','dd.mm.yyyy') and m.datum<=to_date('$todate','dd.mm.yyyy') and m.subjektid>1
				group by m.subjektid,case when po.meno is null then po.prieznaz else po.meno||' '||po.prieznaz end,f.fondnameall,p.cislozmluvy,po.rcico,f.refmena,
							po.podielnikid_polaris
			) a
			where ROWNUM<2
			order by a.fondid";
	$dba->query($query);

	if ($fromdate!="")  $fromdate=toDBDate($fromdate);
	if ($todate!="") $todate=toDBDate($todate);
	
	while ($dba->next_record())
	{		
		
		$fondid=$dba->f('fondid');
		$cislozmluvy=$dba->f('cislozmluvy');
		$klient=$dba->f('klient');
		$klientdb=str_replace(" ","_",$klient);
		$rodne_cislo=$dba->f('rcico');
		$refmena=$dba->f("refmena");
		$polarisid=$dba->f("podielnikid_polaris");
		$typ_portfolia=$dba->f("fondnameall");
		$cub="";
		$ucetaktiva="";
		$k1ucet="";
		$sumaAlertBottom="";
		
		//-------------------generovanie html kodu polaris vypisu----------------------
		
		include("polarisPrvaStrana.php");	
		echo $vypis_obsah;
		include("polarisDruhaStrana.php");
		//$vypis_obsah="";

		if($sumaAlertBottom=="")		//kvoli kontrole chyb v goldmanne by bolo dobre zjednotit vypisy z goldmannu a polarisu do jedneho automatu
		{	/*				
			$dir = "c:/vypisy/podladatumu/".$todate."/";
			if (!file_exists($dir)) mkdir($dir, 0777, true);
						
			//generovanie heslovaneho pdf do spolocneho adresara aby sa to z neho dalo lahko naraz kopirovat na mailovu kampan
			$myfile = $dir . "Prehlad_Transakcii_".$klientdb."_".$cislozmluvy."_k_".$todateread.".pdf";

			include("pdfko_heslovane.php");
			$pdf->writeHTML($vypis_polaris, true, false, true, false, '');	
			$pdf->AddPage();
			$pdf->setPage($pdf->getPage());  			
					
			$pdf->Output($myfile, 'F');	
			*/
			$dir = "c:/vypisy/".$klientdb."/";			
			if (!file_exists($dir)) mkdir($dir, 0777, true);	
			//file_put_contents( "\\\\DC1\\GroupShares\\Trading\\Traderi\\Miso\\vypisy\\".$klientdb.".txt",$vypis_transakcii . $vypis_portfolia);
			/*
			//generovanie heslovaneho pdf
			$myfile = $dir . "Prehlad_Transakcii_".$klientdb."_".$cislozmluvy."_k_".$todateread.".pdf";

			include("pdfko_heslovane.php");
			$pdf->writeHTML($vypis_polaris, true, false, true, false, '');
			$pdf->AddPage();
			$pdf->setPage($pdf->getPage());  			
			
			$pdf->Output($myfile, 'F');	
			*/
			//generovanie neheslovaneho pdf
			$myfile = $dir . "Prehlad_Transakcii_".$klientdb."_".$cislozmluvy."_k_".$todateread."_neheslovane_polaris.pdf";
						
			include("pdfko_polaris_neheslovane.php");	
			$pdf->SetFont('gtwalsheimpro', '', 10);
			$pdf->writeHTML($vypis_polaris_str1, true, false, true, false, '');
			//pecat
			$image_file = 'obrazky/pecat.jpg';
			$pdf->Image($image_file, 155, 32,'', 40, '', '', 'T', false, 300, '', false, false, 0, false, false, false);
			
			//---------------skusit umiestnit gradient pod girlandu do jedneho obrazku-----------
			
			//gradient
			$image_file = 'obrazky/gradient.jpg';
			$pdf->Image($image_file, 145, 83,'', 45, '', '', 'T', false, 300, '', false, false, 0, false, false, false);
			//girlanda
				/*
				//maska
			$image_file = 'obrazky/girlanda3_maska.png';
			$mask = $pdf->Image($image_file, 15, 60, 180, '', '', '', '', false, 300, '', true);
				//maskovany obrazok
			$image_file = 'obrazky/girlanda4.png';
			$pdf->Image($image_file, 15, 160, 180, '', '', '', '', false, 300, '', false, $mask);			
				*/
			$image_file = 'obrazky/girlanda5.png';
			$pdf->Image($image_file, 15, 60,180, '', '', '', 'T', false, 300);
			$pdf->AddPage();
			$pdf->setPage($pdf->getPage()); 
			$pdf->writeHTML($vypis_polaris_str2, true, false, true, false, '');
			$pdf->SetDisplayMode('fullpage','OneColumn','UseNone');
			$pdf->Output($myfile, 'F');	
			//$pdf->Output('Local Expedition', 'I');
		}
		else
		{			
			echo PosliMail('<EMAIL>',"Neplatna zostava transakcii",iconv("UTF-8","WINDOWS-1250", $vypis_obsah));	
		}		
	}	
	//toto kopiruje vypisy z goldmann servera C disku na sietovy M disk	
	//je to presunute sem aby to nebezalo stale v cykle
	//exec("robocopy c:\\vypisy\\ \\\DC1\\GroupShares\\Backoffice\\Klienti\\ /E");
	//exec("rmdir /s /q c:\\vypisy");
	
	unset($hlavicka1);
	unset($hlavicka2);
	unset($hlavicka3);
	
echo '<script>alert("Vypisy su ulozene")</script>';
}
?>
<html>
<head>
	<!--<meta http-equiv="Content-Type" content="text/html; charset=<?php //echo CHARSET ?>">-->
	<meta http-equiv="Content-Type" content="text/html">
	
	<link rel="stylesheet" type="text/css" href="../style/menu.css">
	<script src="../lib/js_functions.php" language="javascript">
	</script>
	<script language="Javascript">
	var pobockaObj=new Array();
	
		
	function ShowReport()
	{
		
			if(IsDate(null,document.forms['data']['todate'].value,null))
			{								
				if(checkPrintDateValidity(document.forms['data']['todate'].value,'new'))
				{
					document.forms['data']['showit'].value='spusti';
					document.forms['data'].submit();
				}
			}			
		
		
	}

	
	</script>
</head>
<body bgcolor="<?php //echo $bgcolor; ?>" topmargin="2" leftmargin="5" rightmargin="0">

<form name="data" method="post">
	<input type="hidden" name="showit" value="">
	<span id="spdate">		
		<input id="todate" class="clsDate" name="todate" type="text" maxlength="10" value="1.9.2023" onblur="OnBlurDate(this,true);">
		<input disabled name="btn_ok" type="button" class="butonik_login" value="OK" onclick="javascript:ShowReport()">
	</span>
</form>


<script language="javascript">
	centerWindow(parent.window,900,500);	
		document.forms['data']['btn_ok'].disabled=false;
</script>
</body>
</html>



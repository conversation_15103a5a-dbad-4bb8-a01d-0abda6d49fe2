<?php
/*require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
require_once('/home/<USER>/www/lib/tcpdf/tcpdf.php');
require('/home/<USER>/www/lib/phpmailer/Exception.php');
require('/home/<USER>/www/lib/phpmailer/PHPMailer.php');
require('/home/<USER>/www/lib/phpmailer/SMTP.php');

$fromdate = $_POST['fromdate'];
$todate = $_POST['todate'];
$klient = json_decode($_POST['klient']);

$fondid_add = "subjektid in ($klient->fondid) ";
$query = "SELECT *
FROM (SELECT CASE
                 WHEN z_td < to_date('$fromdate', 'YYYY-MM-DD') THEN 1
                 WHEN k_td > to_date('$todate', 'YYYY-MM-DD') THEN 3
                 ELSE 2 END                  AS trieda,
             k.z_td                          AS zaciatok,
             k.k_td                          AS koniec,
             k.sum_td                        AS suma,
             k.ir_td                         AS sadzba,
             k.iv_b                          AS brutto,
             k.iv_n                          AS netto_old,
             k.iv_n                          AS netto,
             k.mena,
             k.suma_dane                     AS dan,
             k.datvysporiadaniarealntd       AS vysporiadanie,
             k.cutd,
             k.subjektid,
             z_td,
             k_td,
             p.nazovpartnera                 AS miesto,
             k.datum_cas_obchodu             AS datum_cas,
             (SELECT typ_pokynu
              FROM typy_pokynov tp
              WHERE tp.typid = k.typ_pokynu) AS typPokynu,
             ''                              AS detailKTV
      FROM konfirmaciaktv k
               JOIN partner p ON k.partnerid = p.partnerid
      WHERE (
          (
              z_td >= to_date('2023-01-01', 'YYYY-MM-DD')
                  AND z_td <= to_date('2024-05-01', 'YYYY-MM-DD')
              )
              OR (
              k_td >= to_date('2023-01-01', 'YYYY-MM-DD')
                  AND k_td <= to_date('2024-05-01', 'YYYY-MM-DD')
              )
          )
        AND logactivityid IN (17, 25)
        AND k.$fondid_add
      UNION ALL
      SELECT CASE
                 WHEN z_td < to_date('2023-01-01', 'YYYY-MM-DD') THEN 1
                 WHEN k_td > to_date('2024-05-01', 'YYYY-MM-DD') THEN 3
                 ELSE 2 END                                              AS trieda,
             k.z_td                                                      AS zaciatok,
             k.k_td                                                      AS koniec,
             pdr.transsumareal                                           AS suma,
             k.ir_td                                                     AS sadzba,
             pdr.auvreal                                                 AS brutto,
             k.iv_n * pdr.transsumareal / (SELECT sum(x.transsumareal)
                                           FROM pooldetailreal x
                                           WHERE x.poolid = po.poolid)   AS netto_old,
             (pdr.auvreal - pdr.dan)                                     AS netto,
             k.mena,
             pdr.dan                                                     AS dan,
             k.datvysporiadaniarealntd                                   AS vysporiadanie,
             k.cutd,
             k.subjektid,
             z_td,
             k_td,
             p.nazovpartnera                                             AS miesto,
             k.datum_cas_obchodu                                         AS datum_cas,
             (SELECT typ_pokynu
              FROM typy_pokynov tp
              WHERE tp.typid = k.typ_pokynu)                             AS typPokynu,
             CASE WHEN sum_td_pov IS NULL THEN '' ELSE 'uzavreteSKK' END AS detailKTV
      FROM konfirmaciaktv k
               JOIN pool po ON k.dealid = po.dealid
               JOIN pooldetailreal pdr ON pdr.poolid = po.poolid
          AND pdr.subjektid in (1752)
               JOIN partner p ON k.partnerid = p.partnerid
      WHERE (
          (
              z_td >= to_date('$fromdate', 'YYYY-MM-DD')
                  AND z_td <= to_date('$todate', 'YYYY-MM-DD')
              )
              OR (
              k_td >= to_date('$fromdate', 'YYYY-MM-DD')
                  AND k_td <= to_date('$todate', 'YYYY-MM-DD')
              )
          )
        AND k.subjektid = 0
        AND k.logactivityid IN (17, 25)
      UNION ALL
      SELECT CASE
                 WHEN z_td < to_date('$fromdate', 'YYYY-MM-DD') THEN 1
                 WHEN k_td > to_date('$todate', 'YYYY-MM-DD') THEN 3
                 ELSE 2 END                  AS trieda,
             k.z_td                          AS zaciatok,
             k.k_td                          AS koniec,
             pdr.transsumareal_pov           AS suma,
             k.ir_td                         AS sadzba,
             0                               AS brutto,
             0                               AS netto_old,
             0                               AS netto,
             'SKK'                           AS mena,
             0                               AS dan,
             k.datvysporiadaniarealntd       AS vysporiadanie,
             k.cutd,
             k.subjektid,
             z_td,
             k_td,
             p.nazovpartnera                 AS miesto,
             k.datum_cas_obchodu             AS datum_cas,
             (SELECT typ_pokynu
              FROM typy_pokynov tp
              WHERE tp.typid = k.typ_pokynu) AS typPokynu,
             'vyplateneEUR'                  AS detailKTV
      FROM konfirmaciaktv k
               JOIN pool po ON k.dealid = po.dealid
               JOIN pooldetailreal pdr ON pdr.poolid = po.poolid
          AND pdr.$fondid_add
               JOIN partner p ON k.partnerid = p.partnerid
      WHERE (
          (
              z_td >= to_date('$fromdate', 'YYYY-MM-DD')
                  AND z_td <= to_date('$todate', 'YYYY-MM-DD')
              )
              OR (
              k_td >= to_date('$fromdate', 'YYYY-MM-DD')
                  AND k_td <= to_date('$todate', 'YYYY-MM-DD')
              )
          )
        AND k.subjektid = 0
        AND k.logactivityid IN (17, 25)
        AND k.sum_td_pov IS NOT NULL -- ktv po konverzii preratane do EUR )
     ) result
ORDER BY mena, trieda, zaciatok, koniec;
";

$terminovaneVklady = Connection::getDataFromDatabase($query, defaultDB);
$terminovaneVklady = $terminovaneVklady[1];

echo "<pre>";
print_r($terminovaneVklady[0]);
include("/home/<USER>/www/src/Controllers/hromadnaTlac/vypis_transakcii.php");
echo $vypis_obsah;
echo "/<pre>";


/*$fondid = $klient->fondid;
$cislozmluvy = $klient->cislozmluvy;
$klient = $klient->klient;
$klientdb = str_replace(" ", "_", $klient);
$vypisy_heslo = $klient->vypisy_heslo;
$refmena = $klient->refmena;
$polarisid = $klient->podielnikid_polaris;
$typ_portfolia = $klient->fondnameall;
$fnshort = $klient->fondnameshort;
$cub = "";
$ucetaktiva = "";
$k1ucet = "";
$sumaAlertBottom = "";

$farbaciara1 = 'black';    //tenka ciara pod hlavickou tabulky
$farbaciara2 = '#1782c5';    //nazov tabulky
$farbaciara3 = '#DCE2EA';    //hruba ciara okolo tabulky


//'#1782c5' - modra
//'#DCE2EA' - oranzova

//-------------------generovanie html kodu vypisu transakcii----------------------
include("vypis_transakcii.php");
$vypis_obsah = "";
include("vypis_portfolia.php");
$vypis_obsah = "";
include("vypis_zhodnotenie.php");
$vypis_obsah = "";
//echo $vypis_transakcii."<br><br>";
//file_put_contents('c:/vypisy/test.txt',$vypis_transakcii.":::::",FILE_APPEND);

if ($sumaAlertBottom == "") {

    $dir = "/home/<USER>/www/vypisy/" . $todate;
    //$dir = "c:/vypisy/01-Summary/01_vypisy_podladatumu/" . $todate . "/";
    if (!file_exists($dir)) mkdir($dir, 0777, true);

    //generovanie heslovaneho pdf do spolocneho adresara aby sa to z neho dalo lahko naraz kopirovat na mailovu kampan
    $myfile = $dir . "V�pis_portf�lia_" . $klientdb . "_" . $cislozmluvy . "_od_" . '$fromdate'read . "_do_" . $todateread . ".pdf";

    include("pdfko_heslovane.php");
    $image_file = 'obrazky/raster1.jpg';
    $pdf->Image($image_file, 10, 75.5, 190, '', 'JPG', '', 'T', false, 300, '', false, false, 0, false, false, false);
    $pdf->SetXY(0, 40);
    $pdf->writeHTML($vypis_zhodnotenie, true, false, true, false, '');
    $pdf->AddPage();
    $pdf->setPage($pdf->getPage());
    $pdf->Image($image_file, 10, 75.5, 190, '', 'JPG', '', 'T', false, 300, '', false, false, 0, false, false, false);
    $pdf->SetXY(0, 40);
    $pdf->writeHTML($vypis_portfolia, true, false, true, false, '');
    $pdf->AddPage();
    $pdf->setPage($pdf->getPage());
    $pdf->Image($image_file, 10, 75.5, 190, '', 'JPG', '', 'T', false, 300, '', false, false, 0, false, false, false);
    $pdf->SetXY(0, 40);
    $pdf->writeHTML($vypis_transakcii, true, false, true, false, '');
    $pdf->SetDisplayMode('fullpage', 'OneColumn', 'UseNone');
    $pdf->Output($myfile, 'F');
    $pdf = null;

    $dir = "/home/<USER>/www/vypisy/" . $klientdb . "/Vypisy/";
    //$dir = "c:/vypisy/" . $klientdb . "/Vypisy/";
    if (!file_exists($dir)) mkdir($dir, 0777, true);
    //file_put_contents( "\\\\DC1\\GroupShares\\Trading\\Traderi\\Miso\\vypisy\\".$klientdb.".txt",$vypis_transakcii . $vypis_portfolia);

    //generovanie heslovaneho pdf
    $myfile = $dir . "V�pis_portf�lia_" . $klientdb . "_" . $cislozmluvy . "_od_" . '$fromdate'read . "_do_" . $todateread . ".pdf";

    include("pdfko_heslovane.php");
    $image_file = 'obrazky/raster1.jpg';
    $pdf->Image($image_file, 10, 75.5, 190, '', 'JPG', '', 'T', false, 300, '', false, false, 0, false, false, false);
    $pdf->SetXY(0, 40);
    $pdf->writeHTML($vypis_zhodnotenie, true, false, true, false, '');
    $pdf->AddPage();
    $pdf->setPage($pdf->getPage());
    $pdf->Image($image_file, 10, 75.5, 190, '', 'JPG', '', 'T', false, 300, '', false, false, 0, false, false, false);
    $pdf->SetXY(0, 40);
    $pdf->writeHTML($vypis_portfolia, true, false, true, false, '');
    $pdf->AddPage();
    $pdf->setPage($pdf->getPage());
    $pdf->Image($image_file, 10, 75.5, 190, '', 'JPG', '', 'T', false, 300, '', false, false, 0, false, false, false);
    $pdf->SetXY(0, 40);
    $pdf->writeHTML($vypis_transakcii, true, false, true, false, '');
    $pdf->SetDisplayMode('fullpage', 'OneColumn', 'UseNone');
    $pdf->Output($myfile, 'F');
    $pdf = null;


    //generovanie neheslovaneho pdf
    $myfile = $dir . "V�pis_portf�lia_" . $klientdb . "_" . $cislozmluvy . "_od_" . '$fromdate'read . "_do_" . $todateread . "_neheslovane.pdf";
    include("pdfko_neheslovane.php");
    $image_file = '/home/<USER>/www/src/assets/img/raster1.jpg';
    $pdf->Image($image_file, 10, 75.5, 190, '', 'JPG', '', 'T', false, 300, '', false, false, 0, false, false, false);
    $pdf->SetXY(0, 40);
    $pdf->writeHTML($vypis_zhodnotenie, true, false, true, false, '');
    $pdf->AddPage();
    $pdf->setPage($pdf->getPage());
    $pdf->Image($image_file, 10, 75.5, 190, '', 'JPG', '', 'T', false, 300, '', false, false, 0, false, false, false);
    $pdf->SetXY(0, 40);
    $pdf->writeHTML($vypis_portfolia, true, false, true, false, '');
    $pdf->AddPage();
    $pdf->setPage($pdf->getPage());
    $pdf->Image($image_file, 10, 75.5, 190, '', 'JPG', '', 'T', false, 300, '', false, false, 0, false, false, false);
    $pdf->SetXY(0, 40);
    $pdf->writeHTML($vypis_transakcii, true, false, true, false, '');
    $pdf->SetDisplayMode('fullpage', 'OneColumn', 'UseNone');
    $pdf->Output($myfile, 'F');
    //$pdf->Output('Local Expedition', 'I');
    $pdf = null;

} else {
    echo PosliMail('<EMAIL>', "Neplatna zostava transakcii - " . $cislozmluvy, iconv("UTF-8", "UTF-8", $vypis_transakcii));
}*/
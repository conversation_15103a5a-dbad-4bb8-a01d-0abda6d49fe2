<?php

	class MYPDF extends TCPDF {

		//Page header
		public function Header() {
			// Logo
			global $hlavicka1,	$hlavicka2,	$hlavicka3;
			
			//$image_file = 'logo_SF.jpg';
			//$this->Image($image_file, 10, 10, 45, '', 'JPG', '', 'T', false, 300, '', false, false, 0, false, false, false);
			
//			$image_file = 'obrazky/hlavicka4.jpg';
//			$this->Image($image_file, 3, 3,210 , '', 'JPG', '', 'T', false, 300, '', false, false, 0, false, false, false);

			$image_file = 'obrazky/hlavicka4_1.png';
			$this->Image($image_file, 3, 3,70 , '', 'PNG', '', 'T', false, 300, '', false, false, 0, false, false, false);
			$image_file = 'obrazky/hlavicka4_2.jpg';
			$this->Image($image_file, 91, 3,122 , '', 'JPG', '', 'T', false, 300, '', false, false, 0, false, false, false);

			/*$this->SetFont('robotocondensed', 'B',6);		
			$this->Cell(110, 5, '', 0, false, 'R', 0, '', 0, false, 'M', 'M');			
			$this->Cell(45, 5, $hlavicka1, 0, false, 'L', 0, '', 0, false, 'M', 'M');		
			$this->LN(3);	
			$this->Cell(155, 5, '', 0, false, 'R', 0, '', 0, false, 'M', 'M');
			$this->Cell(45, 5, $hlavicka2, 0, false, 'L', 0, '', 0, false, 'M', 'M');	
			$this->LN(3);	
			$this->Cell(155, 5, '', 0, false, 'R', 0, '', 0, false, 'M', 'M');
			$this->Cell(45, 5, $hlavicka3, 0, false, 'L', 0, '', 0, false, 'M', 'M');
			$this->LN(3);*/				
		}

		// Page footer
		public function Footer() {
			global $zobrazPatickuPage1;
			// Position at 15 mm from bottom
			$image_file = 'obrazky/paticka5.png';
			$this->Image($image_file, 3, 264,210 , '', 'PNG', '', 'T', false, 300, '', false, false, 0, false, false, false);
			$this->SetY(-14.3);
			// Set font
			$this->SetFont('robotocondensed', 'I', 8);
			// Page number
			//$this->Cell(0, 10, ($this->PageNo()-$zobrazPatickuPage1), 0, false, 'C', 0, '', 0, false, 'T', 'M');
			$this->Cell(100, 10, '', 0, false, 'R', 0, '', 0, false, 'M', 'M');
			$this->Cell(30, 10, '- '.$this->getAliasNumPage().'/'.$this->getAliasNbPages().' -', 0, false, 'C', 0, '', 0, false, 'T', 'M');
		}
	}

?>

<?php	
$db=new	DB_Sql;
$db3=new DB_Sql;

	$id_fond = $fondid;
		
	$dbdate=$todate;
	$tms_dbdate = toTimeStamp($todate);
	
	$testhodnota = 0;	
		
	$vypis_obsah = '	
		<br><br><br>		
		<table style="width:100%;border-collapse:collapse;">
			<tr  style="width:100%;text-align:center;">
				<td style="height:26.25pt;">
					<div style="color: #6B819B;">
						<span style="font-size: 21pt;font-weight:400;">V�pis o stave majetku</span>
					</div>	
				</td>
			</tr>
			<tr style="width:100%;">
				<td style="height:20pt;min-width:100%;width:100%;"></td>
			</tr>	
			<tr  style="width:100%;text-align:center;">
				<td style="height:26.25pt;vertical-align:bottom;">
					<div  style="color: #000;vertical-align:bottom;">
						<span style="font-size: 9.25pt;font-weight:600;"><b>ku d�u '. $todateread .'</b></span>
					</div>	
				</td>
			</tr>
		</table>
		<br><br>
		<table>
			<tr style="font-weight:normal;color: #0E0E0E;">
				<td style="min-width:4%;width:4%;"></td>
				<td style="vertical-align:bottom;width:71%;text-align:left;height:10pt;"><div style="vertical-align:bottom;"><span style="font-weight:400;font-size:7.5pt;">Klient:    </span><span style="font-weight:700;font-size:9.75pt;">'.$klient.'</span></div></td>
				<td style="vertical-align:bottom;width:20%;text-align:right;height:10pt;"><div style="vertical-align:bottom;"><span style="font-weight:400;font-size:7.5pt;">Portf�lio:    </span><span style="font-weight:700;font-size:9.75pt;">'.$cislozmluvy.'</span></div></td>
				<td style="min-width:5%;width:5%;"></td>
			</tr>
		</table>
	';
/*
	
		<br><br><br>
			<table style="width:100%;border-collapse:collapse;">
				<tr style="width:100%;text-align:center;">
					<td>
						<h1>V�pis o stave majetku<br>ku d�u '.$todateread.'</h1>
					</td>
				</tr>				
			</table>
			<br><br><br><br>
			<table >
				<tr style="font-weight:normal;">
					<td style="width:20%;">Klient:</td>
					<td style="width:80%;">'.$klient.'</td>
				</tr>
				<tr style="font-weight:normal;">
					<td style="width:20%;">Portf�lio:</td>
					<td style="width:80%;">'.$cislozmluvy.'</td>
				</tr>
			</table>
	';*/
		
	//---------------hotovost a terminovane vklady---------------
	$query="
		SELECT 
			sum( 
				(sumadenom*sign(0.5-md_d) 
				  + COALESCE(
							(
								select sum(sumadenom*sign(0.5-md_d)) 
								from majetoktotal 
								where datum=mt.datum  and uctovnykod in (315132,315113) and eqid = mt.eqid and subjektid = mt.subjektid
								and kodaktiva = mt.kodaktiva and ucetaktiva = mt.ucetaktiva
							),0
						)
				) * f_menovy_kurz_lot(menadenom,'$refmena',datum) 
			) as suma 
		from  
			majetoktotal mt
		LEFT JOIN dbequitycurrric dcr ON dcr.isincurrric = mt.kodaktiva
		LEFT JOIN dbequitycurr dc ON dc.isincurr = dcr.isincurr
		LEFT JOIN dbequity d ON d.isin = dc.isin
		WHERE datum = to_date('$dbdate', 'YYYY-MM-DD')
		  AND uctovnykod in (221110, 221210) 
		  AND mt.eqid in ('BU', 'TD') 
		  AND subjektid = $id_fond
	";		 
	
	$db->query($query);
	$db->next_record();
	$p1= $db->f('suma');	//sumaPreTypAktiva("'BU','TD'", '221110,221210','315132,315113');
	$suma_p1=fromDBNumber($p1,2); 
	
	//---------------dlhopisy spolu---------------
	$query="
		SELECT 
			sum( (sumadenom*sign(0.5-md_d)) * f_menovy_kurz_lot(menadenom,'$refmena',datum) 
			) as suma 
		FROM 
            majetoktotal mt
            LEFT JOIN dbequitycurrric dcr ON dcr.isincurrric = mt.kodaktiva
            LEFT JOIN dbequitycurr dc ON dc.isincurr = dcr.isincurr
            LEFT JOIN dbequity d ON d.isin = dc.isin
        WHERE 
            datum = to_date('$dbdate', 'YYYY-MM-DD') 
            AND uctovnykod in (251110, 251120) 
            AND mt.eqid = 'Bonds' 
            AND subjektid = $id_fond;
	";		 
	
	$db->query($query);
	$db->next_record();
	$d1= $db->f('suma');	//sumaPreTypAktiva("'Bonds'", '251110,251120');
	$suma_d1=fromDBNumber($d1,2);
	
	//---------------akcie---------------
	$query="
		SELECT 
				sum( (sumadenom*sign(0.5-md_d)) *f_menovy_kurz_lot(menadenom,'$refmena',datum,1) 
				) as suma 
			from 
				majetoktotal mt, dbequitycurrric dcr, dbequitycurr dc, dbequity d
			where 
				datum=to_date('$dbdate','YYYY-MM-DD') and dcr.isincurrric (+)= mt.kodaktiva and dc.isincurr (+)= dcr.isincurr and
				d.isin (+)= dc.isin and
				uctovnykod in (251200) and
				d.druheqid not in (8,15,17) and 
				mt.eqid in ('Shares') and
				subjektid=$id_fond
	";		 
	
	$db->query($query);
	$db->next_record();
	$a1=$db->f('suma');	//sumaPreTypAktiva("'Shares'", '251200');
	$suma_a1=fromDBNumber($a1,2);
	
	//---------------podiely spolu---------------
	$query="
			SELECT 
				sum( (sumadenom*sign(0.5-md_d)) * f_menovy_kurz_lot(menadenom,'$refmena',datum) 
				) as suma 
			FROM 
				majetoktotal mt
				LEFT JOIN dbequitycurrric dcr ON dcr.isincurrric = mt.kodaktiva
				LEFT JOIN dbequitycurr dc ON dc.isincurr = dcr.isincurr
				LEFT JOIN dbequity d ON d.isin = dc.isin
			WHERE 
				datum = to_date('$dbdate', 'YYYY-MM-DD')
				AND uctovnykod in (251300, 251200)
				AND (d.eqid = 'Fonds' OR (d.druheqid in (8, 15, 17) AND d.eqid = 'Shares'))
				AND mt.eqid in ('Fonds', 'Shares')
				AND subjektid = $id_fond;
		";		 
		
	$db->query($query);
	$db->next_record();	
	$f1=$db->f('suma');	//	sumaPreTypAktiva("'Fonds','Shares'", '251300,251200');
	$suma_f1=fromDBNumber($f1,2);
	
	//---------------pohladavky a zavazky spolu---------------
	$query="
			SELECT 
				sum((sumadenom*sign(0.5-md_d) 
					   - COALESCE(
								(
									select sum(sumadenom*sign(0.5-md_d)) 
									from majetoktotal 
									where datum=mt.datum and uctovnykod in (315132,315113) and eqid = mt.eqid and subjektid = mt.subjektid
									and kodaktiva = mt.kodaktiva and ucetaktiva = mt.ucetaktiva
									and exists
									(
										select * 
										from majetoktotal 
										where datum=mt.datum and uctovnykod in (221110,221210) and eqid = mt.eqid and subjektid = mt.subjektid
											and kodaktiva = mt.kodaktiva and ucetaktiva = mt.ucetaktiva
									)
								),0
							)
					) * f_menovy_kurz_lot(menadenom,'$refmena',datum)
				) as suma 
			from 
				majetoktotal mt
			left join dbequitycurrric dcr on dcr.isincurrric = mt.kodaktiva
			left join dbequitycurr dc on dc.isincurr = dcr.isincurr
			left join dbequity d on d.isin = dc.isin
			where datum = to_date('$dbdate', 'YYYY-MM-DD')
			  and uctovnykod in (221110, 221210) 
			  and mt.eqid in ('BU', 'TD') 
			  and subjektid = $fondid;
		";
		 
	$db->query($query);
	$db->next_record();
	$paz1=$db->f('suma'); //sumaPreTypAktiva("'Fonds','Bonds','Shares','TD','BU'", "(select uctovnykod from navuctovanie where uctovnykod like '325%' or uctovnykod like '315%' or uctovnykod like '261%')",'315132,315113','-','221110,221210');
	$suma_paz1=fromDBNumber($paz1,2);
	
	$total=$p1 + $d1 + $a1 + $f1 + $paz1;
	$total_suma=fromDBNumber($total,2);

	if($total==0) $total=1;		//tu je mozna chyba, ak bude mat klient rovnaku sumu plus aj minus tak mu to vypocita obravky percentualny podieln na portfoliu

	$p2=($p1/$total) * 100;
	$podiel_p2=fromDBNumber($p2,2);
	$d2=($d1/$total) * 100;
	$podiel_d2=fromDBNumber($d2,2);
	$a2=($a1/$total) * 100;
	$podiel_a2=fromDBNumber($a2,2);
	$f2=($f1/$total) * 100;
	$podiel_f2=fromDBNumber($f2,2);
	$paz2=($paz1/$total) * 100;
	$podiel_paz2=fromDBNumber($paz2,2);
	
	$vypis_obsah= $vypis_obsah.'
		<br><br><br><br>
		<table style="width:100%;border-collapse:collapse;">
			<tr>
				<td>
					<h3 style="color:'.$farbaciara2.'">�trukt�ra portf�lia pod�a tried akt�v</h3>
				</td>
			</tr>
		</table>	
		<br><br>
		<table style="width:100%;border-collapse:collapse;border-top:1pt solid '.$farbaciara3.';border-bottom:1pt solid '.$farbaciara3.';">			
				<tr>
					<th style="width:60%;text-align:left;font-size:8;" ><b>Trieda akt�v</b></th>
					<th style="width:20%;text-align:right;font-size:8;" ><b>Objem v '. $refmena .'</b></th>
					<th style="width:20%;text-align:right;font-size:8;" ><b>Podiel v %</b></th>
				</tr>
				<tr style="width:100%;line-height:1pt;">
					<td colspan="3" style="border-bottom:0.1mm solid '.$farbaciara1.';height:1pt;min-width:100%;width:100%;"></td>
				</tr>
				<tr style="width:100%;line-height:1pt;">
					<td colspan="3" style="height:1pt;min-width:100%;width:100%;"></td>
				</tr>
	';
		 
	if($suma_p1!=0)
	{
		$vypis_obsah= $vypis_obsah.'
				<tr>
					<td style="width:60%;text-align:left;font-size:8;">Pe�a�n� prostriedky</td>
					<td style="width:20%;text-align:right;font-size:8;">'. $suma_p1 .'</td>
					<td style="width:20%;text-align:right;font-size:8;">'. $podiel_p2 .'</td>
				</tr>
		';
	}
	if($suma_d1!=0)
	{
		$vypis_obsah= $vypis_obsah.'
				<tr>
					<td style="width:60%;text-align:left;font-size:8;">Dlhopisy</td>
					<td style="width:20%;text-align:right;font-size:8;">'. $suma_d1 .'</td>
					<td style="width:20%;text-align:right;font-size:8;">'. $podiel_d2 .'</td>					
				</tr>
		';
	}
	if($suma_a1!=0)
	{
		$vypis_obsah= $vypis_obsah.'
				<tr>
					<td style="width:60%;text-align:left;font-size:8;">Akcie, ADR a GDR</td>
					<td style="width:20%;text-align:right;font-size:8;">'. $suma_a1 .'</td>
					<td style="width:20%;text-align:right;font-size:8;">'. $podiel_a2 .'</td>					
				</tr>
		';
	}
	if($suma_f1!=0)
	{
		$vypis_obsah= $vypis_obsah.'
				<tr>
					<td style="width:60%;text-align:left;font-size:8;">Podielov� listy a ETF</td>
					<td style="width:20%;text-align:right;font-size:8;">'. $suma_f1 .'</td>
					<td style="width:20%;text-align:right;font-size:8;">'. $podiel_f2 .'</td>					
				</tr>
		';
	}
	if($suma_paz1!=0)
	{
		$vypis_obsah= $vypis_obsah.'
				<tr>
					<td style="width:60%;text-align:left;font-size:8;">Z�v�zky a poh�ad�vky</td>
					<td style="width:20%;text-align:right;font-size:8;">'. $suma_paz1 .'</td>
					<td style="width:20%;text-align:right;font-size:8;">'. $podiel_paz2 .'</td>					
				</tr>
		';
	}
	
	$vypis_obsah= $vypis_obsah.'
				<tr>
					<td style="width:60%;text-align:left;font-size:8;"><b>Trhov� hodnota celkom</b></td>
					<td style="width:20%;text-align:right;font-size:8;"><b>'. $total_suma .'</b></td>
					<td style="width:20%;text-align:right;font-size:8;"><b>100,00</b></td>					
				</tr>
				<tr style="width:100%;line-height:1pt;">
					<td colspan="3" style="height:1pt;min-width:100%;width:100%;"></td>
				</tr>
		</table>
	';
	
	//---------------hotovost a terminovane vklady detail---------------
	$query="
		SELECT 		
			mt.ucetaktiva as ucetaktiva,  subjektid, eqid,  menadenom, sumadenom*sign(0.5-md_d) as sumadenom, 			
			f_menovy_kurz_kriz(menadenom,'$refmena',to_date('$dbdate','YYYY-MM-DD')) as kurz, 
			sumadenom*sign(0.5-md_d)*f_menovy_kurz_lot(menadenom,'$refmena',to_date('$dbdate','YYYY-MM-DD')) as sumaref,
			COALESCE(
					(select sumadenom*sign(0.5-md_d) from majetoktotal where 
						subjektid=mt.subjektid
						and datum=mt.datum
						and uctovnykod in (315132,315113)
						and eqid = mt.eqid
						and kodaktiva = mt.kodaktiva
						and ucetaktiva = mt.ucetaktiva
					 ),0
				) as auv, 
			COALESCE(
					(select sumadenom*sign(0.5-md_d)*f_menovy_kurz_lot(mt.menadenom,'$refmena',to_date('$dbdate','YYYY-MM-DD'),1) from majetoktotal where 
						subjektid=mt.subjektid
						and datum=mt.datum
						and uctovnykod in (315132,315113)
						and eqid = mt.eqid
						and kodaktiva = mt.kodaktiva
						and ucetaktiva = mt.ucetaktiva
					),0
				) as auvref 
		from 
			majetoktotal mt
		where 
			datum=to_date('$dbdate','YYYY-MM-DD') and eqid in ('BU','TD') and uctovnykod in (221110, 221210)
			and subjektid=$id_fond
		order by eqid, menadenom
	";	
	
	$db->query($query);	
	
	$cnt=0;
	while($db->next_record()) 
	{		
		if($cnt==0)
		{
			$vypis_obsah= $vypis_obsah.'
				<br><br><br>
				<table style="width:100%;border-collapse:collapse;">					
					<tr>
						<td>
							<h3 style="color:'.$farbaciara2.'">Peňa�n� prostriedky</h3>
						</td>
					</tr>
				</table>
				<br><br>
				<table style="width:100%;border-collapse:collapse;border-top:1pt solid '.$farbaciara3.';border-bottom:1pt solid '.$farbaciara3.';">
					<tr>
							<th style="width:14%;text-align:left;font-size:8;"><b>Typ ��tu</b></th>
							<th style="width:6%;text-align:right;font-size:8;"><b>Mena</b></th>
							<th style="width:10%;text-align:right;font-size:8;"><b>D�tum<BR>zriadenia TV</b></th>
							<th style="width:10%;text-align:right;font-size:8;"><b>D�tum<BR>splatnosti TV</b></th>
							<th style="width:13%;text-align:right;font-size:8;"><b>Objem</b></th>
							<th style="width:10%;text-align:center;font-size:8;"><b>AUV</b></th>
							<th style="width:8%;text-align:right;font-size:8;"><b>�rokov�<BR>sadzba</b></th>
							<th style="width:7%;text-align:right;font-size:8;"><b>Kurz</b></th>
							<th style="width:13%;text-align:right;font-size:8;"><b>Objem<br>v '. $refmena.'</b></th>
							<th style="width:9%;text-align:right;font-size:8;"><b>Podiel<br>v %</b></th>
					</tr>
					<tr style="width:100%;line-height:1pt;">
						<td colspan="10" style="border-bottom:0.1mm solid '.$farbaciara1.';height:1pt;min-width:100%;width:100%;"></td>
					</tr>
					<tr style="width:100%;line-height:1pt;">
						<td colspan="10" style="height:1pt;min-width:100%;width:100%;"></td>
					</tr>
			';		
		}		
		$cnt++;
		
		if($db->f("eqid")=='BU'){
			if(substr($db->f("ucetaktiva"),-9)=='kolateral'){
				$typ='Bežný účet - kolaterál';
			}else{
				$typ='Bežný účet';
			}	
		}
		else $typ='Term�novan� vklad';
		
		$obj=$db->f("sumaref")+$db->f("auvref");
		$auv=fromDBNumber($db->f("auv"),2);
		$p=($obj/$total) * 100;
		$podiel=fromDBNumber($p,2);
		$mena=$db->f("menadenom");
		$objem=fromDBNumber($db->f("sumadenom"),2);
		$objem_ref=fromDBNumber($obj,2);
		$menovykurz = fromDBNumber($db->f("kurz"),4);
		$subjektid = $db->f("subjektid");
		$ucetaktiva = $db->f("ucetaktiva");

		$z_td = "";
		$k_td = "";
		$ir_td = "";
		if ($typ == 'Term�novan� vklad') {	
			$query3 = " select z_td, k_td, ir_td 
						from konfirmaciaktv 
						where 
							subjektid = $subjektid  and Z_TD <= to_date('$dbdate','YYYY-MM-DD')						
							and K_TD >= to_date('$dbdate','YYYY-MM-DD')  and CUTD = '$ucetaktiva' and rownum = 1
			";
			
			$db3->query($query3);
			if (!$db3->next_record())		// ak sme nic nenasli, pozreme este cez pool
			{
				$query3 = "	select z_td, k_td, ir_td 
							from konfirmaciaktv k, pool p, pooldetailreal pdr 
							where 
								k.subjektid = 0  and k.Z_TD <= to_date('$dbdate','YYYY-MM-DD') and k.K_TD >= to_date('$dbdate','YYYY-MM-DD') 
								and k.CUTD = '$ucetaktiva' and k.dealid = p.dealid and pdr.poolid = p.poolid and pdr.subjektid = $subjektid and rownum = 1
				";	
				$db3->query($query3);
				$db3->next_record();
			}
			$z_td = gmdate(L_DATEFORMATZERO,fromDBDate($db3->f("z_td")));
			$k_td = gmdate(L_DATEFORMATZERO,fromDBDate($db3->f("k_td")));
			$ir_td = $db3->f("ir_td");
			$ir_td = fromDBNumber($ir_td,2);
		}	
	
		$vypis_obsah= $vypis_obsah.'
			<tr >
				<td style="width:14%;text-align:left;font-size:8;">'. $typ .'</td>
				<td style="width:6%;text-align:right;font-size:8;">'. $mena .'</td>
				<td style="width:10%;text-align:right;font-size:8;">'. $z_td .'</td>
				<td style="width:10%;text-align:right;font-size:8;">'. $k_td .'</td>
				<td style="width:13%;text-align:right;font-size:8;">'. $objem .'</td>
				<td style="width:10%;text-align:center;font-size:8;">'. $auv .'</td>
				<td style="width:8%;text-align:right;font-size:8;">'. $ir_td .'</td>
				<td style="width:7%;text-align:right;font-size:8;">'. $menovykurz .'</td>
				<td style="width:13%;text-align:right;font-size:8;">'. $objem_ref .'</td>
				<td style="width:9%;text-align:right;font-size:8;">'. $podiel .'</td>
			</tr>
		';
	}
	
	if($cnt!=0)
	{	
		$vypis_obsah= $vypis_obsah.'
				<tr>
					<td style="width:14%;text-align:left;font-size:8;"><b>Spolu</b></td>
					<td style="width:6%;text-align:right;font-size:8;"></td>
					<td style="width:10%;text-align:right;font-size:8;"></td>
					<td style="width:10%;text-align:right;font-size:8;"></td>
					<td style="width:13%;text-align:right;font-size:8;"></td>
					<td style="width:10%;text-align:right;font-size:8;"></td>
					<td style="width:8%;text-align:right;font-size:8;"></td>
					<td style="width:7%;text-align:right;font-size:8;"></td>
					<td style="width:13%;text-align:right;font-size:8;"><b>'. $suma_p1 .'</b></td>
					<td style="width:9%;text-align:right;font-size:8;"><b>'. $podiel_p2 .'</b></td>
				</tr>
				<tr style="width:100%;line-height:1pt;">
					<td colspan="10" style="height:1pt;min-width:100%;width:100%;"></td>
				</tr>				
			</table>
		';
	}
		
	//---------------dlhopisy detail---------------
	$query="
		select 
				pkg_conversion.getCPNominal(d.isin,to_date('$dbdate','YYYY-MM-DD')) as nominalemisie,	
				d.cpnaz, menadenom as mena, d.isinreal as isinreal, d.isin, 
				d.maturitydate as datsplatnosti, 
				d.kupon, pocty.pocet, pocty.objemsauv, pocty.objemref, pocty.kurz, pocty.auv, pocty.kurz_mena
		from (
			select 	sum(case when md_d=0 then pocet else (-1)*pocet end) as pocet, 					
					max(kurzaktiva) as kurz, 
					sum((select sum(case when mt2.md_d=0 then mt2.sumadenom else (-1)*mt2.sumadenom end)
						from majetoktotal mt2 where mt.ucetaktiva=mt2.ucetaktiva and
						mt2.KODAKTIVA=mt.kodaktiva and mt.datum=mt2.datum and mt.subjektid=mt2.subjektid and
						mt2.uctovnykod in (251120) )
						) as auv,
					sum((select sum(case when mt2.md_d=0 then mt2.sumadenom else (-1)*mt2.sumadenom end)
						from majetoktotal mt2 where mt.ucetaktiva=mt2.ucetaktiva and
						mt2.KODAKTIVA=mt.kodaktiva and mt.datum=mt2.datum and mt.subjektid=mt2.subjektid and
						mt2.uctovnykod in (251110) )
						) as objemsauv,
					sum((select sum((case when mt2.md_d=0 then mt2.sumadenom else (-1)*mt2.sumadenom end) * 
						f_menovy_kurz_lot(menadenom, '$refmena', mt2.datum, 1))
						from majetoktotal mt2 where mt.ucetaktiva=mt2.ucetaktiva and
						mt2.KODAKTIVA=mt.kodaktiva and mt.datum=mt2.datum and mt.subjektid=mt2.subjektid and
						mt2.uctovnykod in (251110, 251120) )
						) as objemref,
					kodaktiva,  menadenom, 
					max(f_menovy_kurz_kriz(menadenom, '$refmena', mt.datum, 1)) as kurz_mena
			from 
				majetoktotal mt
			where 
				mt.datum=to_date('$dbdate','YYYY-MM-DD') and mt.eqid in ('Bonds') and mt.uctovnykod in (251110) and	mt.subjektid in ($id_fond)
			GROUP BY mt.kodaktiva,menadenom
		) pocty, dbequity d, dbequitycurr c, dbequitycurrric r
		where pocty.kodaktiva=r.isincurrric and r.isincurr=c.isincurr and d.isin=c.isin 
		ORDER BY d.isinreal
	";	
	$db->query($query);
	
	$cnt=0;
	while($db->next_record()) 
	{		
		if($cnt==0)
		{
			$vypis_obsah= $vypis_obsah.'
				<br><br><br>
				<table style="width:100%;border-collapse:collapse;">					
					<tr>
						<td>
							<h3 style="color:'.$farbaciara2.'">Dlhopisy</h3>
						</td>
					</tr>
				</table>
				<br><br>
				<table style="width:100%;border-collapse:collapse;border-top:1pt solid '.$farbaciara3.';border-bottom:1pt solid '.$farbaciara3.';">
					<tr>
							<th style="width:13%;text-align:left;font-size:8;"><b>N�zov</b></th>
							<th style="width:13%;text-align:left;font-size:8;"><b>ISIN</b></th>
							<th style="width:7%;text-align:right;font-size:8;"><b>Po�et <BR> kusov</b></th>
							<th style="width:9%;text-align:right;font-size:8;"><b>Aktu�l-<BR>na cena</b></th>
							<th style="width:13%;text-align:right;font-size:8;"><b>Objem</b></th>
							<th style="width:6%;text-align:right;font-size:8;"><b>Mena</b></th>
							<th style="width:10%;text-align:right;font-size:8;"><b>AUV</b></th>
							<th style="width:7%;text-align:right;font-size:8;"><b>Kurz</b></th>
							<th style="width:13%;text-align:right;font-size:8;"><b>Objem s AUV<br>v '. $refmena .'</b></th>
							<th style="width:9%;text-align:right;font-size:8;"><b>Podiel<br>v %</b></th>
					</tr>
					<tr style="width:100%;line-height:1pt;">
						<td colspan="10" style="border-bottom:0.1mm solid '.$farbaciara1.';height:1pt;min-width:100%;width:100%;"></td>
					</tr>
					<tr style="width:100%;line-height:1pt;">
						<td colspan="10" style="height:1pt;min-width:100%;width:100%;"></td>
					</tr>
			';			
		}
		$cnt++;
		
		$nazov=$db->f("cpnaz");
		$mena=$db->f("mena");
		$kurz=$db->f("kurz");
		$kurz_mena=$db->f("kurz_mena");
		$isinreal=$db->f("isinreal");
		//echo $isinreal;
		$pocet=$db->f("pocet");
		$auv=fromDBNumber($db->f("auv"),2);
		$objemsauv=fromDBNumber($db->f("objemsauv"),2);//bez AUV
		$obj=$db->f("objemref");
		$p=($obj/$total) * 100;
		$podiel=fromDBNumber($p,2);
		$objemref=fromDBNumber($db->f("objemref"),2);

		$vypis_obsah= $vypis_obsah.'
			<tr>
				<td style="width:13%;text-align:left;font-size:8;font-weight:700;">'. $nazov .'</td>
				<td style="width:13%;text-align:left;font-size:8;">'. $isinreal .'</td>
				<td style="width:7%;text-align:right;font-size:8;">'. $pocet .'</td>
				<td style="width:9%;text-align:right;font-size:8;">'. fromDBNumber($kurz, 2) .'</td>
				<td style="width:13%;text-align:right;font-size:8;">'. $objemsauv .'</td>
				<td style="width:6%;text-align:right;font-size:8;">'. $mena .'</td>
				<td style="width:10%;text-align:right;font-size:8;">'. $auv .'</td> 
				<td style="width:7%;text-align:right;font-size:8;">'. fromDBNumber($kurz_mena, 4) .'</td>
				<td style="width:13%;text-align:right;font-size:8;">'. $objemref .'</td>
				<td style="width:9%;text-align:right;font-size:8;">'. $podiel .'</td>
			</tr>
		';
	}
	
	if($cnt!=0)
	{	
		$vypis_obsah= $vypis_obsah.'			
					<tr>
						<td style="width:13%;text-align:left;font-size:8;"><b>Spolu</b></td>
						<td style="width:13%;text-align:left;font-size:8;"></td>
						<td style="width:7%;text-align:right;font-size:8;"></td>
						<td style="width:9%;text-align:right;font-size:8;"></td>
						<td style="width:13%;text-align:rightfont-size:8;;"></td>
						<td style="width:6%;text-align:right;font-size:8;"></td>
						<td style="width:10%;text-align:right;font-size:8;"></td>
						<td style="width:7%;text-align:right;font-size:8;"></td>
						<td style="width:13%;text-align:right;font-size:8;"><b>'. $suma_d1 .'</b></td>
						<td style="width:9%;text-align:right;font-size:8;"><b>'. $podiel_d2 .'</b></td>
					</tr>
					<tr style="width:100%;line-height:1pt;">
						<td colspan="10" style="height:1pt;min-width:100%;width:100%;"></td>
					</tr>
			</table>
		';			
	}
	
	//---------------akcie detail---------------		
	$query="select d.cpnaz, menadenom as mena, d.isinreal, pocty.pocet, pocty.kurz,
					pocty.objem, pocty.objemref, pocty.kurz_mena				
			from (
				SELECT sum(case when md_d=0 then pocet else (-1)*pocet end) as pocet, 
					sum(case when md_d=0 then sumadenom else (-1)*sumadenom end) as objem, 
					sum((case when md_d=0 then sumadenom else (-1)*sumadenom end) * 
						f_menovy_kurz_lot(menadenom, '$refmena', datum, 1)) as objemref,
					kodaktiva,menadenom,max(kurzaktiva) as kurz, max(f_menovy_kurz_kriz(menadenom, '$refmena', mt.datum, 1)) as kurz_mena
				from
					majetoktotal mt, dbequitycurrric dcr, dbequitycurr dc, dbequity d
				where
					mt.datum=to_date('$dbdate','YYYY-MM-DD') and mt.uctovnykod in (251200) and mt.subjektid=$id_fond and
					dcr.isincurrric = mt.kodaktiva and dc.isincurr = dcr.isincurr and d.isin = dc.isin and
					d.druheqid not in (8,15,17) -- neberieme tu ETF a index certif
				group by mt.kodaktiva, menadenom
			) pocty, dbequity d, dbequitycurr c, dbequitycurrric r
			where pocty.kodaktiva=r.isincurrric and d.isin=c.isin and r.isincurr=c.isincurr 
			ORDER BY d.isinreal
	";
	
	$db->query($query);
	$cnt=0;
	while($db->next_record()) 
	{
		if($cnt==0)
		{
			$vypis_obsah= $vypis_obsah.'
				<br><br><br>
				<table style="width:100%;border-collapse:collapse;">					
					<tr>
						<td>
							<h3 style="color:'.$farbaciara2.'">Akcie, ADR a GDR</h3>
						</td>
					</tr>
				</table>
				<br><br>
				<table style="width:100%;border-collapse:collapse;border-top:1pt solid '.$farbaciara3.';border-bottom:1pt solid '.$farbaciara3.';">					
					<tr>
						<th style="width:21%;text-align:left;font-size:8;"><b>N�zov</b></th>
						<th style="width:13%;text-align:left;font-size:8;"><b>ISIN</b></th>
						<th style="width:7%;text-align:right;font-size:8;"><b>Po�et<BR>kusov</b></th>
						<th style="width:9%;text-align:right;font-size:8;"><b>Aktu�lna<BR>cena</b></th>
						<th style="width:14%;text-align:right;font-size:8;"><b>Objem</b></th>
						<th style="width:6%;text-align:right;font-size:8;"><b>Mena</b></th>
						<th style="width:7%;text-align:right;font-size:8;"><b>Kurz</b></th>
						<th style="width:14%;text-align:right;font-size:8;"><b>Objem<BR>v '. $refmena.'</b></th>
						<th style="width:9%;text-align:right;font-size:8;"><b>Podiel<br>v %</b></th>
					</tr>
					<tr style="width:100%;line-height:1pt;">
						<td colspan="9" style="border-bottom:0.1mm solid '.$farbaciara1.';height:1pt;min-width:100%;width:100%;"></td>
					</tr>
					<tr style="width:100%;line-height:1pt;">
						<td colspan="9" style="height:1pt;min-width:100%;width:100%;"></td>
					</tr>
			';
		}
		$cnt++;
		
		$nazov=$db->f("cpnaz");
		$mena=$db->f("mena");
		$isinreal=$db->f("isinreal");
		$pocet=$db->f("pocet");
		$objem=$db->f("objem");
		$kurz=$db->f("kurz");
		$kurz_mena=$db->f("kurz_mena");
		$obj=$db->f("objemref");
		$p=($obj/$total) * 100;
		$podiel=fromDBNumber($p,2);
		$objem=fromDBNumber($db->f("objem"),2);
		$objemref=fromDBNumber($db->f("objemref"),2);

	
		$vypis_obsah= $vypis_obsah.'
			<tr>
				<td style="width:21%;text-align:left;font-size:8;font-weight:700;">'. $nazov .'</td>
				<td style="width:13%;text-align:left;font-size:8;">'. $isinreal .'</td>
				<td style="width:7%;text-align:right;font-size:8;">'. $pocet .'</td>
				<td style="width:9%;text-align:right;font-size:8;">'. fromDBNumber($kurz, 2) .'</td>
				<td style="width:14%;text-align:right;font-size:8;">'. $objem .'</td>
				<td style="width:6%;text-align:right;font-size:8;">'. $mena .'</td>
				<td style="width:7%;text-align:right;font-size:8;">'. fromDBNumber($kurz_mena, 4) .'</td>
				<td style="width:14%;text-align:right;font-size:8;">'. $objemref .'</td>
				<td style="width:9%;text-align:right;font-size:8;">'. $podiel .'</td>
			</tr>
		';
	}
	
	if($cnt!=0)
	{	
		$vypis_obsah= $vypis_obsah.'
			<tr bgcolor="">
					<td style="width:21%;text-align:left;font-size:8;"><b>Spolu</b></td>
					<td style="width:13%;text-align:left;font-size:8;"></td>
					<td style="width:6%;text-align:right;font-size:8;"></td>
					<td style="width:9%;text-align:right;font-size:8;"></td>
					<td style="width:8%;text-align:right;font-size:8;"></td>
					<td style="width:14%;text-align:right;font-size:8;"></td>
					<td style="width:7%;text-align:right;font-size:8;"></td>
					<td style="width:14%;text-align:right;font-size:8;"><b>'. $suma_a1 .'</b></td>
					<td style="width:9%;text-align:right;font-size:8;"><b>'. $podiel_a2 .'</b></td>
				</tr>
				<tr style="width:100%;line-height:1pt;">
					<td colspan="9" style="height:1pt;min-width:100%;width:100%;"></td>
				</tr>
			</table>
		';
	}
	
	//---------------podiely detail---------------	
	$query="	select d.cpnaz, menadenom as mena, d.isinreal, pocty.pocet, pocty.kurz,
						pocty.objem, pocty.objemref, pocty.kurz_mena
				 from (
					SELECT sum(case when md_d=0 then pocet else (-1)*pocet end) as pocet,
							sum(case when md_d=0 then sumadenom else (-1)*sumadenom end) as objem, 
							sum((case when md_d=0 then sumadenom else (-1)*sumadenom end) * 
								f_menovy_kurz_lot(menadenom, '$refmena', datum, 1)) as objemref,
							kodaktiva,menadenom, max(kurzaktiva) as kurz, max(f_menovy_kurz_lot(menadenom, '$refmena', mt.datum, 1)) as kurz_mena
					from
						majetoktotal mt, dbequitycurrric dcr, dbequitycurr dc, dbequity d
					where
						mt.datum=to_date('$dbdate','YYYY-MM-DD') and mt.uctovnykod in (251200,251300) and mt.subjektid=$id_fond and
						dcr.isincurrric = mt.kodaktiva and dc.isincurr = dcr.isincurr and d.isin = dc.isin and
						(d.eqid = 'Fonds' or (d.druheqid in (8,15,17) and d.eqid='Shares' ))
					group by mt.kodaktiva,mt.menadenom
				) pocty, dbequity d, dbequitycurr c, dbequitycurrric r
				where pocty.kodaktiva=r.isincurrric and d.isin=c.isin and r.isincurr=c.isincurr 
	";
	
	$db->query($query);
	
	$cnt=0;
	while($db->next_record()) 
	{		
		if($cnt==0)
		{
			$vypis_obsah= $vypis_obsah.'
				<br><br><br>
				<table style="width:100%;border-collapse:collapse;">
					<tr>
							<td>
								<h3 style="color:'.$farbaciara2.'">Podielov� listy a ETF</h3>
							</td>
					</tr>
				</table>
				<br><br>
				<table style="width:100%;border-collapse:collapse;border-top:1pt solid '.$farbaciara3.';border-bottom:1pt solid '.$farbaciara3.';">					
					<tr>
						<th style="width:21%;text-align:left;font-size:8;"><b>N�zov</b></th>
						<th style="width:13%;text-align:left;font-size:8;"><b>ISIN</b></th>
						<th style="width:7%;text-align:right;font-size:8;"><b>Po�et <BR> kusov</b></th>
						<th style="width:9%;text-align:right;font-size:8;"><b>Aktu�lna<BR>cena</b></th>
						<th style="width:14%;text-align:right;font-size:8;"><b>Objem</b></th>
						<th style="width:6%;text-align:right;font-size:8;"><b>Mena</b></th>
						<th style="width:7%;text-align:right;font-size:8;"><b>Kurz</b></th>
						<th style="width:14%;text-align:right;font-size:8;"><b>Objem<BR>v '. $refmena.'</b></th>
						<th style="width:9%;text-align:right;font-size:8;"><b>Podiel<br>v %</b></th>
					</tr>
					<tr style="width:100%;line-height:1pt;">
						<td colspan="9" style="border-bottom:0.1mm solid '.$farbaciara1.';height:1pt;min-width:100%;width:100%;"></td>
					</tr>
					<tr style="width:100%;line-height:1pt;">
						<td colspan="9" style="height:1pt;min-width:100%;width:100%;"></td>
					</tr>
			';
		}
		$cnt++;
	
		$nazov=$db->f("cpnaz");
		$mena=$db->f("mena");
		$isinreal=$db->f("isinreal");
		$pocet=$db->f("pocet");
		$kurz=$db->f("kurz");
		$kurz_mena=$db->f("kurz_mena");
		$objem=$db->f("objem");
		$obj=$db->f("objemref");
		$p=($obj/$total) * 100;
		$podiel=fromDBNumber($p,2);
		$objem=fromDBNumber($db->f("objem"),2);
		$objemref=fromDBNumber($db->f("objemref"),2);
	
		$vypis_obsah= $vypis_obsah.'	
			<tr>
				<td style="width:21%;text-align:left;font-size:8;font-weight:700;">'. $nazov .'</td>
				<td style="width:13%;text-align:left;font-size:8;">'. $isinreal .'</td>
				<td style="width:7%;text-align:right;font-size:8;">'. $pocet .'</td>
				<td style="width:9%;text-align:right;font-size:8;">'. fromDBNumber($kurz, 2) .'</td>
				<td style="width:14%;text-align:right;font-size:8;">'. $objem .'</td>
				<td style="width:6%;text-align:right;font-size:8;">'. $mena .'</td>
				<td style="width:7%;text-align:right;font-size:8;">'. fromDBNumber($kurz_mena, 4) .'</td>
				<td style="width:14%;text-align:right;font-size:8;">'. $objemref .'</td>
				<td style="width:9%;text-align:right;font-size:8;">'. $podiel .'</td>
			</tr>
		';
	}
	
	if($cnt!=0)
	{	
		$vypis_obsah= $vypis_obsah.'
			<tr>
					<td style="width:21%;text-align:left;font-size:8;"><b>Spolu</b></td>
					<td style="width:13%;text-align:left;font-size:8;"></td>
					<td style="width:7%;text-align:right;font-size:8;"></td>
					<td style="width:9%;text-align:right;font-size:8;"></td>
					<td style="width:14%;text-align:right;font-size:8;"></td>
					<td style="width:6%;text-align:right;font-size:8;"></td>
					<td style="width:7%;text-align:right;font-size:8;"></td>
					<td style="width:14%;text-align:right;font-size:8;"><b>'. $suma_f1 .'</b></td>
					<td style="width:9%;text-align:right;font-size:8;"><b>'. $podiel_f2 .'</b></td>
				</tr>
				<tr style="width:100%;line-height:1pt;">
					<td colspan="9" style="height:1pt;min-width:100%;width:100%;"></td>
				</tr>
			</table>
		';
	}
	
	//---------------pohladavky a zavazky detail---------------		
	$query="
		SELECT 
				n.popis,decode(mt.eqid,'BU','','TD','',mt.pocet) as pocet,
				mt.ucetaktiva, mt.subjektid,mt.eqid, menadenom, sumadenom*sign(0.5-md_d) as sumadenom, menaref, 
				f_menovy_kurz_kriz(menadenom,'$refmena',to_date('$dbdate','YYYY-MM-DD'),1) as kurz, 
				f_menovy_kurz_lot(menadenom,'$refmena',to_date('$dbdate','YYYY-MM-DD'),1) as kurz_lot,
				sumadenom*sign(0.5-md_d)*f_menovy_kurz_lot(menadenom,'$refmena',to_date('$dbdate','YYYY-MM-DD'),1) as sumaref,
				case  	when (mt.eqid in ('Bonds','Fonds','Shares','Depo') or mt.uctovnykod in(315601,315602,315603,315604,315605,315606,315607,315608,315609)) then d.cpnaz
						else mt.kodaktiva end as kodaktiva,
				nvl(
					( case when mt.uctovnykod in (315121,325121)
								then (	select sum(sign(0.5-md_d)*mt2.sumadenom)
										from majetoktotal mt2 
										where 
											mt.ucetaktiva = mt2.ucetaktiva and mt.kodaktiva =  mt2.kodaktiva  and  mt.datum = mt2.datum and 
											mt.subjektid = mt2.subjektid and mt.uctovnykod+1 = mt2.uctovnykod and mt.eqid = mt2.eqid
									)
							else 0 end 
					),0) as auvdenom
		from 
			majetoktotal mt,  navuctovanie n,dbequity d
		where 
			datum=to_date('$dbdate','YYYY-MM-DD') and mt.uctovnykod = n.uctovnykod
			and (n.uctovnykod like '315%' or n.uctovnykod like '325%' or n.uctovnykod like '261%')
			and d.isin(+) =  substr(mt.kodaktiva,0,12) and (mt.uctovnykod not in (315122,325122) ) -- auv je zaratane k dlhopisu
			and not exists(
					select * 
					from majetoktotal 
					where datum=mt.datum  and uctovnykod in (221110,221210) 
						and mt.uctovnykod in (select uctovnykod from navuctovanie where uctovnykod like '325%' or uctovnykod like '315%' or uctovnykod like '261%')
						and eqid = mt.eqid and subjektid = mt.subjektid and kodaktiva = mt.kodaktiva and ucetaktiva = mt.ucetaktiva
				)
			and subjektid=$id_fond
		order by n.popis, menadenom
	";	
		
	$db->query($query);
	
	$cnt=0;
	while($db->next_record()) 
	{	
		if($cnt==0)
		{
			$vypis_obsah= $vypis_obsah.'
				<br><br><br>
				<table style="width:100%;border-collapse:collapse;">
					<tr>
						<td>
							<h3 style="color:'.$farbaciara2.'">Z�v�zky a poh�ad�vky</h3>
						</td>
					</tr>
				</table>
				<br><br>
				<table style="width:100%;border-collapse:collapse;border-top:1pt solid '.$farbaciara3.';border-bottom:1pt solid '.$farbaciara3.';">
					
						<tr>
							<th style="width:15%;text-align:left;font-size:8;"><b>N�zov</b></th>
							<th style="width:20%;text-align:left;font-size:8;"><b>N�zov CP</b></th>
							<th style="width:14%;text-align:right;font-size:8;"><b>Objem</b></th>
							<th style="width:8%;text-align:right;font-size:8;"><b>Mena</b></th>
							<th style="width:10%;text-align:right;font-size:8;"><b>Po�et</b></th>
							<th style="width:9%;text-align:right;font-size:8;"><b>Kurz</b></th>
							<th style="width:14%;text-align:right;font-size:8;"><b>Objem v '. $refmena.'</b></th>
							<th style="width:10%;text-align:right;font-size:8;"><b>Podiel v %</b></th>
						</tr>
					<tr style="width:100%;line-height:1pt;">
						<td colspan="8" style="border-bottom:0.1mm solid '.$farbaciara1.';height:1pt;min-width:100%;width:100%;"></td>
					</tr>
					<tr style="width:100%;line-height:1pt;">
						<td colspan="8" style="height:1pt;min-width:100%;width:100%;"></td>
					</tr>
			';
		}
		$cnt++;
		
		$popis = $db->f("popis");
		$obj=$db->f("sumaref") + $db->f("auvdenom")*$db->f("kurz_lot");
		$paz=($obj/$total) * 100;
		$podiel=fromDBNumber($paz,2);
		$mena=$db->f("menadenom");
		$objem=fromDBNumber($db->f("sumadenom")+$db->f("auvdenom"),2);
		$objem_ref=fromDBNumber($obj,2);
		$menovykurz = fromDBNumber($db->f("kurz"),4);
		$subjektid = $db->f("subjektid");
		$ucetaktiva = $db->f("ucetaktiva");
		$pocet = $db->f("pocet");
		if ($pocet!='') $pocet = fromDBNumber($pocet,0);
		$kodaktiva = $db->f('kodaktiva');

		$vypis_obsah= $vypis_obsah.'	
			<tr>
				<td style="width:15%;text-align:left;font-size:8;">'. $popis .'</td>
				<td style="width:20%;text-align:left;font-size:8;">'. $kodaktiva .'</td>
				<td style="width:14%;text-align:right;font-size:8;">'. $objem .'</td>
				<td style="width:8%;text-align:right;font-size:8;">'. $mena .'</td>
				<td style="width:10%;text-align:right;font-size:8;">'. $pocet .'</td>
				<td style="width:9%;text-align:right;font-size:8;">'. $menovykurz .'</td>
				<td style="width:14%;text-align:right;font-size:8;">'. $objem_ref .'</td>
				<td style="width:10%;text-align:right;font-size:8;">'. $podiel .'</td>
			</tr>
		';
	}
	
	if($cnt!=0)
	{	
		$vypis_obsah= $vypis_obsah.'
					<tr>
						<td style="width:15%;text-align:left;font-size:8;"><b>Spolu</b></td>
						<td style="width:20%;text-align:left;font-size:8;"></td>
						<td style="width:14%;text-align:right;font-size:8;"></td>
						<td style="width:9%;text-align:right;font-size:8;"></td>
						<td style="width:10%;text-align:right;font-size:8;"></td>
						<td style="width:9%;text-align:right;font-size:8;"></td>
						<td style="width:14%;text-align:right;font-size:8;"><b>'. $suma_paz1 .'</b></td>
						<td style="width:9%;text-align:right;font-size:8;"><b>'. $podiel_paz2 .'</b></td>
					</tr>
					<tr style="width:100%;line-height:1pt;">
						<td colspan="8" style="height:1pt;min-width:100%;width:100%;"></td>
					</tr>
			</table>
		';
	}
	
	//---------------O�ak�van� finan�n� toky---------------	

	$activity = "12,15,18";

	$query = "
		select  sum(round(
						decode(md_d,0,md_d0,1,md_d1)*
						pkg_conversion.getCPNominal(d.isin,m.datum)*
						m.pocet*		
						f.faktor*
						(f.kupon/100)*
						f_koef_auv_isincurrric(m.kodaktiva,f.datesplatnost,2)
						,NVL(d.rounding,25)
					)
				) as urok,
				0 as istina, to_char(f.datesplatnost,'dd.mm.yyyy') as mydatum, f.datesplatnost, d.cpnaz, m.menadenom,
				min(case when (po.typ_zdanenia = 'FO') then NVL(d.sadzbafo, 0)
						when (po.typ_zdanenia = 'PO') then NVL(d.sadzbapo, 0)
						when (po.typ_zdanenia = 'NO') then NVL(d.sadzbano, 0)			
						else 0 end
				) as dan,
				'kupon' as typ, m.subjektid, p.cislozmluvy
		from
			majetoktotal m, floatkupon f, dbequity d, dbequitycurr dc, dbequitycurrric dcr, navuctovanie n,
			portfolio p,  podielnik po
		where
			m.datum=to_date('$dbdate','YYYY-MM-DD') and m.uctovnykod in (315124) and m.subjektid in ($id_fond) and
			f.isincurrric = m.kodaktiva and f.datesplatnost > m.datum and dcr.isincurrric = m.kodaktiva and
			dc.isincurr = dcr.isincurr and d.isin = dc.isin and m.uctovnykod = n.uctovnykod and	
			p.fondid = m.subjektid  and  (is_excoupon(m.kodaktiva,m.datum) = 1) and ((f.datesplatnost-nvl(d.exfrekkup,0)) <= m.datum)  
			and (is_coupon_date(m.kodaktiva,m.datum) = 0) and p.PODIELNIKID = po.PODIELNIKID 
			and f.datesplatnost<to_date((substr('2023-09-01',1,3)||(substr('2023-09-01',4,1)+1)||substr('2023-09-01',5)),'YYYY-MM-DD')
		group by
			m.kodaktiva, f.datesplatnost, d.cpnaz, m.menadenom, d.dan, m.subjektid, p.cislozmluvy, po.typ_zdanenia
		  
		union all
		 
		select
				sum(round(
						decode(md_d,0,md_d0,1,md_d1)*
						pkg_conversion.getCPNominal(d.isin,m.datum)*
						m.pocet*		
						f.faktor*
						(f.kupon/100)*
						f_koef_auv_isincurrric(m.kodaktiva,f.datesplatnost,2)
						,NVL(d.rounding,25)
					)
				) as urok,
				0 as istina, to_char(f.datesplatnost,'dd.mm.yyyy') as mydatum,
				f.datesplatnost, d.cpnaz, m.menadenom,
				min(case when (po.typ_zdanenia = 'FO') then NVL(d.sadzbafo, 0)
						when (po.typ_zdanenia = 'PO') then NVL(d.sadzbapo, 0)
						when (po.typ_zdanenia = 'NO') then NVL(d.sadzbano, 0)			
						else 0 end
				) as dan,
				'kupon' as typ, m.subjektid, p.cislozmluvy
		from
			majetoktotal m, floatkupon f, dbequity d, dbequitycurr dc, dbequitycurrric dcr, navuctovanie n, portfolio p, podielnik po
		where
			m.datum=to_date('$dbdate','YYYY-MM-DD') and m.uctovnykod in (251110,325121,315121) and m.subjektid in ($id_fond) and
			f.isincurrric = m.kodaktiva and f.datesplatnost > m.datum and dcr.isincurrric = m.kodaktiva and
			dc.isincurr = dcr.isincurr and d.isin = dc.isin and m.uctovnykod = n.uctovnykod and	
			p.fondid = m.subjektid  and not ((is_excoupon(m.kodaktiva,m.datum) = 1) and ((f.datesplatnost-nvl(d.exfrekkup,0)) <= m.datum) 
			and (is_coupon_date(m.kodaktiva,m.datum) = 0)) and p.PODIELNIKID = po.PODIELNIKID
			and f.datesplatnost<to_date((substr('2023-09-01',1,3)||(substr('2023-09-01',4,1)+1)||substr('2023-09-01',5)),'YYYY-MM-DD')
		group by
			m.kodaktiva, f.datesplatnost, d.cpnaz, m.menadenom, d.dan, m.subjektid, p.cislozmluvy, po.typ_zdanenia

		union all

		select	
				0 as urok, round(
								decode(md_d,0,md_d0,1,md_d1)*
								pkg_conversion.getCPNominal(d.isin,m.datum)*
								( case 	when is_existina(m.kodaktiva,m.datum) = 1
											then nvl((select pocet from majetoktotal m2 where m2.datum=m.datum and m2.subjektid=m.subjektid and m2.kodaktiva = m.kodaktiva and m2.uctovnykod=315123),0)
										else m.pocet end)*
								f_istina(m.kodaktiva,f.datesplatnost)/100
								,NVL(d.rounding,25)
							) as istina,
				to_char(f.datesplatnost,'dd.mm.yyyy') as mydatum, f.datesplatnost, d.cpnaz, m.menadenom,
				d.dan, 'istina' as typ, m.subjektid, p.cislozmluvy
		from
			majetoktotal m, floatkupon f, dbequity d, dbequitycurr dc, dbequitycurrric dcr, navuctovanie n, portfolio p
		where
			m.datum=to_date('$dbdate','YYYY-MM-DD') and m.uctovnykod in (251110,325121,315121) and m.subjektid in ($id_fond) and
			f.isincurrric = m.kodaktiva and f.datesplatnost > m.datum and dcr.isincurrric = m.kodaktiva and
			dc.isincurr = dcr.isincurr and d.isin = dc.isin and f.istina != 0 and
			m.uctovnykod = n.uctovnykod and	 p.fondid = m.subjektid and f.datesplatnost<to_date((substr('2023-09-01',1,3)||(substr('2023-09-01',4,1)+1)||substr('2023-09-01',5)),'YYYY-MM-DD')
		order by 
			datesplatnost, subjektid, cpnaz
	";

	$db->query($query);
	
	$cnt=0;
	while($db->next_record()) 
	{
		if($cnt==0)
		{
			$vypis_obsah= $vypis_obsah.'
				<br><br><br>
				<table style="width:100%;border-collapse:collapse;">
					<tr >
						<td>
							<h3 style="color:'.$farbaciara2.'">O�ak�van� finan�n� toky ku d�u '. $showdate .'</h3>
						</td>
					</tr>
				</table>
				<br><br>
				<table style="width:100%;border-collapse:collapse;border-top:1pt solid '.$farbaciara3.';border-bottom:1pt solid '.$farbaciara3.';">						
					<tr>
						<td style="width:15%;text-align:left;font-size:8;"><b>D�tum</b></td>
						<td style="width:35%;text-align:left;font-size:8;"><b>Investi�n� n�stroj</b></td>
						<td style="width:9%;text-align:right;font-size:8;"><b>Mena</b></td> 
						<td style="width:14%;text-align:right;font-size:8;"><b>Istina</b></td>
						<td style="width:14%;text-align:right;font-size:8;"><b>Kup�n</b></td>
						<td style="width:13%;text-align:right;font-size:8;"><b>Da�</b></td>
					</tr>
					<tr style="width:100%;line-height:1pt;">
						<td colspan="6" style="border-bottom:0.1mm solid '.$farbaciara1.';height:1pt;min-width:100%;width:100%;"></td>
					</tr>
					<tr style="width:100%;line-height:1pt;">
						<td colspan="6" style="height:1pt;min-width:100%;width:100%;"></td>
					</tr>
			';
		}
		$cnt++;
			
		$datum=$db->f("mydatum");
		$nastroj=$db->f("cpnaz");
		$mena=$db->f("menadenom");
		$dan=$db->f("dan");
		$urok=$db->f("urok");
		$istina=$db->f("istina");
		$dan = $urok/100 * $dan;
		$istina=fromDBNumber($istina,2);
		$urok=fromDBNumber($urok,2);
		$dan=fromDBNumber($dan,2);
		
		$vypis_obsah= $vypis_obsah.'
				<tr >
					<td style="width:15%;text-align:left;font-size:8;">'. $datum .'</td>
					<td style="width:35%;text-align:left;font-size:8;">'. $nastroj .'</td>
					<td style="width:9%;text-align:right;font-size:8;">'. $mena .'</td>
					<td style="width:14%;text-align:right;font-size:8;">'. $istina .'</td>
					<td style="width:14%;text-align:right;font-size:8;">'. $urok .'</td>
					<td style="width:13%;text-align:right;font-size:8;">'. $dan .'</td>
				</tr>
		';	
	}
	
	if($cnt!=0)
	{	
		$vypis_obsah= $vypis_obsah.'
				
				<tr style="width:100%;line-height:1pt;">
					<td colspan="6" style="height:1pt;min-width:100%;width:100%;"></td>
				</tr>
			</table>
		';
	}	
	
	//echo $vypis_obsah;
	$vypis_portfolia = iconv("WINDOWS-1250", "UTF-8", $vypis_obsah);
	//$vypis_portfolia = str_replace("<td></td>","<td> </td>",$vypis_portfolia);
	//$vypis_portfolia = str_replace("<th></th>","<th> </th>",$vypis_portfolia);
	$vypis_obsah='';
	
?>
	

<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

define('PROBLEM_INFO', 0);
define('PROBLEM_WARNING', 1);
define('PROBLEM_SERIOUS', 2);

class Problem
{
	var $problemName;
	var $description;
	var $seriousness;
	var $url;
	var $subjektid;
	var $validGroups;

	function __construct($subjektid, $problemName, $description, $seriousness, $url, $validGroups)
	{
		$this->subjektid = $subjektid;
		$this->problemName = $problemName;
		$this->description = $description;
		$this->seriousness = $seriousness;
		$this->url = $url;
		$this->validGroups = $validGroups;
	}

	function getSubjektId()
	{
		return $this->subjektid;
	}

	function getProblemName()
	{
		return $this->problemName;
	}

	function getDescription()
	{
		return $this->description;
	}

	function getSeriousness()
	{
		return $this->seriousness;
	}

	function getUrl()
	{
		return $this->url;
	}

	function isValidGroup($usergroupid)
	{
		if (in_array($usergroupid, $this->validGroups)) {
			return true;
		}
		return false;
	}


}

class Check
{

	var $detectors;
	var $problemList;
	var $subjektid;

	function Check()
	{
		$this->detectors = array();
		$this->problemList = array();
		$this->subjektid = '';
	}

	function addDetectorClass($className)
	{
		$this->detectors[] = $className;
	}

	function setSubjektid($subjektid)
	{
		$this->subjektid = $subjektid;
	}

	function checkAll()
	{
		global $sess_usergroup_01, $sess_usergroup_02,
		$sess_usergroup_03, $sess_usergroup_04,
		$sess_usergroup_05, $sess_usergroup_06,
		$sess_usergroup_07, $sess_usergroup_08,
		$sess_usergroup_09, $sess_usergroup_10,
		$sess_usergroup_11, $sess_usergroup_12;

		$problemsArrays = [];
		foreach ($this->detectors as $className) {
			if (class_exists($className)) {
				$detector = new $className();
				$detector->doCheck($this->subjektid);
				$detectorProblems = $detector->getProblems();

				// Merge the problems from this detector with the existing problems
				$problemsArrays = array_merge($problemsArrays, $detectorProblems ? $detectorProblems : []);

			}
		}
		$groups = [];
		for ($i = 1; $i <= 12; $i++) {
			if (${"sess_usergroup_" . str_pad($i, 2, "0", STR_PAD_LEFT)} == 1) {
				$groups[] = $i;
			}
		}
		// foreach ($problemsArrays as $index => $problem) {
		// 	$isValid = false;
		// 	foreach ($groups as $usergroupid) {
		// 		if ($problem->isValidGroup($usergroupid)) {
		// 			$isValid = true;
		// 		}

		// 	}
		// 	if (!$isValid) {
		// 		$problemsArrays[$index] = null;
		// 	}
		// }
		return $problemsArrays;
	}
}

class DetectorClass
{

	var $problems;

	function DetectorClass()
	{
		$this->problems = array();
	}

	function doCheck($id_fond = '')
	{
		// empty abstract function
	}

	function getProblems()
	{
		return $this->problems;
	}
}
?>
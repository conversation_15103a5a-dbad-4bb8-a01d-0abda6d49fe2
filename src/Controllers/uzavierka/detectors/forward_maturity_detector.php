<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

class ForwardMaturityDetector extends DetectorClass
{
    function doCheck($id_fond = '')
    {
        global $config;
        $groups = array(1, 3, 4);

        $fond_restriction = "";
        if ($id_fond != '') {
            $fond_restriction = "
				and k.subjektid = $id_fond
			";
        }
        $query = "SELECT
				k.subjektid,
				k.dealid,
				k.sumakredit,
				k.menakredit,
				k.sumadebet,
				k.menadebet,
				(t.datum+p.line) AS datum,
				p.line as daystill
			FROM
				KONVERZIA k, TODAY t, PIVOT p
			WHERE
				 typ_konverzie = 1 AND
				 dat_realizacia = (t.datum+p.line) AND 
				 k.subjektid = t.fondid AND
				 logactivityid in (12,13) AND
				 p.line BETWEEN 0 AND " . $config['Kontrola']['PocetDni'] . "
				 $fond_restriction
			ORDER BY k.dat_realizacia
		";

        $result = Connection::getDataFromDatabase($query, defaultDB)[1];
        foreach ($result as $item) {
            $subjektid = $item['subjektid'];
            $daystill = $item['daystill'];
            $datum = $item['datum'];
            $sumakredit = $item['sumakredit'];
            $menakredit = $item['menakredit'];
            $sumadebet = $item['sumadebet'];
            $menadebet = $item['menadebet'];

            /* Splatne NDF */
            if ($daystill > 0) {
                $problem_type = PROBLEM_INFO;
                switch ($daystill) {
                    case 1:
                        $sklonovanie_den = 'deň';
                        break;
                    case 2:
                    case 3:
                    case 4:
                        $sklonovanie_den = 'dni';
                        break;
                    default:
                        $sklonovanie_den = 'dní';
                        break;
                }
                $popis = 'Non-delivery forward ' . $sumakredit . ' ' . $menakredit . ' /  ' .
                    $sumadebet . ' ' . $menadebet . ' bude splatný o ' . $daystill
                    . ' ' . $sklonovanie_den . ' (' . gmdate(L_DATEFORMAT, $datum) . ').';
                $link = '';
            } else {
                $problem_type = PROBLEM_SERIOUS;
                $popis = 'Non-delivery forward ' . $sumakredit . ' ' . $menakredit . ' /  ' .
                    $sumadebet . ' ' . $menadebet . ' je splatný v aktuálny evidenčný deň.';
                $link = '';
            }
            $this->problems[] = new Problem($subjektid, 'Splatný non-delivery forward', $popis, $problem_type, $link, $groups);
            continue;
        }
    }
}
?>
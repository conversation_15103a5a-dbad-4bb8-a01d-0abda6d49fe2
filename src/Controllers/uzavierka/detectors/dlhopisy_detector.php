<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

class DlhopisyDetector extends DetectorClass
{
    function doCheck($id_fond = '')
    {
        $groups = array(1, 3, 4);

        $query = "SELECT 
				mt.mena, 
				mt.subjektid, 
				COALESCE(SUM(CASE WHEN mt.uctovnykod = 325121 THEN mt.pocet ELSE 0 END), 0) istina, 
				COALESCE(SUM(CASE WHEN mt.uctovnykod = 325122 THEN mt.pocet ELSE 0 END), 0) AUV 
			from 
				majetoktoday mt
 			where 
				mt.uctovnykod in (325121,325122)
				and mt.eqid = 'BU' 
			group by 
				mt.mena,
				mt.subjektid 
			having 
				COALESCE(SUM(CASE WHEN mt.uctovnykod = 325121 THEN mt.pocet ELSE 0 END), 0)  > COALESCE(SUM(CASE WHEN mt.uctovnykod = 325122 THEN mt.pocet ELSE 0 END), 0)";

        $result = Connection::getDataFromDatabase($query, defaultDB)[1];
        foreach ($result as $item) {
            $subjektid = $item['subjektid'];
            $mena = $item['mena'];

            $this->problems[] = new Problem($subjektid, 'Dlhopis bez AUV', 'Záväzok z kúpy dlhopisu v ' . $mena . ' má viac istín ako AUV.', PROBLEM_WARNING, '', $groups);
            continue;
        }

        $query = "SELECT
				mt.mena, 
				mt.subjektid, 
				COALESCE(SUM(CASE WHEN mt.uctovnykod = 325121 THEN mt.pocet ELSE 0 END), 0) istina, 
				COALESCE(SUM(CASE WHEN mt.uctovnykod = 325122 THEN mt.pocet ELSE 0 END), 0) AUV 
			from 
				majetoktoday mt
 			where 
				mt.uctovnykod in (325121,325122)
				and mt.eqid = 'BU' 
			group by 
				mt.mena, 
				mt.subjektid 
			having 
				COALESCE(SUM(CASE WHEN mt.uctovnykod = 325121 THEN mt.pocet ELSE 0 END), 0)  < COALESCE(SUM(CASE WHEN mt.uctovnykod = 325122 THEN mt.pocet ELSE 0 END), 0)";

        $result = Connection::getDataFromDatabase($query, defaultDB)[1];
        foreach ($result as $item) {
            $subjektid = $item['subjektid'];
            $mena = $item['mena'];

            $this->problems[] = new Problem($subjektid, 'AUV bez dlhopisu ', 'Záväzok z kúpy dlhopisu v ' . $mena . ' má viac AUV ako istín.', PROBLEM_SERIOUS, '', $groups);
            continue;
        }

        $query = "SELECT 
				mt.mena, 
				mt.subjektid, 
				COALESCE(SUM(CASE WHEN mt.uctovnykod = 315121 THEN mt.pocet ELSE 0 END), 0) istina, 
				COALESCE(SUM(CASE WHEN mt.uctovnykod = 315122 THEN mt.pocet ELSE 0 END), 0) AUV 
			from 
				majetoktoday mt
 			where 
				mt.uctovnykod in (315121,315122)
				and mt.eqid = 'BU'
			group by 
				mt.mena, 
				mt.subjektid 
			having 
				COALESCE(SUM(CASE WHEN mt.uctovnykod = 315121 THEN mt.pocet ELSE 0 END), 0)  > COALESCE(SUM(CASE WHEN mt.uctovnykod = 315122 THEN mt.pocet ELSE 0 END), 0) 
			
		";

        $result = Connection::getDataFromDatabase($query, defaultDB)[1];
        foreach ($result as $item) {
            $subjektid = $item['subjektid'];
            $mena = $item['mena'];

            $this->problems[] = new Problem($subjektid, 'Dlhopis bez AUV', 'Pohľadávka z predaja dlhopisu v ' . $mena . ' má viac istín ako AUV.', PROBLEM_WARNING, '', $groups);
            continue;
        }

        $query = "SELECT 
				mt.mena, 
				mt.subjektid, 
				COALESCE(SUM(CASE WHEN mt.uctovnykod = 315121 THEN mt.pocet ELSE 0 END), 0) istina, 
				COALESCE(SUM(CASE WHEN mt.uctovnykod = 315122 THEN mt.pocet ELSE 0 END), 0) AUV 
			from 
				majetoktoday mt
 			where 
				mt.uctovnykod in (315121,315122)
				and mt.eqid = 'BU' 
			group by 
				mt.mena, 
				mt.subjektid 
			having 
				COALESCE(SUM(CASE WHEN mt.uctovnykod = 315121 THEN mt.pocet ELSE 0 END), 0)  < COALESCE(SUM(CASE WHEN mt.uctovnykod = 315122 THEN mt.pocet ELSE 0 END), 0)";

        $result = Connection::getDataFromDatabase($query, defaultDB)[1];
        foreach ($result as $item) {
            $subjektid = $item['subjektid'];
            $mena = $item['mena'];

            $this->problems[] = new Problem($subjektid, 'AUV bez dlhopisu ', 'Pohľadávka z predaja dlhopisu v ' . $mena . ' má viac AUV ako istín.', PROBLEM_SERIOUS, '', $groups);
            continue;
        }

        $query = "SELECT
				d.cpnazskratka,  
				mt.subjektid, 
				COALESCE(SUM(CASE WHEN mt.uctovnykod = 325121 THEN mt.pocet ELSE 0 END), 0) istina, 
				COALESCE(SUM(CASE WHEN mt.uctovnykod = 325122 THEN mt.pocet ELSE 0 END), 0) AUV 
			from 
				majetoktoday mt,
				DBEQUITY d, 
				DBEQUITYCURR c, 
				DBEQUITYCURRRIC r
 			where 
				mt.uctovnykod in (325121,325122)
				and mt.eqid = 'Bonds' 
				and d.isin = c.isin 
				and c.isincurr = r.isincurr				
				and mt.kodaktiva = r.isincurrric 
			group by 
				d.cpnazskratka, 
				mt.subjektid 
			having 
				COALESCE(SUM(CASE WHEN mt.uctovnykod = 325121 THEN mt.pocet ELSE 0 END), 0)  > COALESCE(SUM(CASE WHEN mt.uctovnykod = 325122 THEN mt.pocet ELSE 0 END), 0)";

        $result = Connection::getDataFromDatabase($query, defaultDB)[1];
        foreach ($result as $item) {
            $subjektid = $item['subjektid'];
            $cp = $item['cpnazskratka'];

            $this->problems[] = new Problem($subjektid, 'Dlhopis bez AUV', 'Záväzok z predaja dlhopisu ' . $cp . ' má viac istín ako AUV.', PROBLEM_WARNING, '', $groups);
            continue;
        }

        $query = "SELECT
				d.cpnazskratka,  
				mt.subjektid, 
				COALESCE(SUM(CASE WHEN mt.uctovnykod = 325121 THEN mt.pocet ELSE 0 END), 0) istina, 
				COALESCE(SUM(CASE WHEN mt.uctovnykod = 325122 THEN mt.pocet ELSE 0 END), 0) AUV 
			from 
				majetoktoday mt,
				DBEQUITY d, 
				DBEQUITYCURR c, 
				DBEQUITYCURRRIC r
 			where 
				mt.uctovnykod in (325121,325122)
				and mt.eqid = 'Bonds' 
				and d.isin = c.isin 
				and c.isincurr = r.isincurr				
				and mt.kodaktiva = r.isincurrric 
			group by 
				d.cpnazskratka, 
				mt.subjektid 
			having 
				COALESCE(SUM(CASE WHEN mt.uctovnykod = 325121 THEN mt.pocet ELSE 0 END), 0)  < COALESCE(SUM(CASE WHEN mt.uctovnykod = 325122 THEN mt.pocet ELSE 0 END), 0)";

        $result = Connection::getDataFromDatabase($query, defaultDB)[1];
        foreach ($result as $item) {
            $subjektid = $item['subjektid'];
            $cp = $item['cpnazskratka'];

            $this->problems[] = new Problem($subjektid, 'AUV bez dlhopisu ', 'Záväzok z predaja dlhopisu ' . $cp . ' má viac AUV ako istín.', PROBLEM_SERIOUS, '', $groups);
            continue;
        }

        $query = "SELECT 
				d.cpnazskratka,  
				mt.subjektid, 
				COALESCE(SUM(CASE WHEN mt.uctovnykod = 315121 THEN mt.pocet ELSE 0 END), 0) istina, 
				COALESCE(SUM(CASE WHEN mt.uctovnykod = 315122 THEN mt.pocet ELSE 0 END), 0) AUV 
			from 
				majetoktoday mt,
				DBEQUITY d, 
				DBEQUITYCURR c, 
				DBEQUITYCURRRIC r
 			where 
				mt.uctovnykod in (315121,315122)
				and mt.eqid = 'Bonds' 
				and d.isin = c.isin 
				and c.isincurr = r.isincurr				
				and mt.kodaktiva = r.isincurrric 
			group by 
				d.cpnazskratka, 
				mt.subjektid 
			having 
				COALESCE(SUM(CASE WHEN mt.uctovnykod = 315121 THEN mt.pocet ELSE 0 END), 0)  > COALESCE(SUM(CASE WHEN mt.uctovnykod = 315122 THEN mt.pocet ELSE 0 END), 0)";

        $result = Connection::getDataFromDatabase($query, defaultDB)[1];
        foreach ($result as $item) {
            $subjektid = $item['subjektid'];
            $cp = $item['cpnazskratka'];

            $this->problems[] = new Problem($subjektid, 'Dlhopis bez AUV', 'Pohľadávka z kúpy dlhopisu ' . $cp . ' má viac istín ako AUV.', PROBLEM_WARNING, '', $groups);
            continue;
        }

        $query = "SELECT
				d.cpnazskratka,  
				mt.subjektid, 
				COALESCE(SUM(CASE WHEN mt.uctovnykod = 315121 THEN mt.pocet ELSE 0 END), 0) istina, 
				COALESCE(SUM(CASE WHEN mt.uctovnykod = 315122 THEN mt.pocet ELSE 0 END), 0) AUV 
			from 
				majetoktoday mt,
				DBEQUITY d, 
				DBEQUITYCURR c, 
				DBEQUITYCURRRIC r
 			where 
				mt.uctovnykod in (315121,315122)
				and mt.eqid = 'Bonds' 
				and d.isin = c.isin 
				and c.isincurr = r.isincurr				
				and mt.kodaktiva = r.isincurrric 
			group by 
				d.cpnazskratka, 
				mt.subjektid 
			having 
				COALESCE(SUM(CASE WHEN mt.uctovnykod = 315121 THEN mt.pocet ELSE 0 END), 0)  < COALESCE(SUM(CASE WHEN mt.uctovnykod = 315122 THEN mt.pocet ELSE 0 END), 0)  
			
		";

        $result = Connection::getDataFromDatabase($query, defaultDB)[1];
        foreach ($result as $item) {
            $subjektid = $item['subjektid'];
            $cp = $item['cpnazskratka'];

            $this->problems[] = new Problem($subjektid, 'AUV bez dlhopisu ', 'Pohľadávka z kúpy dlhopisu ' . $cp . ' má viac AUV ako istín.', PROBLEM_SERIOUS, '', $groups);
            continue;
        }

        $query = "SELECT
				d.cpnazskratka,  
				mt.subjektid, 
				COALESCE(SUM(CASE WHEN mt.uctovnykod = 251110 THEN mt.pocet ELSE 0 END), 0) istina, 
				COALESCE(SUM(CASE WHEN mt.uctovnykod = 251120 THEN mt.pocet ELSE 0 END), 0) AUV 
			from 
				majetoktoday mt,
				DBEQUITY d, 
				DBEQUITYCURR c, 
				DBEQUITYCURRRIC r
 			where 
				mt.uctovnykod in (251110,251120)
				and mt.eqid = 'Bonds' 
				and d.isin = c.isin 
				and c.isincurr = r.isincurr				
				and mt.kodaktiva = r.isincurrric 
			group by 
				d.cpnazskratka, 
				mt.subjektid 
			having 
				COALESCE(SUM(CASE WHEN mt.uctovnykod = 251110 THEN mt.pocet ELSE 0 END), 0)  > COALESCE(SUM(CASE WHEN mt.uctovnykod = 251120 THEN mt.pocet ELSE 0 END), 0)  
			
		";

        $result = Connection::getDataFromDatabase($query, defaultDB)[1];
        foreach ($result as $item) {
            $subjektid = $item['subjektid'];
            $cp = $item['cpnazskratka'];

            $this->problems[] = new Problem($subjektid, 'Dlhopis bez AUV', 'Dlhopis ' . $cp . ' má viac istín ako AUV.', PROBLEM_WARNING, '', $groups);
            continue;
        }

        $query = "SELECT 
				d.cpnazskratka,  
				mt.subjektid, 
				COALESCE(SUM(CASE WHEN mt.uctovnykod = 251110 THEN mt.pocet ELSE 0 END), 0) istina, 
				COALESCE(SUM(CASE WHEN mt.uctovnykod = 251120 THEN mt.pocet ELSE 0 END), 0) AUV 
			from 
				majetoktoday mt,
				DBEQUITY d, 
				DBEQUITYCURR c, 
				DBEQUITYCURRRIC r
 			where 
				mt.uctovnykod in (251110,251120)
				and mt.eqid = 'Bonds' 
				and d.isin = c.isin 
				and c.isincurr = r.isincurr				
				and mt.kodaktiva = r.isincurrric 
			group by 
				d.cpnazskratka, 
				mt.subjektid 
			having 
				COALESCE(SUM(CASE WHEN mt.uctovnykod = 251110 THEN mt.pocet ELSE 0 END), 0)  < COALESCE(SUM(CASE WHEN mt.uctovnykod = 251120 THEN mt.pocet ELSE 0 END), 0)  
			
		";

        $result = Connection::getDataFromDatabase($query, defaultDB)[1];
        foreach ($result as $item) {
            $subjektid = $item['subjektid'];
            $cp = $item['cpnazskratka'];

            $this->problems[] = new Problem($subjektid, 'AUV bez dlhopisu ', 'Dlhopis ' . $cp . ' má viac AUV ako istín.', PROBLEM_SERIOUS, '', $groups);
            continue;
        }
    }
}
?>
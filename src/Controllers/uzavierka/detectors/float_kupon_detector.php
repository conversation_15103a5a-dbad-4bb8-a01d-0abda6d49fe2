<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

class FloatKuponDetector extends DetectorClass
{
    function doCheck($id_fond = '')
    {
        global $config;
        $groups = array(1, 3, 4);
        $fond_restriction = "";
        if ($id_fond != '') {
            $fond_restriction = "
				and mt.subjektid = $id_fond
			";
        }
        $query = "SELECT
				mt.subjektid,
				MIN(t.datum+p.line) as datum,
				d.cpnazskratka as skratka,
				d.isin,
				fk.kupon,
				fk.datefrom as from_date,
				fk.datetill as till_date,
				p.line as daystill
				
			FROM
				FLOATKUPON fk, MAJETOKTODAY mt, pivot p, dbequity d, dbequitycurr c, dbequitycurrric r, TODAY t
			WHERE
				t.fondid = mt.subjektid AND
				d.eqid = 'Bonds' AND
				d.isin = c.isin and 
				d.floatkupon = 1 and
				c.isincurr = r.isincurr and
				fk.isincurrric = r.isincurrric and
				fk.isincurrric = mt.kodaktiva AND
				(t.datum+p.line) = fk.datefrom AND
				p.line BETWEEN 0 AND " . $config['Kontrola']['PocetDni'] . " $fond_restriction
			GROUP BY mt.subjektid, d.cpnazskratka, fk.kupon, d.isin, fk.datefrom, fk.datetill, p.line		
		";
        
        $result = Connection::getDataFromDatabase($query, defaultDB)[1];
        foreach ($result as $item) {
            $subjektid = $item['subjektid'];
            $datum = $item['datum'];
            $skratka = $item['skratka'];
            $isin = $item['isin'];
            $kupon = $item['kupon'];
            $from_date = $item['from_date'];
            $till_date = $item['till_date'];
            $daystill = $item['daystill'];

            /* Nove obdobie kuponu - nie je zadany ziadny kupon */
            if ($kupon == '') {
                $problem_type = $daystill > 0 ? PROBLEM_WARNING : PROBLEM_SERIOUS;
                $this->problems[] = new Problem(
                    $subjektid,
                    'Nezadaný float kupón',
                    'Kupón dlhopisu ' .
                    $skratka . ' (' . $isin . ') pre obdobie od ' . $from_date . ' do ' .
                    $till_date . ' nie je zadaný.',
                    $problem_type,
                    'cp/cp_udaje_frame.php?action=detail&loaddetail=1&cp_type=Bonds&archiv=f&isin=' . $isin,
                    $groups
                );
                continue;
            }

            /* Nove obdobie kuponu - je zadany nulovy kupon*/
            if ($kupon == 0 && $kupon != '') {
                $problem_type = $daystill > 0 ? PROBLEM_WARNING : PROBLEM_SERIOUS;
                $this->problems[] = new Problem(
                    $subjektid,
                    'Nulový float kupón',
                    'Kupón dlhopisu ' .
                    $skratka . ' (' . $isin . ') pre obdobie od ' . $from_date . ' do ' .
                    $till_date . ' je ' . number_format($kupon, 5, '.', '') . ' %.',
                    $problem_type,
                    'cp/cp_udaje_frame.php?action=detail&loaddetail=1&cp_type=Bonds&archiv=f&isin=' . $isin,
                    $groups
                );
                continue;
            }
            
            /* Nove obdobie kuponu - kupon je zadany*/
            $this->problems[] = new Problem(
                $subjektid,
                'Nové obdobie float kupónu',
                'Kupón dlhopisu ' .
                $skratka . ' (' . $isin . ') pre obdobie od ' . $from_date . ' do ' .
                $till_date . ' je ' . number_format($kupon, 5, '.', '') . ' %.',
                PROBLEM_INFO,
                'cp/cp_udaje_frame.php?action=detail&loaddetail=1&cp_type=Bonds&archiv=f&isin=' . $isin,
                $groups
            );
        }
    }
}
?>
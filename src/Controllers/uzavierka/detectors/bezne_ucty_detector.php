<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";


class BezneUctyDetector extends DetectorClass
{
    function doCheck($id_fond = '')
    {
        $groups = array(1, 3, 4);

        $query = "SELECT
 				mt.ucetaktiva ,
				mt.kodaktiva AS mena,
				SUM(SIGN(md_d - 0.5) * (-1) * mt.pocet) AS pocet,
				mt.subjektid
			FROM
				majetoktoday mt
			WHERE
				
				mt.uctovnykod = 221110
        			and mt.subjektid > 0
			GROUP BY mt.ucetaktiva, mt.kodaktiva, mt.subjektid
      			HAVING (SUM(SIGN(md_d - 0.5) * (-1) * mt.pocet)) < 0";

        $result = Connection::getDataFromDatabase($query, defaultDB);
        foreach ($result as $item) {
            $subjektid = $item['subjektid'];
            $ucet = $item['ucetaktiva'];

            $this->problems[] = new Problem($subjektid, '<PERSON>á<PERSON>ný stav BÚ', 'Na účte ' . $ucet . ' je záporný zostatok.', PROBLEM_SERIOUS, '', $groups);
        }
    }
}


?>
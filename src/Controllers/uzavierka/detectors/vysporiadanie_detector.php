<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

function sklonovany_den($num_days)
{
    switch ($num_days) {
        case 1:
            $res = 'dňa';
            break;
        default:
            $res = 'dní';
            break;
    }
    return $res;

}

class FVysporiadanieUhradyDetector extends DetectorClass
{
    function doCheck($id_fond = '')
    {
        global $config;
        $groups = array(1, 3, 4);

        $fond_restriction = "";
        if ($id_fond != '') {
            $fond_restriction = "
				and mc.subjektid = $id_fond
			";
        }

        $query = "SELECT distinct
			cestatill as dat,
			mc.*, cestatill - t.datum AS days
			from majetokcesta mc, pivot, today t
		where
			in_out=0
			and date_trunc('day', cestatill) <= t.datum + line
			and t.fondid = mc.subjektid
			and mc.destinacia != 'konfirmaciaktv'	
			and mc.destinacia != 'dbequity'
			and mc.destinacia != 'konfirmaciapp'
			and mc.destinacia != 'splatenieakcia'		
			and mc.eqid = 'BU'
			and line<=" . $config['Kontrola']['PocetDni'] . "
			$fond_restriction
		order by subjektid
		";

        $result = Connection::getDataFromDatabase($query, defaultDB)[1];
        foreach ($result as $item) {
            $msg = "";
            $days = $item['days'];

            if ($days < 0) {
                $seriousness = PROBLEM_SERIOUS;
                $msg = "Finančne nevysporiadaná úhrada.";
            }
            if ($days == 0) {
                $seriousness = PROBLEM_WARNING;
                $msg = "Úhradu je potrebné finančne vysporiadať v aktuálny evidenčný dátum.";
            }
            if ($days > 0) {
                $seriousness = PROBLEM_INFO;
                $msg = "Úhradu je potrebné finančne vysporiadať do $days " . sklonovany_den($days) . ".";
            }

            $link = "uhrady/uhrady_frame.php?id_fond=" . $item['subjektid'] . "&action=insert" . "&cub=" . $item['ucetaktiva'];

            $this->problems[] = new Problem($item['subjektid'], $item['popis'], $msg, $seriousness, $link, $groups);
        }
    }
}

class FVysporiadanieDoslePlatbyDetector extends DetectorClass
{
    function doCheck($id_fond = '')
    {
        global $ROOT, $config;
        $groups = array(1, 3, 4);

        $fond_restriction = "";
        if ($id_fond != '') {
            $fond_restriction = "
				and mc.subjektid = $id_fond
			";
        }

        $query = "SELECT distinct
			cestatill as dat,
			mc.*, cestatill - t.datum AS days
			from majetokcesta mc, pivot, today t
		where
			in_out=1
			and date_trunc('day', cestatill) <= t.datum + line
			and t.fondid = mc.subjektid
			and mc.destinacia != 'konfirmaciaktv'
			and mc.destinacia != 'dbequity'	
			and mc.destinacia != 'konfirmaciapp'	
			and mc.destinacia != 'splatenieakcia'	
			and mc.eqid = 'BU'
			and line<=" . $config['Kontrola']['PocetDni'] . "
			$fond_restriction
		order by subjektid
		";

        $result = Connection::getDataFromDatabase($query, defaultDB)[1];
        foreach ($result as $item) {
            $msg = "";
            $days = $item['days'];

            if ($days < 0) {
                $seriousness = PROBLEM_SERIOUS;
                $msg = "Nevysporiadaná došlá platba.";
            }
            if ($days == 0) {
                $seriousness = PROBLEM_WARNING;
                $msg = "Došlú platbu je potrebné vysporiadať v aktuálny evidenčný dátum.";
            }
            if ($days > 0) {
                $seriousness = PROBLEM_INFO;
                $msg = "Došlú platbu je potrebné vysporiadať do $days " . sklonovany_den($days) . ".";
            }


            $link = "platby/dosle_platby_natip_frame.php?id_fond=" . $item['subjektid'];
            $cinnost = 'natipovať';

            $this->problems[] = new Problem($item['subjektid'], $item['popis'], $msg, $seriousness, $link, $groups);
        }
    }
}

class MVysporiadanieDosleAktivaDetector extends DetectorClass
{
    function doCheck($id_fond = '')
    {
        global $ROOT, $config;
        $groups = array(1, 3, 4);

        $fond_restriction = "";
        if ($id_fond != '') {
            $fond_restriction = "
				and mc.subjektid = $id_fond
			";
        }

        $query = "SELECT distinct
			cestatill as dat,
			mc.*, cestatill - t.datum AS days
			from majetokcesta mc, pivot, today t
		where
			in_out=1
			and date_trunc('day', cestafrom) <= t.datum + line
			and t.fondid = mc.subjektid
			and mc.eqid not in ('BU', 'TD')
			and line<=" . $config['Kontrola']['PocetDni'] . "
			$fond_restriction
		order by subjektid";

        $result = Connection::getDataFromDatabase($query, defaultDB)[1];
        foreach ($result as $item) {
            $msg = "";
            $days = $item['days'];

            if ($days < 0) {
                $seriousness = PROBLEM_SERIOUS;
                $msg = "Majetkovo nevysporiadané prichádzajúce aktívum.";
            }
            if ($days == 0) {
                $seriousness = PROBLEM_WARNING;
                $msg = "Prichádzajúce aktívum je potrebné majetkovo vysporiadať v aktuálny evidenčný dátum.";
            }
            if ($days > 0) {
                $seriousness = PROBLEM_INFO;
                $msg = "Prichádzajúce aktívum je potrebné majetkovo vysporiadať do $days " . sklonovany_den($days) . ".";
            }

            $link = "platby/dosle_cp_frame.php?id_fond=" . $item['subjektid'] . "&eqid=" . $item['eqid'];
            $this->problems[] = new Problem($item['subjektid'], $item['popis'], $msg, $seriousness, $link, $groups);
        }
    }
}

class MVysporiadanieOdchadzajuceAktivaDetector extends DetectorClass
{
    function doCheck($id_fond = '')
    {
        global $config;
        $groups = array(1, 3, 4);

        $fond_restriction = "";
        if ($id_fond != '') {
            $fond_restriction = "
				and mc.subjektid = $id_fond
			";
        }

        $query = "SELECT distinct
			cestatill as dat,
			mc.*, cestatill - t.datum AS days
			from majetokcesta mc, pivot, today t
		where
			in_out=0
			and date_trunc('day', cestafrom) <= t.datum + line
			and t.fondid = mc.subjektid
			and mc.eqid not in ('BU', 'TD')
			and line<=" . $config['Kontrola']['PocetDni'] . "
			$fond_restriction
		order by subjektid";

        $result = Connection::getDataFromDatabase($query, defaultDB)[1];
        foreach ($result as $item) {
            $msg = "";
            $days = $item['days'];

            if ($days < 0) {
                $seriousness = PROBLEM_SERIOUS;
                $msg = "Majetkovo nevysporiadané odchádzajúce aktívum.";
            }
            if ($days == 0) {
                $seriousness = PROBLEM_WARNING;
                $msg = "Odchádzajúce aktívum je potrebné majetkovo vysporiadať v aktuálny evidenčný dátum.";
            }
            if ($days > 0) {
                $seriousness = PROBLEM_INFO;
                $msg = "Odchádzajúce aktívum je potrebné majetkovo vysporiadať do $days " . sklonovany_den($days) . ".";
            }

            $link = "platby/Majetok_uhrada_natipovanie_frame.php?SFondID=" . $item['subjektid'] . "&eqid=" . $item['eqid'];

            $this->problems[] = new Problem($item['subjektid'], $item['popis'], $msg, $seriousness, $link, $groups);
        }
    }
}

class VkladFVysporiadanieDetector extends DetectorClass
{
    function doCheck($id_fond = '')
    {
        global $config;
        $groups = array(1, 3, 4);
        $fond_restriction = "";
        if ($id_fond != '') {
            $fond_restriction = "
				and k.subjektid = $id_fond
			";
        }

        $query = "SELECT mc.subjektid, mc.pocet, mc.popis, mc.ucetaktiva, ob.logactivityid
			from majetokcesta mc, obratybu ob
			where mc.eqid = 'BU'
			and mc.destinacia = 'konfirmaciapp'
			and mc.in_out = 1
			and ob.krdb = 1
			and ob.logactivityid in (1,4,6)
			and ob.ss = mc.sparovanie
			and ob.suma = mc.pocet
      			and ob.subjektid = mc.subjektid";

        $result = Connection::getDataFromDatabase($query, defaultDB)[1];
        foreach ($result as $item) {
            $subjektid = $item['subjektid'];
            $popis = $item['popis'];
            $ucet = $item['ucetaktiva'];
            $logactivityid = $item['logactivityid'];
            $link = '';
            $cinnost = '';
            if ($logactivityid == 1) {
                $link = "platby/dosle_platby_natip_frame.php?id_fond=" . $item['subjektid'];
                $cinnost = 'natipovať';
            } else {
                $link = "platby/dosle_platby_spar_frame.php?id_fond=" . $item['subjektid'];
                $cinnost = 'spárovať';
            }

            $this->problems[] = new Problem(
                $subjektid,
                $popis,
                'Vklad na účte ' . $ucet . ' je potrebné finančne vysporiadať (' . $cinnost . ') v aktuálny evidenčný dátum.',
                PROBLEM_SERIOUS,
                $link,
                $groups
            );
            continue;
        }
    }
}


?>
<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

class SecurityMaturityDetector extends DetectorClass
{
    function doCheck($id_fond = '')
    {
        global $config;
        $groups = array(1, 3, 4);
        $fond_restriction = "";
        if ($id_fond != '') {
            $fond_restriction = "
				and mt.subjektid = $id_fond
			";
        }

        $pocetDni = $config['Kontrola']['PocetDni'];

        /* splatny kupon */
        $query = "SELECT  DISTINCT
				pocet as stav_majetok,
				mt.subjektid,
				d.cpnazskratka AS skratka,
				d.isin,
				to_char(fk.datesplatnost,'DD.MM.YYYY') as datum,
				fk.datesplatnost - t.datum as daystill
			FROM
				MAJETOKTODAY mt, DBEQUITY d, DBEQUITYCURR c, DBEQUITYCURRRIC r, TODAY t, floatkupon fk
			WHERE
				t.fondid = mt.subjektid
				and d.eqid = 'Bonds'
				and d.isin = c.isin 
				and c.isincurr = r.isincurr				
				and mt.uctovnykod in (251120, 315124) 
				and mt.kodaktiva = r.isincurrric
				and fk.isincurrric = mt.kodaktiva
				and fk.datefrom < t.datum
				and fk.datesplatnost >= t.datum
				and fk.datesplatnost - t.datum < $pocetDni
				and mt.obratid = 0
				and mt.subjektid not in (select s.subjektid from splatenie s where t.datum=s.datumvyplaty and s.kodaktiva = mt.kodaktiva and s.uctovnykod = 251120)
				$fond_restriction ";

        $result = Connection::getDataFromDatabase($query, defaultDB)[1];
        foreach ($result as $item) {
            $subjektid = $item['subjektid'];
            $datum = $item['datum'];
            $skratka = $item['skratka'];
            $isin = $item['isin'];
            $daystill = $item['daystill'];

            /* Splatny dlhopis */
            if ($daystill > 0) {
                $problem_type = PROBLEM_INFO;
                switch ($daystill) {
                    case 1:
                        $sklonovanie_den = 'deň';
                        break;
                    case 2:
                    case 3:
                    case 4:
                        $sklonovanie_den = 'dni';
                        break;
                    default:
                        $sklonovanie_den = 'dní';
                        break;
                }
                $popis = "Kupón dlhopisu $skratka ($isin) bude splatný o $daystill $sklonovanie_den ($datum).";
                $link = '';
            } else {
                $problem_type = PROBLEM_WARNING;
                $popis = "Kupón dlhopisu $skratka ($isin) je splatný v aktuálny evidenčný deň.";
                $link = '';
            }
            $this->problems[] = new Problem($subjektid, 'Splatný kupón', $popis, $problem_type, $link, $groups);
            continue;
        }

        /* potvrdenie kuponu */
        $query = "SELECT 
				mt.pocet as stav_majetok,
				mt.subjektid,
				d.cpnazskratka AS skratka,
				d.isin,
				to_char((select max(datum) from today),'DD.MM.YYYY') as datum,
				0 as daystill
			from majetoktoday mt, dbequity d 
			where
				mt.eqid = 'Bonds'
				and is_coupon_date(mt.kodaktiva, (select max(datum) from today))=1
				and mt.uctovnykod = 251110
				and mt.obratid = 0
				and not exists(select * from majetoktoday mt2 where mt2.subjektid=mt.subjektid and mt2.uctovnykod=315124 and mt2.eqid=mt.eqid and mt2.kodaktiva=mt.kodaktiva and mt2.ucetaktiva=mt.ucetaktiva and mt2.mena=mt.mena and mt2.md_d=mt.md_d)
				and d.isin = (select max(dc.isin) from dbequitycurr dc, dbequitycurrric dcr where dc.isincurr=dcr.isincurr and dcr.isincurrric=mt.kodaktiva)  
				$fond_restriction 
			union all
			select 
				mt.pocet as stav_majetok,
				mt.subjektid,
				d.cpnazskratka AS skratka,
				d.isin,
				to_char((select max(datum)+1 from today),'DD.MM.YYYY') as datum,
				1 as daystill
			from majetoktoday mt, dbequity d 
			where
				mt.eqid = 'Bonds'
				and is_coupon_date(mt.kodaktiva, (select max(datum)+1 from today))=1
				and is_coupon_date(mt.kodaktiva, (select max(datum)+1 from today))=0
				and mt.uctovnykod = 251110
				and mt.obratid = 0
				and not exists(select * from majetoktoday mt2 where mt2.subjektid=mt.subjektid and mt2.uctovnykod=315124 and mt2.eqid=mt.eqid and mt2.kodaktiva=mt.kodaktiva and mt2.ucetaktiva=mt.ucetaktiva and mt2.mena=mt.mena and mt2.md_d=mt.md_d)
				and d.isin = (select max(dc.isin) from dbequitycurr dc, dbequitycurrric dcr where dc.isincurr=dcr.isincurr and dcr.isincurrric=mt.kodaktiva)  
				$fond_restriction
			union all
			select 
				mt.pocet as stav_majetok,
				mt.subjektid,
				d.cpnazskratka AS skratka,
				d.isin,
				to_char((select max(datum)+2 from today),'DD.MM.YYYY') as datum,
				2 as daystill
			from majetoktoday mt, dbequity d 
			where
				mt.eqid = 'Bonds'
				and is_coupon_date(mt.kodaktiva, (select max(datum)+2 from today))=1
				and is_coupon_date(mt.kodaktiva, (select max(datum)+2 from today))=0
				and mt.uctovnykod = 251110
				and mt.obratid = 0
				and not exists(select * from majetoktoday mt2 where mt2.subjektid=mt.subjektid and mt2.uctovnykod=315124 and mt2.eqid=mt.eqid and mt2.kodaktiva=mt.kodaktiva and mt2.ucetaktiva=mt.ucetaktiva and mt2.mena=mt.mena and mt2.md_d=mt.md_d)
				and d.isin = (select max(dc.isin) from dbequitycurr dc, dbequitycurrric dcr where dc.isincurr=dcr.isincurr and dcr.isincurrric=mt.kodaktiva)  
				$fond_restriction
			union all
			select 
				mt.pocet as stav_majetok,
				mt.subjektid,
				d.cpnazskratka AS skratka,
				d.isin,
				to_char((select max(datum)+3 from today),'DD.MM.YYYY') as datum,
				3 as daystill
			from majetoktoday mt, dbequity d 
			where
				mt.eqid = 'Bonds'
				and is_coupon_date(mt.kodaktiva, (select max(datum)+3 from today))=1
				and is_coupon_date(mt.kodaktiva, (select max(datum)+3 from today))=0
				and mt.uctovnykod = 251110
				and mt.obratid = 0
				and not exists(select * from majetoktoday mt2 where mt2.subjektid=mt.subjektid and mt2.uctovnykod=315124 and mt2.eqid=mt.eqid and mt2.kodaktiva=mt.kodaktiva and mt2.ucetaktiva=mt.ucetaktiva and mt2.mena=mt.mena and mt2.md_d=mt.md_d)
				and d.isin = (select max(dc.isin) from dbequitycurr dc, dbequitycurrric dcr where dc.isincurr=dcr.isincurr and dcr.isincurrric=mt.kodaktiva)  
				$fond_restriction
			union all
			select 
				mt.pocet as stav_majetok,
				mt.subjektid,
				d.cpnazskratka AS skratka,
				d.isin,
				to_char((select max(datum)+4 from today),'DD.MM.YYYY') as datum,
				4 as daystill
			from majetoktoday mt, dbequity d 
			where
				mt.eqid = 'Bonds'
				and is_coupon_date(mt.kodaktiva, (select max(datum)+4 from today))=1
				and is_coupon_date(mt.kodaktiva, (select max(datum)+4 from today))=0
				and mt.uctovnykod = 251110
				and mt.obratid = 0
				and not exists(select * from majetoktoday mt2 where mt2.subjektid=mt.subjektid and mt2.uctovnykod=315124 and mt2.eqid=mt.eqid and mt2.kodaktiva=mt.kodaktiva and mt2.ucetaktiva=mt.ucetaktiva and mt2.mena=mt.mena and mt2.md_d=mt.md_d)
				and d.isin = (select max(dc.isin) from dbequitycurr dc, dbequitycurrric dcr where dc.isincurr=dcr.isincurr and dcr.isincurrric=mt.kodaktiva)  
				$fond_restriction 
			union all
			select 
				mt.pocet as stav_majetok,
				mt.subjektid,
				d.cpnazskratka AS skratka,
				d.isin,
				to_char((select max(datum)+5 from today),'DD.MM.YYYY') as datum,
				5 as daystill
			from majetoktoday mt, dbequity d 
			where
				mt.eqid = 'Bonds'
				and is_coupon_date(mt.kodaktiva, (select max(datum)+5 from today))=1
				and is_coupon_date(mt.kodaktiva, (select max(datum)+5 from today))=0
				and mt.uctovnykod = 251110
				and mt.obratid = 0
				and not exists(select * from majetoktoday mt2 where mt2.subjektid=mt.subjektid and mt2.uctovnykod=315124 and mt2.eqid=mt.eqid and mt2.kodaktiva=mt.kodaktiva and mt2.ucetaktiva=mt.ucetaktiva and mt2.mena=mt.mena and mt2.md_d=mt.md_d)
				and d.isin = (select max(dc.isin) from dbequitycurr dc, dbequitycurrric dcr where dc.isincurr=dcr.isincurr and dcr.isincurrric=mt.kodaktiva)  
				$fond_restriction";
				echo $query;
        $result = Connection::getDataFromDatabase($query, defaultDB)[1];
        foreach ($result as $item) {
            $subjektid = $item['subjektid'];
            $datum = $item['datum'];
            $skratka = $item['skratka'];
            $isin = $item['isin'];
            $daystill = $item['daystill'];

            /* Splatny dlhopis */
            if ($daystill > 0) {
                $problem_type = PROBLEM_INFO;
                switch ($daystill) {
                    case 1:
                        $sklonovanie_den = 'deň';
                        break;
                    case 2:
                    case 3:
                    case 4:
                        $sklonovanie_den = 'dni';
                        break;
                    default:
                        $sklonovanie_den = 'dní';
                        break;
                }
                $popis = "Kupón dlhopisu $skratka ($isin) treba potvrdiť o $daystill $sklonovanie_den ($datum).";
                $link = '';
            } else {
                $problem_type = PROBLEM_SERIOUS;
                $popis = "Kupón dlhopisu $skratka ($isin) treba potvrdiť v aktuálny evidenčný deň.";
                $link = ''; //$ROOT.'splatenie/dlhopis_frame.php?id_fond='.$subjektid;
            }
            $this->problems[] = new Problem($subjektid, 'Nepotvrdený kupón', $popis, $problem_type, $link, $groups);
            continue;
        }

        /* splatna istina */
        $query = "SELECT
				DISTINCT
				pocet as stav_majetok,
				mt.subjektid,
				d.cpnazskratka AS skratka,
				d.isin,
				to_char(fk.datesplatnost,'DD.MM.YYYY') as datum,
				fk.datesplatnost - t.datum as daystill
			FROM
				MAJETOKTODAY mt, DBEQUITY d, DBEQUITYCURR c, DBEQUITYCURRRIC r, TODAY t, floatkupon fk
			WHERE
				t.fondid = mt.subjektid
				and d.eqid = 'Bonds'
				and d.isin = c.isin 
				and c.isincurr = r.isincurr				
				and mt.uctovnykod in (251110, 315123) 
				and mt.kodaktiva = r.isincurrric
				and fk.isincurrric = mt.kodaktiva
				and fk.datefrom < t.datum
				and fk.datesplatnost >= t.datum
				and fk.istina > 0
				and fk.datesplatnost - t.datum < $pocetDni
				and mt.obratid = 0
				and mt.subjektid not in (select s.subjektid from splatenie s where t.datum=s.datumvyplaty and s.kodaktiva = mt.kodaktiva and s.uctovnykod = 251110)
				$fond_restriction 
		";

        $result = Connection::getDataFromDatabase($query, defaultDB)[1];
        foreach ($result as $item) {
            $subjektid = $item['subjektid'];
            $datum = $item['datum'];
            $skratka = $item['skratka'];
            $isin = $item['isin'];
            $daystill = $item['daystill'];

            /* Splatny dlhopis */
            if ($daystill > 0) {
                $problem_type = PROBLEM_INFO;
                switch ($daystill) {
                    case 1:
                        $sklonovanie_den = 'deň';
                        break;
                    case 2:
                    case 3:
                    case 4:
                        $sklonovanie_den = 'dni';
                        break;
                    default:
                        $sklonovanie_den = 'dní';
                        break;
                }
                $popis = "Istina dlhopisu $skratka ($isin) bude splatná o $daystill $sklonovanie_den ($datum).";
                $link = '';
            } else {
                $problem_type = PROBLEM_WARNING;
                $popis = "Istina dlhopisu $skratka ($isin) je splatná v aktuálny evidenčný deň.";
                $link = '';   //$ROOT.'splatenie/dlhopis_frame.php?id_fond='.$subjektid;
            }
            $this->problems[] = new Problem($subjektid, 'Splatná istina', $popis, $problem_type, $link, $groups);
            continue;
        }


        /* potvrdenie istiny */
        $query = "SELECT
				mt.pocet as stav_majetok,
				mt.subjektid,
				d.cpnazskratka AS skratka,
				d.isin,
				to_char((select max(datum) from today),'DD.MM.YYYY') as datum,
				0 as daystill
			from majetoktoday mt, dbequity d 
			where
				mt.eqid = 'Bonds'
				and is_existina_date(mt.kodaktiva, (select max(datum) from today))=1
				and mt.uctovnykod = 251110
				and mt.obratid = 0
				and not exists(select * from majetoktoday mt2 where mt2.subjektid=mt.subjektid and mt2.uctovnykod=315123 and mt2.eqid=mt.eqid and mt2.kodaktiva=mt.kodaktiva and mt2.ucetaktiva=mt.ucetaktiva and mt2.mena=mt.mena and mt2.md_d=mt.md_d)
				and d.isin = (select max(dc.isin) from dbequitycurr dc, dbequitycurrric dcr where dc.isincurr=dcr.isincurr and dcr.isincurrric=mt.kodaktiva)  
				$fond_restriction
			UNION ALL 
			select 
				mt.pocet as stav_majetok,
				mt.subjektid,
				d.cpnazskratka AS skratka,
				d.isin,
				to_char((select max(datum)+1 from today),'DD.MM.YYYY') as datum,
				1 as daystill
			from majetoktoday mt, dbequity d 
			where
				mt.eqid = 'Bonds'
				and is_existina_date(mt.kodaktiva, (select max(datum)+1 from today))=1
				and is_istina_date(mt.kodaktiva, (select max(datum)+1 from today))=0
				and mt.uctovnykod = 251110
				and mt.obratid = 0
				and not exists(select * from majetoktoday mt2 where mt2.subjektid=mt.subjektid and mt2.uctovnykod=315123 and mt2.eqid=mt.eqid and mt2.kodaktiva=mt.kodaktiva and mt2.ucetaktiva=mt.ucetaktiva and mt2.mena=mt.mena and mt2.md_d=mt.md_d)
				and d.isin = (select max(dc.isin) from dbequitycurr dc, dbequitycurrric dcr where dc.isincurr=dcr.isincurr and dcr.isincurrric=mt.kodaktiva)  
				$fond_restriction
			UNION ALL
			select 
				mt.pocet as stav_majetok,
				mt.subjektid,
				d.cpnazskratka AS skratka,
				d.isin,
				to_char((select max(datum)+2 from today),'DD.MM.YYYY') as datum,
				2 as daystill
			from majetoktoday mt, dbequity d 
			where
				mt.eqid = 'Bonds'
				and is_existina_date(mt.kodaktiva, (select max(datum)+2 from today))=1
				and is_istina_date(mt.kodaktiva, (select max(datum)+2 from today))=0
				and mt.uctovnykod = 251110
				and mt.obratid = 0
				and not exists(select * from majetoktoday mt2 where mt2.subjektid=mt.subjektid and mt2.uctovnykod=315123 and mt2.eqid=mt.eqid and mt2.kodaktiva=mt.kodaktiva and mt2.ucetaktiva=mt.ucetaktiva and mt2.mena=mt.mena and mt2.md_d=mt.md_d)
				and d.isin = (select max(dc.isin) from dbequitycurr dc, dbequitycurrric dcr where dc.isincurr=dcr.isincurr and dcr.isincurrric=mt.kodaktiva)  
				$fond_restriction
			UNION ALL
			select 
				mt.pocet as stav_majetok,
				mt.subjektid,
				d.cpnazskratka AS skratka,
				d.isin,
				to_char((select max(datum)+3 from today),'DD.MM.YYYY') as datum,
				3 as daystill
			from majetoktoday mt, dbequity d 
			where
				mt.eqid = 'Bonds'
				and is_existina_date(mt.kodaktiva, (select max(datum)+3 from today))=1
				and is_istina_date(mt.kodaktiva, (select max(datum)+3 from today))=0
				and mt.uctovnykod = 251110
				and mt.obratid = 0
				and not exists(select * from majetoktoday mt2 where mt2.subjektid=mt.subjektid and mt2.uctovnykod=315123 and mt2.eqid=mt.eqid and mt2.kodaktiva=mt.kodaktiva and mt2.ucetaktiva=mt.ucetaktiva and mt2.mena=mt.mena and mt2.md_d=mt.md_d)
				and d.isin = (select max(dc.isin) from dbequitycurr dc, dbequitycurrric dcr where dc.isincurr=dcr.isincurr and dcr.isincurrric=mt.kodaktiva)  
				$fond_restriction
			UNION ALL
			select 
				mt.pocet as stav_majetok,
				mt.subjektid,
				d.cpnazskratka AS skratka,
				d.isin,
				to_char((select max(datum)+4 from today),'DD.MM.YYYY') as datum,
				4 as daystill
			from majetoktoday mt, dbequity d 
			where
				mt.eqid = 'Bonds'
				and is_existina_date(mt.kodaktiva, (select max(datum)+4 from today))=1
				and is_istina_date(mt.kodaktiva, (select max(datum)+4 from today))=0
				and mt.uctovnykod = 251110
				and mt.obratid = 0
				and not exists(select * from majetoktoday mt2 where mt2.subjektid=mt.subjektid and mt2.uctovnykod=315123 and mt2.eqid=mt.eqid and mt2.kodaktiva=mt.kodaktiva and mt2.ucetaktiva=mt.ucetaktiva and mt2.mena=mt.mena and mt2.md_d=mt.md_d)
				and d.isin = (select max(dc.isin) from dbequitycurr dc, dbequitycurrric dcr where dc.isincurr=dcr.isincurr and dcr.isincurrric=mt.kodaktiva)  
				$fond_restriction 
			UNION ALL
			select 
				mt.pocet as stav_majetok,
				mt.subjektid,
				d.cpnazskratka AS skratka,
				d.isin,
				to_char((select max(datum)+5 from today),'DD.MM.YYYY') as datum,
				5 as daystill
			from majetoktoday mt, dbequity d 
			where
				mt.eqid = 'Bonds'
				and is_existina_date(mt.kodaktiva, (select max(datum)+5 from today))=1
				and is_istina_date(mt.kodaktiva, (select max(datum)+5 from today))=0
				and mt.uctovnykod = 251110
				and mt.obratid = 0
				and not exists(select * from majetoktoday mt2 where mt2.subjektid=mt.subjektid and mt2.uctovnykod=315123 and mt2.eqid=mt.eqid and mt2.kodaktiva=mt.kodaktiva and mt2.ucetaktiva=mt.ucetaktiva and mt2.mena=mt.mena and mt2.md_d=mt.md_d)
				and d.isin = (select max(dc.isin) from dbequitycurr dc, dbequitycurrric dcr where dc.isincurr=dcr.isincurr and dcr.isincurrric=mt.kodaktiva)  
				$fond_restriction 
		";

        $result = Connection::getDataFromDatabase($query, defaultDB)[1];
        foreach ($result as $item) {
            $subjektid = $item['subjektid'];
            $datum = $item['datum'];
            $skratka = $item['skratka'];
            $isin = $item['isin'];
            $daystill = $item['daystill'];

            /* Splatny dlhopis */
            if ($daystill > 0) {
                $problem_type = PROBLEM_INFO;
                switch ($daystill) {
                    case 1:
                        $sklonovanie_den = 'deň';
                        break;
                    case 2:
                    case 3:
                    case 4:
                        $sklonovanie_den = 'dni';
                        break;
                    default:
                        $sklonovanie_den = 'dní';
                        break;
                }
                $popis = "Istinu dlhopisu $skratka ($isin) treba potvrdiť o $daystill $sklonovanie_den ($datum).";
                $link = '';
            } else {
                $problem_type = PROBLEM_SERIOUS;
                $popis = "Istinu dlhopisu $skratka ($isin) treba potvrdiť v aktuálny evidenčný deň.";
                $link = ''; //$ROOT.'splatenie/dlhopis_frame.php?id_fond='.$subjektid;
            }
            $this->problems[] = new Problem($subjektid, 'Nepotvrdená istina', $popis, $problem_type, $link, $groups);
            continue;
        }

        /* kup�n po splatnosti */
        $query = "SELECT
				f_last_istinadate(mt.kodaktiva, t.datum) as istdate,
				to_char(f_last_coupondate(mt.kodaktiva, t.datum),'DD.MM.YYYY') as datum,
				t.datum as todaydate,
				(CASE WHEN uctovnykod=315123 THEN 'istina' ELSE 'kupon' END) as typSplatenia, 
				mt.subjektid,
				p.cislozmluvy,
				round(de.nominalemisie * f_kuponfaktor(mt.kodaktiva, f_last_coupondate(mt.kodaktiva, t.datum)-1) / 100 * f_koef_isincurric_auv(mt.kodaktiva, f_last_coupondate(mt.kodaktiva, t.datum), 2 ),COALESCE(de.rounding,25)) as kuponkus,
				de.nominalemisie * f_istina(mt.kodaktiva, f_last_istinadate(mt.kodaktiva, t.datum)) / 100 as istinakus,
				mt.kodaktiva,
				(CASE WHEN mt.uctovnykod=315123 THEN 251110 ELSE 251120 END) as uctovnykod,
				de.cpnazskratka,
				mt.mena,
				de.nominalemisie,
				de.dan,
				de.istfrek,
				mt.pocet,
				de.druheqid,
				de.isin, 
				dc.currencytrade,
				dr.ric,
				mt.ucetaktiva,
				months_diff(maturitydate,dateemisie) as pocetmesiacov,
				to_char(de.maturitydate,'dd.mm.yyyy')as maturitydate,
				de.splatnost as splatnost,
				de.cpnazskratka AS skratka,
				f_last_coupondate(mt.kodaktiva, t.datum) - t.datum as daystill
	
			FROM 
				dbequity de,
				dbequitycurr dc,
				dbequitycurrric dr,
				majetoktoday mt,
				today t, 
				portfolio p
			where
				dr.isincurrric=mt.kodaktiva and
				dr.isincurr=dc.isincurr and
				dc.isin=de.isin and
				mt.eqid='Bonds' and
				mt.subjektid=t.fondid and
				p.fondid = mt.subjektid and
				mt.md_d = 0 and
				not exists(select * from majetoktoday mt2 where mt2.subjektid=mt.subjektid and mt2.eqid=mt.eqid and mt2.ucetaktiva=mt.ucetaktiva and mt2.kodaktiva=mt.kodaktiva and mt2.mena=mt.mena and mt2.uctovnykod=mt.uctovnykod and mt2.md_d=1)
				and mt.uctovnykod = 315124
				and (NOT is_coupon_date(mt.kodaktiva, t.datum)=1 OR is_coupon_date(mt.kodaktiva, t.datum)=1)
		";

        $result = Connection::getDataFromDatabase($query, defaultDB)[1];
        foreach ($result as $item) {
            $subjektid = $item['subjektid'];
            $datum = $item['datum'];
            $skratka = $item['skratka'];
            $isin = $item['isin'];
            $daystill = $item['daystill'];
            $days = -1 * $daystill;
            if ($daystill < 0) {
                $problem_type = PROBLEM_INFO;
                switch ($days) {
                    case 1:
                        $sklonovanie_den = 'deň';
                        break;
                    case 2:
                    case 3:
                    case 4:
                        $sklonovanie_den = 'dni';
                        break;
                    default:
                        $sklonovanie_den = 'dní';
                        break;
                }
                $problem_type = PROBLEM_WARNING;
                $popis = "Kupón dlhopisu $skratka ($isin) je $days $sklonovanie_den po splatnosti.";
                $link = '';  //$ROOT.'splatenie/dlhopis_frame.php?id_fond='.$subjektid;

                $this->problems[] = new Problem($subjektid, 'Kupón po splatnosti', $popis, $problem_type, $link, $groups);
                continue;
            }
        }
        /* istina po splatnosti */
        $query = "SELECT
				to_char(f_last_istinadate(mt.kodaktiva, t.datum),'DD.MM.YYYY') as datum,
				f_last_coupondate(mt.kodaktiva, t.datum) as kupdate,
				t.datum as todaydate,
				(CASE WHEN uctovnykod=315123 THEN 'istina' ELSE 'kupon' END) as typSplatenia, 
				mt.subjektid,
				p.cislozmluvy,
				round(de.nominalemisie * f_kuponfaktor(mt.kodaktiva, f_last_coupondate(mt.kodaktiva, t.datum)-1) / 100 * f_koef_isincurric_auv(mt.kodaktiva, f_last_coupondate(mt.kodaktiva, t.datum), 2 ),COALESCE(de.rounding,25)) as kuponkus,
				de.nominalemisie * f_istina(mt.kodaktiva, f_last_istinadate(mt.kodaktiva, t.datum)) / 100 as istinakus,
				mt.kodaktiva,
				(CASE WHEN mt.uctovnykod=315123 THEN 251110 ELSE 251120 END) as uctovnykod,
				de.cpnazskratka,
				mt.mena,
				de.nominalemisie,
				de.dan,
				de.istfrek,
				mt.pocet,
				de.druheqid,
				de.isin, 
				dc.currencytrade,
				dr.ric,
				mt.ucetaktiva,
				months_diff(maturitydate,dateemisie) as pocetmesiacov,
				to_char(de.maturitydate,'dd.mm.yyyy')as maturitydate,
				de.splatnost as splatnost,
				de.cpnazskratka AS skratka,
				f_last_istinadate(mt.kodaktiva, t.datum) - t.datum as daystill
	
			FROM 
				dbequity de,
				dbequitycurr dc,
				dbequitycurrric dr,
				majetoktoday mt,
				today t, 
				portfolio p
			where
				dr.isincurrric=mt.kodaktiva and
				dr.isincurr=dc.isincurr and
				dc.isin=de.isin and
				mt.eqid='Bonds' and
				mt.subjektid=t.fondid and
				p.fondid = mt.subjektid and
				mt.md_d = 0 and
				not exists(select * from majetoktoday mt2 where mt2.subjektid=mt.subjektid and mt2.eqid=mt.eqid and mt2.ucetaktiva=mt.ucetaktiva and mt2.kodaktiva=mt.kodaktiva and mt2.mena=mt.mena and mt2.uctovnykod=mt.uctovnykod and mt2.md_d=1)
				and mt.uctovnykod = 315123		
				and (NOT is_existina(mt.kodaktiva, t.datum)=1 OR is_istina_date(mt.kodaktiva, t.datum)=1)
		";

        $result = Connection::getDataFromDatabase($query, defaultDB)[1];
        foreach ($result as $item) {
            $subjektid = $item['subjektid'];
            $datum = $item['datum'];
            $skratka = $item['skratka'];
            $isin = $item['isin'];
            $daystill = $item['daystill'];
            $days = -1 * $daystill;

            if ($daystill < 0) {
                $problem_type = PROBLEM_INFO;
                switch ($days) {
                    case 1:
                        $sklonovanie_den = 'deň';
                        break;
                    case 2:
                    case 3:
                    case 4:
                        $sklonovanie_den = 'dni';
                        break;
                    default:
                        $sklonovanie_den = 'dní';
                        break;
                }
                $problem_type = PROBLEM_WARNING;
                $popis = "Istina dlhopisu $skratka ($isin) je $days $sklonovanie_den po splatnosti.";
                $link = '';  //$ROOT.'splatenie/dlhopis_frame.php?id_fond='.$subjektid;

                $this->problems[] = new Problem($subjektid, 'Istina po splatnosti', $popis, $problem_type, $link, $groups);
                continue;
            }
        }

    }
}


class KuponIstinaFVysporiadanieDetector extends DetectorClass
{
    function doCheck($id_fond = '')
    {
        global $config;
        $groups = array(1, 3, 4);
        $fond_restriction = "";
        if ($id_fond != '') {
            $fond_restriction = "
				and k.subjektid = $id_fond
			";
        }
        $query = "SELECT mc.subjektid, mc.pocet, mc.popis, mc.ucetaktiva, ob.logactivityid
			from majetokcesta mc, obratybu ob
			where mc.eqid = 'BU'
			and mc.destinacia = 'dbequity'
			and mc.in_out = 1
			and ob.krdb = 1
			and ob.logactivityid in (1,4,6)
			and ob.suma = mc.pocet
      			and ob.subjektid = mc.subjektid";

        $items = Connection::getDataFromDatabase($query, defaultDB)[1];
        foreach ($items as $item) {
            $subjektid = $item['subjektid'];
            $popis = $item['popis'];
            $ucet = $item['ucetaktiva'];
            $logactivityid = $item['logactivityid'];
            $link = '';
            $cinnost = '';
            if ($logactivityid == 1) {
                $link = "platby/dosle_platby_natip_frame.php?id_fond=" . $item['subjektid'];
                $cinnost = 'natipovať';
            } else {
                $link = "platby/dosle_platby_spar_frame.php?id_fond=" . $item['subjektid'];
                $cinnost = 'spárovať';
            }

            $this->problems[] = new Problem(
                $subjektid,
                $popis,
                $popis . ' na účte ' . $ucet . ' je potrebné finančne vysporiadať (' . $cinnost . ') v aktuálny evidenčný dátum.',
                PROBLEM_SERIOUS,
                $link,
                $groups
            );
            continue;
        }
    }
}

class DividendaFVysporiadanieDetector extends DetectorClass
{
    function doCheck($id_fond = '')
    {
        global $config;
        $groups = array(1, 3, 4);
        $fond_restriction = "";
        if ($id_fond != '') {
            $fond_restriction = "
				and k.subjektid = $id_fond
			";
        }
        $query = "SELECT mc.subjektid, mc.pocet, mc.popis, mc.ucetaktiva, ob.logactivityid
			from majetokcesta mc, obratybu ob
			where mc.eqid = 'BU'
			and mc.destinacia = 'splatenieakcia'
			and mc.in_out = 1
			and ob.krdb = 1
			and ob.logactivityid in (1,4,6)
			and ob.suma = mc.pocet
      			and ob.subjektid = mc.subjektid";

        $result = Connection::getDataFromDatabase($query, defaultDB)[1];
        foreach ($result as $item) {
            $subjektid = $item['subjektid'];
            $popis = $item['popis'];
            $ucet = $item['ucetaktiva'];
            $logactivityid = $item['logactivityid'];
            $link = '';
            $cinnost = '';
            if ($logactivityid == 1) {
                $link = "platby/dosle_platby_natip_frame.php?id_fond=" . $item['subjektid'];
                $cinnost = 'natipovať';
            } else {
                $link = "platby/dosle_platby_spar_frame.php?id_fond=" . $item['subjektid'];
                $cinnost = 'spárovať';
            }

            $this->problems[] = new Problem(
                $subjektid,
                $popis,
                'Dividendu na účte ' . $ucet . ' je potrebné finančne vysporiadať (' . $cinnost . ') v aktuálny evidenčný dátum.',
                PROBLEM_SERIOUS,
                $link,
                $groups
            );
            continue;
        }
    }
}

?>
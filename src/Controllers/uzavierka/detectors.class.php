<?php
require_once "check.class.php";

/* Float kupony */
require_once "detectors/float_kupon_detector.php";
$kontrola->addDetectorClass('FloatKuponDetector');

/* <PERSON><PERSON><PERSON> dlhopisov */
if ($config['AUV']['KalendarSplacania']) {
	require_once "detectors/security_maturity_detector3.php";
} elseif ($config['AUV']['NestandardnyKupon']) {
	echo "tu2";
	require_once "detectors/security_maturity_detector2.php";
} else {
	echo "tu3";
	require_once "detectors/security_maturity_detector.php";
}

$kontrola->addDetectorClass('SecurityMaturityDetector');

/* Splatenie non-delivery forward kuponu */
require_once "detectors/forward_maturity_detector.php";
$kontrola->addDetectorClass('ForwardMaturityDetector');

if ($uzavierka_check == true) {
	echo "JETOPRAVDA";
	/* Majetkove pripisanie KTV */
	require_once "detectors/new_ktv_detector.php";
	$kontrola->addDetectorClass('NewKtvDetector');
	$kontrola->addDetectorClass('NewKtvDetector2');
	$kontrola->addDetectorClass('NewKtvDetector3');
	$kontrola->addDetectorClass('NewKtvDetector4');

	/* Majetkova uhrada KTV */
	require_once "detectors/ktv_maturity_detector.php";
	$kontrola->addDetectorClass('KtvMaturityDetector');
	$kontrola->addDetectorClass('KtvMaturityDetector2');
	$kontrola->addDetectorClass('KtvMaturityDetector3');
}

require_once "detectors/bezne_ucty_detector.php";

// zaporne zostatky na beznych uctoch
$kontrola->addDetectorClass('BezneUctyDetector');

// zhoda poctu istin s poctom AUV
require_once "detectors/dlhopisy_detector.php";
$kontrola->addDetectorClass('DlhopisyDetector');


// nepotvrdene obchody s CP
$kontrola->addDetectorClass('NewTradeDetector');
$kontrola->addDetectorClass('NewTradeDetector4');
$kontrola->addDetectorClass('NewKonverziaDetector');
$kontrola->addDetectorClass('NewKonverziaDetector2');
$kontrola->addDetectorClass('NewKonverziaDetector3');
$kontrola->addDetectorClass('NewKonverziaDetector4');

require_once "detectors/vysporiadanie_detector.php";

// financne vysporiadanie dividend
$kontrola->addDetectorClass('DividendaFVysporiadanieDetector');
// financne vysporiadanie kuponov a istin
$kontrola->addDetectorClass('KuponIstinaFVysporiadanieDetector');
// financne vysporiadanie vkladov
$kontrola->addDetectorClass('VkladFVysporiadanieDetector');

// majetkove vysporiadanie dosle aktiva
$kontrola->addDetectorClass('MVysporiadanieDosleAktivaDetector');
// majetkove vysporiadanie odchadzajuce aktiva
$kontrola->addDetectorClass('MVysporiadanieOdchadzajuceAktivaDetector');

// financne vysporiadanie uhrady
$kontrola->addDetectorClass('FVysporiadanieUhradyDetector');
// financne vysporiadanie dosle platby
$kontrola->addDetectorClass('FVysporiadanieDoslePlatbyDetector');

?>
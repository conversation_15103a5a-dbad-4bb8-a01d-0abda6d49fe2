<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
require_once "/home/<USER>/www/src/lib/notifications/Notification.class.php";

$errors = [];

$sess_userid = $_SESSION["user"]["data"]["userid"];
$username = $_SESSION["user"]["data"]["username"];

$datum = Connection::getDataFromDatabase("SELECT max(datum) as datum from today", defaultDB)[1][0]["datum"];
$pomerNominalu = "(case when e.splatnost in (2,3) then 1 else 1-floor(months_between($datum,e.dateemisie)/e.istfrek)/months_between(e.maturitydate,e.dateemisie)*e.istfrek end)";

$queryPool = [];
//PRI NEKUSOVYCH POLOZKACH SA NASTAVUJE KURZAKTIVA NA 1
$queryPool[] = [
	"query" => "UPDATE majetoktotal SET kurzaktiva = 1 WHERE datum = ? AND jednotka NOT LIKE 'ks'",
	"params" => [$datum],
	"db" => defaultDB,
	"name" => "Nastavenie kurzaktiva na 1 pre nekusové položky",
];
//PRI KUSOVYCH POLOZKACH SA NASTAVUJE KURZAKTIVA NA KURZ Z KURZYAKTIVARCHIV
$updateMajetokTotalQuery = "UPDATE majetoktotal mt
			set kurzmen = (
			select ka.kurz
			from kurzyaktivarchiv ka
			where ";

if ($config["Uzavierka"]["NIPPredposlednyKurz"]) {
	$updateMajetokTotalQuery .= "ka.datum = (CASE WHEN mt.uctovnykod=325300 AND (
					SELECT (CASE WHEN b.skratka is not null then 1 else 0 end) 
					FROM banka b, funkcia_banka fb WHERE b.banka_id = fb.banka_id and fb.funkcia_id = 3
				)=1
				THEN (select max(datum) from kurzyaktivarchiv where datum < mt.datum) ELSE mt.datum END) and";
} else {
	$updateMajetokTotalQuery .= "ka.datum = mt.datum and";
}

$updateMajetokTotalQuery .= "(mt.menadenom || mt.menaref||(
					CASE WHEN mt.uctovnykod = 325300 THEN COALESCE((
						SELECT (CASE WHEN b.skratka is not null then '@'|| b.skratka else '' end) FROM banka b, funkcia_banka fb
						WHERE b.banka_id = fb.banka_id and fb.funkcia_id = 3
						),'') ELSE '' END
					) like ka.ric or 
				mt.menaref || mt.menadenom||(
					CASE WHEN mt.uctovnykod = 325300 THEN COALESCE((
						SELECT (CASE WHEN b.skratka is not null then '@'|| b.skratka else '' end) FROM banka b, funkcia_banka fb
						WHERE b.banka_id = fb.banka_id and fb.funkcia_id = 3
						),'') ELSE '' END
					) like ric)
	)
	where
		mt.datum = '$datum'";

$queryPool[] = [
	"query" => $updateMajetokTotalQuery,
	"params" => [],
	"db" => defaultDB,
	"name" => "Nastavenie kurzaktiva pre kusové položky",
];

// KED SA ZHODUJU MENA A REFERENCNA MENA kurz = 1
$queryPool[] = [
	"query" => "UPDATE majetoktotal SET kurzmen = 1 WHERE datum = ? AND menadenom = menaref",
	"params" => [$datum],
	"db" => defaultDB,
	"name" => "Nastavenie kurzmen na 1 pre zhodné meny",
];

// FORWARDOVE KURZY
$queryPool[] = [
	"query" => "UPDATE majetoktotal mt
		SET kurzmen = COALESCE((
 		SELECT
 			ka.kurz
 		FROM
 			kurzyaktivarchiv ka, konverzia ko
 		WHERE
 			ka.ric = 'NDF-'||ko.dealid and
 			ka.datum = mt.datum and
 			ka.ric = 'NDF-'||mt.kodaktiva
 	), kurzmen)
	where
		mt.datum = '$datum' and
		uctovnykod in (select uctovnykod from kodobratumd_d where kodobratu in (178,179)) and
		menadenom<>menaref",
	"params" => [],
	"db" => defaultDB,
	"name" => "Nastavenie kurzmen pre forwardove kurzy",
];

// VYPOCET SUMADENOM PRE VSETKY POLOZKY V MAJETOKTOTAL PRENASOBENIM pocet * kurz
$queryPool[] = [
	"query" => "UPDATE majetoktotal SET sumadenom = pocet * kurzaktiva WHERE datum = ?",
	"params" => [$datum],
	"db" => defaultDB,
	"name" => "Výpočet sumadenom pre všetky položky v majetoktotal",
];

// VYPOCET SUMADENOM PRE AUV DLHOPISOV
if ($config["AUV"]["KalendarSplacania"]) {
	$queryPool[] = [
		"query" => "UPDATE majetoktotal mt
			SET sumadenom = COALESCE(pocet *
				(select 
					round(e.nominalemisie *
						f_kuponfaktor(mt.kodaktiva, '$datum'::date) / 100 * 
						f_koef_isincurric_auv(mt.kodaktiva, '$datum'::date, 0 ),COALESCE(rounding,25)
					) 
				from dbequity e, dbequitycurr c, dbequitycurrric r
				where
					e.isin = c.isin	and
					c.isincurr = r.isincurr	and
					r.isincurrric =	mt.kodaktiva
		 		),0)
		where
			datum = '$datum' and
			eqid like 'Bonds' and
			uctovnykod in
			(
				select uctovnykod from kodobratumd_d where kodobratu = 703 and md_d = 0
				union all
				select uctovnykod from kodobratumd_d where kodobratu = 137 and md_d = 1
				union all
				select uctovnykod from kodobratumd_d where kodobratu = 252 and md_d = 0
				union all
				select uctovnykod from kodobratumd_d where kodobratu = 352 and md_d = 1
			)",
		"params" => [],
		"db" => defaultDB,
		"name" => "Výpočet sumadenom pre AUV dlhopisov",
	];

	//POHLADAVKA NA KUPON V OBDObI EXKUPONU - OCENUJE SA PLNYM AUV
	$queryPool[] = [
		"query" => "UPDATE majetoktotal mt
			SET sumadenom = pocet *
				(select 
					round(e.nominalemisie *
						f_kuponfaktor(mt.kodaktiva, f_last_coupondate(mt.kodaktiva, '$datum'::date)-1) / 100 * 
						f_koef_isincurric_auv(mt.kodaktiva, f_last_coupondate(mt.kodaktiva, '$datum'::date), 2 ),COALESCE(e.rounding,25)
					) 
				from dbequity e, dbequitycurr c, dbequitycurrric r
				where
					e.isin = c.isin	and
					c.isincurr = r.isincurr	and
					r.isincurrric =	mt.kodaktiva
		 		)
		where
			datum = '$datum' and
			eqid like 'Bonds' and
			uctovnykod = 315124",
		"params" => [],
		"db" => defaultDB,
		"name" => "Výpočet sumadenom pre pohladavku na kupon v obdobi exkuponu",
	];
} else {
	$queryPool[] = [
		"query" => "UPDATE majetoktotal mt
			SET sumadenom = pocet *
				(select e.nominalemisie * $pomerNominalu * (COALESCE((
				 select kupon from floatkupon fk where fk.isincurrric = mt.kodaktiva and
				 		 fk.datefrom<=mt.datum and fk.datetill>=mt.datum 
			),kupon)/100) * f_koef_auv(e.isin, $datum)
				from dbequity e, dbequitycurr c, dbequitycurrric r
				where
					e.isin = c.isin	and
					c.isincurr = r.isincurr	and
					r.isincurrric =	mt.kodaktiva
	 		)
	where
		datum = '$datum' and
		eqid like 'Bonds' and
		uctovnykod in
		(
			select uctovnykod from kodobratumd_d where kodobratu = 703 and md_d = 0
			union all
			select uctovnykod from kodobratumd_d where kodobratu = 137 and md_d = 1
			union all
			select uctovnykod from kodobratumd_d where kodobratu = 252 and md_d = 0
			union all
			select uctovnykod from kodobratumd_d where kodobratu = 352 and md_d = 1
		)",
		"params" => [],
		"db" => defaultDB,
		"name" => "Výpočet sumadenom pre AUV dlhopisov",
	];
}

$queryPool[] = [
	"query" => "UPDATE majetoktotal mt
		SET sumadenom = (COALESCE((
		SELECT 
			sum(pdr.auvreal-COALESCE((
				SELECT
					pdr2.auvreal
				FROM
					rekonfirmaciacpobratid rc2,
					majetokarchiv ma2,
					pool p2,
					pooldetailreal pdr2
				WHERE
					rc2.dealid = rcp.dealid and
					rc2.obratid = ma2.obratid and
					ma2.kodobratu = 252 and
					ma2.md_d = 1 AND
					ma2.subjektid = mt.subjektid AND
					p2.dealid = rcp.dealid AND
					p2.poolid = pdr2.poolid AND
					pdr2.subjektid = ma2.subjektid
			),0))
		FROM 
			rekonfirmaciacp rcp, 
			rekonfirmaciacpobratid rc1,
			majetokarchiv ma1,
			pool p,
			pooldetailreal pdr
		WHERE
			rcp.dealid = rc1.dealid and 
			rc1.obratid = ma1.obratid and
			ma1.kodobratu = 123 and
			ma1.md_d = 0 and
			ma1.subjektid = mt.subjektid and
			ma1.kodaktiva = mt.kodaktiva AND
			p.dealid = rcp.dealid AND
			p.poolid = pdr.poolid AND
			pdr.subjektid = ma1.subjektid
	),0)+ COALESCE((
		SELECT 
			sum(rcp.auvreal-COALESCE((
				SELECT
					rcp.auvreal
				FROM
					rekonfirmaciacpobratid rc2,
					majetokarchiv ma2
				WHERE
					rc2.dealid = rcp.dealid and
					rc2.obratid = ma2.obratid and
					ma2.kodobratu = 252 and
					ma2.md_d = 1 AND
					ma2.subjektid = mt.subjektid
			),0))
		FROM 
			rekonfirmaciacp rcp, 
			rekonfirmaciacpobratid rc1,
			majetokarchiv ma1
		WHERE
			rcp.dealid = rc1.dealid and 
			rc1.obratid = ma1.obratid and
			ma1.kodobratu = 123 and
			ma1.md_d = 0 and
			ma1.subjektid = mt.subjektid and
			ma1.kodaktiva = mt.kodaktiva AND
			rcp.dealid not in (
				SELECT
					dealid
				FROM
					pool
				WHERE
					dealid IS NOT NULL
			) 
	),0))
	where
		datum = '$datum' and
		eqid like 'Bonds' and
		uctovnykod = 315122",
	"params" => [],
	"db" => defaultDB,
	"name" => "Neviem",
];

$queryPool[] = [
	"query" => "UPDATE majetoktotal mt 
	SET sumadenom = (COALESCE((
		SELECT 
			sum(pdr.auvreal-COALESCE((
				SELECT
					pdr2.auvreal
				FROM
					rekonfirmaciacpobratid rc2,
					majetokarchiv ma2,
					pool p2,
					pooldetailreal pdr2
				WHERE
					rc2.dealid = rcp.dealid and
					rc2.obratid = ma2.obratid and
					ma2.kodobratu = 352 and
					ma2.md_d = 0 AND
					ma2.subjektid = mt.subjektid AND
					p2.dealid = rcp.dealid AND
					p2.poolid = pdr2.poolid AND
					pdr2.subjektid = ma2.subjektid
			),0))
		FROM 
			rekonfirmaciacp rcp, 
			rekonfirmaciacpobratid rc1,
			majetokarchiv ma1,
			pool p,
			pooldetailreal pdr
		WHERE
			rcp.dealid = rc1.dealid and 
			rc1.obratid = ma1.obratid and
			ma1.kodobratu = 133 and
			ma1.md_d = 1 and
			ma1.subjektid = mt.subjektid and
			ma1.kodaktiva = mt.kodaktiva AND
			p.dealid = rcp.dealid AND
			p.poolid = pdr.poolid AND
			pdr.subjektid = ma1.subjektid
		),0)+ COALESCE((
		SELECT 
			sum(rcp.auvreal-COALESCE((
				SELECT
					rcp.auvreal
				FROM
					rekonfirmaciacpobratid rc2,
					majetokarchiv ma2
				WHERE
					rc2.dealid = rcp.dealid and
					rc2.obratid = ma2.obratid and
					ma2.kodobratu = 352 and
					ma2.md_d = 0 AND
					ma2.subjektid = mt.subjektid
			),0))
		FROM 
			rekonfirmaciacp rcp, 
			rekonfirmaciacpobratid rc1,
			majetokarchiv ma1
		WHERE
			rcp.dealid = rc1.dealid and 
			rc1.obratid = ma1.obratid and
			ma1.kodobratu = 133 and
			ma1.md_d = 1 and
			ma1.subjektid = mt.subjektid and
			ma1.kodaktiva = mt.kodaktiva AND
			rcp.dealid not in (
				SELECT
					dealid
				FROM
					pool
				WHERE
					dealid IS NOT NULL
			) 
		),0))
	where
		datum = '$datum' and
		eqid like 'Bonds' and
		uctovnykod = 325122",
	"params" => [],
	"db" => defaultDB,
	"name" => "Neviem",
];

//VYPOCET SUMADENOM PRE NOMINALY
if ($config["AUV"]["KalendarSplacania"]) {
	$queryPool[] = [
		"query" => "UPDATE majetoktotal mt
		SET sumadenom = pocet * kurzaktiva/100 * f_istinafaktor(mt.kodaktiva, mt.datum) *
			(select e.nominalemisie  
				from dbequity e, dbequitycurr c, dbequitycurrric r
				where
					e.isin = c.isin	and
					c.isincurr = r.isincurr	and
					r.isincurrric =	mt.kodaktiva
		 		)
		where
			datum = '$datum' and
			eqid like 'Bonds' and
			uctovnykod in
	 		(
				select uctovnykod from kodobratumd_d where kodobratu = 351 and md_d = 0
				union all
				select uctovnykod from kodobratumd_d where kodobratu = 351 and md_d = 1
				union all
				select uctovnykod from kodobratumd_d where kodobratu = 251 and md_d = 0
				union all
				select uctovnykod from kodobratumd_d where kodobratu = 251 and md_d = 1
				union all
				select uctovnykod from kodobratumd_d where kodobratu = 136 and md_d = 1
				union all
				select uctovnykod from kodobratumd_d where kodobratu = 131 and md_d = 1
				union all
				select uctovnykod from kodobratumd_d where kodobratu = 121 and md_d = 0
			)",
		"params" => [],
		"db" => defaultDB,
		"name" => "Výpočet sumadenom pre nominaly",
	];

	//POHLADAVKA NA ISTINU V OBDOBI EXISTINY - OCENUJE SA SPLACANOU ISTINOU
	$queryPool[] = [
		"query" => "UPDATE majetoktotal mt
		SET sumadenom = pocet *
				(select e.nominalemisie * f_istina(mt.kodaktiva, f_last_istinadate(mt.kodaktiva, '$datum'::date))  
				from dbequity e, dbequitycurr c, dbequitycurrric r
				where
					e.isin = c.isin	and
					c.isincurr = r.isincurr	and
					r.isincurrric =	mt.kodaktiva
		 		)/100
		where
			datum = '$datum' and
			eqid like 'Bonds' and
			uctovnykod = 315123",
		"params" => [],
		"db" => defaultDB,
		"name" => "POHLADAVKA NA ISTINU V OBDOBI EXISTINY - OCENUJE SA SPLACANOU ISTINOU",
	];
} else {
	$queryPool[] = [
		"query" => "UPDATE majetoktotal mt
			SET sumadenom = pocet * kurzaktiva/100 *
	 		(
	 			select e.nominalemisie * $pomerNominalu
	 			from
	 				dbequity e,
	 				dbequitycurr c,
	 				dbequitycurrric r
				where
					e.isin = c.isin and
					c.isincurr = r.isincurr	and
					r.isincurrric =	mt.kodaktiva
			)
	where
		datum = '$datum' and
		eqid like 'Bonds' and
		uctovnykod in
 		(
			select uctovnykod from kodobratumd_d where kodobratu = 351 and md_d = 0
			union all
			select uctovnykod from kodobratumd_d where kodobratu = 351 and md_d = 1
			union all
			select uctovnykod from kodobratumd_d where kodobratu = 251 and md_d = 0
			union all
			select uctovnykod from kodobratumd_d where kodobratu = 251 and md_d = 1
			union all
			select uctovnykod from kodobratumd_d where kodobratu = 136 and md_d = 1
			union all
			select uctovnykod from kodobratumd_d where kodobratu = 131 and md_d = 1
			union all
			select uctovnykod from kodobratumd_d where kodobratu = 121 and md_d = 0
		)",
		"params" => [],
		"db" => defaultDB,
		"name" => "Bez komentára",
	];
}

//VYPOCET SUMAREF PRE VSETKY POLOZKY KDE SU ROVNAKE MENY
$queryPool[] = [
	"query" => "UPDATE majetoktotal
		SET sumaref = COALESCE((sumadenom * kurzmen), 0)
		WHERE datum = '$datum' AND menadenom = menaref",
	"params" => [],
	"db" => defaultDB,
	"name" => "Výpočet sumaref pre vsetky polozky, kde su rovnake meny",
];

//VYPOCET SUMAREF PRE VSETKY POLOZKY, KDE SA KOMBINACIA MENADENOM A MENAREF ROVNA MENOVÉMU PARU (IDE O NASOBIENIE KURZOM)
$queryPool[] = [
	"query" => "UPDATE majetoktotal
		SET sumaref = COALESCE(sumadenom * (kurzmen*(select max(currencylot) from state s where s.currencycode=menaref) / (select max(currencylot) from state s where s.currencycode=menadenom)),0)
	where
		datum = '$datum' and
		menadenom || menaref in (select ric from kurzyaktivarchiv where datum = '$datum')",
	"params" => [],
	"db" => defaultDB,
	"name" => "Výpočet sumaref pre vsetky, kde sa kombinacia menadenom a menaref rovna menovemu paru (ide o nasobenie kurzom)",
];

//VYPOCET SUMAREF PRE VSETKY POLOZKY, KDE SA KOMBINACIA MENADENOM A MENAREF ROVNA MENOVÉMU PARU (IDE O DELENIE KURZOM)
$queryPool[] = [
	"query" => "UPDATE majetoktotal
		SET sumaref = COALESCE(sumadenom / (kurzmen * (select max(currencylot) from state s where s.currencycode=menadenom)/(select max(currencylot) from state s where s.currencycode=menaref)),0)
	where
		datum = '$datum' and
		menaref	|| menadenom in (select ric from kurzyaktivarchiv where datum = '$datum')",
	"params" => [],
	"db" => defaultDB,
	"name" => "Výpočet sumaref pre vsetky, kde sa kombinacia menaref a menadenom rovna menovemu paru (ide o delenie kurzom)",
];

$queryPool[] = [
	"query" => "UPDATE majetoktotal 
		SET sumaref = COALESCE(round((f_menovy_kurz_kriz(menadenom::text,menaref::text,'$datum'::date,1::int)*sumadenom),2),0)
		where datum = ?",
	"params" => [$datum],
	"db" => defaultDB,
	"name" => "Neviem"
];

//ZAPIS VYPOCITANYCH HODNOT NAV DO pricestore
$queryPool[] = [
	"query" => "INSERT INTO pricestore (datum, fondid, nav, hm, refmena)
	(
		select '$datum', t.fondid, COALESCE(sum(sumaref),0) as nav,
		COALESCE(sum(sumaref_hm),0) as hm, (select refmena from fonds where fondid=t.fondid) as refmena
		from
		(
			select md_d1 * sumaref as sumaref,
			md_d1 * (CASE WHEN 
				(mt.uctovnykod between 261000 and 261999) or -- Peniaze na ceste
				(mt.uctovnykod between 315000 and 315999) or -- Pohladavky 
				(mt.uctovnykod between 325000 and 325999) -- Zavazky
				THEN 0 ELSE sumaref END) as sumaref_hm, subjektid
			from majetoktotal$depo mt, navuctovanie nu
			where
				mt.uctovnykod = nu.uctovnykod and
				md_d = 1 and
				mt.datum = '$datum'
			union all
			select md_d0 * sumaref as sumaref,
			md_d0 * (CASE WHEN 
				(mt.uctovnykod between 261000 and 261999) or -- Peniaze na ceste
				(mt.uctovnykod between 315000 and 315999) or -- Pohladavky 
				(mt.uctovnykod between 325000 and 325999) -- Zavazky
				THEN 0 ELSE sumaref END) as sumaref_hm, subjektid
			from majetoktotal$depo mt, navuctovanie nu
			where
				mt.uctovnykod = nu.uctovnykod and
				md_d = 0 and
				mt.datum = '$datum'
		) m, today t
		where
			m.subjektid = t.fondid
		group by t.fondid
	)",
	"params" => [],
	"db" => defaultDB,
	"name" => "Zápis vypočítaných hodnôt NAV do pricestore",
];

$transaction = Connection::runTransaction(defaultDB, null, $queryPool);
if (str_contains($transaction, "poriadku")) { ?>
	<section class="flex gap-2">
		<ol class="relative text-gray-500 border-s border-gray-200 dark:border-gray-700 dark:text-gray-400">
			<li class="mb-10 ms-6">
				<span
					class="absolute flex items-center justify-center w-8 h-8 bg-green-200 rounded-full -start-4 ring-4 ring-white dark:ring-gray-900 dark:bg-green-900">
					<svg class="w-3.5 h-3.5 text-green-500 dark:text-green-400" aria-hidden="true"
						xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 16 12">
						<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
							d="M1 5.917 5.724 10.5 15 1.5" />
					</svg>
				</span>
				<div class="ml-3">
					<h3 class="font-medium leading-tight">Odhlásenie používateľov</h3>
					<p class="text-sm">Všetci používatelia boli odhlásení.</p>
				</div>
			</li>
			<li class="mb-10 ms-6">
				<span
					class="absolute flex items-center justify-center w-8 h-8 bg-green-200 rounded-full -start-4 ring-4 ring-white dark:ring-gray-900 dark:bg-green-900">
					<svg class="w-3.5 h-3.5 text-green-500 dark:text-green-400" aria-hidden="true"
						xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 16 12">
						<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
							d="M1 5.917 5.724 10.5 15 1.5" />
					</svg>
				</span>
				<div class="ml-3">
					<h3 class="font-medium leading-tight">Menové páry</h3>
					<p class="text-sm">Menové páry sú kompletné.</p>
				</div>
			</li>
			<li class="mb-10 ms-6">
				<span
					class="absolute flex items-center justify-center w-8 h-8 bg-green-200 rounded-full -start-4 ring-4 ring-white dark:ring-gray-900 dark:bg-green-900">
					<svg class="w-3.5 h-3.5 text-green-500 dark:text-green-400" aria-hidden="true"
						xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 16 12">
						<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
							d="M1 5.917 5.724 10.5 15 1.5" />
					</svg>
				</span>
				<div class="ml-3">
					<h3 class="font-medium leading-tight">Kurzy</h3>
					<p class="text-sm">Ocenenie BU a CP</p>
				</div>
			</li>
			<li class="mb-10 ms-6">
				<span
					class="absolute flex items-center justify-center w-8 h-8 bg-green-200 rounded-full -start-4 ring-4 ring-white dark:ring-gray-900 dark:bg-green-900">
					<svg class="w-3.5 h-3.5 text-green-500 dark:text-green-400" aria-hidden="true"
						xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 16 12">
						<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
							d="M1 5.917 5.724 10.5 15 1.5" />
					</svg>
				</span>
				<div class="ml-3">
					<h3 class="font-medium leading-tight">Hlavný proces uzávierky</h3>
					<p class="text-sm">Uzávierka bola úspešne dokončená.</p>
				</div>
			</li>
			<li class="mb-10 ms-6">
				<span
					class="absolute flex items-center justify-center w-8 h-8 bg-green-200 rounded-full -start-4 ring-4 ring-white dark:ring-gray-900 dark:bg-green-900">
					<svg class="w-3.5 h-3.5 text-green-500 dark:text-green-400" aria-hidden="true"
						xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 16 12">
						<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
							d="M1 5.917 5.724 10.5 15 1.5" />
					</svg>
				</span>
				<div class="ml-3">
					<h3 class="font-medium leading-tight">Ocenenie</h3>
					<p class="text-sm">Príprava ocenenia</p>
				</div>
			</li>
			<li class="mb-10 ms-6">
				<span
					class="absolute flex items-center justify-center w-8 h-8 bg-gray-100 rounded-full -start-4 ring-4 ring-white dark:ring-gray-900 dark:bg-gray-700">
					<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
						stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
						class="w-3.5 h-3.5 animate-spin text-gray-500 dark:text-gray-400">
						<path d="M21 12a9 9 0 1 1-6.219-8.56" />
					</svg>
				</span>
				<div class="ml-3">
					<h3 class="font-medium leading-tight">Generovanie reportov</h3>
					<p class="text-sm">Generovanie reportov a odosielanie emailov</p>
				</div>
			</li>
			<li class="ms-6">
				<span
					class="absolute flex items-center justify-center w-8 h-8 bg-gray-100 rounded-full -start-4 ring-4 ring-white dark:ring-gray-900 dark:bg-gray-700">
					<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
						stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
						class="w-3.5 h-3.5 text-gray-500 dark:text-gray-400">
						<path d="M20 6 9 17l-5-5" />
					</svg>
				</span>
				<div class="ml-3">
					<h3 class="font-medium leading-tight">Konfirmácia</h3>
					<p class="text-sm">Zhrnutie uzávierky a jej potvrdenie</p>
					<div class="ml-3"></div>
			</li>
		</ol>
		<div class="flex flex-col gap-6 items-center justify-center w-full">
			<svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" viewBox="0 0 24 24" fill="none"
				stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
				class="lucide lucide-loader-circle-icon animate-spin lucide-loader-circle">
				<path d="M21 12a9 9 0 1 1-6.219-8.56" />
			</svg>
			<span>Pripravujem reporty...</span>
		</div>
		<script>
			htmx.ajax('GET', "/uzavierka/process/report-pokles-nav", {
				target: "#uzavierka-modal-body"
			});
		</script>
	</section>
<?php } else { ?>
	<section class="flex gap-2">
		<ol class="relative text-gray-500 border-s border-gray-200 dark:border-gray-700 dark:text-gray-400">
			<li class="mb-10 ms-6">
				<span
					class="absolute flex items-center justify-center w-8 h-8 bg-green-200 rounded-full -start-4 ring-4 ring-white dark:ring-gray-900 dark:bg-green-900">
					<svg class="w-3.5 h-3.5 text-green-500 dark:text-green-400" aria-hidden="true"
						xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 16 12">
						<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
							d="M1 5.917 5.724 10.5 15 1.5" />
					</svg>
				</span>
				<div class="ml-3 text-red-400">
					<h3 class="font-medium leading-tight">Odhlásenie používateľov</h3>
					<p class="text-sm">Všetci používatelia boli odhlásení.</p>
				</div>
			</li>
			<li class="mb-10 ms-6">
				<span
					class="absolute flex items-center justify-center w-8 h-8 bg-green-200 rounded-full -start-4 ring-4 ring-white dark:ring-gray-900 dark:bg-green-900">
					<svg class="w-3.5 h-3.5 text-green-500 dark:text-green-400" aria-hidden="true"
						xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 16 12">
						<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
							d="M1 5.917 5.724 10.5 15 1.5" />
					</svg>
				</span>
				<div class="ml-3">
					<h3 class="font-medium leading-tight">Menové páry</h3>
					<p class="text-sm">Menové páry sa nepodarilo získať a overiť.</p>
				</div>
			</li>
			<li class="mb-10 ms-6">
				<span
					class="absolute flex items-center justify-center w-8 h-8 bg-green-200 rounded-full -start-4 ring-4 ring-white dark:ring-gray-900 dark:bg-green-900">
					<svg class="w-3.5 h-3.5 text-green-500 dark:text-green-400" aria-hidden="true"
						xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 16 12">
						<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
							d="M1 5.917 5.724 10.5 15 1.5" />
					</svg>
				</span>
				<div class="ml-3">
					<h3 class="font-medium leading-tight">Kurzy</h3>
					<p class="text-sm">Ocenenie BU a CP</p>
				</div>
			</li>
			<li class="mb-10 ms-6">
				<span
					class="absolute flex items-center justify-center w-8 h-8 bg-green-200 rounded-full -start-4 ring-4 ring-white dark:ring-gray-900 dark:bg-green-900">
					<svg class="w-3.5 h-3.5 text-green-500 dark:text-green-400" aria-hidden="true"
						xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 16 12">
						<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
							d="M1 5.917 5.724 10.5 15 1.5" />
					</svg>
				</span>
				<div class="ml-3">
					<h3 class="font-medium leading-tight">Hlavný proces uzávierky</h3>
					<p class="text-sm">Uzávierka bola úspešne dokončená.</p>
				</div>
			</li>
			<li class="mb-10 ms-6">
				<span
					class="absolute flex items-center justify-center w-8 h-8 bg-red-200 text-red-500 rounded-full -start-4 ring-4 ring-white dark:ring-gray-900 dark:bg-red-900">
					<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
						stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
						class="lucide lucide-circle-x-icon w-3.5 h-3.5 lucide-circle-x">
						<circle cx="12" cy="12" r="10" />
						<path d="m15 9-6 6" />
						<path d="m9 9 6 6" />
					</svg>
				</span>
				<div class="ml-3">
					<h3 class="font-medium leading-tight">Ocenenie</h3>
					<p class="text-sm">Príprava ocenenia</p>
				</div>
			</li>
			<li class="mb-10 ms-6">
				<span
					class="absolute flex items-center mr-4 justify-center w-8 h-8 bg-gray-100 rounded-full -start-4 ring-4 ring-white dark:ring-gray-900 dark:bg-gray-700">
					<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
						stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
						class="w-3.5 h-3.5 text-gray-500 dark:text-gray-400">
						<path d="M20 6 9 17l-5-5" />
					</svg>
				</span>
				<div class="ml-3">
					<h3 class="font-medium leading-tight">Generovanie reportov</h3>
					<p class="text-sm">Generovanie reportov a odosielanie emailov</p>
				</div>
			</li>
			<li class="ms-6">
				<span
					class="absolute flex items-center justify-center w-8 h-8 bg-gray-100 rounded-full -start-4 ring-4 ring-white dark:ring-gray-900 dark:bg-gray-700">
					<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
						stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
						class="w-3.5 h-3.5 text-gray-500 dark:text-gray-400">
						<path d="M20 6 9 17l-5-5" />
					</svg>
				</span>
				<div class="ml-3">
					<h3 class="font-medium leading-tight">Konfirmácia</h3>
					<p class="text-sm">Zhrnutie uzávierky a jej potvrdenie</p>
					<div class="ml-3"></div>
			</li>
		</ol>
		<div class="flex flex-col gap-6 items-center justify-center w-full">
			<svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none"
				stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
				class="lucide lucide-circle-x-icon text-red-500 lucide-circle-x">
				<circle cx="12" cy="12" r="10" />
				<path d="m15 9-6 6" />
				<path d="m9 9 6 6" />
			</svg>
			<h2 class="text-2xl font-bold text-red-700">Chyba!</h2>
			<div>
				<?php foreach ($errors as $error) { ?>
					<p class="text-red-500 mb-4"><?php echo $error; ?></p>
				<?php } ?>
			</div>
			<button type="button" class="text-white bg-gray-800 hover:bg-gray-900 focus:outline-none
				 focus:ring-4 focus:ring-gray-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 
				 dark:bg-gray-800 dark:hover:bg-gray-700 dark:focus:ring-gray-700 dark:border-gray-700"
				hx-get="/force-logout?restared=true" hx-target="#uzavierka-modal-body">Reštartovať</button>
		</div>
	</section>
<?php } ?>
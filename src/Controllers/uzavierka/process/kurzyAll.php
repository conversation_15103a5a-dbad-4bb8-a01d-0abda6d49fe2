<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/src/lib/functions/conversionEUR.php";
require_once "/home/<USER>/www/conf/settings.php";
require_once "/home/<USER>/www/src/lib/notifications/Notification.class.php";

$errors = [];
$fromDB = isset($_GET["fromDB"]) ? true : false;

//ZISTIT DATUM KU KTOREMU TREBA KURZY
$datumy = Connection::getDataFromDatabase("SELECT
    DATE_TRUNC('day', MAX(datum) + INTERVAL '1 day') AS datestamp,
    TO_CHAR(DATE_TRUNC('day', MAX(datum) + INTERVAL '1 day'), 'DD.MM.YYYY') AS datum
    FROM kurzyaktivarchiv;", defaultDB)[1][0];

$maxdatumDownload = $datumy["datum"];
$datestamp = $datumy["datestamp"];

//ZISTI MAX DATUM KU KTOREMU JE MOZNE ZADAT KURZY
$maxdatumDownload = Connection::getDataFromDatabase("SELECT TO_CHAR((MAX(datum))::date, 'yyyy-mm-dd') AS datum FROM today;", defaultDB)[1][0]["datum"];
$datumUzavierky = Connection::getDataFromDatabase("SELECT to_char(max(dateuzavierky),'dd.mm.yyyy') as datumuzavierky FROM uzavierkalog where activityid=11;", defaultDB)[1][0]["datumuzavierky"];
$mode = EURConversion::GetConversionMode($datumUzavierky);

$url = "https://nbs.sk/export/sk/exchange-rate/$maxdatumDownload/xml";
$dir = '/home/<USER>/www/temp/';

$file_name = "kurzy_$maxdatumDownload.xml";

// Save file into file location
$save_file_loc = $dir . $file_name;

if (!file_exists($save_file_loc)) {
    // Initialize the cURL session
    $ch = curl_init($url);

    // Initialize directory name where
    // file will be save


    // Open file
    $fp = fopen($save_file_loc, 'wb');

    // It set an option for a cURL transfer
    curl_setopt($ch, CURLOPT_FILE, $fp);
    curl_setopt($ch, CURLOPT_HEADER, 0);

    // Perform a cURL session
    curl_exec($ch);

    // Closes a cURL session and frees all resources
    curl_close($ch);

    // Close file
    fclose($fp);
}

try {
    $kurzy = parseEcbExchangeRates($save_file_loc);
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}

if (!$fromDB) {
    try {
        $kurzyCP = realneCSV();
    } catch (Exception $e) {
        echo "Error: " . $e->getMessage();
    }
} else {
    $kurzyCP = [];
}



if (isset($datum_offset)) {
    // majdem realny offset 
    $query = "select to_date('$maxdatumDownload', 'DD.MM.YYYY') - max(datum) as datum_offset from pricestore where datum <=to_date('$maxdatumDownload', 'DD.MM.YYYY') - $datum_offset ";
    $db->query($query);
    if ($db->next_record())
        $datum_offset = $db->f("datum_offset");
    else
        $datum_offset = 0;
} else {
    $datum_offset = 0;
}

$maxDatumek = Connection::getDataFromDatabase("SELECT max(datum) - INTERVAL '1 day' as datum FROM today;", defaultDB)[1][0]["datum"];

$queryParMajetok = "('EUR' || mt.menadenom = mp.par or 'EUR' || mt.menaref = mp.par) and";
$query = "SELECT MAX(r.ric)                 AS ric,
       MAX(e.isinreal)            AS isinreal,
       MAX(r.ric)                 AS riclink,
       MAX(e.cpnazskratka)        AS skratka,
       MAX(e.cpnaz)               AS nazov,
       MAX(e.nominalemisie)       AS nominal,
       MAX(e.eqid)                AS eqid,
       MAX(r.isincurrric)         AS isincurrric,
       MAX(c.currencytrade)       AS curr,
       MAX(ka.kurz)               AS kurz,
       MAX((SELECT MAX(k.kurz)
            FROM kurzyaktivarchiv k
                     JOIN (SELECT MAX(datum) AS datum
                           FROM pricestore
                           WHERE datum <= '$maxDatumek'::date) pd ON k.datum = pd.datum
            WHERE k.ric = r.ric)) AS prev_kurz
FROM dbequitycurrric r
         JOIN dbequitycurr c ON r.isincurr = c.isincurr
         JOIN dbequity e ON c.isin = e.isin
         JOIN majetoktotal mt ON r.isincurrric = mt.kodaktiva AND mt.datum = '$maxDatumek'
         LEFT JOIN kurzyaktivarchiv ka ON ka.datum = '$maxdatumDownload'::date AND ka.ric = r.ric
WHERE mt.jednotka LIKE 'ks'
GROUP BY mt.kodaktiva
UNION ALL
SELECT mp.par                      AS ric,
       MAX(mp.par)                 AS isinreal,
       MAX(mp.par)                 AS riclink,
       MAX(mp.par)                 AS skratka,
       MAX(mp.par)                 AS nazov,
       0                           AS nominal,
       ''                          AS eqid,
       ''                          AS isincurrric,
       ''                          AS curr,
       MAX(ka.kurz)                AS kurz,
       MAX((SELECT MAX(k.kurz)
            FROM kurzyaktivarchiv k
                     JOIN (SELECT MAX(datum) AS datum
                           FROM pricestore
                           WHERE datum <= '$maxDatumek'::date) pd ON k.datum = pd.datum
            WHERE k.ric = mp.par)) AS prev_kurz
FROM majetoktotal mt
         JOIN menovypar mp ON 'EUR' || mt.menadenom = mp.par OR 'EUR' || mt.menaref = mp.par
         LEFT JOIN kurzyaktivarchiv ka ON ka.datum = '$maxdatumDownload'::date AND ka.ric = mp.par
WHERE mt.kurzaktiva = 0
  AND mt.menadenom <> mt.menaref
GROUP BY mp.par
UNION ALL
SELECT mp.par                      AS ric,
       MAX(mp.par)                 AS isinreal,
       MAX(mp.par)                 AS riclink,
       MAX(mp.par)                 AS skratka,
       MAX(mp.par)                 AS nazov,
       0                           AS nominal,
       ''                          AS eqid,
       ''                          AS isincurrric,
       ''                          AS curr,
       MAX(ka.kurz)                AS kurz,
       MAX((SELECT MAX(k.kurz)
            FROM kurzyaktivarchiv k
                     JOIN (SELECT MAX(datum) AS datum
                           FROM pricestore
                           WHERE datum <= '$maxDatumek'::date) pd ON k.datum = pd.datum
            WHERE k.ric = mp.par)) AS prev_kurz
FROM menovypar mp
         LEFT JOIN kurzyaktivarchiv ka ON ka.datum = '$maxdatumDownload'::date AND ka.ric = mp.par
WHERE mp.kurz = 1
  AND mp.par NOT IN (SELECT mp.par
                     FROM majetoktotal mt
                              JOIN menovypar mp ON 'EUR' || mt.menadenom = mp.par OR 'EUR' || mt.menaref = mp.par
                              LEFT JOIN kurzyaktivarchiv ka ON ka.datum = '$maxdatumDownload'::date AND ka.ric = mp.par
                     WHERE mt.kurzaktiva = 0
                       AND mt.menadenom <> mt.menaref
                     GROUP BY mp.par)
GROUP BY mp.par
ORDER BY skratka;";

$kurzyTableData = Connection::getDataFromDatabase($query, defaultDB)[1];

if (empty($errors)) { ?>
    <section class="flex gap-2 h-full">
        <ol class="relative text-gray-500 border-s border-gray-200 dark:border-gray-700 dark:text-gray-400">
            <li class="mb-10 ms-6">
                <span
                    class="absolute flex items-center justify-center w-8 h-8 bg-green-200 rounded-full -start-4 ring-4 ring-white dark:ring-gray-900 dark:bg-green-900">
                    <svg class="w-3.5 h-3.5 text-green-500 dark:text-green-400" aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 16 12">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M1 5.917 5.724 10.5 15 1.5" />
                    </svg>
                </span>
                <div class="ml-3">
                    <h3 class="font-medium leading-tight">Odhlásenie používateľov</h3>
                    <p class="text-sm">Všetci používatelia boli odhlásení.</p>
                </div>
            </li>
            <li class="mb-10 ms-6">
                <span
                    class="absolute flex items-center justify-center w-8 h-8 bg-green-200 rounded-full -start-4 ring-4 ring-white dark:ring-gray-900 dark:bg-green-900">
                    <svg class="w-3.5 h-3.5 text-green-500 dark:text-green-400" aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 16 12">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M1 5.917 5.724 10.5 15 1.5" />
                    </svg>
                </span>
                <div class="ml-3">
                    <h3 class="font-medium leading-tight">Menové páry</h3>
                    <p class="text-sm">Menové páry sú kompletné.</p>
                </div>
            </li>
            <li class="mb-10 ms-6 bg-gray-500 p-3 rounded-lg text-gray-200">
                <span id="formSubmitter"
                    class="absolute flex items-center justify-center w-8 h-8 bg-blue-600 hover:bg-blue-900 transition-all
                    cursor-pointer text-gray-200 rounded-full p-1.5 -start-4 ring-4 ring-white dark:ring-gray-100 dark:bg-blue-900 dark:hover:bg-blue-600">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="lucide lucide-check-check-icon lucide-check-check">
                        <path d="M18 6 7 17l-5-5" />
                        <path d="m22 10-7.5 7.5L13 16" />
                    </svg>
                </span>
                <h3 class="font-medium leading-tight">Kurzy</h3>
                <p class="text-sm">Po kliknutí na tlačidlo potvrdíte kurzy.</p>
            </li>
            <li class="mb-10 ms-6">
                <span
                    class="absolute flex items-center justify-center w-8 h-8 bg-gray-100 rounded-full -start-4 ring-4 ring-white dark:ring-gray-900 dark:bg-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="w-3.5 h-3.5 text-gray-500 dark:text-gray-400">
                        <path d="M20 6 9 17l-5-5" />
                    </svg>
                </span>
                <div class="ml-3">
                    <h3 class="font-medium leading-tight">Hlavný proces uzávierky</h3>
                    <p class="text-sm">Uzávierka bola úspešne dokončená.</p>
                </div>
            </li>
            <li class="mb-10 ms-6">
                <span
                    class="absolute flex items-center justify-center w-8 h-8 bg-gray-100 rounded-full -start-4 ring-4 ring-white dark:ring-gray-900 dark:bg-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="w-3.5 h-3.5 text-gray-500 dark:text-gray-400">
                        <path d="M20 6 9 17l-5-5" />
                    </svg>
                </span>
                <div class="ml-3">
                    <h3 class="font-medium leading-tight">Ocenenie</h3>
                    <p class="text-sm">Príprava ocenenia</p>
                </div>
            </li>
            <li class="mb-10 ms-6">
                <span
                    class="absolute flex items-center mr-4 justify-center w-8 h-8 bg-gray-100 rounded-full -start-4 ring-4 ring-white dark:ring-gray-900 dark:bg-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="w-3.5 h-3.5 text-gray-500 dark:text-gray-400">
                        <path d="M20 6 9 17l-5-5" />
                    </svg>
                </span>
                <div class="ml-3">
                    <h3 class="font-medium leading-tight">Generovanie reportov</h3>
                    <p class="text-sm">Generovanie reportov a odosielanie emailov</p>
                </div>
            </li>
            <li class="ms-6">
                <span
                    class="absolute flex items-center justify-center w-8 h-8 bg-gray-100 rounded-full -start-4 ring-4 ring-white dark:ring-gray-900 dark:bg-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="w-3.5 h-3.5 text-gray-500 dark:text-gray-400">
                        <path d="M20 6 9 17l-5-5" />
                    </svg>
                </span>
                <div class="ml-3">
                    <h3 class="font-medium leading-tight">Konfirmácia</h3>
                    <p class="text-sm">Zhrnutie uzávierky a jej potvrdenie</p>
                    <div class="ml-3"></div>
            </li>
        </ol>
        <div class="flex flex-col gap-6 items-center justify-center w-full">
            <div id="kurzickyTabulecka" class="relative overflow-x-auto">
                <form id="kurzovyForm">
                    <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
                        <thead
                            class="text-xs sticky top-0 text-gray-700 uppercase bg-gray-50 dark:bg-gray-800 dark:text-gray-400">
                            <tr>
                                <th scope="col" class="px-6 py-3">
                                    RIC aktíva
                                </th>
                                <th scope="col" class="px-6 py-3">
                                    ISIN
                                </th>
                                <th scope="col" class="px-6 py-3">
                                    Mena
                                </th>
                                <th scope="col" class="px-6 py-3">
                                    Názov aktíva
                                </th>
                                <th scope="col" class="px-6 py-3">
                                    Aktuálny kurz
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($kurzyTableData as $key => $value) {
                                $isincurrric = $value["isincurrric"];
                                $ric = $value["ric"];
                                $skratka = $value["skratka"];
                                $curr = $value["curr"];
                                $isin = substr($isincurrric, 0, 12);
                                $isinreal = $value["isinreal"];
                                $nazov = $value["nazov"];
                                $kurz = number_format($value["kurz"], 4);
                                $prev_kurz = $value["prev_kurz"];
                                $riclink = $value["riclink"];
                                $riclink = (substr($riclink, 0, 3) == "USD") ? substr($riclink, 3, 3) . "=" : $riclink;
                                $riclink = (substr($riclink, 3, 3) == "USD") ? substr($riclink, 0, 3) . "=" : $riclink;
                                $is_changed = false;

                                if ($value["eqid"] != "") {
                                    foreach ($kurzyCP as $key => $item) {
                                        //echo "VALUE ISIN: " . $item["ISIN"] . " ADN THEN ISIN: $isin <br>";
                                        if ($item["ISIN"] == $isin) {
                                            $kurz = number_format((float) $item["Coupon"], 4);
                                            $is_changed = true;
                                        }
                                    }
                                }


                                ?>
                                <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 border-gray-200">
                                    <th scope="row"
                                        class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                        <?php echo $ric; ?>
                                        <input type="hidden" name="ric" value="<?php echo $ric; ?>" />
                                    </th>
                                    <td class="px-6 py-4">
                                        <?php echo $isinreal; ?>
                                        <input type="hidden" name="isinreal" value="<?php echo $isinreal; ?>" />
                                    </td>
                                    <td class=" px-6 py-4">
                                        <?php echo $curr; ?>
                                        <input type="hidden" name="curr" value="<?php echo $curr; ?>" />
                                    </td>
                                    <td class="px-6 py-4">
                                        <?php echo $nazov; ?>
                                        <input type="hidden" name="nazov" value="<?php echo $nazov; ?>" />
                                    </td>
                                    <td class="px-6 py-4">
                                        <?php if ($value["eqid"] == "") {
                                            $menovyParMena = substr($ric, 3, 3); ?>
                                            <?php if ($kurzy["rates"][$menovyParMena] != null) {
                                                $kurz = number_format((float) $kurzy["rates"][$menovyParMena], 4);

                                                if ($prev_kurz != 0 && $prev_kurz != null && $prev_kurz != "") {
                                                    $difference = abs(($kurz / $prev_kurz) - 1) * 100;
                                                } else {
                                                    $difference = 0;
                                                }
                                                ?>
                                                <span>Kurz pred: <?php echo $prev_kurz; ?></span>
                                                <input type="hidden" name="prev_kurz" value="<?php echo $prev_kurz; ?>">
                                                <input type="text" id="small-input" name="kurz" value="<?php echo (float) $kurz; ?>"
                                                    class="block w-full <?php echo $difference > 3 ? "bg-red-500" : "bg-gray-50 dark:bg-gray-700"; ?> p-2 text-gray-900 border  rounded-lg 
                                         text-xs focus:ring-blue-500 focus:border-blue-500 <?php echo $kurzy["rates"][$menovyParMena] != null ? "dark:border-gray-600 border-gray-300" : "border-green-300 dark:border-green-600"; ?>
                                         dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                                            <?php }
                                        } else {
                                            if ($prev_kurz != 0 && $prev_kurz != null && $prev_kurz != "") {
                                                $difference = abs(((float) $kurz / (float) $prev_kurz) - 1) * 100;
                                            } else {
                                                $difference = 0;
                                            }
                                            if($curr === "GBP"){
                                                $kurz = $kurz / 100;
                                            }
                                            ?>
                                            <span>Kurz pred: <?php echo $prev_kurz; ?></span>
                                            <input type="hidden" name="prev_kurz" value="<?php echo $prev_kurz; ?>">
                                            <input type="text" id="small-input" name="kurz"
                                                value="<?php echo $kurz === NULL ? 0 : number_format((float) $kurz, 4); ?>"
                                                class="block w-full <?php echo $difference > 3 ? "bg-red-500" : "bg-gray-50 dark:bg-gray-700"; ?> p-2 text-gray-900 border  rounded-lg 
                                                 text-xs focus:ring-blue-500 focus:border-blue-500
                                                 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                                        <?php } ?>
                                    </td>
                                </tr>
                            <?php } ?>
                        </tbody>
                    </table>
                    <input type="submit" class="hidden" id="kurzovyFormSubmitter" />
                </form>
            </div>
            <div id="kurzickyPotvrdenie" class="flex-col gap-6 items-center justify-center w-full" style="display: none;">
                <svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide lucide-loader-circle-icon animate-spin lucide-loader-circle">
                    <path d="M21 12a9 9 0 1 1-6.219-8.56" />
                </svg>
                <span>Potvrdzujem kurzy...</span>
            </div>
        </div>
        <script>
            $("#formSubmitter").on("click", () => {
                $("#kurzovyFormSubmitter").click();
            });

            $("#kurzovyForm").on("submit", (e) => {
                e.preventDefault();
                $("#kurzickyTabulecka").css("display", "none")
                $("#kurzickyPotvrdenie").css("dsiplay", "flex");
                const formData = new FormData(e.currentTarget);
                const dataCount = formData.getAll("ric");
                let formDataObj = [];
                for (let i = 0; dataCount.length > i; i++) {
                    formDataObj.push({
                        ric: formData.getAll("ric")[i],
                        isinreal: formData.getAll("isinreal")[i],
                        curr: formData.getAll("curr")[i],
                        nazov: formData.getAll("nazov")[i],
                        prev_kurz: formData.getAll("prev_kurz")[i],
                        kurz: formData.getAll("kurz")[i],
                    });
                };
                console.log(formDataObj);

                if (formDataObj.length > 0) {
                    htmx.ajax('POST', `/uzavierka/process/potvrditKurzy`,
                        {
                            target: "#uzavierka-modal-body",
                            values: { "data": JSON.stringify(formDataObj) }
                        }).then((response) => {

                        });
                } else {
                    alert("Neni čo potvrdiť.");
                }
            })
        </script>
    </section>
<?php } else { ?>
    <section class="flex gap-2">
        <ol class="relative text-gray-500 border-s border-gray-200 dark:border-gray-700 dark:text-gray-400">
            <li class="mb-10 ms-6">
                <span
                    class="absolute flex items-center justify-center w-8 h-8 bg-green-200 rounded-full -start-4 ring-4 ring-white dark:ring-gray-900 dark:bg-green-900">
                    <svg class="w-3.5 h-3.5 text-green-500 dark:text-green-400" aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 16 12">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M1 5.917 5.724 10.5 15 1.5" />
                    </svg>
                </span>
                <div class="ml-3 text-red-400">
                    <h3 class="font-medium leading-tight">Odhlásenie používateľov</h3>
                    <p class="text-sm">Všetci používatelia boli odhlásení.</p>
                </div>
            </li>
            <li class="mb-10 ms-6">
                <span
                    class="absolute flex items-center justify-center w-8 h-8 bg-red-200 text-red-500 rounded-full -start-4 ring-4 ring-white dark:ring-gray-900 dark:bg-red-900">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="lucide lucide-circle-x-icon w-3.5 h-3.5 lucide-circle-x">
                        <circle cx="12" cy="12" r="10" />
                        <path d="m15 9-6 6" />
                        <path d="m9 9 6 6" />
                    </svg>
                </span>
                <div class="ml-3">
                    <h3 class="font-medium leading-tight">Menové páry</h3>
                    <p class="text-sm">Menové páry sa nepodarilo získať a overiť.</p>
                </div>
            </li>
            <li class="mb-10 ms-6">
                <span
                    class="absolute flex items-center justify-center w-8 h-8 bg-green-200 rounded-full -start-4 ring-4 ring-white dark:ring-gray-900 dark:bg-green-900">
                    <svg class="w-3.5 h-3.5 text-green-500 dark:text-green-400" aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 16 12">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M1 5.917 5.724 10.5 15 1.5" />
                    </svg>
                </span>
                <div class="ml-3">
                    <h3 class="font-medium leading-tight">Kurzy</h3>
                    <p class="text-sm">Ocenenie BU a CP</p>
                </div>
            </li>
            <li class="mb-10 ms-6">
                <span
                    class="absolute flex items-center justify-center w-8 h-8 bg-gray-100 rounded-full -start-4 ring-4 ring-white dark:ring-gray-900 dark:bg-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="w-3.5 h-3.5 text-gray-500 dark:text-gray-400">
                        <path d="M20 6 9 17l-5-5" />
                    </svg>
                </span>
                <div class="ml-3">
                    <h3 class="font-medium leading-tight">Hlavný proces uzávierky</h3>
                    <p class="text-sm">Uzávierka bola úspešne dokončená.</p>
                </div>
            </li>
            <li class="mb-10 ms-6">
                <span
                    class="absolute flex items-center justify-center w-8 h-8 bg-gray-100 rounded-full -start-4 ring-4 ring-white dark:ring-gray-900 dark:bg-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="w-3.5 h-3.5 text-gray-500 dark:text-gray-400">
                        <path d="M20 6 9 17l-5-5" />
                    </svg>
                </span>
                <div class="ml-3">
                    <h3 class="font-medium leading-tight">Ocenenie</h3>
                    <p class="text-sm">Príprava ocenenia</p>
                </div>
            </li>
            <li class="mb-10 ms-6">
                <span
                    class="absolute flex items-center mr-4 justify-center w-8 h-8 bg-gray-100 rounded-full -start-4 ring-4 ring-white dark:ring-gray-900 dark:bg-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="w-3.5 h-3.5 text-gray-500 dark:text-gray-400">
                        <path d="M20 6 9 17l-5-5" />
                    </svg>
                </span>
                <div class="ml-3">
                    <h3 class="font-medium leading-tight">Generovanie reportov</h3>
                    <p class="text-sm">Generovanie reportov a odosielanie emailov</p>
                </div>
            </li>
            <li class="ms-6">
                <span
                    class="absolute flex items-center justify-center w-8 h-8 bg-gray-100 rounded-full -start-4 ring-4 ring-white dark:ring-gray-900 dark:bg-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="w-3.5 h-3.5 text-gray-500 dark:text-gray-400">
                        <path d="M20 6 9 17l-5-5" />
                    </svg>
                </span>
                <div class="ml-3">
                    <h3 class="font-medium leading-tight">Konfirmácia</h3>
                    <p class="text-sm">Zhrnutie uzávierky a jej potvrdenie</p>
                    <div class="ml-3"></div>
            </li>
        </ol>
        <div class="flex flex-col gap-6 items-center justify-center w-full">
            <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="lucide lucide-circle-x-icon text-red-500 lucide-circle-x">
                <circle cx="12" cy="12" r="10" />
                <path d="m15 9-6 6" />
                <path d="m9 9 6 6" />
            </svg>
            <h2 class="text-2xl font-bold text-red-700">Chyba!</h2>
            <div>
                <?php foreach ($errors as $error) { ?>
                    <p class="text-red-500 mb-4"><?php echo $error; ?></p>
                <?php } ?>
            </div>
            <button type="button" class="text-white bg-gray-800 hover:bg-gray-900 focus:outline-none
                 focus:ring-4 focus:ring-gray-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 
                 dark:bg-gray-800 dark:hover:bg-gray-700 dark:focus:ring-gray-700 dark:border-gray-700"
                hx-get="/force-logout?restared=true" hx-target="#uzavierka-modal-body">Reštartovať</button>
        </div>
    </section>
    <?php
}

function parseEcbExchangeRates($xmlFilePath)
{
    // Load the XML
    $xml = simplexml_load_file($xmlFilePath);
    if ($xml === false) {
        throw new Exception("Failed to load XML file: $xmlFilePath");
    }

    // Register namespaces
    $xml->registerXPathNamespace('gesmes', 'http://www.gesmes.org/xml/2002-08-01');
    $xml->registerXPathNamespace('eurofxref', 'http://www.ecb.int/vocabulary/2002-08-01/eurofxref');

    // Find the node with the time attribute (date)
    $timeNode = $xml->xpath('//eurofxref:Cube[@time]');
    if (empty($timeNode)) {
        throw new Exception("Could not find date node in XML.");
    }

    $date = (string) $timeNode[0]['time'];

    // Extract currency and rates
    $rates = [];
    foreach ($timeNode[0]->Cube as $currencyNode) {
        $currency = (string) $currencyNode['currency'];
        $rateRaw = (string) $currencyNode['rate'];
        $rate = (float) str_replace([' ', ','], ['', '.'], $rateRaw); // Normalize number
        $rates[$currency] = $rate;
    }

    return [
        'date' => $date,
        'rates' => $rates,
    ];
}


function realneCSV()
{
    if (($handle = fopen("/home/<USER>/www/temp/kurzyCP.csv", 'r')) !== FALSE) {
        $headerSkipped = false; // Flag to skip the header rows

        // Array to store the parsed data
        $data = [];

        while (($row = fgetcsv($handle, 1000, ',')) !== FALSE) {
            // Skip header rows based on the first column or other criteria
            if (!$headerSkipped) {
                // Assuming the first few rows are headers, so we skip them
                if (empty($row[0])) {
                    continue;
                }
                $headerSkipped = true; // Skip headers from now on
            }

            // Extracting the data and skipping rows with missing or incomplete values
            if (count($row) >= 10) { // You can adjust this based on the minimum number of columns
                $parsedRow = [
                    'ISIN' => $row[0],
                    'Type' => $row[1],
                    'Name' => $row[2],
                    'Price' => $row[3],
                    'Coupon' => $row[4],
                    'Maturity' => $row[5],
                    'YTM' => $row[6],
                    'Currency' => $row[7],
                    'Difference' => $row[8],
                    'PreviousPrice' => $row[9],
                ];

                // Check if ISIN is a valid bond identifier or equity identifier
                if (preg_match('/^[A-Z0-9]+$/', $parsedRow['ISIN'])) {
                    $data[] = $parsedRow;
                }
            }
        }
        fclose($handle);

        return $data;
    } else {
        return [];
    }
}
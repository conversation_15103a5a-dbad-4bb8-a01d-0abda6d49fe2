<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
require_once "/home/<USER>/www/src/lib/notifications/Notification.class.php";

$errors = [];

$sess_userid = $_SESSION["user"]["data"]["userid"];
$username = $_SESSION["user"]["data"]["username"];

$datumy = Connection::getDataFromDatabase("SELECT to_char(max(datum),'YYYY-MM-DD') as datum,
 to_char(max(datum) + 1,'YYYY-MM-DD') as datum_next from today", defaultDB)[1][0];

$datumUzavierky = $datumy["datum"];
$datumNovy = $datumy["datum_next"];

//ROZDIEL DATUMOV
$rozdiel = Connection::getDataFromDatabase("SELECT max(t.datum) - max(ul.dateuzavierky) - 1 as rozdiel
FROM today t, uzavierkalog ul
WHERE ul.fondid = t.fondid and ul.activityid = 11", defaultDB)[1][0]["rozdiel"];

$pocet_dni = $rozdiel > 0 ? $rozdiel : 0;

// ZAPIS DO POZICIE NULOVE POLOZKY PRE PREDANE CP
$from_where_spravca = "
    from
				majetoktoday mt, spravca s, today t
			where
				s.spravcaid = mt.subjektid and
				t.fondid = mt.subjektid and
				eqid in ('Bonds', 'Shares', 'Fonds') and
				uctovnykod >= 251000 and
				uctovnykod <= 251999";

$from_where_fond = "
    from
				majetoktoday mt, fonds f, today t
			where
				f.fondid = mt.subjektid and
				t.fondid = mt.subjektid and
				eqid in ('Bonds', 'Shares', 'Fonds') and
				uctovnykod >= 251000 and
				uctovnykod <= 251999";

$insertIntoPoziciaCP = Connection::InsertUpdateCreateDelete("INSERT INTO poziciacp
    (isincurrric,subjektid,uctovnykod,datum)
    (
        select
            kodaktiva,subjektid,uctovnykod,datum
        from
        (
            select
                kodaktiva,
                subjektid,
                uctovnykod,
                max(t.datum) as datum,
                abs(sum(((md_d*2)-1) * (-1) * pocet)) as pocet
            $from_where_fond
            group by
                subjektid,eqid,kodaktiva,uctovnykod
            union all
            select
                kodaktiva,
                subjektid,
                uctovnykod,
                max(t.datum) as datum,
                abs(sum(((md_d*2)-1) * (-1) * pocet)) as pocet
            $from_where_spravca
            group by
                subjektid,eqid,kodaktiva,uctovnykod) a
        where pocet = 0);", [], defaultDB);

if (gettype($insertIntoPoziciaCP) !== "integer") {
	$errors[] = "Nepodarilo sa zapísať údaje do tabuľky [poziciacp]. " . $insertIntoPoziciaCP;
}

// Skopirovať všetky zápisy do majetokarchiv - orkrem totálových 0

$insertIntoMajetokArchiv = Connection::InsertUpdateCreateDelete("INSERT INTO majetokarchiv
    (obratid,destinacia,subjektid,kodobratu,uctovnykod,eqid,kodaktiva,ucetaktiva,jednotka,pocet,mena,md_d,obratdatetimereal,obratdatatimezauctovane)
    (select obratid,destinacia,subjektid,kodobratu,uctovnykod,eqid,kodaktiva,ucetaktiva,jednotka,pocet,mena,md_d,obratdatetimereal,obratdatatimezauctovane
			from majetoktoday
			where
				obratid not in (0,1,2))", [], defaultDB);

if (gettype($insertIntoMajetokArchiv) !== "integer") {
	$errors[] = "Nepodarilo sa zapísať údaje do tabuľky [majetokarchiv]. " . $insertIntoMajetokArchiv;
}

/**
 * Kroky 2,3,4 - generovanie budúcich výnosov.
 * Do majetoktoday sa zapisujú tie ku ktorým ešte budúci výnos neexistuje.
 * Výnimkou sú záznamy z predošlej uzávierky (obratid not in ..(..where obratid > 0)), pri týchto
 * zapisujem 1 namiesto 0 (greatest(max(mt.obratid),1)), aby nedošlo k porušeniu constraint pre obratid = 0
 *
 * $pocet_dni nadobuda hodnoty 1 - x
 * pri 0
 */

$insertIntoMajetokTodayQuery = "INSERT INTO majetoktoday
    (obratid,destinacia,subjektid,kodobratu,uctovnykod,eqid,kodaktiva,ucetaktiva,jednotka,pocet,mena,md_d,obratdatetimereal,obratdatatimezauctovane)
    select
			1,
			'uzavierka',
			mt.subjektid,
			701,
			ko.uctovnykod,
			mt.eqid,
			mt.kodaktiva,
			mt.ucetaktiva,
			mt.jednotka,
			sum(COALESCE(((((mt.pocet*ktv.ir_td/100))/ktv.cond1)),0)),
			mt.mena,
			ko.md_d,
			CURRENT_DATE,
			max(t.datum)
		from
			majetoktoday mt,
			konfirmaciaktv ktv,
			kodobratumd_d ko,
			today t
		where
			mt.uctovnykod = 221210 and
			((mt.kodobratu=0 and OBRATDATATIMEZAUCTOVANE not in (select COALESCE(max(datum), to_date('1970-01-01','YYYY-MM-DD')) from pricestore where datum<(select max(OBRATDATATIMEZAUCTOVANE)
			from majetoktoday where uctovnykod = 221210 and kodobratu = 353 and ucetaktiva = mt.ucetaktiva
			) and fondid = mt.subjektid
			)) or mt.kodobratu = 253) and
			ko.kodobratu = 701 and
			ktv.cutd = mt.ucetaktiva and
			ktv.logactivityid > 11 and
			ktv.logactivityid < 23 and
			ktv.mena = mt.mena and
			mt.subjektid = t.fondid 
		group by
			mt.subjektid,
			mt.eqid,
			mt.kodaktiva,
			mt.ucetaktiva,
			ko.uctovnykod,
			mt.jednotka,
			mt.mena,
			ko.md_d
		union all
        select
			1,
			'uzavierka',
			subjektid,
			702,
			uctovnykod,
			eqid,
			kodaktiva,
			ucetaktiva,
			jednotka,
			-- sumarizacia a nasobenie urokom podla uctu
			pocet * f_urocenie(ucetaktiva, pocet)/100 / f_dnivroku(obdtz)";
if (!$config["Uzavierka"]["UrokBU"]) {
	$insertIntoMajetokTodayQuery .= " *
			(CASE WHEN (SELECT count(*) from majetoktoday where kodobratu=210 and
			subjektid=o.subjektid and mena=o.mena)>0 THEN 0 ELSE 1 END)";
}
$insertIntoMajetokTodayQuery .= "
    ,mena,
			md_d,
			CURRENT_DATE,
			datum
		from
		(
			select
				mt.subjektid,
				ko.uctovnykod,
				mt.eqid,
				mt.kodaktiva,
				mt.ucetaktiva,
				max(mt.jednotka) as jednotka,
				sum(mt.pocet * sign(mt.md_d - 0.5)*(-1)) as pocet,
				mt.mena as mena,
				max(ko.md_d) as md_d,
				max(mt.obratdatatimezauctovane) as obdtz,
				max(t.datum) as datum
			from
				majetoktoday mt,
				kodobratumd_d ko,
				today t
			where
				mt.uctovnykod = 221110 and
				ko.kodobratu = 702 and
				mt.subjektid = t.fondid 
			group by
				mt.subjektid,
				mt.eqid,
				mt.kodaktiva,
				mt.ucetaktiva,
				ko.uctovnykod,
				mt.mena
		) o
		union all
        select
			1,
			'uzavierka',
			mt.subjektid,
			704,
			ko.uctovnykod,
			'BU',
			max(r.isincurrric),
			max(kcp.cubu),
			max(kcp.currencyidtrade),
			sum(COALESCE((((((mt.pocet * e.nominalemisie * e.kupon/100) * (100 - e.dan)/100)) / e.zaklad)),0)),
			mt.mena,
			max(ko.md_d),
			CURRENT_DATE,
			max(t.datum)
		from
			majetoktoday mt,
			kodobratumd_d ko,
			konfirmaciacp kcp,
			rekonfirmaciacpobratid rcpo,
			dbequitycurrric r,
			dbequitycurr c,
			dbequity e,
			today t
		where
			mt.uctovnykod = 251400 and
			ko.kodobratu = 704 and
			substr(mt.kodaktiva,0,15) = c.isincurr and
			e.isin = c.isin and
			r.isincurr = c.isincurr and
			mt.obratid = rcpo.obratid and
			rcpo.dealid = kcp.dealid and
			c.currencytrade = mt.mena
		group by
			mt.subjektid,
			mt.eqid,
			mt.kodaktiva,
			mt.ucetaktiva,
			ko.uctovnykod,
			mt.mena
		union all
        select
			2,
			'uzavierka',
			mt.subjektid,
			701,
			ko.uctovnykod,
			mt.eqid,
			mt.kodaktiva,
			mt.ucetaktiva,
			mt.jednotka,
			sum(COALESCE(((((mt.pocet*ktv.ir_td/100))/ktv.cond1)),0)) * least($pocet_dni,max(t.datum) - max(ktv.z_td)),
			mt.mena,
			ko.md_d,
			CURRENT_DATE,
			max(t.datum)
		from
			majetoktoday mt,
			konfirmaciaktv ktv,
			kodobratumd_d ko,
			today t
		where
			mt.obratid = 0 and
			mt.uctovnykod = 221210 and
			ko.kodobratu = 701 and
			ktv.cutd = mt.ucetaktiva and
			ktv.logactivityid > 11 and
			ktv.logactivityid < 23 and
			mt.subjektid = t.fondid and
			ktv.mena = mt.mena 
		group by
			mt.subjektid,
			mt.eqid,
			mt.kodaktiva,
			mt.ucetaktiva,
			ko.uctovnykod,
			mt.jednotka,
			mt.mena,
			ko.md_d
		union all
        select
			2,
			'uzavierka',
			subjektid,
			702,
			uctovnykod,
			eqid,
			kodaktiva,
			ucetaktiva,
			jednotka,
			pocet * f_urocenie(ucetaktiva, pocet)/100 / f_dnivroku(obdtz) * $pocet_dni";
if (!$config["Uzavierka"]["UrokBU"]) {
	$insertIntoMajetokTodayQuery .= " *
			(CASE WHEN (SELECT count(*) from majetoktoday where kodobratu=210 and
			subjektid=o.subjektid and mena=o.mena)>0 THEN 0 ELSE 1 END)";
}
$insertIntoMajetokTodayQuery .= "
            ,mena,
			md_d,
			CURRENT_DATE,
			datum
		from
		(
			select
				mt.subjektid, 
				ko.uctovnykod,
				mt.eqid,
				mt.kodaktiva,
				mt.ucetaktiva,
				max(mt.jednotka) as jednotka,
				sum(mt.pocet * sign(mt.md_d - 0.5)*(-1)) as pocet,
				mt.mena,
				max(ko.md_d) as md_d,
				max(mt.obratdatatimezauctovane) as obdtz,
				max(t.datum) as datum
			from
				majetoktoday mt,
				kodobratumd_d ko,
				today t
			where
				mt.obratid = 0 and
				mt.uctovnykod = 221110 and
				ko.kodobratu = 702 and
				mt.subjektid = t.fondid 
			group by
				mt.subjektid,
				mt.eqid,
				mt.kodaktiva,
				mt.ucetaktiva,
				ko.uctovnykod,
				mt.mena
		) o
		union all
        select
			2,
			'uzavierka',
			mt.subjektid,
			704,
			ko.uctovnykod,
			'BU',
			max(r.isincurrric),
			max(kcp.cubu),
			max(kcp.currencyidtrade),
			sum(COALESCE((((((mt.pocet * e.nominalemisie * e.kupon/100) * (100 - e.dan)/100)) / e.zaklad)),0)) * $pocet_dni,
			mt.mena,
			max(ko.md_d),
			CURRENT_DATE,
			max(t.datum)
		from
			majetoktoday mt,
			kodobratumd_d ko,
			konfirmaciacp kcp,
			rekonfirmaciacpobratid rcpo,
			dbequitycurrric r,
			dbequitycurr c,
			dbequity e,
			today t
		where
			mt.obratid = 0 and
			mt.uctovnykod = 251400 and
			ko.kodobratu = 704 and
			substr(mt.kodaktiva,0,15) = c.isincurr and
			e.isin = c.isin and
			r.isincurr = c.isincurr and
			mt.obratid = rcpo.obratid and
			rcpo.dealid = kcp.dealid and
			mt.subjektid = t.fondid
		group by
			mt.subjektid,
			mt.eqid,
			mt.kodaktiva,
			mt.ucetaktiva,
			ko.uctovnykod,
			mt.mena";

//echo $insertIntoMajetokTodayQuery;
$insertIntoMajetokToday = Connection::InsertUpdateCreateDelete($insertIntoMajetokTodayQuery, [], defaultDB);
if (gettype($insertIntoMajetokToday) !== "integer") {
	$errors[] = "Nepodarilo sa vložiť údaje do tabuľky [majetoktoday1]. " . $insertIntoMajetokToday;
}


//SUMARIZACIA DO MAJETOKTOTAL
$from_where_spravca = "
    from
		majetoktoday mt, spravca s, today t, navuctovanie n
	where
		mt.subjektid = 1 and
		s.spravcaid = 1 and
		t.fondid = 1 and
		mt.uctovnykod = n.uctovnykod
";
$from_where_fond = "
    from
		majetoktoday mt, fonds f, today t, navuctovanie n
	where
		f.fondid = mt.subjektid and
		t.fondid = mt.subjektid and
		mt.uctovnykod = n.uctovnykod
";

$insertIntoMajetokTotalQuery = "INSERT INTO majetoktotal
    (subjektid,ucetaktiva,eqid,kodaktiva,pocet,jednotka,kurzaktiva,sumadenom,menadenom,kurzmen,sumaref,uctovnykod,menaref,md_d,datum)
    (
			select
				subjektid,ucetaktiva,eqid,kodaktiva,pocet,jednotka,kurzaktiva,sumadenom,mena,kurzmen,sumaref,uctovnykod,menaref,md_d,datum
			from
			(
				select
					subjektid,
					ucetaktiva,
					max(eqid) as eqid,
					kodaktiva,
					abs(sum(((md_d*2)-1) * (-1) * pocet)) as pocet,
					max(jednotka) as jednotka,
					0 as kurzaktiva,
					0 as sumadenom,
					mena,
					0 as kurzmen,
					0 as sumaref,
					mt.uctovnykod,
					max(refmena) as menaref,
					greatest(sign(sum(((md_d*2)-1) * (-1) * pocet) * (-1)),0) as md_d,
					max(t.datum) as datum
				$from_where_spravca
				group by
					subjektid,eqid,kodaktiva,ucetaktiva,mt.uctovnykod,mena
				union all
				select
					subjektid,
					ucetaktiva,
					max(eqid) as eqid,
					kodaktiva,
					abs(sum(((md_d*2)-1) * (-1) * pocet)) as pocet,
					max(jednotka) as jednotka,
					0 as kurzaktiva,
					0 as sumadenom,
					mena,
					0 as kurzmen,
					0 as sumaref,
					mt.uctovnykod,
					max(refmena) as menaref,
					greatest(sign(sum(((md_d*2)-1) * (-1) * pocet) * (-1)),0) as md_d,
					max(t.datum) as datum
				$from_where_fond
				group by
					subjektid,eqid,kodaktiva,ucetaktiva,mt.uctovnykod,mena

			) as pii
			where pocet <> 0
		)";

$insertIntoMajetokTotal = Connection::InsertUpdateCreateDelete($insertIntoMajetokTotalQuery, [], defaultDB);
if (gettype($insertIntoMajetokTotal) !== "integer") {
	$errors[] = "Nepodarilo sa vložiť údaje do tabuľky [majetoktotal]. " . $insertIntoMajetokTotal;
}

//ZMAZAT VSETKO Z MAJETOKTODAY
$deleteFromMajetokToday = Connection::InsertUpdateCreateDelete("DELETE FROM majetoktoday", [], defaultDB);
if (gettype($deleteFromMajetokToday) !== "integer") {
	$errors[] = "Nepodarilo sa zmazať údaje z tabuľky [majetoktoday2]. " . $deleteFromMajetokToday;
}

//ZMAZAT VSETKO Z POOLASSET
$deleteFromPoolAsset = Connection::InsertUpdateCreateDelete("DELETE FROM poolasset", [], defaultDB);
if (gettype($deleteFromPoolAsset) !== "integer") {
	$errors[] = "Nepodarilo sa zmazať údaje z tabuľky [poolasset]. " . $deleteFromPoolAsset;
}

//VLOZIT VSETKY TOTALOVE ZAZNAMY Z PRVSLUHNEHO OBCHODNEHO DNE Z MAJETOKTOTAL DO MAJETOKTODAY.
$insertIntoMajetokTodayQuery = "INSERT INTO majetoktoday
    (obratid, destinacia, subjektid,kodobratu,uctovnykod,eqid,kodaktiva,ucetaktiva,jednotka,pocet, mena, md_d,obratdatetimereal,obratdatatimezauctovane)
    (select 0, 'uzavierka',subjektid,0, uctovnykod,eqid,kodaktiva,ucetaktiva,jednotka,abs(pocet),menadenom,md_d,t.datum, t.datum
		from majetoktotal mt, today t
		where
			mt.datum = t.datum and
			t.fondid = mt.subjektid
		)";

$insertIntoMajetokToday = Connection::InsertUpdateCreateDelete($insertIntoMajetokTodayQuery, [], defaultDB);
if (gettype($insertIntoMajetokToday) !== "integer") {
	$errors[] = "Nepodarilo sa vložiť údaje do tabuľky [majetoktoday3]. " . $insertIntoMajetokToday;
}

if (empty($errors)) {
	$notification = new Notification(
		11,
		'uzavierkalog',
		0,
		'uzavierka',
		$sess_userid,
		$username,
		"uzavierka",
		json_encode(["dateuzavierky", $datumUzavierky]),
		false,
		0
	);
	$notification->createNotifcation();
}

//ZMAZAT PRIPADNE STORNO Z UZAVIERKALOG
$deleteFromUzavierkaLog = Connection::InsertUpdateCreateDelete("DELETE FROM uzavierkalog
    WHERE dateuzavierky = (SELECT max(datum) FROM today)", [], defaultDB);
if (gettype($deleteFromUzavierkaLog) !== "integer") {
	$errors[] = "Nepodarilo sa zmazať údaje z tabuľky [uzavierkalog]. " . $deleteFromUzavierkaLog;
}

//ZAPISAT DO UZAVIERKALOG
$insertIntoUzavierkaLog = Connection::InsertUpdateCreateDelete("INSERT INTO uzavierkalog
    (error, dateuzavierky, datetimeactivity, activityid, fondid)
    (SELECT '', max(datum), CURRENT_DATE, 11, fondid FROM today GROUP BY fondid)", [], defaultDB);
if (gettype($insertIntoUzavierkaLog) !== "integer") {
	$errors[] = "Nepodarilo sa vložiť údaje do tabuľky [uzavierkalog]. " . $insertIntoUzavierkaLog;
}

//Zväčšiť evidenčný dátum o 1 deň
$updateToday = Connection::InsertUpdateCreateDelete("UPDATE today
    SET datum = datum + 1, confirmed = 1", [], defaultDB);
if (gettype($updateToday) !== "integer") {
	$errors[] = "Nepodarilo sa zväčšiť evidenčný dátum o 1 deň. " . $updateToday;
}


/** Dogenerujem zaznamy pre exkupon, existinu a splatenie AUV */
// !!! Pozor, toto sa da pouzit iba za predpokladu, ze sa uzatvara kazdy den!!!
// ak sa neuzatvara kazdy den, tak toto by sa malo vykonavat pri povrdeni datumu
// kedze sa to zapisuje pod obratid 0, tak sa to bude dat vystornovat

$deleteFromMajetokToday = Connection::InsertUpdateCreateDelete("DELETE FROM majetoktoday
    WHERE eqid = 'Bonds'
    AND is_coupon_date(kodaktiva, (SELECT max(datum)-1 FROM today))=1
    AND uctovnykod = 251120", [], defaultDB);
if (gettype($deleteFromMajetokToday) !== "integer") {
	$errors[] = "Nepodarilo sa zmazať údaje z tabuľky [majetoktoday4]. " . $deleteFromMajetokToday;
}

$insertIntoMajetokToday = Connection::InsertUpdateCreateDelete("INSERT INTO majetoktoday
    (obratid, destinacia, subjektid, kodobratu, uctovnykod, eqid, kodaktiva, ucetaktiva, jednotka, pocet, mena, md_d, obratdatetimereal, obratdatatimezauctovane)
    (select obratid, destinacia, subjektid, kodobratu, 251120, eqid, kodaktiva, ucetaktiva, jednotka, pocet, mena, md_d, obratdatetimereal, obratdatatimezauctovane
		from majetoktoday  
		where
			eqid = 'Bonds'
			and is_coupon_date(kodaktiva, (select max(datum)-1 from today))=1
			and uctovnykod = 251110)", [], defaultDB);
if (gettype($insertIntoMajetokToday) !== "integer") {
	$errors[] = "Nepodarilo sa vložiť údaje do tabuľky [majetoktoday5]. " . $insertIntoMajetokToday;
}

//KTV pri splateni, dogeneruje sa zaznam do majetokcesta
$splatneKTV = "Splatné KTV";
$insertIntoMajetokCesta = Connection::InsertUpdateCreateDelete("INSERT INTO majetokcesta 
    (dealid, tranza, in_out, sparovanie, destinacia, subjektid, eqid, kodaktiva, ucetaktiva, jednotka, mena, pocet, cestafrom, cestatill, popis)
    (
				select
					dealid,
					1,
					0,
					dealid,
					'konfirmaciaktv',
					k.subjektid,
					'TD',
					k.mena,
					cutd,
					k.mena,
					k.mena,
					( 				select sum(pocet) from majetoktoday m
						where
							m.ucetaktiva = cutd and
							m.uctovnykod in (
								select uctovnykod
								from kodobratumd_d
								where
									kodobratu = 253 and
									md_d = 0)
							and
							m.kodaktiva = k.mena and
							m.md_d = 0 and
						 	( (k.subjektid<>0 and m.subjektid=k.subjektid) or
						 		(k.subjektid=0 and m.subjektid in (select subjektid from pooldetailreal pd, pool p where p.dealid=k.dealid and p.poolid=pd.poolid)))
		
					) as splistina,
					k_td,
					to_date(to_char(t.datum, 'YYYY-MM-DD') || ' 23:59:59', 'YYYY-MM-DD HH24:MI:SS'),
					'$splatneKTV'
				from
					konfirmaciaktv k, today t
				where
					t.fondid=k.subjektid and
					to_date(to_char(k_td, 'YYYY-MM-DD'), 'YYYY-MM-DD') <= to_date(to_char(t.datum, 'YYYY-MM-DD'),'YYYY-MM-DD') and
					exists(
						select * from majetoktoday m
						where
							m.ucetaktiva = cutd and
							m.uctovnykod in (
								select uctovnykod
								from kodobratumd_d
								where
									kodobratu = 701 and
									md_d = 0)
							and
							m.kodaktiva = k.mena and
							m.md_d = 0 and
						 	( (k.subjektid<>0 and m.subjektid=k.subjektid) or
						 		(k.subjektid=0 and m.subjektid in (select subjektid from pooldetailreal pd, pool p where p.dealid=k.dealid and p.poolid=pd.poolid)))
					) and
					exists(
						select * from majetoktoday m
						where
							m.ucetaktiva = cutd and
							m.uctovnykod in (
								select uctovnykod
								from kodobratumd_d
								where
									kodobratu = 253 and
									md_d = 0)
							and
							m.kodaktiva = k.mena and
							m.md_d = 0 and
						 	( (k.subjektid<>0 and m.subjektid=k.subjektid) or
						 		(k.subjektid=0 and m.subjektid in (select subjektid from pooldetailreal pd, pool p where p.dealid=k.dealid and p.poolid=pd.poolid)))
					) and 
					k.logactivityid < 23 and
					k.dealid not in (
						select dealid
						from majetokcesta mc
						where
							mc.destinacia='konfirmaciaktv' and
							mc.popis='$splatneKTV'
						)
				)", [], defaultDB);
if (gettype($insertIntoMajetokCesta) !== "integer") {
	$errors[] = "Nepodarilo sa vložiť údaje do tabuľky [majetokcesta]. " . $insertIntoMajetokCesta;
}

if (!empty($errors)) {
	$notification = new Notification(
		12,
		'uzavierkalog',
		0,
		'uzavierkaFailed',
		0,
		'uzavierka',
		"uzavierkaFailed",
		json_encode(["dateuzavierky", $datumUzavierky]),
		false,
		0
	);
	$notification->createNotifcation();

	$insertIntoUzavierkaLog = Connection::InsertUpdateCreateDelete("INSERT INTO uzavierkalog
    (error, dateuzavierky, datetimeactivity, activityid, fondid)
    (select 'Chyba pri zápise do databázy', datum, CURRENT_DATE, 12, fondid from today)", [], defaultDB);
	?>
	<section class="flex gap-2">
		<ol class="relative text-gray-500 border-s border-gray-200 dark:border-gray-700 dark:text-gray-400">
			<li class="mb-10 ms-6">
				<span
					class="absolute flex items-center justify-center w-8 h-8 bg-green-200 rounded-full -start-4 ring-4 ring-white dark:ring-gray-900 dark:bg-green-900">
					<svg class="w-3.5 h-3.5 text-green-500 dark:text-green-400" aria-hidden="true"
						xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 16 12">
						<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
							d="M1 5.917 5.724 10.5 15 1.5" />
					</svg>
				</span>
				<div class="ml-3 text-red-400">
					<h3 class="font-medium leading-tight">Odhlásenie používateľov</h3>
					<p class="text-sm">Všetci používatelia boli odhlásení.</p>
				</div>
			</li>
			<li class="mb-10 ms-6">
				<span
					class="absolute flex items-center justify-center w-8 h-8 bg-green-200 rounded-full -start-4 ring-4 ring-white dark:ring-gray-900 dark:bg-green-900">
					<svg class="w-3.5 h-3.5 text-green-500 dark:text-green-400" aria-hidden="true"
						xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 16 12">
						<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
							d="M1 5.917 5.724 10.5 15 1.5" />
					</svg>
				</span>
				<div class="ml-3">
					<h3 class="font-medium leading-tight">Menové páry</h3>
					<p class="text-sm">Menové páry sa nepodarilo získať a overiť.</p>
				</div>
			</li>
			<li class="mb-10 ms-6">
				<span
					class="absolute flex items-center justify-center w-8 h-8 bg-green-200 rounded-full -start-4 ring-4 ring-white dark:ring-gray-900 dark:bg-green-900">
					<svg class="w-3.5 h-3.5 text-green-500 dark:text-green-400" aria-hidden="true"
						xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 16 12">
						<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
							d="M1 5.917 5.724 10.5 15 1.5" />
					</svg>
				</span>
				<div class="ml-3">
					<h3 class="font-medium leading-tight">Kurzy</h3>
					<p class="text-sm">Ocenenie BU a CP</p>
				</div>
			</li>
			<li class="mb-10 ms-6">
				<span
					class="absolute flex items-center justify-center w-8 h-8 bg-red-200 text-red-500 rounded-full -start-4 ring-4 ring-white dark:ring-gray-900 dark:bg-red-900">
					<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
						stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
						class="lucide lucide-circle-x-icon w-3.5 h-3.5 lucide-circle-x">
						<circle cx="12" cy="12" r="10" />
						<path d="m15 9-6 6" />
						<path d="m9 9 6 6" />
					</svg>
				</span>
				<div class="ml-3">
					<h3 class="font-medium leading-tight">Hlavný proces uzávierky</h3>
					<p class="text-sm">Uzávierka bola úspešne dokončená.</p>
				</div>
			</li>
			<li class="mb-10 ms-6">
				<span
					class="absolute flex items-center justify-center w-8 h-8 bg-gray-100 rounded-full -start-4 ring-4 ring-white dark:ring-gray-900 dark:bg-gray-700">
					<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
						stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
						class="w-3.5 h-3.5 text-gray-500 dark:text-gray-400">
						<path d="M20 6 9 17l-5-5" />
					</svg>
				</span>
				<div class="ml-3">
					<h3 class="font-medium leading-tight">Ocenenie</h3>
					<p class="text-sm">Príprava ocenenia</p>
				</div>
			</li>
			<li class="mb-10 ms-6">
				<span
					class="absolute flex items-center mr-4 justify-center w-8 h-8 bg-gray-100 rounded-full -start-4 ring-4 ring-white dark:ring-gray-900 dark:bg-gray-700">
					<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
						stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
						class="w-3.5 h-3.5 text-gray-500 dark:text-gray-400">
						<path d="M20 6 9 17l-5-5" />
					</svg>
				</span>
				<div class="ml-3">
					<h3 class="font-medium leading-tight">Generovanie reportov</h3>
					<p class="text-sm">Generovanie reportov a odosielanie emailov</p>
				</div>
			</li>
			<li class="ms-6">
				<span
					class="absolute flex items-center justify-center w-8 h-8 bg-gray-100 rounded-full -start-4 ring-4 ring-white dark:ring-gray-900 dark:bg-gray-700">
					<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
						stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
						class="w-3.5 h-3.5 text-gray-500 dark:text-gray-400">
						<path d="M20 6 9 17l-5-5" />
					</svg>
				</span>
				<div class="ml-3">
					<h3 class="font-medium leading-tight">Konfirmácia</h3>
					<p class="text-sm">Zhrnutie uzávierky a jej potvrdenie</p>
					<div class="ml-3"></div>
			</li>
		</ol>
		<div class="flex flex-col gap-6 items-center justify-center w-full">
			<svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none"
				stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
				class="lucide lucide-circle-x-icon text-red-500 lucide-circle-x">
				<circle cx="12" cy="12" r="10" />
				<path d="m15 9-6 6" />
				<path d="m9 9 6 6" />
			</svg>
			<h2 class="text-2xl font-bold text-red-700">Chyba!</h2>
			<div>
				<?php foreach ($errors as $error) { ?>
					<p class="text-red-500 mb-4"><?php echo $error; ?></p>
				<?php } ?>
			</div>
			<button type="button" class="text-white bg-gray-800 hover:bg-gray-900 focus:outline-none
				 focus:ring-4 focus:ring-gray-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 
				 dark:bg-gray-800 dark:hover:bg-gray-700 dark:focus:ring-gray-700 dark:border-gray-700"
				hx-get="/force-logout?restared=true" hx-target="#uzavierka-modal-body">Reštartovať</button>
		</div>
	</section>
<?php } else { ?>
	<section class="flex gap-2">
		<ol class="relative text-gray-500 border-s border-gray-200 dark:border-gray-700 dark:text-gray-400">
			<li class="mb-10 ms-6">
				<span
					class="absolute flex items-center justify-center w-8 h-8 bg-green-200 rounded-full -start-4 ring-4 ring-white dark:ring-gray-900 dark:bg-green-900">
					<svg class="w-3.5 h-3.5 text-green-500 dark:text-green-400" aria-hidden="true"
						xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 16 12">
						<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
							d="M1 5.917 5.724 10.5 15 1.5" />
					</svg>
				</span>
				<div class="ml-3">
					<h3 class="font-medium leading-tight">Odhlásenie používateľov</h3>
					<p class="text-sm">Všetci používatelia boli odhlásení.</p>
				</div>
			</li>
			<li class="mb-10 ms-6">
				<span
					class="absolute flex items-center justify-center w-8 h-8 bg-green-200 rounded-full -start-4 ring-4 ring-white dark:ring-gray-900 dark:bg-green-900">
					<svg class="w-3.5 h-3.5 text-green-500 dark:text-green-400" aria-hidden="true"
						xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 16 12">
						<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
							d="M1 5.917 5.724 10.5 15 1.5" />
					</svg>
				</span>
				<div class="ml-3">
					<h3 class="font-medium leading-tight">Menové páry</h3>
					<p class="text-sm">Menové páry sú kompletné.</p>
				</div>
			</li>
			<li class="mb-10 ms-6">
				<span
					class="absolute flex items-center justify-center w-8 h-8 bg-green-200 rounded-full -start-4 ring-4 ring-white dark:ring-gray-900 dark:bg-green-900">
					<svg class="w-3.5 h-3.5 text-green-500 dark:text-green-400" aria-hidden="true"
						xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 16 12">
						<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
							d="M1 5.917 5.724 10.5 15 1.5" />
					</svg>
				</span>
				<div class="ml-3">
					<h3 class="font-medium leading-tight">Kurzy</h3>
					<p class="text-sm">Ocenenie BU a CP</p>
				</div>
			</li>
			<li class="mb-10 ms-6">
				<span
					class="absolute flex items-center justify-center w-8 h-8 bg-green-200 rounded-full -start-4 ring-4 ring-white dark:ring-gray-900 dark:bg-green-900">
					<svg class="w-3.5 h-3.5 text-green-500 dark:text-green-400" aria-hidden="true"
						xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 16 12">
						<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
							d="M1 5.917 5.724 10.5 15 1.5" />
					</svg>
				</span>
				<div class="ml-3">
					<h3 class="font-medium leading-tight">Hlavný proces uzávierky</h3>
					<p class="text-sm">Uzávierka bola úspešne dokončená.</p>
				</div>
			</li>
			<li class="mb-10 ms-6">
				<span
					class="absolute flex items-center justify-center w-8 h-8 bg-gray-100 rounded-full -start-4 ring-4 ring-white dark:ring-gray-900 dark:bg-gray-700">
					<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
						stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
						class="w-3.5 h-3.5 animate-spin text-gray-500 dark:text-gray-400">
						<path d="M21 12a9 9 0 1 1-6.219-8.56" />
					</svg>
				</span>
				<div class="ml-3">
					<h3 class="font-medium leading-tight">Ocenenie</h3>
					<p class="text-sm">Príprava ocenenia</p>
				</div>
			</li>
			<li class="mb-10 ms-6">
				<span
					class="absolute flex items-center mr-4 justify-center w-8 h-8 bg-gray-100 rounded-full -start-4 ring-4 ring-white dark:ring-gray-900 dark:bg-gray-700">
					<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
						stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
						class="w-3.5 h-3.5 text-gray-500 dark:text-gray-400">
						<path d="M20 6 9 17l-5-5" />
					</svg>
				</span>
				<div class="ml-3">
					<h3 class="font-medium leading-tight">Generovanie reportov</h3>
					<p class="text-sm">Generovanie reportov a odosielanie emailov</p>
				</div>
			</li>
			<li class="ms-6">
				<span
					class="absolute flex items-center justify-center w-8 h-8 bg-gray-100 rounded-full -start-4 ring-4 ring-white dark:ring-gray-900 dark:bg-gray-700">
					<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
						stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
						class="w-3.5 h-3.5 text-gray-500 dark:text-gray-400">
						<path d="M20 6 9 17l-5-5" />
					</svg>
				</span>
				<div class="ml-3">
					<h3 class="font-medium leading-tight">Konfirmácia</h3>
					<p class="text-sm">Zhrnutie uzávierky a jej potvrdenie</p>
					<div class="ml-3"></div>
			</li>
		</ol>
		<div class="flex flex-col gap-6 items-center justify-center w-full">
			<svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" viewBox="0 0 24 24" fill="none"
				stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
				class="lucide lucide-loader-circle-icon animate-spin lucide-loader-circle">
				<path d="M21 12a9 9 0 1 1-6.219-8.56" />
			</svg>
			<span>Prebieha ocenenie...</span>
		</div>
		<script>
			setTimeout(() => {
				htmx.ajax('GET', "/uzavierka/process/ocenenie", {
					target: "#uzavierka-modal-body",
				});
			}, 500);
		</script>
	</section>
<?php } ?>
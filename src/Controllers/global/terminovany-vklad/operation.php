<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
require_once "/home/<USER>/www/src/lib/notifications/Notification.class.php";

$errors = [];
$data = json_decode($_POST["data"]);
$data = $data[0];
$sess_userid = $_SESSION["user"]["data"]["userid"];
$username = $_SESSION["user"]["data"]["username"];

$object = $data->object;

print_r($data->subjektid);
$dealid = $data->dealid;
if ($object === "istina") {
    $splatenaIstina = false;
    $dealid = $data->dealid;
    $splIstina = trim($data->sum_td);
    $cub = $data->cub;
    $till = $data->k_td;
    $vs = $data->dealid;
    $cutd = $data->cutd;
    $mena = $data->mena;
    $subjektid = $data->subjektid;

    $konfirmaciaKTV = Connection::getDataFromDatabase("SELECT * FROM konfirmaciaktv WHERE dealid = $dealid", defaultDB)[1];

    $currentDate = date("Y-m-d");

    //Nastaviť aktivitu na 23 => Splatenie istiny
    $updateKonfirmaciaKTV = Connection::InsertUpdateCreateDelete("UPDATE konfirmaciaktv SET logactivityid = 23 WHERE dealid = ?", [$dealid], defaultDB);
    $notification = new Notification(23, 'konfirmaciaktv', $dealid, 'splatenieIstiny', $sess_userid, $username, "splatenieIstiny", json_encode(["dealid", $dealid]), false, 0);

    $kodobratuArray = [112, 114];
    foreach ($kodobratuArray as $kodobratu) {
        $majetokObratID = Connection::getDataFromDatabase("SELECT nextval('majetok_today_obratid')", defaultDB)[1][0]["nextval"];
        $kodObratus = Connection::getDataFromDatabase("SELECT * FROM kodobratumd_d WHERE kodobratu = $kodobratu", defaultDB)[1];
        foreach ($kodObratus as $item) {
            $md_d = $item["md_d"];
            $uctovnykod = $item["uctovnykod"];
            $equid = $item["equid"];
            $pocet = $splIstina;
            $ucetakt = $cub;
            $datreal = $till;

            if ((intval($kodobratu) == 114) && (intval($md_d) == 1)) {
                $ucetakt = $cutd;
            }

            if ($subjektid > 0) {
                $obratdatatimezauctovane = Connection::getDataFromDatabase("SELECT datum FROM today WHERE fondid = $subjektid", defaultDB)[1][0]["datum"];

                $insertIntoMajetokToday = Connection::InsertUpdateCreateDelete("INSERT INTO majetoktoday (obratid, destinacia, subjektid,kodobratu,uctovnykod,eqid,kodaktiva,ucetaktiva,jednotka,pocet,mena,md_d,obratdatetimereal,obratdatatimezauctovane) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?)", [
                    $majetokObratID,
                    'konfrimaciaktv',
                    $subjektid,
                    $kodobratu,
                    $uctovnykod,
                    $equid,
                    $mena,
                    $ucetakt,
                    $pocet,
                    $mena,
                    $md_d,
                    $datreal,
                    $obratdatatimezauctovane,
                    $mena
                ], defaultDB);
                if (gettype($insertIntoMajetokToday) !== "integer") {
                    $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [majetoktoday1]. " . $insertIntoMajetokToday;
                }
            } else {
                // Ak subjektid = 0, tak sa vložia všetky subjekty z pooldetailreal
                $insertIntoMajetokToday = Connection::InsertUpdateCreateDelete("INSERT INTO majetoktoday
							(obratid,destinacia,subjektid,kodobratu,
							uctovnykod,eqid,kodaktiva,ucetaktiva,
							pocet,mena,md_d,obratdatetimereal,
							obratdatatimezauctovane,jednotka)
							SELECT
							" . $majetokObratID . ",'konfirmaciaktv',subjektid,$kodobratu,
							$uctovnykod,'$equid','$mena','$ucetakt',
							transsumareal,'$mena',$md_d,'$datreal',
							(select datum from today where fondid = subjektid),'$mena'
							from pooldetailreal pd, pool p where p.dealid=" . $dealid . " and p.poolid=pd.poolid", [], defaultDB);
                if (gettype($insertIntoMajetokToday) !== "integer") {
                    $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [majetoktoday2]. " . $insertIntoMajetokToday;
                }
            }
        }
    }

    $insertIntoKTVExportObratID = Connection::InsertUpdateCreateDelete("INSERT INTO ktvexportobratid (dealid,obratid,status) VALUES (?,?,?)", [$dealid, $majetokObratID, 1], defaultDB);
    if (gettype($insertIntoKTVExportObratID) !== "integer") {
        $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [ktvexportobratid]. " . $insertIntoKTVExportObratID;
    }

    $insertIntoMajetokCesta = Connection::InsertUpdateCreateDelete("INSERT INTO majetokcesta (dealid,tranza,in_out,sparovanie,destinacia,subjektid,eqid,kodaktiva,ucetaktiva,jednotka,mena,pocet,cestafrom,cestatill,popis) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)", [
        $dealid,
        1,
        1,
        $vs,
        'konfirmaciaktv',
        $subjektid,
        'BU',
        $mena,
        $cub,
        $mena,
        $mena,
        (float) trim($splIstina),
        $currentDate,
        $till,
        'Splatná istina'
    ], defaultDB);
    if (gettype($insertIntoMajetokCesta) !== "integer") {
        $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [majetokcesta]. " . $insertIntoMajetokCesta;
    }

    $splatenaIstina = true;

    // NATIPOVANIE PLATBY A ISTINY
    $obratyBUID = Connection::getDataFromDatabase("SELECT nextval('s_obratybu')", defaultDB)[1][0]["nextval"];
    $insertIntoObratyBU = Connection::InsertUpdateCreateDelete("INSERT INTO obratybu (id,cub,cubpartnera,forma,ks,mena,nazpartnera,obratdatetime,ss,subjektid,suma,vs,krdb,logdatatimeactivity,logactivityid,loguserid) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)", [
        $obratyBUID,
        $cub,
        '',
        0,
        '',
        $mena,
        '',
        $till,
        '',
        $subjektid,
        (float) trim($splIstina),
        $vs,
        1,
        $currentDate,
        1,
        $sess_userid
    ], defaultDB);
    if (gettype($insertIntoObratyBU) !== "integer") {
        $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [obratybu]. " . $insertIntoObratyBU;
    }

    $notification = new Notification(1, 'obratybu', $obratyBUID, 'zaevidovaniePlatby', $sess_userid, $username, "splatenieIstiny", json_encode(["id", $obratyBUID]), false, 0);
} else {
    // Splatenie uroku
    $dealid = $data->dealid;
    $splUrok = $data->sum_td;
    $cub = $data->cub;
    $till = $data->k_td;
    $vs = $data->dealid;
    $cutd = $data->cutd;
    $mena = $data->mena;
    $subjektid = $data->subjektid;
    $mktd = $data->k_td;
    $iv_n_old = $data->iv_n;
    $splUrokBrutto = $data->zaklad_dane;
    $sadzba_dane = $data->sadzba_dane;
    $dan = $data->dan;

    $konfirmaciaKTV = Connection::getDataFromDatabase("SELECT * FROM konfirmaciaktv WHERE dealid = $dealid", defaultDB)[1];

    $currentDate = date("Y-m-d");

    $updateKonfirmaciaKTV = Connection::InsertUpdateCreateDelete("UPDATE konfirmaciaktv SET logactivityid = 24 WHERE dealid = ?", [$dealid], defaultDB);
    $notification = new Notification(24, 'konfirmaciaktv', $dealid, 'splatenieUroku', $sess_userid, $username, "splatenieUroku", json_encode(["dealid", $dealid]), false, 0);

    // rozpocitanie poolovaneho uroku
    $urokBrutto_arr = array();
    $urokNetto_arr = array();
    $urok_rest_brutto = array();
    $urok_rest_netto = array();
    $offset_brutto = array();
    $offset_netto = array();
    $urokBrutto_accum = 0;
    $urokNetto_accum = 0;

    if ($subjektid == 0) {
        $selectQuery = "SELECT
						pd.subjektid,
						pd.poolid, 
						floor($splUrokBrutto*transsumareal/k.sum_td*100)/100 as urokBrutto,
						floor($splUrok*transsumareal/k.sum_td*100)/100 as urokNetto,
						round((((($splUrokBrutto*transsumareal/k.sum_td*100)/100)-(floor($splUrokBrutto*transsumareal/k.sum_td*100)/100))*100000),0) as OfsetBrutto,
						round((((($splUrok*transsumareal/k.sum_td*100)/100)-(floor($splUrok*transsumareal/k.sum_td*100)/100))*100000),0) as OfsetNetto
					from 
						konfirmaciaktv k, pooldetailreal pd, pool p 
					where 
						k.dealid=p.dealid and 
						p.dealid=" . $dealid . " and 
						p.poolid=pd.poolid
					order by
						transsumareal desc, pd.subjektid";
        $detail = Connection::getDataFromDatabase($selectQuery, defaultDB)[1];
        foreach ($detail as $item) {
            $urokBrutto_arr[$item["subjektid"]] = $item["urokbrutto"];
            $urokNetto_arr[$item["subjektid"]] = $item["uroknetto"];
            $urok_rest_brutto[] = $item["subjektid"];
            $urok_rest_netto[] = $item["subjektid"];
            $offset_brutto[] = $item["ofsetbrutto"];
            $offset_netto[] = $item["ofsetnetto"];
            $urokBrutto_accum += $item["urokbrutto"];
            $urokNetto_accum += $item["uroknetto"];
        }
        array_multisort($offset_brutto, SORT_NUMERIC, SORT_DESC, $urok_rest_brutto);
        array_multisort($offset_netto, SORT_NUMERIC, SORT_DESC, $urok_rest_netto);

        if (($splUrokBrutto - $urokBrutto_accum) > 0.001) {
            $subjekt_index = 0;
            while (($splUrokBrutto - $urokBrutto_accum) > 0.001 || $subjekt_index < sizeof($urokBrutto_arr)) {
                echo $subjekt_index . " ";
                $urokBrutto_arr[$urok_rest_brutto[$subjekt_index]] += 0.01;
                $urokBrutto_accum += 0.01;
                $subjekt_index++;
            }
        }

        if (($splUrok - $urokNetto_accum) > 0.001) {
            $subjekt_index = 0;
            while (($splUrok - $urokNetto_accum) > 0.001) {
                echo $subjekt_index . " ";
                $urokNetto_arr[$urok_rest_netto[$subjekt_index]] += 0.01;
                $urokNetto_accum += 0.01;
                $subjekt_index++;
            }
        }

        $poolid = $item["poolid"];
    }

    $kodObratuArr = [113, 115];
    foreach ($kodObratuArr as $kodobratu) {
        $majetokObratID = Connection::getDataFromDatabase("SELECT nextval('majetok_today_obratid')", defaultDB)[1][0]["nextval"];
        $poplatok = 1;

        $kodObratus = Connection::getDataFromDatabase("SELECT * FROM kodobratumd_d WHERE kodobratu = $kodobratu", defaultDB)[1];
        foreach ($kodObratus as $item) {
            $md_d = $item["md_d"];
            $uctovnykod = $item["uctovnykod"];
            $equid = $item["equid"];
            $pocet = $splUrokBrutto;
            $ucetakt = $cub;
            $datreal = $till;

            if ((intval($kodobratu) == 115) && (intval($md_d) == 1)) {
                $ucetakt = $cutd;
            }

            if ($subjektid > 0) {
                //peniaze na ceste uctujeme v netto, brutto odratavame vedenu pohladavku
                if ($kodobratu == 113) {
                    $pocet = $splUrok;
                }

                $obratdatatimezauctovane = Connection::getDataFromDatabase("SELECT datum FROM today WHERE fondid = $subjektid", defaultDB)[1][0]["datum"];

                $insertIntoMajetokToday = Connection::InsertUpdateCreateDelete("INSERT INTO majetoktoday
								(obratid,destinacia,subjektid,kodobratu,
								uctovnykod,eqid,kodaktiva,ucetaktiva,
								pocet,mena,md_d,obratdatetimereal,
								obratdatatimezauctovane,jednotka)
								SELECT
								$majetokObratID,'konfirmaciaktv',$subjektid, 116, uctovnykod,
								'$equid','$mena','$ucetakt',
								COALESCE((COALESCE((select pocet from majetoktoday where mena='$mena' and subjektid=$subjektid and obratid=0 and uctovnykod='315113' and ucetaktiva='$ucetakt' and md_d=0),0)-$splUrokBrutto),0),                
								'$mena', md_d, '$datreal',
								(select datum from today where fondid = $subjektid),'$mena'
								FROM kodobratumd_d, konfirmaciaktv k   WHERE kodobratu=116 and k.dealid=" . $dealid, [], defaultDB);
                if (gettype($insertIntoMajetokToday) !== "integer") {
                    $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [majetoktoday1]. " . $insertIntoMajetokToday;
                }
            } else {

                foreach ($urokBrutto_arr as $subjektid => $suma_uroku) {
                    if ($kodobratu == 113) {
                        $suma_uroku = $urokNetto_arr[$subjektid];
                    }
                    $obratdatatimezauctovane = Connection::getDataFromDatabase("SELECT datum FROM today WHERE fondid = $subjektid", defaultDB)[1][0]["datum"];

                    $insertIntoMajetokToday = Connection::InsertUpdateCreateDelete("INSERT INTO majetoktoday (obratid, destinacia, subjektid,kodobratu,uctovnykod,eqid,kodaktiva,ucetaktiva,jednotka,pocet,mena,md_d,obratdatetimereal,obratdatatimezauctovane) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?)", [
                        $majetokObratID,
                        'konfrimaciaktv',
                        $subjektid,
                        $kodobratu,
                        $uctovnykod,
                        $equid,
                        $mena,
                        $ucetakt,
                        $mena,
                        $suma_uroku,
                        $mena,
                        $md_d,
                        $datreal,
                        $obratdatatimezauctovane
                    ], defaultDB);
                    if (gettype($insertIntoMajetokToday) !== "integer") {
                        $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [majetoktoday2]. " . $insertIntoMajetokToday;
                    }
                }
            }
        }

        $insertIntoKTVExportObratID = Connection::InsertUpdateCreateDelete("INSERT INTO ktvexportobratid (dealid,obratid,status) VALUES (?,?,?)", [$dealid, $majetokObratID, 2], defaultDB);

        $rozdiel = (float) $splUrokBrutto - (float) $splUrokBrutto;
        $rozdielpocet = abs($rozdiel);
        $md_d_sign = ($rozdiel > 0) ? "" : "1 - ";

        $obratID = Connection::getDataFromDatabase("SELECT nextval('majetok_today_obratid')", defaultDB)[1][0]["nextval"];

        if ($subjektid > 0) {
            $insertIntoMajetokToday = Connection::InsertUpdateCreateDelete("INSERT INTO majetoktoday
								(obratid,destinacia,subjektid,kodobratu,
								uctovnykod,eqid,kodaktiva,ucetaktiva,
								pocet,mena,md_d,obratdatetimereal,
								obratdatatimezauctovane,jednotka)
								SELECT
								$majetokObratID,'konfirmaciaktv',$subjektid, 116, uctovnykod,
								'$equid','$mena','$ucetakt',
								COALESCE((COALESCE((select pocet from majetoktoday where mena='$mena' and subjektid=$subjektid and obratid=0 and uctovnykod='315113' and ucetaktiva='$ucetakt' and md_d=0),0)-$splUrokBrutto),0),                
								'$mena', md_d, '$datreal',
								(select datum from today where fondid = $subjektid),'$mena'
								FROM kodobratumd_d, konfirmaciaktv k   WHERE kodobratu=116 and k.dealid=" . $dealid, [], defaultDB);
            if (gettype($insertIntoMajetokToday) !== "integer") {
                $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [majetoktoday3]. " . $insertIntoMajetokToday;
            }
        } else {
            foreach ($urokBrutto_arr as $subjektID => $suma_uroku) {
                $insertIntoMajetokToday = Connection::InsertUpdateCreateDelete("INSERT INTO majetoktoday
									(obratid,destinacia,subjektid,kodobratu,
									uctovnykod,eqid,kodaktiva,ucetaktiva,
									pocet,mena,md_d,obratdatetimereal,
									obratdatatimezauctovane,jednotka)
									SELECT
									$majetokObratID,'konfirmaciaktv',$subjektID, 116, uctovnykod,
									'$equid','$mena','$ucetakt',
									COALESCE((COALESCE((select pocet from majetoktoday where mena='$mena' and subjektid=$subjektID and obratid=0 and uctovnykod='315113' and ucetaktiva='$ucetakt' and md_d=0),0))-$suma_uroku,0),
									'$mena',md_d, '$datreal',
									(select datum from today where fondid = $subjektID),'$mena'
									FROM kodobratumd_d, konfirmaciaktv k 
									WHERE kodobratu=116 and k.dealid=" . $dealid, [], defaultDB);
                if (gettype($insertIntoMajetokToday) !== "integer") {
                    $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [majetoktoday4]. " . $insertIntoMajetokToday;
                }

                $updatePoolDetailReal = Connection::InsertUpdateCreateDelete("UPDATE pooldetailreal SET auvreal = ?, dan = ? WHERE poolid = ? AND subjektid = ?", [$suma_uroku, ($suma_uroku - $urokNetto_arr[$subjektID]), $poolid, $subjektID], defaultDB);
                if (gettype($updatePoolDetailReal) !== "integer") {
                    $errors[] = "Nepodarilo sa updatovať tabuľku [pooldetailreal]. " . $updatePoolDetailReal;
                }
            }
        }

        $insertIntoMajetokCesta = Connection::InsertUpdateCreateDelete("INSERT INTO majetokcesta (dealid,tranza,in_out,sparovanie,destinacia,subjektid,eqid,kodaktiva,ucetaktiva,jednotka,mena,pocet,cestafrom,cestatill,popis) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)", [
            $dealid,
            2,
            1,
            $vs,
            'konfirmaciaktv',
            $subjektid,
            'BU',
            $mena,
            $cub,
            $mena,
            $mena,
            $splUrok,
            $currentDate,
            $till,
            'Splatný úrok'
        ], defaultDB);
        if (gettype($insertIntoMajetokCesta) !== "integer") {
            $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [majetokcesta]. " . $insertIntoMajetokCesta;
        }

        $splatenyUrok = true;

        $updateKonfirmaciaKTV = Connection::InsertUpdateCreateDelete("UPDATE konfirmaciaktv SET cubdan=?, iv_n=?, iv_b=?, dan=?, suma_dane=? WHERE dealid=?", [$cub, $splUrok, $splUrokBrutto, $sadzba_dane, $dan, $dealid], defaultDB);

        //NATIPOVANIE PLATBY A UROKU
        $obratyBUID = Connection::getDataFromDatabase("SELECT nextval('s_obratybu')", defaultDB)[1][0]["nextval"];
        $insertIntoObratyBU = Connection::InsertUpdateCreateDelete("INSERT INTO obratybu (id,cub,cubpartnera,forma,ks,mena,nazpartnera,obratdatetime,ss,subjektid,suma,vs,krdb,logdatatimeactivity,logactivityid,loguserid) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)", [
            $obratyBUID,
            $cub,
            '',
            0,
            '',
            $mena,
            '',
            $till,
            '',
            $subjektid,
            $splUrok,
            $vs,
            1,
            $currentDate,
            1,
            $sess_userid
        ], defaultDB);
        if (gettype($insertIntoObratyBU) !== "integer") {
            $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [obratybu]. " . $insertIntoObratyBU;
        }

        $notification = new Notification(1, 'obratybu', $obratyBUID, 'splatenieUroku', $sess_userid, $username, "splatenieUroku", json_encode(["id", $obratyBUID]), false, 0);
    }
}

if (!$splatenaIstina) {
    $getNotif = Connection::getDataFromDatabase("SELECT * FROM notifications WHERE destinacia = 'konfirmaciaktv' AND activityid = 23 AND objektid = $dealid", defaultDB)[1][0];
    if ($getNotif !== NULL) {
        $splatenaIstina = true;
    }
}
if (!$splatenyUrok) {
    $getNotif = Connection::getDataFromDatabase("SELECT * FROM notifications WHERE destinacia = 'konfirmaciaktv' AND activityid = 24 AND objektid = $dealid", defaultDB)[1][0];
    if ($getNotif !== NULL) {
        $splatenyUrok = true;
    }
}
if ($splatenyUrok && $splatenaIstina) {
    $updateKonfirmaciaKTV = Connection::InsertUpdateCreateDelete("UPDATE konfirmaciaktv SET logactivityid = ? WHERE dealid = ?", [25, $dealid], defaultDB);
    $notification = new Notification(25, 'konfirmaciaktv', $dealid, 'splatenieAll', $sess_userid, $username, "splatenieAll", json_encode(["dealid", $dealid]), false, 0);
}

if (
    empty($errors)
) {
    ?>
    <div id="toast-success"
        class="flex border-green-300 border items-center w-full max-w-sm p-4 mb-4 text-green-500 bg-white absolute right-5 rounded-lg shadow dark:text-gray-400 dark:bg-gray-800"
        role="alert">
        <div
            class="inline-flex items-center justify-center flex-shrink-0 w-8 h-8 text-green-500 bg-green-100 rounded-lg dark:bg-green-800 dark:text-green-200">
            <svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                viewBox="0 0 20 20">
                <path
                    d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 8.207-4 4a1 1 0 0 1-1.414 0l-2-2a1 1 0 0 1 1.414-1.414L9 10.586l3.293-3.293a1 1 0 0 1 1.414 1.414Z" />
            </svg>
            <span class="sr-only">Check icon</span>
        </div>
        <div class="ms-3 text-sm font-semibold">Operácia bola úspešne dokončená.</div>
        <button type="button"
            class="ms-auto -mx-1.5 -my-1.5 bg-white text-gray-400 hover:text-gray-900 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex items-center justify-center h-8 w-8 dark:text-gray-500 dark:hover:text-white dark:bg-gray-800 dark:hover:bg-gray-700"
            data-dismiss-target="#toast-success" aria-label="Close">
            <span class="sr-only">Close</span>
            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
            </svg>
        </button>
    </div>
    <script>
        setTimeout(() => {
            window.location.reload();
        }, 1000);
    </script>
<?php } else {
    echo "<pre>";
    print_r($errors);
    echo "</pre>";
    ?>
    <div id="toast-danger"
        class="flex items-center w-full max-w-sm p-4 mb-4 text-white absolute right-5 bg-red-500 rounded-lg shadow-lg"
        role="alert">
        <?php ?>
        <div
            class="inline-flex items-center justify-center flex-shrink-0 w-8 h-8 text-red-500 bg-red-100 rounded-lg dark:bg-red-800 dark:text-red-200">
            <svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                viewBox="0 0 20 20">
                <path
                    d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 11.793a1 1 0 1 1-1.414 1.414L10 11.414l-2.293 2.293a1 1 0 0 1-1.414-1.414L8.586 10 6.293 7.707a1 1 0 0 1 1.414-1.414L10 8.586l2.293-2.293a1 1 0 0 1 1.414 1.414L11.414 10l2.293 2.293Z" />
            </svg>
            <span class="sr-only">Error icon</span>
        </div>
        <div class="ms-3 text-sm font-bold">Investičný zámer sa nepodarilo vytvoriť kvôli neznámej chybe!</div>
        <button type="button"
            class="ms-auto -mx-1.5 -my-1.5 text-white hover:text-gray-900 transition-all rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex items-center justify-center h-8 w-8 dark:text-gray-500 dark:hover:text-white dark:bg-gray-800 dark:hover:bg-gray-700"
            data-dismiss-target="#toast-danger" aria-label="Close">
            <span class="sr-only">Close</span>
            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
            </svg>
        </button>
    </div>
    <script>
        $("#submitter span").html("Potvrdiť");
        $("#submitter svg").css("display", "none");
        setTimeout(() => {
            $("#toast-danger").fadeOut();
        }, 2500);
    </script>
<?php }
?>
<?php
require_once('/home/<USER>/www/src/lib/connection.php');
require_once("/home/<USER>/www/conf/settings.php");

$data = json_decode($_POST["data"]);
$page = $_POST["page"];
$limit = $_POST["limit"];

if (isset($data->reconfirmed) && $data->reconfirmed == "1") {
    $reconfirmedQuery = " pr.stav = IN(1, 5)";
} elseif (isset($data->reconfirmed) && $data->reconfirmed == "2") {
    $reconfirmedQuery = " pr.stav = 0";
} else {
    $reconfirmedQuery = " pr.stav IN (0, 1, 5)";
}

if (sizeof($data->types) > 0) {
    $typQuery = " AND a.typ IN (";
    foreach ($data->types as $type) {
        $typQuery .= "'$type',";
    }
    $typQuery = substr($typQuery, 0, -1);
    $typQuery .= ")";
}

if (sizeof($data->clients) > 0) {
    $clientQuery = " AND a.cislozmluvy IN (";
    foreach ($data->clients as $client) {
        $clientQuery .= "'$client',";
    }
    $clientQuery = substr($clientQuery, 0, -1);
    $clientQuery .= ")";
}

if (sizeof($data->isins) > 0) {
    $isinQuery = " AND a.isin IN (";
    foreach ($data->isins as $isin) {
        $isinQuery .= "'$isin',";
    }
    $isinQuery = substr($isinQuery, 0, -1);
    $isinQuery .= ")";
}

if (sizeof($data->meny) > 0) {
    $menaQuery = " AND a.mena IN (";
    foreach ($data->meny as $mena) {
        $menaQuery .= "'$mena',";
    }
    $menaQuery = substr($menaQuery, 0, -1);
    $menaQuery .= ")";
}

if ($data->hotovost === 1) {
    $hotovostQuery = " AND a.suma <= mt.cash AND a.suma >= 0 ";
} elseif ($data->hotovost === 2) {
    $hotovostQuery = " AND a.suma > mt.cash and a.suma >= 0 ";
} else {
    $hotovostQuery = "";
}

if (sizeof($data->dates) > 0) {
    $dateQuery = " AND a.datum IN (";
    foreach ($data->dates as $date) {
        $dateQuery .= "'$date',";
    }
    $dateQuery = substr($dateQuery, 0, -1);
    $dateQuery .= ")";
} else {
    $dateQuery = "";
}

$count = Connection::getDataFromDatabase("WITH cash as (select round(sum(((0.5 - md_d) / 0.5) * pocet), 2) as cash, mena, ucetaktiva, subjektid
              from majetoktoday
              where subjektid > 1
                and uctovnykod in (221110, 325111, 325121, 325122, 325130, 325140, 325150, 325160)
                and eqid = 'BU'
              group by mena, ucetaktiva, subjektid),
     main as (SELECT pr.id,
                     pr.suma      AS suma,
                     pr.mena,
                     po.cislozmluvy,
                     pr.datum,
                     CASE
                         WHEN pr.typ = 'TRAN' THEN 'Transakcia s CP'
                         WHEN pr.typ = 'VYROV' THEN 'Vyrovnanie transakcie'
                         WHEN pr.typ = 'SPRAVA' THEN 'Za správu'
                         WHEN pr.typ = 'MANAZ' THEN 'Za riadenie'
                         WHEN pr.typ = 'OSTATNE' THEN 'Ostatné'
                         ELSE 'Neznáma operácia'
                         END      AS typ,
                     pr.typ       AS typ_db,
                     pr.fondid,
                     CASE
                         WHEN f.uhrada_poplatkov = 1 THEN 'inkaso'
                         ELSE 'faktura'
                         END      AS uhrada_poplatkov,
                     pr.cub       AS cub,
                     pr.dansadzba AS dansadzba1,
                     kcp.isin
              FROM portfolio po
                       JOIN poplatok_register pr ON po.fondid = pr.fondid
                       JOIN fonds f ON f.fondid = po.fondid
                       LEFT JOIN konfirmaciacp kcp ON pr.dealid = kcp.dealid
              WHERE $reconfirmedQuery
                AND pr.datum IS NOT NULL AND pr.suma IS NOT NULL)
select COUNT(*) as count
FROM main a
         LEFT JOIN cash mt ON a.cub = mt.ucetaktiva and a.fondid = mt.subjektid and a.mena = mt.mena
WHERE 1=1 
$typQuery
$clientQuery
$isinQuery
$menaQuery
$hotovostQuery
$dateQuery", defaultDB)[1][0]["count"];

$poplatkyQuery = "WITH cash as (select round(sum(((0.5 - md_d) / 0.5) * pocet), 2) as cash, mena, ucetaktiva, subjektid
              from majetoktoday
              where subjektid > 1
                and uctovnykod in (221110, 325111, 325121, 325122, 325130, 325140, 325150, 325160)
                and eqid = 'BU'
              group by mena, ucetaktiva, subjektid),
main as (SELECT pr.id,
                     pr.suma      AS suma,
                     pr.mena,
                     po.cislozmluvy,
                     pr.datum,
                     CASE
                         WHEN pr.typ = 'TRAN' THEN 'Transakcia s CP'
                         WHEN pr.typ = 'VYROV' THEN 'Vyrovnanie transakcie'
                         WHEN pr.typ = 'SPRAVA' THEN 'Za správu'
                         WHEN pr.typ = 'MANAZ' THEN 'Za riadenie'
                         WHEN pr.typ = 'OSTATNE' THEN 'Ostatné'
                         ELSE 'Neznáma operácia'
                         END      AS typ,
                     pr.typ       AS typ_db,
                     pr.fondid,
                     CASE
                         WHEN f.uhrada_poplatkov = 1 THEN 'inkaso'
                         ELSE 'faktura'
                         END      AS uhrada_poplatkov,
                     pr.cub       AS cub,
                     pr.dansadzba AS dansadzba1,
                     kcp.isin
              FROM portfolio po
                       JOIN poplatok_register pr ON po.fondid = pr.fondid
                       JOIN fonds f ON f.fondid = po.fondid
                       LEFT JOIN konfirmaciacp kcp ON pr.dealid = kcp.dealid
              WHERE $reconfirmedQuery
                AND pr.datum IS NOT NULL AND pr.suma IS NOT NULL)
select a.id,
       a.suma,
       a.mena,
       a.cislozmluvy,
       a.datum,
       a.typ as typ,
       a.typ_db,
       a.fondid,
       a.uhrada_poplatkov,
       a.cub,
       a.dansadzba1,
       a.isin,
       COALESCE(mt.cash, 0)                                                         as cash
FROM main a
         LEFT JOIN cash mt ON a.cub = mt.ucetaktiva and a.fondid = mt.subjektid and a.mena = mt.mena
WHERE 1=1 
$typQuery
$clientQuery
$isinQuery
$menaQuery
$hotovostQuery
$dateQuery
order by a.cislozmluvy, a.mena, a.typ";

if ($page !== 1) {
    $calc_page = ($page - 1) * $limit;
} else {
}
$total_count = $count;
?>
<form id="paginationForm" class="mb-0 w-full flex justify-between" hx-replace-url="true" hx-push-url="true">
    <input type="hidden" id="filteredValuesInput" name="filteredValuesInput" value='<?php echo $_POST["data"]; ?>' />
    <input type="hidden" id="numPageInput" name="page" value="<?php echo $page; ?>" />
    <span
        class="text-sm font-normal text-gray-500 dark:text-gray-400 mb-4 md:mb-0 block w-full md:inline md:w-auto">Počet
        výsledkov: po requeste
        <span class="font-semibold text-gray-900 dark:text-white"></span><span
            class="font-semibold text-gray-900 dark:text-white"
            id="totalCountBottom"><?php echo $count; ?></span></span>
    <?php if (ceil($total_count / $limit) > 0): ?>
        <ul class="inline-flex -space-x-px rtl:space-x-reverse text-sm h-8">
            <?php if ($page > 1): ?>
                <input type="hidden" id="numPageInput" name="page" value="<?php echo $page - 1; ?>" />
                <button type="submit" value="?page=<?php echo $page - 1 ?>&limit=<?php echo $limit; ?>">
                    <li
                        class="flex items-center justify-center px-3 h-8 ms-0 leading-tight text-gray-500 bg-white border border-gray-300 rounded-s-lg hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                        Prev
                    </li>
                </button>
            <?php endif; ?>

            <?php if ($page > 3): ?>
                <input type="hidden" id="numPageInput" name="page" value="<?php echo $page; ?>" />
                <button type="submit" value="?page=1&limit=<?php echo $limit; ?>">
                    <li
                        class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                        1
                    </li>
                </button>
                <li
                    class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                    ...</li>
            <?php endif; ?>

            <?php if ($page - 2 > 0): ?>
                <input type="hidden" id="numPageInput" name="page" value="<?php echo $page - 2; ?>" />
                <button type="submit" value="?page=<?php echo $page - 2 ?>&limit=<?php echo $limit; ?>">
                    <li
                        class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                        <?php echo $page - 2 ?>
                    </li>
                </button>
            <?php endif; ?>
            <?php if ($page - 1 > 0): ?>
                <input type="hidden" id="numPageInput" name="page" value="<?php echo $page - 1; ?>" />
                <button type="submit" value="?page=<?php echo $page - 1 ?>&limit=<?php echo $limit; ?>">
                    <li
                        class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                        <?php echo $page - 1 ?>
                    </li>
                </button>
            <?php endif; ?>
            <input type="hidden" id="numPageInput" name="page" value="<?php echo $page; ?>&limit=<?php echo $limit; ?>" />
            <button type="submit" value="?page=<?php echo $page ?>&limit=<?php echo $limit; ?>">
                <li
                    class="flex items-center justify-center px-3 h-8 text-blue-600 border border-gray-300 bg-blue-50 hover:bg-blue-100 hover:text-blue-700 dark:border-gray-700 dark:bg-gray-700 dark:text-white">
                    <?php echo $page ?>
                </li>
            </button>

            <?php if ($page + 1 < ceil($total_count / $limit) + 1): ?>
                <input type="hidden" id="numPageInput" name="page"
                    value="<?php echo $page + 1; ?>&limit=<?php echo $limit; ?>" />
                <button type="submit" value="?page=<?php echo $page + 1 ?>&limit=<?php echo $limit; ?>">
                    <li
                        class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                        <?php echo $page + 1 ?>
                    </li>
                </button>
            <?php endif; ?>
            <?php if ($page + 2 < ceil($total_count / $limit) + 1): ?>
                <input type="hidden" id="numPageInput" name="page"
                    value="<?php echo $page + 2; ?>&limit=<?php echo $limit; ?>" />
                <button type="submit" value="?page=<?php echo $page + 2 ?>&limit=<?php echo $limit; ?>">
                    <li
                        class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                        <?php echo $page + 2 ?>
                    </li>
                </button>
            <?php endif; ?>
            <?php if ($page < ceil($total_count / $limit) - 2): ?>
                <li
                    class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                    ...</li>
                <input type="hidden" id="numPageInput" name="page" value="<?php echo ceil($total_count / $limit); ?>" />
                <button type="submit"
                    value="?page=<?php echo ceil($total_count / $limit) ?>&limit=<?php echo $limit; ?></button>">
                    <li
                        class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                        <?php echo ceil($total_count / $limit) ?>
                    </li>
                </button>
            <?php endif; ?>
            <?php if ($page < ceil($total_count / $limit)): ?>
                <input type="hidden" id="numPageInput" name="page" value="<?php echo $page + 1; ?>" />
                <button type="submit" value="?page=<?php echo $page + 1 ?>&limit=<?php echo $limit; ?>">
                    <li
                        class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 rounded-e-lg hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                        Next
                    </li>
                </button>
            <?php endif; ?>
        </ul>
    <?php endif; ?>
</form>
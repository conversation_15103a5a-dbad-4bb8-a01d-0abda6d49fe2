<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

$typyPoplatkovQuery = "SELECT DISTINCT
       kcp.isin as isin
FROM portfolio po
         JOIN poplatok_register pr ON po.fondid = pr.fondid
         JOIN fonds f ON f.fondid = po.fondid
         LEFT JOIN konfirmaciacp kcp ON pr.dealid = kcp.dealid
WHERE pr.stav IN (0,1,5)
  AND pr.datum IS NOT NULL AND pr.suma IS NOT NULL";

$typyPoplatkov = Connection::getDataFromDatabase($typyPoplatkovQuery, defaultDB)[1]; ?>
<ul class="p-3 text-sm text-gray-700 dark:text-gray-200" aria-labelledby="dropdownRadioButton">
    <?php foreach ($typyPoplatkov as $key => $poplatok) { ?>
        <li class="mb-0.5">
            <label
                class="w-full text-sm font-medium text-gray-900 rounded-sm dark:text-gray-300 flex cursor-pointer transition-all items-center gap-2 p-2 rounded-sm hover:bg-gray-100 dark:hover:bg-gray-600">
                <input id="filter-radio-example-<?php echo $key; ?>" type="checkbox" onchange="submitWholeForm(this)"
                    value="<?php echo $poplatok["isin"] ? $poplatok["isin"] : "empty"; ?>" name="isinFilter"
                    class="w-4 h-4 filterInput text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                <?php echo $poplatok["isin"] ? $poplatok["isin"] : "bez ISINU"; ?>
            </label>
        </li>
    <?php } ?>
</ul>
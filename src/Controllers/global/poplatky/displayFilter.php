<?php
require_once('/home/<USER>/www/src/lib/connection.php');
require_once("/home/<USER>/www/conf/settings.php");

$data = json_decode($_POST["data"]);

if ($data->$reconfirmed !== "") {
    if ($data->$reconfirmed === "1" || $data->reconfirmed === "2") { ?>
        <span
            class="bg-green-100 text-green-800 text-xs border font-medium me-2 px-2.5 py-0.5 rounded-sm dark:bg-green-900 dark:text-green-300">
            <?php echo $data->$reconfirmed === "1" ? "Rekonfirmovaný" : "Nerekonfirmovaný"; ?>
        </span>
    <?php }
}
foreach ($data->dates as $date) { ?>
    <span
        class="bg-green-100 text-green-800 text-xs border font-medium me-2 px-2.5 py-0.5 rounded-sm dark:bg-green-900 dark:text-green-300">
        <?php echo $date; ?>
    </span>
<?php }
foreach ($data->types as $type) { ?>
    <span
        class="bg-green-100 text-green-800 text-xs border font-medium me-2 px-2.5 py-0.5 rounded-sm dark:bg-green-900 dark:text-green-300">
        <?php echo $type; ?>
    </span>
<?php }
foreach ($data->clients as $client) { ?>
    <span
        class="bg-green-100 text-green-800 text-xs border font-medium me-2 px-2.5 py-0.5 rounded-sm dark:bg-green-900 dark:text-green-300">
        <?php echo $client; ?>
    </span>
<?php }
foreach ($data->isins as $isin) { ?>
    <span
        class="bg-green-100 text-green-800 text-xs border font-medium me-2 px-2.5 py-0.5 rounded-sm dark:bg-green-900 dark:text-green-300">
        <?php echo $isin; ?>
    </span>
<?php }
foreach ($data->meny as $mena) { ?>
    <span
        class="bg-green-100 text-green-800 text-xs border font-medium me-2 px-2.5 py-0.5 rounded-sm dark:bg-green-900 dark:text-green-300">
        <?php echo $mena; ?>
    </span>
<?php }
if ($data->hotovost === "1") { ?>
    <span
        class="bg-green-100 text-green-800 text-xs border font-medium me-2 px-2.5 py-0.5 rounded-sm dark:bg-green-900 dark:text-green-300">
        Hotovost: Ano
    </span>
<?php } elseif ($data->hotovost === "2") { ?>
    <span
        class="bg-green-100 text-green-800 text-xs border font-medium me-2 px-2.5 py-0.5 rounded-sm dark:bg-green-900 dark:text-green-300">
        Hotovost: Nie
    </span>
<?php }
?>
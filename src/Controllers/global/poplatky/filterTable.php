<?php
require_once('/home/<USER>/www/src/lib/connection.php');
require_once("/home/<USER>/www/conf/settings.php");

$data = json_decode($_POST["data"]);
$page = $_POST["page"];
$limit = $_POST["limit"];

if (isset($_POST["sortingEnabled"]) && $_POST["sortingEnabled"] == true) {
    $sortingConditions = [];

    if (isset($_POST["klientSort"]) && $_POST["klientSort"] !== "") {
        $sortingConditions[] = "a.cislozmluvy " . $_POST["klientSort"];
    }
    if (isset($_POST["typSort"]) && $_POST["typSort"] !== "") {
        $sortingConditions[] = "a.typ " . $_POST["typSort"];
    }
    if (isset($_POST["dateSort"]) && $_POST["dateSort"] !== "") {
        $sortingConditions[] = "a.datum " . $_POST["dateSort"];
    }
    if (isset($_POST["isinSort"]) && $_POST["isinSort"] !== "") {
        $sortingConditions[] = "a.isin " . $_POST["isinSort"];
    }
    if (isset($_POST["menaSort"]) && $_POST["menaSort"] !== "") {
        $sortingConditions[] = "a.mena " . $_POST["menaSort"];
    }
    if (isset($_POST["hotovostSort"]) && $_POST["hotovostSort"] !== "") {
        $sortingConditions[] = "a.suma " . $_POST["hotovostSort"];
    }

    // If there are sorting conditions, join them with commas
    if (!empty($sortingConditions)) {
        $sortingQuery = " ORDER BY " . implode(", ", $sortingConditions);
    } else {
        $sortingQuery = "";
    }
}

if (isset($data->reconfirmed) && $data->reconfirmed == "1") {
    $reconfirmedQuery = " pr.stav IN (1, 5)";
} elseif (isset($data->reconfirmed) && $data->reconfirmed == "2") {
    $reconfirmedQuery = " pr.stav = 0";
} else {
    $reconfirmedQuery = " pr.stav IN (0, 1, 5)";
}

if (sizeof($data->types) > 0) {
    $typQuery = " AND a.typ IN (";
    foreach ($data->types as $type) {
        $typQuery .= "'$type',";
    }
    $typQuery = substr($typQuery, 0, -1);
    $typQuery .= ")";
}

if (sizeof($data->clients) > 0) {
    $clientQuery = " AND a.cislozmluvy IN (";
    foreach ($data->clients as $client) {
        $clientQuery .= "'$client',";
    }
    $clientQuery = substr($clientQuery, 0, -1);
    $clientQuery .= ")";
}

if (sizeof($data->isins) > 0) {
    $isinQuery = " AND a.isin IN (";
    foreach ($data->isins as $isin) {
        $isinQuery .= "'$isin',";
    }
    $isinQuery = substr($isinQuery, 0, -1);
    $isinQuery .= ")";
}

if (sizeof($data->meny) > 0) {
    $menaQuery = " AND a.mena IN (";
    foreach ($data->meny as $mena) {
        $menaQuery .= "'$mena',";
    }
    $menaQuery = substr($menaQuery, 0, -1);
    $menaQuery .= ")";
}

if ($data->hotovost === 1) {
    $hotovostQuery = " AND a.suma <= mt.cash AND a.suma >= 0 ";
} elseif ($data->hotovost === 2) {
    $hotovostQuery = " AND a.suma > mt.cash and a.suma >= 0 ";
} else {
    $hotovostQuery = "";
}

if (sizeof($data->dates) > 0) {
    $dateQuery = " AND a.datum IN (";
    foreach ($data->dates as $date) {
        $dateQuery .= "'$date',";
    }
    $dateQuery = substr($dateQuery, 0, -1);
    $dateQuery .= ")";
} else {
    $dateQuery = "";
}

$count = Connection::getDataFromDatabase("WITH cash as (select round(sum(((0.5 - md_d) / 0.5) * pocet), 2) as cash, mena, ucetaktiva, subjektid
              from majetoktoday
              where subjektid > 1
                and uctovnykod in (221110, 325111, 325121, 325122, 325130, 325140, 325150, 325160)
                and eqid = 'BU'
              group by mena, ucetaktiva, subjektid),
     main as (SELECT pr.id,
                     pr.suma      AS suma,
                     pr.mena,
                     po.cislozmluvy,
                     pr.datum,
                     CASE
                         WHEN pr.typ = 'TRAN' THEN 'Transakcia s CP'
                         WHEN pr.typ = 'VYROV' THEN 'Vyrovnanie transakcie'
                         WHEN pr.typ = 'SPRAVA' THEN 'Za správu'
                         WHEN pr.typ = 'MANAZ' THEN 'Za riadenie'
                         WHEN pr.typ = 'OSTATNE' THEN 'Ostatné'
                         ELSE 'Neznáma operácia'
                         END      AS typ,
                     pr.typ       AS typ_db,
                     pr.fondid,
                     CASE
                         WHEN f.uhrada_poplatkov = 1 THEN 'inkaso'
                         ELSE 'faktura'
                         END      AS uhrada_poplatkov,
                     pr.cub       AS cub,
                     pr.dansadzba AS dansadzba1,
                     kcp.isin
              FROM portfolio po
                       JOIN poplatok_register pr ON po.fondid = pr.fondid
                       JOIN fonds f ON f.fondid = po.fondid
                       LEFT JOIN konfirmaciacp kcp ON pr.dealid = kcp.dealid
              WHERE $reconfirmedQuery
                AND pr.datum IS NOT NULL AND pr.suma IS NOT NULL)
select COUNT(*) as count
FROM main a
         LEFT JOIN cash mt ON a.cub = mt.ucetaktiva and a.fondid = mt.subjektid and a.mena = mt.mena
WHERE a.suma IS NOT NULL
$typQuery
$clientQuery
$isinQuery
$menaQuery
$hotovostQuery
$dateQuery", defaultDB)[1][0]["count"];

$poplatkyQuery = "WITH cash as (select round(sum(((0.5 - md_d) / 0.5) * pocet), 2) as cash, mena, ucetaktiva, subjektid
              from majetoktoday
              where subjektid > 1
                and uctovnykod in (221110, 325111, 325121, 325122, 325130, 325140, 325150, 325160)
                and eqid = 'BU'
              group by mena, ucetaktiva, subjektid),
main as (SELECT pr.id,
                     pr.suma      AS suma,
                     pr.mena,
                     po.cislozmluvy,
                     pr.datum,
                     pr.stav,
                     CASE
                         WHEN pr.typ = 'TRAN' THEN 'Transakcia s CP'
                         WHEN pr.typ = 'VYROV' THEN 'Vyrovnanie transakcie'
                         WHEN pr.typ = 'SPRAVA' THEN 'Za správu'
                         WHEN pr.typ = 'MANAZ' THEN 'Za riadenie'
                         WHEN pr.typ = 'OSTATNE' THEN 'Ostatné'
                         ELSE 'Neznáma operácia'
                         END      AS typ,
                     pr.typ       AS typ_db,
                     pr.fondid,
                     CASE
                         WHEN f.uhrada_poplatkov = 1 THEN 'inkaso'
                         ELSE 'faktura'
                         END      AS uhrada_poplatkov,
                     pr.cub       AS cub,
                     pr.dansadzba AS dansadzba1,
                     pr.dan AS dan,
                     kcp.isin
              FROM portfolio po
                       JOIN poplatok_register pr ON po.fondid = pr.fondid
                       JOIN fonds f ON f.fondid = po.fondid
                       LEFT JOIN konfirmaciacp kcp ON pr.dealid = kcp.dealid
              WHERE $reconfirmedQuery
                AND pr.datum IS NOT NULL AND pr.suma IS NOT NULL)
select a.id,
       a.suma,
       a.mena,
       a.cislozmluvy,
       a.datum,
       a.typ as typ,
       a.typ_db,
       a.fondid,
       a.uhrada_poplatkov,
       a.cub,
       a.dansadzba1,
       a.dan,
       a.stav,
       a.isin,
       COALESCE(mt.cash, 0)                                                         as cash
FROM main a
         LEFT JOIN cash mt ON a.cub = mt.ucetaktiva and a.fondid = mt.subjektid and a.mena = mt.mena
WHERE 1=1
$typQuery
$clientQuery
$isinQuery
$menaQuery
$hotovostQuery
$dateQuery
$sortingQuery
";

if ($page !== 1) {
    $calc_page = ($page - 1) * $limit;
    if ($count < $calc_page) {
        $calc_page = 0;
    }
    $poplatkyQuery .= " OFFSET $calc_page LIMIT $limit";
} else {
    $poplatkyQuery .= " LIMIT $limit";
}
$poplatky = Connection::getDataFromDatabase($poplatkyQuery, defaultDB)[1];

foreach ($poplatky as $key => $poplatok) { ?>
    <tr
        class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 border-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600">
        <td class="w-4 p-4">
            <div class="flex items-center">
                <input type="hidden" class="totalCountInput" value="<?php echo $count; ?>" />
                <input type="hidden" name="dansadzba1"
                    value="<?php echo $poplatok["dansadzba1"] ? $poplatok["dansadzba1"] : 0; ?>" />
                <input type="hidden" name="dan" value="<?php echo $poplatok["dan"]; ?>" />
                <input type="hidden" name="fondid" value="<?php echo $poplatok["fondid"]; ?>" />
                <input id="checkbox-<?php echo $poplatok["id"]; ?>" type="checkbox" name="id"
                    value="<?php echo $poplatok["id"]; ?>" onchange="handleCheckboxSelect(this)"
                    class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 tableCheckboxSelect rounded-sm focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                <label for="checkbox-<?php echo $poplatok["id"]; ?>" class="sr-only">checkbox</label>
            </div>
        </td>
        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
            <input type="hidden" name="idcheck" value="<?php echo $poplatok["id"]; ?>" />
            <input type="hidden" name="cislozmluvy" value="<?php echo $poplatok["cislozmluvy"]; ?>" />
            <?php echo $poplatok["cislozmluvy"]; ?>
        </th>
        <td class="px-6 py-4">
            <input type="hidden" name="typ" value="<?php echo $poplatok["typ"]; ?>" />
            <input type="hidden" name="uhradapoplatkov" value="<?php echo $poplatok["uhrada_poplatkov"]; ?>" />
            <span class="flex w-full justify-between items-center"><?php echo $poplatok["typ"]; ?></span>
        </td>
        <td class="px-6 py-4 reconfirmedColumnData">
            <input type="hidden" name="reconfirmed" value="<?php echo $poplatok["stav"]; ?>" />
            <span class="flex w-full justify-between items-center"><small
                    class="font-bold p-1 px-2 rounded-md <?php echo $poplatok["stav"] == 0 ? "text-red-500 bg-red-100" : "text-green-500"; ?>">
                    <?php echo $poplatok["stav"] === 0 ? "Nerekonfirmovaný" : "Rekonfirmovaný"; ?></small></span>
        </td>
        <td class="px-6 py-4">
            <input type="hidden" name="datum" value="<?php echo $poplatok["datum"]; ?>" />
            <?php echo $poplatok["datum"]; ?>
        </td>
        <td class="px-6 py-4">
            <input type="hidden" name="isin" value="<?php echo $poplatok["isin"]; ?>" />
            <?php echo $poplatok["isin"]; ?>
        </td>
        <td class="px-6 sumaColumnData py-4">
            <input type="hidden" name="suma" value="<?php echo $poplatok["suma"] === "" ? 0 : $poplatok["suma"]; ?>" />
            <input type="hidden" name="sumaMinus" value="<?php echo $poplatok["suma"] < 0 ? "true" : "false" ?>" />
            <?php if ($poplatok["suma"] < 0) { ?>
                <span
                    class="bg-red-100 text-red-800 text-sm font-bold me-2 px-2.5 py-0.5 rounded-sm dark:bg-red-900 dark:text-red-300"><?php echo $poplatok["suma"]; ?></span>
            <?php } else {
                echo $poplatok["suma"];
            } ?>
        </td>
        <td class="px-6 menaColumnData py-4">
            <input type="hidden" name="mena" value="<?php echo $poplatok["mena"]; ?>" />
            <?php echo $poplatok["mena"]; ?>
        </td>
        <td class="px-6 py-4">
            <input type="hidden" name="cub" value="<?php echo $poplatok["cub"]; ?>" />
            <?php echo $poplatok["cub"]; ?>
        </td>
        <td class="px-6 py-4 font-bold">
            <input type="hidden" name="cash" value="<?php echo $poplatok["cash"]; ?>" />
            <?php echo $poplatok["cash"] . " " . $poplatok["mena"] ?>
        </td>
        <td class="px-6 py-4 relative">
            <div class="absolute flex gap-1 bg-white shadow-lg dark:bg-gray-600 items-center actionConfirm flex-col z-20 p-2 rounded-lg"
                style="left: -7rem; top: 3rem; display: none;">
                <ul class="p-1 space-y-1 text-sm text-gray-700 dark:text-gray-200" aria-labelledby="dropdownHelperButton">
                    <?php if ($poplatok["stav"] > 0) { ?>
                        <li>
                            <button type="submit" id="changeSum" data-modal-target="changeAmountModal"
                                data-modal-toggle="changeAmountModal"
                                class="flex p-2 rounded-sm w-full flex items-center gap-1 transition-all hover:bg-gray-100 cursor-pointer dark:hover:bg-gray-800">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                    class="lucide lucide-circle-dollar-sign">
                                    <circle cx="12" cy="12" r="10" />
                                    <path d="M16 8h-6a2 2 0 1 0 0 4h4a2 2 0 1 1 0 4H8" />
                                    <path d="M12 18V6" />
                                </svg>
                                <span class="font-bold">Zmena sumy</span>
                            </button>
                        </li>
                        <li>
                            <button type="submit" id="changeMena" data-modal-target="changeAmountModal"
                                data-modal-toggle="changeAmountModal"
                                class="flex p-2 rounded-sm w-full flex items-center gap-1 transition-all hover:bg-gray-100 cursor-pointer dark:hover:bg-gray-800">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                    class="lucide lucide-arrow-left-right">
                                    <path d="M8 3 4 7l4 4" />
                                    <path d="M4 7h16" />
                                    <path d="m16 21 4-4-4-4" />
                                    <path d="M20 17H4" />
                                </svg>
                                <span class="font-bold">Zmena meny</span>
                            </button>
                        </li>
                    <?php } ?>
                    <?php if ($poplatok["stav"] === 0) { ?>
                        <li>
                            <button type="submit" id="reconfirm" data-modal-target="changeAmountModal"
                                data-modal-toggle="changeAmountModal"
                                class="flex p-2 rounded-sm flex w-full items-center gap-1 transition-all hover:bg-gray-100 cursor-pointer dark:hover:bg-gray-800">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                    class="lucide lucide-check-check">
                                    <path d="M18 6 7 17l-5-5" />
                                    <path d="m22 10-7.5 7.5L13 16" />
                                </svg>
                                <span class="font-bold">Rekonfirmácia</span>
                            </button>
                        </li>
                    <?php } ?>
                    <?php if ($poplatok["stav"] > 0) { ?>
                        <li>
                            <button type="submit" id="payment" data-modal-target="changeAmountModal"
                                data-modal-toggle="changeAmountModal" onmousedown="start()" onmouseup="end()"
                                class="flex p-2 rounded-sm w-full flex items-center gap-1 transition-all hover:bg-gray-100 cursor-pointer dark:hover:bg-gray-800">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                    class="lucide lucide-receipt-euro">
                                    <path d="M4 2v20l2-1 2 1 2-1 2 1 2-1 2 1 2-1 2 1V2l-2 1-2-1-2 1-2-1-2 1-2-1-2 1Z" />
                                    <path d="M8 12h5" />
                                    <path d="M16 9.5a4 4 0 1 0 0 5.2" />
                                </svg>
                                <span class="font-bold">Splatenie</span>
                            </button>
                        </li>
                    <?php } ?>
                    <li class="border-t">
                        <button type="submit" id="cancelContract" data-modal-target="changeAmountModal"
                            data-modal-toggle="changeAmountModal"
                            class="flex p-2 rounded-sm flex w-full items-center gap-1 transition-all hover:bg-gray-100 cursor-pointer dark:hover:bg-gray-800">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                class="lucide lucide-square-x">
                                <rect width="18" height="18" x="3" y="3" rx="2" ry="2" />
                                <path d="m15 9-6 6" />
                                <path d="m9 9 6 6" />
                            </svg>
                            <span class="font-bold text-red-400">Ukončenie
                                zmluvy</span>
                        </button>
                    </li>
                </ul>
            </div>
            <button type="button" onclick="showActionMenu(this)"
                class="p-1 rounded-lg actionShow hover:bg-blue-300 hover:text-blue-800 transition-all cursor-pointer">
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide lucide-ellipsis-vertical">
                    <circle cx="12" cy="12" r="1" />
                    <circle cx="12" cy="5" r="1" />
                    <circle cx="12" cy="19" r="1" />
                </svg>
            </button>
        </td>
    </tr>
<?php } ?>
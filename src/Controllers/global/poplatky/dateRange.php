<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

$data = json_decode($_POST["data"], true);
if(isset($data) && $data !== ""){
    if (sizeof($data["types"]) > 0) {
        $typQuery = " AND a.typ IN (";
        foreach ($data["types"] as $type) {
            $typQuery .= "'$type',";
        }
        $typQuery = substr($typQuery, 0, -1);
        $typQuery .= ")";
    }
    
    if (sizeof($data["clients"]) > 0) {
        $clientQuery = " AND a.cislozmluvy IN (";
        foreach ($data["clients"] as $client) {
            $clientQuery .= "'$client',";
        }
        $clientQuery = substr($clientQuery, 0, -1);
        $clientQuery .= ")";
    }
    
    if (sizeof($data["isins"]) > 0) {
        $isinQuery = " AND a.isin IN (";
        foreach ($data["isins"] as $isin) {
            $isinQuery .= "'$isin',";
        }
        $isinQuery = substr($isinQuery, 0, -1);
        $isinQuery .= ")";
    }
    
    if (sizeof($data["meny"]) > 0) {
        $menaQuery = " AND a.mena IN (";
        foreach ($data["meny"] as $mena) {
            $menaQuery .= "'$mena',";
        }
        $menaQuery = substr($menaQuery, 0, -1);
        $menaQuery .= ")";
    }
    
    if ($data["hotovost"] === 1) {
        $hotovostQuery = " AND a.suma <= mt.cash AND a.suma >= 0 ";
    } elseif ($data["hotovost"] === 2) {
        $hotovostQuery = " AND a.suma > mt.cash and a.suma >= 0 ";
    } else {
        $hotovostQuery = "";
    }
    
    if (sizeof($data["dates"]) > 0) {
        $dateQuery = " AND a.datum IN (";
        foreach ($data["dates"] as $date) {
            $dateQuery .= "'$date',";
        }
        $dateQuery = substr($dateQuery, 0, -1);
        $dateQuery .= ")";
    } else {
        $dateQuery = "";
    }
} else {
    $typQuery = "";
    $clientQuery = "";
    $isinQuery = "";
    $menaQuery = "";
    $hotovostQuery = "";
    $dateQuery = "";
}

$typyPoplatkovQuery = "WITH cash as (select round(sum(((0.5 - md_d) / 0.5) * pocet), 2) as cash, mena, ucetaktiva, subjektid
from majetoktoday
where subjektid > 1
  and uctovnykod in (221110, 325111, 325121, 325122, 325130, 325140, 325150, 325160)
  and eqid = 'BU'
group by mena, ucetaktiva, subjektid),
main as (SELECT pr.id,
                     pr.suma      AS suma,
                     pr.mena,
                     po.cislozmluvy,
                     pr.datum,
                     CASE
                         WHEN pr.typ = 'TRAN' THEN 'Transakcia s CP'
                         WHEN pr.typ = 'VYROV' THEN 'Vyrovnanie transakcie'
                         WHEN pr.typ = 'SPRAVA' THEN 'Za správu'
                         WHEN pr.typ = 'MANAZ' THEN 'Za riadenie'
                         WHEN pr.typ = 'OSTATNE' THEN 'Ostatné'
                         ELSE 'Neznáma operácia'
                         END      AS typ,
                     pr.typ       AS typ_db,
                     pr.fondid,
                     CASE
                         WHEN f.uhrada_poplatkov = 1 THEN 'inkaso'
                         ELSE 'faktura'
                         END      AS uhrada_poplatkov,
                     pr.cub       AS cub,
                     pr.dansadzba AS dansadzba1,
                     kcp.isin
              FROM portfolio po
                       JOIN poplatok_register pr ON po.fondid = pr.fondid
                       JOIN fonds f ON f.fondid = po.fondid
                       LEFT JOIN konfirmaciacp kcp ON pr.dealid = kcp.dealid
              WHERE pr.stav IN (0, 1, 5)
                AND pr.datum IS NOT NULL AND pr.suma IS NOT NULL)
select DISTINCT a.datum
FROM main a
LEFT JOIN cash mt ON a.cub = mt.ucetaktiva and a.fondid = mt.subjektid and a.mena = mt.mena
WHERE 1=1 
$typQuery
$clientQuery
$isinQuery
$menaQuery
$hotovostQuery
$dateQuery
$sortingQuery
ORDER BY datum DESC
";

$typyPoplatkov = Connection::getDataFromDatabase($typyPoplatkovQuery, defaultDB)[1]; ?>
<ul class="p-3 text-sm text-gray-700 dark:text-gray-200" aria-labelledby="dropdownRadioButton">
    <?php foreach ($typyPoplatkov as $key => $poplatok) { ?>
        <li class="mb-0.5">
            <label
                class="w-full text-sm font-medium text-gray-900 rounded-sm dark:text-gray-300 flex cursor-pointer transition-all items-center gap-2 p-2 rounded-sm hover:bg-gray-100 dark:hover:bg-gray-600">
                <input type="checkbox" onchange="submitWholeForm(this)"
                    value="<?php echo $poplatok["datum"] ? $poplatok["datum"] : "empty"; ?>" name="datumFilter"
                    class="w-4 h-4 filterInput text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                <?php echo $poplatok["datum"] ? $poplatok["datum"] : "bez datumU"; ?>
            </label>
        </li>
    <?php } ?>
</ul>
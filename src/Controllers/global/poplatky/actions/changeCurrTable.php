<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

$data = json_decode($_POST["data"], true);
$meny = Connection::getDataFromDatabase("SELECT DISTINCT mena FROM spravcabu", defaultDB)[1];
if (sizeof($data) === 0) { ?>
    <div class="flex items-center p-4  rounded-lg bg-gray-50 dark:bg-gray-800 h-full justify-center flex-col gap-4"
        role="alert">
        <svg class="shrink-0 w-12 h-12 dark:text-gray-300" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
            fill="currentColor" viewBox="0 0 20 20">
            <path
                d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z" />
        </svg>
        <span class="sr-only">Info</span>
        <div class="ms-3 text-sm font-medium text-gray-800 dark:text-gray-300">
            Neboli vybrané žiadne záznamy. Prosím <span data-modal-hide="changeAmountModal"
                class="font-semibold underline cursor-pointer hover:no-underline">zatvrote</span> toto okno a zopakujte to
            znova.
        </div>
    </div>
<?php } else { ?>
    <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
        <thead class="text-xs text-gray-700 bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
            <tr>
                <th scope="col" class="px-1 py-3">
                    <span class="text-xs uppercase mr-2">klient</span>
                </th>
                <th scope="col" class="px-1 py-3">
                    <span class="text-xs uppercase mr-2">typ poplatku</span>
                </th>
                <th scope="col" class="px-1 py-3">
                    <span class="text-xs uppercase mr-2">Mena</span>
                </th>
                <th scope="col" class="px-1 py-3">
                    <span class="text-xs uppercase mr-2">Suma</span>
                </th>
                <th scope="col" class="px-1 py-3">
                    <span class="text-xs uppercase mr-2">Nová mena</span>
                </th>
            </tr>
        </thead>
        <tbody id="poplatkyTBODY">
            <?php
            foreach ($data as $key => $poplatok) {
                $bezdph = 100 * ($poplatok["suma"] / (100 + (float) $poplatok["dansadzba1"]));
                ?>
                <tr
                    class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 border-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600">
                    <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                        <input type="hidden" name="cislozmluvy" value="<?php echo $poplatok["cislozmluvy"]; ?>" />
                        <input type="hidden" name="fondid" value="<?php echo $poplatok["fondid"]; ?>" />
                        <input type="hidden" name="id" value="<?php echo $poplatok["id"]; ?>" />
                        <?php echo $poplatok["cislozmluvy"]; ?>
                    </th>
                    <td class="px-6 py-4">
                        <input type="hidden" name="typ" value="<?php echo $poplatok["typ"]; ?>" />
                        <?php echo $poplatok["typ"]; ?>
                    </td>
                    <td class="px-6 menaColumnData py-4">
                        <input type="hidden" name="mena" value="<?php echo $poplatok["mena"]; ?>" />
                        <?php echo $poplatok["mena"]; ?>
                    </td>
                    <td class="px-6 sumaColumnData py-4">
                        <input type="hidden" name="suma" value="<?php echo $poplatok["suma"]; ?>" />
                        <?php echo $poplatok["suma"]; ?>
                    </td>
                    <td class="px-6 py-4">
                        <input type="hidden" name="typ" value="<?php echo $poplatok["typ"]; ?>" />
                        <select id="novamena" name="novamena"
                            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                            <?php foreach ($meny as $key => $mena) {
                                if ($mena["mena"] === $poplatok["mena"]) {
                                    continue;
                                }
                                ?>
                                <option value="<?php echo $mena["mena"]; ?>"><?php echo $mena["mena"]; ?></option>
                            <?php } ?>
                        </select>
                    </td>
                </tr>
            <?php } ?>
        </tbody>
    </table>
<?php } ?>
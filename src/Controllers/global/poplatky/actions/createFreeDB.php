<?php
require_once('/home/<USER>/www/src/lib/connection.php');
require_once("/home/<USER>/www/conf/settings.php");
require_once "/home/<USER>/www/src/lib/notifications/Notification.class.php";
require_once "/home/<USER>/www/src/lib/notifications/Subactivity.class.php";

$data = json_decode($_POST["data"], true);
$poplatok_ids = [];


$sess_userid = $_SESSION["user"]["data"]["userid"];
$username = $_SESSION["user"]["data"]["username"];

$poplatokID = $_POST["id"];
$portfolio = $_POST["fondid"];
$suma = $_POST["amountWithoutVat"];
$danSadzba = $_POST["dansadzba1"];
$mena = $_POST["mena"];
$typPoplatku = $_POST["typPoplatku"];

$dan = round(($suma * ($danSadzba / 100)), 2);

$newID = Connection::getDataFromDatabase("SELECT nextval('s_poplatok_register')", defaultDB)[1][0]["nextval"];

$datumek = Connection::getDataFromDatabase("SELECT datum FROM today WHERE fondid = $portfolio", defaultDB)[1][0]["datum"];

$insertIntoPoplatokRegister = Connection::InsertUpdateCreateDelete("INSERT INTO poplatok_register (id, fondid, datum, suma, mena, typ, stav, dealid, tranza, dansadzba, dan, dovod)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", [
    $newID,
    $portfolio,
    $datumek,
    $suma + $dan,
    $mena,
    $typPoplatku,
    0,
    0,
    0,
    $danSadzba,
    $dan,
    $_POST["reason"]
], defaultDB);

if (gettype($insertIntoPoplatokRegister) !== "integer") {
    $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [poplatok_register]. " . $insertIntoPoplatokRegister;
}

if (empty($errors)) {
    $notification = new Notification(7, 'poplatok_register', $newID, "tvorbaPoplatku", $sess_userid, $username, "tvorbaPoplatku", json_encode(["id", $newID]), false, $portfolio);
    $notifID = $notification->createNotifcation();

    ?>
    <div id="toast-success"
        class="flex border-green-300 border items-center w-full max-w-sm p-4 mb-4 text-green-500 bg-white absolute right-5 rounded-lg shadow dark:text-gray-400 dark:bg-gray-800"
        role="alert">
        <div
            class="inline-flex items-center justify-center flex-shrink-0 w-8 h-8 text-green-500 bg-green-100 rounded-lg dark:bg-green-800 dark:text-green-200">
            <svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                viewBox="0 0 20 20">
                <path
                    d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 8.207-4 4a1 1 0 0 1-1.414 0l-2-2a1 1 0 0 1 1.414-1.414L9 10.586l3.293-3.293a1 1 0 0 1 1.414 1.414Z" />
            </svg>
            <span class="sr-only">Check icon</span>
        </div>
        <div class="ms-3 text-sm font-semibold">Poplatok bol úspešne vytvorený</div>
        <button type="button"
            class="ms-auto -mx-1.5 -my-1.5 bg-white text-gray-400 hover:text-gray-900 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex items-center justify-center h-8 w-8 dark:text-gray-500 dark:hover:text-white dark:bg-gray-800 dark:hover:bg-gray-700"
            data-dismiss-target="#toast-success" aria-label="Close">
            <span class="sr-only">Close</span>
            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
            </svg>
        </button>
    </div>
    <script>
        setTimeout(() => {
            window.location.reload();
        }, 1500);
    </script>
<?php } else {
    ?>
    <div id="toast-danger"
        class="flex items-center w-full max-w-sm p-4 mb-4 text-white absolute right-5 bg-red-500 rounded-lg shadow-lg"
        role="alert">
        <?php ?>
        <div
            class="inline-flex items-center justify-center flex-shrink-0 w-8 h-8 text-red-500 bg-red-100 rounded-lg dark:bg-red-800 dark:text-red-200">
            <svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                viewBox="0 0 20 20">
                <path
                    d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 11.793a1 1 0 1 1-1.414 1.414L10 11.414l-2.293 2.293a1 1 0 0 1-1.414-1.414L8.586 10 6.293 7.707a1 1 0 0 1 1.414-1.414L10 8.586l2.293-2.293a1 1 0 0 1 1.414 1.414L11.414 10l2.293 2.293Z" />
            </svg>
            <span class="sr-only">Error icon</span>
        </div>
        <section class="flex flex-col">
            <div class="ms-3 text-sm font-bold">Investičný zámer sa nepodarilo vytvoriť kvôli neznámej chybe!</div>
            <div>
                <?php foreach ($errors as $error) {
                    echo $error . "<br>";
                } ?>
            </div>
        </section>
        <button type="button"
            class="ms-auto -mx-1.5 -my-1.5 text-white hover:text-gray-900 transition-all rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex items-center justify-center h-8 w-8 dark:text-gray-500 dark:hover:text-white dark:bg-gray-800 dark:hover:bg-gray-700"
            data-dismiss-target="#toast-danger" aria-label="Close">
            <span class="sr-only">Close</span>
            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
            </svg>
        </button>
    </div>
<?php } ?>
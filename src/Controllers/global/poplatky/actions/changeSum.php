<?php
require_once('/home/<USER>/www/src/lib/connection.php');
require_once("/home/<USER>/www/conf/settings.php");
require_once "/home/<USER>/www/src/lib/notifications/Notification.class.php";
require_once "/home/<USER>/www/src/lib/notifications/Subactivity.class.php";

$data = json_decode($_POST["data"], true);
$poplatok_ids = [];

$sess_userid = $_SESSION["user"]["data"]["userid"];
$username = $_SESSION["user"]["data"]["username"];

foreach ($data as $key => $poplatok) {
    if ($poplatok["sumaPrevious"] == $poplatok["suma"]) {
        continue;
    }
    $poplatokID = $poplatok["id"];
    $newID = Connection::getDataFromDatabase("SELECT nextval('s_poplatok_register')", defaultDB)[1][0]["nextval"];

    $insertIntoPoplatokRegister = Connection::InsertUpdateCreateDelete("INSERT INTO poplatok_register (id, fondid, datum, suma, mena, typ, stav, dealid, tranza)
    SELECT $newID, fondid, datum, suma, mena, typ, 7, dealid, tranza
    FROM poplatok_register
    WHERE id = $poplatokID", [], defaultDB);
    if (gettype($insertIntoPoplatokRegister) !== "integer") {
        echo "INSERT INTO poplatok_register (id, fondid, datum, suma, mena, typ, stav, dealid, tranza)
    SELECT $newID, fondid, datum, suma, mena, typ, 7, dealid, tranza
    FROM poplatok_register
    WHERE id = $poplatokID";
        $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [poplatok_register]. " . $insertIntoPoplatokRegister;
        break;
    }

    $updatePoplatokRegister = Connection::InsertUpdateCreateDelete("UPDATE poplatok_register SET suma = ? WHERE id = ?", [$poplatok["suma"], $poplatokID], defaultDB);
    if (gettype($updatePoplatokRegister) !== "integer") {
        $errors[] = "Nepodarilo sa aktualizovať údaje v tabuľke [poplatok_register2]. " . $updatePoplatokRegister;
        break;
    }

    if ($poplatok["mena"] !== "EUR") {
        if ($poplatok["dansadzba1"] > 0) {
            $decimal = 2;
        } else {
            $decimal = 1;
        }
    } else {
        $decimal = 2;
    }

    $updatePoplatokRegister2 = Connection::InsertUpdateCreateDelete("UPDATE poplatok_register SET suma = round(suma, $decimal), dan = round((suma / ((100 + dansadzba) / 100))*COALESCE(dansadzba,0)/100 ,($decimal)) WHERE id = ?", [$poplatokID], defaultDB);
    if (gettype($updatePoplatokRegister2) !== "integer") {
        $errors[] = "Nepodarilo sa aktualizovať údaje v tabuľke [poplatok_register]. " . $updatePoplatokRegister2;
        break;
    }
    $poplatok_ids[] = [
        "id" => $newID,
        "fondid" => $poplatok["fondid"],
        "sumaPrevious" => $poplatok["sumaPrevious"],
        "suma" => $poplatok["suma"],
        "mena" => $poplatok["mena"]
    ];
}
if (empty($errors)) {
    if (sizeof($poplatok_ids) > 1) {
        $notification = new Notification(7, 'poplatok_register', 0, "zmenaSumyPoplatokGlobal", $sess_userid, $username, "zmenaSumyPoplatokGlobal", json_encode(["id", "0"]), false, 0);
        $notifID = $notification->createNotifcation();

        foreach ($poplatok_ids as $key => $item) {
            $subactivity1 = new Subactivity($notifID, 7, 'poplatok_register', $item["id"], 'zmenaSumyPoplatok', $sess_userid, json_encode(["id", $item]), $item["fondid"], "Suma poplatku <strong>" . $item["id"] . "</strong> bola zmenená. Zo sumy <strong>" . number_format($item["sumaPrevious"], 2, ".", " ") . " " . $item["mena"] . "</strong> na <strong>" . number_format($item["suma"], 2, ".", " ") . " " . $item["mena"] . '</strong>');
            $subactivity1->createSubActivity();
        }
    } elseif (sizeof($poplatok_ids) === 1) {
        $notification = new Notification(7, 'poplatok_register', $poplatok_ids[0]["id"], "zmenaSumyPoplatok", $sess_userid, $username, "zmenaSumyPoplatok", json_encode(["id", $poplatok_ids[0]["id"]]), false, 0);
        $notifID = $notification->createNotifcation();
    } else {
        echo "Nebolo zmenené ani jedno políčko";
    }
    ?>
    <div id="toast-success"
        class="flex border-green-300 border items-center w-full max-w-sm p-4 mb-4 text-green-500 bg-white absolute right-5 rounded-lg shadow dark:text-gray-400 dark:bg-gray-800"
        role="alert">
        <div
            class="inline-flex items-center justify-center flex-shrink-0 w-8 h-8 text-green-500 bg-green-100 rounded-lg dark:bg-green-800 dark:text-green-200">
            <svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                viewBox="0 0 20 20">
                <path
                    d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 8.207-4 4a1 1 0 0 1-1.414 0l-2-2a1 1 0 0 1 1.414-1.414L9 10.586l3.293-3.293a1 1 0 0 1 1.414 1.414Z" />
            </svg>
            <span class="sr-only">Check icon</span>
        </div>
        <div class="ms-3 text-sm font-semibold">Suma poplatkov bola úspešne zmenená.</div>
        <button type="button"
            class="ms-auto -mx-1.5 -my-1.5 bg-white text-gray-400 hover:text-gray-900 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex items-center justify-center h-8 w-8 dark:text-gray-500 dark:hover:text-white dark:bg-gray-800 dark:hover:bg-gray-700"
            data-dismiss-target="#toast-success" aria-label="Close">
            <span class="sr-only">Close</span>
            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
            </svg>
        </button>
    </div>
    <script>
        setTimeout(() => {
            window.location.reload();
        }, 1500);
    </script>
<?php } else {
    ?>
    <div id="toast-danger"
        class="flex items-center w-full max-w-sm p-4 mb-4 text-white absolute right-5 bg-red-500 rounded-lg shadow-lg"
        role="alert">
        <?php ?>
        <div
            class="inline-flex items-center justify-center flex-shrink-0 w-8 h-8 text-red-500 bg-red-100 rounded-lg dark:bg-red-800 dark:text-red-200">
            <svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                viewBox="0 0 20 20">
                <path
                    d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 11.793a1 1 0 1 1-1.414 1.414L10 11.414l-2.293 2.293a1 1 0 0 1-1.414-1.414L8.586 10 6.293 7.707a1 1 0 0 1 1.414-1.414L10 8.586l2.293-2.293a1 1 0 0 1 1.414 1.414L11.414 10l2.293 2.293Z" />
            </svg>
            <span class="sr-only">Error icon</span>
        </div>
        <section class="flex flex-col">
            <div class="ms-3 text-sm font-bold">Investičný zámer sa nepodarilo vytvoriť kvôli neznámej chybe!</div>
            <div>
                <?php foreach ($errors as $error) {
                    echo $error . "<br>";
                } ?>
            </div>
        </section>
        <button type="button"
            class="ms-auto -mx-1.5 -my-1.5 text-white hover:text-gray-900 transition-all rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex items-center justify-center h-8 w-8 dark:text-gray-500 dark:hover:text-white dark:bg-gray-800 dark:hover:bg-gray-700"
            data-dismiss-target="#toast-danger" aria-label="Close">
            <span class="sr-only">Close</span>
            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
            </svg>
        </button>
    </div>
<?php } ?>
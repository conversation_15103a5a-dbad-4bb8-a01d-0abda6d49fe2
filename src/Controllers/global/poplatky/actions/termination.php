<?php
require_once('/home/<USER>/www/src/lib/connection.php');
require_once("/home/<USER>/www/conf/settings.php");
require_once "/home/<USER>/www/src/lib/notifications/Notification.class.php";
require_once "/home/<USER>/www/src/lib/notifications/Subactivity.class.php";

$data = json_decode($_POST["data"], true);
$loading = true;

$fondid = $data[0]["fondid"];

$days_in_year = 365;
$days_in_year = Connection::getDataFromDatabase("SELECT (DATE (EXTRACT(YEAR FROM datum) || '-12-31'))::DATE -
       (DATE ((EXTRACT(YEAR FROM datum) - 1) || '-12-31'))::DATE AS days_in_year
FROM (SELECT MAX(datum) AS datum FROM majetoktotal) dat
WHERE dat.datum IS NOT NULL;
", defaultDB)[1][0]["days_in_year"];
if (empty($days_in_year)) {
    $errors[] = "Nepodarilo sa získať počet dní v roku.";
}

$setRoundingDan = "2";
$setRoundingPoplatok = "2";

$sess_userid = $_SESSION["user"]["data"]["userid"];
$username = $_SESSION["user"]["data"]["username"];

$detailik = Connection::getDataFromDatabase("SELECT
		f.fondid, f.refmena, 
		COALESCE(man.dan,0) as managementdan, COALESCE(cus.dan,0) as custodiandan, 
		man.ratio as managementfeeratio, cus.ratio as custodianfeeratio, 
		man.freq as MANAGENENTCHARGEFREQ, cus.freq as custodianchargefreq, 
		f.typpredlohy, fb.cub as ucet	
	FROM
		fonds f, fondsfeesdetail man, fondsfeesdetail cus, fondsbu fb
	WHERE
		f.fondid = man.fondid and man.chargetype='managementfee' and
		f.fondid = cus.fondid and cus.chargetype='custodianfee' and
		f.refmena = fb.mena and f.fondid = fb.fondid and 
		f.fondid = $fondid
		and fb.cub like '%/7500'", defaultDB)[1];
if (empty($detailik)) {
    $errors[] = "Nepodarilo sa získať detaily o poplatkoch.";
}

foreach ($detailik as $key => $item) {
    $fondid = $item["fondid"];
    $ucet = $item["ucet"];
    $refmena = $item["refmena"];
    $poplatok = [];
    $perioda = [];
    $danRatio = [];

    if ($item["typpredlohy"] == 1) {
        /* Ak je to SII (Sympatia Individual Invest), tak si pripravime info pre manazersky poplatok */
        $poplatok[150] = ($item["managementfeeratio"]);
        $perioda[150] = $item["MANAGENENTCHARGEFREQ"];
        $danRatio[150] = $item["managementdan"];
    } else {
        /* Ak je to PK (Privatny klient), tak si pripravime info pre poplatky za riadenie a za spravu portfolia */
        $poplatok[150] = ($item["managementfeeratio"]);
        $poplatok[151] = ($item["custodianfeeratio"]);
        $perioda[150] = $item["MANAGENENTCHARGEFREQ"];
        $perioda[151] = $item["custodianchargefreq"];
        $danRatio[150] = $item["managementdan"];
        $danRatio[151] = $item["custodiandan"];
    }

    foreach ($poplatok as $kodobratu => $ratio) {
        $freq = $perioda[$kodobratu];
        $danRat = $danRatio[$kodobratu];
        $divisor = 1;

        /* Zistime si, ci sa ma urobit zapis a tiez vybere prislusne informacie pre vypocty
         * Uctovne zaznamy zapisujeme do DB iba ak cislo predchadzajuceho dna v obdobi je
         * vacsie ako cislo aktualneho dna v obdobi, co znamena, ze sme ukoncili predchadzajuce
         * obdobie a zacali nove. 
         */

        $typ = ($kodobratu == 150) ? "MANAZ" : "SPRAVA";


        /* Ak je potrebne zapisat informacie do databazy, tak ich zapiseme.
         * Poplatok sa rata ako priemer NAV daneho portfolia na konci jednotlivych
         * mesiacov obdobia vynasobeny percentom poplatku a usposobnene na dany pocet 
         * dni v roku (pa) .
         */

        $poplatok_base = "round((COALESCE(avg(COALESCE(p.sumaden,0)),0)*(count(p.datum))/$days_in_year*$ratio/100),$setRoundingPoplatok)";
        $poplatokNextVal = Connection::getDataFromDatabase("SELECT nextval('s_poplatok_register')", defaultDB)[1][0]["nextval"];

        $urcityDetail = Connection::getDataFromDatabase("SELECT
						$poplatokNextVal as id, fondid, DatumDo, poplatok+dan as poplatokdan, mena, '$typ', 0, 0, 0, DatumOd, $danRat, dan
					FROM
		      	(
						SELECT
							$fondid as fondid, '$refmena' as mena, 
							$poplatok_base as poplatok,
							round($poplatok_base*$danRat/100,$setRoundingDan) as dan,
							min(p.datum) as DatumOd,
							max(p.datum) as DatumDo
						FROM (
					select
					datum,
					SUM(
                           COALESCE(
                                   (CASE
                                        WHEN md_d = 0 THEN md_d0
                                        ELSE md_d1
                                       END)
                                       * sumadenom * f_menovy_kurz(m.menadenom, '$refmena', m.datum, 1),
                                   0
                           )
                   ) AS sumaden
					from
					majetoktotal m, navuctovanie nu
					WHERE
						m.uctovnykod = nu.uctovnykod and
				 		m.datum > COALESCE((select max(datum) from poplatok_register where fondid=$fondid and typ='$typ'),(select min(datum) from majetoktotal where subjektid=$fondid)-1) and
						m.subjektid = $fondid 
					group by 
						datum
					) p) as p2", defaultDB)[1][0];

        $poplatok_ids[] = [
            "id" => $urcityDetail["id"],
            "fondid" => $item["fondid"],
            "cub" => $item["ucet"],
            "suma" => $urcityDetail["poplatokdan"]
        ];


        $poplatokRegisterQuery = "INSERT INTO poplatok_register(id, fondid, datum, suma, mena, typ, stav, dealid, tranza, DatumOd, DanSadzba, Dan)
            SELECT
						$poplatokNextVal, fondid, DatumDo, poplatok+dan as poplatokdan, mena, '$typ', 0, 0, 0, DatumOd, $danRat, dan
					FROM
		      	(
						SELECT
							$fondid as fondid, '$refmena' as mena, 
							$poplatok_base as poplatok,
							round($poplatok_base*$danRat/100,$setRoundingDan) as dan,
							min(p.datum) as DatumOd,
							max(p.datum) as DatumDo
						FROM (
					select
					datum,
					SUM(
                           COALESCE(
                                   (CASE
                                        WHEN md_d = 0 THEN md_d0
                                        ELSE md_d1
                                       END)
                                       * sumadenom * f_menovy_kurz(m.menadenom, '$refmena', m.datum, 1),
                                   0
                           )
                   ) AS sumaden
					from
					majetoktotal m, navuctovanie nu
					WHERE
						m.uctovnykod = nu.uctovnykod and
				 		--m.datum > COALESCE((select max(datum) from poplatok_register where fondid=$fondid and typ='$typ'),(select min(datum) from majetoktotal where subjektid=$fondid)-1) and
						m.subjektid = $fondid 
					group by 
						datum
					) p) as p2";
        $insertIntoPoplatokRegister = Connection::InsertUpdateCreateDelete($poplatokRegisterQuery, [], defaultDB);
        if (gettype($insertIntoPoplatokRegister) !== "integer") {
            $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [poplatok_register]. " . $insertIntoPoplatokRegister;
            break;
        } else {
            $loading = false;
        }
    }
}
if (empty($errors) && !$loading) {
    if (sizeof($poplatok_ids) > 1) {
        $notification = new Notification(1, 'poplatok_register', 0, "terminationPoplatokGlobal", $sess_userid, $username, "terminationPoplatokGlobal", json_encode(["id", "0"]), false, 0);
        $notifID = $notification->createNotifcation();

        foreach ($poplatok_ids as $key => $item) {
            $subactivity1 = new Subactivity($notifID, 7, 'poplatok_register', $item["id"], 'terminationPoplatok', $sess_userid, json_encode(["id", $item]), $item["fondid"], "Poplatok bol vygenerovaný so sumou <strong>" . number_format($item["suma"], 2, ".", " ") . " " . $item["mena"] . "</strong>  a bol mu priradený účet <strong>" . $item["cub"] . '</strong>');
            $subactivity1->createSubActivity();
        }
    } elseif (sizeof($poplatok_ids) === 1) {
        $notification = new Notification(7, 'poplatok_register', $poplatok_ids[0]["id"], "terminationPoplatok", $sess_userid, $username, "terminationPoplatok", json_encode(["id", $poplatok_ids[0]["newid"]]), false, $_POST["fondid"]);
        $notifID = $notification->createNotifcation();
    } else {
        echo "Nebolo zmenené ani jedno políčko";
    }
    ?>
    <div class="flex items-center p-4  rounded-lg bg-gray-50 dark:text-gray-100 dark:bg-gray-800 h-full justify-center flex-col gap-4"
        role="alert">
        <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor"
            stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
            class="lucide text-green-500 lucide-circle-check-big">
            <path d="M21.801 10A10 10 0 1 1 17 3.335" />
            <path d="m9 11 3 3L22 4" />
        </svg>
        <span class="sr-only">Info</span>
        <div class="ms-3 text-sm font-medium text-gray-800 dark:text-gray-300">
            Vygenerovanie poplatku za ukončenie zmluvy prebehlo úspešne.
        </div>
    </div>
    <script>
        setTimeout(() => {
            window.location.reload();
        }, 1500);
    </script>
<?php } elseif (!empty($errors) && !$loading) { ?>
    <div class="flex items-center p-4  rounded-lg bg-gray-50 dark:text-gray-100 dark:bg-gray-800 h-full justify-center flex-col gap-4"
        role="alert">
        <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor"
            stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide text-red-600 lucide-circle-x">
            <circle cx="12" cy="12" r="10" />
            <path d="m15 9-6 6" />
            <path d="m9 9 6 6" />
        </svg>
        <span class="sr-only">Info</span>
        <div class="ms-3 text-sm font-medium text-gray-800 dark:text-gray-300">
            Pri generovaní poplatku sa vyskytla chyba
        </div>
    </div>
<?php } 
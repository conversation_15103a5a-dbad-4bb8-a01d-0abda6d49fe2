<?php
require_once('/home/<USER>/www/src/lib/connection.php');
require_once("/home/<USER>/www/conf/settings.php");
require_once "/home/<USER>/www/src/lib/notifications/Notification.class.php";
require_once "/home/<USER>/www/src/lib/notifications/Subactivity.class.php";

$data = json_decode($_POST["data"], true);

$poplatok_ids = [];

$sess_userid = $_SESSION["user"]["data"]["userid"];
$username = $_SESSION["user"]["data"]["username"];

if (sizeof($data) > 0) {
    foreach ($data as $key => $poplatok) {
        $suma = $poplatok["suma"];
        $fondid = $poplatok["fondid"];
        $mena = $poplatok["mena"];
        $ucet = $poplatok["cub"];
        $typ_poplatku = $poplatok["typ"];

        switch ($typ_poplatku) {
            case 'Transakcia s CP':
                $subkodobratu = 1;
                break;
            case 'Vyrovnanie transakcie':
                $subkodobratu = 2;
                break;
            case 'Za správu':
                $subkodobratu = 4;
                break;
            case 'Za riadenie':
                $subkodobratu = 3;
                break;
            case 'Ostatné':
                $subkodobratu = 0;
                break;
        }

        if ($suma === 0) {
            $updatePoplatokRegister = Connection::InsertUpdateCreateDelete("UPDATE poplatok_register SET stav = 8 WHERE id = ?", [$poplatok["id"]], defaultDB);
            if (gettype($updatePoplatokRegister) !== "integer") {
                $errors[] = "Nepodarilo sa aktualizovať údaje v tabuľke [poplatok_register]. " . $updatePoplatokRegister;
                break;
            }
        } else {
            $mtid = Connection::getDataFromDatabase("SELECT nextval('s_majetoktoday_obratid')", defaultDB)[1][0]["nextval"];

            $insertIntoMajetokToday = Connection::InsertUpdateCreateDelete("INSERT INTO majetoktoday (obratid, destinacia, subjektid, kodobratu, uctovnykod, eqid, kodaktiva, ucetaktiva, jednotka, pocet, mena, md_d, obratdatetimereal, obratdatatimezauctovane)
            select $mtid,'poplatok_register',$fondid,kodobratu,
                        uctovnykod,equid,'$mena', '$ucet','$mena',
                        abs($suma),'$mena',md_d,(select datum from today where fondid=$fondid),(select datum from today where fondid=$fondid)
                        from kodobratumd_d where kodobratu=297 and subkodobratu = $subkodobratu", [], defaultDB);

            if (gettype($insertIntoMajetokToday) !== "integer") {
                $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [majetoktoday]. " . $insertIntoMajetokToday;
                break;
            }

            $dealidSeq = Connection::getDataFromDatabase("SELECT id from ididentify where popis like 'poplatok_register'", defaultDB)[1][0]["id"];
            $deID = Connection::getDataFromDatabase("SELECT nextval('s_poplatok_register_mc_dealid')", defaultDB)[1][0]["nextval"];

            $dealid = $deID . $dealidSeq;

            $insertIntoMajetokCesta = Connection::InsertUpdateCreateDelete("INSERT INTO majetokcesta (dealid, tranza, in_out, sparovanie, destinacia, subjektid, eqid, kodaktiva, ucetaktiva, jednotka, mena, pocet, cestafrom, cestatill, popis)
            VALUES ($dealid, 0, 1, (select cislozmluvy from portfolio where fondid=$fondid) ,'poplatok_register',$fondid ,
                        'BU','$mena', '$ucet','$mena','$mena',abs($suma),(select datum from today where fondid=$fondid),(select datum from today where fondid=$fondid),
                        'poplatok dobropis'
                    )", [], defaultDB);
            if (gettype($insertIntoMajetokCesta) !== "integer") {
                $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [majetokcesta]. " . $insertIntoMajetokCesta;
                break;
            }

            $updatePoplatokRegister = Connection::InsertUpdateCreateDelete("UPDATE poplatok_register SET stav = CASE 
                WHEN 1 = 2 THEN 5  ELSE NULL  END WHERE id = ?", [$poplatok["id"]], defaultDB);
            if (gettype($updatePoplatokRegister) !== "integer") {
                $errors[] = "Nepodarilo sa aktualizovať údaje v tabuľke [poplatok_register]. " . $updatePoplatokRegister;
                break;
            }

            $insertIntoPoplatokRegisterLinks = Connection::InsertUpdateCreateDelete("INSERT INTO poplatok_register_links (poplatok_register_id, mc_dealid)
            VALUES (?, ?)", [$poplatok["id"], $dealid], defaultDB);
            if (gettype($insertIntoPoplatokRegisterLinks) !== "integer") {
                $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [poplatok_register_links]. " . $insertIntoPoplatokRegisterLinks;
                break;
            }
        }

        $poplatok_ids[] = [
            "id" => $poplatok["id"],
            "fondid" => $poplatok["fondid"],
            "cub" => $poplatok["cub"],
            "suma" => $poplatok["suma"]
        ];
    }

    if (empty($errors)) {
        if (sizeof($poplatok_ids) > 1) {
            $notification = new Notification(1, 'poplatok_register', 0, "splateniePoplatokGlobal", $sess_userid, $username, "splateniePoplatokGlobal", json_encode(["id", "0"]), false, 0);
            $notifID = $notification->createNotifcation();

            foreach ($poplatok_ids as $key => $item) {
                $subactivity1 = new Subactivity($notifID, 7, 'poplatok_register', $item["id"], 'splateniePoplatok', $sess_userid, json_encode(["id", $item]), $item["fondid"], "Poplatok bol splatený so sumou <strong>" . number_format($item["suma"], 2, ".", " ") . " " . $item["mena"]);
                $subactivity1->createSubActivity();
            }
        } elseif (sizeof($poplatok_ids) === 1) {
            $notification = new Notification(7, 'poplatok_register', $poplatok_ids[0]["id"], "splateniePoplatok", $sess_userid, $username, "splateniePoplatok", json_encode(["id", $poplatok_ids[0]["id"]]), false, $_POST["fondid"]);
            $notifID = $notification->createNotifcation();
        } else {
            echo "Nebolo zmenené ani jedno políčko";
        }
        ?>
        <div class="flex items-center p-4  rounded-lg bg-gray-50 dark:text-gray-100 dark:bg-gray-800 h-full justify-center flex-col gap-4"
            role="alert">
            <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="lucide text-green-500 lucide-circle-check-big">
                <path d="M21.801 10A10 10 0 1 1 17 3.335" />
                <path d="m9 11 3 3L22 4" />
            </svg>
            <span class="sr-only">Info</span>
            <div class="ms-3 text-sm font-medium text-gray-800 dark:text-gray-300">
                Splatenie poplatkov prebehlo úspešne.
            </div>
        </div>
        <script>
            setTimeout(() => {
                window.location.reload();
            }, 1500);
        </script>
    <?php } else {
        print_r($errors);
        ?>
        <div class="flex items-center p-4  rounded-lg bg-gray-50 dark:text-gray-100 dark:bg-gray-800 h-full justify-center flex-col gap-4"
            role="alert">
            <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide text-red-600 lucide-circle-x">
                <circle cx="12" cy="12" r="10" />
                <path d="m15 9-6 6" />
                <path d="m9 9 6 6" />
            </svg>
            <span class="sr-only">Info</span>
            <div class="ms-3 text-sm font-medium text-gray-800 dark:text-gray-300">
                Pri splatení poplatkov sa vyskytla chyba
            </div>
        </div>
    <?php }
} else { ?>
    <div class="flex items-center p-4  rounded-lg bg-gray-50 dark:text-gray-100 dark:bg-gray-800 h-full justify-center flex-col gap-4"
        role="alert">
        <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor"
            stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-frown">
            <circle cx="12" cy="12" r="10" />
            <path d="M16 16s-1.5-2-4-2-4 2-4 2" />
            <line x1="9" x2="9.01" y1="9" y2="9" />
            <line x1="15" x2="15.01" y1="9" y2="9" />
        </svg>
        <span class="sr-only">Info</span>
        <div class="ms-3 text-sm font-medium text-gray-800 dark:text-gray-300">
            Neboli vybrané žiadne poplatky
        </div>
    </div>
<?php } ?>
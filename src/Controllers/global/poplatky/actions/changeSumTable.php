<?php
$data = json_decode($_POST["data"], true);
if (sizeof($data) === 0) { ?>
    <div class="flex items-center p-4  rounded-lg bg-gray-50 dark:bg-gray-800 h-full justify-center flex-col gap-4"
        role="alert">
        <svg class="shrink-0 w-12 h-12 dark:text-gray-300" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
            fill="currentColor" viewBox="0 0 20 20">
            <path
                d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z" />
        </svg>
        <span class="sr-only">Info</span>
        <div class="ms-3 text-sm font-medium text-gray-800 dark:text-gray-300">
            Neboli vybrané žiadne záznamy. Prosím <span data-modal-hide="changeAmountModal"
                class="font-semibold underline cursor-pointer hover:no-underline">zatvrote</span> toto okno a zopakujte to
            znova.
        </div>
    </div>
<?php } else { ?>
    <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
        <thead class="text-xs text-gray-700 bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
            <tr>
                <th scope="col" class="px-1 py-3">
                    <span class="text-xs uppercase mr-2">klient</span>
                </th>
                <th scope="col" class="px-1 py-3">
                    <span class="text-xs uppercase mr-2">typ poplatku</span>
                </th>
                <th scope="col" class="px-1 py-3">
                    <span class="text-xs mr-2">SUMA (bez DPH)</span>
                </th>
                <th scope="col" class="px-1 py-3">
                    <span class="text-xs uppercase mr-2">suma</span>
                </th>
                <th scope="col" class="px-1 py-3">
                    <span class="text-xs uppercase mr-2">Daň</span>
                </th>
                <th scope="col" class="px-1 py-3">
                    <span class="text-xs uppercase mr-2">mena</span>
                </th>
            </tr>
        </thead>
        <tbody id="poplatkyTBODY">
            <?php
            foreach ($data as $key => $poplatok) {
                $bezdph = 100 * ($poplatok["suma"] / (100 + (float)$poplatok["dansadzba1"]));
                ?>
                <tr
                    class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 border-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600">
                    <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                        <input type="hidden" name="cislozmluvy" value="<?php echo $poplatok["cislozmluvy"]; ?>" />
                        <input type="hidden" name="fondid" value="<?php echo $poplatok["fondid"]; ?>" />
                        <input type="hidden" name="id" value="<?php echo $poplatok["id"]; ?>" />
                        <?php echo $poplatok["cislozmluvy"]; ?>
                    </th>
                    <td class="px-6 py-4">
                        <input type="hidden" name="typ" value="<?php echo $poplatok["typ"]; ?>" />
                        <?php echo $poplatok["typ"]; ?>
                    </td>
                    <td class="px-6 sumaColumnData py-4">
                        <p><?php echo $danove ?></p>
                        <input type="text" name="sumabezdph" class="block w-full sumabezdph p-2 text-gray-900 border border-gray-300 rounded-lg bg-gray-50 text-xs focus:ring-blue-500 
                        ocus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white 
                        dark:focus:ring-blue-500 dark:focus:border-blue-500"
                            value="<?php echo number_format($bezdph, 2, ".", " "); ?>" />
                    </td>
                    <td class="px-6 sumaColumnData py-4">
                        <input type="hidden" name="sumaPrevious" value="<?php echo $poplatok["suma"]; ?>" />
                        <input type="text" id="suma" name="suma" class="block w-full sumaInput p-2 text-gray-900 border border-gray-300 rounded-lg bg-gray-50 text-xs focus:ring-blue-500 
                        ocus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white 
                        dark:focus:ring-blue-500 dark:focus:border-blue-500"
                            value="<?php echo $poplatok["suma"]; ?>" />

                    </td>
                    <td class="px-6 sumaColumnData py-4">
                        <input type="hidden" class="dansadzba" name="dansadzba" value="<?php echo $poplatok["dansadzba1"] ? $poplatok["dansadzba1"] : 0; ?>" />
                        <p class="vyskaDane"><?php echo $poplatok["dan"]; ?></p>
                    </td>
                    <td class="px-6 menaColumnData py-4">
                        <input type="hidden" name="mena" value="<?php echo $poplatok["mena"]; ?>" />
                        <?php echo $poplatok["mena"]; ?>
                    </td>
                </tr>
            <?php } ?>
        </tbody>
    </table>
    <script>
        document.addEventListener('htmx:afterRequest', function (evt) {
            document.querySelectorAll(".sumabezdph").forEach((item) => {
                item.addEventListener("keyup", (e) => {
                    const bezDPH = e.target.value;
                    const suma = e.target.closest("tr").querySelector(".sumaColumnData input[name='suma']");
                    const dan = e.target.closest("tr").querySelector(".sumaColumnData input[name='dansadzba']").value;
                    console.log((100 + parseFloat(dan)) / 100)
                    suma.value = (bezDPH * (100 + parseFloat(dan)) / 100).toFixed(2);
                    e.target.closest("tr").querySelector(".sumaColumnData .vyskaDane").innerHTML = (suma.value - bezDPH).toFixed(2);
                });
            });
            document.querySelectorAll(".sumaInput").forEach((item) => {
                item.addEventListener("keyup", (e) => {
                    const suma = e.target.value;
                    const bezDPH = e.target.closest("tr").querySelector(".sumaColumnData input[name='sumabezdph']");
                    const dan = e.target.closest("tr").querySelector(".sumaColumnData input[name='dansadzba']").value;
                    bezDPH.value = (100 * (suma / (100 + parseFloat(dan)))).toFixed(2);
                    e.target.closest("tr").querySelector(".sumaColumnData .vyskaDane").innerHTML = (suma - parseFloat(bezDPH.value)).toFixed(2);
                });
            });
        });
    </script>
<?php } ?>
<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
require_once "/home/<USER>/www/src/lib/notifications/Notification.class.php";

$amount = $_POST["amount"];

if (isset($_SESSION["client"])) {
    $id_fond = $_SESSION["client"]["fondid"];
} elseif ($_SESSION["mode"]["mode"] === "admin") {
    $id_fond = 1;
} else {
    $id_fond = 0;
}

$poolid = $_POST["poolid"];
$accountFrom = $_POST["accountFrom"];
$accountTo = $_POST["accountTo"];
$mena = $_POST["accountFromCurr"];
$menaTo = $_POST["accountToCurr"];

$queryPool = [];

echo "mena: $mena, menaTo: $menaTo";
if($mena !== $menaTo){
    $error = "Nemôžete presunúť prostriedky medzi účtami s rôznymi menami. Prosím zvoľte účet s rovnakou menou.";
}

if ($amount === "" || $amount === NULL || $amount === 0) {
    $error = "Čiastka nemôže byť prázdna. Prosím zadajte valídne číslo.";
}
if ($amount > $_POST["accountFromAmount"]) {
    $error = "Nedostatok prostriedkov na tento presun. Prosím zadajte počet nižší alebo rovný ako je stav účtu.";
}

if ($error == "") {
    $sess_userid = $_SESSION["user"]["data"]["userid"];
    $dealid1 = Connection::getDataFromDatabase("SELECT nextval('s_konfirmaciapp')", defaultDB)[1][0]["nextval"];
    $dealid2 = Connection::getDataFromDatabase("SELECT nextval('s_konfirmaciapp')", defaultDB)[1][0]["nextval"];

    if (isset($_POST["poolData"])) {
        $id_fonds = $_POST["poolData"];
        $id_fonds = json_decode($id_fonds);
        foreach ($id_fonds as $key => $value) {
            $sumaBuy = $value->sumaBuy;
            $total += $sumaBuy;
            if ($sumaBuy > 0) {
                $fondid = $value->subjektid;
                $queryPool[] = [
                    "query" => "INSERT INTO pooldetail (subjektid, poolid, transsuma, ks) VALUES (?,?,?,?)",
                    "params" => [$fondid, $poolid, $sumaBuy, $sumaBuy],
                    "db" => defaultDB,
                    "name" => "insert into pooldetail $key"
                ];
            }
        }
    } else {
        $queryPool[] = [
            "query" => "INSERT INTO pooldetail (subjektid, poolid, transsuma, ks) VALUES (?,?,?,?)",
            "params" => [$id_fond, $poolid, $sumaBuy, $sumaBuy],
            "db" => defaultDB,
            "name" => "insert into pooldetail $key"
        ];
    }


    $seq = Connection::getDataFromDatabase("SELECT id from ididentify where popis like 'konfirmaciapp'", defaultDB)[1][0]["id"];

    $currentDate = Connection::getDataFromDatabase("SELECT datum from today where fondid=$id_fond", defaultDB)[1][0]["datum"];
    $datetimereal = date("Y-m-d");

    $allDealid1 = $dealid1 . $seq;
    $allDealid2 = $dealid2 . $seq;

    $queryPool[] = [
        "query" => "INSERT into konfirmaciapp
                (dealid, subjektid, dealid_related, druhobchodu,
                suma, mena, ucet,externy_ucet,datum_zauctovania,
                logactivityid,loguserid, logdatatimeactivity
                )
                values
                (?,?,?,?,?,?,?,?,?,?,?,?)",
        "params" => [
            $allDealid1,
            $id_fond,
            $allDealid2,
            'presun',
            $amount,
            $mena,
            $accountFrom,
            $accountTo,
            $currentDate,
            1,
            $sess_userid,
            $datetimereal
        ],
        "db" => defaultDB,
        "name" => "insert into konfirmaciapp 1"
    ];

    $queryPool[] = [
        "query" => "INSERT into konfirmaciapp
                (dealid, subjektid, dealid_related, druhobchodu,
                suma, mena, ucet,externy_ucet,datum_zauctovania,
                logactivityid,loguserid, logdatatimeactivity
                )
                values
                (?,?,?,?,?,?,?,?,?,?,?,?)",
        "params" => [
            $allDealid2,
            $id_fond,
            null,
            'presun',
            $amount,
            $mena,
            $accountTo,
            $accountFrom,
            $currentDate,
            1,
            $sess_userid,
            $datetimereal
        ],
        "db" => defaultDB,
        "name" => "insert into konfirmaciapp 2"
    ];

    if ($id_fond === 0) {
        $queryPool[] = [
            "query" => "UPDATE pool SET destinacia = ?, dealid = ?, dealid2 = ? where poolid = ?",
            "params" => ['konfirmaciapp', $allDealid1, $allDealid2, $poolid],
            "db" => defaultDB,
            "name" => "update pool"
        ];
    }

    $transaction = Connection::runTransaction(defaultDB, null, $queryPool);
    if (str_contains($transaction, "poriadku")) {
        $username = $_SESSION["user"]["data"]["username"];
        $sess_userid = $_SESSION["user"]["data"]["userid"];
        $notification = new Notification(
            1,
            'konfirmaciapp',
            $allDealid2,
            'presunCreate',
            $sess_userid,
            $username,
            "presunCreate",
            json_encode(["dealid", $allDealid2]),
            false,
            $id_fond
        );
        $notification->createNotifcation();
        ?>
        <div id="toast-success"
            class="flex border-green-300 border items-center w-full max-w-sm p-4 mb-4 text-green-500 bg-white rounded-lg shadow dark:text-gray-400 dark:bg-gray-800"
            role="alert">
            <div
                class="inline-flex items-center justify-center flex-shrink-0 w-8 h-8 text-green-500 bg-green-100 rounded-lg dark:bg-green-800 dark:text-green-200">
                <svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                    viewBox="0 0 20 20">
                    <path
                        d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 8.207-4 4a1 1 0 0 1-1.414 0l-2-2a1 1 0 0 1 1.414-1.414L9 10.586l3.293-3.293a1 1 0 0 1 1.414 1.414Z" />
                </svg>
                <span class="sr-only">Check icon</span>
            </div>
            <div class="ms-3 text-sm font-semibold">Presun prostriedkov bol úspešne inicalizovaný. <?php echo $transaction; ?>
            </div>
            <button type="button"
                class="ms-auto -mx-1.5 -my-1.5 bg-white text-gray-400 hover:text-gray-900 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex items-center justify-center h-8 w-8 dark:text-gray-500 dark:hover:text-white dark:bg-gray-800 dark:hover:bg-gray-700"
                data-dismiss-target="#toast-success" aria-label="Close">
                <span class="sr-only">Close</span>
                <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                </svg>
            </button>
        </div>
        <script>
            setTimeout(() => {
                htmx.ajax('GET', "/presun-prostriedkov", {
                    target: "#pageContentMain",
                });
            }, 1500);
        </script>
    <?php } else { ?>
        <div id="toast-danger" class="flex items-center w-full max-w-sm p-4 mb-4 text-white bg-red-500 rounded-lg shadow-lg"
            role="alert">
            <?php ?>
            <div
                class="inline-flex items-center justify-center flex-shrink-0 w-8 h-8 text-red-500 bg-red-100 rounded-lg dark:bg-red-800 dark:text-red-200">
                <svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                    viewBox="0 0 20 20">
                    <path
                        d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 11.793a1 1 0 1 1-1.414 1.414L10 11.414l-2.293 2.293a1 1 0 0 1-1.414-1.414L8.586 10 6.293 7.707a1 1 0 0 1 1.414-1.414L10 8.586l2.293-2.293a1 1 0 0 1 1.414 1.414L11.414 10l2.293 2.293Z" />
                </svg>
                <span class="sr-only">Error icon</span>
            </div>
            <div class="ms-3 text-sm font-bold">Nepodarilo sa vytvoriť presun! Chyba v databáze. <?php print_r($transaction); ?>
            </div>
            <button type="button"
                class="ms-auto -mx-1.5 -my-1.5 text-white hover:text-gray-900 transition-all rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex items-center justify-center h-8 w-8 dark:text-gray-500 dark:hover:text-white dark:bg-gray-800 dark:hover:bg-gray-700"
                data-dismiss-target="#toast-danger" aria-label="Close">
                <span class="sr-only">Close</span>
                <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                </svg>
            </button>
        </div>
        <script>
            setTimeout(() => {
                $("#toast-danger").fadeOut();
            }, 1500);
        </script>
    <?php }
} else {
    ?>
    <div id="toast-danger" class="flex items-center w-full max-w-sm p-4 mb-4 text-white bg-red-500 rounded-lg shadow-lg"
        role="alert">
        <?php ?>
        <div
            class="inline-flex items-center justify-center flex-shrink-0 w-8 h-8 text-red-500 bg-red-100 rounded-lg dark:bg-red-800 dark:text-red-200">
            <svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                viewBox="0 0 20 20">
                <path
                    d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 11.793a1 1 0 1 1-1.414 1.414L10 11.414l-2.293 2.293a1 1 0 0 1-1.414-1.414L8.586 10 6.293 7.707a1 1 0 0 1 1.414-1.414L10 8.586l2.293-2.293a1 1 0 0 1 1.414 1.414L11.414 10l2.293 2.293Z" />
            </svg>
            <span class="sr-only">Error icon</span>
        </div>
        <div class="ms-3 text-sm font-bold"><?php echo $error; ?></div>
        <button type="button"
            class="ms-auto -mx-1.5 -my-1.5 text-white hover:text-gray-900 transition-all rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex items-center justify-center h-8 w-8 dark:text-gray-500 dark:hover:text-white dark:bg-gray-800 dark:hover:bg-gray-700"
            data-dismiss-target="#toast-danger" aria-label="Close">
            <span class="sr-only">Close</span>
            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
            </svg>
        </button>
    </div>
    <script>
        setTimeout(() => {
            $("#toast-danger").fadeOut();
        }, 5000);
    </script>
    <?php
}
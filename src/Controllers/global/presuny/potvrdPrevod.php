<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
require_once "/home/<USER>/www/src/lib/notifications/Notification.class.php";

$dealid = $_POST["dealid"];
$ucet_zdroj = $_POST["ucet_zdroj"];
$ucet_ciel = $_POST["ucet_ciel"];
$mena = $_POST["mena"];
$suma = $_POST["suma"];

$queryPool = [];

if (isset($_SESSION["client"])) {
    $id_fond = $_SESSION["client"]["fondid"];
} elseif ($_SESSION["mode"]["mode"] === "admin") {
    $id_fond = 1;
} else {
    $id_fond = 0;
}

$username = $_SESSION["user"]["data"]["username"];
$sess_userid = $_SESSION["user"]["data"]["userid"];

$dealidNEW = Connection::getDataFromDatabase("SELECT nextval('majetok_today_obratid')", defaultDB)[1][0]["nextval"];

$queryPool[] = [
    "query" => "UPDATE konfirmaciapp SET logactivityid = 12, datum_zauctovania = (select datum from today where fondid = $id_fond)
        WHERE dealid in ($dealid, $dealidNEW)",
    "params" => [],
    "db" => defaultDB,
    "name" => "Update konfirmaciapp a.k.a. potvrdenie prevodu"
];

if ($id_fond != 0) {
    $queryPool[] = [
        "query" => "INSERT INTO majetoktoday (obratid,destinacia,subjektid,kodobratu,
				 	uctovnykod,eqid,kodaktiva,ucetaktiva,jednotka,
				 	pocet,mena,md_d,obratdatetimereal,obratdatatimezauctovane) 
                    select 
				 	$dealidNEW,'konfirmaciapp',$id_fond,279,uctovnykod, equid,'" . $mena . "','" . $ucet_zdroj . "',
				 	'" . $mena . "'," . $suma . ",'" . $mena . "',md_d,CURRENT_DATE,(select datum from today where fondid = $id_fond)
				 	from kodobratumd_d where kodobratu=279 and md_d=1 and subkodobratu in (0,1)",
        "params" => [],
        "db" => defaultDB,
        "name" => "Insert into majetoktoday a.k.a. potvrdenie prevodu"
    ];

    $queryPool[] = [
        "query" => "INSERT INTO majetoktoday (destinacia,eqid,jednotka,
				kodaktiva,kodobratu,
				md_d,mena,
				obratdatatimezauctovane,obratdatetimereal,obratid,
				pocet,subjektid,
				ucetaktiva,uctovnykod) 
			SELECT
				'konfirmaciapp',equid,'" . $mena . "',
				'" . $mena . "', 274,
				md_d,'" . $mena . "', 
				(select datum from today where fondid = $id_fond), CURRENT_DATE,$dealidNEW,
				" . $suma . ",$id_fond,
				'" . $ucet_ciel . "', uctovnykod
			FROM
				kodobratumd_d
			WHERE
				kodobratu = 274 and md_d = 0",
        "params" => [],
        "db" => defaultDB,
        "name" => "Insert into majetoktoday a.k.a. potvrdenie prevodu 2"
    ];
} else {
    $queryPool[] = [
        "query" => "INSERT INTO majetoktoday(obratid,destinacia,subjektid,kodobratu,
				 	uctovnykod,eqid,kodaktiva,ucetaktiva,jednotka,
				 	pocet,mena,md_d,obratdatetimereal,obratdatatimezauctovane) 
				select 
				 	$dealidNEW,'konfirmaciapp',pd.subjektid,279,uctovnykod, equid,'" . $mena . "',
					(CASE WHEN pd.transsuma>=0 THEN '" . $ucet_zdroj . "' ELSE '" . $ucet_ciel . "' END),
				 	'" . $mena . "',abs(pd.transsuma),'" . $mena . "',md_d,CURRENT_DATE,(select datum from today where fondid = $id_fond)
				 from kodobratumd_d, pool p, pooldetail pd where kodobratu=279 and md_d=1 and subkodobratu in (0,1) and
				 (p.dealid = $dealid or p.dealid2 = $dealid) and p.poolid = pd.poolid",
        "params" => [],
        "db" => defaultDB,
        "name" => "Insert into majetoktoday a.k.a. potvrdenie prevodu 3"
    ];

    $queryPool[] = [
        "query" => "INSERT INTO
				majetoktoday 
				(destinacia,eqid,jednotka,
				kodaktiva,kodobratu,
				md_d,mena,
				obratdatatimezauctovane,obratdatetimereal,obratid,
				pocet,subjektid,
				ucetaktiva,uctovnykod) 
			SELECT
				'konfirmaciapp',equid,'" . $mena . "',
				'" . $mena . "', 274,
				md_d,'" . $mena . "', 
				(select datum from today where fondid = $id_fond), CURRENT_DATE,$dealidNEW,
				abs(pd.transsuma),pd.subjektid,
				(CASE WHEN pd.transsuma>=0 THEN '" . $ucet_ciel . "' else '" . $ucet_zdroj . "' END), uctovnykod
			FROM
				kodobratumd_d, pool p, pooldetail pd 
			WHERE
				kodobratu = 274 and md_d = 0  and
				(p.dealid = $dealid or p.dealid2 = $dealid) and p.poolid = pd.poolid",
        "params" => [],
        "db" => defaultDB,
        "name" => "Insert into majetoktoday a.k.a. potvrdenie prevodu 4"
    ];
}

$queryPool[] = [
    "query" => "INSERT INTO konfirmaciappobratid (dealid, obratid) VALUES (?,?)",
    "params" => [$dealid, $dealidNEW],
    "db" => defaultDB,
    "name" => "Insert into konfirmaciappobratid"
];

$year = gmdate("y");

$queryPool[] = [
    "query" => "INSERT INTO dennikpm
    (
			userid,
			dealid,
			destinacia,
			poradovecislo,
			cislo
		)
		values
		(
			$sess_userid,
			" . $dealid . ",
			'konfirmaciapp',
			(
				select COALESCE(max(poradovecislo),0) + 1
				from dennikpm 
				where userid=$sess_userid
			),
			to_char(
				(select COALESCE(max(poradovecislo),0) + 1
				from dennikpm
				where userid=$sess_userid
				), 'FM999999'
			) || lpad('$year',2,'0') || lpad((
							select to_char(agentid, 'FM9999')
							from users
							where userid=$sess_userid
							),4,'0')
		)",
    "params" => [],
    "db" => defaultDB,
    "name" => "Insert into dennikpm"
];

$transaction = Connection::runTransaction(defaultDB, null, $queryPool);
if (str_contains($transaction, "poriadku")) {
    $notification = new Notification(
        12,
        'konfirmaciapp',
        $dealidNEW,
        'potvrdPrevodHelper',
        $sess_userid,
        $username,
        "potvrdPrevodHelper",
        json_encode(["dealid", $dealidNEW]),
        false,
        $id_fond
    );
    $notification->createNotifcation();

    $notification = new Notification(
        12,
        'konfirmaciapp',
        $dealid,
        'potvrdPrevod',
        $sess_userid,
        $username,
        "potvrdPrevod",
        json_encode(["dealid", $dealid]),
        false,
        $id_fond
    );
    $notification->createNotifcation();

    ?>
    <div id="toast-success"
        class="flex border-green-300 border items-center w-full max-w-sm p-4 mb-4 text-green-500 bg-white rounded-lg shadow dark:text-gray-400 dark:bg-gray-800"
        role="alert">
        <div
            class="inline-flex items-center justify-center flex-shrink-0 w-8 h-8 text-green-500 bg-green-100 rounded-lg dark:bg-green-800 dark:text-green-200">
            <svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                viewBox="0 0 20 20">
                <path
                    d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 8.207-4 4a1 1 0 0 1-1.414 0l-2-2a1 1 0 0 1 1.414-1.414L9 10.586l3.293-3.293a1 1 0 0 1 1.414 1.414Z" />
            </svg>
            <span class="sr-only">Check icon</span>
        </div>
        <div class="ms-3 text-sm font-semibold">Presun prostriedkov bol úspešne inicalizovaný. <?php echo $transaction; ?>
        </div>
        <button type="button"
            class="ms-auto -mx-1.5 -my-1.5 bg-white text-gray-400 hover:text-gray-900 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex items-center justify-center h-8 w-8 dark:text-gray-500 dark:hover:text-white dark:bg-gray-800 dark:hover:bg-gray-700"
            data-dismiss-target="#toast-success" aria-label="Close">
            <span class="sr-only">Close</span>
            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
            </svg>
        </button>
    </div>
    <script>
        setTimeout(() => {
            htmx.ajax('GET', "/presun-prostriedkov", {
                target: "#pageContentMain",
            });
        }, 1500);
    </script>
<?php } else { ?>
    <div id="toast-danger"
        class="flex items-center w-full max-w-xs p-4 mb-4 text-gray-500 bg-white rounded-lg shadow dark:text-gray-400 dark:bg-gray-800"
        role="alert">
        <?php ?>
        <div
            class="inline-flex items-center justify-center flex-shrink-0 w-8 h-8 text-red-500 bg-red-100 rounded-lg dark:bg-red-800 dark:text-red-200">
            <svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                viewBox="0 0 20 20">
                <path
                    d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 11.793a1 1 0 1 1-1.414 1.414L10 11.414l-2.293 2.293a1 1 0 0 1-1.414-1.414L8.586 10 6.293 7.707a1 1 0 0 1 1.414-1.414L10 8.586l2.293-2.293a1 1 0 0 1 1.414 1.414L11.414 10l2.293 2.293Z" />
            </svg>
            <span class="sr-only">Error icon</span>
        </div>
        <div class="ms-3 text-sm font-normal">Nepodarilo sa potvrdiť prevod kvôli neznámej chybe.</div>
        <div>
            <?php print_r($transaction); ?>
        </div>
        <button type="button"
            class="ms-auto -mx-1.5 -my-1.5 bg-white text-gray-400 hover:text-gray-900 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex items-center justify-center h-8 w-8 dark:text-gray-500 dark:hover:text-white dark:bg-gray-800 dark:hover:bg-gray-700"
            data-dismiss-target="#toast-danger" aria-label="Close">
            <span class="sr-only">Close</span>
            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
            </svg>
        </button>
    </div>
    <script>
        setTimeout(() => {
            $("#toast-danger").fadeOut();
            document.querySelector(".potvrdIcon").style.display = "inline-flex";
            document.querySelector(".potvrdSpinner").style.display = "none";
        }, 2000);
    </script>
<?php }
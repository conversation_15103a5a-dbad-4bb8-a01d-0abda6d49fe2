<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
require_once "/home/<USER>/www/src/lib/notifications/Notification.class.php";

$dealid = $_POST["dealid"];

if (isset($_SESSION["client"])) {
    $id_fond = $_SESSION["client"]["fondid"];
} elseif ($_SESSION["mode"]["mode"] === "admin") {
    $id_fond = 1;
} else {
    $id_fond = 0;
}

$queryPool = [];

$username = $_SESSION["user"]["data"]["username"];
$sess_userid = $_SESSION["user"]["data"]["userid"];

$queryPool[] = [
    "query" => "UPDATE konfirmaciapp SET logactivityid = 3 WHERE dealid = ?",
    "params" => [$dealid],
    "db" => defaultDB,
    "name" => "Update konfirmaciapp a.k.a. odstranenie"
];

$queryPool[] = [
    "query" => "UPDATE konfirmaciapp SET logactivityid = 3 where
			dealid = (select dealid_related from konfirmaciapp where dealid=$dealid)",
    "params" => [],
    "db" => defaultDB,
    "name" => "Update konfirmaciapp a.k.a. odstranenie"
];

$transaction = Connection::runTransaction(defaultDB, null, $queryPool);
if (str_contains($transaction, "poriadku")) {
    $notification = new Notification(
        3,
        'konfirmaciapp',
        $dealid,
        'deletePresunProstriedkovHelper',
        $sess_userid,
        $username,
        "deletePresunProstriedkovHelper",
        json_encode(["dealid", $dealid]),
        false,
        $id_fond,
        "helper"
    );
    $notification->createNotifcation();

    $dealidRelated = Connection::getDataFromDatabase("select dealid_related from konfirmaciapp where dealid = $dealid", defaultDB)[1][0]["dealid_related"];
    $notification = new Notification(
        3,
        'konfirmaciapp',
        $dealidRelated,
        'deletePresunProstriedkov',
        $sess_userid,
        $username,
        "deletePresunProstriedkov",
        json_encode(["dealid", $dealidRelated]),
        false,
        $id_fond
    );
    $notification->createNotifcation(); ?>
    <div id="toast-success"
        class="flex border-green-300 border items-center w-full max-w-sm p-4 mb-4 text-green-500 bg-white rounded-lg shadow dark:text-gray-400 dark:bg-gray-800"
        role="alert">
        <div
            class="inline-flex items-center justify-center flex-shrink-0 w-8 h-8 text-green-500 bg-green-100 rounded-lg dark:bg-green-800 dark:text-green-200">
            <svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                viewBox="0 0 20 20">
                <path
                    d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 8.207-4 4a1 1 0 0 1-1.414 0l-2-2a1 1 0 0 1 1.414-1.414L9 10.586l3.293-3.293a1 1 0 0 1 1.414 1.414Z" />
            </svg>
            <span class="sr-only">Check icon</span>
        </div>
        <div class="ms-3 text-sm font-semibold">Presun prostriedkov bol úspešne inicalizovaný. <?php echo $transaction; ?>
        </div>
        <button type="button"
            class="ms-auto -mx-1.5 -my-1.5 bg-white text-gray-400 hover:text-gray-900 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex items-center justify-center h-8 w-8 dark:text-gray-500 dark:hover:text-white dark:bg-gray-800 dark:hover:bg-gray-700"
            data-dismiss-target="#toast-success" aria-label="Close">
            <span class="sr-only">Close</span>
            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
            </svg>
        </button>
    </div>
    <script>
        setTimeout(() => {
            htmx.ajax('GET', "/presun-prostriedkov", {
                target: "#pageContentMain",
            });
        }, 1500);
    </script>
    <?php
} else {
    echo "error";
}
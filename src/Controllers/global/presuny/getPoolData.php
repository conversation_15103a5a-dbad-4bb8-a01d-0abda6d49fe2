<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
$include = [1, 6];

$include_sql = implode(",", $include);
if ($include_sql != "") {
    $include_sql = "," . $include_sql;
}


$selectik = "
	SELECT 
		poolid, subjektid, cislozmluvy, 
		SUM(pocet * (CASE WHEN kategoria in (0 $include_sql) AND mena = '$mena' and ucetaktiva='$accountFrom' THEN 1 ELSE 0 END)) as stav_bu_cashflow1,
        SUM(pocet * (CASE WHEN kategoria in (4,5) AND mena = '$mena' and ucetaktiva='$accountFrom' THEN 1 ELSE 0 END)) as stav_minus1,
		SUM(pocet * (CASE WHEN kategoria in (0 $include_sql) AND mena = '$mena' and ucetaktiva='$accountTo' THEN 1 ELSE 0 END)) as stav_bu_cashflow2,
        SUM(pocet * (CASE WHEN kategoria in (4,5) AND mena = '$mena' and ucetaktiva='$accountTo' THEN 1 ELSE 0 END)) as stav_minus2
	FROM 
		poolasset pl, portfolio p, fonds f
	WHERE 
		pl.poolid = $poolid AND
		f.fondid=p.fondid AND
		pl.subjektid = p.fondid AND
		pl.eqid = 'BU'
	GROUP BY 
		cislozmluvy,poolid, subjektid
	ORDER BY
		cislozmluvy
";
echo $selectik;
$selectConn = Connection::getDataFromDatabase($selectik, defaultDB);
?>

<table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
    <thead class="text-xs text-gray-700 uppercase bg-gray-100 dark:bg-gray-700 dark:text-gray-400">
        <tr>
            <th scope="col" class="px-6 py-3 rounded-s-lg">
                Číslo zmluvy
            </th>
            <th scope="col" class="px-6 py-3">
                Stav na účte
            </th>
            <th scope="col" class="px-6 py-3">
                Odchádzajúce / Rezervované
            </th>
            <th scope="col" class="px-6 py-3">
                Prevod z účtu<br />
                <?php echo $accountFrom; ?>
            </th>
            <th scope="col" class="px-6 py-3">
                Prevod na účet<br />
                <?php echo $accountTo; ?>
            </th>
            <th scope="col" class="px-6 py-3">
                Odchádzajúce / Rezervované
            </th>
            <th scope="col" class="px-6 py-3">
                Stav na účte
            </th>
        </tr>
    </thead>
    <tbody id="swapper">
        <?php
        $sumaAll = 0;
        foreach ($selectConn[1] as $key => $item) {
            $maxPossible1 = 0;
            $maxPossible2 = 0;

            if ($item["stav_bu_cashflow2"] < 0 && $item["stav_bu_cashflow1"] === 0) {
                $maxPossible1 = abs($item["stav_bu_cashflow2"]);
            } else {
                $maxPossible1 = $item["stav_bu_cashflow1"];
            }

            if ($item["stav_bu_cashflow1"] < 0 && $item["stav_bu_cashflow2"] === 0) {
                $maxPossible2 = abs($item["stav_bu_cashflow1"]);
            } else {
                $maxPossible2 = $item["stav_bu_cashflow2"];
            }

            ?>
            <tr class="bg-white odd:bg-white hover:bg-gray-100 even:bg-gray-50 dark:bg-gray-800">
                <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                    <a href="/klienti/detail/<?php echo $item["podielnikid"]; ?>/#<?php echo $item["cislozmluvy"]; ?>"
                        class="p-0.5 px-3 hover:underline bg-gray-700 text-white rounded-lg inline-flex gap-1 items-center"><?php echo $item["cislozmluvy"]; ?><svg
                            xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-square-arrow-out-up-right">
                            <path d="M21 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h6" />
                            <path d="m21 3-9 9" />
                            <path d="M15 3h6v6" />
                        </svg></a>
                </th>
                <td class="px-6 text-right font-bold py-4">
                    <span
                        class="<?php echo (int) $item["stav_bu_cashflow1"] >= 0 ? "bg-green-500" : "bg-red-500" ?> px-2 py-1 rounded-lg text-white font-bold">

                        <?php echo number_format($item["stav_bu_cashflow1"], 2, ".", " "); ?></span>
                </td>
                <td class="px-6 text-right font-bold py-4">
                    <span
                        class="<?php echo (int) $item["stav_minus1"] >= 0 ? "bg-green-300 text-green-700" : "bg-red-300 text-red-700" ?> px-2 py-1 rounded-lg font-bold">
                        <?php echo number_format($item["stav_minus1"], 2, ".", " "); ?></span>
                </td>
                <td class="px-6 py-4 inputGroup">
                    <form class="accFromForm flex items-center gap-2">
                        <input type="hidden" name="maxPossible" value="<?php echo $maxPossible1; ?>" />
                        <input type="hidden" name="subjektid" id="subjektid" value="<?php echo $item["subjektid"]; ?>" />
                        <input type="text" name="suma" value="0" class="accFromInput" style="all: unset; border: 1px solid #e7e7e7;
  border-radius: 7px;
  padding: 0.3rem .6rem; text-align: right; font-weight: bold;" />
                        <button type="submit"
                            class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm p-2.5 text-center inline-flex items-center me-2 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24"
                                height="24" fill="none" viewBox="0 0 24 24">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="4"
                                    d="M5 11.917 9.724 16.5 19 7.5" />
                            </svg>
                        </button>
                    </form>
                </td>
                <td class="px-6 py-4 inputGroup ">
                    <form class="accToForm flex items-center gap-2">
                        <input type="hidden" name="maxPossible" value="<?php echo $maxPossible2; ?>" />
                        <input type="hidden" name="subjektid" id="subjektid" value="<?php echo $item["subjektid"]; ?>" />
                        <input type="text" name="suma" value="0" class="accToInput" style="all: unset; border: 1px solid #e7e7e7;
  border-radius: 7px;
  padding: 0.3rem .6rem; text-align: right; font-weight: bold;" />
                        <button type="submit"
                            class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm p-2.5 text-center inline-flex items-center me-2 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24"
                                height="24" fill="none" viewBox="0 0 24 24">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="4"
                                    d="M5 11.917 9.724 16.5 19 7.5" />
                            </svg>
                        </button>
                    </form>
                </td>
                <td class="px-6 text-right font-bold py-4">
                    <?php echo number_format($item["stav_minus2"], 2, ".", " "); ?>
                </td>
                <td class="px-6 text-right font-bold py-4">
                    <?php echo number_format($item["stav_bu_cashflow2"], 2, ".", " "); ?>
                </td>
            </tr>
            <?php
        } ?>
    </tbody>
</table>
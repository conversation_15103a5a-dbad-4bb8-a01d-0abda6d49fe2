<?php
/**
 * <PERSON><PERSON>ný súbor [dlhopis_ifr.php]
 */
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
require_once "/home/<USER>/www/src/lib/notifications/Notification.class.php";
require_once "/home/<USER>/www/src/lib/notifications/Subactivity.class.php";

$errors = [];
$action = $_POST["action"];
$data = json_decode($_POST["data"]);
$sess_userid = $_SESSION["user"]["data"]["userid"];
$username = $_SESSION["user"]["data"]["username"];

if (str_contains($_SERVER["REQUEST_URI"], "/kupon/potvrdenie")) {
    $itemect = "kupon";
    $action = "potvrdenie";
    $pageHeading = "Potvrdenie kupónu (zoznam dlhopisov)";
} else if (str_contains($_SERVER["REQUEST_URI"], "/kupon/splatenie")) {
    $itemect = "kupon";
    $action = "splatenie";
    $pageHeading = "Splatenie kupónu (zoznam dlhopisov)";
}

if ($action === "potvrdenie") {
    foreach ($data as $item) {
        $subjektid = $item->subjektid;
        $pocet = $item->pocet;
        $mena = $item->mena;
        $uctovnykod = $item->uctovnykod;
        $kodobratu = $item->kodobratu;
        $kodaktiva = $item->kodaktiva;
        $ucetaktiva = $item->ucetaktiva;
        $ocakdate = $item->ocakdate;
        $suma = $item->suma;
        $obratid = Connection::getDataFromDatabase("SELECT nextval('majetok_today_obratid')", defaultDB)[1][0]["nextval"];
        $today = Connection::getDataFromDatabase("SELECT datum from today where fondid = $subjektid", defaultDB)[1][0]["datum"];

        $query = "INSERT INTO majetoktoday (obratid, destinacia, subjektid,kodobratu,uctovnykod,eqid,kodaktiva,ucetaktiva,
				jednotka,pocet,mena,md_d,obratdatetimereal,obratdatatimezauctovane) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,CURRENT_TIMESTAMP,?)";
        echo "INSERT INTO majetoktoday (obratid, destinacia, subjektid,kodobratu,uctovnykod,eqid,kodaktiva,ucetaktiva,
				jednotka,pocet,mena,md_d,obratdatetimereal,obratdatatimezauctovane) VALUES ($obratid, 'dbequity', $subjektid, $kodobratu, $uctovnykod, 'Bonds', $kodaktiva, $ucetaktiva, 'ks', $pocet, $mena, 0, CURRENT_TIMESTAMP, $today)";
        $insertIntoMajetokToday = Connection::InsertUpdateCreateDelete($query, [$obratid, 'dbequity', $subjektid, $kodobratu, $uctovnykod, 'Bonds', $kodaktiva, $ucetaktiva, 'ks', $pocet, $mena, 0, $today], defaultDB);
        if (gettype($insertIntoMajetokToday) !== "integer") {
            $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [majetoktoday]. " . $insertIntoMajetokToday;
        } else {
            $notification = new Notification(0, 'majetoktoday', $subjektid, $item->typ === "istina" ? "dlhopisConfirmNominalu" : "dlhopisConfirmKuponu", $sess_userid, $username, $item->typ === "istina" ? "dlhopisConfirmNominalu" : "dlhopisConfirmKuponu", json_encode(["subjektid", $subjektid]), false, 0);
            $notification->createNotifcation();
        }
    }
} else {
    foreach ($data as $item) {
        $subseparator = "";
        $queryMajetokCesta = "";
        $queryCashflow = "";
        $tempMajetokToday = "";
        $queryMajetokToday = "";
        $opacnyZaznamExistuje = false;
        $splatnyDlhopis = false;
        $_suma = 0;
        $queryObraty = "";
        $subjektid = $item->subjektid;
        $pocet = $item->pocet;
        $mena = $item->mena;
        $uctovnykod = $item->uctovnykod;
        $kodobratu = $item->kodobratu;
        $kodaktiva = $item->kodaktiva;
        $ucetaktiva = $item->ucetaktiva;
        $ocakdate = $item->ocakdate;
        $suma = $item->suma;
        $cub = $item->cub;
        $today = Connection::getDataFromDatabase("SELECT datum from today where fondid = $subjektid", defaultDB)[1][0]["datum"];
        $vs = $item->vs;

        $splatenieQuery = "SELECT *, (CASE WHEN maturitydate <= '$ocakdate' THEN 1 ELSE 0 END) as posledneSplatenie FROM dbequity d WHERE d.isin IN (
            SELECT isin FROM dbequitycurr WHERE isincurr IN (
                SELECT isincurr FROM dbequitycurrric WHERE isincurrric = '$kodaktiva'))";
        $posledneSplatenie = Connection::getDataFromDatabase($splatenieQuery, defaultDB)[1][0];
        if (!$splatnyDlhopis) {
            $dealidseq = Connection::getDataFromDatabase("SELECT nextval('s_maturity')", defaultDB)[1][0]["nextval"];
            $dealid = $dealidseq . "910";
            if (!isset($vs)) {
                $vs = $dealid;
            }

            $ocakDatePlus = strtotime($ocakdate);
            $ocakDatePlus = date('Y-m-d', strtotime($ocakdate . ' + 7 days'));

            $insertIntoMajetokCesta = Connection::InsertUpdateCreateDelete("INSERT INTO majetokcesta (dealid,tranza,in_out,sparovanie,destinacia,subjektid,eqid,kodaktiva,ucetaktiva,jednotka,mena,pocet,cestafrom,cestatill,popis) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)", [
                $dealid,
                1,
                1,
                $vs,
                'dbequity',
                $subjektid,
                'BU',
                $mena,
                $cub,
                $mena,
                $mena,
                $suma,
                $ocakdate,
                $ocakDatePlus,
                'Splatný kupón'
            ], defaultDB);
            if (gettype($insertIntoMajetokCesta) !== "integer") {
                $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [majetokcesta]. " . $insertIntoMajetokCesta;
            }
        }

        if ($posledneSplatenie["posledneSplatenie"] == 1 && isset($kodaktiva)) {
            $splatnyDlhopis = true;
            $dealidseq = Connection::getDataFromDatabase("SELECT nextval('s_maturity')", defaultDB)[1][0]["nextval"];
            $dealid = $dealidseq . "910";
            if (!isset($vs)) {
                $vs = $dealid;
            }

            $ocakDatePlus = strtotime($ocakdate);
            $ocakDatePlus = date('Y-m-d', strtotime($ocakdate . ' + 7 days'));

            $insertIntoMajetokCesta = Connection::InsertUpdateCreateDelete("INSERT INTO majetokcesta (dealid,tranza,in_out,sparovanie,destinacia,subjektid,eqid,kodaktiva,ucetaktiva,jednotka,mena,pocet,cestafrom,cestatill,popis) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)", [
                $dealid,
                1,
                1,
                $vs,
                'dbequity',
                $subjektid,
                'BU',
                $mena,
                $cub,
                $mena,
                $mena,
                $suma,
                $ocakdate,
                $ocakDatePlus,
                'Splatný dlhopis'
            ], defaultDB);
            if (gettype($insertIntoMajetokCesta) !== "integer") {
                $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [majetokcesta]. " . $insertIntoMajetokCesta;
            }
        }

        $wherePredajQuery = "r.dealid = k.dealid and
				k.isin = '" . $item->isin . "' and
				k.ric = '" . $item->ric . "' and
				k.currencyidtrade = '" . $item->curr . "' and
				k.druhobchodu = 'predaj' and
				k.subjektid = $subjektid and
				r.datvysporiadaniamureal >
					(select COALESCE(max(datum),to_date('1970-01-01','YYYY-MM-DD'))
					from poziciacp pc
					where
						pc.isincurrric like '" . $item->kodaktiva . "' and
						pc.subjektid = $subjektid and
						pc.uctovnykod = " . $item->uctovnykod . "
		 	)";

        $whereNakupQuery = "
				r.dealid = k.dealid and
				k.isin = '" . $item->isin . "' and
				k.ric = '" . $item->ric . "' and
				k.currencyidtrade = '" . $item->curr . "' and
				k.druhobchodu = 'nakup' and
				k.subjektid = $subjektid and
				r.datvysporiadaniamureal >
					(select COALESCE(max(datum),'1970-01-01')
					from poziciacp pc
					where
						pc.isincurrric like '" . $item->kodaktiva . "' and
						pc.subjektid = $subjektid and
						pc.uctovnykod = " . $item->uctovnykod . ")";

        // query pouzite v zapise do majetoktoday sa lisi pre nominal a kupon - teraz ho vytvorime
        if ($item->typ === "istina") {
            $vap = "COALESCE(
				(
					(
						(select sum(r.cenaobchodu - r.auvreal)
						from
						rekonfirmaciacp r, konfirmaciacp k
						where $where_nakup)
						-
							COALESCE(
						(
									SELECT
										sum(r.obstar_hodnota)
									FROM
							rekonfirmaciacp r, konfirmaciacp k
									WHERE
										$where_predaj
								),0
						)
					)
					/
					(
						(select sum(r.kusovreal)
						from
						rekonfirmaciacp r, konfirmaciacp k
						where $where_nakup)
						-
						(select COALESCE(sum(r.kusovreal),0)
						from
						rekonfirmaciacp r, konfirmaciacp k
						where $where_predaj)
					)
				),0)
				* " . $obj->pocetkusov[$j] . " ";

            // zakladna hodnota kodu obratu pre zapisy do majetoktoday - pre selektovanie z tabulky kodobratumd_d
            $basecode = 136;

            // NA TOTO SA TREBA POZRIET CI TO TREBA ALE DAL SOM TO TU
            if ($posledneSplatenie["druheqid"] == 0) {
                // Aha, toto je SPP - u nej je potrebne rozdelit vynos
                // na kapitalovy a urokovy (subkody 2 a 3)
                $subkod = 2;
                $kurzspp = $posledneSplatenie['kurzspp'];
            } else {
                $subkod = 1;
            }

            $subkod = 1;
        } else {
            //výpočet pre kupón
            $vap = "COALESCE(
					(
						(
							(select sum(r.auvreal)
							from
							rekonfirmaciacp r, konfirmaciacp k
							where $where_nakup)
							-
							COALESCE(
							(
									SELECT
										sum(r.obstar_auv)
									FROM
								rekonfirmaciacp r, konfirmaciacp k
									WHERE
										$where_predaj
								),0
							)
						)
						/
						(
							(select sum(r.kusovreal)
							from
							rekonfirmaciacp r, konfirmaciacp k
							where $where_nakup)
							-
							(select COALESCE(sum(r.kusovreal),0)
							from
							rekonfirmaciacp r, konfirmaciacp k
							where $where_predaj)
						)
					),0)
		 			* " . $item->pocetkusov . " ";

            // zakladna hodnota kodu obratu pre zapisy do majetoktoday - pre selektovanie z tabulky kodobratumd_d
            $basecode = 137;
            $subkod = 0;
            $istina = false;
        }

        $obratid = Connection::getDataFromDatabase("SELECT nextval('majetok_today_obratid')", defaultDB)[1][0]["nextval"];
        if ($subkod == 2) {
            $vap_a = '(' . $item->suma . ' - (' . $item->suma . ' * (1 - (' . $kurzspp . '/100))))';
        } else {
            $vap_a = $vap;
        }


        $kodobratumddValues = Connection::getDataFromDatabase("SELECT uctovnykod, equid FROM kodobratumd_d WHERE kodobratu=$basecode AND subkodobratu=$subkod AND md_d=0", defaultDB)[1][0];
        $majetokTodayInsert1 = Connection::InsertUpdateCreateDelete("INSERT INTO majetoktoday (obratid, destinacia, subjektid,kodobratu,uctovnykod,eqid,kodaktiva,ucetaktiva,jednotka,pocet,mena,md_d,obratdatetimereal,obratdatatimezauctovane) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,CURRENT_TIMESTAMP,?)", [
            $obratid,
            'dbequity',
            $subjektid,
            $basecode,
            $kodobratumddValues["uctovnykod"],
            $kodobratumddValues["equid"],
            $kodaktiva,
            $ucetaktiva,
            $mena,
            0,
            $mena,
            0,
            $today
        ], defaultDB);
        if (gettype($majetokTodayInsert1) !== "integer") {
            $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [majetoktoday1]. " . $majetokTodayInsert1;
        }

        // Zrušenie kupónu (vždy) / istiny (pri) poslednom splatení
        $kodobratumddValues = Connection::getDataFromDatabase("SELECT uctovnykod, equid FROM kodobratumd_d WHERE kodobratu=$basecode AND md_d=1 AND subkodobratu=0", defaultDB)[1][0];
        $majetokTodayInsert2 = Connection::InsertUpdateCreateDelete("INSERT INTO majetoktoday (obratid, destinacia, subjektid,kodobratu,uctovnykod,eqid,kodaktiva,ucetaktiva,jednotka,pocet,mena,md_d,obratdatetimereal,obratdatatimezauctovane) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,CURRENT_TIMESTAMP,?)", [
            $obratid,
            'dbequity',
            $subjektid,
            $basecode,
            $kodobratumddValues["uctovnykod"],
            $kodobratumddValues["equid"],
            $kodaktiva,
            $ucetaktiva,
            'ks',
            $pocet,
            $mena,
            1,
            $today
        ], defaultDB);
        if (gettype($majetokTodayInsert2) !== "integer") {
            $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [majetoktoday2]. " . $majetokTodayInsert2;
        }

        // Zrušenie pohľadávky
        $kodobratumddValues = Connection::getDataFromDatabase("SELECT uctovnykod, equid FROM kodobratumd_d WHERE kodobratu=$basecode AND md_d=1 AND subkodobratu=4", defaultDB)[1][0];
        $majetokTodayInsert3 = Connection::InsertUpdateCreateDelete("INSERT INTO majetoktoday (obratid, destinacia, subjektid,kodobratu,uctovnykod,eqid,kodaktiva,ucetaktiva,jednotka,pocet,mena,md_d,obratdatetimereal,obratdatatimezauctovane) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,CURRENT_TIMESTAMP,?)", [
            $obratid,
            'dbequity',
            $subjektid,
            $basecode,
            $kodobratumddValues["uctovnykod"],
            $kodobratumddValues["equid"],
            $kodaktiva,
            $ucetaktiva,
            'ks',
            $pocet,
            $mena,
            1,
            $today
        ], defaultDB);
        if (gettype($majetokTodayInsert3) !== "integer") {
            $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [majetoktoday3]. " . $majetokTodayInsert3;
        }

        if ($subkod == 2) {
            $vap_b = '(' . $vap . ') - (' . $item->suma . '*(' . $kurzspp . '/100))';
            $kodobratumddValues = Connection::getDataFromDatabase("SELECT uctovnykod, equid FROM kodobratumd_d WHERE kodobratu=$basecode AND subkodobratu=3 AND md_d=0", defaultDB)[1][0];
            $majetokTodayInsert4 = Connection::InsertUpdateCreateDelete("INSERT INTO majetoktoday (obratid, destinacia, subjektid,kodobratu,uctovnykod,eqid,kodaktiva,ucetaktiva,jednotka,pocet,mena,md_d,obratdatetimereal,obratdatatimezauctovane) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,CURRENT_TIMESTAMP,?)", [
                $obratid,
                'dbequity',
                $subjektid,
                $basecode,
                $kodobratumddValues["uctovnykod"],
                $kodobratumddValues["equid"],
                $kodaktiva,
                $ucetaktiva,
                $mena,
                $vap_b,
                $mena,
                0,
                $today
            ], defaultDB);
            if (gettype($majetokTodayInsert4) !== "integer") {
                $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [majetoktoday4]. " . $majetokTodayInsert4;
            }
        }

        $obratid2 = Connection::getDataFromDatabase("SELECT nextval('majetok_today_obratid')", defaultDB)[1][0]["nextval"];

        $kodobratumddValues = Connection::getDataFromDatabase("SELECT uctovnykod, equid FROM kodobratumd_d WHERE kodobratu=" . ($basecode + 2) . " AND md_d=0", defaultDB)[1][0];
        $majetokTodayInsert5 = Connection::InsertUpdateCreateDelete("INSERT INTO majetoktoday (obratid, destinacia, subjektid,kodobratu,uctovnykod,eqid,kodaktiva,ucetaktiva,jednotka,pocet,mena,md_d,obratdatetimereal,obratdatatimezauctovane) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,CURRENT_TIMESTAMP,?)", [
            $obratid2,
            'dbequity',
            $subjektid,
            $basecode + 2,
            $kodobratumddValues["uctovnykod"],
            $kodobratumddValues["equid"],
            $kodaktiva,
            $ucetaktiva,
            $mena,
            0,
            $mena,
            0,
            $today
        ], defaultDB);
        if (gettype($majetokTodayInsert5) !== "integer") {
            $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [majetoktoday5]. " . $majetokTodayInsert4;
        }

        $kodobratumddValues = Connection::getDataFromDatabase("SELECT uctovnykod, equid FROM kodobratumd_d WHERE kodobratu=" . ($basecode + 2) . " AND subkodobratu=$subkod AND md_d=1", defaultDB)[1][0];
        $majetokTodayInsert6 = Connection::InsertUpdateCreateDelete("INSERT INTO majetoktoday (obratid, destinacia, subjektid,kodobratu,uctovnykod,eqid,kodaktiva,ucetaktiva,jednotka,pocet,mena,md_d,obratdatetimereal,obratdatatimezauctovane) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,CURRENT_TIMESTAMP,?)", [
            $obratid2,
            'dbequity',
            $subjektid,
            $basecode + 2,
            $kodobratumddValues["uctovnykod"],
            $kodobratumddValues["equid"],
            $kodaktiva,
            $ucetaktiva,
            $mena,
            $suma,
            $mena,
            1,
            $today
        ], defaultDB);
        if (gettype($majetokTodayInsert6) !== "integer") {
            $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [majetoktoday6]. " . $majetokTodayInsert4;
        }

        if ($subkod == 2) {
            $kodobratumddValues = Connection::getDataFromDatabase("SELECT uctovnykod, equid FROM kodobratumd_d WHERE kodobratu=" . ($basecode + 2) . " AND subkodobratu=3 AND md_d=1", defaultDB)[1][0];
            $majetokTodayInsert7 = Connection::InsertUpdateCreateDelete("INSERT INTO majetoktoday (obratid, destinacia, subjektid,kodobratu,uctovnykod,eqid,kodaktiva,ucetaktiva,jednotka,pocet,mena,md_d,obratdatetimereal,obratdatatimezauctovane) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,CURRENT_TIMESTAMP,?)", [
                $obratid2,
                'dbequity',
                $subjektid,
                $basecode + 2,
                $kodobratumddValues["uctovnykod"],
                $kodobratumddValues["equid"],
                $kodaktiva,
                $ucetaktiva,
                $mena,
                0,
                $mena,
                0,
                $today
            ], defaultDB);
            if (gettype($majetokTodayInsert7) !== "integer") {
                $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [majetoktoday7]. " . $majetokTodayInsert4;
            }
        }

        $splatenieInsert = Connection::InsertUpdateCreateDelete("INSERT INTO splatenie (dealid,tranza,subjektid,kodaktiva,mena,uctovnykod,pocet,suma,datum,fiktivne,datumvyplaty, datum_naroku) VALUES (?,?,?,?,?,?,?,?,?,?,?,?)", [
            $dealid,
            1,
            $subjektid,
            $kodaktiva,
            $mena,
            $uctovnykod,
            $pocet,
            $suma,
            $today,
            0,
            $today,
            $ocakdate
        ], defaultDB);
        if (gettype($splatenieInsert) !== "integer") {
            $errors[] = "DATABASE ERROR: [splatenieInsert]" . $splatenieInsert;
        }

        $splatenieObratidInsert = Connection::InsertUpdateCreateDelete("INSERT INTO splatenieobratid (dealid,obratid,tranza) VALUES (?,?,?)", [
            $dealid,
            $obratid,
            1
        ], defaultDB);
        if (gettype($splatenieObratidInsert) !== "integer") {
            $errors[] = "DATABASE ERROR: [splatenieObratidInsert]" . $splatenieObratidInsert;
        }

        $splatenieObratidInsert2 = Connection::InsertUpdateCreateDelete("INSERT INTO splatenieobratid (dealid,obratid,tranza) VALUES (?,?,?)", [
            $dealid,
            $obratid2,
            1
        ], defaultDB);
        if (gettype($splatenieObratidInsert2) !== "integer") {
            $errors[] = "DATABASE ERROR: [splatenieObratidInsert2]" . $splatenieObratidInsert2;
        }

        //TODO: Pridať permission checking. HINT: funckia hasActivityPermission() 
        // Automatické natipovanie platby
        $ss = 'null';
        $ks = 'null';
        $vs = $item->vs;
        if ($vs == "") {
            $vs = $dealid;
        }

        $id = Connection::getDataFromDatabase("SELECT nextval('s_obratybu')", defaultDB)[1][0]["nextval"];

        $obratyInsert = Connection::InsertUpdateCreateDelete("INSERT INTO obratybu (id,cub,cubpartnera,forma,ks,mena,nazpartnera,obratdatetime,ss,subjektid,suma,vs,krdb,logdatatimeactivity,logactivityid,loguserid) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)", [
            $id,
            $cub,
            null,
            0,
            $ks,
            $mena,
            null,
            $today,
            $ss,
            $subjektid,
            $suma,
            $vs,
            1,
            $today,
            1,
            $sess_userid
        ], defaultDB);
        if (gettype($obratyInsert) !== "integer") {
            $errors[] = "DATABASE ERROR: [obratyInsert]" . $obratyInsert;
        }

        $obratyInsert2 = Connection::InsertUpdateCreateDelete("INSERT INTO obratybuobratid (obratid, id, atribut) VALUES (?,?,?)", [
            $obratid2,
            $id,
            0
        ], defaultDB);
        if (gettype($obratyInsert2) !== "integer") {
            $errors[] = "DATABASE ERROR: [obratyInsert2]" . $obratyInsert2;
        }

        // Daň z kupónu
        if ($item->typ == "kupon") {
            $emitentstateid = Connection::getDataFromDatabase("SELECT emitentstateid FROM dbequity db, equityemitent e WHERE db.isin = '" . $item->isin . "' AND e.emitentid = db.emitentid", defaultDB)[1][0]["emitentstateid"];
            if (gettype($emitentstateid) !== "integer") {
                $errors[] = "DATABASE ERROR: [emitentstateid]" . $emitentstateid;
            }
            $danvynosyInsert = Connection::InsertUpdateCreateDelete("INSERT INTO danvynosy (dealid, destinacia, dansadzba, danzaklad, dan, mena, cub, stateid, vynoskus) VALUES (?,?,?,?,?,?,?,?,?)", [
                $dealid,
                'splatenie',
                $item->sadzba_dane,
                $item->zaklad_dane,
                $item->zaplatena_dan,
                $mena,
                $uctovnykod,
                $emitentstateid,
                $item->nakus
            ], defaultDB);
        }

        $poslednaSplatnost = false;
        $splatnostQuery = Connection::getDataFromDatabase("SELECT * FROM dbequity WHERE isin='" . $item->isin . "' AND maturitydate = '$ocakdate'", defaultDB)[1][0];
        if ($splatnostQuery !== "" || $splatnostQuery !== NULL) {
            $poslednaSplatnost = true;
        }

        if ($poslednaSplatnost) {
            $notification = new Notification(22, 'splatenie', $dealid, $item->typ === "istina" ? "dlhopisSplatenieNominalu" : "dlhopisSplatenieKuponu", $sess_userid, $username, $item->typ === "istina" ? "dlhopisSplatenieNominalu" : "dlhopisSplatenieKuponu", json_encode(["dealid", $dealid]), false, 0);
            $notifID = $notification->createNotifcation();

            $subactivity1 = new Subactivity($notifID, $item->typ === "istina" ? 21 : 20, 'splatenie', $dealid, 'dlhopisSplatenie' . $item->typ === "istina" ? "Nominalu" : "Kuponu", $sess_userid, json_encode(["dealid", $dealid]), $item->subjektid, "");
            $subactivity1->createSubActivity();

            $subactivity2 = new Subactivity($notifID, 1, 'obratybu', $item->subjektid, $item->typ === "istina" ? "dlhopisSplatenieNominalu" : "dlhopisSplatenieKuponu", $sess_userid, json_encode(["id", $id]), $item->subjektid, "");
            $subactivity2->createSubActivity();
        }
    }
}
if (
    empty($errors)
) {
    ?>
    <div id="toast-success"
        class="flex border-green-300 border items-center w-full max-w-sm p-4 mb-4 text-green-500 bg-white absolute right-5 rounded-lg shadow dark:text-gray-400 dark:bg-gray-800"
        role="alert">
        <div
            class="inline-flex items-center justify-center flex-shrink-0 w-8 h-8 text-green-500 bg-green-100 rounded-lg dark:bg-green-800 dark:text-green-200">
            <svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                viewBox="0 0 20 20">
                <path
                    d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 8.207-4 4a1 1 0 0 1-1.414 0l-2-2a1 1 0 0 1 1.414-1.414L9 10.586l3.293-3.293a1 1 0 0 1 1.414 1.414Z" />
            </svg>
            <span class="sr-only">Check icon</span>
        </div>
        <div class="ms-3 text-sm font-semibold">Operácia bola úspešne dokončená.</div>
        <button type="button"
            class="ms-auto -mx-1.5 -my-1.5 bg-white text-gray-400 hover:text-gray-900 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex items-center justify-center h-8 w-8 dark:text-gray-500 dark:hover:text-white dark:bg-gray-800 dark:hover:bg-gray-700"
            data-dismiss-target="#toast-success" aria-label="Close">
            <span class="sr-only">Close</span>
            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
            </svg>
        </button>
    </div>
    <script>
        setTimeout(() => {
            window.location.reload();
        }, 1000);
    </script>
<?php } else {
    echo "<pre>";
    print_r($errors);
    echo "</pre>";
    ?>
    <div id="toast-danger"
        class="flex items-center w-full max-w-sm p-4 mb-4 text-white absolute right-5 bg-red-500 rounded-lg shadow-lg"
        role="alert">
        <?php ?>
        <div
            class="inline-flex items-center justify-center flex-shrink-0 w-8 h-8 text-red-500 bg-red-100 rounded-lg dark:bg-red-800 dark:text-red-200">
            <svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                viewBox="0 0 20 20">
                <path
                    d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 11.793a1 1 0 1 1-1.414 1.414L10 11.414l-2.293 2.293a1 1 0 0 1-1.414-1.414L8.586 10 6.293 7.707a1 1 0 0 1 1.414-1.414L10 8.586l2.293-2.293a1 1 0 0 1 1.414 1.414L11.414 10l2.293 2.293Z" />
            </svg>
            <span class="sr-only">Error icon</span>
        </div>
        <div class="ms-3 text-sm font-bold">Investičný zámer sa nepodarilo vytvoriť kvôli neznámej chybe!</div>
        <button type="button"
            class="ms-auto -mx-1.5 -my-1.5 text-white hover:text-gray-900 transition-all rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex items-center justify-center h-8 w-8 dark:text-gray-500 dark:hover:text-white dark:bg-gray-800 dark:hover:bg-gray-700"
            data-dismiss-target="#toast-danger" aria-label="Close">
            <span class="sr-only">Close</span>
            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
            </svg>
        </button>
    </div>
    <script>
        $("#submitter span").html("Potvrdiť");
        $("#submitter svg").css("display", "none");
        setTimeout(() => {
            $("#toast-danger").fadeOut();
        }, 2500);
    </script>
<?php }
?>
<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

$searchQuery = $_POST["query"];
$action = $_POST["action"];
$object = $_POST["object"];

$query = "SELECT DISTINCT dcr.isincurrric,
       d.isinreal,
       dcr.isincurrric as isincurrric,
       d.cpnaz as cpnaz,
       d.cpnazskratka,
       dc.currencytrade as mena
from dbequity d,
     dbequitycurr dc,
     dbequitycurrric dcr,
     sanctionlist s
where";
if ($searchQuery !== "") {
    $query .= "(SIMILARITY(isinreal,'%$searchQuery%') > 0.2 OR SIMILARITY(cpnaz,'%$searchQuery%') > 0.2 OR SIMILARITY(cpnazskratka,'%$searchQuery%') > 0.2) and ";
}
$query .= " dc.isin = d.isin
  and d.eqid = 'Bonds'
  and dcr.isincurr = dc.isincurr
  and s.isin IS DISTINCT FROM d.isin
  and d.maturitydate >= (select max(datum) from today)
order by d.isinreal, d.cpnaz, d.cpnazskratka, dc.currencytrade;";
$dlhopisy = Connection::getDataFromDatabase($query, defaultDB)[1];
if ($dlhopisy[0] === NULL) {
    echo '<div class="p-4 my-4 text-sm text-blue-800 rounded-lg bg-blue-50 w-full dark:bg-gray-700 dark:text-blue-400"
                    role="alert">
                    <span class="font-bold">Žiadne záznamy!</span> Podľa zadaných kritérii sme nenašli žiadne dáta
                </div>';
    exit;
}

foreach ($dlhopisy as $dlhopis) { ?>
    <tr hx-get="/dlhopisy/<?php echo $object; ?>/<?php echo $action; ?>/<?php echo $dlhopis["isincurrric"]; ?>" hx-target="#pageContentMain"
        hx-replace-url="true"
        class="bg-white border-b transition-all cursor-pointer dark:bg-gray-800 dark:border-gray-700 border-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600">
        <th scope="row" class="px-6 py-4 font-bold text-gray-900 whitespace-nowrap dark:text-white">
            <?php echo $dlhopis["isinreal"]; ?>
        </th>
        <th scope="row" class="px-6 py-4 font-bold text-gray-900 whssitespace-nowrap dark:text-white">
            <?php echo $dlhopis["cpnaz"]; ?>
        </th>
        <td class="px-6 py-4">
            <?php echo $dlhopis["cpnazskratka"]; ?>
        </td>
        <td class="px-6 py-4">
            <?php echo $dlhopis["mena"]; ?>
        </td>
        <td class="px-6 py-4">
            <button>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                    stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide lucide-log-in">
                    <path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4" />
                    <polyline points="10 17 15 12 10 7" />
                    <line x1="15" x2="3" y1="12" y2="12" />
                </svg>
            </button>
        </td>
    </tr>
<?php } ?>
<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
require_once "/home/<USER>/www/src/lib/notifications/Notification.class.php";
require_once "/home/<USER>/www/src/lib/notifications/Subactivity.class.php";
require_once "/home/<USER>/www/src/lib/Payments/PaymentsProcessor.class.php";

if (isset($_SESSION["client"])) {
    $fondid = $_SESSION["client"]["fondid"];
} else if ($_SESSION["mode"]["mode"] === "admin") {
    $fondid = 1;
} else {
    $fondid = 0;
}

$suma = $_POST["amount"];
$ucet = $_POST["cub"];
$datumZadania = $_POST["datum"];

$sess_userid = $_SESSION["user"]["data"]["userid"];
$username = $_SESSION["user"]["data"]["username"];

if ($fondid === 1) {
    $mena = Connection::getDataFromDatabase("SELECT mena FROM spravcabu WHERE cub = '$ucet' AND spravcaid = $fondid", defaultDB)[1][0]["mena"];
} else {
    $mena = Connection::getDataFromDatabase("SELECT mena FROM fondsbu WHERE cub = '$ucet' AND fondid = $fondid", defaultDB)[1][0]["mena"];
}
echo "MENA: $ucet";
$dattimereal = date("Y-m-d");
$dealid1 = Connection::getDataFromDatabase("SELECT nextval('s_konfirmaciapp')", defaultDB)[1][0]["nextval"];

$queryPool[] = [
    "query" => "INSERT INTO konfirmaciapp
    (dealid, subjektid, dealid_related, druhobchodu,
	 suma, mena, ucet,externy_ucet,datum_zauctovania,
	 logactivityid,loguserid, logdatatimeactivity
	) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
    "params" => [
        $dealid1 . "912",
        $fondid,
        null,
        'vyber',
        $suma,
        $mena,
        $ucet,
        null,
        $datumZadania,
        1,
        $sess_userid,
        $dattimereal
    ],
    "db" => defaultDB,
    "name" => "insert into konfirmaciapp"
];


$queryPool[] = [
    "query" => "INSERT INTO rezervacia
    (dealid, destinacia, subjektid, eqid, kodaktiva, ucetaktiva, jednotka, mena, pocet, obratdatetime)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
    "params" => [
        $dealid1 . "912",
        'konfirmaciapp',
        $fondid,
        'BU',
        $mena,
        $ucet,
        $mena,
        $mena,
        $suma,
        $dattimereal
    ],
    "db" => defaultDB,
    "name" => "insert into rezervacia"
];

$transaction = Connection::runTransaction(defaultDB, null, $queryPool);
if (str_contains($transaction, "poriadku")) {
    $notification = new Notification(
        12,
        'konfirmaciapp',
        $dealid1 . "912",
        'vyberProstriedkov',
        $sess_userid,
        $username,
        "vyberProstriedkov",
        json_encode(["dealid", $dealid1 . "912"]),
        false,
        $fondid,
        "user",
        "Vyber prostriedkov z účtu <strong>" . $ucet . '</strong> so sumou <strong>' . number_format($suma, 2, ".", " ") . " " . $mena . '</strong>'
    );
    $notification->createNotifcation();
    ?>
    <script>
        document.body.style.overflow = "auto";
        htmx.ajax('GET', "/vyber-prostriedkov", {
            target: "#pageContentMain",
        });
    </script>
<?php } else {
    echo "ERROR: $transaction";
}
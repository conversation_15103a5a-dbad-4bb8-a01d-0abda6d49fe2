<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
require_once "/home/<USER>/www/src/lib/notifications/Notification.class.php";
require_once "/home/<USER>/www/src/lib/notifications/Subactivity.class.php";
require_once "/home/<USER>/www/src/lib/Payments/PaymentsProcessor.class.php";

if (isset($_SESSION["client"])) {
    $fondid = $_SESSION["client"]["fondid"];
} else if ($_SESSION["mode"]["mode"] === "admin") {
    $fondid = 1;
} else {
    $fondid = 0;
}

$sess_userid = $_SESSION["user"]["data"]["userid"];
$username = $_SESSION["user"]["data"]["username"];
$queryPool = [];

$presun = $_POST["amount"];
$fondidd = $_POST["secondParam"];
$mena = $_POST["mena"];
$cub = $_POST["cub"];

$ucetDetail = Connection::getDataFromDatabase(
    "SELECT DISTINCT fb.cub as naucet FROM fondsbu fb WHERE fb.fondid = '$fondidd' AND fb.mena = '$mena' AND fb.cub = '$cub'",
    defaultDB
)[1][0];

$naucet = $ucetDetail["naucet"];

if ($naucet == '' || $naucet == NULL) {
    ?>
    <script>
        alert("Cieľový klient nemá účet v danej mene");
    </script>
    <?php
    exit;
}

$datum = $_POST["datum"];
$datetimereal = date("Y-m-d");

$dealid1 = Connection::getDataFromDatabase("SELECT nextval('s_konfirmaciapp')", defaultDB)[1][0]["nextval"] . "912";
$dealid2 = Connection::getDataFromDatabase("SELECT nextval('s_konfirmaciapp')", defaultDB)[1][0]["nextval"] . "912";

$queryPool[] = [
    "query" => "INSERT INTO konfirmaciapp
    (dealid, subjektid, dealid_related, druhobchodu,
	 suma, mena, ucet,externy_ucet,datum_prevodu,
	 logactivityid,loguserid, logdatatimeactivity
	)
	values
	($dealid1,$fondid,$dealid2,'prevod',
	 $presun, '$mena', '$cub',null,'$datum',
	 1,$sess_userid, '$datetimereal'
	)",
    "params" => [],
    "db" => defaultDB,
    "name" => "insert into konfirmaciapp 1"
];

$queryPool[] = [
    "query" => "INSERT INTO konfirmaciapp
    (dealid, subjektid, dealid_related, druhobchodu,
	 suma, mena, ucet,externy_ucet,datum_prevodu,
	 logactivityid,loguserid, logdatatimeactivity
	)
	values
	($dealid2,$fondidd,null,'prevod',
	 $presun, '$mena', '$naucet',null,'$datum',
	 1,$sess_userid, '$datetimereal'
	)",
    "params" => [],
    "db" => defaultDB,
    "name" => "insert into konfirmaciapp 2"
];

$transaction = Connection::runTransaction(defaultDB, null, $queryPool);
if (str_contains($transaction, "poriadku")) {
    $notification = new Notification(
        12,
        'konfirmaciapp',
        $dealid1,
        'prevodProstriedkov',
        $sess_userid,
        $username,
        "prevodProstriedkov",
        json_encode(["dealid", $dealid1]),
        false,
        $fondid,
        "user",
        "Prevod prostriedkov z účtu <strong>" . $cub . '</strong> na účet <strong>' . $naucet . '</strong> so sumou <strong>' . number_format($presun, 2, ".", " ") . " " . $mena . '</strong>'
    );
    $notification->createNotifcation();

    $notification = new Notification(
        12,
        'konfirmaciapp',
        $dealid2,
        'prevodProstriedkov',
        $sess_userid,
        $username,
        "prevodProstriedkov",
        json_encode(["dealid", $dealid2]),
        false,
        $fondid,
        "user",
        "Prevod prostriedkov z účtu <strong>" . $cub . '</strong> na účet <strong>' . $naucet . '</strong> so sumou <strong>' . number_format($presun, 2, ".", " ") . " " . $mena . '</strong>'
    );
    $notification->createNotifcation();

    ?>
    <script>
        document.body.style.overflow = "auto";
        htmx.ajax('GET', "/prevod-prostriedkov", {
            target: "#pageContentMain",
        });
    </script>
    <?php
} else {
    echo "ERROR: $transaction";
}
<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
require_once "/home/<USER>/www/src/lib/notifications/Notification.class.php";
require_once "/home/<USER>/www/src/lib/notifications/Subactivity.class.php";
require_once "/home/<USER>/www/src/lib/Payments/PaymentsProcessor.class.php";

$amount = $_POST["amount"];
$cub = $_POST["cub"];
$cubpartnera = $_POST["cubpartnera"];
$queryPool = [];

$sess_userid = $_SESSION["user"]["data"]["userid"];
$username = $_SESSION["user"]["data"]["username"];

if (isset($_SESSION["client"])) {
    $fondid = $_SESSION["client"]["fondid"];
} else if ($_SESSION["mode"]["mode"] === "admin") {
    $fondid = 1;
} else {
    $fondid = 0;
}

if ($amount === "" || $amount === NULL || $amount === 0) {
    $error = "Čiastka nemôže byť prázdna. Prosím zadajte valídne číslo.";
}

if ($fondid === 1) {
    $detail = Connection::getDataFromDatabase("SELECT datum, '1111111111' as cislozmluvy FROM today WHERE fondid = $fondid", defaultDB)[1][0];
} else {
    $detail = Connection::getDataFromDatabase("SELECT datum, p.cislozmluvy FROM today t, portfolio p WHERE t.fondid = $fondid AND p.fondid = t.fondid", defaultDB)[1][0];
}

$today = $detail["datum"];
$cislozmluvy = $detail["cislozmluvy"];

if ($fondid === 1) {
    $mena = Connection::getDataFromDatabase("SELECT mena FROM spravcabu WHERE cub = '$cub' AND spravcaid = $fondid", defaultDB)[1][0]["mena"];
} else {
    $mena = Connection::getDataFromDatabase("SELECT mena FROM fondsbu WHERE cub = '$cub' AND fondid = $fondid", defaultDB)[1][0]["mena"];
}

$dattimereal = date("Y-m-d");

$dealid = Connection::getDataFromDatabase("SELECT nextval('s_konfirmaciapp')", defaultDB)[1][0]["nextval"] . "912";

$queryPool[] = [
    "query" => "INSERT INTO konfirmaciapp
    (dealid, subjektid, dealid_related, druhobchodu,
	 suma, mena, ucet,externy_ucet,datum_zauctovania,
	 logactivityid,loguserid, logdatatimeactivity
	) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
    "params" => [
        $dealid,
        $fondid,
        null,
        'vklad',
        $amount,
        $mena,
        $cub,
        $cubpartnera,
        $today,
        12,
        $sess_userid,
        $dattimereal
    ],
    "db" => defaultDB,
    "name" => "insert into konfirmaciapp"
];
$year = gmdate("y");

$queryPool[] = [
    "query" => "INSERT INTO dennikpm
    (
			userid,
			dealid,
			destinacia,
			poradovecislo,
			cislo
		)
		values
		(
			$sess_userid,
			" . $dealid . ",
			'konfirmaciapp',
			(
				select COALESCE(max(poradovecislo),0) + 1
				from dennikpm
				where userid=$sess_userid
			),
				(select COALESCE(max(poradovecislo),0) + 1
				from dennikpm
				where userid=$sess_userid
				)
			|| lpad('$year',2,'0') || lpad((
							select agentid
							from users
							where userid=$sess_userid
							)::text,4,'0')
		)",
    "params" => [],
    "db" => defaultDB,
    "name" => "insert into dennikpm"
];

$queryPool[] = [
    "query" => "INSERT INTO majetokcesta
        (dealid,tranza,in_out,sparovanie,destinacia,
		 	 subjektid,eqid,kodaktiva,ucetaktiva,jednotka,
		 	 mena,pocet,cestafrom,cestatill,popis)
        VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)",
    "params" => [
        $dealid,
        0,
        1,
        $dealid,
        'konfirmaciapp',
        $fondid,
        'BU',
        $mena,
        $cub,
        $mena,
        $mena,
        $amount,
        $today,
        $today,
        'vklad pp'
    ],
    "db" => defaultDB,
    "name" => "insert into majetokcesta"
];

//NATIPOVANIE PLATBY
$ss = $dealid;
$vs = $cislozmluvy;
$id = Connection::getDataFromDatabase("SELECT nextval('s_obratybu')", defaultDB)[1][0]["nextval"];

$queryPool[] = [
    "query" => "INSERT INTO obratybu
        (id,cub,cubpartnera,forma,
				 ks,mena,nazpartnera,obratdatetime,
				 ss,subjektid,suma,vs,krdb,
				 logdatatimeactivity,logactivityid,loguserid
				) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)",
    "params" => [
        $id,
        $cub,
        $cubpartnera,
        0,
        $ks,
        $mena,
        "vkladanie",
        $today,
        $ss,
        $fondid,
        $amount,
        $vs,
        1,
        $dattimereal,
        1,
        $sess_userid
    ],
    "db" => defaultDB,
    "name" => "insert into obratybu"
];

$transaction = Connection::runTransaction(defaultDB, null, $queryPool);
if (str_contains($transaction, "poriadku")) {
    $notification = new Notification(
        12,
        'konfirmaciapp',
        $dealid,
        'vlozitProstriedky',
        $sess_userid,
        $username,
        "vlozitProstriedky",
        json_encode(["dealid", $dealid]),
        false,
        $fondid,
        "user",
        "Vklad prostriedkov na účet <strong>" . $cub . '</strong> so sumou <strong>' . number_format($amount, 2, ".", " ") . " " . $mena . '</strong>'
    );
    $notifID = $notification->createNotifcation();

    $subactivity = new Subactivity(
        $notifID,
        1,
        'konfirmaciapp',
        $dealid,
        'vlozitProstriedky',
        $sess_userid,
        json_encode(["dealid", $dealid]),
        $fondid,
        "Natipovanie vkladu prostriedkov na účet <strong>" . $cub . '</strong> so sumou <strong>' . number_format($amount, 2, ".", " ") . " " . $mena . '</strong>'
    );
    $subactivity->createSubActivity();

    //SPRACOVANIE PLATIEB a.k.a. spracuj_platby.php
    $processor = new PaymentProcessor();
    $result = $processor->processIncomingPayments($fondid, 1, $id);
    if ($result["paymentId"] == $id) { ?>
        <div class="p-4 m-5 text-sm text-green-800 rounded-lg bg-green-50 dark:bg-gray-900 dark:text-green-400" role="alert">
            <span class="font-bold">Zámer na vloženie peňažných prostriedkov bol úspešne zrealizovaný!</span>
            <span hx-get="/vysporiadanie/penazne/prichadzajuce/sparovanie/uhrada?ucet=<?php echo $cub; ?>&mena=<?php echo $mena; ?>" hx-target="#pageContentMain" hx-push-url="true"
                hx-replace-url="true" class="underline cursor-pointer hover:no-underline">Ihneď spárovať platbu</span>
        </div>
    <?php } else { ?>
        <div id="alert-2"
            class="flex items-center p-4 mb-4 text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400"
            role="alert">
            <svg class="shrink-0 w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                viewBox="0 0 20 20">
                <path
                    d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z" />
            </svg>
            <span class="sr-only">Info</span>
            <div class="ms-3 text-sm font-medium">
                Vyskytla sa chyba! Detail: <?php print_r($result); ?>
            </div>
            <button type="button"
                class="ms-auto -mx-1.5 -my-1.5 bg-red-50 text-red-500 rounded-lg focus:ring-2 focus:ring-red-400 p-1.5 hover:bg-red-200 inline-flex items-center justify-center h-8 w-8 dark:bg-gray-800 dark:text-red-400 dark:hover:bg-gray-700"
                data-dismiss-target="#alert-2" aria-label="Close">
                <span class="sr-only">Close</span>
                <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                </svg>
            </button>
        </div>
    <?php }
} else { ?>
    <div id="alert-2"
        class="flex items-center p-4 mb-4 text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400"
        role="alert">
        <svg class="shrink-0 w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
            viewBox="0 0 20 20">
            <path
                d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z" />
        </svg>
        <span class="sr-only">Info</span>
        <div class="ms-3 text-sm font-medium">
            Vyskytla sa chyba! Detail: <?php print_r($result); ?>
        </div>
        <button type="button"
            class="ms-auto -mx-1.5 -my-1.5 bg-red-50 text-red-500 rounded-lg focus:ring-2 focus:ring-red-400 p-1.5 hover:bg-red-200 inline-flex items-center justify-center h-8 w-8 dark:bg-gray-800 dark:text-red-400 dark:hover:bg-gray-700"
            data-dismiss-target="#alert-2" aria-label="Close">
            <span class="sr-only">Close</span>
            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
            </svg>
        </button>
    </div>
<?php }
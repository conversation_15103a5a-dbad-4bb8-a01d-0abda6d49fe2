<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
require_once "/home/<USER>/www/src/lib/notifications/Notification.class.php";
require_once "/home/<USER>/www/src/lib/notifications/Subactivity.class.php";
require_once "/home/<USER>/www/src/lib/Payments/PaymentsProcessor.class.php";

if (isset($_SESSION["client"])) {
    $fondid = $_SESSION["client"]["fondid"];
} else if ($_SESSION["mode"]["mode"] === "admin") {
    $fondid = 1;
} else {
    $fondid = 0;
}

$sess_userid = $_SESSION["user"]["data"]["userid"];
$username = $_SESSION["user"]["data"]["username"];

$dealid = $_POST["dealid"];
$dealid_related = $_POST["dealid_related"];
$dealids = [$dealid, $dealid_related];
$dealidsText = implode(",", $dealids);
$suma = $_POST["suma"];
$mena = $_POST["mena"];
$ucet_zdroj = $_POST["ucet_zdroj"];
$ucet_ciel = $_POST["ucet_ciel"];
$externy_ucet = $_POST["externy_ucet"];
$datum_prevodu = $_POST["datum_prevodu"];
$na_id_fond = $_POST["na_id_fond"];
$year = gmdate("y");
$queryPool = [];

$obratID = Connection::getDataFromDatabase("SELECT nextval('majetok_today_obratid')", defaultDB)[1][0]["nextval"];

$queryPool[] = [
    "query" => "UPDATE konfirmaciapp SET logactivityid = 12, datum_zauctovania = ? WHERE dealid IN ($dealidsText)",
    "params" => [$datum_prevodu],
    "db" => defaultDB,
    "name" => "Update konfirmaciapp"
];

$queryPool[] = [
    "query" => "INSERT INTO majetoktoday
    (obratid,destinacia,subjektid,kodobratu,
				 	uctovnykod,eqid,kodaktiva,ucetaktiva,jednotka,
				 	pocet,mena,md_d,obratdatetimereal,obratdatatimezauctovane) 
				select 
				 	$obratID,'konfirmaciapp',$fondid,279,uctovnykod, equid,'" . $mena . "','" . $ucet_zdroj . "',
				 	'" . $mena . "'," . $suma . ",'" . $mena . "',md_d,CURRENT_DATE,(select datum from today where fondid = $fondid)
				 	from kodobratumd_d where kodobratu=279 and md_d=1 and subkodobratu in (0,1)
    ",
    "params" => [],
    "db" => defaultDB,
    "name" => "Insert into majetoktoday"
];

$queryPool[] = [
    "query" => "INSERT INTO majetoktoday
    (destinacia,eqid,jednotka,
				kodaktiva,kodobratu,
				md_d,mena,
				obratdatatimezauctovane,obratdatetimereal,obratid,
				pocet,subjektid,
				ucetaktiva,uctovnykod) 
			SELECT
				'konfirmaciapp',equid,'" . $mena . "',
				'" . $mena . "', 274,
				md_d,'" . $mena . "', 
				(select datum from today where fondid = " . $na_id_fond . "), CURRENT_DATE,$obratID,
				" . $suma . "," . $na_id_fond . ",
				'" . $ucet_ciel . "', uctovnykod
			FROM
				kodobratumd_d
			WHERE
				kodobratu = 274 and md_d = 0 ",
    "params" => [],
    "db" => defaultDB,
    "name" => "Insert into majetoktoday 2"
];

$queryPool[] = [
    "query" => "INSERT INTO konfirmaciappobratid (dealid, obratid) VALUES (?,?)",
    "params" => [$dealid, $obratID],
    "db" => defaultDB,
    "name" => "Insert into konfirmaciappobratid"
];

$queryPool[] = [
    "query" => "WITH nextval AS (
        SELECT COALESCE(MAX(poradovecislo), 0) + 1 AS next_p FROM dennikpm WHERE userid = $sess_userid
        )
        INSERT INTO dennikpm (userid, dealid, destinacia, poradovecislo, cislo)
        SELECT
        $sess_userid,
        $dealid,
        'konfirmaciapp',
        next_p,
        next_p || LPAD('$year', 2, '0') || LPAD(agentid::text, 4, '0')
        FROM nextval, users
        WHERE users.userid = $sess_userid;
        ",
    "params" => [],
    "db" => defaultDB,
    "name" => "Insert into dennikpm"
];

$transaction = Connection::runTransaction(defaultDB, null, $queryPool);
if (str_contains($transaction, "poriadku")) {
    $notification = new Notification(
        12,
        'konfirmaciapp',
        $dealid,
        'potvrdPrevodProstriedkov',
        $sess_userid,
        $username,
        "potvrdPrevodProstriedkov",
        json_encode(["dealid", $dealid]),
        false,
        $fondid,
        "user",
        "Potvrdenie prevodu prostriedkov z účtu <strong>" . $ucet_zdroj . '</strong> na účet <strong>' . $ucet_ciel . '</strong> so sumou <strong>' . number_format($suma, 2, ".", " ") . " " . $mena . '</strong>'
    );
    $notification->createNotifcation();
    ?>
    <script>
        document.body.style.overflow = "auto";
        htmx.ajax('GET', "/prevod-prostriedkov", {
            target: "#pageContentMain",
        });
    </script>
<?php } else {
    echo "ERROR: $transaction";
}
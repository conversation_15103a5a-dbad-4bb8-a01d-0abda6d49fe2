<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
require_once "/home/<USER>/www/src/lib/notifications/Notification.class.php";
require_once "/home/<USER>/www/src/lib/notifications/Subactivity.class.php";
require_once "/home/<USER>/www/src/lib/Payments/PaymentsProcessor.class.php";

if (isset($_SESSION["client"])) {
    $fondid = $_SESSION["client"]["fondid"];
} else if ($_SESSION["mode"]["mode"] === "admin") {
    $fondid = 1;
} else {
    $fondid = 0;
}

$sess_userid = $_SESSION["user"]["data"]["userid"];
$username = $_SESSION["user"]["data"]["username"];

$dealid = $_POST["dealid"];
$suma = $_POST["suma"];
$mena = $_POST["mena"];
$ucet_ciel = $_POST["ucet_zdroj"];
$externy_ucet = $_POST["externy_ucet"];
$datum_prevodu = $_POST["datum_prevodu"];
$year = gmdate("y");
$queryPool = [];

$obratID = Connection::getDataFromDatabase("SELECT nextval('majetok_today_obratid')", defaultDB)[1][0]["nextval"];

$queryPool[] = [
    "query" => "UPDATE konfirmaciapp SET logactivityid = 12, externy_ucet = ?, datum_zauctovania = ? WHERE dealid = ?",
    "params" => [$ucet_ciel, $datum_prevodu, $dealid],
    "db" => defaultDB,
    "name" => "Update konfirmaciapp"
];

$queryPool[] = [
    "query" => "INSERT INTO majetoktoday (obratid,destinacia,subjektid,kodobratu,
				 	uctovnykod,eqid,kodaktiva,ucetaktiva,jednotka,
				 	pocet,mena,md_d,obratdatetimereal,obratdatatimezauctovane) select 
				 	$obratID,'konfirmaciapp',$fondid,279,uctovnykod, equid,'" . $mena . "','" . $ucet_ciel . "',
				 	'" . $mena . "'," . $suma . ",'" . $mena . "',md_d,CURRENT_DATE,(select datum from today where fondid = $fondid)
				 	from kodobratumd_d where kodobratu=279 and md_d=1 and subkodobratu in (0,1)",
    "params" => [],
    "db" => defaultDB,
    "name" => "Insert into majetoktoday"
];

$queryPool[] = [
    "query" => "INSERT INTO konfirmaciappobratid (dealid, obratid) VALUES (?,?)",
    "params" => [$dealid, $obratID],
    "db" => defaultDB,
    "name" => "Insert into konfirmaciappobratid"
];

$queryPool[] = [
    "query" => "DELETE FROM rezervacia WHERE dealid = ?",
    "params" => [$dealid],
    "db" => defaultDB,
    "name" => "Delete from rezervacia"
];

$queryPool[] = [
    "query" => "WITH nextval AS (
        SELECT COALESCE(MAX(poradovecislo), 0) + 1 AS next_p FROM dennikpm WHERE userid = $sess_userid
        )
        INSERT INTO dennikpm (userid, dealid, destinacia, poradovecislo, cislo)
        SELECT
        $sess_userid,
        $dealid,
        'konfirmaciapp',
        next_p,
        next_p || LPAD('$year', 2, '0') || LPAD(agentid::text, 4, '0')
        FROM nextval, users
        WHERE users.userid = $sess_userid;
        ",
    "params" => [],
    "db" => defaultDB,
    "name" => "Insert into dennikpm"
];

$transaction = Connection::runTransaction(defaultDB, null, $queryPool);
if (str_contains($transaction, "poriadku")) {
    $notification = new Notification(
        12,
        'konfirmaciapp',
        $dealid,
        'potvrditVyberProstriedkov',
        $sess_userid,
        $username,
        "potvrditVyberProstriedkov",
        json_encode(["dealid", $dealid]),
        false,
        $fondid,
        "user",
        "Potvrdenie vyberu prostriedkov z účtu <strong>" . $ucet_ciel . '</strong> so sumou <strong>' . number_format($suma, 2, ".", " ") . " " . $mena . '</strong>'
    );
    $notification->createNotifcation();
    ?>
    <script>
        document.body.style.overflow = "auto";
        $(".actionSpinner").css("display", "none");
        $(".actionIcon").css("display", "inline-flex");
        htmx.ajax('GET', "/vyber-prostriedkov", {
            target: "#pageContentMain",
        });
    </script>
<?php } else {
    echo "ERROR: $transaction";
}
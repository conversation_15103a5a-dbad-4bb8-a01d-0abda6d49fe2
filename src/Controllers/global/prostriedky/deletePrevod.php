<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
require_once "/home/<USER>/www/src/lib/notifications/Notification.class.php";
require_once "/home/<USER>/www/src/lib/notifications/Subactivity.class.php";
require_once "/home/<USER>/www/src/lib/Payments/PaymentsProcessor.class.php";

if (isset($_SESSION["client"])) {
    $fondid = $_SESSION["client"]["fondid"];
} else if ($_SESSION["mode"]["mode"] === "admin") {
    $fondid = 1;
} else {
    $fondid = 0;
}

$sess_userid = $_SESSION["user"]["data"]["userid"];
$username = $_SESSION["user"]["data"]["username"];

$dealid = $_POST["dealid"];
$suma = $_POST["suma"];
$mena = $_POST["mena"];
$ucet_ciel = $_POST["ucet_zdroj"];
$datum_prevodu = $_POST["datum_prevodu"];
$queryPool = [];

print_r($_POST);

$queryPool[] = [
    "query" => "UPDATE konfirmaciapp SET logactivityid = 3 WHERE dealid = ?",
    "params" => [$dealid],
    "db" => defaultDB,
    "name" => "Update konfirmaciapp"
];

$queryPool[] = [
    "query" => "DELETE FROM rezervacia WHERE dealid = ?",
    "params" => [$dealid],
    "db" => defaultDB,
    "name" => "Delete from rezervacia"
];

$transaction = Connection::runTransaction(defaultDB, null, $queryPool);
if (str_contains($transaction, "poriadku")) {
    $notification = new Notification(
        12,
        'konfirmaciapp',
        $dealid,
        'deletePrevodProstriedkov',
        $sess_userid,
        $username,
        "deletePrevodProstriedkov",
        json_encode(["dealid", $dealid]),
        false,
        $fondid,
        "user",
        "Zrušenie prevodu prostriedkov z účtu <strong>" . $ucet_ciel . '</strong> so sumou <strong>' . number_format($suma, 2, ".", " ") . " " . $mena . '</strong>'
    );
    $notification->createNotifcation();
    ?>
    <script>
        document.body.style.overflow = "auto";
        $(".actionSpinner").css("display", "none");
        $(".actionIcon").css("display", "inline-flex");
        htmx.ajax('GET', "/prevod-prostriedkov", {
            target: "#pageContentMain",
        });
    </script>
<?php } else {
    echo "ERROR: $transaction";
}
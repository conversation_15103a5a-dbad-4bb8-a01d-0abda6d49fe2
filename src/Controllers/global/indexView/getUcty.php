<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

$query = "SELECT
		cislouctu
	from
	(
			select
			cutd as cislouctu
			from konfirmaciaktv
			where
			subjektid NOT IN (0) and
			logactivityid in (17,18,19,20,21,22)
		UNION ALL
			select
			ucetaktiva as cislouctu
			from majetoktoday mt, konverzia k
			where
			mt.subjektid NOT IN (0) and
			mt.subjektid = k.subjektid and
			'NDF-'||mt.kodaktiva = 'NDF-'||k.dealid and
			mena='SKK' and
			uctovnykod in (315181,325181) and
			logactivityid=12 and
			typ_konverzie = 1
		UNION ALL
			select
			ucetaktiva as cislouctu
			from majetoktoday
			where
			subjektid NOT IN (0) and
			uctovnykod in (325413,325411,325412,325422,325421,325435,325437,325521,325522,325143,325443)
		UNION ALL
			select
			ucetaktiva as cislouctu
			from majetoktoday
			where
			subjektid NOT IN (0) and
			uctovnykod in (325210)
		UNION ALL
			select
			ucetaktiva as cislouctu
			from majetoktoday
			where
			subjektid NOT IN (0) and
			uctovnykod in (325300,325350)
		UNION ALL
			select
			ucetaktiva as cislouctu
			from majetoktoday
			where
			subjektid NOT IN (0) and
			uctovnykod in (325700)
		UNION ALL
			select
			ucetaktiva as cislouctu
			from majetoktoday
			where
			subjektid NOT IN (0) and
			uctovnykod in ( 221110,221210,251110,251200,251300,251400)
		UNION ALL
			select
			ucetaktiva as cislouctu
			from majetokcesta ma
			where subjektid NOT IN (0) and
			 in_out=1
		UNION ALL
			select
			ma.ucetaktiva as cislouctu
			from majetokcesta ma, today t, pool p, pooldetailreal pdr
			where ma.subjektid = 0 and 
      			pdr.subjektid NOT IN (0) and
			t.fondid = ma.subjektid and
			ma.cestatill >=t.datum and
			ma.in_out=1 and 
			(ma.destinacia = p.destinacia or (ma.destinacia = 'rekonfirmaciacp' and p.destinacia = 'konfirmaciacp'))  
			and ma.dealid = p.dealid 
			and p.poolid = pdr.poolid
			and ma.eqid in ('Bonds', 'Shares', 'Fonds')
		UNION ALL
			select
			ma.ucetaktiva as cislouctu
			from majetokcesta ma, today t, pool p, pooldetailreal pdr
			where ma.subjektid = 0 and 
      			pdr.subjektid NOT IN (0) and
			t.fondid = ma.subjektid and
			ma.cestatill >=t.datum and
			ma.in_out=1 and 
			(ma.destinacia = p.destinacia or (ma.destinacia = 'rekonfirmaciacp' and p.destinacia = 'konfirmaciacp'))  
			and ma.dealid = p.dealid 
			and p.poolid = pdr.poolid
			and ma.eqid in ('BU')
			and ma.tranza = 1
		UNION ALL
			select
			ma.ucetaktiva as cislouctu
			from majetokcesta ma, today t, pool p, pooldetailreal pdr
			where ma.subjektid = 0 and 
      			pdr.subjektid NOT IN (0) and
			t.fondid = ma.subjektid and
			ma.cestatill >=t.datum and
			ma.in_out=1 and 
			(ma.destinacia = p.destinacia or (ma.destinacia = 'rekonfirmaciacp' and p.destinacia = 'konfirmaciacp'))  
			and ma.dealid = p.dealid 
			and p.poolid = pdr.poolid
			and ma.eqid in ('BU')
			and ma.tranza = 2
      		UNION ALL
			select
			ucetaktiva as cislouctu
			from majetokcesta ma
			where subjektid NOT IN (0) and
			 in_out=0
		UNION ALL
			select 
       		ma.ucetaktiva as cislouctu
			from majetokcesta ma, pool p, pooldetailreal pdr
			where 
			ma.subjektid = 0 and
			ma.in_out=0 and 
			(ma.destinacia = p.destinacia or (ma.destinacia = 'rekonfirmaciacp' and p.destinacia = 'konfirmaciacp'))  
			and ma.dealid = p.dealid and 
			p.poolid = pdr.poolid and
			pdr.subjektid NOT IN (0)
			and ma.eqid in ('Bonds', 'Shares', 'Fonds')
		UNION ALL
			select 
       		ma.ucetaktiva as cislouctu
			from majetokcesta ma, pool p, pooldetailreal pdr
			where 
			ma.subjektid = 0 and
			ma.in_out=0 and 
			(ma.destinacia = p.destinacia or (ma.destinacia = 'rekonfirmaciacp' and p.destinacia = 'konfirmaciacp'))  
			and ma.dealid = p.dealid and 
			p.poolid = pdr.poolid and
			pdr.subjektid NOT IN (0)
			and ma.eqid in ('BU')
		UNION ALL
			select
			ucetaktiva as cislouctu
			from rezervacia
			where subjektid NOT IN (0)
				and destinacia <> 'konfirmaciacp'
		UNION ALL
			select
			r.ucetaktiva as cislouctu
			from rezervacia r, pool p, pooldetail pd
			where r.subjektid = 0 and p.dealid = r.dealid and pd.poolid = p.poolid and r.destinacia = 'konfirmaciaktv' and pd.subjektid NOT IN (0)
		UNION ALL
			select
			ucet as cislouctu
			from konfirmaciapp
			where subjektid NOT IN (0) and
			druhobchodu = 'prevod' and
			logactivityid < 3 and
      			dealid_related is not NULL
		UNION ALL
			select
			ucet as cislouctu
			from konfirmaciapp
			where subjektid NOT IN (0) and
			druhobchodu = 'prevod' and
			logactivityid < 3 and
      			dealid_related is NULL  
	) ksks
    group by cislouctu";

$druhy = Connection::getDataFromDatabase($query, defaultDB)[1];
?>

<ul class="p-3 text-sm text-gray-700 dark:text-gray-200" aria-labelledby="dropdownRadioButton">
    <?php foreach ($druhy as $key => $druh) { ?>
        <li class="mb-0.5">
            <label
                class="w-full text-sm font-medium text-gray-900 rounded-sm dark:text-gray-300 flex cursor-pointer transition-all items-center gap-2 p-2 rounded-sm hover:bg-gray-100 dark:hover:bg-gray-600">
                <input type="checkbox" onchange="submitWholeForm(this)" value="<?php echo $druh["aktivum"]; ?>"
                    name="aktivaFilter"
                    class="w-4 h-4 filterInput text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                <?php echo $druh["aktivum"]; ?>
            </label>
        </li>
    <?php } ?>
</ul>
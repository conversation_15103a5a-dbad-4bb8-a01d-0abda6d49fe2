<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

$fondID = 0;
$offset = $_GET["offset"];

$query = "SELECT 
CASE 
    WHEN dlhopisy = 0 THEN 'Nie' 
    WHEN dlhopisy = 1 THEN 'Áno' 
END as dlhopisy, 
CASE 
    WHEN akcie = 0 THEN 'Nie' 
    WHEN akcie = 1 THEN 'Áno' 
END as akcie, 
refmena
from 
fonds 
where 
fondid=$fondID
";
$detailyFondu = Connection::getDataFromDatabase($query, defaultDB)[1][0];
$fond_dlhopisy = $detailyFondu['dlhopisy'];
$fond_akcie = $detailyFondu['akcie'];
$fond_refmena = $detailyFondu['refmena'];

$detailQuery = "SELECT nav * f_menovy_kurz_kriz(refmena, '$fond_refmena', datum,1) as nav, datum FROM pricestore WHERE fondid = $fondID and datum = (select max(datum) from pricestore where fondid = $fondID)";
$detailStav = Connection::getDataFromDatabase($detailQuery, defaultDB)[1][0];
$trhovaHodnota = $detailStav['nav'];
$trhovyDatum = $detailStav['datum'];

$query = "SELECT
cislouctu,
CASE
   WHEN ksks.eqid = 'BU' THEN 1
   WHEN ksks.eqid = 'TD' THEN 2
   WHEN ksks.eqid = 'Bonds' THEN 3
   WHEN ksks.eqid = 'Shares' THEN 4
   WHEN ksks.eqid = 'Fonds' THEN 5
   ELSE 6 END             AS orderList,
ksks.eqid                  as druh,
kodaktiva                  as aktivum,
max(jednotka)              as jednotka,
sum(stav)                  as stav,
sum(stav - rezervacia - odchadzajuce - zavazokemisia - ktvviazane - poplatky - ostatne_zavazky +
   prevody)               as volne,
sum(rezervacia + poplatky) as rezervacia,
sum(odchadzajuce)          as odchadzajuce,
sum(prichadzajuce)         as prichadzajuce,
cpnaz                      as cpnaz,
cpnazskratka               as cpnazskratka,
isinreal                   as isinreal,
dcr.isincurr               as isincurr,
subjektid                  as subjektid,
cislozmluvy                as cislozmluvy,
podielnikid                as podielnikid
from
(
    select
    cutd as cislouctu,
    '' as destinacia,
    subjektid,
    mena as kodaktiva,
    'TD' as eqid,
    mena as jednotka,
    0 as stav,
    0 as rezervacia,
    0 as odchadzajuce,
    0 as prichadzajuce,
    0 as pocet,
    0 as zavazokemisia,
    0 as ktvviazane,
    0 as poplatky,
    0 as ostatne_zavazky,
    0 as prevody
    from konfirmaciaktv
    where
    subjektid NOT IN (0) and
    logactivityid in (17,18,19,20,21,22)
UNION ALL
    select
    ucetaktiva as cislouctu,
    '' as destinacia,
    mt.subjektid,
    mena||' ('||kodaktiva||')' as kodaktiva,
    'NDF' as eqid,
    mena as jednotka,
    pocet*(sign(md_d-0.5)*(-1)) as stav,
    0 as rezervacia,
    0 as odchadzajuce,
    0 as prichadzajuce,
    pocet*(sign(md_d-0.5)*(-1)) as pocet,
    0 as zavazokemisia,
    0 as ktvviazane,
    0 as poplatky,
    0 as ostatne_zavazky,
    0 as prevody
    from majetoktoday mt, konverzia k
    where
    mt.subjektid NOT IN (0) and
    mt.subjektid = k.subjektid and
    'NDF-'||mt.kodaktiva = 'NDF-'||k.dealid and
    mena='SKK' and
    uctovnykod in (315181,325181) and
    logactivityid=12 and
    typ_konverzie = 1
UNION ALL
    select
    ucetaktiva as cislouctu,
    'majetoktoday' as destinacia,
    subjektid,
    kodaktiva,
    eqid,
    jednotka,
    0 as stav,
    0 as rezervacia,
    0 as odchadzajuce,
    0 as prichadzajuce,
    0 as pocet,
    0 as zavazokemisia,
    0 as ktvviazane,
    (sign(md_d - 0.5) * pocet) as poplatky,
    0 as ostatne_zavazky,
    0 as prevody
    from majetoktoday
    where
    subjektid NOT IN (0) and
    uctovnykod in (325413,325411,325412,325422,325421,325435,325437,325521,325522,325143,325443)
UNION ALL
    select
    ucetaktiva as cislouctu,
    'majetoktoday' as destinacia,
    subjektid,
    kodaktiva,
    eqid,
    jednotka,
    0 as stav,
    0 as rezervacia,
    0 as odchadzajuce,
    0 as prichadzajuce,
    0 as pocet,
    (sign(md_d - 0.5) * pocet) as zavazokemisia,
    0 as ktvviazane,
    0 as poplatky,
    0 as ostatne_zavazky,
    0 as prevody
    from majetoktoday
    where
    subjektid NOT IN (0) and
    uctovnykod in (325210)
UNION ALL
    select
    ucetaktiva as cislouctu,
    'majetoktoday' as destinacia,
    subjektid,
    kodaktiva,
    eqid,
    jednotka,
    0 as stav,
    0 as rezervacia,
    0 as odchadzajuce,
    0 as prichadzajuce,
    0 as pocet,
    0 as zavazokemisia,
    0 as ktvviazane,
    0 as poplatky,
    (sign(md_d - 0.5) * pocet) as ostatne_zavazky,
    0 as prevody
    from majetoktoday
    where
    subjektid NOT IN (0) and
    uctovnykod in (325300,325350)
UNION ALL
    select
    ucetaktiva as cislouctu,
    'majetoktoday' as destinacia,
    subjektid,
    kodaktiva,
    eqid,
    jednotka,
    0 as stav,
    0 as rezervacia,
    (sign(md_d - 0.5) * pocet) as odchadzajuce,
    0 as prichadzajuce,
    0 as pocet,
    0 as zavazokemisia,
    0 as ktvviazane,
    0 as poplatky,
    0 as ostatne_zavazky,
    0 as prevody
    from majetoktoday
    where
    subjektid NOT IN (0) and
    uctovnykod in (325700)
UNION ALL
    select
    ucetaktiva as cislouctu,
    'majetoktoday' as destinacia,
    subjektid,
    kodaktiva,
    eqid,
    jednotka,
    (sign(md_d - 0.5) * (-1) * pocet) as stav,
    0 as rezervacia,
    0 as odchadzajuce,
    0 as prichadzajuce,
    (sign(md_d - 0.5) * (-1) * pocet) as pocet,
    0 as zavazokemisia,
    0 as ktvviazane,
    0 as poplatky,
    0 as ostatne_zavazky,
    0 as prevody
    from majetoktoday
    where
    subjektid NOT IN (0) and
    uctovnykod in ( 221110,221210,251110,251200,251300,251400)
UNION ALL
    select
    ucetaktiva as cislouctu,
    'majetokcesta' as destinacia,
    subjektid,
    kodaktiva,
    eqid,
    jednotka,
    0 as stav,
    0 as rezervacia,
    ((1-in_out) * pocet) as odchadzajuce,
    (in_out * pocet) as prichadzajuce,
    (sign(in_out - 0.5) * pocet) as pocet,
    0 as zavazokemisia,
    0 as ktvviazane,
    0 as poplatky,
    0 as ostatne_zavazky,
    0 as prevody
    from majetokcesta ma
    where subjektid NOT IN (0) and
     in_out=1
UNION ALL
    select
    ma.ucetaktiva as cislouctu,
    'majetokcesta' as destinacia,
    pdr.subjektid,
    ma.kodaktiva,
    ma.eqid,
    ma.jednotka,
    0 as stav,
    0 as rezervacia,
    ((1-ma.in_out) * pdr.ksreal) as odchadzajuce,
    (ma.in_out * pdr.ksreal) as prichadzajuce,
    (sign(ma.in_out - 0.5) * pdr.ksreal) as pocet,
    0 as zavazokemisia,
    0 as ktvviazane,
    0 as poplatky,
    0 as ostatne_zavazky,
    0 as prevody
    from majetokcesta ma, today t, pool p, pooldetailreal pdr
    where ma.subjektid = 0 and 
          pdr.subjektid NOT IN (0) and
    t.fondid = ma.subjektid and
    ma.cestatill >=t.datum and
    ma.in_out=1 and 
    (ma.destinacia = p.destinacia or (ma.destinacia = 'rekonfirmaciacp' and p.destinacia = 'konfirmaciacp'))  
    and ma.dealid = p.dealid 
    and p.poolid = pdr.poolid
    and ma.eqid in ('Bonds', 'Shares', 'Fonds')
UNION ALL
    select
    ma.ucetaktiva as cislouctu,
    'majetokcesta' as destinacia,
    pdr.subjektid,
    ma.kodaktiva,
    ma.eqid,
    ma.jednotka,
    0 as stav,
    0 as rezervacia,
    ((1-ma.in_out) * pdr.transsumareal) as odchadzajuce,
    (ma.in_out * pdr.transsumareal) as prichadzajuce,
    (sign(ma.in_out - 0.5) * pdr.transsumareal) as pocet,
    0 as zavazokemisia,
    0 as ktvviazane,
    0 as poplatky,
    0 as ostatne_zavazky,
    0 as prevody
    from majetokcesta ma, today t, pool p, pooldetailreal pdr
    where ma.subjektid = 0 and 
          pdr.subjektid NOT IN (0) and
    t.fondid = ma.subjektid and
    ma.cestatill >=t.datum and
    ma.in_out=1 and 
    (ma.destinacia = p.destinacia or (ma.destinacia = 'rekonfirmaciacp' and p.destinacia = 'konfirmaciacp'))  
    and ma.dealid = p.dealid 
    and p.poolid = pdr.poolid
    and ma.eqid in ('BU')
    and ma.tranza = 1
UNION ALL
    select
    ma.ucetaktiva as cislouctu,
    'majetokcesta' as destinacia,
    pdr.subjektid,
    ma.kodaktiva,
    ma.eqid,
    ma.jednotka,
    0 as stav,
    0 as rezervacia,
     ((1 - ma.in_out) * (COALESCE(pdr.auvreal, 0) - COALESCE(pdr.dan, 0))) AS odchadzajuce,
     (ma.in_out * (COALESCE(pdr.auvreal, 0) - COALESCE(pdr.dan, 0)))       AS prichadzajuce,
     (SIGN(ma.in_out - 0.5) *
      (COALESCE(pdr.auvreal, 0) - COALESCE(pdr.dan, 0)))                   AS pocet,
    0 as zavazokemisia,
    0 as ktvviazane,
    0 as poplatky,
    0 as ostatne_zavazky,
    0 as prevody
    from majetokcesta ma, today t, pool p, pooldetailreal pdr
    where ma.subjektid = 0 and 
          pdr.subjektid NOT IN (0) and
    t.fondid = ma.subjektid and
    ma.cestatill >=t.datum and
    ma.in_out=1 and 
    (ma.destinacia = p.destinacia or (ma.destinacia = 'rekonfirmaciacp' and p.destinacia = 'konfirmaciacp'))  
    and ma.dealid = p.dealid 
    and p.poolid = pdr.poolid
    and ma.eqid in ('BU')
    and ma.tranza = 2
      UNION ALL
    select
    ucetaktiva as cislouctu,
    'majetokcesta' as destinacia,
    subjektid,
    kodaktiva,
    eqid,
    jednotka,
    0 as stav,
    0 as rezervacia,
    ((1-in_out) * pocet) as odchadzajuce,
    (in_out * pocet) as prichadzajuce,
    (sign(in_out - 0.5) * pocet) as pocet,
    0 as zavazokemisia,
    0 as ktvviazane,
    0 as poplatky,
    0 as ostatne_zavazky,
    0 as prevody
    from majetokcesta ma
    where subjektid NOT IN (0) and
     in_out=0
UNION ALL
    select 
           ma.ucetaktiva as cislouctu,
    'majetokcesta' as destinacia,
    pdr.subjektid,
    ma.kodaktiva,
    ma.eqid,
    ma.jednotka,
    0 as stav,
    0 as rezervacia,
    ((1-ma.in_out) * pdr.ksreal) as odchadzajuce,
    (ma.in_out * pdr.ksreal) as prichadzajuce,
    (sign(ma.in_out - 0.5) * pdr.ksreal) as pocet,
    0 as zavazokemisia,
    0 as ktvviazane,
    0 as poplatky,
    0 as ostatne_zavazky,
    0 as prevody
    from majetokcesta ma, pool p, pooldetailreal pdr
    where 
    ma.subjektid = 0 and
    ma.in_out=0 and 
    (ma.destinacia = p.destinacia or (ma.destinacia = 'rekonfirmaciacp' and p.destinacia = 'konfirmaciacp'))  
    and ma.dealid = p.dealid and 
    p.poolid = pdr.poolid and
    pdr.subjektid NOT IN (0)
    and ma.eqid in ('Bonds', 'Shares', 'Fonds')
UNION ALL
    select 
           ma.ucetaktiva as cislouctu,
    'majetokcesta' as destinacia,
    pdr.subjektid,
    ma.kodaktiva,
    ma.eqid,
    ma.jednotka,
    0 as stav,
    0 as rezervacia,
    ((1-ma.in_out) * pdr.transsumareal) as odchadzajuce,
    (ma.in_out * pdr.transsumareal) as prichadzajuce,
    (sign(ma.in_out - 0.5) * pdr.transsumareal) as pocet,
    0 as zavazokemisia,
    0 as ktvviazane,
    0 as poplatky,
    0 as ostatne_zavazky,
    0 as prevody
    from majetokcesta ma, pool p, pooldetailreal pdr
    where 
    ma.subjektid = 0 and
    ma.in_out=0 and 
    (ma.destinacia = p.destinacia or (ma.destinacia = 'rekonfirmaciacp' and p.destinacia = 'konfirmaciacp'))  
    and ma.dealid = p.dealid and 
    p.poolid = pdr.poolid and
    pdr.subjektid NOT IN (0)
    and ma.eqid in ('BU')
UNION ALL
    select
    ucetaktiva as cislouctu,
    'rezervacia' as destinacia,
    subjektid,
    kodaktiva,
    eqid,
    jednotka,
    0 as stav,
    pocet as rezervacia,
    0 as odchadzajuce,
    0 as prichadzajuce,
    (pocet * -1) as pocet,
    0 as zavazokemisia,
    0 as ktvviazane,
    0 as poplatky,
    0 as ostatne_zavazky,
    0 as prevody
    from rezervacia
    where subjektid NOT IN (0)
        and destinacia <> 'konfirmaciacp'
UNION ALL
    select
    r.ucetaktiva as cislouctu,
    'rezervacia' as destinacia,
    r.subjektid,
    r.kodaktiva,
    r.eqid,
    r.jednotka,
    0 as stav,
    pd.transsuma as rezervacia,
    0 as odchadzajuce,
    0 as prichadzajuce,
    (pd.transsuma * -1) as pocet,
    0 as zavazokemisia,
    0 as ktvviazane,
    0 as poplatky,
    0 as ostatne_zavazky,
    0 as prevody
    from rezervacia r, pool p, pooldetail pd
    where r.subjektid = 0 and p.dealid = r.dealid and pd.poolid = p.poolid and r.destinacia = 'konfirmaciaktv' and pd.subjektid NOT IN (0)
UNION ALL
    select
    ucet as cislouctu,
    'prevodPP' as destinacia,
    subjektid,
    mena as kodaktiva,
    'BU' as eqid,
    mena as jednotka,
    0 as stav,
    0 as rezervacia,
    suma as odchadzajuce,
    0 as prichadzajuce,
    (-1 * suma) as pocet,
    0 as zavazokemisia,
    0 as ktvviazane,
    0 as poplatky,
    0 as ostatne_zavazky,
    0 as prevody
    from konfirmaciapp
    where subjektid NOT IN (0) and
    druhobchodu = 'prevod' and
    logactivityid < 3 and
          dealid_related is not NULL
UNION ALL
    select
    ucet as cislouctu,
    'prevodPP' as destinacia,
    subjektid,
    mena as kodaktiva,
    'BU' as eqid,
    mena as jednotka,
    0 as stav,
    0 as rezervacia,
    0 as odchadzajuce,
    suma as prichadzajuce,
    suma as pocet,
    0 as zavazokemisia,
    0 as ktvviazane,
    0 as poplatky,
    0 as ostatne_zavazky,
    suma as prevody
    from konfirmaciapp
    where subjektid NOT IN (0) and
    druhobchodu = 'prevod' and
    logactivityid < 3 and
          dealid_related is NULL  
) ksks
LEFT JOIN dbequitycurrric dcr ON dcr.isincurrric = ksks.kodaktiva
LEFT JOIN dbequitycurr dc ON dc.isincurr = dcr.isincurr
LEFT JOIN dbequity d ON d.isin = dc.isin
LEFT JOIN portfolio por ON subjektid = por.fondid
group by cislouctu, ksks.eqid, kodaktiva, cpnaz, cpnazskratka, isinreal,  dcr.isincurr, subjektid, cislozmluvy, podielnikid
order by orderList, aktivum
OFFSET $offset
LIMIT 30
";
//echo $query;
$tableData = Connection::getDataFromDatabase($query, defaultDB)[1];


foreach ($tableData as $key => $item) { ?>
    <tr <?php if ($key === sizeof($tableData) - 1)
        echo "hx-trigger='revealed' hx-get='/api/get-transactions-another?offset=" . ($offset + 30) . "' hx-target='#poplatkyTBODY' hx-swap='beforeend'" ?>
            class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 border-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600">
            <th scope="row" class="px-3 py-2 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                <input type="hidden" name="cislozmluvy" value="<?php echo $item["cislozmluvy"]; ?>" />
            <input type="hidden" name="idcheck" value="<?php echo $item["id"]; ?>" />
            <?php echo $item["cislozmluvy"]; ?>
        </th>
        <td class="px-3 py-2">
            <span class="flex w-full justify-between items-center"><?php echo $item["cislouctu"]; ?></span>
        </td>
        <td class="px-3 py-2 DruhColumnData">
            <span class="flex w-full justify-between items-center"><span class="font-bold">
                    <?php echo $item["druh"]; ?></span></span>
        </td>
        <td class="px-3 py-2">
            <?php echo $item["aktivum"]; ?>
        </td>
        <td class="px-3 py-2">
            <?php echo number_format($item["stav"], 2, ".", " "); ?>
        </td>
        <td class="px-3 py-2 font-bold">
            <?php echo number_format($item["volne"], 2, ".", " "); ?>
        </td>
        <td class="px-3 py-2">
            <?php if ($item["rezervacia"] > 0) { ?>
                <span
                    class="bg-yellow-100 text-yellow-800 text-xs font-medium me-2 px-2.5 py-0.5 rounded-sm dark:bg-yellow-900 dark:text-yellow-300">
                    <?php echo number_format($item["rezervacia"], 2, ".", " "); ?></span>
            <?php } else {
                echo number_format($item["rezervacia"], 2, ".", " ");
            } ?>
        </td>
        <td class="px-3 py-2">
            <?php if ($item["odchadzajuce"] > 0) { ?>
                <span
                    class="bg-red-100 text-red-800 text-xs font-medium me-2 px-2.5 py-0.5 rounded-sm dark:bg-red-900 dark:text-red-300">
                    <?php echo number_format($item["odchadzajuce"], 2, ".", " "); ?>
                </span>
            <?php } else {
                echo number_format($item["odchadzajuce"], 2, ".", " ");
            }
            ?>
        </td>
        <td class="px-3 py-2">
            <?php if ($item["prichadzajuce"] > 0) { ?>
                <span
                    class="bg-green-100 text-green-800 text-xs font-medium me-2 px-2.5 py-0.5 rounded-sm dark:bg-green-900 dark:text-green-300">
                    <?php echo number_format($item["prichadzajuce"], 2, ".", " "); ?>
                </span>
            <?php } else {
                echo number_format($item["prichadzajuce"], 2, ".", " ");
            } ?>
        </td>
        <td class="px-3 py-2">
            <button class="p-2 rounded-lg hover:bg-green-200 transition-all">
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide lucide-check-check-icon lucide-check-check">
                    <path d="M18 6 7 17l-5-5" />
                    <path d="m22 10-7.5 7.5L13 16" />
                </svg>
            </button>
        </td>
    </tr>
    <?php
}
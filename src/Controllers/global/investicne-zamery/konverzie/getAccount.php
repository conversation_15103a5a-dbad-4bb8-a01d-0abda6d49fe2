<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

$partnerid = $_POST["partnerid"];
$buyCurr = $_POST["currencyBuy"];
$sellCurr = $_POST["currencySell"];

$partners = Connection::getDataFromDatabase("SELECT partnerid, nazovpartnera, skratka FROM public.partner t WHERE archive_date IS NULL AND typpartnera = 1 AND partnerid = $partnerid ORDER BY nazovpartnera", defaultDB)[1];

$skratka = $partners[0]["skratka"];
if ($skratka === "EXA") {
    $skratka = "EXANTE";
}

$cubs = Connection::getDataFromDatabase("select distinct cub, mena from fondsbu where mena like '$buyCurr' AND banka LIKE '$skratka' order by cub", defaultDB)[1];
$debets = Connection::getDataFromDatabase("select distinct cub, mena from fondsbu where mena like '$sellCurr' AND banka LIKE '$skratka' order by cub ", defaultDB)[1];

?>

<div class="space-y-2">
    <input type="hidden" name="creditAccountCurrency" id="creditAccountCurrency"
        value="<?php echo $cubs[0]["mena"] ?>" />
    <input type="hidden" name="creditAccountValue" id="creditAccountValue" value="<?php echo $cubs[0]["cub"]; ?>" />
    <label for="creditAccount" class="block text-sm font-medium text-gray-700">Účet kredit</label>
    <select id="creditAccount"
        class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
        <?php foreach ($cubs as $key => $item) { ?>
            <option value="<?php echo $item["cub"]; ?>"><?php echo $item["cub"]; ?></option>
        <?php } ?>
    </select>
</div>
<div class="space-y-2">
    <input type="hidden" name="debetAccountCurrency" id="debetAccountCurrency"
        value="<?php echo $debets[0]["mena"] ?>" />
    <input type="hidden" name="debitAccountValue" id="debitAccountValue" value="<?php echo $cubs[0]["cub"]; ?>" />
    <label for="debitAccount" class="block text-sm font-medium text-gray-700">Účet debet</label>
    <select id="debitAccount"
        class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
        <?php foreach ($debets as $key => $item) { ?>
            <option value="<?php echo $item["cub"]; ?>"><?php echo $item["cub"]; ?></option>
        <?php } ?>
    </select>
</div>
<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
require_once "/home/<USER>/www/src/lib/notifications/Notification.class.php";

$spravca = $_SESSION["mode"]["mode"] === "admin" ? true : false;
$queryPool = [];

$pooling = false;
if ($_SESSION["mode"]["mode"] === "global") {
    $pooling = true;
}
$poolErrors = [];

$poolid = $_POST["poolid"];
if ($pooling && $poolid === "") {
    array_push($poolErrors, "Nemam pool");
}
if (isset($_SESSION["client"])) {
    $id_fond = $_SESSION["client"]["fondid"];
} elseif ($_SESSION["mode"]["mode"] === "admin") {
    $id_fond = 1;
} else {
    $id_fond = 0;
}

$totalSell = 0;
$totalBuy = 0;

if (isset($_POST["poolData"]) && $_POST["poolData"] !== "") {
    $id_fonds = $_POST["poolData"];
    $id_fonds = json_decode($id_fonds);
    foreach ($id_fonds as $key => $value) {
        $sumaBuy = $value->sumaBuy;
        $totalBuy += $sumaBuy;
        $totalSell += $value->sumaSell;
        if ($sumaBuy > 0) {
            $fondid = $value->subjektid;
            $query = "INSERT INTO pooldetail (subjektid, poolid, transsuma, ks) VALUES (?,?,?,?)";
            $insertINTODetail = Connection::InsertUpdateCreateDelete($query, [$fondid, $poolid, $sumaBuy, $sumaBuy], defaultDB);
            if ($insertINTODetail !== 1) {
                array_push($poolErrors, "Error vkladania do pooldetail;");
            }
        }
    }
}
$dealidseq = Connection::getDataFromDatabase("SELECT nextval('s_konverzia');", defaultDB)[1][0]["nextval"];
$dealNo = Connection::getDataFromDatabase("SELECT id FROM ididentify WHERE popis LIKE 'konverzia'", defaultDB);

$dealid = (int) ($dealidseq . $dealNo);
$subjektid = $id_fond;
$dat_konfirmacia = $_POST["confirmationDate"];
$dat_realizacia = $_POST["currencyDate"];
if ($pooling) {
    $sumakredit = $totalBuy;
    $sumadebet = $totalSell;
} else {
    $sumakredit = number_format($_POST["clientBuys"], 2);
    $sumadebet = number_format($_POST["clientSells"], 2);
}
$menakredit = $_POST["creditAccountCurrency"];
$menadebet = $_POST["debetAccountCurrency"];
$menovypar = $_POST["currencyPair"];
$kurz = number_format($_POST["exchangeRate"], 9);
$cukredit = $_POST["creditAccount"];
$cudebet = $_POST["debitAccountValue"];
$assigneduserid = $_POST["assigneduserid"];
$datumpokynu = $_POST["datumpokynu"];
$pokyn_ako = $_POST["pokyn_ako"];
$logactivityid = 1;
$logdatatimeactivity = date("Y-m-d");
$loguserid = $_SESSION["user"]["data"]["userid"];
$username = $_SESSION["user"]["data"]["username"];
$year = date("Y");
$typkonverzie = $_POST["conversionType"];
$partnerid = $_POST["partnerid"];

//INSERT INTO KONVERZIA
$insertIntoKonverziaQuery = "INSERT INTO konverzia (
            dealidseq,dealid,subjektid,dat_konfirmacia,
            dat_realizacia,sumakredit,sumadebet,
            menakredit,menadebet,menovypar,kurz,
            cukredit,cudebet,logactivityid,loguserid,
            logdatatimeactivity,partnerid,typ_konverzie,assigneduserid, pokyn_ako, datum_pokynu
        )
        values
        (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";

$queryPool[] = [
    "query" => $insertIntoKonverziaQuery,
    "params" => [
        $dealidseq,
        $dealid,
        $subjektid,
        $dat_konfirmacia,
        $dat_realizacia,
        $sumakredit,
        $sumadebet,
        $menakredit,
        $menadebet,
        $menovypar,
        $kurz,
        $cukredit,
        $cudebet,
        $logactivityid,
        $loguserid,
        $logdatatimeactivity,
        $partnerid,
        $typkonverzie,
        $assigneduserid,
        $pokyn_ako,
        $datumpokynu
    ],
    "db" => defaultDB,
    "name" => "insert into konverzia"
];

$queryPool[] = [
    "query" => "INSERT INTO dennikpm (userid, dealid, destinacia, poradovecislo, cislo) VALUES
        (
            $loguserid,$dealid,'konverzia',
            (
                select COALESCE(max(poradovecislo),0) + 1
                from dennikpm
                where userid=$loguserid
            ),
            (
                (select COALESCE(max(poradovecislo),0) + 1
                from dennikpm
                where userid=$loguserid
                )::char
            ) || lpad('$year',2,'0') || lpad((
                            select agentid::char
                            from users
                            where userid=$loguserid
                            ),4,'0')
        )",
    "params" => [],
    "db" => defaultDB,
    "name" => "insert into dennikpm"
];

$queryPool[] = [
    "query" => "INSERT INTO rezervacia (dealid, destinacia, eqid, kodaktiva, ucetaktiva, jednotka, mena, pocet, obratdatetime, subjektid)
    VALUES (?,?,?,?,?,?,?,?,?,?)",
    "params" => [
        $dealid,
        'konverzia',
        'BU',
        $menakredit,
        $cukredit,
        $menakredit,
        $menakredit,
        $sumakredit,
        date("Y-m-d"),
        $id_fond
    ],
    "db" => defaultDB,
    "name" => "insert into rezervacia"
];

$currentDate = date("Y-m-d");
$currencyBuy = $_POST["currencyBuy"];
$currencySell = $_POST["currencySell"];

if (isset($_POST["poolid"])) {
    if ($menovypar === $currencyBuy . $currencySell) {
        $kurz_cashflow = 1 / $kurz;
    } else {
        $kurz_cashflow = $kurz;
    }

    $queryPool[] = [
        "query" => "UPDATE pool SET destinacia = ?, dealid = ? WHERE poolid = ?",
        "params" => ['konverzia', $dealid, $poolid],
        "db" => defaultDB,
        "name" => "update pool"
    ];

    $queryPool[] = [
        "query" => "INSERT INTO cashflow
        (dealid, tranza, in_out, sparovanie, destinacia, subjektid, eqid, kodaktiva, ucetaktiva, jednotka, mena, pocet, datum, 
                datumzmeny, poznamka, stav) 
            select 
                $dealid, 0, 0, Null, 'konverzia', p.subjektid, 'BU', '$menadebet', $cudebet, '$menadebet', '$menadebet', 
                transsuma*$kurz_cashflow, '$dat_realizacia', '$currentDate', 'Debetná suma konverzie', 0
            from pooldetail p where p.poolid = $poolid",
        "params" => [],
        "db" => defaultDB,
        "name" => "insert into cashflow 01"
    ];

    $queryPool[] = [
        "query" => "INSERT INTO cashflow
        (dealid, tranza, in_out, sparovanie, destinacia, subjektid, eqid, kodaktiva, ucetaktiva, jednotka, mena, pocet, datum, 
                datumzmeny, poznamka, stav) 
            select 
                $dealid, 0, 1, Null, 'konverzia', p.subjektid, 'BU', '$menakredit', '$cukredit', '$menakredit', '$menakredit', 
                transsuma, '$dat_realizacia', '$currentDate', 'Kreditná suma konverzie', 0
            from pooldetail p where p.poolid = $poolid",
        "params" => [],
        "db" => defaultDB,
        "name" => "insert into cashflow 02"
    ];
} else {
    $queryPool[] = [
        "query" => "INSERT INTO cashflow
        (dealid, tranza, in_out, sparovanie, destinacia, subjektid, eqid, kodaktiva, ucetaktiva, jednotka, mena, pocet, datum, 
                datumzmeny, poznamka, stav) 
            values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
        "params" => [
            $dealid,
            0,
            0,
            Null,
            'konverzia',
            $id_fond,
            'BU',
            $menadebet,
            $cudebet,
            $menadebet,
            $menadebet,
            $sumadebet,
            $dat_realizacia,
            $currentDate,
            'Debetná suma konverzie',
            0
        ],
        "db" => defaultDB,
        "name" => "insert into cashflow 03"
    ];

    $queryPool[] = [
        "query" => "INSERT INTO cashflow
        (dealid, tranza, in_out, sparovanie, destinacia, subjektid, eqid, kodaktiva, ucetaktiva, jednotka, mena, pocet, datum, 
                datumzmeny, poznamka, stav) 
            values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
        "params" => [
            $dealid,
            0,
            1,
            Null,
            'konverzia',
            $id_fond,
            'BU',
            $menakredit,
            $cukredit,
            $menakredit,
            $menakredit,
            $sumakredit,
            $dat_realizacia,
            $currentDate,
            'Kreditná suma konverzie',
            0
        ],
        "db" => defaultDB,
    ];
}

$queryPool[] = [
    "query" => "UPDATE konverzia SET logactivityid = 4 WHERE dealid = ?",
    "params" => [$dealid],
    "db" => defaultDB,
    "name" => "update konverzia"
];

$transaction = Connection::runTransaction(defaultDB, null, $queryPool);
if (str_contains($transaction, "poriadku") && empty($poolErrors)) {
    $notification = new Notification(
        1,
        'konverzia',
        $dealid,
        'create',
        $loguserid,
        $username,
        "zamerKonvcreate",
        json_encode(["dealid", $dealid]),
        true,
        $id_fond,
        "user",
        "Vytvoril investičný zámer s ID <strong>" . $dealid . '</strong> na konverziu menového páru <strong>' . $menovypar . '</strong>' . " pre subjekt <strong>" . $fondid . '</strong>'
    );
    $notification->createNotifcation();
    ?>
    <div id="toast-success"
        class="flex border-green-300 border items-center w-full max-w-sm p-4 mb-4 text-green-500 bg-white rounded-lg shadow dark:text-gray-400 dark:bg-gray-800"
        role="alert">
        <div
            class="inline-flex items-center justify-center flex-shrink-0 w-8 h-8 text-green-500 bg-green-100 rounded-lg dark:bg-green-800 dark:text-green-200">
            <svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                viewBox="0 0 20 20">
                <path
                    d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 8.207-4 4a1 1 0 0 1-1.414 0l-2-2a1 1 0 0 1 1.414-1.414L9 10.586l3.293-3.293a1 1 0 0 1 1.414 1.414Z" />
            </svg>
            <span class="sr-only">Check icon</span>
        </div>
        <div class="ms-3 text-sm font-semibold">Investičný zámer bol úspešne aktualizovaný.</div>
        <button type="button"
            class="ms-auto -mx-1.5 -my-1.5 bg-white text-gray-400 hover:text-gray-900 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex items-center justify-center h-8 w-8 dark:text-gray-500 dark:hover:text-white dark:bg-gray-800 dark:hover:bg-gray-700"
            data-dismiss-target="#toast-success" aria-label="Close">
            <span class="sr-only">Close</span>
            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
            </svg>
        </button>
    </div>
    <script>
        setTimeout(() => {
            window.location.href = "/investicne-zamery";
        }, 1500);
    </script>
<?php } else {
    print_r($poolErrors);
    ?>
    <div id="toast-danger" class="flex items-center w-full max-w-sm p-4 mb-4 text-white bg-red-500 rounded-lg shadow-lg"
        role="alert">
        <?php ?>
        <div
            class="inline-flex items-center justify-center flex-shrink-0 w-8 h-8 text-red-500 bg-red-100 rounded-lg dark:bg-red-800 dark:text-red-200">
            <svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                viewBox="0 0 20 20">
                <path
                    d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 11.793a1 1 0 1 1-1.414 1.414L10 11.414l-2.293 2.293a1 1 0 0 1-1.414-1.414L8.586 10 6.293 7.707a1 1 0 0 1 1.414-1.414L10 8.586l2.293-2.293a1 1 0 0 1 1.414 1.414L11.414 10l2.293 2.293Z" />
            </svg>
            <span class="sr-only">Error icon</span>
        </div>
        <div class="ms-3 text-sm font-bold">Investičný zámer sa nepodarilo aktualizovať kvôli neznámej chybe!
            <?php print_r($transaction); ?>
        </div>
        <button type="button"
            class="ms-auto -mx-1.5 -my-1.5 text-white hover:text-gray-900 transition-all rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex items-center justify-center h-8 w-8 dark:text-gray-500 dark:hover:text-white dark:bg-gray-800 dark:hover:bg-gray-700"
            data-dismiss-target="#toast-danger" aria-label="Close">
            <span class="sr-only">Close</span>
            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
            </svg>
        </button>
    </div>
    <script>
        setTimeout(() => {
            $("#toast-danger").fadeOut();
        }, 1500);
    </script>
<?php } ?>
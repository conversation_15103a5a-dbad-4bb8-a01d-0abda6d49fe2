<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

$buyCurr = $_POST["buyCurr"];
$sellCurr = $_POST["sellCurr"];

$mode = $_SESSION["mode"]["mode"];

if (isset($_SESSION["client"])) {
    $fondid = $_SESSION["client"]["fondid"];
} elseif ($_SESSION["mode"]["mode"] === "admin") {
    $fondid = 1;
} else {
    $fondid = 0;
}

if ($buyCurr === $sellCurr) { ?>
    <section class="px-4">
        <p class="w-full h-72 text-lg font-bold bg-red-100 my-4 rounded-lg flex items-center justify-center">Meny sa nemôžu
            zhodovať!</p>
    </section>
    <?php
    exit;
}
$partners = Connection::getDataFromDatabase("SELECT partnerid, nazovpartnera, skratka FROM public.partner t WHERE archive_date IS NULL AND typpartnera = 1 ORDER BY nazovpartnera", defaultDB)[1];
$skratka = $partners[0]['skratka'];
$cubs = Connection::getDataFromDatabase("select distinct cub, mena from fondsbu where mena like '$buyCurr' AND banka LIKE '$skratka' order by cub", defaultDB)[1];
$debets = Connection::getDataFromDatabase("select distinct cub, mena from fondsbu where mena like '$sellCurr' AND banka LIKE '$skratka' order by cub ", defaultDB)[1];
$par = "$buyCurr$sellCurr";
$parReversed = "$sellCurr$buyCurr";
$menovyPar = Connection::getDataFromDatabase("SELECT par FROM menovypar WHERE par = '$par' OR par = '$parReversed'", defaultDB)[1][0]["par"];
$today = Connection::getDataFromDatabase("SELECT max(datum) as datum FROM today", defaultDB)[1][0]["datum"];
if ($fondid != 0) {
    if (sizeof($debets) === 1) {
        $ucetaktiva = $debets[0]["cub"];
    }

    $hasToSell = Connection::getDataFromDatabase("SELECT sum(pocet * ((0.5 - md_d) / 0.5)) as pocet FROM majetoktoday
WHERE subjektid = $fondid AND uctovnykod = 221110 AND eqid = 'BU' AND md_d = 0 AND mena = '$sellCurr'", defaultDB)[1][0]["pocet"];
    if ($hasToSell === NULL) { ?>
        <section class="px-4">
            <p class="w-full h-72 text-lg font-bold bg-red-100 my-4 rounded-lg flex items-center justify-center">Klient nemá
                dostatok prostriedkov!</p>
        </section>
        <?php
        exit;
    }
    $hasToBuy = Connection::getDataFromDatabase("SELECT pocet * ((0.5 - md_d) / 0.5) as pocet FROM majetoktoday
WHERE subjektid = $fondid AND uctovnykod = 221110 AND eqid = 'BU' AND md_d = 0 AND mena = '$buyCurr'", defaultDB)[1][0]["pocet"];
}

if (!isset($menovyPar) || $menovyPar === "") {
    exit();
}
?>

<div class="dark:bg-gray-800 bg-white rounded-lg w-full mx-auto">
    <div id="toast" class="absolute bottom-0 right-5"></div>
    <div class="p-6 space-y-6">
        <section class="flex items-center justify-between">
            <h1 class="text-2xl dark:text-gray-100 font-semibold text-left">Konfirmácia menovej konverzie</h1>
            <div class="space-y-2 dark:text-gray-100">
                <label for="subject" class="block text-sm font-medium">Subjekt</label>
                <span class="my-2 font-semibold text-xl"><?php if ($_SESSION["mode"]["mode"] === "global") {
                    echo "Globálny režim";
                } else {
                    if (isset($_SESSION["client"])) {
                        echo $_SESSION["client"]["fondid"];
                    }
                } ?></span>
            </div>
        </section>
        <form class="space-y-6" hx-post="/api/investicne-zamery/konverzie/create" hx-target="#toast">
            <input type="hidden" id="currencyBuy" value="<?php echo $buyCurr; ?>" />
            <input type="hidden" id="currencySell" value="<?php echo $sellCurr; ?>" />
            <div class="">
                <div class="space-y-2">
                    <label for="partner"
                        class="block text-sm font-medium text-gray-700 dark:text-gray-100">Partner</label>
                    <select id="partner" name="partnerid"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                        <?php foreach ($partners as $key => $item) { ?>
                            <option value="<?php echo $item["partnerid"]; ?>"><?php echo $item["nazovpartnera"]; ?></option>
                        <?php } ?>
                    </select>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6" id="uctyWrapper">
                <div class="space-y-2">
                    <input type="hidden" name="creditAccountCurrency" id="creditAccountCurrency"
                        value="<?php echo $buyCurr; ?>" />
                    <label for="creditAccount" class="block text-sm font-medium text-gray-700 dark:text-gray-100">Účet
                        kredit</label>
                    <select id="creditAccount" name="creditAccount"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                        <?php foreach ($cubs as $key => $item) { ?>
                            <option value="<?php echo $item["cub"]; ?>">
                                <?php echo $item["cub"]; ?>
                            </option>
                        <?php } ?>
                    </select>
                </div>
                <div class="space-y-2">
                    <input type="hidden" name="debetAccountCurrency" id="debetAccountCurrency"
                        value="<?php echo $debets[0]["mena"] ?>" />
                    <label for="debitAccount" class="block text-sm font-medium text-gray-700 dark:text-gray-100">Účet
                        debet</label>
                    <select id="debitAccountValue" name="debitAccountValue"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                        <?php foreach ($debets as $key => $item) { ?>
                            <option value="<?php echo $item["cub"]; ?>">
                                <?php echo $item["cub"]; ?>
                            </option>
                        <?php } ?>
                    </select>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="space-y-2">
                    <label for="confirmationDate"
                        class="block text-sm font-medium text-gray-700 dark:text-gray-100">Dátum
                        konfirmácie</label>
                    <input type="date" id="confirmationDate" name="confirmationDate" value="<?php echo $today; ?>"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                </div>
                <div class="space-y-2">
                    <label for="currencyDate" class="block text-sm font-medium text-gray-700 dark:text-gray-100">Dátum
                        valuty</label>
                    <input type="date" id="currencyDate" name="currencyDate" value="<?php echo $today; ?>"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="space-y-2">
                    <label for="currencyPair" class="block text-sm font-medium text-gray-700 dark:text-gray-100">Menový
                        pár</label>
                    <input type="text" id="currencyPair" name="currencyPair" value="<?php echo $menovyPar; ?>" readonly
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                </div>
                <div class="space-y-2">
                    <label for="conversionType" class="block text-sm font-medium text-gray-700 dark:text-gray-100">Typ
                        konverzie</label>
                    <select id="conversionType" name="conversionType"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                        <option value="0">Spot</option>
                        <option value="2">Forward Delivery</option>
                    </select>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="space-y-2">
                    <label for="exchangeRate"
                        class="block text-sm font-medium text-gray-700 dark:text-gray-100">Kurz</label>
                    <input type="number" id="exchangeRate" name="exchangeRate" step="0.0001"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                </div>
                <div class="flex justify-between gap-4 flex-col">
                    <?php
                    $typ = Connection::getDataFromDatabase("SELECT pokyn FROM fonds WHERE fondid = $fondid", defaultDB)[1][0]["pokyn"];
                    if ($typ == "1") { ?>
                        <label for="assigneduser" class="block text-sm font-medium text-gray-900 dark:text-white">Zadávateľ
                            pokynu:</label>
                        <input type="hidden" name="assigneduser" id="assigneduser" value="<?php echo $fondid; ?>" />
                        <span class="dark:text-gray-200">Klient</span>
                        <?php
                    } else {
                        ?>
                        <div>
                            <label for="assigneduser"
                                class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Zadávateľ
                                pokynu:</label>
                            <select id="assigneduser" name="assigneduser"
                                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700
                                 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                                <?php
                                $users = Connection::getDataFromDatabase("SELECT u.userid, u.username
                                FROM users u
                                        INNER JOIN usergroupusers ug ON u.userid = ug.userid
                                WHERE u.lasttime > '2024-09-01'
                                GROUP BY u.userid", defaultDB)[1];
                                foreach ($users as $user) { ?>
                                    <option value="<?php echo $user["userid"]; ?>"><?php echo $user["username"]; ?></option>
                                <?php } ?>
                            </select>
                        </div>
                    <?php } ?>
                    <section class="flex items-center gap-10">
                        <div class="w-full">
                            <label for="datumpokynu"
                                class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Dátum
                                pokynu</label>
                            <input type="datetime-local" id="datumpokynu" name="datumpokynu"
                                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700
                             dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" required />
                        </div>
                        <div class="w-full">
                            <label for="pokyn_ako"
                                class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Spôsob
                                prijatia pokynu</label>
                            <select id="pokyn_ako" name="pokyn_ako"
                                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700
                             dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                                <option value="0">Onboarding</option>
                                <option value="1">Osobne</option>
                                <option value="2">Email</option>
                                <option value="3">Telefón</option>
                            </select>
                        </div>
                    </section>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="space-y-2">
                    <label for="clientBuys" class="block text-sm font-medium text-gray-700 dark:text-gray-100">Klient
                        nakupuje</label>
                    <div class="mt-1 flex rounded-md gap-4">
                        <input type="text" id="clientBuys" name="clientBuys" <?php echo $mode === "global" ? "disabled" : "" ?>
                            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                        <section class="bg-gray-200 px-3 rounded-lg font-semibold">
                            <div class="inline-flex items-center justify-center h-full rounded-r-md text-md gap-1 ">
                                <span><?php echo $buyCurr ?></span>
                            </div>
                        </section>
                    </div>
                </div>
                <div class="space-y-2">
                    <label for="clientSells" class="block text-sm font-medium text-gray-700 dark:text-gray-100">Klient
                        predáva</label>
                    <div class="mt-1 flex rounded-md gap-4">
                        <input type="text" id="clientSells" name="clientSells" <?php echo $mode === "global" ? "disabled" : "" ?>
                            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                        <section class="bg-gray-200 px-3 rounded-lg font-semibold">
                            <?php if ($fondid != 0) { ?><small class="text-xs">Maximálne: </small><input type="hidden"
                                    name="maxToSell" id="maxToSell" value="<?php echo $hasToSell; ?>" /><?php } ?>
                            <div class="inline-flex items-center rounded-r-md text-md gap-1 ">
                                <span><?php echo $fondid != 0 ? number_format($hasToSell, 2, ".", " ") : "" ?></span>
                                <span><?php echo $sellCurr ?></span>
                            </div>
                        </section>
                    </div>
                </div>
            </div>
            <div class="flex justify-end space-x-4 pt-4">
                <?php if (!isset($_SESSION["client"])) { ?>s
                    <input type="hidden" name="poolid" id="poolid" />
                    <input type="hidden" name="poolData" id="poolData" />
                    <div id="buttonToPool">
                        <button type="button" id="poolingBtn" data-modal-target="pooling-modal" style="display: none;"
                            data-modal-toggle="pooling-modal"
                            class="text-white w-full flex items-center justify-center gap-2 transition-all mt-6 text-xl bg-gray-700 hover:bg-gray-900 focus:outline-none focus:ring-4 focus:ring-gray-300 font-extrabold rounded-lg text-sm px-5 py-2.5 me-2 mb-2"><svg
                                xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
                                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                class="lucide lucide-database">
                                <ellipse cx="12" cy="5" rx="9" ry="3" />
                                <path d="M3 5V19A9 3 0 0 0 21 19V5" />
                                <path d="M3 12A9 3 0 0 0 21 12" />
                            </svg> Pool</button>
                    </div>
                <?php } ?>
                <button type="submit"
                    class="text-white w-full flex items-center justify-center gap-2 transition-all mt-6 text-xl bg-blue-700 hover:bg-blue-900 transition-all focus:outline-none focus:ring-4 focus:ring-gray-300 font-extrabold rounded-lg text-sm px-5 py-2.5 me-2 mb-2">Potvrdiť</button>
            </div>
        </form>
    </div>
</div>

<?php
if (isset($_SESSION["client"])) { ?>
    <script>
        (function () {
            let typingTimer;
            const doneTyping = 350;
            $("#debitAccount").on("change", (e) => {
                const value = JSON.parse(e.target.value);
                const cub = value.cub;
                const mena = value.mena;
                document.getElementById("debetAccountCurrency").value = mena;
                document.getElementById("debitAccountValue").value = cub;
            });

            $("#creditAccount").on("change", (e) => {
                const value = JSON.parse(e.target.value);
                const cub = value.cub;
                const mena = value.mena;
                document.getElementById("creditAccountCurrency").value = mena;
                document.getElementById("creditAccountValue").value = cub;
            });

            $("#partner").on("change", (e) => {
                const value = e.target.value;
                const currencyBuy = $("#currencyBuy").val();
                const currencySell = $("#currencySell").val();
                htmx.ajax('POST', '/api/penazne-fondy/get/account', {
                    target: '#uctyWrapper', swap: 'innerHTML', values: {
                        partnerid: value,
                        currencyBuy: currencyBuy,
                        currencySell: currencySell
                    }
                })
            });

            function recalculateTVValues(e) {
                let suma = parseFloat(e.target.value.replace(",", "."));
                const maxSuma = document.getElementById("maxToSell").value;
                const menaSell = document.getElementById("currencySell").value;
                const menaBuy = document.getElementById("currencyBuy").value;
                const currencyPair = document.getElementById("currencyPair").value;
                const creditAccountCurrency = document.getElementById("creditAccountCurrency").value;
                const kurz = document.getElementById("exchangeRate").value;



                let value = 0;
                let valueMax = 0;
                if (currencyPair === creditAccountCurrency) {
                    value = suma / kurz;
                    valueMax = maxSuma * kurz;
                } else {
                    value = suma * kurz;
                    valueMax = maxSuma / kurz;
                }

                console.log({ maxSuma, suma });

                if (parseFloat(maxSuma) < parseFloat(suma)) {
                    e.target.value = maxSuma;
                    suma = maxSuma;
                    document.getElementById("clientBuys").value = valueMax;
                    document.getElementById("clientSells").value = maxSuma;
                } else {
                    e.target.value = parseFloat(suma);
                    document.getElementById("clientBuys").value = value;
                    document.getElementById("clientSells").value = suma;
                }
            }

            function recalculateTVValuesReverse(e) {
                let suma = parseFloat(e.target.value.replace(",", "."));
                const maxSuma = document.getElementById("maxToSell").value;
                let menaSell = document.getElementById("currencySell").value;
                let menaBuy = document.getElementById("currencyBuy").value;
                const currencyPair = document.getElementById("currencyPair").value;
                const creditAccountCurrency = document.getElementById("creditAccountCurrency").value;
                const kurz = document.getElementById("exchangeRate").value;

                console.log({ maxSuma, more: document.getElementById("clientSells").value });
                let value = 0;
                let valueMax = 0;

                if (currencyPair === creditAccountCurrency) {
                    value = suma * kurz;
                    valueMax = maxSuma / kurz;
                } else {
                    value = suma / kurz;
                    valueMax = maxSuma * kurz;
                }

                if (parseFloat(maxSuma) < value) {
                    recalculateTVValues({
                        target: {
                            value: parseFloat(maxSuma).toFixed(2)
                        }
                    });
                } else {
                    e.target.value = parseFloat(suma);
                    document.getElementById("clientSells").value = parseFloat(value).toFixed(2);
                }
            }

            $("#clientBuys").on("input", (e) => {
                clearTimeout(typingTimer);
                typingTimer = setTimeout(() => {
                    recalculateTVValuesReverse(e);
                }, doneTyping);
            });

            $("#clientSells").on("input", (e) => {
                const maxSuma = parseFloat(document.getElementById("maxToSell").value);
                console.log(document.getElementById("maxToSell").value);
                if (e.target.value > maxSuma) {
                    e.target.value = maxSuma;
                }
                recalculateTVValues(e);
            });
        })();
    </script>
<?php } else {
    include "src/Components/pooling/modal.php"; ?>
    <script>
        function enablePooling() {
            if (document.getElementById("poolingBtn")) {
                $("#poolingBtn").css("display", "inline-flex");
                generatePool();
            } else {
                $("#poolingBtn").hide();
            }
        }

        function generatePool() {
            const exchangeRate = document.getElementById("exchangeRate").value;
            const creditAccount = document.getElementById("creditAccount").value;
            const debitAccount = document.getElementById("debitAccount").value;
            const currencyBuy = document.getElementById("currencyBuy").value;
            const currencySell = document.getElementById("currencySell").value;
            const currPair = document.getElementById("currencyPair").value;
            const kurz = document.getElementById("exchangeRate").value;
            htmx.ajax('POST', `/api/investicne-zamery/generatePool`,
                {
                    target: '#modalWrapperko',
                    values: {
                        "mena": currencyBuy,
                        "mena2": currencySell,
                        "typ": "Konv",
                        "cub": {
                            cub: creditAccount,
                            mena: currencyBuy
                        },
                        "cum": {
                            cub: debitAccount,
                            mena: currencySell
                        },
                        "kurz": kurz,
                        "currPair": currPair
                    }
                }).then(() => {

                });
            $("#generated").val(1);
        }

        $("#exchangeRate").on("change", (e) => {
            if (e.target.value !== "0") {
                enablePooling(e);
            }
        });

        $("#debitAccount").on("change", (e) => {
            const value = JSON.parse(e.target.value);
            const cub = value.cub;
            const mena = value.mena;
            document.getElementById("debetAccountCurrency").value = mena;
            document.getElementById("debitAccountValue").value = cub;
        });

        $("#creditAccount").on("change", (e) => {
            const value = JSON.parse(e.target.value);
            const cub = value.cub;
            const mena = value.mena;
            document.getElementById("creditAccountCurrency").value = mena;
            document.getElementById("creditAccountValue").value = cub;
        });

        $("#partner").on("change", (e) => {
            const value = e.target.value;
            const currencyBuy = $("#currencyBuy").val();
            const currencySell = $("#currencySell").val();
            htmx.ajax('POST', '/api/penazne-fondy/get/account', {
                target: '#uctyWrapper', swap: 'innerHTML', values: {
                    partnerid: value,
                    currencyBuy: currencyBuy,
                    currencySell: currencySell
                }
            })
        });

        function recalculateTVValues(e) {
            const formData = new FormData(e.currentTarget);
            const clientBuys = formData.get("poolBuyingAllInput");
            const clientSells = formData.get("poolSellingAllInput");
            const poolid = formData.get("poolidModal");
            const poolData = formData.get("poolDetailData");

            document.getElementById("clientBuys").value = clientBuys;
            document.getElementById("clientSells").value = clientSells;
            document.getElementById("poolid").value = poolid;
            document.getElementById("poolData").value = poolData;
        }

        document.getElementById("poolDetailForm").addEventListener("submit", (e) => {
            e.preventDefault();
            recalculateTVValues(e);
        });
    </script>
<?php } ?>
<script src="/src/assets/js/global/investicny-zamer/form.js"></script>
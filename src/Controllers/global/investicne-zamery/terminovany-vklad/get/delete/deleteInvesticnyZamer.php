<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
require_once "/home/<USER>/www/src/lib/notifications/Notification.class.php";

$currentDate = date("Y-m-d");

if (isset($_SESSION["client"])) {
    $id_fond = $_SESSION["client"]["fondid"];
    $clientName = Connection::getDataFromDatabase("SELECT meno || ' ' || priez<PERSON>z as name, p.podielnikid FROM podielnik p, portfolio po WHERE p.podielnikid = po.podielnikid AND po.fondid = $id_fond", defaultDB)[1][0];
} elseif ($_SESSION["mode"]["mode"] === "admin") {
    $id_fond = 1;
    $clientName = "Správca";
} else {
    $id_fond = 0;
    $clientName = "Global";
}

$sess_userid = $_SESSION["user"]["data"]["userid"];
$username = $_SESSION["user"]["data"]["username"];
$dealid = $_POST["dealid"];
$errors = [];



if (isset($_POST["type"]) && $_POST["type"] === "KTV") {
    $queryUpdate = "UPDATE konfirmaciaktv SET logactivityid = ?, logdatatimeactivity = ?, loguserid = ? WHERE subjektid = ? AND dealid = ?";
    $queryUpdateConn = Connection::InsertUpdateCreateDelete($queryUpdate, [3, $currentDate, $sess_userid, $id_fond, $dealid], defaultDB);
    if (gettype($queryUpdateConn) !== "integer") {
        $errors[] = "Nepodarilo sa zmeniť údaje v tabuľke [konfirmaciaktv]. " . $queryUpdateConn;
    }

    $queryDeleteRezervacia = "DELETE FROM rezervacia WHERE dealid = ?";
    $queryDeleteRezervaciaConn = Connection::InsertUpdateCreateDelete($queryDeleteRezervacia, [$dealid], defaultDB);
    if (gettype($queryDeleteRezervaciaConn) !== "integer") {
        $errors[] = "Nepodarilo sa zmazať údaje v tabuľke [rezervacia]. " . $queryDeleteRezervaciaConn;
    }

    $queryUpdateCashFlow = "UPDATE cashflow SET stav = ? WHERE dealid = ?";
    $queryUpdateCashFlowConn = Connection::InsertUpdateCreateDelete($queryUpdateCashFlow, [1, $dealid], defaultDB);
    if (gettype($queryUpdateCashFlowConn) !== "integer") {
        $errors[] = "Nepodarilo sa zmeniť údaje v tabuľke [cashflow]. " . $queryUpdateCashFlowConn;
    }

    if (empty($errors)) {
        $notification = new Notification(
            3,
            'konfirmaciaktv',
            $dealid,
            'delete',
            $sess_userid,
            $username,
            "zamerKTVdelete",
            json_encode(["dealid", $dealid]),
            false,
            $id_fond,
            "user",
            "Zrušil investičný zámer s ID <strong>" . $dealid . '</strong> na terminovaný vklad pre subjekt <strong>' . $clientName["name"] . '</strong>'
        );
        $notification->createNotifcation();
    }
} else if ((isset($_POST["type"]) && $_POST["type"] === "Akcia") || ((isset($_POST["type"]) && $_POST["type"] === "Dlhopis")) || ((isset($_POST["type"]) && $_POST["type"] === "Fond"))) {
    $queryUpdate = "UPDATE konfirmaciacp SET logactivityid = ?, logdatatimeactivity = ?, loguserid = ? WHERE dealid = ?";
    $queryUpdateConn = Connection::InsertUpdateCreateDelete($queryUpdate, [3, $currentDate, $sess_userid, $dealid], defaultDB);
    if (gettype($queryUpdateConn) !== "integer") {
        $errors[] = "Nepodarilo sa zmeniť údaje v tabuľke [konfirmaciacp]. " . $queryUpdateConn;
    }

    $queryDeleteRezervacia = "DELETE FROM rezervacia WHERE dealid = ?";
    $queryDeleteRezervaciaConn = Connection::InsertUpdateCreateDelete($queryDeleteRezervacia, [$dealid], defaultDB);
    if (gettype($queryDeleteRezervaciaConn) !== "integer") {
        $errors[] = "Nepodarilo sa zmazať údaje v tabuľke [rezervacia]. " . $queryDeleteRezervaciaConn;
    }

    $queryUpdateCashFlow = "UPDATE cashflow SET stav = ? WHERE dealid = ?";
    $queryUpdateCashFlowConn = Connection::InsertUpdateCreateDelete($queryUpdateCashFlow, [1, $dealid], defaultDB);
    if (gettype($queryUpdateCashFlowConn) !== "integer") {
        $errors[] = "Nepodarilo sa zmeniť údaje v tabuľke [cashflow]. " . $queryUpdateCashFlowConn;
    }

    if (empty($errors)) {
        $notification = new Notification(
            3,
            'konfirmaciacp',
            $dealid,
            'delete',
            $sess_userid,
            $username,
            "zamerCPdelete",
            json_encode(["dealid", $dealid]),
            false,
            $id_fond,
            "user",
            "Zrušil investičný zámer s ID <strong>" . $dealid . '</strong> na cenný papier pre subjekt <strong>' . $clientName["name"] . '</strong>'
        );
        $notification->createNotifcation();
    }
} else {
    $deleteKonverziaQuery = "UPDATE konverzia SET dat_pokyn = ?, logactivityid = ?, logdatatimeactivity = ? WHERE dealid = ?";
    $deleteKonverzia = Connection::InsertUpdateCreateDelete($deleteKonverziaQuery, [$currentDate, 11, $currentDate, $dealid], defaultDB);
    if (gettype($deleteKonverzia) !== "integer") {
        $errors[] = "Nepodarilo sa zmeniť údaje v tabuľke [konverzia]. " . $deleteKonverzia;
    }

    if (empty($errors)) {
        $notification = new Notification(
            11,
            'konverzia',
            $dealid,
            'delete',
            $sess_userid,
            $username,
            "zamerKonvdelete",
            json_encode(["dealid", $dealid]),
            false,
            $id_fond,
            "user",
            "Zrušil investičný zámer s ID <strong>" . $dealid . '</strong> na konverziu pre subjekt <strong>' . $clientName["name"]. '</strong>'
        );
        $notification->createNotifcation();
    }
}

if (empty($errors)) { ?>
    <div id="toast-success"
        class="flex border-green-300 border items-center w-full max-w-sm p-4 mb-4 text-green-500 bg-white absolute right-5 rounded-lg shadow dark:text-gray-400 dark:bg-gray-800"
        role="alert">
        <div
            class="inline-flex items-center justify-center flex-shrink-0 w-8 h-8 text-green-500 bg-green-100 rounded-lg dark:bg-green-800 dark:text-green-200">
            <svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                viewBox="0 0 20 20">
                <path
                    d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 8.207-4 4a1 1 0 0 1-1.414 0l-2-2a1 1 0 0 1 1.414-1.414L9 10.586l3.293-3.293a1 1 0 0 1 1.414 1.414Z" />
            </svg>
            <span class="sr-only">Check icon</span>
        </div>
        <div class="ms-3 text-sm font-semibold">Investičný zámer bol úspešne odstránený.</div>
        <button type="button"
            class="ms-auto -mx-1.5 -my-1.5 bg-white text-gray-400 hover:text-gray-900 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex items-center justify-center h-8 w-8 dark:text-gray-500 dark:hover:text-white dark:bg-gray-800 dark:hover:bg-gray-700"
            data-dismiss-target="#toast-success" aria-label="Close">
            <span class="sr-only">Close</span>
            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
            </svg>
        </button>
    </div>
    <script>
        setTimeout(() => {
            htmx.ajax('GET', window.location.pathname + window.location.search, {
                target: "#pageContentMain",
            });
        }, 1500);
    </script>
    <?php
} else { ?>
    <div id="toast-danger"
        class="flex items-center w-full max-w-sm p-4 mb-4 text-white absolute right-5 bg-red-500 rounded-lg shadow-lg"
        role="alert">
        <?php ?>
        <div
            class="inline-flex items-center justify-center flex-shrink-0 w-8 h-8 text-red-500 bg-red-100 rounded-lg dark:bg-red-800 dark:text-red-200">
            <svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                viewBox="0 0 20 20">
                <path
                    d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 11.793a1 1 0 1 1-1.414 1.414L10 11.414l-2.293 2.293a1 1 0 0 1-1.414-1.414L8.586 10 6.293 7.707a1 1 0 0 1 1.414-1.414L10 8.586l2.293-2.293a1 1 0 0 1 1.414 1.414L11.414 10l2.293 2.293Z" />
            </svg>
            <span class="sr-only">Error icon</span>
        </div>
        <div class="ms-3 text-sm font-bold">Investičný zámer sa nepodarilo odstrániť kvôli týmto chybám:
            <pre>
                                                                <?php print_r($errors); ?>
                                                    </pre>
        </div>
        <button type="button"
            class="ms-auto -mx-1.5 -my-1.5 text-white hover:text-gray-900 transition-all rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex items-center justify-center h-8 w-8 dark:text-gray-500 dark:hover:text-white dark:bg-gray-800 dark:hover:bg-gray-700"
            data-dismiss-target="#toast-danger" aria-label="Close">
            <span class="sr-only">Close</span>
            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
            </svg>
        </button>
    </div>
    <script>
        setTimeout(() => {
            $("#toast-danger").fadeOut();
        }, 2500);
    </script>
<?php } ?>
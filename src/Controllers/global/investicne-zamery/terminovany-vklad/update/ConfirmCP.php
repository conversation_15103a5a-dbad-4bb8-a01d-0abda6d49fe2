<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
require_once "/home/<USER>/www/src/lib/notifications/Notification.class.php";

$kurz = $_POST["limitkurz"];
$typ = $_POST["typ_pokynu"];
$partnerid = $_POST["partnerid"];
$datumobchodu = $_POST["datumobchodu"];
$casobchodu = $_POST["casobchodu"];
$sess_userid = $_SESSION["user"]["data"]["userid"];
$username = $_SESSION["user"]["data"]["username"];
$kusovreal = $_POST["zostatok"];
$dealid = $_POST["dealid"];
$limitprice = $_POST["limitprice"];
$errors = [];



if (isset($_SESSION["client"])) {
	$fondid = $_SESSION["client"]["fondid"];
} elseif ($_SESSION["mode"]["mode"] === "admin") {
	$fondid = 1;
} else {
	$fondid = 0;
}

$price = (float) $kusovreal * (float) ($kurz / 100);

$datum_cas_obchodu = $datumobchodu . " " . $casobchodu;

$queryUpdate1 = "UPDATE konfirmaciacp SET partnerid = ?, limitkurz = ?, kusovreal = ?, datum_cas_obchodu = ?, typ_pokynu = ?, limitprice = ? WHERE dealid = ?";
$queryUpdate1Conn = Connection::InsertUpdateCreateDelete($queryUpdate1, [$partnerid, $kurz, $kusovreal, $datum_cas_obchodu, $typ, $price, $dealid], defaultDB);
if (gettype($queryUpdate1Conn) !== "integer") {
	$errors[] = "QueryUpdate1 ERROR: " . $queryUpdate1Conn;
}

$queryUpdate2 = "UPDATE konfirmaciacp SET logactivityid = ?, logdatatimeactivity = ? WHERE dealid = ?";
$queryUpdate2Conn = Connection::InsertUpdateCreateDelete($queryUpdate2, [10, date("Y-m-d"), $dealid], defaultDB);
if (gettype($queryUpdate2Conn) !== "integer") {
	$errors[] = "QueryUpdate2 ERROR: " . $queryUpdate2Conn;
}

if ($eqid === "Fond") {
	$insertIntoMajetokQuery = "insert into majetoktoday
		(OBRATID, DESTINACIA, SUBJEKTID, KODOBRATU, UCTOVNYKOD, EQID, KODAKTIVA, UCETAKTIVA, JEDNOTKA, POCET, MENA, MD_D, 
		OBRATDATETIMEREAL, OBRATDATATIMEZAUCTOVANE) 
		select majetok_today_obratid.nextval, 'konfirmaciacp', pd.subjektid, k.kodobratu, k.uctovnykod,k.equid, kcp.currencyidtrade, kcp.cubu, kcp.currencyidtrade, 
		pd.transsuma, kcp.currencyidtrade, k.md_d, t.datum, t.datum
		from
			konfirmaciacp kcp, today t, kodobratumd_d k, POOLDETAIL pd, pool p
		where
			kcp.druhobchodu = 'nakup' and
			k.kodobratu = 126 and
			k.md_d = 1 and
			kcp.dealid = ? and
			p.dealid = kcp.dealid and
			p.poolid = pd.poolid and
			pd.subjektid = t.fondid";
	$insertIntoMajetok = Connection::InsertUpdateCreateDelete($insertIntoMajetokQuery, [$dealid], defaultDB);
	if (gettype($insertIntoMajetok) !== "integer") {
		$errors[] = "InsertIntoMajetok ERROR: " . $insertIntoMajetok;
	}

	$insertIntoMajetokCestaQuery = "insert into majetokcesta (dealid,tranza,sparovanie,destinacia,
									 subjektid,eqid,kodaktiva,ucetaktiva,
									 jednotka,mena,pocet,cestafrom,cestatill,
									 popis,in_out)
		 						(select dealid, 1, dealid,'konfirmaciacp',
		 							 subjektid,'BU',currencyidtrade,cubu,
		 							 currencyidtrade,currencyidtrade,limitprice,expfrom,datumfv,
		 							 'nákup fondu',0 from konfirmaciacp where druhobchodu = 'nakup' and dealid = ? )";

	$insertIntoMajetokCesta = Connection::InsertUpdateCreateDelete($insertIntoMajetokCestaQuery, [$dealid], defaultDB);
	if (gettype($insertIntoMajetokCesta) !== "integer") {
		$errors[] = "InsertIntoMajetokCesta ERROR: " . $insertIntoMajetokCesta;
	}
	if (gettype($insertIntoMajetokCest) !== "integer") {
		$errors[] = "InsertIntoMajetokCesta ERROR: " . $insertIntoMajetokCesta;
	}
}

if (empty($errors)) {
	$notification = new Notification(10, 'konfirmaciacp', $dealid, 'konfPass', $sess_userid, $username, "zamerCPToKonf", json_encode(["dealid", $dealid]), true, $fondid);
	$notification->createNotifcation();
	echo "success";
} else {
	print_r($errorrs);
	echo "error1";
}
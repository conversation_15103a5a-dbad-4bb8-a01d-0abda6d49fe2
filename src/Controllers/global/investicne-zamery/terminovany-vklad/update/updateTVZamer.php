<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
require_once "/home/<USER>/www/src/lib/notifications/Notification.class.php";

$dealidseq = $_POST["dealidseq"];
$dealid = $_POST["dealid"];

$logid1 = Connection::getDataFromDatabase("SELECT nextval('s_processlog')", defaultDB)[1][0]["nextval"];
$lastactivityid = 6;

$sess_userid = $_SESSION["user"]["data"]["userid"];
$username = $_SESSION["user"]["data"]["username"];
$agentIDQuery = Connection::getDataFromDatabase("select agentid from users where userid=$sess_userid", defaultDB);
$agentid = $agentIDQuery[1][0]["agentid"];

$year = date("Y");
$queryPool = [];
$partnerid = $_POST["partnerid"];
$cond1 = $_POST["urocenie"];
$cond3 = $_POST["autoprolognacia"];
$cond4 = $_POST["kapitalizovanie"];
$cond5 = $_POST["predcasnyvyber"];
$cond6 = $_POST["sankcia"];
$cubist = $_POST["prevodistiny"];
$cuburok = $_POST["prevoduroku"];
$dan = $_POST["dan"];
$dk_td = $_POST["datekonf"];
$ir_td = $_POST["sadzba"];
$iv_b = $_POST["urokbrutto"];
$iv_n = $_POST["uroknetto"];
$k_td = $_POST["datesplatnost"];
$mena = $_POST["mena"];
$pd_td = $_POST["dobaviazanosti"];
$z_td = $_POST["datestart"];

if (isset($_SESSION["client"])) {
    $id_fond = $_SESSION["client"]["fondid"];
} elseif ($_SESSION["mode"]["mode"] === "admin") {
    $id_fond = 1;
} else {
    $id_fond = 0;
}

$sum_td = $_POST["istina"];
$cub = $_POST["ucet"];

$queryPool[] = [
    "query" => "UPDATE konfirmaciaktv SET vs = ?, logactivityid = ?, partnerid = ?, logdatatimeactivity = ?, loguserid = ?, cond1 = ?,
            cond3 = ?, cond4 = ?, cond5 = ?, cond6 = ?, cub = ?, cubist = ?, cuburok = ?, dan = ?, dk_td = ?, ir_td = ?, iv_b = ?, iv_n = ?, k_td = ?, mena = ?,
            pd_td = ?, z_td = ?, subjektid = ?, sum_td = ? WHERE dealid = ?",
    "params" => [
        '',
        $lastactivityid,
        (int) $partnerid,
        date("Y-m-d"),
        $sess_userid,
        $cond1,
        (intval($cond3) == 1 ? "t" : "f"),
        (intval($cond4) == 1 ? "t" : "f"),
        (intval($cond5) == 1 ? "t" : "f"),
        (intval($cond6) == 1 ? "t" : "f"),
        $cub,
        $cubist,
        $cuburok,
        $dan,
        $dk_td,
        $ir_td,
        $iv_b,
        $iv_n,
        $k_td,
        $mena,
        $pd_td,
        $z_td,
        $id_fond,
        (int) $sum_td,
        $dealid
    ],
    "db" => defaultDB,
    "name" => "update konfirmaciaktv"
];

$queryPool[] = [
    "query" => "UPDATE rezervacia SET obratdatetime = ?, pocet = ? WHERE dealid = ?",
    "params" => [date("Y-m-d"), $sum_td, $dealid],
    "db" => defaultDB,
    "name" => "update rezervacia"
];

if ($id_fond != 0) {
    $queryPool[] = [
        "query" => "INSERT INTO cashflow
    (dealid, tranza, in_out, sparovanie, destinacia, subjektid, eqid, kodaktiva, ucetaktiva, jednotka, mena, pocet, datum, datumzmeny, poznamka, stav)
    VALUES
    (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
        "params" => [
            $dealid,
            0,
            0,
            null,
            'konfirmaciaktv',
            $id_fond,
            'BU',
            $mena,
            $cub,
            $mena,
            $mena,
            $sum_td,
            $z_td,
            date("Y-m-d"),
            'Úhrada za KTV',
            0
        ],
        "db" => defaultDB,
        "name" => "insert into cashflow 01"
    ];

    $queryPool[] = [
        "query" => "INSERT INTO cashflow
    (dealid, tranza, in_out, sparovanie, destinacia, subjektid, eqid, kodaktiva, ucetaktiva, jednotka, mena, pocet, datum, datumzmeny, poznamka, stav)
    VALUES
    (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
        "params" => [
            $dealid,
            0,
            1,
            null,
            'konfirmaciaktv',
            $id_fond,
            'BU',
            $mena,
            $cub,
            $mena,
            $mena,
            $sum_td,
            $k_td,
            date("Y-m-d"),
            'pripísanie istiny z KTV',
            0
        ],
        "db" => defaultDB,
        "name" => "insert into cashflow 02"
    ];

    $queryPool[] = [
        "query" => "INSERT INTO cashflow
    (dealid, tranza, in_out, sparovanie, destinacia, subjektid, eqid, kodaktiva, ucetaktiva, jednotka, mena, pocet, datum, datumzmeny, poznamka, stav)
    VALUES
    (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
        "params" => [
            $dealid,
            0,
            1,
            null,
            'konfirmaciaktv',
            $id_fond,
            'BU',
            $mena,
            $cub,
            $mena,
            $mena,
            $iv_n,
            $k_td,
            date("Y-m-d"),
            'pripísanie úroku z KTV',
            0
        ],
        "db" => defaultDB,
        "name" => "insert into cashflow 03"
    ];

    $queryPool[] = [
        "query" => "INSERT INTO cashflow
    (dealid, tranza, in_out, sparovanie, destinacia, subjektid, eqid, kodaktiva, ucetaktiva, jednotka, mena, pocet, datum, datumzmeny, poznamka, stav)
    VALUES
    (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
        "params" => [
            $dealid,
            0,
            1,
            null,
            'konfirmaciaktv',
            $id_fond,
            'TV',
            $mena,
            'TV',
            $mena,
            $mena,
            $sum_td,
            $z_td,
            date("Y-m-d"),
            'majetkové pripísanie KTV',
            0
        ],
        "db" => defaultDB,
        "name" => "insert into cashflow 04"
    ];

    $queryPool[] = [
        "query" => "INSERT INTO cashflow
    (dealid, tranza, in_out, sparovanie, destinacia, subjektid, eqid, kodaktiva, ucetaktiva, jednotka, mena, pocet, datum, datumzmeny, poznamka, stav)
    VALUES
    (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
        "params" => [
            $dealid,
            0,
            0,
            null,
            'konfirmaciaktv',
            $id_fond,
            'TV',
            $mena,
            'TV',
            $mena,
            $mena,
            $sum_td,
            $k_td,
            date("Y-m-d"),
            'majetkové odpísanie KTV',
            0
        ],
        "db" => defaultDB,
        "name" => "insert into cashflow 05"
    ];
}

if (empty($errors)) {
    $notification = new Notification(2, 'konfirmaciaktv', $dealid, 'zamerKTVupdate', $sess_userid, $username, "zamerKTVupdate", json_encode(["dealid", $dealid]), false, $id_fond);
    $notification->createNotifcation();
    ?>
    <div id="toast-success"
        class="flex border-green-300 border items-center w-full max-w-sm p-4 mb-4 text-green-500 bg-white rounded-lg shadow dark:text-gray-400 dark:bg-gray-800"
        role="alert">
        <div
            class="inline-flex items-center justify-center flex-shrink-0 w-8 h-8 text-green-500 bg-green-100 rounded-lg dark:bg-green-800 dark:text-green-200">
            <svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                viewBox="0 0 20 20">
                <path
                    d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 8.207-4 4a1 1 0 0 1-1.414 0l-2-2a1 1 0 0 1 1.414-1.414L9 10.586l3.293-3.293a1 1 0 0 1 1.414 1.414Z" />
            </svg>
            <span class="sr-only">Check icon</span>
        </div>
        <div class="ms-3 text-sm font-semibold">Investičný zámer bol úspešne aktualizovaný.</div>
        <button type="button"
            class="ms-auto -mx-1.5 -my-1.5 bg-white text-gray-400 hover:text-gray-900 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex items-center justify-center h-8 w-8 dark:text-gray-500 dark:hover:text-white dark:bg-gray-800 dark:hover:bg-gray-700"
            data-dismiss-target="#toast-success" aria-label="Close">
            <span class="sr-only">Close</span>
            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
            </svg>
        </button>
    </div>
    <script>
        setTimeout(() => {
            window.location.href = "/investicne-zamery";
        }, 1500);
    </script>
<?php } else { ?>
    <div id="toast-danger"
        class="flex items-center w-full max-w-xs p-4 mb-4 text-gray-500 bg-white rounded-lg shadow dark:text-gray-400 dark:bg-gray-800"
        role="alert">
        <?php ?>
        <div
            class="inline-flex items-center justify-center flex-shrink-0 w-8 h-8 text-red-500 bg-red-100 rounded-lg dark:bg-red-800 dark:text-red-200">
            <svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                viewBox="0 0 20 20">
                <path
                    d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 11.793a1 1 0 1 1-1.414 1.414L10 11.414l-2.293 2.293a1 1 0 0 1-1.414-1.414L8.586 10 6.293 7.707a1 1 0 0 1 1.414-1.414L10 8.586l2.293-2.293a1 1 0 0 1 1.414 1.414L11.414 10l2.293 2.293Z" />
            </svg>
            <span class="sr-only">Error icon</span>
        </div>
        <div class="ms-3 text-sm font-normal">Investičný zámer sa nepodarilo aktualizovať kvôli týmto chybám:
            <pre>
                                                    <?php print_r($errors); ?>
                                            </pre>
        </div>
        <button type="button"
            class="ms-auto -mx-1.5 -my-1.5 bg-white text-gray-400 hover:text-gray-900 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex items-center justify-center h-8 w-8 dark:text-gray-500 dark:hover:text-white dark:bg-gray-800 dark:hover:bg-gray-700"
            data-dismiss-target="#toast-danger" aria-label="Close">
            <span class="sr-only">Close</span>
            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
            </svg>
        </button>
    </div>
<?php }
?>
<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
require_once "/home/<USER>/www/src/lib/notifications/Notification.class.php";

$sess_userid = $_SESSION["user"]["data"]["userid"];
$username = $_SESSION["user"]["data"]["username"];
$eqid = $_POST["eqid"];

$year = date("Y");
$spravca = $_SESSION["mode"]["mode"] === "global" ? false : true;
$total = 0;
$poolErrors = [];
$errors = [];
$id_pool = $_POST["poolid"];

//NEW columns for obchodny dennik
$assigneduserid = $_POST["assigneduser"];
$pokyn_ako = $_POST["pokyn_ako"];
$datumpokynu = $_POST["datumpokynu"];

if (isset($_POST["poolData"]) && $_POST["poolData"] !== "") {
    $id_fonds = $_POST["poolData"];
    $id_fonds = json_decode($id_fonds);
    foreach ($id_fonds as $key => $value) {
        $suma = $value->suma;
        $total += $suma;
        if ($suma > 0) {
            $fondid = $value->subjektid;
            $query = "INSERT INTO pooldetail (subjektid, poolid, transsuma, ks) VALUES (?,?,?,?)";
            $insertINTODetail = Connection::InsertUpdateCreateDelete($query, [$fondid, $id_pool, $suma, $suma], defaultDB);
            if ($insertINTODetail !== 1) {
                array_push($poolErrors, "Error vkladania do pooldetail;");
            }
        }
    }
}

$druhobchodu = $_POST["action"] === "buy" ? "nakup" : "predaj";
$queryPool = [];
$kusov = $_POST["pocetkusov"];
$limitkurz = $_POST["aktualnykurz"] ? $_POST["aktualnykurz"] : $_POST["limitnykurz"];
$limitprice = $_POST["investedprice"] ? $_POST["investedprice"] : $_POST["limitprice"];
$limitprice = str_replace(' ', '', $limitprice);
$limitprice = number_format((float) $limitprice, 2, '.', '');
$expfrom = $_POST["platnostod"];
$exptill = $_POST["platnostdo"];
$datpok = $_POST["datumpokynu"];
$cubu = $_POST["spravbu"];
$cum = $_POST["cum"];
$partnerid = $_POST["partnerid"];
$datumvysporf = $_POST["datumvysporf"];
$datumvysporm = $_POST["datumvysporm"];
$percentoPoplatku = $_POST["percentopoplatku"];
$ric = $_POST["ric"];
$currtrade = $_POST["mena"];



switch ($eqid) {
    case ("Bonds"):
        //$config['AUV']['KalendarSplacania']
        if ($ric === "koko") {

            $auvwork = " (e.nominalemisie * COALESCE((
						 select kupon*faktor 
						 from floatkupon fk 
						 where fk.isincurrric = r.isincurrric and
					 		 fk.datefrom<='$exptill'
					 		 and fk.datetill>='$exptill' 
				),kupon) / 100)";
        } else {
            $auvwork = "(e.nominalemisie * COALESCE((
					 select kupon from floatkupon fk where fk.isincurrric = r.isincurrric and
					 		 fk.datefrom<=(to_date('$exptill','YYYY-MM-DD') ) 
					 		 and fk.datetill>=(to_date('$exptill','YYYY-MM-DD') ) 
				),kupon) / 100)";
        }
        break;
    case ("Shares"):
        $auvwork = 0;
        break;
    case ("Fonds"):
        $auvwork = 0;
        break;
    case ("Depo"):
        $auvwork = 0;
        break;
    default:
        $auvwork = 0;
        break;
}

if (isset($_SESSION["client"])) {
    $id_fond = $_SESSION["client"]["fondid"];
} else {
    $id_fond = 0;
}

if ($spravca) {
    $fondname = Connection::getDataFromDatabase("SELECT nazov as name FROM spravca WHERE spravcaid = 1", defaultDB)[1][0]["name"];
}

$evidencnyDatum = Connection::getDataFromDatabase("SELECT datum as today, TO_CHAR(datum, 'YYYYMMDD') as datednes FROM today WHERE fondid = $id_fond", defaultDB)[1][0];
$today = $evidencnyDatum["today"];
$datednes = $evidencnyDatum["datednes"];

$dealid = Connection::getDataFromDatabase("SELECT nextval('s_konfirmaciacp')", defaultDB)[1][0]["nextval"];
$dealidseq = Connection::getDataFromDatabase("SELECT id FROM ididentify WHERE popis = 'konfirmaciacp'", defaultDB);
$dealidseq = $dealid . $dealidseq[1][0]["id"];

$queryPool[] = [
    "query" => "INSERT INTO konfirmaciacp (dealid, dealidseq, subjektid, eqid, isin, druhobchodu, kusov, currencyidtrade, ric, limitkurz, limitprice, expfrom, exptill, datpok, cubu, cum,
    userid, kusovreal, tranza, logactivityid, loguserid, logdatatimeactivity, auvwork, verif, partnerid, datumfv, datummv, poplatokpercent, assigneduserid, pokyn_ako, datum_pokynu) 
    SELECT 
        $dealidseq,
        $dealid,
        $id_fond,
        '$eqid',
        e.isin,
        '$druhobchodu',
        $kusov,
        c.currencytrade,
        r.ric,
        $limitkurz,
        $limitprice,
        '$expfrom',
        '$exptill',
        '$datpok',
        '$cubu',
        '$cum',
        $sess_userid,
        0,
        0,
        4,
        $sess_userid,
        current_timestamp,
        $auvwork,
        3,
        $partnerid,
        '$datumvysporf',
        '$datumvysporm',
        $percentoPoplatku,
        $assigneduserid,
        $pokyn_ako,
        '$datumpokynu'
    FROM dbequity e, dbequitycurr c, dbequitycurrric r
    WHERE e.isin = c.isin AND c.isincurr = r.isincurr AND r.isincurrricid = $ric",
    "params" => [],
    "db" => defaultDB,
    "name" => "insert into konfirmaciacp"
];

$queryPool[] = [
    "query" => "INSERT into dennikpm
        (
            userid,
            dealid,
            destinacia,
            poradovecislo,
            cislo
        )
        values
        (
            $sess_userid,
            $dealidseq,
            'konfirmaciacp',
            (
                select COALESCE(max(poradovecislo),0) + 1
                from dennikpm
                where userid=$sess_userid
            ),
            (
                (select COALESCE(max(poradovecislo),0) + 1
                from dennikpm
                where userid=$sess_userid
                )
            )::char || lpad('$year',2,'*') || lpad((
                            select agentid::char
                            from users
                            where userid=$sess_userid
                            ),4,'*')
        )",
    "params" => [],
    "db" => defaultDB,
    "name" => "insert into dennikpm"
];


if ($druhobchodu === "predaj") {
    $insertIntoRezervacia = "INSERT INTO rezervacia
                (dealid, destinacia, eqid, kodaktiva,ucetaktiva,jednotka, mena, pocet, obratdatetime, subjektid)
                (select $dealidseq,
                    'konfirmaciacp',
                    '$eqid',
                    r.isincurrric,
                    $cum,
                    'ks',
                    c.currencytrade,
                    $kusov,
                    current_timestamp,
                    $id_fond
                from
                    dbequity e, dbequitycurr c, dbequitycurrric r
                where
                    e.isin = c.isin and
                    c.isincurr = r.isincurr and
                    r.isincurrricid = $ric
                )";
} else {
    if ($eqid == "Bonds") {
        $rez_pocet = "$limitprice + ($kusov * e.nominalemisie * COALESCE((
             select kupon from floatkupon fk where fk.isincurrric = r.isincurrric and
                      fk.datefrom<=(DATE '$exptill' + interval '3 days')
                      and fk.datetill>=(DATE '$exptill' + interval '3 days')
        ),kupon) / 100)";
    } else if ($eqid == "Shares" or $eqid == "Fonds" or $eqid == "Depo") {
        $rez_pocet = "$limitprice";
    }
    $insertIntoRezervacia = "INSERT INTO rezervacia
                (dealid, destinacia, eqid, kodaktiva,ucetaktiva,jednotka, mena, pocet, obratdatetime, subjektid)
                (select $dealidseq,
                    'konfirmaciacp',
                    'BU',
                    c.currencytrade,
                    '$cubu',
                    c.currencytrade,
                    c.currencytrade,
                    $rez_pocet,
                    current_timestamp,
                    $id_fond
                from
                    dbequity e, dbequitycurr c, dbequitycurrric r
                where
                    e.isin = c.isin and
                    c.isincurr = r.isincurr and
                    r.isincurrricid = $ric
                )";
}

$queryPool[] = [
    "query" => $insertIntoRezervacia,
    "params" => [],
    "db" => defaultDB,
    "name" => "insert into rezervacia"
];

if ($eqid == "Shares" || $eqid == "Bonds" || ($eqid == "Fonds" && $druhobch == "predaj")) {
    $je_nakup = ($druhobch == "nakup") ? 1 : 0;
    $je_predaj = ($druhobch == "predaj") ? 1 : 0;
    $poplatok_sql = "(1 " . ($druhobch == "nakup" ? "+" : "-") . " " . $percentoPoplatku . "/100)";

    $date_auv = ($druhobch == "nakup") ? $exptill : $expfrom;
    switch ($eqid) {
        case "Bonds":
            $auv_ks = "(e.nominalemisie * COALESCE((
                 select kupon from floatkupon fk where fk.isincurrric = r.isincurrric and
                          fk.datefrom<='$date_auv' 
                          and fk.datetill>='$date_auv'
            ),kupon) / 100)";
            break;
        default:
            $auv_ks = 0;
            break;
    }
    if ($id_fond == 0) {
        $queryPool[] = [
            "query" => "UPDATE pool SET destinacia = ?, dealid = ? WHERE poolid = ?",
            "params" => ["konfirmaciacp", $dealidseq, $id_pool],
            "db" => defaultDB,
            "name" => "update pool"
        ];

        $queryPool[] = [
            "query" => "INSERT INTO cashflow(dealid, tranza, 
                                 in_out, sparovanie, destinacia, 
                                 subjektid, eqid, kodaktiva, ucetaktiva, 
                                 jednotka, mena, pocet, datum, datumzmeny, poznamka, stav)
                            (select $dealidseq,
                                0, $je_predaj, Null, 'konfirmaciacp', 
                                p.subjektid, 'BU', '$currtrade', f.cub, 
                                '$currtrade', '$currtrade', transsuma, 
                                '$datumvysporf', CURRENT_DATE, 'konfirmacia cp na BU', 0
                            from pooldetail p, fondsbu f where p.poolid = $id_pool and p.subjektid = f.fondid and f.mena = '$currtrade')
                            ",
            "params" => [],
            "db" => defaultDB,
            "name" => "insert into cashflow 01"
        ];

        $queryPool[] = [
            "query" => "INSERT INTO cashflow(dealid, tranza, 
                                in_out, sparovanie, destinacia, subjektid, eqid, kodaktiva, ucetaktiva, jednotka, mena, pocet, datum, datumzmeny, poznamka, stav)
            (select $dealidseq,
                0, $je_nakup, Null, 'konfirmaciacp', p.subjektid, '$eqid', 
                (select isincurrric from dbequitycurrric where isincurrricid = $ric), 
                $cum, 'ks', 
                '$currtrade', p.ks, 
                '$datumvysporm', CURRENT_DATE, 'konfirmacia cp v majetku', 0
            from pooldetail p, fondsbu f where p.poolid = $id_pool and p.subjektid = f.fondid and f.mena = '$currtrade')
            ",
            "params" => [],
            "db" => defaultDB,
            "name" => "insert into cashflow 02"
        ];
    } else {
        $suma_bu = 0;
        if ($eqid == "Fonds") {
            $suma_bu = "$limitprice";
        } else {
            $suma_bu = "$limitprice*$poplatok_sql+($auv_ks*" . $kusov . ")";
        }

        $queryPool[] = [
            "query" => "INSERT INTO cashflow(dealid, tranza, in_out, sparovanie, destinacia, subjektid, eqid, kodaktiva, ucetaktiva, jednotka, mena, pocet, datum, datumzmeny, poznamka, stav)
            (select $dealidseq,
                0, $je_predaj, Null, 'konfirmaciacp', $id_fond, 'BU', c.currencytrade, '$cubu', c.currencytrade,
                c.currencytrade, $suma_bu, to_date('$datumvysporf','YYYY-MM-DD'), CURRENT_DATE, 'konfirmacia cp na BU', 0
            from
                dbequity e, dbequitycurr c, dbequitycurrric r
            where
                e.isin = c.isin and
                c.isincurr = r.isincurr and
                r.isincurrricid = $ric
            ) 
        ",
            "params" => [],
            "db" => defaultDB,
            "name" => "insert into cashflow 123"
        ];

        $queryPool[] = [
            "query" => "INSERT INTO cashflow(dealid, tranza, in_out, sparovanie, destinacia, subjektid, eqid, kodaktiva, ucetaktiva, jednotka, mena, pocet, datum, datumzmeny, poznamka, stav)
            (select $dealidseq,
                0, $je_nakup, Null, 'konfirmaciacp', $id_fond, '$eqid', r.isincurrric, '$cum', 'ks', 
                c.currencytrade, $kusov, to_date('$datumvysporm','YYYY-MM-DD'), CURRENT_DATE, 'konfirmacia cp v majetku', 0
            from
                dbequity e, dbequitycurr c, dbequitycurrric r
            where
                e.isin = c.isin and
                c.isincurr = r.isincurr and
                r.isincurrricid = $ric
            )",
            "params" => [],
            "db" => defaultDB,
            "name" => "insert into cashflow 1234"
        ];
    }
} else if ($eqid == "Fonds") {
    $je_nakup = ($druhobch == "nakup") ? 1 : 0;
    $je_predaj = ($druhobch == "predaj") ? 1 : 0;
    if ($je_nakup)
        $pocet = "floor((transsuma-transsuma*" . $percentoPoplatku . "/100.0)/$limitkurz)";
    else if ($je_predaj)
        $pocet = "ks";

    if ($id_fond == 0) {	//pooling
        $queryPool[] = [
            "query" => "UPDATE pool SET destinacia = ?, dealid = ? WHERE poolid = ?",
            "params" => ["konfirmaciacp", $dealidseq, $id_pool],
            "db" => defaultDB,
            "name" => "update pool"
        ];

        $queryPool[] = [
            "query" => "INSERT INTO cashflow(dealid, tranza, 
                                 in_out, sparovanie, destinacia, 
                                 subjektid, eqid, kodaktiva, ucetaktiva, 
                                 jednotka, mena, pocet, datum, datumzmeny, poznamka, stav)
                            (select $dealidseq,
                                0, $je_predaj, Null, 'konfirmaciacp', 
                                p.subjektid, 'BU', '$currtrade', f.cub, 
                                '$currtrade', '$currtrade', transsuma, 
                                to_date('$datumvysporf','YYYY-MM-DD'), CURRENT_DATE, 'konfirmacia cp na BU', 0
                            from pooldetail p, fondsbu f where p.poolid = $id_pool and p.subjektid = f.fondid and f.mena = '$currtrade')
                            ",
            "params" => [],
            "db" => defaultDB,
            "name" => "insert into cashflow POOLING1"
        ];

        $queryPool[] = [
            "query" => "INSERT INTO cashflow(dealid, tranza, 
                                in_out, sparovanie, destinacia, subjektid, eqid, kodaktiva, ucetaktiva, jednotka, mena, pocet, datum, datumzmeny, poznamka, stav)
            (select $dealidseq,
                0, $je_nakup, Null, 'konfirmaciacp', p.subjektid, '$eqid', 
                (select isincurrric from dbequitycurrric where isincurrricid = $ric), 
                '$cum', 'ks', 
                '$currtrade', $pocet, 
                to_date('$datumvysporm','YYYY-MM-DD'), CURRENT_DATE, 'konfirmacia cp v majetku', 0
            from pooldetail p, fondsbu f where p.poolid = $id_pool and p.subjektid = f.fondid and f.mena = '$currtrade')
            ",
            "params" => [],
            "db" => defaultDB,
            "name" => "insert into cashflow POOLING2"
        ];
    } else {	// bez poolingu
        $queryPool[] = [
            "query" => "INSERT INTO cashflow
        (dealid, tranza, in_out, sparovanie, destinacia, subjektid, eqid, kodaktiva, ucetaktiva, jednotka, mena, pocet, datum, datumzmeny, poznamka, stav)
        (select $dealidseq,
                    0, $je_predaj, Null, 'konfirmaciacp', $id_fond, 'BU', c.currencytrade, '$cubu', c.currencytrade,
                    c.currencytrade, $limitprice, to_date('$datumvysporf','YYYY-MM-DD'), CURRENT_DATE, 'konfirmacia cp na BU', 0
                from
                    dbequity e, dbequitycurr c, dbequitycurrric r
                where
                    e.isin = c.isin and
                    c.isincurr = r.isincurr and
                    r.isincurrricid = $ric
                )
        ",
            "params" => [],
            "db" => defaultDB,
            "name" => "insert into cashflow HEEEEEEEEEEJ"
        ];

        $queryPool[] = [
            "query" => "INSERT INTO cashflow
        (dealid, tranza, in_out, sparovanie, destinacia, subjektid, eqid, kodaktiva, ucetaktiva, jednotka, mena, pocet, datum, datumzmeny, poznamka, stav)
        (select $dealidseq,
                    0, $je_nakup, Null, 'konfirmaciacp', $id_fond, '$eqid', r.isincurrric, $cum, 'ks', 
                    c.currencytrade, " . floor(($limitprice - $limitprice * $percentoPoplatku / 100.0) / $limitkurz) . ", to_date('$datumvysporm','YYYY-MM-DD'), CURRENT_DATE, 'konfirmacia cp v majetku', 0
                from
                    dbequity e, dbequitycurr c, dbequitycurrric r
                where
                    e.isin = c.isin and
                    c.isincurr = r.isincurr and
                    r.isincurrricid = $ric
                ) ",
            "params" => [],
            "db" => defaultDB,
            "name" => "insert into cashflow BUM"
        ];
    }
}

$transaction = Connection::runTransaction(defaultDB, null, $queryPool);
if (str_contains($transaction, "poriadku") && empty($poolErrors)) {
    switch ($eqid) {
        case "Bonds":
            $popis = "dlhopisu";
            break;
        case "Shares":
            $popis = "akcie";
            break;
        case "Fonds":
            $popis = "fondu";
            break;
    }

    $notification = new Notification(
        4,
        'konfirmaciacp',
        $dealidseq,
        'create',
        $sess_userid,
        $username,
        "zamerCPCreate",
        json_encode(["dealid", $dealidseq]),
        true,
        $id_fond,
        "user",
        "Vytvoril investičný zámer s ID <strong>" . $dealidseq . '</strong> na ' . $druhobchodu . ' ' . $popis . ' so sumou <strong>'
        . number_format($limitprice, 2, ".", " ") . " " . $currtrade . '</strong>' . " pre subjekt <strong>" . $fondid . '</strong>'
    );
    $notification->createNotifcation();
    ?>
    <div id="toast-success"
        class="flex border-green-300 border items-center w-full max-w-sm p-4 mb-4 text-green-500 bg-white absolute right-5 rounded-lg shadow dark:text-gray-400 dark:bg-gray-800"
        role="alert">
        <div
            class="inline-flex items-center justify-center flex-shrink-0 w-8 h-8 text-green-500 bg-green-100 rounded-lg dark:bg-green-800 dark:text-green-200">
            <svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                viewBox="0 0 20 20">
                <path
                    d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 8.207-4 4a1 1 0 0 1-1.414 0l-2-2a1 1 0 0 1 1.414-1.414L9 10.586l3.293-3.293a1 1 0 0 1 1.414 1.414Z" />
            </svg>
            <span class="sr-only">Check icon</span>
        </div>
        <div class="ms-3 text-sm font-semibold">Investičný zámer bol úspešne vytvorený.</div>
        <button type="button"
            class="ms-auto -mx-1.5 -my-1.5 bg-white text-gray-400 hover:text-gray-900 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex items-center justify-center h-8 w-8 dark:text-gray-500 dark:hover:text-white dark:bg-gray-800 dark:hover:bg-gray-700"
            data-dismiss-target="#toast-success" aria-label="Close">
            <span class="sr-only">Close</span>
            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
            </svg>
        </button>
    </div>
    <script>
        setTimeout(() => {
            htmx.ajax("GET", "/investicne-zamery", {
                target: "#pageContentMain",
            });
        }, 2000);
    </script>
<?php } else {
    ?>
    <div id="toast-danger"
        class="flex items-center w-full max-w-sm p-4 mb-4 text-white absolute right-5 bg-red-500 rounded-lg shadow-lg"
        role="alert">
        <div
            class="inline-flex items-center justify-center flex-shrink-0 w-8 h-8 text-red-500 bg-red-100 rounded-lg dark:bg-red-800 dark:text-red-200">
            <svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                viewBox="0 0 20 20">
                <path
                    d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 11.793a1 1 0 1 1-1.414 1.414L10 11.414l-2.293 2.293a1 1 0 0 1-1.414-1.414L8.586 10 6.293 7.707a1 1 0 0 1 1.414-1.414L10 8.586l2.293-2.293a1 1 0 0 1 1.414 1.414L11.414 10l2.293 2.293Z" />
            </svg>
            <span class="sr-only">Error icon</span>
        </div>
        <div class="ms-3 text-sm font-bold">Investičný zámer sa nepodarilo vytvoriť kvôli týmto chybám:
            <pre>
                                                                <?php print_r($transaction); ?>
                                                    </pre>
        </div>
        <button type="button"
            class="ms-auto -mx-1.5 -my-1.5 text-white hover:text-gray-900 transition-all rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex items-center justify-center h-8 w-8 dark:text-gray-500 dark:hover:text-white dark:bg-gray-800 dark:hover:bg-gray-700"
            data-dismiss-target="#toast-danger" aria-label="Close">
            <span class="sr-only">Close</span>
            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
            </svg>
        </button>
    </div>
    <script>
        setTimeout(() => {
            $("#toast-danger").fadeOut();
        }, 2500);
    </script>
<?php }
?>
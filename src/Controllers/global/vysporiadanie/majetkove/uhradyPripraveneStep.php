<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
require_once "/home/<USER>/www/src/lib/notifications/Notification.class.php";

//Kontrola zmeny
$kontrola = Connection::getDataFromDatabase("SELECT kodobratu, (logdatatimeactivity - '$logdatatimeactivity') as rozdiel FROM uhrada WHERE id = $uhradaid", defaultDB)[1][0];
$kodobratu = $kontrola["kodobratu"];
$rozdiel = $kontrola["rozdiel"];

if ($rozdiel != 0) {
    $changedID = $uhradaid;
}

$obratID = Connection::getDataFromDatabase("SELECT nextval('majetok_today_obratid')", defaultDB)[1][0]["nextval"];
if ($id_fond != 0) {
    $queryPool[] = [
        "query" => "INSERT INTO majetoktoday 
                (obratid, destinacia,subjektid,kodobratu, uctovnykod,eqid,kodaktiva,ucetaktiva,jednotka,pocet,mena,md_d, obratdatetimereal,obratdatatimezauctovane)
                (select $obratID, 'uhradaobratid',subjektid,uhrada.kodobratu+300,ko.uctovnykod,'BU',mena, cub, mena, suma, mena,ko.md_d,CURRENT_DATE,
                (select datum from today where fondid = $id_fond) from uhrada, kodobratumd_d ko where id = $uhradaid and ko.kodobratu=uhrada.kodobratu+300)",
        "params" => [],
        "db" => defaultDB,
        "name" => "Insert into majetoktoday1 zapis s md_d = 1"
    ];
} else {
    //Pooling
    if ($kodobratu != 332) {
        //rekonfirmacia spolocnej uhrady poplatku
        if ($kodobratu == 350) {
            $queryPool[] = [
                "query" => "INSERT INTO majetoktoday 
                    ( obratid,destinacia, subjektid,kodobratu, uctovnykod, eqid,kodaktiva,ucetaktiva,jednotka,pocet,mena,md_d, obratdatetimereal,obratdatatimezauctovane)
                    (select max($mtid), max('uhradaobratid'),pr.fondid,max(u.kodobratu+300),ko.uctovnykod,max('BU'),u.mena, u.cub, u.mena, sum(pr.suma), u.mena,ko.md_d,max(CURRENT_DATE),
                     max((select datum from today where fondid = pr.fondid)) 
									from uhrada u, kodobratumd_d ko, poplatok_register_links p, poplatok_register pr 
									where 
										u.id = $uhradaid 
										and ko.kodobratu=u.kodobratu+300
										and p.uhrada_id = u.id
										and pr.id = p.poplatok_register_id
									group by 
						  				  destinacia,pr.fondid,ko.uctovnykod,u.mena,u.cub,ko.md_d
										)",
                "params" => [],
                "db" => defaultDB,
                "name" => "Insert into majetoktoday ked je kodobratu 350"
            ];
        }
        if ($kodobratu != 334) {
            if (in_array($kodobratu, [302, 310, 311, 312, 313, 314, 343, 344, 345, 346, 347, 348])) {
                $obratIDs = [];
                $detail = Connection::getDataFromDatabase("SELECT * FROM fondsfeesstore WHERE dealid = $dealid AND mena = '$mena'", defaultDB)[1];
                foreach ($detail as $d) {
                    $obratid = Connection::getDataFromDatabase("SELECT nextval('majetok_today_obratid')", defaultDB)[1][0]["nextval"];
                    $obratIDs[] = $obratid;
                    $suma = $d["suma"];
                    $fondid = $d["fondid"];

                    $queryPool[] = [
                        "query" => "INSERT INTO majetoktoday 
                    (obratid, destinacia,subjektid,kodobratu, uctovnykod,eqid,kodaktiva,ucetaktiva,jednotka,pocet,mena,md_d, obratdatetimereal,obratdatatimezauctovane)
                    (select $obratid, 'uhradaobratid',$fondid,uhrada.kodobratu+300,ko.uctovnykod,'BU',mena, cub, mena, $suma, mena,ko.md_d,CURRENT_DATE,
                    (select datum from today where fondid = $fondid) from uhrada, kodobratumd_d ko where id = $uhradaid and ko.kodobratu=uhrada.kodobratu+300)",
                        "params" => [],
                        "db" => defaultDB,
                        "name" => "Insert into majetoktoday kodobratu 302, 310, 311, 312, 313, 314, 343, 344, 345, 346, 347, 348"
                    ];

                    $queryPool[] = [
                        "query" => "INSERT INTO fondsfeesobratid (dealid, obratid) VALUES (?,?)",
                        "params" => [$dealid, $obratid],
                        "db" => defaultDB,
                        "name" => "Insert into fondsfeesobratid"
                    ];

                    $queryPool[] = [
                        "query" => "INSERT INTO uhradaobratid (id, obratid) VALUES (?,?)",
                        "params" => [$uhradaid, $obratid],
                        "db" => defaultDB,
                        "name" => "Insert into uhradaobratid"
                    ];
                }
                $pass_uhradaobratid = true;
            } else {
                $queryPool[] = [
                    "query" => "INSERT INTO majetoktoday 
                        (obratid, destinacia,subjektid,kodobratu, uctovnykod,eqid,kodaktiva,ucetaktiva,jednotka,pocet,mena,md_d, obratdatetimereal,obratdatatimezauctovane)
                        select $obratID, 'uhradaobratid', pdr.subjektid,u.kodobratu+300,k.uctovnykod,'BU',u.mena, u.cub, u.mena, transsumareal, 
								u.mena,k.md_d,CURRENT_DATE, (select datum from today where fondid = $id_fond)
							from 
								uhrada u, pool p, pooldetailreal pdr,kodobratumd_d k
								where u.id = $uhradaid and p.poolid = pdr.poolid and
								p.dealid = u.dealid and u.destinacia like '%'||p.destinacia||'%' and
								pdr.tranza = u.tranza and k.kodobratu=u.kodobratu+300 and u.logactivityid<>3",
                    "params" => [],
                    "db" => defaultDB,
                    "name" => "Insert into majetoktoday kodobratu 302, 310, 311, 312, 313, 314, 343, 344, 345, 346, 347, 348"
                ];
            }
        } else {
            $queryPool[] = [
                "query" => "INSERT INTO majetoktoday 
                    ( obratid,destinacia, subjektid,kodobratu, uctovnykod, eqid,kodaktiva,ucetaktiva,jednotka,pocet,mena,md_d, obratdatetimereal,obratdatatimezauctovane)
                    select $obratID, 'uhradaobratid', pdr.subjektid,u.kodobratu+300,k.uctovnykod,'BU',u.mena, u.cub, u.mena, pdr.transsumareal, 
                                    u.mena,k.md_d,CURRENT_DATE, (select datum from today where fondid = $id_fond)
						from 
							uhrada u, pool p, pooldetailreal pdr,kodobratumd_d k, konverzia ko
							where u.id = $uhradaid and p.poolid = pdr.poolid and
							p.dealid = u.dealid and u.destinacia like '%'||p.destinacia||'%' and
							pdr.tranza = u.tranza and k.kodobratu=u.kodobratu+300 and
							ko.dealid = u.dealid and u.logactivityid<>3",
                "params" => [],
                "db" => defaultDB,
                "name" => "Insert into majetoktoday kodobratu 332"
            ];
        }
    } else {
        $queryPool[] = [
            "query" => "INSERT INTO majetoktoday 
        ( obratid,destinacia, subjektid,kodobratu, uctovnykod, eqid,kodaktiva,ucetaktiva,jednotka,pocet,mena,md_d, obratdatetimereal,obratdatatimezauctovane)
        select $obratID, 'uhradaobratid', pdr.subjektid,u.kodobratu+300,k.uctovnykod,'BU',u.mena, u.cub, u.mena, pdr.transsuma,
						u.mena,k.md_d,CURRENT_DATE, (select datum from today where fondid = $id_fond)
					from 
						uhrada u, pool p, pooldetail pdr,kodobratumd_d k
						where u.id = $uhradaid and p.poolid = pdr.poolid and
						p.dealid = u.dealid and u.destinacia like '%'||p.destinacia||'%' and
						u.tranza = 1 and k.kodobratu=u.kodobratu+300 and u.logactivityid<>3",
            "params" => [],
            "db" => defaultDB,
            "name" => "Insert into majetoktoday kodobratu 332"
        ];
    }
}

$obratyBuID = Connection::getDataFromDatabase("SELECT nextval('s_obratybu')", defaultDB)[1][0]["nextval"];

$queryPool[] = [
    "query" => "INSERT INTO obratybu 
        ( id, subjektid,cub,mena,suma,vs,ss,ks,forma,cubpartnera,nazpartnera,obratdatetime,logactivityid,loguserid, logdatatimeactivity,krdb)
        (select $obratyBuID, subjektid,cub,mena,suma,vs,ss,ks,forma,cubpartnera,nazpartnera,to_char(TO_DATE('$akt_splatnost', 'DD.MM.YYYY'), 'MM-DD-YYYY')::date, 15, $sess_userid,CURRENT_DATE, 0 from uhrada where id = $uhradaid)",
    "params" => [],
    "db" => defaultDB,
    "name" => "Insert into obratybu"
];

if (!isset($obratIDs)) {
    $queryPool[] = [
        "query" => "INSERT INTO obratybuobratid (obratid, id) VALUES (?,?)",
        "params" => [$obratID, $obratyBuID],
        "db" => defaultDB,
        "name" => "Insert into obratybuobratid"
    ];
} else {
    foreach ($obratIDs as $obratid) {
        $queryPool[] = [
            "query" => "INSERT INTO obratybuobratid (obratid, id) VALUES (?,?)",
            "params" => [$obratid, $obratyBuID],
            "db" => defaultDB,
            "name" => "Insert into obratybuobratid"
        ];
    }
}

//Rekonfirmacia CP
$queryPool[] = [
    "query" => "UPDATE rekonfirmaciacp SET datvysporiadaniabureal = '$akt_splatnost' WHERE dealid = '$dealid' AND tranza = '$tranza'",
    "params" => [],
    "db" => defaultDB,
    "name" => "Update rekonfirmaciacp"
];

//Konto podielnik
$queryPool[] = [
    "query" => "INSERT INTO kontopodielnik 
    (podielnikid,obratid,fondid,kodaktiva,ucetaktiva,jednotka,mena,pocet,krdb,popis,obratdatetimereal,obratdatatimezauctovanie,logactivityid)
    (select to_number(u.vs,'9999999999'), $obratID, u.subjektid, u.mena, u.cub, u.mena, u.mena, u.suma, 0, 'Vyber podielnika',to_char(TO_DATE('$akt_splatnost', 'DD.MM.YYYY'), 'MM-DD-YYYY')::date, (select datum from today where fondid = $id_fond), 6
				from uhrada u
				where
					u.id = $uhradaid and
					u.kodobratu in (303)
			)",
    "params" => [],
    "db" => defaultDB,
    "name" => "Insert into kontopodielnik"
];

//notifikacia odtial bola presunutá aby sa vytvorila až ked celá transakcia prebehne v poriadku

$queryPool[] = [
    "query" => "INSERT INTO kontopodielnik 
    (podielnikid,obratid,fondid,kodaktiva,ucetaktiva,jednotka,mena,pocet,krdb,popis,obratdatetimereal,obratdatatimezauctovanie,logactivityid)
    (select u.vs::integer, $obratID, u.subjektid, u.mena, u.cub, u.mena, u.mena, u.suma, 0, 'Presun - vyber podielnika',to_char(TO_DATE('$akt_splatnost', 'DD.MM.YYYY'), 'MM-DD-YYYY')::date, CURRENT_DATE, 7
				from uhrada u
				where
					u.id = $uhradaid and
					u.kodobratu in (306)
			)",
    "params" => [],
    "db" => defaultDB,
    "name" => "Insert into kontopodielnik"
];

//notifikacia odtial bola presunutá aby sa vytvorila až ked celá transakcia prebehne v poriadku

$IDcko = Connection::getDataFromDatabase("SELECT u.ss as ziadostpresunid
				from uhrada u
				where u.id = $uhradaid
				and u.kodobratu in (306)", defaultDB)[1][0]["ziadostpresunid"];
if ($IDcko != null) {
    $queryPool[] = [
        "query" => "UPDATE ziadostpresun SET logactivityid = ?, loguserid = ?, logdatatimeactivity = ? WHERE ziadostpresunid = $IDcko",
        "params" => [7, $sess_userid, date("Y-m-d")],
        "db" => defaultDB,
        "name" => "Update ziadostpresun"
    ];
}

//VYPLATA VYNOSOV
$queryPool[] = [
    "query" => "INSERT INTO kontopodielnik 
    (podielnikid,obratid,fondid,kodaktiva,ucetaktiva,jednotka,mena,pocet,krdb,popis,obratdatetimereal,obratdatatimezauctovanie,logactivityid)
    (select u.vs::integer, $obratID, u.subjektid, u.mena, u.cub, u.mena, u.mena, u.suma, 0, 'Výplata výnosu',to_char(TO_DATE('$akt_splatnost', 'DD.MM.YYYY'), 'MM-DD-YYYY')::date, (select datum from today where fondid=u.subjektid), 5
				from uhrada u
				where
					u.id = $uhradaid and
					u.kodobratu in (307)
			)",
    "params" => [],
    "db" => defaultDB,
    "name" => "Insert into kontopodielnik"
];

//notifikacia odtial bola presunutá aby sa vytvorila až ked celá transakcia prebehne v poriadku

//Vratenie poplatku
$queryPool[] = [
    "query" => "INSERT INTO kontopodielnik 
    (podielnikid,obratid,fondid,kodaktiva,ucetaktiva,jednotka,mena,pocet,krdb,popis,obratdatetimereal,obratdatatimezauctovanie,logactivityid)
    (
				select kp.podielnikid,$obratID,kp.fondid,kp.kodaktiva,kp.ucetaktiva,kp.jednotka,kp.mena,kp.pocet,
					0,
					'Úhrada vrátenia poplatku',
					CURRENT_DATE,
					to_char(TO_DATE('$akt_splatnost', 'DD.MM.YYYY'), 'MM-DD-YYYY')::date,
					9
				from kontopodielnik kp, uhrada u
				where
					u.id = $uhradaid and
					kp.obratid = u.dealid and
					u.kodobratu in (315)
			)",
    "params" => [],
    "db" => defaultDB,
    "name" => "Insert into kontopodielnik"
];

$queryPool[] = [
    "query" => "INSERT INTO kontopodielnik 
    (podielnikid,obratid,fondid,kodaktiva,ucetaktiva,jednotka,mena,pocet,krdb,popis,obratdatetimereal,obratdatatimezauctovanie,logactivityid)
    (
				select kp.podielnikid,$obratID,kp.fondid,kp.kodaktiva,kp.ucetaktiva,kp.jednotka,kp.mena,kp.pocet,
					0,
					'Úhrada vrátenia vkladu',
					CURRENT_DATE,
					to_char(TO_DATE('$akt_splatnost', 'DD.MM.YYYY'), 'MM-DD-YYYY')::date,
					9
				from kontopodielnik kp, uhrada u
				where
					u.id = $uhradaid and
					kp.obratid::varchar = u.vs and
					u.kodobratu in (316)
			)",
    "params" => [],
    "db" => defaultDB,
    "name" => "Insert into kontopodielnik"
];

//notifikacia odtial bola presunutá aby sa vytvorila až ked celá transakcia prebehne v poriadku

if (!$pass_uhradaobratid) {
    $queryPool[] = [
        "query" => "INSERT INTO uhradaobratid (id,obratid) values (?,?)",
        "params" => [$uhradaid, $obratID],
        "db" => defaultDB,
        "name" => "Insert into uhradaobratid"
    ];
}

//UHRADA - Update plog
$queryPool[] = [
    "query" => "UPDATE uhrada SET logactivityid = ?, logdatatimeactivity = CURRENT_DATE WHERE id = ?",
    "params" => [9, $uhradaid],
    "db" => defaultDB,
    "name" => "Update uhrada"
];

//notifikacia odtial bola presunutá aby sa vytvorila až ked celá transakcia prebehne v poriadku

// DELETE FROM MAJETOKCESTA
$queryPool[] = [
    "query" => "DELETE FROM majetokcesta WHERE dealid = ? AND tranza = ? AND subjektid = ? AND in_out = 0 AND popis NOT IN ('presun')",
    "params" => [$dealid, $tranza, $id_fond],
    "db" => defaultDB,
    "name" => "Delete from majetokcesta"
];

//UPDATE MAJETOKCESTA - Update pri presune
$queryPool[] = [
    "query" => "UPDATE majetokcesta mc
			set
				in_out = 1,
				subjektid = (select fondidnew from ziadostpresun where ziadostpresunid = mc.dealid ),
				ucetaktiva =
					(
						select cub
						from fondsbu
						where
							fondid = (select fondidnew from ziadostpresun where ziadostpresunid = mc.dealid) and
							mena = (select mena from ziadostpresun where ziadostpresunid = mc.dealid)
					)
			where
				dealid=(select dealid from uhrada where id=$uhradaid) and
				tranza=(select tranza from uhrada where id=$uhradaid) and
				subjektid = (select subjektid from uhrada where id=$uhradaid) and
				in_out=0 and
				popis in ('presun')",
    "params" => [],
    "db" => defaultDB,
    "name" => "Update majetokcesta"
];

//ZAPIS do fondfeesstore pre vybrane uhrady
$kodyobratu = "310,311,312,313,314,343,344,345,346,347,348";

$queryPool[] = [
    "query" => "UPDATE fondsfeesstore SET logactivityid = ?
    WHERE dealid in (select dealid from uhrada where id = $uhradaid and kodobratu in ($kodyobratu))",
    "params" => [5],
    "db" => defaultDB,
    "name" => "Update fondsfeesstore"
];

//notifikacia odtial bola presunutá aby sa vytvorila až ked celá transakcia prebehne v poriadku

if ($id_fond != 0) {
    if ($kodobratu === 332) {
        $queryPool[] = [
            "query" => "INSERT INTO cashflow
            (dealid, tranza, in_out, sparovanie, destinacia, subjektid, eqid, kodaktiva, ucetaktiva, jednotka, mena, pocet, datum, datumzmeny, poznamka, stav)
            select r.dealid, 0, 1, Null, 'konfirmaciacp', u.subjektid, 'BU', u.mena, r.cubu, u.mena, u.mena, u.suma, r.exptill, 
						CURRENT_DATE, 'Úhrada nákupu cp z BU', 0 
						from uhrada u, konfirmaciacp r 
						where u.id=$uhradaid and u.dealid=r.dealid and u.logactivityid<>3",
            "params" => [],
            "db" => defaultDB,
            "name" => "Insert into cashflow"
        ];

        $queryPool[] = [
            "query" => "INSERT INTO cashflow
            (dealid, tranza, in_out, sparovanie, destinacia, subjektid, eqid, kodaktiva, ucetaktiva, jednotka, mena, pocet, datum, datumzmeny, poznamka, stav)
            select r.dealid, 1, 0, Null, 'konfirmaciacp', u.subjektid, 'BU', u.mena, r.cubu, u.mena, u.mena, u.suma,to_char(TO_DATE('$akt_splatnost', 'DD.MM.YYYY'), 'MM-DD-YYYY')::date, 
						CURRENT_DATE, 'Úhrada nákupu cp z BU', 0 
						from uhrada u, konfirmaciacp r
						where u.id=$uhradaid and u.dealid=r.dealid and u.logactivityid<>3",
            "params" => [],
            "db" => defaultDB,
            "name" => "Insert into cashflow"
        ];

        $queryPool[] = [
            "query" => "INSERT INTO cashflow
            (dealid, tranza, in_out, sparovanie, destinacia, subjektid, eqid, kodaktiva, ucetaktiva, jednotka, mena, pocet, datum, datumzmeny, poznamka, stav)
            select r.dealid, 1, 1, Null, 'konfirmaciacp', u.subjektid, 'BU', u.mena, r.cubu, u.mena, u.mena, u.suma,to_char(TO_DATE('$akt_splatnost', 'DD.MM.YYYY'), 'MM-DD-YYYY')::date, 
						CURRENT_DATE, 'Úhrada nákupu cp z BU', 0 
						from uhrada u, konfirmaciacp r
						where u.id=$uhradaid and u.dealid=r.dealid and u.logactivityid<>3",
            "params" => [],
            "db" => defaultDB,
            "name" => "Insert into cashflow"
        ];
    } else {
        // KTV CASHFLOW INSERT
        $queryPool[] = [
            "query" => "INSERT INTO cashflow
            (dealid, tranza, in_out, sparovanie, destinacia, subjektid, eqid, kodaktiva, ucetaktiva, jednotka, mena, pocet, datum, datumzmeny, poznamka, stav)
            (select kktv.dealid, 0, 1, Null, 'konfirmaciaktv', kktv.subjektid, 'BU', kktv.mena, kktv.cub, kktv.mena, kktv.mena, kktv.sum_td,to_char(TO_DATE('$akt_splatnost', 'DD.MM.YYYY'), 'MM-DD-YYYY')::date, CURRENT_DATE, 'Úhrada zriadenia KTV z BU', 0 
						 from uhrada u, konfirmaciaktv kktv where u.id=$uhradaid and u.logactivityid<>3
							and u.destinacia='konfirmaciaktv' and u.dealid=kktv.dealid)",
            "params" => [],
            "db" => defaultDB,
            "name" => "Insert into cashflow"
        ];

        // CP CASHFLOW INSERT
        $queryPool[] = [
            "query" => "INSERT INTO cashflow
            (dealid, tranza, in_out, sparovanie, destinacia, subjektid, eqid, kodaktiva, ucetaktiva, jednotka, mena, pocet, datum, datumzmeny, poznamka, stav)
            (select r.dealid, r.tranza, 1, Null, 'konfirmaciacp', u.subjektid, 'BU', u.mena, r.cutrade, u.mena, u.mena, r.transsuma, to_char(TO_DATE('$akt_splatnost', 'DD.MM.YYYY'), 'MM-DD-YYYY')::date, CURRENT_DATE, 'Úhrada nákupu cp z BU', 0 
						 from uhrada u, rekonfirmaciacp r where u.id=$uhradaid and u.logactivityid<>3
							and u.destinacia='rekonfirmaciacp' and u.dealid=r.dealid and u.tranza=r.tranza)",
            "params" => [],
            "db" => defaultDB,
            "name" => "Insert into cashflow"
        ];

        // Konverzia CASHFLOW INSERT
        $queryPool[] = [
            "query" => "INSERT INTO cashflow
            (dealid, tranza, in_out, sparovanie, destinacia, subjektid, eqid, kodaktiva, ucetaktiva, jednotka, mena, pocet, datum, datumzmeny, poznamka, stav)
            select 
						u.dealid, 0, 1, Null, 'konverzia', u.subjektid, 'BU', k.menadebet, k.cudebet, k.menadebet, k.menadebet, 
						u.suma,to_char(TO_DATE('$akt_splatnost', 'DD.MM.YYYY'), 'MM-DD-YYYY')::date, CURRENT_DATE, 'Storno debetnej sumy konverzie - úhrada', 0
					from konverzia k, uhrada u where 
						u.id=$uhradaid and k.dealid = u.dealid	and u.logactivityid<>3",
            "params" => [],
            "db" => defaultDB,
            "name" => "Insert into cashflow"
        ];
    }
} else {
    //Pooling
    if ($kodobratu === 332) {
        $queryPool[] = [
            "query" => "INSERT INTO cashflow
            (dealid, tranza, in_out, sparovanie, destinacia, subjektid, eqid, kodaktiva, ucetaktiva, jednotka, mena, pocet, datum, datumzmeny, poznamka, stav)
            select r.dealid, 0, 1, Null, 'konfirmaciacp', pdr.subjektid, 'BU', u.mena, r.cubu, u.mena, u.mena, pdr.transsuma, r.exptill, 
						CURRENT_DATE, 'Úhrada nákupu cp z BU', 0 
						from uhrada u, konfirmaciacp r, pool p, pooldetail pdr 
						where u.id=$uhradaid and u.dealid=r.dealid and
						u.dealid = p.dealid and u.tranza = 1 and p.poolid = pdr.poolid and
						u.destinacia like '%'||p.destinacia||'%' and u.logactivityid<>3",
            "params" => [],
            "db" => defaultDB,
            "name" => "Insert into cashflow1 a kodobratu 332"
        ];

        $queryPool[] = [
            "query" => "INSERT INTO cashflow
            (dealid, tranza, in_out, sparovanie, destinacia, subjektid, eqid, kodaktiva, ucetaktiva, jednotka, mena, pocet, datum, datumzmeny, poznamka, stav)
            select r.dealid, 1, 0, Null, 'konfirmaciacp', pdr.subjektid, 'BU', u.mena, r.cubu, u.mena, u.mena, pdr.transsuma, to_char(TO_DATE('$akt_splatnost', 'DD.MM.YYYY'), 'MM-DD-YYYY')::date, 
						CURRENT_DATE, 'Úhrada nákupu cp z BU', 0 
						from uhrada u, konfirmaciacp r, pool p, pooldetail pdr 
						where u.id=$uhradaid and u.dealid=r.dealid and
						u.dealid = p.dealid and u.tranza = 1 and p.poolid = pdr.poolid and
						u.destinacia like '%'||p.destinacia||'%' and u.logactivityid<>3",
            "params" => [],
            "db" => defaultDB,
            "name" => "Insert into cashflow2 a kodobratu 332"
        ];

        $queryPool[] = [
            "query" => "INSERT INTO cashflow
            (dealid, tranza, in_out, sparovanie, destinacia, subjektid, eqid, kodaktiva, ucetaktiva, jednotka, mena, pocet, datum, datumzmeny, poznamka, stav)
            select r.dealid, 1, 1, Null, 'konfirmaciacp', pdr.subjektid, 'BU', u.mena, r.cubu, u.mena, u.mena, pdr.transsuma, to_char(TO_DATE('$akt_splatnost', 'DD.MM.YYYY'), 'MM-DD-YYYY')::date, 
						CURRENT_DATE, 'Úhrada nákupu cp z BU', 0 
						from uhrada u, konfirmaciacp r, pool p, pooldetail pdr 
						where u.id=$uhradaid and u.dealid=r.dealid and
						u.dealid = p.dealid and u.tranza = 1 and p.poolid = pdr.poolid and
						u.destinacia like '%'||p.destinacia||'%' and u.logactivityid<>3",
            "params" => [],
            "db" => defaultDB,
            "name" => "Insert into cashflow3 a kodobratu 332"
        ];
    } else {
        //KTV CASHFLOW INSERT
        $queryPool[] = [
            "query" => "INSERT INTO cashflow
            (dealid, tranza, in_out, sparovanie, destinacia, subjektid, eqid, kodaktiva, ucetaktiva, jednotka, mena, pocet, datum, datumzmeny, poznamka, stav)
            select k.dealid, 0, 1, Null, 'konfirmaciaktv', pdr.subjektid, 'BU', k.mena, k.cub, k.mena, k.mena, pdr.transsumareal, to_char(TO_DATE('$akt_splatnost', 'DD.MM.YYYY'), 'MM-DD-YYYY')::date, 
						CURRENT_DATE, 'Úhrada zriadenia KTV z BU', 0 
						from uhrada u, konfirmaciaktv k, pool p, pooldetailreal pdr 
						where u.id=$uhradaid and u.destinacia='konfirmaciaktv' and u.dealid=k.dealid and
						u.dealid = p.dealid and u.tranza = pdr.tranza and p.poolid = pdr.poolid and
						u.destinacia like '%'||p.destinacia||'%' and u.logactivityid<>3",
            "params" => [],
            "db" => defaultDB,
            "name" => "Insert into cashflow a kodobratu 333"
        ];

        // CP CASHFLOW INSERT
        $queryPool[] = [
            "query" => "INSERT INTO cashflow
            (dealid, tranza, in_out, sparovanie, destinacia, subjektid, eqid, kodaktiva, ucetaktiva, jednotka, mena, pocet, datum, datumzmeny, poznamka, stav)
            select r.dealid, r.tranza, 1, Null, 'konfirmaciacp', pdr.subjektid, 'BU', u.mena, r.cutrade, u.mena, u.mena, pdr.transsumareal, to_char(TO_DATE('$akt_splatnost', 'DD.MM.YYYY'), 'MM-DD-YYYY')::date, 
						CURRENT_DATE, 'Úhrada nákupu cp z BU', 0 
						from uhrada u, rekonfirmaciacp r, pool p, pooldetailreal pdr 
						where u.id=$uhradaid and u.dealid=r.dealid and
						u.dealid = p.dealid and u.tranza = pdr.tranza and p.poolid = pdr.poolid and
						u.destinacia like '%'||p.destinacia||'%' and r.tranza = u.tranza and u.logactivityid<>3",
            "params" => [],
            "db" => defaultDB,
            "name" => "Insert into cashflow a kodobratu 333"
        ];

        // Konverzia CASHFLOW INSERT
        $queryPool[] = [
            "query" => "INSERT INTO cashflow
            (dealid, tranza, in_out, sparovanie, destinacia, subjektid, eqid, kodaktiva, ucetaktiva, jednotka, mena, pocet, datum, datumzmeny, poznamka, stav)
            select 
						p.dealid, 0, 1, Null, 'konverzia', pdr.subjektid, 'BU', k.menadebet, k.cudebet, k.menadebet, k.menadebet, 
						(
							CASE WHEN k.menovypar=k.menakredit||k.menadebet THEN pdr.transsumareal*k.kurz ELSE pdr.transsumareal*1/kurz END
						), to_char(TO_DATE('$akt_splatnost', 'DD.MM.YYYY'), 'MM-DD-YYYY')::date , CURRENT_DATE, 'Storno debetnej sumy konverzie - úhrada', 0
					from pooldetailreal pdr, pool p, konverzia k, uhrada u where 
						p.dealid = k.dealid and pdr.poolid = p.poolid and	
						u.id=$uhradaid and k.dealid = u.dealid	and u.logactivityid<>3",
            "params" => [],
            "db" => defaultDB,
            "name" => "Insert into cashflow a kodobratu 333"
        ];
    }
}
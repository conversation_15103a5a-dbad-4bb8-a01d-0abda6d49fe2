<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
require_once "/home/<USER>/www/src/lib/notifications/Notification.class.php";

$errors = [];

$dealid = $_POST["dealid"];
$subjektid = $_POST["fondid"];
$tranza = $_POST["tranza"];
$destinacia = $_POST["destinacia"];
$in_out = $_POST["inout"];
$eqid = $_POST["eqid"];
$mena = $_POST["mena"];
$date = $_POST["date"];
$datetill = $_POST["datetill"];

echo "SELECT isin FROM konfirmaciacp WHERE dealid = $dealid";
// $isin = Connection::getDataFromDatabase(
//     "SELECT isin FROM konfirmaciacp WHERE dealid = $dealid",
//     defaultDB
// )[1][0]["isin"];

// if (!$isin) {
//     $isin = "";
// }

$obratID = Connection::getDataFromDatabase("SELECT nextval('majetok_today_obratid')", defaultDB)[1][0]["nextval"];
echo "OBRATID ziskany z DB sekvencie: $obratID";

switch ($eqid) {
    case "Bonds":
        $column = "ksreal";
        $is_excoupon = Connection::getDataFromDatabase(
            "SELECT is_coupon_date('$isin', to_date('2025-03-27', 'YYYY-MM-DD')) as is_coupon_date",
            defaultDB
        )[1][0]["is_coupon_date"];

        echo "SELECT is_coupon_date('$isin', to_date('2025-03-27', 'YYYY-MM-DD')) as is_coupon_date" . "<br>";
        echo "IS EXCOUPON? $is_excoupon";

        if ($is_excoupon) {
            $kodobratu = "251";
        } else {
            $kodobratu = "251,252";
        }

        $destinacia = 'rekonfirmaciacp';
        $destinacia_pool = 'konfirmaciacp';
        $activityid = '15';

        echo "UPDATE rekonfirmaciacp
        SET datvysporiadaniamureal = to_char(TO_DATE('$akt_splatnost', 'DD.MM.YYYY'), 'MM-DD-YYYY')::date,
        datvysporiadaniamu = to_char(TO_DATE('$datetill', 'DD.MM.YYYY'), 'MM-DD-YYYY')::date
        WHERE dealid = $dealid AND tranza = $tranza";

        // $updateRekonfCP = Connection::InsertUpdateCreateDelete("UPDATE rekonfirmaciacp
        // SET datvysporiadaniamureal = to_char(TO_DATE('$akt_splatnost', 'DD.MM.YYYY'), 'MM-DD-YYYY')::date,
        // datvysporiadaniamu = to_char(TO_DATE('$datetill', 'DD.MM.YYYY'), 'MM-DD-YYYY')::date
        // WHERE dealid = ? AND tranza = ?", [$dealid, $tranza], defaultDB);
        // if (gettype($updateRekonfCP) !== "integer") {
        //     $errors[] = "Nepodarilo sa zmeniť datum v tabuľke [rekonfirmaciacp]. " . $updateRekonfCP;
        // }
        break;
    case "Shares":
        $column = "ksreal";
        $kodobratu = "254";
        $destinacia = 'rekonfirmaciacp';
        $destinacia_pool = 'konfirmaciacp';
        $activityid = '15';


        echo "UPDATE rekonfirmaciacp
        SET datvysporiadaniamureal = to_char(TO_DATE('$akt_splatnost', 'DD.MM.YYYY'), 'MM-DD-YYYY')::date,
        datvysporiadaniamu = to_char(TO_DATE('$datetill', 'DD.MM.YYYY'), 'MM-DD-YYYY')::date
        WHERE dealid = $dealid AND tranza = $tranza";
        // $updateRekonfCP = Connection::InsertUpdateCreateDelete("UPDATE rekonfirmaciacp
        // SET datvysporiadaniamureal = to_char(TO_DATE('$akt_splatnost', 'DD.MM.YYYY'), 'MM-DD-YYYY')::date,
        // datvysporiadaniamu = to_char(TO_DATE('$datetill', 'DD.MM.YYYY'), 'MM-DD-YYYY')::date
        // WHERE dealid = ? AND tranza = ?", [$dealid, $tranza], defaultDB);
        // if (gettype($updateRekonfCP) !== "integer") {
        //     $errors[] = "Nepodarilo sa zmeniť datum v tabuľke [rekonfirmaciacp]. " . $updateRekonfCP;
        // }

        break;
    case "Fonds":
        $column = "ksreal";
        $kodobratu = "255";
        $destinacia = 'rekonfirmaciacp';
        $destinacia_pool = 'konfirmaciacp';
        $activityid = '15';

        echo "UPDATE rekonfirmaciacp
        SET datvysporiadaniamureal = to_char(TO_DATE('$akt_splatnost', 'DD.MM.YYYY'), 'MM-DD-YYYY')::date,
        datvysporiadaniamu = to_char(TO_DATE('$datetill', 'DD.MM.YYYY'), 'MM-DD-YYYY')::date
        WHERE dealid = $dealid AND tranza = $tranza";

        // $updateRekonfCP = Connection::InsertUpdateCreateDelete("UPDATE rekonfirmaciacp
        // SET datvysporiadaniamureal = to_char(TO_DATE('$akt_splatnost', 'DD.MM.YYYY'), 'MM-DD-YYYY')::date,
        // datvysporiadaniamu = to_char(TO_DATE('$datetill', 'DD.MM.YYYY'), 'MM-DD-YYYY')::date
        // WHERE dealid = ? AND tranza = ?", [$dealid, $tranza], defaultDB);

        // if (gettype($updateRekonfCP) !== "integer") {
        //     $errors[] = "Nepodarilo sa zmeniť datum v tabuľke [rekonfirmaciacp]. " . $updateRekonfCP;
        // }

        break;
    case "Depo":
        $column = "ksreal";
        $kodobratu = "256";
        $destinacia = 'rekonfirmaciacp';
        $destinacia_pool = 'konfirmaciacp';
        $activityid = '15';

        echo "UPDATE rekonfirmaciacp
        SET datvysporiadaniamureal = to_char(TO_DATE('$akt_splatnost', 'DD.MM.YYYY'), 'MM-DD-YYYY')::date,
        datvysporiadaniamu = to_char(TO_DATE('$datetill', 'DD.MM.YYYY'), 'MM-DD-YYYY')::date
        WHERE dealid = $dealid AND tranza = $tranza";

        // $updateRekonfCP = Connection::InsertUpdateCreateDelete("UPDATE rekonfirmaciacp
        // SET datvysporiadaniamureal = to_char(TO_DATE('$akt_splatnost', 'DD.MM.YYYY'), 'MM-DD-YYYY')::date,
        // datvysporiadaniamu = to_char(TO_DATE('$datetill', 'DD.MM.YYYY'), 'MM-DD-YYYY')::date
        // WHERE dealid = ? AND tranza = ?", [$dealid, $tranza], defaultDB);

        // if (gettype($updateRekonfCP) !== "integer") {
        //     $errors[] = "Nepodarilo sa zmeniť datum v tabuľke [rekonfirmaciacp]. " . $updateRekonfCP;
        // }

        break;

    case "TD":
        $column = "transsumareal";
        $kodobratu = "253";
        $destinacia = 'konfirmaciaktv';
        $destinacia_pool = 'konfirmaciaktv';
        $activityid = '17';

        echo "UPDATE konfirmaciaktv
        SET daterek_td = to_char(TO_DATE('$akt_splatnost', 'DD.MM.YYYY'), 'MM-DD-YYYY')::date
        WHERE dealid = $dealid";

        // $updateKonfTV = Connection::InsertUpdateCreateDelete("UPDATE konfirmaciaktv
        // SET daterek_td = to_char(TO_DATE('$akt_splatnost', 'DD.MM.YYYY'), 'MM-DD-YYYY')::date
        // WHERE dealid = ?", [$dealid], defaultDB);

        // if (gettype($updateKonfTV) !== "integer") {
        //     $errors[] = "Nepodarilo sa zmeniť datum v tabuľke [konfirmaciaktv]. " . $updateKonfTV;
        // }

        break;
}

$detailMC = Connection::getDataFromDatabase("SELECT cestatill as dat,
		m.* from majetokcesta m
		where eqid like '$eqid'
		and dealid=$dealid
		and tranza=$tranza
		and lower(destinacia) like '$destinacia'", defaultDB)[1][0];

echo "DETAILMC: <br>";
print_r($detailMC);

$dealid = $detailMC["dealid"];
$kodaktiva = $detailMC["kodaktiva"];
$ucetaktiva = $detailMC["ucetaktiva"];
$jednotka = $detailMC["jednotka"];
$mena = $detailMC["mena"];
$pocet = $detailMC["pocet"];
$datetimereal = $detailMC["dat"];

if ($subjektid != 0) {
    echo "SELECT kodobratu FROM kodobratumd_d WHERE kodobratu in ($kodobratu) ORDER BY kodobratu";
    $kodobratu = Connection::getDataFromDatabase(
        "SELECT * FROM kodobratumd_d WHERE kodobratu in ($kodobratu) ORDER BY kodobratu",
        defaultDB
    )[1];

    foreach ($kodobratu as $kod) {
        $kodobratu = $kod["kodobratu"];
        $md_d = $kod["md_d"];
        $eqid = $kod["equid"];
        $uctovnykod = $kod["uctovnykod"];

        $today = Connection::getDataFromDatabase("SELECT datum from today where fondid = $subjektid", defaultDB)[1][0]["datum"];

        echo "INSERT INTO majetoktoday 
        (destinacia,eqid,jednotka,kodaktiva,kodobratu,
				 md_d,mena,obratdatatimezauctovane,obratdatetimereal,
				 obratid,pocet,subjektid,ucetaktiva,uctovnykod
				)
        VALUES ($destinacia,
            $eqid,
            $jednotka,
            $kodaktiva,
            $kodobratu,
            $md_d,
            $mena,
            $today,
            $datetimereal,
            $obratID,
            $pocet,
            $subjektid,
            $ucetaktiva,
            $uctovnykod)";

        // $insertIntoMajetokToday = Connection::InsertUpdateCreateDelete("INSERT INTO majetoktoday 
        // (destinacia,eqid,jednotka,kodaktiva,kodobratu,
		// 		 md_d,mena,obratdatatimezauctovane,obratdatetimereal,
		// 		 obratid,pocet,subjektid,ucetaktiva,uctovnykod
		// 		)
        // VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?)", [
        //     $destinacia,
        //     $eqid,
        //     $jednotka,
        //     $kodaktiva,
        //     $kodobratu,
        //     $md_d,
        //     $mena,
        //     $today,
        //     $datetimereal,
        //     $obratID,
        //     $pocet,
        //     $subjektid,
        //     $ucetaktiva,
        //     $uctovnykod
        // ], defaultDB);

        // if (gettype($insertIntoMajetokToday) !== "integer") {
        //     $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [majetoktoday]. " . $insertIntoMajetokToday;
        // }
    }
} else {
    echo "INSERT INTO majetoktoday 
    (destinacia,eqid,jednotka,kodaktiva,kodobratu,
				 md_d,mena,obratdatatimezauctovane,obratdatetimereal,
				 obratid,pocet,subjektid,ucetaktiva,uctovnykod
				)
                select '$destinacia',equid,'$jednotka','$kodaktiva',kodobratu,
				 md_d,'$mena',$today,to_date('$datetimereal','YYYY-MM-DD'),
				 $obratid,$column,pd.subjektid,'$ucetaktiva',uctovnykod
				from
			 		pool p,pooldetailreal pd, kodobratumd_d
				where
					p.dealid = $dealid and
					p.destinacia = '$destinacia_pool' and
					p.poolid = pd.poolid and 
					pd.tranza = $tranza and
			 		kodobratu in ($kodobratu)";

    // $insertIntoMajetokToday = Connection::InsertUpdateCreateDelete("INSERT INTO majetoktoday 
    // (destinacia,eqid,jednotka,kodaktiva,kodobratu,
	// 			 md_d,mena,obratdatatimezauctovane,obratdatetimereal,
	// 			 obratid,pocet,subjektid,ucetaktiva,uctovnykod
	// 			)
    //             select '$destinacia',equid,'$jednotka','$kodaktiva',kodobratu,
	// 			 md_d,'$mena',$today,to_date('$datetimereal','YYYY-MM-DD'),
	// 			 $obratid,$column,pd.subjektid,'$ucetaktiva',uctovnykod
	// 			from
	// 		 		pool p,pooldetailreal pd, kodobratumd_d
	// 			where
	// 				p.dealid = $dealid and
	// 				p.destinacia = '$destinacia_pool' and
	// 				p.poolid = pd.poolid and 
	// 				pd.tranza = $tranza and
	// 		 		kodobratu in ($kodobratu)", [], defaultDB);

    // if (gettype($insertIntoMajetokToday) !== "integer") {
    //     $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [majetoktoday]. " . $insertIntoMajetokToday;
    // }
}

$majID = Connection::getDataFromDatabase("SELECT nextval('majetok_today_obratid')", defaultDB)[1][0]["nextval"];

if ($eqid === "TD") {
    //Zakomentovany kod v subore dosle_cp_zauctuj.php
} else {
    $danQuery = "COALESCE((
    SELECT
			CASE poplatokcp
                WHEN 1 THEN ff1.dan
                WHEN 2 THEN ff2.dan
                WHEN 4 THEN ff4.dan
                ELSE 0
            END
		FROM
			fondsfeesdetail ff1, fondsfeesdetail ff2,
			fondsfeesdetail ff4, dbequity de
		WHERE
			ff1.fondid = f.fondid and ff1.chargetype='vysporiadanieCPtyp1' and
			ff2.fondid = f.fondid and ff2.chargetype='vysporiadanieCPtyp2' and
			ff4.fondid = f.fondid and ff4.chargetype='vysporiadanieCPtyp4' and
			de.isin = k.isin
	),0)";

    if ($subjektid != 0) {
        $poplatokRegisterID = Connection::getDataFromDatabase("SELECT nextval('s_poplatok_register')", defaultDB)[1][0]["nextval"];
        $poplatokQuery = "COALESCE((
        SELECT
				CASE poplatokcp
                    WHEN 1 THEN ff1.suma * f_menovy_kurz_kriz(ff1.mena, s.refmena, TO_DATE('$datetimereal', 'YYYY-MM-DD'), 1)
                    WHEN 2 THEN ff2.suma * f_menovy_kurz_kriz(ff2.mena, s.refmena, TO_DATE('$datetimereal', 'YYYY-MM-DD'), 1)
                    WHEN 4 THEN ff4.suma * f_menovy_kurz_kriz(ff4.mena, s.refmena, TO_DATE('$datetimereal', 'YYYY-MM-DD'), 1)
                    ELSE 0
                END
			FROM
				fondsfeesdetail ff1, fondsfeesdetail ff2, 
				fondsfeesdetail ff4, dbequity de
			WHERE
				ff1.fondid = f.fondid and ff1.chargetype='vysporiadanieCPtyp1' and
				ff2.fondid = f.fondid and ff2.chargetype='vysporiadanieCPtyp2' and
				ff4.fondid = f.fondid and ff4.chargetype='vysporiadanieCPtyp4'
				and de.isin = k.isin),0)";
        $insertIntoPoplatokQuery = "INSERT INTO poplatok_register(id, fondid, datum, suma, mena, typ, stav, dealid, tranza)
        SELECT
				$poplatokRegisterID, k.subjektid, (select datum from today where fondid = k.subjektid), round(($poplatokQuery) ,2),
				s.refmena, 'VYROV', 0, $dealid, $tranza			
			from 
				konfirmaciacp k, fonds f, spravca s
			where 
				k.dealid=$dealid and 
				f.typpredlohy<>1 and
				k.subjektid = f.fondid AND
				(
					k.dealid not in (
						SELECT
							objektid
						FROM
							processlog
						WHERE
							destinacia = 'rekonfirmaciacp' and
							activityid = 15
					) 
				)";

        echo $poplatokQuery;
        // $insertIntoPoplatokRegister = Connection::InsertUpdateCreateDelete($insertIntoPoplatokQuery, [], defaultDB);
        // if (gettype($insertIntoPoplatokRegister) !== "integer") {
        //     $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [poplatok_register]. " . $insertIntoPoplatokRegister;
        // }
    } else {
        //TODO make it for global mode
    }
}

$sess_userid = $_SESSION["user"]["data"]["userid"];
$username = $_SESSION["user"]["data"]["username"];

// if (empty($errors)) {
//     $notification = new Notification(
//         $activityid,
//         $destinacia,
//         $dealid,
//         'majetkoveUhradaNatipovanie',
//         $sess_userid,
//         $username,
//         "majetkoveUhradaNatipovanie",
//         json_encode(["dealid", $dealid]),
//         false,
//         $id_fond
//     );
//     $notification->createNotifcation();
// }

echo "UPDATE $destinacia 
    SET logdatatimeactivity = CURRENT_DATE,
    loguserid = $sess_userid
    WHERE dealid = $dealid" . ($destinacia == 'rekonfirmaciacp' ? " AND tranza = $tranza" : "");

// $updateDestinacia = Connection::InsertUpdateCreateDelete("UPDATE $destinacia 
//     SET logdatatimeactivity = CURRENT_DATE,
//     loguserid = ?
//     WHERE dealid = ?" . ($destinacia == 'rekonfirmaciacp' ? " AND tranza = $tranza" : ""), [$sess_userid, $dealid], defaultDB);

// if (gettype($updateDestinacia) !== "integer") {
//     $errors[] = "Nepodarilo sa zmeniť logdatatimeactivity v tabuľke [$destinacia]. " . $updateDestinacia;
// }

if ($destinacia == 'rekonfirmaciacp') {
    echo "INSERT INTO rekonfirmaciacpobratid 
    (dealid, obratid, tranza) 
    VALUES ($dealid,
        $obratID,
        $tranza)";
    // $insertIntoRekonfObratID = Connection::InsertUpdateCreateDelete("INSERT INTO rekonfirmaciacpobratid 
    // (dealid, obratid, tranza) 
    // VALUES (?, ?, ?)", [
    //     $dealid,
    //     $obratID,
    //     $tranza
    // ], defaultDB);

    // if (gettype($insertIntoRekonfObratID) !== "integer") {
    //     $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [rekonfirmaciacpobratid]. " . $insertIntoRekonfObratID;
    // }


    
    if ($majID != '') {
        echo "INSERT INTO rekonfirmaciacpobratid 
        (dealid, obratid, tranza) 
        VALUES ( $dealid,
            $majID,
            $tranza)";
        // $insertIntoRekonfObratID = Connection::InsertUpdateCreateDelete("INSERT INTO rekonfirmaciacpobratid 
        // (dealid, obratid, tranza) 
        // VALUES (?, ?, ?)", [
        //     $dealid,
        //     $majID,
        //     $tranza
        // ], defaultDB);

        // if (gettype($insertIntoRekonfObratID) !== "integer") {
        //     $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [rekonfirmaciacpobratid2]. " . $insertIntoRekonfObratID;
        // }
    }

    if ($subjektid != 0) {
        echo "INSERT INTO cashflow
        (dealid, tranza, in_out, sparovanie, destinacia, subjektid, eqid, kodaktiva, ucetaktiva, 
        jednotka, mena, pocet, datum, datumzmeny, poznamka, stav)
        (select r.dealid, r.tranza, 0, Null, 'konfirmaciacp', k.subjektid, '$eqid_param',
            (select isincurrric from dbequitycurrric where ric=k.ric), r.cum, 'ks', k.currencyidtrade, r.kusovreal,
            to_date('$datetimereal','YYYY-MM-DD'), CURRENT_DATE, 'pripísanie cp na majetkový účet', 0 
		FROM rekonfirmaciacp r, konfirmaciacp k where r.dealid=$dealid and r.dealid=k.dealid 
			and r.tranza=$tranza)";
        // $insertIntoCashFlow = Connection::InsertUpdateCreateDelete("INSERT INTO cashflow
        // (dealid, tranza, in_out, sparovanie, destinacia, subjektid, eqid, kodaktiva, ucetaktiva, 
        // jednotka, mena, pocet, datum, datumzmeny, poznamka, stav)
        // (select r.dealid, r.tranza, 0, Null, 'konfirmaciacp', k.subjektid, '$eqid_param',
        //     (select isincurrric from dbequitycurrric where ric=k.ric), r.cum, 'ks', k.currencyidtrade, r.kusovreal,
        //     to_date('$datetimereal','YYYY-MM-DD'), CURRENT_DATE, 'pripísanie cp na majetkový účet', 0 
		// FROM rekonfirmaciacp r, konfirmaciacp k where r.dealid=$dealid and r.dealid=k.dealid 
		// 	and r.tranza=$tranza)", [], defaultDB);
        // if (gettype($insertIntoCashFlow) !== "integer") {
        //     $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [cashflow]. " . $insertIntoCashFlow;
        // }
    } else {
        echo "INSERT INTO cashflow
        (dealid, tranza, in_out, sparovanie, destinacia, subjektid, eqid, kodaktiva, ucetaktiva, jednotka, mena, 
				pocet, datum, datumzmeny, poznamka, stav) 
        select $dealid, r.tranza, 0, Null, 'konfirmaciacp', pdr.subjektid, '$eqid', 
        (select isincurrric from dbequitycurrric where ric=k.ric), 
			r.cum, 'ks', k.currencyidtrade, pdr.ksreal,to_date('$datetimereal','YYYY-MM-DD'), 
            CURRENT_DATE, 
			'pripísanie cp na majetkový účet', 0
			from pool p, pooldetailreal pdr, rekonfirmaciacp r, konfirmaciacp k 
			where 
				p.dealid = $dealid and p.destinacia = 'konfirmaciacp' and 
				p.poolid = pdr.poolid and p.dealid = k.dealid and 
				r.dealid=k.dealid and r.tranza=$tranza and 
				pdr.tranza = r.tranza";
        // $insertIntoCashFlow = Connection::InsertUpdateCreateDelete("INSERT INTO cashflow
        // (dealid, tranza, in_out, sparovanie, destinacia, subjektid, eqid, kodaktiva, ucetaktiva, jednotka, mena, 
		// 		pocet, datum, datumzmeny, poznamka, stav) 
        // select $dealid, r.tranza, 0, Null, 'konfirmaciacp', pdr.subjektid, '$eqid', 
        // (select isincurrric from dbequitycurrric where ric=k.ric), 
		// 	r.cum, 'ks', k.currencyidtrade, pdr.ksreal,to_date('$datetimereal','YYYY-MM-DD'), 
        //     CURRENT_DATE, 
		// 	'pripísanie cp na majetkový účet', 0
		// 	from pool p, pooldetailreal pdr, rekonfirmaciacp r, konfirmaciacp k 
		// 	where 
		// 		p.dealid = $dealid and p.destinacia = 'konfirmaciacp' and 
		// 		p.poolid = pdr.poolid and p.dealid = k.dealid and 
		// 		r.dealid=k.dealid and r.tranza=$tranza and 
		// 		pdr.tranza = r.tranza", [], defaultDB);

        // if (gettype($insertIntoCashFlow) !== "integer") {
        //     $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [cashflow]. " . $insertIntoCashFlow;
        // }
    }
}

if ($destinacia === "konfirmaciaktv") {
    if ($majID != '') {
        echo "INSERT INTO rekonfirmaciaktvobratid 
        (dealid, obratid) 
        VALUES ($dealid,
            $majID)";
        // $insertIntoRekonfObratID = Connection::InsertUpdateCreateDelete("INSERT INTO rekonfirmaciaktvobratid 
        // (dealid, obratid) 
        // VALUES (?, ?)", [
        //     $dealid,
        //     $majID
        // ], defaultDB);

        // if (gettype($insertIntoRekonfObratID) !== "integer") {
        //     $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [rekonfirmaciaktvobratid]. " . $insertIntoRekonfObratID;
        // }
    }

    echo "INSERT INTO rekonfirmaciaktvobratid 
        (dealid, obratid) 
        VALUES ( $dealid,
        $obratID)";
    // $insertIntoRekonfKTV = Connection::InsertUpdateCreateDelete("INSERT INTO rekonfirmaciaktvobratid 
    //     (dealid, obratid) 
    //     VALUES (?, ?)", [
    //     $dealid,
    //     $obratID
    // ], defaultDB);

    if ($subjektid != 0) {
        echo "INSERT INTO cashflow
        (dealid, tranza, in_out, sparovanie, destinacia, subjektid, eqid, kodaktiva, ucetaktiva, 
        jednotka, mena, pocet, datum, datumzmeny, poznamka, stav)
        (select kktv.dealid, 0, 0, Null, 'konfirmaciaktv', kktv.subjektid, 'TV', kktv.mena, kktv.cutd, kktv.mena,
        ktv.mena, kktv.sum_td, to_date('$datetimereal','YYYY-MM-DD'), CURRENT_DATE,
        'majetkové pripísanie KTV - rekonfirmácia', 0 from konfirmaciaktv kktv where kktv.dealid = $dealid)";
        // $insertIntoCashFlow = Connection::InsertUpdateCreateDelete(
        //     "INSERT INTO cashflow
        // (dealid, tranza, in_out, sparovanie, destinacia, subjektid, eqid, kodaktiva, ucetaktiva, 
        // jednotka, mena, pocet, datum, datumzmeny, poznamka, stav)
        // (select kktv.dealid, 0, 0, Null, 'konfirmaciaktv', kktv.subjektid, 'TV', kktv.mena, kktv.cutd, kktv.mena,
        // ktv.mena, kktv.sum_td, to_date('$datetimereal','YYYY-MM-DD'), CURRENT_DATE,
        // 'majetkové pripísanie KTV - rekonfirmácia', 0 from konfirmaciaktv kktv where kktv.dealid = $dealid)",
        //     [],
        //     defaultDB
        // );

        // if (gettype($insertIntoCashFlow) !== "integer") {
        //     $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [cashflow]. " . $insertIntoCashFlow;
        // }
    } else {
        echo "INSERT INTO cashflow
        (dealid, tranza, in_out, sparovanie, destinacia, subjektid, eqid, kodaktiva, ucetaktiva, jednotka, mena, 
				pocet, datum, datumzmeny, poznamka, stav)
        select $dealid, 0, 0, Null, 'konfirmaciaktv', pdr.subjektid, 'TV', k.mena, k.cutd, k.mena, k.mena, transsumareal, 
				to_date('$datetimereal','YYYY-MM-DD'), sysdate, 'majetkové pripísanie KTV - rekonfirmácia', 0
			from pool p, pooldetailreal pdr, konfirmaciaktv k
			where p.dealid = $dealid and p.destinacia = '$destinacia' and p.poolid = pdr.poolid and p.dealid = k.dealid";
        // $insertIntoCashFlow = Connection::InsertUpdateCreateDelete(
        //     "INSERT INTO cashflow
        // (dealid, tranza, in_out, sparovanie, destinacia, subjektid, eqid, kodaktiva, ucetaktiva, jednotka, mena, 
		// 		pocet, datum, datumzmeny, poznamka, stav)
        // select $dealid, 0, 0, Null, 'konfirmaciaktv', pdr.subjektid, 'TV', k.mena, k.cutd, k.mena, k.mena, transsumareal, 
		// 		to_date('$datetimereal','YYYY-MM-DD'), sysdate, 'majetkové pripísanie KTV - rekonfirmácia', 0
		// 	from pool p, pooldetailreal pdr, konfirmaciaktv k
		// 	where p.dealid = $dealid and p.destinacia = '$destinacia' and p.poolid = pdr.poolid and p.dealid = k.dealid",
        //     [],
        //     defaultDB
        // );

        // if (gettype($insertIntoCashFlow) !== "integer") {
        //     $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [cashflow]. " . $insertIntoCashFlow;
        // }
    }
}

echo "DELETE FROM majetokcesta 
WHERE dealid = $dealid AND lower(destinacia) = $destinacia AND eqid = $eqid AND tranza = $tranza";
// $deleteFromMajetokCesta = Connection::InsertUpdateCreateDelete("DELETE FROM majetokcesta 
// WHERE dealid = ? AND lower(destinacia) = ? AND eqid = ? AND tranza = ?",
//     [$dealid, $destinacia, $eqid, $tranza],
//     defaultDB
// );

// if(gettype($deleteFromMajetokCesta) !== "integer") {
//     $errors[] = "Nepodarilo sa vymazať záznam z tabuľky [majetokcesta]. " . $deleteFromMajetokCesta;
// }

if (empty($errors)) {
    ?>
    <div id="toast-success"
        class="flex items-center w-full max-w-xs p-4 mb-4 text-gray-500 bg-white rounded-lg shadow-sm dark:text-gray-400 dark:bg-gray-600"
        role="alert">
        <div
            class="inline-flex items-center justify-center shrink-0 w-8 h-8 text-green-500 bg-green-100 rounded-lg dark:bg-green-800 dark:text-green-200">
            <svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                viewBox="0 0 20 20">
                <path
                    d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 8.207-4 4a1 1 0 0 1-1.414 0l-2-2a1 1 0 0 1 1.414-1.414L9 10.586l3.293-3.293a1 1 0 0 1 1.414 1.414Z" />
            </svg>
            <span class="sr-only">Check icon</span>
        </div>
        <div class="ms-3 text-sm font-normal">Úhrada bola úspešne natipovaná.</div>
        <button type="button"
            class="ms-auto -mx-1.5 -my-1.5 bg-white text-gray-400 hover:text-gray-900 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex items-center justify-center h-8 w-8 dark:text-gray-500 dark:hover:text-white dark:bg-gray-800 dark:hover:bg-gray-700"
            data-dismiss-target="#toast-success" aria-label="Close">
            <span class="sr-only">Close</span>
            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
            </svg>
        </button>
    </div>
    <script>
        setTimeout(() => {
            htmx.ajax('GET', window.location.pathname + window.location.search, {
                target: "#pageContentMain",
            });
        }, 1000);
    </script>
    <?php
} else { ?>
    <div id="toast-danger"
        class="flex items-center w-full max-w-xs p-4 mb-4 text-gray-500 bg-white rounded-lg shadow-sm dark:text-gray-400 dark:bg-gray-600"
        role="alert">
        <div
            class="inline-flex items-center justify-center shrink-0 w-8 h-8 text-red-500 bg-red-100 rounded-lg dark:bg-red-800 dark:text-red-200">
            <svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                viewBox="0 0 20 20">
                <path
                    d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 11.793a1 1 0 1 1-1.414 1.414L10 11.414l-2.293 2.293a1 1 0 0 1-1.414-1.414L8.586 10 6.293 7.707a1 1 0 0 1 1.414-1.414L10 8.586l2.293-2.293a1 1 0 0 1 1.414 1.414L11.414 10l2.293 2.293Z" />
            </svg>
            <span class="sr-only">Error icon</span>
        </div>
        <div class="ms-3 text-sm font-normal">Nepodarilo sa zaúčtovať úhradu kvôli týmto chybám</div>
        <pre>
                    <?php print_r($errors); ?>
                </pre>
        <button type="button"
            class="ms-auto -mx-1.5 -my-1.5 bg-white text-gray-400 hover:text-gray-900 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex items-center justify-center h-8 w-8 dark:text-gray-500 dark:hover:text-white dark:bg-gray-800 dark:hover:bg-gray-700"
            data-dismiss-target="#toast-danger" aria-label="Close">
            <span class="sr-only">Close</span>
            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
            </svg>
        </button>
    </div>
    <script>
        setTimeout(() => {
            $("#toast-danger").fadeOut();
        }, 2500);
    </script>
<?php }
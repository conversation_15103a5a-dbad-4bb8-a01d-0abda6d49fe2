<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
require_once "/home/<USER>/www/src/lib/notifications/Notification.class.php";
require_once "/home/<USER>/www/src/lib/notifications/Subactivity.class.php";

if ($_SESSION["mode"]["mode"] === "admin") {
    $id_fond = 1;
} elseif (isset($_SESSION["client"])) {
    $id_fond = $_SESSION["client"]["fondid"];
} else {
    $id_fond = 0;
}
$sess_userid = $_SESSION["user"]["data"]["userid"];
$username = $_SESSION["user"]["data"]["username"];

$errors = [];

$last = $_POST["last"];
$suma = $_POST["suma"];
$vs = $_POST["vs"];
$ks = $_POST["ks"];
$ss = $_POST["ss"];
$ucetpartnera = $_POST["ucetpartnera"];
$cub = $_POST["cub"];
$nazovpartnera = $_POST["nazovpartnera"];
$splatnost = $_POST["splatnost"];
$forma = $_POST["forma"];
$dealid = $_POST["dealid"];
$tranza = $_POST["tranza"];
$platba = $_POST["platba"];
$kodobratu = $_POST["kodobratu"];
$logactivityid = $_POST["logactivityid"];
$logdatatimeactivity = $_POST["logdatatimeactivity"];
$tranza = $_POST["tranza"];
$new = $_POST["new"];
$key = $_POST["key"];
$mena = $_POST["mena"];

$kodyObratu = Connection::getDataFromDatabase("SELECT * from kodobratu " . ($kodobratu ? "WHERE kodobratu = $kodobratu" : " WHERE kodobratu IN (320,321,322,324,325,326)"), defaultDB)[1];

if ($new === "0") {
    $verified = "wait";
    //kontrola na existujuci zaznam v uhrade:
    $exists = Connection::getDataFromDatabase("select u.* from uhrada u, majetokcesta mc where u.dealid=mc.dealid and u.tranza = mc.tranza and u.destinacia = mc.destinacia 
and u.subjektid=mc.subjektid and u.dealid=$dealid and u.tranza=$tranza and u.subjektid = $id_fond and u.logactivityid not in (1,2,3)", defaultDB)[1];

    if (sizeof($exists) > 0) {
        echo "errorNatipovana";
        exit;
    } else {
        $uhradaID = Connection::getDataFromDatabase("SELECT nextval('s_uhrada')", defaultDB)[1][0]["nextval"];
        $insertIntoUhrada = Connection::InsertUpdateCreateDelete("INSERT INTO uhrada (id,tranza,dealid,logdatatimeactivity,destinacia,cub,subjektid,mena,suma,vs,ss,ks,cubpartnera,nazpartnera,datesplatnost,cestatill,kodobratu,loguserid,logactivityid,cestafrom,forma)
     (select ($uhradaID || '905')::numeric,tranza,dealid,CURRENT_DATE, destinacia,'$cub',subjektid,'$mena',pocet, '$vs','$ss','$ks','$ucetpartnera','$nazovpartnera','$splatnost',cestatill,$kodobratu,$sess_userid,4, cestafrom, 0 from majetokcesta where subjektid = $id_fond and dealid=$dealid and tranza=$tranza and in_out=0)", [], defaultDB);
        if (gettype($insertIntoUhrada) !== "integer") {
            $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [uhrada] " . $insertIntoUhrada;
        }

        if ($kodobratu == 350) {
            $updatePoplatokRegister = Connection::InsertUpdateCreateDelete("UPDATE poplatok_register SET uhrada_id = ? WHERE dealid = ?", [$uhradaID . '905', $dealid], defaultDB);
            if (gettype($updatePoplatokRegister) !== "integer") {
                $errors[] = "Nepodarilo sa aktualizovať uhradu v tabuľke [poplatok_register] " . $updatePoplatokRegister;
            }
        }
    }
    $uhradaID = $uhradaID . '905';
} else {
    if ($id_fond == 1) {
        $verified = "new";
        $uhradaID = Connection::getDataFromDatabase("SELECT nextval('s_uhrada')", defaultDB)[1][0]["nextval"];
        $uhradaID = $uhradaID . '905';
        $insertIntoUhrada = Connection::InsertUpdateCreateDelete("INSERT INTO uhrada ( id,tranza,dealid, logdatatimeactivity,destinacia,cub, subjektid,mena, suma, vs, ss, ks, cubpartnera, nazpartnera, datesplatnost, cestatill,kodobratu, loguserid, logactivityid, cestafrom, forma)
    VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)", [
            $uhradaID,
            0,
            $uhradaID,
            date("Y-m-d"),
            'uhrada',
            $cub,
            $id_fond,
            $mena ? $mena : '',
            $suma,
            $vs,
            $ss,
            $ks,
            $ucetpartnera,
            $nazovpartnera,
            $splatnost,
            date("Y-m-d"),
            0,
            $sess_userid,
            4,
            date("Y-m-d"),
            0
        ], defaultDB);
        if (gettype($insertIntoUhrada) !== "integer") {
            $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [uhrada] " . $insertIntoUhrada;
        }
    }
}

if (!empty($errors)) { ?>
    <td id="newlyAdded" class="px-6 py-3" colspan="10">
        Nepodarilo sa vytvoriť novú úhradu! <br>
        Dôvody: <br>
        <div class="flex flex-col gap-2">
            <?php foreach ($errors as $key => $error) { ?>
                <span class="text-red-500"><?php echo $error; ?></span><br>
            <?php } ?>
        </div>
        <script>
            setTimeout(() => {
                $("#newlyAdded").remove();
            }, 5000);
        </script>
    </td>
    <?php exit;
} else {
    $notification = new Notification(1, 'uhrada', $uhradaID, "natipovaniePrikazu", $sess_userid, $username, "natipovaniePrikazu", json_encode(["id", $uhradaID]), false, 0);
    $notifID = $notification->createNotifcation();

    $subactivity2 = new Subactivity($notifID, 4, 'uhrada', $uhradaID, "odoslaniePrikazuNaUhradu", $sess_userid, json_encode(["id", $uhradaID]), $id_fond, "nič");
    $subactivity2->createSubActivity();
    ?>
    <script>
        htmx.ajax('GET', window.location.pathname + window.location.search, {
            target: "#pageContentMain",
        });
    </script>
<?php } ?>
<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
require_once "/home/<USER>/www/src/lib/notifications/Notification.class.php";

//UCTOVANIE - ZAPIS DO MAJETOKTODAY

$action = $_POST["action"];

if ($_SESSION["mode"]["mode"] === "admin") {
    $id_fond = 1;
} elseif (isset($_SESSION["client"])) {
    $id_fond = $_SESSION["client"]["fondid"];
} else {
    $id_fond = 0;
}

$errors = [];
$queryPool = [];
$logdatatimeactivity = $_POST["logdatatimeactivity"];
$uhradaid = $_POST["uhradaid"];
$kodobratu = $_POST["kodobratu"];
$dealid = $_POST["dealid"];
$tranza = $_POST["tranza"];
$forma = $_POST["forma"];
// KONTROLA CI UZ NEBOLA TATO PLATBA ZAUCTOVANA
$zauctovane = Connection::getDataFromDatabase("SELECT (logdatatimeactivity - '$logdatatimeactivity') as rozdiel FROM uhrada WHERE id = $uhradaid", defaultDB)[1];

if ($zauctovane[0]["rozdiel"] != 0) { ?>
    <div id="toast-danger"
        class="flex items-center w-full max-w-xs p-4 mb-4 text-gray-500 bg-white rounded-lg shadow-sm dark:text-gray-400 dark:bg-gray-600"
        role="alert">
        <div
            class="inline-flex items-center justify-center shrink-0 w-8 h-8 text-red-500 bg-red-100 rounded-lg dark:bg-red-800 dark:text-red-200">
            <svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                viewBox="0 0 20 20">
                <path
                    d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 11.793a1 1 0 1 1-1.414 1.414L10 11.414l-2.293 2.293a1 1 0 0 1-1.414-1.414L8.586 10 6.293 7.707a1 1 0 0 1 1.414-1.414L10 8.586l2.293-2.293a1 1 0 0 1 1.414 1.414L11.414 10l2.293 2.293Z" />
            </svg>
            <span class="sr-only">Error icon</span>
        </div>
        <div class="ms-3 text-sm font-normal">Úhrada bola zmenená, alebo zaučtovaná iným používateľom.</div>
        <button type="button"
            class="ms-auto -mx-1.5 -my-1.5 bg-white text-gray-400 hover:text-gray-900 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex items-center justify-center h-8 w-8 dark:text-gray-500 dark:hover:text-white dark:bg-gray-800 dark:hover:bg-gray-700"
            data-dismiss-target="#toast-danger" aria-label="Close">
            <span class="sr-only">Close</span>
            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
            </svg>
        </button>
    </div>
    <script>
        setTimeout(() => {
            $("#toast-danger").fadeOut();
        }, 2500);
    </script>
    <?php
    exit;
}

$obratID = Connection::getDataFromDatabase("SELECT nextval('majetok_today_obratid')", defaultDB)[1][0]["nextval"];

function zauctuj302()
{
    global $obratID, $uhradaid, $kodobratu, $id_fond, $queryPool, $tranza, $dealid;

    //Update rekonfcp
    $queryPool[] = [
        "query" => "UPDATE rekonfirmaciacp SET logactivityid = ? 
    WHERE dealid = (select dealid from uhrada where id = ?) AND tranza = (select tranza from uhrada where id = ?)",
        "params" => [18, $uhradaid, $uhradaid],
        "db" => defaultDB,
        "name" => "Update rekonfirmaciacp"
    ];

    if ($id_fond != 0) {
        //zapis s md_d = 1
        $queryPool[] = [
            "query" => "INSERT INTO majetoktoday (obratid, destinacia,subjektid,kodobratu, uctovnykod, eqid,kodaktiva,ucetaktiva,jednotka,pocet,mena,md_d, 
                        obratdatetimereal,obratdatatimezauctovane)
                        (select $obratID, destinacia,subjektid,u.kodobratu,ko.uctovnykod,'BU',mena, cub, mena, suma, mena,ko.md_d,CURRENT_DATE,
                        (select datum from today where fondid = $id_fond) from uhrada u, kodobratumd_d ko where id = $uhradaid and ko.md_d = 1 and ko.kodobratu = u.kodobratu)",
            "params" => [],
            "db" => defaultDB,
            "name" => "Insert into majetoktoday1 zapis s md_d = 1"
        ];

        $pocet = Connection::getDataFromDatabase("SELECT (cenaobchodu - (auvreal)) as pocet FROM rekonfirmaciacp WHERE dealid = $dealid AND tranza = $tranza)", defaultDB)[1][0]["pocet"];
        $queryPool[] = [
            "query" => "INSERT INTO majetoktoday
							(obratid, destinacia,subjektid,kodobratu, uctovnykod, eqid,kodaktiva,ucetaktiva,jednotka,pocet, mena,md_d, obratdatetimereal,obratdatatimezauctovane)
                            (select $obratID, destinacia,subjektid,u.kodobratu,ko.uctovnykod,'BU',mena, cub, mena, $pocet, mena,ko.md_d,CURRENT_DATEA,
                             (select datum from today where fondid = $id_fond) from uhrada u, kodobratumd_d ko where id = $uhradaid and ko.md_d = 0 and ko.subkodobratu = 1 and ko.kodobratu = u.kodobratu)",
            "params" => [],
            "db" => defaultDB,
            "name" => "Insert into majetoktoday2 zapis s md_d = 0 a subkodobratu = 1"
        ];

        $pocet = Connection::getDataFromDatabase("SELECT auvreal as pocet FROM rekonfirmaciacp WHERE dealid = $dealid AND tranza = $tranza)", defaultDB)[1][0]["pocet"];
        $queryPool[] = [
            "query" => "INSERT INTO majetoktoday 
                    (obratid, destinacia,subjektid,kodobratu, uctovnykod, eqid,kodaktiva,ucetaktiva,jednotka,pocet, mena,md_d, obratdatetimereal,obratdatatimezauctovane)
                    (select $obratID, destinacia,subjektid,u.kodobratu,ko.uctovnykod,'BU',mena, cub, mena, $pocet, mena,ko.md_d,CURRENT_DATE,
                    (select datum from today where fondid = $id_fond) from uhrada u, kodobratumd_d ko where id = $uhradaid and ko.md_d = 0 and ko.subkodobratu = 2 and ko.kodobratu = u.kodobratu)",
            "params" => [],
            "db" => defaultDB,
            "name" => "Insert into majetoktoday3 zapis s md_d = 0 a subkodobratu = 2"
        ];

        $pocet = Connection::getDataFromDatabase("SELECT poplatok FROM rekonfirmaciacp WHERE dealid = $dealid AND tranza = $tranza)", defaultDB)[1][0]["pocet"];
        $queryPool[] = [
            "query" => "INSERT INTO majetoktoday 
                    (obratid, destinacia,subjektid,kodobratu, uctovnykod, eqid,kodaktiva,ucetaktiva,jednotka,pocet, mena,md_d, obratdatetimereal,obratdatatimezauctovane)
                    (select $obratID, destinacia,subjektid,u.kodobratu,ko.uctovnykod,'BU',mena, cub, mena, $pocet, mena,ko.md_d,CURRENT_DATE,
                    (select datum from today where fondid = $id_fond) from uhrada u, kodobratumd_d ko where id = $uhradaid and ko.md_d = 0 and ko.subkodobratu = 3 and ko.kodobratu = u.kodobratu)",
            "params" => [],
            "db" => defaultDB,
            "name" => "Insert into majetoktoday4 zapis s md_d = 0 a subkodobratu = 3"
        ];
    } else {
        $queryPool[] = [
            "query" => "INSERT INTO majetoktoday
        (obratid, destinacia,subjektid,kodobratu, uctovnykod, eqid,kodaktiva,ucetaktiva,jednotka,pocet,mena,md_d, obratdatetimereal,obratdatatimezauctovane)
        select 
							$obratID, u.destinacia, pdr.subjektid, u.kodobratu,ko.uctovnykod,'BU', u.mena, u.cub, u.mena, pdr.transsumareal, u.mena,
							ko.md_d,CURRENT_DATE, (select datum from today where fondid = $id_fond) 
						from 
							uhrada u, kodobratumd_d ko, pool p, pooldetailreal pdr
						where 
							u.id = $uhradaid and ko.md_d = 1 and ko.kodobratu = u.kodobratu and
							p.poolid = pdr.poolid and u.tranza = pdr.tranza and
							p.dealid = u.dealid and u.destinacia like '%'||p.destinacia||'%'",
            "params" => [],
            "db" => defaultDB,
            "name" => "Insert into majetoktoday1 zapis s md_d = 1"
        ];

        $queryPool[] = [
            "query" => "INSERT INTO majetoktoday 
                    (obratid, destinacia,subjektid,kodobratu, uctovnykod, eqid,kodaktiva,ucetaktiva,jednotka,pocet,mena,md_d, obratdatetimereal,obratdatatimezauctovane)
                    select 
							$obratID, u.destinacia, pdr.subjektid, u.kodobratu,ko.uctovnykod,'BU', u.mena, u.cub, u.mena, cenaobchodu - auvreal, u.mena,
							ko.md_d,CURRENT_DATE, (select datum from today where fondid = $id_fond) 
						from 
							uhrada u, kodobratumd_d ko, pool p, pooldetailreal pdr
						where 
							u.id = $uhradaid and ko.md_d = 0 and ko.kodobratu = u.kodobratu and ko.subkodobratu = 1 and
							p.poolid = pdr.poolid and u.tranza = pdr.tranza and
							p.dealid = u.dealid and u.destinacia like '%'||p.destinacia||'%'",
            "params" => [],
            "db" => defaultDB,
            "name" => "Insert into majetoktoday2 zapis s md_d = 0 a subkodobratu = 1"
        ];

        $queryPool[] = [
            "query" => "INSERT INTO majetoktoday 
        (obratid, destinacia,subjektid,kodobratu, uctovnykod, eqid,kodaktiva,ucetaktiva,jednotka,pocet,mena,md_d, obratdatetimereal,obratdatatimezauctovane)
        select 
							$obratID, u.destinacia, pdr.subjektid, u.kodobratu,ko.uctovnykod,'BU', u.mena, u.cub, u.mena, auvreal, u.mena,
							ko.md_d,SYSDATE, (select datum from today where fondid = $id_fond) 
						from 
							uhrada u, kodobratumd_d ko, pool p, pooldetailreal pdr
						where 
							u.id = $uhradaid and ko.md_d = 0 and ko.kodobratu = u.kodobratu and ko.subkodobratu = 2 and
							p.poolid = pdr.poolid and u.tranza = pdr.tranza and
							p.dealid = u.dealid and u.destinacia like '%'||p.destinacia||'%'",
            "params" => [],
            "db" => defaultDB,
            "name" => "Insert into majetoktoday3 zapis s md_d = 0 a subkodobratu = 2"
        ];

        $queryPool[] = [
            "query" => "INSERT INTO majetoktoday 
        (obratid, destinacia,subjektid,kodobratu, uctovnykod, eqid,kodaktiva,ucetaktiva,jednotka,pocet,mena,md_d, obratdatetimereal,obratdatatimezauctovane)
        select 
							$obratID, u.destinacia, pdr.subjektid, u.kodobratu,ko.uctovnykod,'BU', u.mena, u.cub, u.mena, poplatok, u.mena,
							ko.md_d,SYSDATE, (select datum from today where fondid = $id_fond) 
						from 
							uhrada u, kodobratumd_d ko, pool p, pooldetailreal pdr
						where 
							u.id = $uhradaid and ko.md_d = 0 and ko.kodobratu = u.kodobratu and ko.subkodobratu = 3 and
							p.poolid = pdr.poolid and u.tranza = pdr.tranza and
							p.dealid = u.dealid and u.destinacia like '%'||p.destinacia||'%'",
            "params" => [],
            "db" => defaultDB,
            "name" => "Insert into majetoktoday4 zapis s md_d = 0 a subkodobratu = 3"
        ];
    }
}

/**
 * Zauctovanie fondov
 * @return void
 */
function zauctuj332()
{
    global $obratID, $uhradaid, $kodobratu, $id_fond, $errors, $tranza, $dealid;

    //Update rekonfcp
    $updateRekonfirmaciaCP = Connection::InsertUpdateCreateDelete("UPDATE rekonfirmaciacp SET logactivityid = ? 
    WHERE dealid = (select dealid from uhrada where id = ?) AND tranza = (select tranza from uhrada where id = ?)", [18, $uhradaid, $uhradaid], defaultDB);
    if (gettype($updateRekonfirmaciaCP) !== "integer") {
        $errors[] = "Nepodarilo sa zmeniť logactivityid v tabuľke [rekonfirmaciacp]. " . $updateRekonfirmaciaCP;
    }

    if ($id_fond != 0) {
        //zapis s md_d = 1
        $insertIntoMajetokToday1 = Connection::InsertUpdateCreateDelete("INSERT INTO majetoktoday 
        (obratid, destinacia,subjektid,kodobratu, uctovnykod, eqid,kodaktiva,ucetaktiva,jednotka,pocet,mena,md_d, obratdatetimereal,obratdatatimezauctovane)
        (select $obratID, destinacia,subjektid,u.kodobratu,ko.uctovnykod,'BU',mena, cub, mena, suma, mena,ko.md_d,CURRENT_DATE,
        (select datum from today where fondid = $id_fond) from uhrada u, kodobratumd_d ko where id = $uhradaid and ko.md_d = 1 and ko.kodobratu = u.kodobratu)", [], defaultDB);
        if (gettype($insertIntoMajetokToday1) !== "integer") {
            $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [majetoktoday1]. " . $insertIntoMajetokToday1;
        }

        $pocet = Connection::getDataFromDatabase("SELECT limitprice FROM konfirmaciacp WHERE dealid = $dealid", defaultDB)[1][0]["limitprice"];
        $insertIntoMajetokToday2 = Connection::InsertUpdateCreateDelete("INSERT INTO majetoktoday 
        (obratid, destinacia,subjektid,kodobratu, uctovnykod, eqid,kodaktiva,ucetaktiva,jednotka,pocet,mena,md_d, obratdatetimereal,obratdatatimezauctovane)
        (select $obratID, destinacia,subjektid,u.kodobratu,ko.uctovnykod,'BU',mena, cub, mena, $pocet, mena,ko.md_d,CURRENT_DATE,
        (select datum from today where fondid = $id_fond) from uhrada u, kodobratumd_d ko where id = $uhradaid and ko.md_d = 0 and ko.subkodobratu = 1 and ko.kodobratu = u.kodobratu)", [], defaultDB);
        if (gettype($insertIntoMajetokToday2) !== "integer") {
            $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [majetoktoday2]. " . $insertIntoMajetokToday2;
        }

        $pocet = 0;
        $insertIntoMajetokToday3 = Connection::InsertUpdateCreateDelete("INSERT INTO majetoktoday 
        (obratid, destinacia,subjektid,kodobratu, uctovnykod, eqid,kodaktiva,ucetaktiva,jednotka,pocet,mena,md_d, obratdatetimereal,obratdatatimezauctovane)
        (select $obratID, destinacia,subjektid,u.kodobratu,ko.uctovnykod,'BU',mena, cub, mena, $pocet, mena,ko.md_d,CURRENT_DATE,
        (select datum from today where fondid = $id_fond) from uhrada u, kodobratumd_d ko where id = $uhradaid and ko.md_d = 0 and ko.subkodobratu = 2 and ko.kodobratu = u.kodobratu)", [], defaultDB);
        if (gettype($insertIntoMajetokToday3) !== "integer") {
            $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [majetoktoday3]. " . $insertIntoMajetokToday3;
        }
    } else {
        $insertIntoMajetokToday = Connection::InsertUpdateCreateDelete("INSERT INTO majetoktoday 
        (obratid, destinacia,subjektid,kodobratu, uctovnykod, eqid,kodaktiva,ucetaktiva,jednotka,pocet,mena,md_d, obratdatetimereal,obratdatatimezauctovane)
        select 
							$obratID, u.destinacia, pdr.subjektid, u.kodobratu,ko.uctovnykod,'BU', u.mena, u.cub, u.mena, pdr.transsuma, u.mena,
							ko.md_d,CURRENT_DATE, (select datum from today where fondid = $id_fond) 
						from 
							uhrada u, kodobratumd_d ko, pool p, pooldetail pdr
						where 
							u.id = $uhradaid and ko.md_d = 1 and ko.kodobratu = u.kodobratu and
							p.poolid = pdr.poolid and u.tranza = 1 and
							p.dealid = u.dealid and u.destinacia like '%'||p.destinacia||'%'", [], defaultDB);
        if (gettype($insertIntoMajetokToday) !== "integer") {
            $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [majetoktoday]. " . $insertIntoMajetokToday;
        }

        $pocet = Connection::getDataFromDatabase("SELECT cenaobchodu FROM rekonfirmaciacp WHERE dealid = $dealid AND tranza = $tranza", defaultDB)[1][0]["cenaobchodu"];
        $insertIntoMajetokToday2 = Connection::InsertUpdateCreateDelete("INSERT INTO majetoktoday 
        (obratid, destinacia,subjektid,kodobratu, uctovnykod, eqid,kodaktiva,ucetaktiva,jednotka,pocet,mena,md_d, obratdatetimereal,obratdatatimezauctovane)
        select 
							$obratID, u.destinacia, pdr.subjektid, u.kodobratu,ko.uctovnykod,'BU', u.mena, u.cub, u.mena, pdr.transsuma, u.mena,
							ko.md_d,SYSDATE, (select datum from today where fondid = $id_fond) 
						from 
							uhrada u, kodobratumd_d ko, pool p, pooldetail pdr
						where 
							u.id = $uhradaid and ko.md_d = 0 and ko.subkodobratu = 1 and ko.kodobratu = u.kodobratu and
							p.poolid = pdr.poolid and u.tranza = 1 and
							p.dealid = u.dealid and u.destinacia like '%'||p.destinacia||'%'", [], defaultDB);
        if (gettype($insertIntoMajetokToday2) !== "integer") {
            $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [majetoktoday2]. " . $insertIntoMajetokToday2;
        }

        $pocet = Connection::getDataFromDatabase("SELECT poplatok FROM rekonfirmaciacp WHERE dealid = $dealid AND tranza = $tranza", defaultDB)[1][0]["poplatok"];
        $insertIntoMajetokToday3 = Connection::InsertUpdateCreateDelete("INSERT INTO majetoktoday 
        (obratid, destinacia,subjektid,kodobratu, uctovnykod, eqid,kodaktiva,ucetaktiva,jednotka,pocet,mena,md_d, obratdatetimereal,obratdatatimezauctovane)
        select 
							$obratID, u.destinacia, pdr.subjektid, u.kodobratu,ko.uctovnykod,'BU', u.mena, u.cub, u.mena, 0, u.mena,
							ko.md_d,SYSDATE, (select datum from today where fondid = $id_fond) 
						from 
							uhrada u, kodobratumd_d ko, pool p, pooldetail pdr
						where 
							u.id = $uhradaid and ko.md_d = 0 and ko.subkodobratu = 2 and ko.kodobratu = u.kodobratu and
							p.poolid = pdr.poolid and u.tranza = 1 and
							p.dealid = u.dealid and u.destinacia like '%'||p.destinacia||'%'", [], defaultDB);
        if (gettype($insertIntoMajetokToday3) !== "integer") {
            $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [majetoktoday3]. " . $insertIntoMajetokToday3;
        }
    }
}

/**
 * Zauctovanie akcii
 * @return void
 */
function zauctuj331()
{
    global $obratID, $uhradaid, $kodobratu, $id_fond, $errors, $tranza, $dealid;

    //Update rekonfcp
    $updateRekonfirmaciaCP = Connection::InsertUpdateCreateDelete("UPDATE rekonfirmaciacp SET logactivityid = ? 
    WHERE dealid = (select dealid from uhrada where id = ?) AND tranza = (select tranza from uhrada where id = ?)", [18, $uhradaid, $uhradaid], defaultDB);
    if (gettype($updateRekonfirmaciaCP) !== "integer") {
        $errors[] = "Nepodarilo sa zmeniť logactivityid v tabuľke [rekonfirmaciacp]. " . $updateRekonfirmaciaCP;
    }

    if ($id_fond != 0) {
        $insertIntoMajetokToday = Connection::InsertUpdateCreateDelete("INSERT INTO majetoktoday 
        (obratid, destinacia,subjektid,kodobratu, uctovnykod,eqid,kodaktiva,ucetaktiva,jednotka,pocet,mena,md_d, obratdatetimereal,obratdatatimezauctovane)
        (select $obratID, destinacia,subjektid,u.kodobratu,ko.uctovnykod,'BU',mena, cub, mena, suma, mena,ko.md_d,CURRENT_DATE,
        (select datum from today where fondid = $id_fond) from uhrada u, kodobratumd_d ko where id = $uhradaid and ko.md_d = 1 and ko.kodobratu = u.kodobratu)", [], defaultDB);
        if (gettype($insertIntoMajetokToday) !== "integer") {
            $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [majetoktoday]. " . $insertIntoMajetokToday;
        }

        $pocet = Connection::getDataFromDatabase("SELECT cenaobchodu FROM rekonfirmaciacp WHERE dealid = $dealid AND tranza = $tranza", defaultDB)[1][0]["cenaobchodu"];
        $insertIntoMajetokToday2 = Connection::InsertUpdateCreateDelete("INSERT INTO majetoktoday 
        (obratid, destinacia,subjektid,kodobratu, uctovnykod, eqid,kodaktiva,ucetaktiva,jednotka,pocet,mena,md_d, obratdatetimereal,obratdatatimezauctovane)
        (select $obratID, destinacia,subjektid,u.kodobratu,ko.uctovnykod,'BU',mena, cub, mena, $pocet, mena,ko.md_d,CURRENT_DATE,
        (select datum from today where fondid = $id_fond) from uhrada u, kodobratumd_d ko where id = $uhradaid and ko.md_d = 0 and ko.subkodobratu = 1 and ko.kodobratu = u.kodobratu)", [], defaultDB);
        if (gettype($insertIntoMajetokToday2) !== "integer") {
            $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [majetoktoday2]. " . $insertIntoMajetokToday2;
        }

        $pocet = Connection::getDataFromDatabase("SELECT poplatok FROM rekonfirmaciacp WHERE dealid = $dealid AND tranza = $tranza", defaultDB)[1][0]["poplatok"];
        $insertIntoMajetokToday3 = Connection::InsertUpdateCreateDelete("INSERT INTO majetoktoday 
        (obratid, destinacia,subjektid,kodobratu, uctovnykod, eqid,kodaktiva,ucetaktiva,jednotka,pocet,mena,md_d, obratdatetimereal,obratdatatimezauctovane)
        (select $obratID, destinacia,subjektid,u.kodobratu,ko.uctovnykod,'BU',mena, cub, mena, $pocet, mena,ko.md_d,CURRENT_DATE,
        (select datum from today where fondid = $id_fond) from uhrada u, kodobratumd_d ko where id = $uhradaid and ko.md_d = 0 and ko.subkodobratu = 2 and ko.kodobratu = u.kodobratu)", [], defaultDB);
        if (gettype($insertIntoMajetokToday3) !== "integer") {
            $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [majetoktoday3]. " . $insertIntoMajetokToday3;
        }
    } else {
        $insertIntoMajetokToday = Connection::InsertUpdateCreateDelete("INSERT INTO majetoktoday
        (obratid, destinacia,subjektid,kodobratu, uctovnykod,eqid,kodaktiva,ucetaktiva,jednotka,pocet,mena,md_d, obratdatetimereal,obratdatatimezauctovane)
        select 
							$obratID, u.destinacia, pdr.subjektid, u.kodobratu,ko.uctovnykod,'BU', u.mena, u.cub, u.mena, pdr.transsumareal, u.mena,
							ko.md_d,CURRENT_DATE, (select datum from today where fondid = $id_fond) 
						from 
							uhrada u, kodobratumd_d ko, pool p, pooldetailreal pdr
						where 
							u.id = $uhradaid and ko.md_d = 1 and ko.kodobratu = u.kodobratu and
							p.poolid = pdr.poolid and u.tranza = pdr.tranza and
							p.dealid = u.dealid and u.destinacia like '%'||p.destinacia||'%'", [], defaultDB);
        if (gettype($insertIntoMajetokToday) !== "integer") {
            $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [majetoktoday]. " . $insertIntoMajetokToday;
        }

        $insertIntoMajetokToday2 = Connection::InsertUpdateCreateDelete("INSERT INTO majetoktoday
        (obratid, destinacia,subjektid,kodobratu, uctovnykod,eqid,kodaktiva,ucetaktiva,jednotka,pocet,mena,md_d, obratdatetimereal,obratdatatimezauctovane)
        select 
							$obratID, u.destinacia, pdr.subjektid, u.kodobratu,ko.uctovnykod,'BU', u.mena, u.cub, u.mena, pdr.cenaobchodu, u.mena,
							ko.md_d,CURRENT_DATE, (select datum from today where fondid = $id_fond) 
						from 
							uhrada u, kodobratumd_d ko, pool p, pooldetailreal pdr
						where 
							u.id = $uhradaid and ko.md_d = 0 and ko.subkodobratu = 1 and ko.kodobratu = u.kodobratu and
							p.poolid = pdr.poolid and u.tranza = pdr.tranza and
							p.dealid = u.dealid and u.destinacia like '%'||p.destinacia||'%'", [], defaultDB);
        if (gettype($insertIntoMajetokToday2) !== "integer") {
            $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [majetoktoday2]. " . $insertIntoMajetokToday2;
        }

        $insertIntoMajetokToday3 = Connection::InsertUpdateCreateDelete("INSERT INTO majetoktoday
        (obratid, destinacia,subjektid,kodobratu, uctovnykod,eqid,kodaktiva,ucetaktiva,jednotka,pocet,mena,md_d, obratdatetimereal,obratdatatimezauctovane)
        select 
							$obratID, u.destinacia, pdr.subjektid, u.kodobratu,ko.uctovnykod,'BU', u.mena, u.cub, u.mena, pdr.poplatok, u.mena,
							ko.md_d,CURRENT_DATE, (select datum from today where fondid = $id_fond) 
						from 
							uhrada u, kodobratumd_d ko, pool p, pooldetailreal pdr
						where 
							u.id = $uhradaid and ko.md_d = 0 and ko.subkodobratu = 2 and ko.kodobratu = u.kodobratu and
							p.poolid = pdr.poolid and u.tranza = pdr.tranza and
							p.dealid = u.dealid and u.destinacia like '%'||p.destinacia||'%'", [], defaultDB);
        if (gettype($insertIntoMajetokToday3) !== "integer") {
            $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [majetoktoday3]. " . $insertIntoMajetokToday3;
        }
    }
}

/**
 * Zauctovanie konverzie
 * @return void
 */
function zauctuj334()
{
    global $obratID, $uhradaid, $kodobratu, $id_fond, $errors, $tranza, $dealid;

    //Update konverzia
    $updateKonverzia = Connection::InsertUpdateCreateDelete("UPDATE konverzia SET logactivityid = ? WHERE dealid = ?", [18, $dealid], defaultDB);
    if (gettype($updateKonverzia) !== "integer") {
        $errors[] = "Nepodarilo sa zmeniť logactivityid v tabuľke [konverzia]. " . $updateKonverzia;
    }

    if ($id_fond != 0) {
        $insertIntoMajetokToday = Connection::InsertUpdateCreateDelete("INSERT INTO majetoktoday 
        (obratid, destinacia,subjektid,kodobratu, uctovnykod,eqid,kodaktiva,ucetaktiva,jednotka,pocet,mena,md_d, obratdatetimereal,obratdatatimezauctovane)
        (select $obratID, destinacia,subjektid,$kodobratu,uctovnykod,'BU',mena, cub, mena, suma, mena,kodobratumd_d.md_d,CURRENT_DATE,
        (select datum from today where fondid = $id_fond) from kodobratumd_d,uhrada where kodobratumd_d.kodobratu = $kodobratu and uhrada.id = $uhradaid)", [], defaultDB);
        if (gettype($insertIntoMajetokToday) !== "integer") {
            $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [majetoktoday]. " . $insertIntoMajetokToday;
        }

        $kodobratu = 634;
        $insertIntoMajetokToday2 = Connection::InsertUpdateCreateDelete("INSERT INTO majetoktoday 
        (obratid, destinacia,subjektid,kodobratu, uctovnykod,eqid,kodaktiva,ucetaktiva,jednotka,pocet,mena,md_d, obratdatetimereal,obratdatatimezauctovane)
        (select $obratID, destinacia,subjektid,$kodobratu,uctovnykod,'BU',mena, cub, mena, suma, mena,kodobratumd_d.md_d,CURRENT_DATE,
        (select datum from today where fondid = $id_fond) from kodobratumd_d,uhrada where kodobratumd_d.kodobratu = $kodobratu and uhrada.id = $uhradaid)", [], defaultDB);
        if (gettype($insertIntoMajetokToday2) !== "integer") {
            $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [majetoktoday]. " . $insertIntoMajetokToday2;
        }
    } else {
        $insertIntoMajetokToday = Connection::InsertUpdateCreateDelete("INSERT INTO majetoktoday 
        (obratid, destinacia,subjektid,kodobratu, uctovnykod,eqid,kodaktiva,ucetaktiva,jednotka,pocet,mena,md_d, obratdatetimereal,obratdatatimezauctovane)
        select 
							$obratID, u.destinacia,pdr.subjektid,u.kodobratu, ko.uctovnykod,'BU',u.mena, u.cub, u.mena,     
								pdr.transsumareal, u.mena, ko.md_d,CURRENT_DATE, 
							(select datum from today where fondid = $id_fond) 
						from 
							uhrada u, kodobratumd_d ko, pool p, pooldetailreal pdr, konverzia k
						where 
							k.dealid = u.dealid and
							u.id = $uhradaid and ko.kodobratu = u.kodobratu and
							p.poolid = pdr.poolid and u.tranza = pdr.tranza and
							p.dealid = u.dealid and u.destinacia like '%'||p.destinacia||'%'", [], defaultDB);
        if (gettype($insertIntoMajetokToday) !== "integer") {
            $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [majetoktoday]. " . $insertIntoMajetokToday;
        }
    }
}

/**
 * Storno vstupneho poplatku, alebo vkladu
 * @return void
 */
function zauctuj315_316()
{
    global $obratID, $uhradaid, $kodobratu, $id_fond, $errors, $tranza, $dealid;

    $insertIntoMajetokToday = Connection::InsertUpdateCreateDelete("INSERT INTO majetoktoday 
    (obratid, destinacia,subjektid,kodobratu, uctovnykod,eqid,kodaktiva,ucetaktiva,jednotka,pocet,mena,md_d, obratdatetimereal,obratdatatimezauctovane)
    (select $obratID, destinacia,subjektid,$kodobratu,uctovnykod,'BU',mena, cub, mena, suma, mena,kodobratumd_d.md_d,CURRENT_DATE,
    (select datum from today where fondid = $id_fond) from kodobratumd_d,uhrada where kodobratumd_d.kodobratu = $kodobratu and uhrada.id = $uhradaid)", [], defaultDB);
    if (gettype($insertIntoMajetokToday) !== "integer") {
        $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [majetoktoday]. " . $insertIntoMajetokToday;
    }

    $notification = new Notification(
        8,
        'kontopodielnik',
        $id_fond,
        'stornoVstupnehoPoplatku',
        $_SESSION['userid'],
        $_SESSION['username'],
        'stornoVstupnehoPoplatku',
        json_encode(["id", $uhradaid]),
        false,
        0
    );
    $notification->createNotifcation();
}


function zauctuj360()
{
    global $obratID, $uhradaid, $kodobratu, $id_fond, $errors, $tranza, $dealid;

    $insertIntoMajetokToday = Connection::InsertUpdateCreateDelete("INSERT INTO majetoktoday 
    (obratid, destinacia,subjektid,kodobratu, uctovnykod,eqid,kodaktiva,ucetaktiva,jednotka,pocet,mena,md_d, obratdatetimereal,obratdatatimezauctovane)
    (select $obratID, destinacia,subjektid,176,uctovnykod,'BU',mena, 
					cub, mena, suma-(
						select sum(pocet) from majetoktoday where uctovnykod 
							= kodobratumd_d.uctovnykod and subjektid = $id_fond
					), mena,kodobratumd_d.md_d,CURRENT_DATE, 
					(select datum from today where fondid = $id_fond) from 
					kodobratumd_d,uhrada where kodobratumd_d.kodobratu = 176 
					and uhrada.id = $uhradaid)", [], defaultDB);
    if (gettype($insertIntoMajetokToday) !== "integer") {
        $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [majetoktoday]. " . $insertIntoMajetokToday;
    }

    $insertIntoMajetokToday2 = Connection::InsertUpdateCreateDelete("INSERT INTO majetoktoday 
    (obratid, destinacia,subjektid,kodobratu, uctovnykod,eqid,kodaktiva,ucetaktiva,jednotka,pocet,mena,md_d, obratdatetimereal,obratdatatimezauctovane)
    (select $obratID, destinacia,subjektid,$kodobratu,uctovnykod,'BU',mena, cub, mena, suma, mena,kodobratumd_d.md_d,CURRENT_DATE,
    (select datum from today where fondid = $id_fond) from kodobratumd_d,uhrada where kodobratumd_d.kodobratu = $kodobratu and uhrada.id = $uhradaid)", [], defaultDB);
    if (gettype($insertIntoMajetokToday2) !== "integer") {
        $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [majetoktoday2]. " . $insertIntoMajetokToday2;
    }
}

function zauctuj310_to_348()
{
    global $obratID, $uhradaid, $kodobratu, $id_fond, $errors, $tranza, $dealid;

    $updateFondsFee = Connection::InsertUpdateCreateDelete("UPDATE fondsfeesstore SET logactivityid = ? WHERE dealid = ? AND mena = (SELECT mena from uhrada where id = $uhradaid)", [18, $dealid], defaultDB);
    if (gettype($updateFondsFee) !== "integer") {
        $errors[] = "Nepodarilo sa zmeniť logactivityid v tabuľke [fondsfeesstore]. " . $updateFondsFee;
    }

    //TODO dokončiť 
}

function zauctuj350()
{
    global $obratID, $uhradaid, $kodobratu, $id_fond, $errors, $tranza, $dealid;

    $insertIntoMajetokToday = Connection::InsertUpdateCreateDelete("INSERT INTO majetoktoday 
    (obratid, destinacia,subjektid,kodobratu, uctovnykod,eqid,kodaktiva,ucetaktiva,jednotka,pocet,mena,md_d, obratdatetimereal,obratdatatimezauctovane)
    (select max($obratID), destinacia,pr.fondid,max($kodobratu),k.uctovnykod,max('BU'),u.mena, u.cub, u.mena, sum(pr.suma), u.mena,k.md_d,max(CURRENT_DATE), max((select datum from today where fondid = $id_fond)) 
					from kodobratumd_d k,uhrada u, poplatok_register_links p, poplatok_register pr
					where k.kodobratu = $kodobratu and u.id = $uhradaid and u.id = p.uhrada_id and p.poplatok_register_id = pr.id
					group by 
						  destinacia,pr.fondid,k.uctovnykod,u.mena,u.cub,k.md_d
					)", [], defaultDB);
    if (gettype($insertIntoMajetokToday) !== "integer") {
        $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [majetoktoday]. " . $insertIntoMajetokToday;
    }
}

function zauctuj320_to_326()
{
    global $obratID, $uhradaid, $kodobratu, $id_fond, $errors;

    $popis = 'financne naklady';
    $queryPool[] = [
        "query" => "INSERT INTO majetokcesta (dealid,tranza,in_out,sparovanie,destinacia,subjektid,eqid,kodaktiva,ucetaktiva,jednotka,mena,pocet,cestafrom,cestatill,popis)
    (select dealid,tranza,0,dealid,'uhrada',$id_fond,'BU',mena,cub,mena,mena,suma,cestafrom,datesplatnost,'$popis' from uhrada where id = $uhradaid)",
        "params" => [],
        "db" => defaultDB,
        "name" => "Insert into majetokcesta"
    ];

    if ($id_fond != 0) {
        $queryPool[] = [
            "query" => "INSERT INTO majetoktoday (obratid, destinacia,subjektid,kodobratu, uctovnykod,eqid,kodaktiva,ucetaktiva,jednotka,pocet,mena,md_d, obratdatetimereal,obratdatatimezauctovane)
                (select $obratID, destinacia,subjektid,$kodobratu,uctovnykod,'BU',mena, cub, mena, suma, mena,kodobratumd_d.md_d,CURRENT_DATE,
                 (select datum from today where fondid = $id_fond) from kodobratumd_d,uhrada where kodobratumd_d.kodobratu = $kodobratu and uhrada.id = $uhradaid)",
            "params" => [],
            "db" => defaultDB,
            "name" => "Insert into majetoktoday"
        ];
    } else {
        $queryPool[] = [
            "query" => "INSERT INTO majetoktoday (obratid, destinacia,subjektid,kodobratu, uctovnykod,eqid,kodaktiva,ucetaktiva,jednotka,pocet,mena,md_d, obratdatetimereal,obratdatatimezauctovane)
        select $obratID, p.destinacia, pdr.subjektid,$kodobratu,k.uctovnykod,'BU',u.mena, u.cub, u.mena, transsumareal, u.mena,
                            k.md_d, CURRENT_TIMESTAMP, (select datum from today where fondid = $id_fond) 
                            from kodobratumd_d k,uhrada u, pool p, pooldetailreal pdr 
                            where k.kodobratu = $kodobratu and u.id = $uhradaid and
                            p.poolid = pdr.poolid and u.tranza = pdr.tranza and
                            p.dealid = u.dealid and p.destinacia = u.destinacia",
            "params" => [],
            "db" => defaultDB,
            "name" => "Insert into majetoktoday"
        ];
    }
}

$kontrola = Connection::getDataFromDatabase("SELECT * FROM kodobratumd_d WHERE kodobratumd_d.kodobratu = $kodobratu", defaultDB)[0];
if ($kontrola === 0) {
    $errors[] = "Nepodarilo sa nájsť kód obdobia pre kodobratu $kodobratu";
}

switch ($kodobratu) {
    case 301:
        continue;
    case 302:
        zauctuj302();
        break;
    case 332:
        zauctuj332();
        break;
    case 334:
        zauctuj334();
        break;
    case 320:
    case 321:
    case 322:
    case 323:
    case 324:
    case 325:
    case 326:
        zauctuj320_to_326();
        break;
    case 331:
        zauctuj331();
        break;
    case 315:
    case 316:
        zauctuj315_316();
        break;
    case 360:
        zauctuj360();
        break;
    case 310:
    case 311:
    case 312:
    case 313:
    case 314:
    case 343:
    case 344:
    case 345:
    case 346:
    case 347:
    case 348:
        zauctuj310_to_348();
        break;
    case 350:
        zauctuj350();
        break;
    default:
        $errors[] = "Nepodarilo sa zauctovať uhradu s kodobratu $kodobratu";
        break;
}

$queryPool[] = [
    "query" => "UPDATE uhrada SET logactivityid = ?, kodobratu = ?, logdatatimeactivity = ?, forma = ? WHERE id = ?",
    "params" => [9, $kodobratu, date("Y-m-d"), $forma, $uhradaid],
    "db" => defaultDB,
    "name" => "Update uhrada"
];

$sess_userid = $_SESSION["user"]["data"]["userid"];
$username = $_SESSION["user"]["data"]["username"];

$someData = Connection::getDataFromDatabase("SELECT u.*,
	 		  to_char(datesplatnost,'DD.MM.YYYY') as akt_splatnost, 
	 		  to_char(datesplatnost,'YYYYMMDD') as datspl 
	 		  from uhrada u where u.id = $uhradaid", defaultDB)[1][0];

$akt_splatnost = $someData["akt_splatnost"];
$datspl = $someData["datspl"];

include "uhradyPripraveneStep.php";
$transaction = Connection::runTransaction(defaultDB, null, $queryPool);
if (str_contains($transaction, "poriadku")) {
    $notification = new Notification(
        6,
        'ziadostreemisia',
        $uhradaid,
        'ziadostreemisia',
        $sess_userid,
        $username,
        "ziadostreemisia",
        json_encode(["fondid", $id_fond]),
        true,
        $id_fond
    );
    $notification->createNotifcation();

    $notification = new Notification(5, 'uhrada', $obratID, 'vyplataVynosu', $sess_userid, $username, "vyplataVynosu", json_encode(["id", $uhradaid]), true, $id_fond);
    $notification->createNotifcation();

    $notification = new Notification(
        7,
        'ziadostpresun',
        $uhradaid,
        'presunPodielnika',
        $sess_userid,
        $username,
        "presunPodielnika",
        json_encode(["obratid", $obratID]),
        true,
        $id_fond
    );
    $notification->createNotifcation();

    $notification = new Notification(
        9,
        'kontopodielnik',
        $obratID,
        "vrateniePoplatku",
        $sess_userid,
        $username,
        "vrateniePoplatku",
        json_encode(["obratid", $obratID]),
        false,
        $id_fond
    );
    $notification->createNotifcation();

    $notification = new Notification(
        9,
        'kontopodielnik',
        $obratID,
        "vratenieVkladu",
        $sess_userid,
        $username,
        "vratenieVkladu",
        json_encode(["obratid", $obratID]),
        false,
        $id_fond
    );
    $notification->createNotifcation();

    $notification = new Notification(
        9,
        'uhrada',
        $uhradaid,
        "uhrada",
        $sess_userid,
        $username,
        "uhrada",
        json_encode(["id", $uhradaid]),
        false,
        $id_fond
    );
    $notification->createNotifcation();

    $notification = new Notification(
        5,
        'fondsfeesstore',
        $dealid,
        "updateFondsFeeStore",
        $sess_userid,
        $username,
        "updateFondsFeeStore",
        json_encode(["dealid", $dealid]),
        true,
        $id_fond
    );
    $notification->createNotifcation();

    $notification = new Notification(
        9,
        'uhrada',
        $uhradaid,
        "uhradaZauctovana",
        $sess_userid,
        $username,
        "uhradaZauctovana",
        json_encode(["id", $uhradaid]),
        false,
        0
    );
    $notifID = $notification->createNotifcation();
    ?>
    <div id="toast-success"
        class="flex items-center w-full max-w-xs p-4 mb-4 text-gray-500 bg-white rounded-lg shadow-sm dark:text-gray-400 dark:bg-gray-600"
        role="alert">
        <div
            class="inline-flex items-center justify-center shrink-0 w-8 h-8 text-green-500 bg-green-100 rounded-lg dark:bg-green-800 dark:text-green-200">
            <svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                viewBox="0 0 20 20">
                <path
                    d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 8.207-4 4a1 1 0 0 1-1.414 0l-2-2a1 1 0 0 1 1.414-1.414L9 10.586l3.293-3.293a1 1 0 0 1 1.414 1.414Z" />
            </svg>
            <span class="sr-only">Check icon</span>
        </div>
        <div class="ms-3 text-sm font-normal">Príkaz na úhradu zaúčtovaný a pripravený na odoslanie banke.</div>
        <button type="button"
            class="ms-auto -mx-1.5 -my-1.5 bg-white text-gray-400 hover:text-gray-900 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex items-center justify-center h-8 w-8 dark:text-gray-500 dark:hover:text-white dark:bg-gray-800 dark:hover:bg-gray-700"
            data-dismiss-target="#toast-success" aria-label="Close">
            <span class="sr-only">Close</span>
            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
            </svg>
        </button>
    </div>
    <script>
        setTimeout(() => {
            htmx.ajax('GET', window.location.pathname + window.location.search, {
                target: "#pageContentMain",
            });
        }, 1000);
    </script>
<?php } else {
    print_r($transaction);
    ?>
    <div id="toast-danger"
        class="flex items-center w-full max-w-xs p-4 mb-4 text-gray-500 bg-white rounded-lg shadow-sm dark:text-gray-400 dark:bg-gray-600"
        role="alert">
        <div
            class="inline-flex items-center justify-center shrink-0 w-8 h-8 text-red-500 bg-red-100 rounded-lg dark:bg-red-800 dark:text-red-200">
            <svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                viewBox="0 0 20 20">
                <path
                    d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 11.793a1 1 0 1 1-1.414 1.414L10 11.414l-2.293 2.293a1 1 0 0 1-1.414-1.414L8.586 10 6.293 7.707a1 1 0 0 1 1.414-1.414L10 8.586l2.293-2.293a1 1 0 0 1 1.414 1.414L11.414 10l2.293 2.293Z" />
            </svg>
            <span class="sr-only">Error icon</span>
        </div>
        <div class="ms-3 text-sm font-normal">Nepodarilo sa zaúčtovať úhradu kvôli neznámej chybe.</div>
        <button type="button"
            class="ms-auto -mx-1.5 -my-1.5 bg-white text-gray-400 hover:text-gray-900 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex items-center justify-center h-8 w-8 dark:text-gray-500 dark:hover:text-white dark:bg-gray-800 dark:hover:bg-gray-700"
            data-dismiss-target="#toast-danger" aria-label="Close">
            <span class="sr-only">Close</span>
            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
            </svg>
        </button>
    </div>
    <script>
        setTimeout(() => {
            $("#toast-danger").fadeOut();
        }, 2500);
    </script>
<?php }


<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
require_once "/home/<USER>/www/src/lib/notifications/Notification.class.php";

$uhradaID = $_POST["uhradaid"];
$sess_userid = $_SESSION["user"]["data"]["userid"];
$username = $_SESSION["user"]["data"]["username"];

$deleteUhrada = Connection::InsertUpdateCreateDelete("UPDATE uhrada SET logactivityid = 3 WHERE id = ?", [$uhradaID], defaultDB);
if (gettype($deleteUhrada) !== "integer") { ?>
    <script>
        document.querySelectorAll(".deleteIcon").forEach((item) => {
            item.style.display = "inline-flex";
        });
        document.querySelectorAll(".deleteSpinner").forEach((item) => {
            item.style.display = "none";
        });
    </script>
<?php } else {
    $notification = new Notification(3, 'uhrada', $uhradaID, "deletePrikaz", $sess_userid, $username, "deletePrikaz", json_encode(["id", $uhradaID]), false, 0);
    $notifID = $notification->createNotifcation(); ?>
    <script>
        htmx.ajax('GET', window.location.pathname + window.location.search, {
            target: "#pageContentMain",
        });
    </script>
<?php }
<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
require_once "/home/<USER>/www/src/lib/notifications/Notification.class.php";

$errors = [];

$dealid = $_POST["dealid"];
$subjektid = $_POST["fondid"];
$tranza = $_POST["tranza"];
$destinacia = $_POST["destinacia"];
$in_out = $_POST["inout"];
$eqid = $_POST["eqid"];
$mena = $_POST["mena"];
$date = $_POST["date"];

$isin = Connection::getDataFromDatabase(
    "SELECT isin FROM konfirmaciacp WHERE dealid = $dealid",
    defaultDB
)[1][0]["isin"];

if (!$isin) {
    $isin = "";
}

$currdatetime = date("Y-m-d H:i:s");

$majetokCestaDetail = Connection::getDataFromDatabase(
    "SELECT * FROM majetokcesta WHERE 
dealid = $dealid AND
tranza = $tranza AND
destinacia = '$destinacia' AND
in_out = $in_out AND
subjektid = $subjektid
",
    defaultDB
)[1][0];

$deleteFromMajetokCesta = Connection::InsertUpdateCreateDelete("DELETE FROM majetokcesta WHERE 
dealid = ? AND
tranza = ? AND
destinacia = ? AND
in_out = ? AND
subjektid = ?
", [$dealid, $tranza, $destinacia, $in_out, $subjektid], defaultDB);

if (gettype($deleteFromMajetokCesta) !== "integer") {
    $errors[] = "Nepodarilo sa vymazať záznam z tabuľky [majetokcesta]. " . $deleteFromMajetokCesta;
}

switch ($eqid) {
    case "TD":
        handleTD($majetokCestaDetail);
        break;
    case "Bonds":
        handleBonds($majetokCestaDetail);
        break;
    case "Shares":
        handleShares($majetokCestaDetail);
        break;
    default:
        $errors[] = "EQID nie je definované";
        break;
}

if ($subjektid != 0) {
    if ($eqid === "Bonds" || $eqid === "Shares" || $eqid === "Fonds") {
        $insertIntoCashFlow = Connection::InsertUpdateCreateDelete("INSERT INTO cashflow
        (dealid, tranza, in_out, sparovanie, destinacia, subjektid, eqid, kodaktiva, ucetaktiva, jednotka, mena, pocet, datum, datumzmeny, poznamka, stav)
        (select r.dealid, r.tranza, 1, Null, 'konfirmaciacp', k.subjektid, '$eqid', (select isincurrric from dbequitycurrric where ric=k.ric), r.cum, 'ks', 
        k.currencyidtrade, r.kusovreal, to_date('$date','YYYY-MM-DD'), CURRENT_DATE, 'predaj cp', 0 
							 from rekonfirmaciacp r, konfirmaciacp k where r.dealid = $dealid and r.dealid=k.dealid 
								and r.tranza = $tranza)", [], defaultDB);
        if (gettype($insertIntoCashFlow) !== "integer") {
            $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [cashflow]. " . $insertIntoCashFlow;
        }
    }
} else {
    //TODO make it global
}

if ($eqid !== "TD") {
    $danovaQuery = "COALESCE((
					SELECT
						decode(poplatokcp,
						1, ff1.dan, 
						4, ff4.dan,
						 0)
					FROM
						fondsfeesdetail ff1, fondsfeesdetail ff2, 
						fondsfeesdetail ff4, dbequity de
					WHERE
						ff1.fondid = f.fondid and ff1.chargetype='vysporiadanieCPtyp1' and
						ff2.fondid = f.fondid and ff2.chargetype='vysporiadanieCPtyp2' and
				
						ff4.fondid = f.fondid and ff4.chargetype='vysporiadanieCPtyp4' and
						de.isin = k.isin
				),0)";
    $subjektid = 0;
    if ($subjektid != 0) {
        $obratID = Connection::getDataFromDatabase("SELECT nextval('majetok_today_obratid')", defaultDB)[1][0]["nextval"];
        $poplatokRegister = Connection::getDataFromDatabase("SELECT nextval('s_poplatok_register')", defaultDB)[1][0]["nextval"];
        $poplatokQuery = "COALESCE((
						SELECT
							CASE poplatokcp
                                   WHEN 1 THEN ff1.suma * f_menovy_kurz_kriz(ff1.mena, s.refmena,
                                                                             (SELECT datum FROM today WHERE fondid = k.subjektid),
                                                                             1)
                                   WHEN 2 THEN ff2.suma * f_menovy_kurz_kriz(ff2.mena, s.refmena,
                                                                             (SELECT datum FROM today WHERE fondid = k.subjektid),
                                                                             1)
                                   WHEN 4 THEN ff4.suma * f_menovy_kurz_kriz(ff4.mena, s.refmena,
                                                                             (SELECT datum FROM today WHERE fondid = k.subjektid),
                                                                             1)
                                   ELSE 0
                                   END
						FROM
							fondsfeesdetail ff1, fondsfeesdetail ff2, 

							fondsfeesdetail ff4, dbequity de
						WHERE
							ff1.fondid = f.fondid and ff1.chargetype='vysporiadanieCPtyp1' and
							ff2.fondid = f.fondid and ff2.chargetype='vysporiadanieCPtyp2' and
							ff4.fondid = f.fondid and ff4.chargetype='vysporiadanieCPtyp4' and
							de.isin = k.isin
					),0)";
        $insertIntoPoplatokQuery = "INSERT INTO poplatok_register(id, fondid, datum, suma, mena, typ, stav, dealid, tranza)
						SELECT
							$poplatokRegister, k.subjektid, (select datum from today where fondid = k.subjektid), round(($poplatokQuery) ,2),
							s.refmena, 'VYROV', 0, $dealid, $tranza
						from 
							konfirmaciacp k, fonds f, spravca s
						where 
							k.dealid=$dealid and 
							f.typpredlohy<>1 and
							k.subjektid = f.fondid AND
							(
								k.dealid not in (
									SELECT
										objektid
									FROM
										processlog
									WHERE
										destinacia = 'rekonfirmaciacp' and
										activityid = 15
								) or
								k.isin in (
									SELECT
										isin
									FROM
										dbequity
									WHERE
										poplatokcp = 1
								)
							)";
        $insertIntoPoplatokRegister = Connection::InsertUpdateCreateDelete($insertIntoPoplatokQuery, [], defaultDB);
        if (gettype($insertIntoPoplatokRegister) !== "integer") {
            $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [poplatok_register]. " . $insertIntoPoplatokRegister;
        }
    } else {
        $obratID = Connection::getDataFromDatabase("SELECT nextval('majetok_today_obratid')", defaultDB)[1][0]["nextval"];
        $poplatokRegister = Connection::getDataFromDatabase("SELECT nextval('s_poplatok_register')", defaultDB)[1][0]["nextval"];

        $poplatokQuery = "COALESCE((
						SELECT
							CASE poplatokcp
                                WHEN 1 THEN ff1.suma * f_menovy_kurz_kriz(ff1.mena, s.refmena,
                                                                        (SELECT datum FROM today WHERE fondid = pd.subjektid),
                                                                        1)
                                WHEN 2 THEN ff2.suma * f_menovy_kurz_kriz(ff2.mena, s.refmena,
                                                                        (SELECT datum FROM today WHERE fondid = pd.subjektid),
                                                                        1)
                                WHEN 4 THEN ff4.suma * f_menovy_kurz_kriz(ff4.mena, s.refmena,
                                                                        (SELECT datum FROM today WHERE fondid = pd.subjektid),
                                                                        1)
                                ELSE 0
                            END
						FROM
							fondsfeesdetail ff1, fondsfeesdetail ff2,  
							fondsfeesdetail ff4, dbequity de, rekonfirmaciacp r
						WHERE
							ff1.fondid = f.fondid and ff1.chargetype='vysporiadanieCPtyp1' and
							ff2.fondid = f.fondid and ff2.chargetype='vysporiadanieCPtyp2' and
							ff4.fondid = f.fondid and ff4.chargetype='vysporiadanieCPtyp4' and
							de.isin = k.isin and 
							r.dealid = $dealid and r.tranza = $tranza),0)";
        $insertIntoPoplatokQuery = "INSERT INTO poplatok_register(id, fondid, datum, suma, mena, typ, stav, dealid, tranza)
						SELECT
							$poplatokRegister, pd.subjektid, (select datum from today where fondid = pd.subjektid), round(($poplatokQuery) ,2),
							s.refmena, 'VYROV', 0, $dealid, $tranza
						from 
							pooldetailreal pd, pool p, konfirmaciacp k, fonds f, spravca s
						where 
							p.dealid=$dealid and 
							p.poolid=pd.poolid and 
							p.dealid=k.dealid and 
							f.typpredlohy<>1 and
							f.fondid = pd.subjektid and
							(
								k.dealid not in (
									SELECT
										objektid
									FROM
										processlog
									WHERE
										destinacia = 'rekonfirmaciacp' and
										activityid = 15
								) or
								k.isin in (
									SELECT
										isin
									FROM
										dbequity
									WHERE
										poplatokcp = 1
								)
							)";
        $insertIntoPoplatokRegister = Connection::InsertUpdateCreateDelete($insertIntoPoplatokQuery, [], defaultDB);
        if (gettype($insertIntoPoplatokRegister) !== "integer") {
            $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [poplatok_register]. " . $insertIntoPoplatokRegister;
        }

        $insertIntoRekonfirmaciaCPobratID = Connection::InsertUpdateCreateDelete("INSERT INTO rekonfirmaciacpobratid (dealid, obratid, tranza) VALUES (?, ?, ?)", [
            $dealid,
            $obratID,
            $tranza
        ], defaultDB);
        if (gettype($insertIntoRekonfirmaciaCPobratID) !== "integer") {
            $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [rekonfirmaciacpobratid]. " . $insertIntoRekonfirmaciaCPobratID;
        }
    }
}

if ($eqid === "Bonds" || $eqid === "Shares" || $eqid === "Fonds" || $eqid === "Depo") {
    //Update rekonfirmacie

    $updateRekCP = Connection::InsertUpdateCreateDelete(
        "UPDATE rekonfirmaciacp SET datvysporiadaniamureal = to_char(TO_DATE('$akt_splatnost', 'DD.MM.YYYY'), 'MM-DD-YYYY')::date WHERE dealid = ? AND tranza = ?",
        [$dealid, $tranza],
        defaultDB
    );
    if (gettype($updateRekCP) !== "integer") {
        $errors[] = "Nepodarilo sa zmeniť datum v tabuľke [rekonfirmaciacp]. " . $updateRekCP;
    }
}

if (empty($errors)) {
    $sess_userid = $_SESSION["user"]["data"]["userid"];
    $username = $_SESSION["user"]["data"]["username"];

    $notification = new Notification(
        15,
        'rekonfirmaciacp',
        $dealid,
        'majetkoveUhradaNatipovanie',
        $sess_userid,
        $username,
        "majetkoveUhradaNatipovanie",
        json_encode(["dealid", $dealid]),
        false,
        $id_fond
    );
    $notification->createNotifcation();
    ?>
    <div id="toast-success"
        class="flex items-center w-full max-w-xs p-4 mb-4 text-gray-500 bg-white rounded-lg shadow-sm dark:text-gray-400 dark:bg-gray-600"
        role="alert">
        <div
            class="inline-flex items-center justify-center shrink-0 w-8 h-8 text-green-500 bg-green-100 rounded-lg dark:bg-green-800 dark:text-green-200">
            <svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                viewBox="0 0 20 20">
                <path
                    d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 8.207-4 4a1 1 0 0 1-1.414 0l-2-2a1 1 0 0 1 1.414-1.414L9 10.586l3.293-3.293a1 1 0 0 1 1.414 1.414Z" />
            </svg>
            <span class="sr-only">Check icon</span>
        </div>
        <div class="ms-3 text-sm font-normal">Úhrada bola úspešne natipovaná.</div>
        <button type="button"
            class="ms-auto -mx-1.5 -my-1.5 bg-white text-gray-400 hover:text-gray-900 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex items-center justify-center h-8 w-8 dark:text-gray-500 dark:hover:text-white dark:bg-gray-800 dark:hover:bg-gray-700"
            data-dismiss-target="#toast-success" aria-label="Close">
            <span class="sr-only">Close</span>
            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
            </svg>
        </button>
    </div>
    <script>
        setTimeout(() => {
            htmx.ajax('GET', window.location.pathname + window.location.search, {
                target: "#pageContentMain",
            });
        }, 1000);
    </script>
    <?php
} else { ?>
    <div id="toast-danger"
        class="flex items-center w-full max-w-xs p-4 mb-4 text-gray-500 bg-white rounded-lg shadow-sm dark:text-gray-400 dark:bg-gray-600"
        role="alert">
        <div
            class="inline-flex items-center justify-center shrink-0 w-8 h-8 text-red-500 bg-red-100 rounded-lg dark:bg-red-800 dark:text-red-200">
            <svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                viewBox="0 0 20 20">
                <path
                    d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 11.793a1 1 0 1 1-1.414 1.414L10 11.414l-2.293 2.293a1 1 0 0 1-1.414-1.414L8.586 10 6.293 7.707a1 1 0 0 1 1.414-1.414L10 8.586l2.293-2.293a1 1 0 0 1 1.414 1.414L11.414 10l2.293 2.293Z" />
            </svg>
            <span class="sr-only">Error icon</span>
        </div>
        <div class="ms-3 text-sm font-normal">Nepodarilo sa zaúčtovať úhradu kvôli týmto chybám</div>
        <pre>
                    <?php print_r($errors); ?>
                </pre>
        <button type="button"
            class="ms-auto -mx-1.5 -my-1.5 bg-white text-gray-400 hover:text-gray-900 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex items-center justify-center h-8 w-8 dark:text-gray-500 dark:hover:text-white dark:bg-gray-800 dark:hover:bg-gray-700"
            data-dismiss-target="#toast-danger" aria-label="Close">
            <span class="sr-only">Close</span>
            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
            </svg>
        </button>
    </div>
    <script>
        setTimeout(() => {
            $("#toast-danger").fadeOut();
        }, 2500);
    </script>
<?php }

/**
 * Handles logic for eqid TD
 * @return void
 */
function handleTD(array $detail)
{
    global $errors, $subjektid, $dealid, $date;

    $mena = $detail["mena"];
    $ucetaktiva = $detail["ucetaktiva"];
    $jednotka = $detail["jednotka"];
    $kodaktiva = $detail["kodaktiva"];
    $pocet = $detail["pocet"];

    $obratID = Connection::getDataFromDatabase("SELECT nextval('majetok_today_obratid')", defaultDB)[1][0]["nextval"];
    if ($subjektid == 0) {
        $insertIntoMajetokToday = Connection::InsertUpdateCreateDelete("INSERT INTO majetoktoday 
        (obratid,destinacia,subjektid,kodobratu,uctovnykod,eqid,kodaktiva,ucetaktiva,jednotka,pocet,mena,md_d,
							obratdatetimereal,obratdatatimezauctovane)
        SELECT
			$obratID,'rekonfirmaciaktv',pdr.subjektid,kodobratu,uctovnykod,equid,'$kodaktiva',
			'$ucetaktiva','$jednotka',
			transsumareal,'$mena',md_d,to_date('$date 00:00:00', 'YYYY-MM-DD HH24:MI:SS'),
			(select datum from today where fondid = $subjektid)
		FROM
			kodobratumd_d k, pool p, pooldetailreal pdr
		WHERE
			p.dealid = $dealid and
			p.poolid = pdr.poolid and
			k.kodobratu = 353 
        ", [], defaultDB);

        if (gettype($insertIntoMajetokToday) !== "integer") {
            $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [majetoktoday]. " . $insertIntoMajetokToday;
        }
    } else {
        $mdds = [0, 1];
        foreach ($mdds as $md_d) {
            $uctovnykod = Connection::getDataFromDatabase("SELECT uctovnykod from kodobratumd_d where kodobratu=353 and md_d=$md_d", defaultDB)[1][0]["uctovnykod"];
            $equid = Connection::getDataFromDatabase("SELECT equid from kodobratumd_d where kodobratu=353 and md_d=$md_d", defaultDB)[1][0]["equid"];
            $today = Connection::getDataFromDatabase("SELECT datum from today where fondid = $subjektid", defaultDB)[1][0]["datum"];

            $insertIntoMajetokToday = Connection::InsertUpdateCreateDelete("INSERT INTO majetoktoday 
            (obratid,destinacia,subjektid,kodobratu,uctovnykod,eqid,kodaktiva,ucetaktiva,jednotka,pocet,mena,md_d,obratdatetimereal,obratdatatimezauctovane)
            VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?)", [
                $obratID,
                "rekonfirmaciaktv",
                $subjektid,
                353,
                $uctovnykod,
                $equid,
                $kodaktiva,
                $ucetaktiva,
                $jednotka,
                $pocet,
                $mena,
                $md_d,
                $date,
                $today
            ], defaultDB);
            if (gettype($insertIntoMajetokToday) !== "integer") {
                $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [majetoktoday1 md_d=$md_d]. " . $insertIntoMajetokToday;
            }
        }
    }
}

/**
 * Handles logic for eqid Bonds
 * @param array $detail
 * @return void
 */
function handleBonds(array $detail)
{
    global $errors, $subjektid, $dealid, $date, $isin, $tranza;

    $obratID = Connection::getDataFromDatabase("SELECT nextval('majetok_today_obratid')", defaultDB)[1][0]["nextval"];

    $mena = $detail["mena"];
    $jednotka = $detail["jednotka"];
    $kodaktiva = $detail["kodaktiva"];
    $ucetaktiva = $detail["ucetaktiva"];
    $pocet = $detail["pocet"];

    $is_excoupon = Connection::getDataFromDatabase(
        "SELECT is_coupon_date('$isin', to_date('2025-03-27', 'YYYY-MM-DD')) as is_coupon_date",
        defaultDB
    )[1][0]["is_coupon_date"];

    if ($is_excoupon) {
        $kodobratu = "351";
    } else {
        $kodobratu = "351,352";
    }

    if ($subjektid == 0) {
        $insertIntoMajetokToday = Connection::InsertUpdateCreateDelete("INSERT INTO majetoktoday 
        (obratid,destinacia,subjektid,kodobratu,uctovnykod,eqid,kodaktiva,ucetaktiva,jednotka,pocet,mena,md_d,obratdatetimereal,obratdatatimezauctovane) 
        SELECT
			$obratID,'rekonfirmaciacp',pdr.subjektid,kodobratu,uctovnykod,equid,'$kodaktiva',
			'$ucetaktiva','$jednotka',
							ksreal,'$mena',md_d,'$date',(select datum from today where fondid = $subjektid)
		FROM
			kodobratumd_d k, pool p, pooldetailreal pdr
		WHERE
			p.dealid = $dealid and
			p.poolid = pdr.poolid and
			pdr.tranza = $tranza and
			k.kodobratu in ($kodobratu)", [], defaultDB);
        if (gettype($insertIntoMajetokToday) !== "integer") {
            $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [majetoktoday]. " . $insertIntoMajetokToday;
        }
    } else {
        $mdds = [0, 1];
        $kodobratu = 351;
        foreach ($mdds as $md_d) {
            $uctovnykod = Connection::getDataFromDatabase("SELECT uctovnykod from kodobratumd_d where kodobratu=$kodobratu and md_d=$md_d", defaultDB)[1][0]["uctovnykod"];
            $equid = Connection::getDataFromDatabase("SELECT equid from kodobratumd_d where kodobratu=$kodobratu and md_d=$md_d", defaultDB)[1][0]["equid"];
            $today = Connection::getDataFromDatabase("SELECT datum from today where fondid = $subjektid", defaultDB)[1][0]["datum"];

            $insertIntoMajetokToday = Connection::InsertUpdateCreateDelete("INSERT INTO majetoktoday 
            (obratid,destinacia,subjektid,kodobratu,uctovnykod,eqid,kodaktiva,ucetaktiva,jednotka,pocet,mena,md_d,obratdatetimereal,obratdatatimezauctovane)
            VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?)", [
                $obratID,
                "rekonfirmaciacp",
                $subjektid,
                $kodobratu,
                $uctovnykod,
                $equid,
                $kodaktiva,
                $ucetaktiva,
                $jednotka,
                $pocet,
                $mena,
                $md_d,
                $date,
                $today
            ], defaultDB);

            if (gettype($insertIntoMajetokToday) !== "integer") {
                $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [majetoktoday2 md_d=$md_d]. " . $insertIntoMajetokToday;
            }

            if (!$is_excoupon) {
                $kodobratu = 352;
                $uctovnykod = Connection::getDataFromDatabase("SELECT uctovnykod from kodobratumd_d where kodobratu=$kodobratu and md_d=$md_d", defaultDB)[1][0]["uctovnykod"];
                $equid = Connection::getDataFromDatabase("SELECT equid from kodobratumd_d where kodobratu=$kodobratu and md_d=$md_d", defaultDB)[1][0]["equid"];
                $today = Connection::getDataFromDatabase("SELECT datum from today where fondid = $subjektid", defaultDB)[1][0]["datum"];

                $insertIntoMajetokToday = Connection::InsertUpdateCreateDelete("INSERT INTO majetoktoday 
                (obratid,destinacia,subjektid,kodobratu,uctovnykod,eqid,kodaktiva,ucetaktiva,jednotka,pocet,mena,md_d,obratdatetimereal,obratdatatimezauctovane)
                VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?)", [
                    $obratID,
                    "rekonfirmaciacp",
                    $subjektid,
                    $kodobratu,
                    $uctovnykod,
                    $equid,
                    $kodaktiva,
                    $ucetaktiva,
                    $jednotka,
                    $pocet,
                    $mena,
                    $md_d,
                    $date,
                    $today
                ], defaultDB);
            }
        }
    }
}

function handleShares(array $detail)
{
    global $errors, $subjektid, $dealid, $date, $isin, $tranza;

    $obratID = Connection::getDataFromDatabase("SELECT nextval('majetok_today_obratid')", defaultDB)[1][0]["nextval"];
    $kodobratu = 354;

    $kodaktiva = $detail["kodaktiva"];
    $ucetaktiva = $detail["ucetaktiva"];
    $jednotka = $detail["jednotka"];
    $pocet = $detail["pocet"];
    $mena = $detail["mena"];

    if ($subjektid == 0) {
        $insertIntoMajetokToday = Connection::InsertUpdateCreateDelete("INSERT INTO majetoktoday 
        (obratid,destinacia,subjektid,kodobratu,uctovnykod,eqid,kodaktiva,ucetaktiva,jednotka,pocet,mena,md_d,obratdatetimereal,obratdatatimezauctovane) 
        SELECT
							$obratID,'rekonfirmaciacp',pdr.subjektid,kodobratu,uctovnykod,equid,'$kodaktiva',
							'$ucetaktiva','$jednotka',
							ksreal,'$mena',md_d,'$date',(select datum from today where fondid = $subjektid)
						FROM
							kodobratumd_d k, pool p, pooldetailreal pdr
						WHERE
							p.dealid = $dealid and
							p.poolid = pdr.poolid and
							pdr.tranza = $tranza and
							k.kodobratu = 354 ", [], defaultDB);
        if (gettype($insertIntoMajetokToday) !== "integer") {
            $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [majetoktoday]. " . $insertIntoMajetokToday;
        }
    } else {
        $mdds = [0, 1];
        foreach ($mdds as $md_d) {
            $uctovnykod = Connection::getDataFromDatabase("SELECT uctovnykod from kodobratumd_d where kodobratu=$kodobratu and md_d=$md_d", defaultDB)[1][0]["uctovnykod"];
            $equid = Connection::getDataFromDatabase("SELECT equid from kodobratumd_d where kodobratu=$kodobratu and md_d=$md_d", defaultDB)[1][0]["equid"];
            $today = Connection::getDataFromDatabase("SELECT datum from today where fondid = $subjektid", defaultDB)[1][0]["datum"];

            $insertIntoMajetokToday = Connection::InsertUpdateCreateDelete("INSERT INTO majetoktoday 
            (obratid,destinacia,subjektid,kodobratu,uctovnykod,eqid,kodaktiva,ucetaktiva,jednotka,pocet,mena,md_d,obratdatetimereal,obratdatatimezauctovane)
            VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?)", [
                $obratID,
                "rekonfirmaciacp",
                $subjektid,
                $kodobratu,
                $uctovnykod,
                $equid,
                $kodaktiva,
                $ucetaktiva,
                $jednotka,
                $pocet,
                $mena,
                $md_d,
                $date,
                $today
            ], defaultDB);

            if (gettype($insertIntoMajetokToday) !== "integer") {
                $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [majetoktoday3 md_d=$md_d]. " . $insertIntoMajetokToday;
            }
        }

        $insertIntoRekonfObratID = Connection::InsertUpdateCreateDelete("INSERT INTO rekonfirmaciacpobratid (dealid, obratid, tranza) VALUES (?, ?, ?)", [
            $dealid,
            $obratID,
            $tranza
        ], defaultDB);
        if (gettype($insertIntoRekonfObratID) !== "integer") {
            $errors[] = "Nepodarilo sa vložiť údaje do tabuľky [rekonfirmaciacpobratid]. " . $insertIntoRekonfObratID;
        }
    }
}
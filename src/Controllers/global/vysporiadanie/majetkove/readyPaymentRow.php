<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
require_once "/home/<USER>/www/src/lib/notifications/Notification.class.php";
require_once "/home/<USER>/www/src/lib/notifications/Subactivity.class.php";

$activityid = $_POST["activityid"];
$suma = $_POST["suma"];
$vs = $_POST["vs"];
$ks = $_POST["ks"];
$ss = $_POST["ss"];
$ucetpartnera = $_POST["ucetpartnera"];
$cub = $_POST["cub"];
$nazovpartnera = $_POST["nazovpartnera"];
$splatnost = $_POST["splatnost"];
$forma = $_POST["forma"];
$dealid = $_POST["dealid"];
$tranza = $_POST["tranza"];
$platba = $_POST["platba"];
$kodobratu = $_POST["kodobratu"];
$uhradaID = $_POST["uhradaid"];
$logdatatimeactivity = $_POST["logdatatimeactivity"];
$logactivityid = $_POST["logactivityid"];
$new = $_POST["new"];
$key = $_POST["key"];
$kodyObratu = Connection::getDataFromDatabase("SELECT * from kodobratu " . ($kodobratu ? "WHERE kodobratu = $kodobratu" : " WHERE kodobratu IN (320,321,322,324,325,326)"), defaultDB)[1];
$sess_userid = $_SESSION["user"]["data"]["userid"];
$username = $_SESSION["user"]["data"]["username"];

if (!isset($_POST["delete"])) {
    if (!isset($key)) {
        $updateUhrada = Connection::InsertUpdateCreateDelete("UPDATE uhrada SET suma = ?, vs = ?, ss = ?, cubpartnera = ?, nazpartnera = ?, datesplatnost = ?, forma = ? WHERE id = ?", [
            $suma,
            $vs,
            $ss,
            $ucetpartnera,
            $nazovpartnera,
            $splatnost,
            $forma,
            $uhradaID
        ], defaultDB);
        if (gettype($updateUhrada) !== "integer") {
            echo "errorDB " . $updateUhrada;
            exit;
        }
        $notification = new Notification(2, 'uhrada', $uhradaID, "updatePrikaz", $sess_userid, $username, "updatePrikaz", json_encode(["id", $uhradaID]), false, 0);
        $notifID = $notification->createNotifcation();
    }
}

switch ($activityid) {
    case 3:
    case 4:
        $verified = "wait";
        $enabled = ($allowcheckneg == "1") ? 1 : 0;
        break;
    case 6:
        $verified = "yes";
        $enabled = ($allowcheckpoz == "1") ? 1 : 0;
        break;
    case 7:
        $verified = "no";
        $enabled = ($allowcheckneg == "1") ? 1 : 0;
        break;
    case 8:
        $verified = "auto";
        $enabled = ($allowcheckneg == "1") ? 1 : 0;
        break;
}

?>
<th scope="row" class="px-6 py-3 font-medium text-gray-900 whitespace-nowrap dark:text-white">
    <input type="hidden" name="suma" id="suma" value="<?php echo $suma; ?>" />
    <?php echo $suma; ?>
</th>
<td class="px-6 py-3">
    <input type="hidden" name="vs" id="vs" value="<?php echo $vs; ?>" />
    <?php echo $vs; ?>
</td>
<td class="px-6 py-3">
    <input type="hidden" name="ks" id="ks" value="<?php echo $ks; ?>" />
    <?php echo $ks; ?>
</td>
<td class="px-6 py-3">
    <input type="hidden" name="ss" id="ss value=" <?php echo $ss; ?>" />
    <?php echo $ss; ?>
</td>
<td class="px-6 py-3">
    <input type="hidden" name="ucetpartnera" id="ucetpartnera" value="<?php echo $ucetpartnera; ?>" />
    <?php echo $ucetpartnera; ?>
</td>
<td class="px-6 py-3">
    <input type="hidden" name="nazovpartnera" id="nazovpartnera" value="<?php echo $nazovpartnera; ?>" />
    <?php echo $nazovpartnera; ?>
</td>
<td class="px-6 py-3">
    <input type="hidden" name="splatnost" id="splatnost" value="<?php echo $splatnost; ?>" />
    <?php echo $splatnost; ?>
</td>
<td class="px-6 py-3">
    <input type="hidden" name="forma" id="forma" value="<?php echo $forma; ?>" />
    <?php echo $forma === 1 ? "Hotovosť" : "Prevod"; ?>
</td>
<td class="px-6 py-5 flex justify-center items-center">
    <?php if ($verified === "auto") { ?>
        <div class="group cursor-help">
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="lucide text-yellow-500 lucide-cloud-cog">
                <circle cx="12" cy="17" r="3" />
                <path d="M4.2 15.1A7 7 0 1 1 15.71 8h1.79a4.5 4.5 0 0 1 2.5 8.2" />
                <path d="m15.7 18.4-.9-.3" />
                <path d="m9.2 15.9-.9-.3" />
                <path d="m10.6 20.7.3-.9" />
                <path d="m13.1 14.2.3-.9" />
                <path d="m13.6 20.7-.4-1" />
                <path d="m10.8 14.3-.4-1" />
                <path d="m8.3 18.6 1-.4" />
                <path d="m14.7 15.8 1-.4" />
            </svg>
            <div role="tooltip"
                class="absolute z-10 px-3 py-2 text-sm font-medium text-white transition-opacity 
                                                duration-300 bg-gray-900 rounded-lg shadow-xs hidden group-hover:inline-block tooltip dark:bg-gray-700">
                Automatická verifikácia príkazu na úhradu
                <div class="tooltip-arrow" data-popper-arrow></div>
            </div>
        </div>

    <?php } ?>
    <?php if ($verified === "wait") { ?>
        <div class="group cursor-help">
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="lucide text-blue-400 lucide-clock-8">
                <circle cx="12" cy="12" r="10" />
                <polyline points="12 6 12 12 8 14" />
            </svg>
            <div role="tooltip"
                class="absolute z-10 px-3 py-2 text-sm font-medium text-white transition-opacity 
                                                duration-300 bg-gray-900 rounded-lg shadow-xs hidden group-hover:inline-block tooltip dark:bg-gray-700">
                <?php if ($uhrada["logactivityid"] == 4) {
                    echo "Odoslanie príkazu na úhradu na verifikáciu depozitárovi";
                } else {
                    echo "Prevzatie príkazu na úhradu depozitárom";
                } ?>
                <div class="tooltip-arrow" data-popper-arrow></div>
            </div>
        </div>
    <?php } elseif ($verified === "yes") { ?>
        <div class="group cursor-help">
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="lucide text-green-500 lucide-circle-check-big">
                <path d="M21.801 10A10 10 0 1 1 17 3.335" />
                <path d="m9 11 3 3L22 4" />
            </svg>
            <div role="tooltip"
                class="absolute z-10 px-3 py-2 text-sm font-medium text-white transition-opacity 
                                                duration-300 bg-gray-900 rounded-lg shadow-xs hidden group-hover:inline-block tooltip dark:bg-gray-700">
                Pozitívna verifikácia príkazu na úhradu depozitárom
                <div class="tooltip-arrow" data-popper-arrow></div>
            </div>
        </div>
    <?php } elseif ($verified === "no") { ?>
        <div class="group cursor-help">
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="lucide text-red-500 lucide-ban">
                <circle cx="12" cy="12" r="10" />
                <path d="m4.9 4.9 14.2 14.2" />
            </svg>
            <div role="tooltip"
                class="absolute z-10 px-3 py-2 text-sm font-medium text-white transition-opacity 
                                                duration-300 bg-gray-900 rounded-lg shadow-xs hidden group-hover:inline-block tooltip dark:bg-gray-700">
                Negatívna verifikácia príkazu na úhradu depozitárom
                <div class="tooltip-arrow" data-popper-arrow></div>
            </div>
        </div>
    <?php } else { ?>
        <div class="group cursor-help">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="lucide lucide-badge-plus">
                <path
                    d="M3.85 8.62a4 4 0 0 1 4.78-4.77 4 4 0 0 1 6.74 0 4 4 0 0 1 4.78 4.78 4 4 0 0 1 0 6.74 4 4 0 0 1-4.77 4.78 4 4 0 0 1-6.75 0 4 4 0 0 1-4.78-4.77 4 4 0 0 1 0-6.76Z" />
                <line x1="12" x2="12" y1="8" y2="16" />
                <line x1="8" x2="16" y1="12" y2="12" />
            </svg>
            <div role="tooltip"
                class="absolute z-10 px-3 py-2 text-sm font-medium text-white transition-opacity 
                                                duration-300 bg-gray-900 rounded-lg shadow-xs hidden group-hover:inline-block tooltip dark:bg-gray-700">
                Novo vytvorená úhrada
                <div class="tooltip-arrow" data-popper-arrow></div>
            </div>
        </div>
    <?php } ?>
</td>
<td class="px-6 py-4 text-right relative">
    <section class="flex gap-2 items-center">
        <form hx-post="/src/Controllers/global/vysporiadanie/majetkove/enableEditMode.php"
            hx-target="#uhrada<?php echo $uhradaID ? $uhradaID : "new" . $key; ?>">
            <input type="hidden" name="activityid" value="<?php echo $logactivityid ? $logactivityid : 1 ?>" />
            <input type="hidden" name="logdatatimeactivity"
                value="<?php echo $logdatatimeactivity ? $logdatatimeactivity : date("Y-m-d") ?>" />
            <?php if (isset($key)) { ?>
                <input type="hidden" name="key" value="<?php echo $key; ?>" />
            <?php } else { ?>
                <input type="hidden" name="uhradaid" value="<?php echo $uhradaID; ?>" />
            <?php } ?>
            <input type="hidden" name="suma" value="<?php echo $suma ?>" />
            <input type="hidden" name="vs" value="<?php echo $vs ?>" />
            <input type="hidden" name="ks" value="<?php echo $ks ?>" />
            <input type="hidden" name="ss" value="<?php echo $ss ?>" />
            <input type="hidden" name="ucetpartnera" value="<?php echo $ucetpartnera ?>" />
            <input type="hidden" name="nazovpartnera" value="<?php echo $nazovpartnera ?>" />
            <input type="hidden" name="splatnost" value="<?php echo $splatnost ?>" />
            <input type="hidden" name="kodobratu" value="<?php echo $kodobratu ?>" />
            <input type="hidden" name="forma" value="<?php echo $forma ?>" />
            <input type="hidden" name="dealid" value="<?php echo $dealid ?>" />
            <input type="hidden" name="cub" value="<?php echo $cub ?>" />
            <button type="submit"
                class="p-1 rounded-lg hover:bg-gray-300 hover:text-gray-700 transition-all cursor-pointer">
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide lucide-pencil">
                    <path
                        d="M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z" />
                    <path d="m15 5 4 4" />
                </svg>
            </button>
        </form>
        <form class="mb-0 deleteUhradaFromList">
            <input type="hidden" name="uhradaid" value="<?php echo $uhradaID ? $uhradaID : $key; ?>" />
            <button type="submit"
                class="p-1 rounded-lg hover:bg-red-300 hover:text-red-800 transition-all cursor-pointer">
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide deleteIcon text-red-500 lucide-trash">
                    <path d="M3 6h18" />
                    <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6" />
                    <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2" />
                </svg>
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide deleteSpinner text-red-500 animate-spin lucide-loader-circle" style="display: none;">
                    <path d="M21 12a9 9 0 1 1-6.219-8.56" />
                </svg>
            </button>
        </form>
        <form hx-post="/src/Controllers/global/vysporiadanie/majetkove/pripraveneUhradySubmit.php" hx-target="#toast">
            <input type="hidden" name="activityid" value="<?php echo $logactivityid ? $logactivityid : 1 ?>" />
            <input type="hidden" name="logdatatimeactivity"
                value="<?php echo $logdatatimeactivity ? $logdatatimeactivity : date("Y-m-d") ?>" />
            <input type="hidden" name="uhradaid" value="<?php echo $uhradaID ?>" />
            <input type="hidden" name="suma" value="<?php echo $suma ?>" />
            <input type="hidden" name="vs" value="<?php echo $vs ?>" />
            <input type="hidden" name="ks" value="<?php echo $ks ?>" />
            <input type="hidden" name="ss" value="<?php echo $ss ?>" />
            <input type="hidden" name="tranza" value="<?php echo $tranza ?>" />
            <input type="hidden" name="ucetpartnera" value="<?php echo $ucetpartnera ?>" />
            <input type="hidden" name="nazovpartnera" value="<?php echo $nazpartnera ?>" />
            <input type="hidden" name="splatnost" value="<?php echo $splatnost ?>" />
            <input type="hidden" name="forma" value="<?php echo $forma ?>" />
            <input type="hidden" name="dealid" value="<?php echo $dealid ?>" />
            <input type="hidden" name="cub" value="<?php echo $cub ?>" />
            <div class="absolute flex gap-1 bg-gray-600 items-center zauctujConfirm hidden flex-col z-20 p-2 rounded-lg"
                style="left: -6rem; top: -4rem;">
                <select name="kodobratu" class="bg-gray-50 border border-gray-300
                                                text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block
                                                w-full p-1 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400
                                                dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                    <?php foreach ($kodyObratu as $key => $value) { ?>
                        <option value="<?php echo $value["kodobratu"] ?>"><?php echo $value["popisobratu"] ?></option>
                    <?php } ?>
                </select>
                <button type="submit" class="text-white bg-gradient-to-r zauctovatBtn from-blue-500 via-blue-600 to-blue-700 hover:bg-gradient-to-br focus:ring-4 focus:outline-none 
                        focus:ring-blue-300 dark:focus:ring-blue-800 shadow-lg shadow-blue-500/50 dark:shadow-lg dark:shadow-blue-800/80 rounded-lg text-xs text-center 
                        w-full p-1 mt-2 font-bold flex justify-center gap-2">
                    <span>Zaúčtovať</span>
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        style="display: none" class="lucide animate-spin lucide-loader-circle">
                        <path d="M21 12a9 9 0 1 1-6.219-8.56" />
                    </svg>
                </button>
            </div>
            <button type="button"
                class="p-1 rounded-lg zauctujShow hover:bg-blue-300 hover:text-blue-800 transition-all cursor-pointer">
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide lucide-forward">
                    <polyline points="15 17 20 12 15 7" />
                    <path d="M4 18v-2a4 4 0 0 1 4-4h12" />
                </svg>
            </button>
        </form>
    </section>
</td>
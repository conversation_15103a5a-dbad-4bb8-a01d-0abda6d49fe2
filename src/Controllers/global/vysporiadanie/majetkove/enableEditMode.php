<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
require_once "/home/<USER>/www/src/lib/notifications/Notification.class.php";

$activityid = $_POST["activityid"];
$suma = $_POST["suma"];
$vs = $_POST["vs"];
$ks = $_POST["ks"];
$ss = $_POST["ss"];
$ucetpartnera = $_POST["ucetpartnera"];
$nazovpartnera = $_POST["nazovpartnera"];
$splatnost = $_POST["splatnost"];
$forma = $_POST["forma"];
$dealid = $_POST["dealid"];
$logdatatimeactivity = $_POST["logdatatimeactivity"];
$logactivityid = $_POST["logactivityid"];
$uhradaID = $_POST["uhradaid"];
$key = $_POST["key"];
$kodobratu = $_POST["kodobratu"];

$kss = Connection::getDataFromDatabase("select * from konstantnysymbol", defaultDB)[1];
?>
<td colspan="10">
    <form hx-post="/src/Controllers/global/vysporiadanie/majetkove/readyPaymentRow.php"
        hx-target="#uhrada<?php echo $uhradaID ? $uhradaID : "new" . $key; ?>"
        class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 border-gray-200">
        <input type="hidden" name="activityid" value="<?php echo $logactivityid ?>" />
        <input type="hidden" name="logdatatimeactivity" value="<?php echo $logdatatimeactivity ?>" />
        <input type="hidden" name="kodobratu" value="<?php echo $kodobratu ?>" />
        <?php if (isset($key)) { ?>
            <input type="hidden" name="key" value="<?php echo $key; ?>" />
        <?php } else { ?>
            <input type="hidden" name="uhradaid" value="<?php echo $uhradaID; ?>" />
        <?php } ?>
        <div class="relative overflow-x-auto">
            <table class="w-full text-sm text-left rtl:text-right">
                <tbody>
                    <th scope="row"
                        class="px-2 py-1 text-left font-medium text-gray-900 whitespace-nowrap dark:text-white">
                        <label class="dark:text-gray-100" for="suma">Suma</label>
                        <input type="text" id="suma" name="suma" value="<?php echo $suma; ?>"
                            class="block w-full p-1 mt-1 firstInput text-gray-900 border border-gray-300 rounded-lg bg-gray-50 text-xs focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                    </th>
                    <td class="px-2 py-1 text-left">
                        <label class="dark:text-gray-100" for="vs">VS</label>
                        <input type="text" id="vs" name="vs" value="<?php echo $vs; ?>"
                            class="block w-full p-1 mt-1 text-gray-900 border border-gray-300 rounded-lg bg-gray-50 text-xs focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                    </td>
                    <td class="px-2 py-1 text-left">
                        <label class="dark:text-gray-100" for="ks">KS</label>
                        <select id="ks" name="ks"
                            class="bg-gray-50 dark:bg-gray-700 mt-1 border border-gray-300 text-gray-900 dark:text-gray-100 text-xs rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1.5">
                            <?php foreach ($kss as $key => $value) { ?>
                                <option value="<?php echo $value["cislo"]; ?>" <?php echo $value["cislo"] === $ks ? "selected" : "" ?>>
                                    <?php echo $value["cislo"]; ?>
                                </option>
                            <?php } ?>
                        </select>
                    </td>
                    <td class="px-2 py-1 text-left">
                        <label class="dark:text-gray-100" for="ss">Špec. symbol</label>
                        <input type="text" id="ss" name="ss" value="<?php echo $ss; ?>"
                            class="block w-full p-1 mt-1 text-gray-900 border border-gray-300 rounded-lg bg-gray-50 text-xs focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                    </td>
                    <td class="px-2 py-1 text-left">
                        <label class="dark:text-gray-100" for="ucetpartnera">Účet partnera</label>
                        <input type="text" id="ucetpartnera" name="ucetpartnera" value="<?php echo $ucetpartnera; ?>"
                            class="block w-full p-1 mt-1 text-gray-900 border border-gray-300 rounded-lg bg-gray-50 text-xs focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                    </td>
                    <td class="px-2 py-1 text-left">
                        <label class="dark:text-gray-100" for="nazovpartnera">Názov partnera</label>
                        <input type="text" id="nazovpartnera" name="nazovpartnera" value="<?php echo $nazovpartnera; ?>"
                            class="block w-full p-1 mt-1 text-gray-900 border border-gray-300 rounded-lg bg-gray-50 text-xs focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                    </td>
                    <td class="px-2 py-1 text-left">
                        <label class="dark:text-gray-100" for="splatnost">Dátum splatnosti</label>
                        <input type="date" id="splatnost" name="splatnost" value="<?php echo $splatnost; ?>"
                            class="block w-full p-1 mt-1 text-gray-900 border border-gray-300 rounded-lg bg-gray-50 text-xs focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                    </td>
                    <td class="px-2 py-1 text-left">
                        <label class="dark:text-gray-100" for="forma">Forma</label>
                        <select name="forma" id="forma"
                            class="bg-gray-50 dark:bg-gray-700 border mt-1 border-gray-300 text-gray-900 dark:text-gray-100 text-xs rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1.5">
                            <option value="1" <?php echo $forma === 1 ? "selected" : "" ?>>Hotovosť</option>
                            <option value="0" <?php echo $forma === 0 ? "selected" : "" ?>>Prevod</option>
                        </select>
                    </td>
                    <td class="px-6 py-1"></td>
                    <td class="px-6 py-1">
                        <section class="flex items-end gap-2" style="height: 50;">
                            <button type="submit"
                                class="p-1 mt-1 rounded-lg bg-green-500 hover:bg-green-300 hover:text-blue-800 transition-all cursor-pointer">
                                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="lucide lucide-circle-check">
                                    <circle cx="12" cy="12" r="10" />
                                    <path d="m9 12 2 2 4-4" />
                                </svg>
                            </button>
                            <button type="button"
                                class="p-1 mt-1 rounded-lg closeEditMode bg-red-500 hover:bg-red-300 hover:text-red-800 transition-all cursor-pointer">
                                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="lucide lucide-circle-x">
                                    <circle cx="12" cy="12" r="10" />
                                    <path d="m15 9-6 6" />
                                    <path d="m9 9 6 6" />
                                </svg> </button>
                        </section>
                    </td>
                </tbody>
            </table>
        </div>
    </form>
    <script>
        document.querySelector(".closeEditMode").addEventListener("click", function (e) {
            htmx.ajax('POST', '/src/Controllers/global/vysporiadanie/majetkove/readyPaymentRow.php', {
                target: "#uhrada<?php echo $uhradaID ? $uhradaID : "new" . $key + 1; ?>",
                values: {
                    "suma": "<?php echo addslashes($suma); ?>",
                    "vs": "<?php echo addslashes($vs); ?>",
                    "ks": "<?php echo addslashes($ks); ?>",
                    "ss": "<?php echo addslashes($ss); ?>",
                    "ucetpartnera": "<?php echo addslashes($ucetpartnera); ?>",
                    "kodobratu": "<?php echo addslashes($kodobratu); ?>",
                    "nazovpartnera": "<?php echo addslashes($nazovpartnera); ?>",
                    "splatnost": "<?php echo addslashes($splatnost); ?>",
                    "forma": "<?php echo addslashes($forma); ?>",
                    "dealid": "<?php echo addslashes($dealid); ?>",
                    "logdatatimeactivity": "<?php echo addslashes($logdatatimeactivity); ?>",
                    "logactivityid": "<?php echo addslashes($logactivityid); ?>",
                    "activityid": <?php echo $activityid; ?>,
                    <?php if ($key) { ?>
                            "key": <?php echo $key + 1; ?>,
                    <?php } else { ?>
                            "uhradaid": <?php echo $uhradaID; ?>,
                    <?php } ?>
                    "delete": "true"
                }
            })
                .then(function (response) {
                    console.log(response);
                });
        });

    </script>
</td>
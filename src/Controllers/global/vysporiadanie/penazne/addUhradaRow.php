<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
require_once "/home/<USER>/www/src/lib/notifications/Notification.class.php";

$data = json_decode($_POST["data"]);
$action = $_POST["action"];
$errors = [];

if (isset($_SESSION["client"])) {
    $id_fond = $_SESSION["client"]["fondid"];
} elseif ($_SESSION["mode"]["mode"] === "admin") {
    $id_fond = 1;
} else {
    $id_fond = 0;
}

$sess_userid = $_SESSION["user"]["data"]["userid"];
$username = $_SESSION["user"]["data"]["username"];

if ($id_fond == 1) {
    $query = "SELECT * FROM spravcabu WHERE spravcaid = $id_fond AND cub LIKE '%$data->cub%'";
} else {
    $query = "SELECT * FROM fondsbu WHERE fondid = $id_fond AND cub LIKE '%$data->cub%'";
}

$mena = $data->mena;

if ($data->ss == '')
    $ss = null;
else
    $ss = $data->ss;

if ($data->ks == '')
    $ks = null;
else
    $ks = $data->ks;

if ($data->vs == '')
    $vs = null;
else
    $vs = $data->vs;

$obratdatetime = $data->datum;
$suma = $data->suma;


//Zapiseme do obratybu
if ($action == "update") {
    $id = $data->id;
    $updateObratyBU = Connection::InsertUpdateCreateDelete("UPDATE obratybu SET ks = ?, mena = ?, nazpartnera = ?, obratdatetime = ?, ss = ?, suma = ?, vs = ?, logactivityid = 2, loguserid = ?, logdatatimeactivity = ? WHERE id = ?", [
        $ks,
        $mena,
        $data->nazovpartnera,
        $obratdatetime,
        $ss,
        $suma,
        $vs,
        $sess_userid,
        date("Y-m-d"),
        $data->id
    ], defaultDB);
    if (gettype($updateObratyBU) !== "integer") {
        $errors[] = "DATABASE ERROR: [updateObratyBU]" . $updateObratyBU;
    } else {
        $notification = new Notification(1, 'obratybu', $id, "updateUhrady", $sess_userid, $username, "updateUhrady", json_encode(["id", $id]), false, 0);
        $notification->createNotifcation();
    }
} else {
    $id = Connection::getDataFromDatabase("SELECT nextval('s_obratybu')", defaultDB)[1][0]["nextval"];
    $insertIntoObratyBU = Connection::InsertUpdateCreateDelete("INSERT INTO obratybu (id,cub,cubpartnera,forma,ks,mena,nazpartnera,obratdatetime,ss,subjektid,suma,vs,krdb,
    logdatatimeactivity,logactivityid,loguserid) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)", [
        $id,
        $data->cub,
        $data->ucetpartnera,
        $data->forma,
        $ks,
        $mena,
        $data->nazovpartnera,
        $obratdatetime,
        $ss,
        $id_fond,
        $suma,
        $vs,
        1,
        date("Y-m-d"),
        1,
        $sess_userid
    ], defaultDB);

    if (gettype($insertIntoObratyBU) !== "integer") {
        $errors[] = "DATABASE ERROR: [insertIntoObratyBU]" . $insertIntoObratyBU;
    } else {
        $notification = new Notification(1, 'obratybu', $id, "natipovanieUhrady", $sess_userid, $username, "natipovanieUhrady", json_encode(["id", $id]), false, 0);
        $notification->createNotifcation();
    }
}

if (empty($errors)) { ?>
    <div id="toast-success"
        class="flex border-green-300 border items-center w-full max-w-sm p-4 mb-4 text-green-500 bg-white rounded-lg shadow dark:text-gray-400 dark:bg-gray-800"
        role="alert">
        <div
            class="inline-flex items-center justify-center flex-shrink-0 w-8 h-8 text-green-500 bg-green-100 rounded-lg dark:bg-green-800 dark:text-green-200">
            <svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                viewBox="0 0 20 20">
                <path
                    d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 8.207-4 4a1 1 0 0 1-1.414 0l-2-2a1 1 0 0 1 1.414-1.414L9 10.586l3.293-3.293a1 1 0 0 1 1.414 1.414Z" />
            </svg>
            <span class="sr-only">Check icon</span>
        </div>
        <div class="ms-3 text-sm font-semibold">Úhrada <strong><?php echo $id; ?></strong> bola úspešne vytvorená</div>
        <button type="button"
            class="ms-auto -mx-1.5 -my-1.5 bg-white text-gray-400 hover:text-gray-900 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex items-center justify-center h-8 w-8 dark:text-gray-500 dark:hover:text-white dark:bg-gray-800 dark:hover:bg-gray-700"
            data-dismiss-target="#toast-success" aria-label="Close">
            <span class="sr-only">Close</span>
            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
            </svg>
        </button>
    </div>
    <script>
        setTimeout(() => {
            window.location.href = "/vysporiadanie/penazne/prichadzajuce/natipovanie/uhrada?ucet=<?php echo $data->cub; ?>&mena=<?php echo $mena; ?>";
        }, 2000);
    </script>
<?php } else {
    print_r($errors);
    ?>
    <div id="toast-danger" class="flex items-center w-full max-w-sm p-4 mb-4 text-white bg-red-500 rounded-lg shadow-lg"
        role="alert">
        <?php ?>
        <div
            class="inline-flex items-center justify-center flex-shrink-0 w-8 h-8 text-red-500 bg-red-100 rounded-lg dark:bg-red-800 dark:text-red-200">
            <svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                viewBox="0 0 20 20">
                <path
                    d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 11.793a1 1 0 1 1-1.414 1.414L10 11.414l-2.293 2.293a1 1 0 0 1-1.414-1.414L8.586 10 6.293 7.707a1 1 0 0 1 1.414-1.414L10 8.586l2.293-2.293a1 1 0 0 1 1.414 1.414L11.414 10l2.293 2.293Z" />
            </svg>
            <span class="sr-only">Error icon</span>
        </div>
        <div class="ms-3 text-sm font-bold">Úhradu sa nepodarilo vytvoriť kvôli neznámej chybe!</div>
        <button type="button"
            class="ms-auto -mx-1.5 -my-1.5 text-white hover:text-gray-900 transition-all rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex items-center justify-center h-8 w-8 dark:text-gray-500 dark:hover:text-white dark:bg-gray-800 dark:hover:bg-gray-700"
            data-dismiss-target="#toast-danger" aria-label="Close">
            <span class="sr-only">Close</span>
            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
            </svg>
        </button>
    </div>
    <script>
        setTimeout(() => {
            $("#toast-danger").fadeOut();
        }, 1500);
    </script>
<?php } ?>
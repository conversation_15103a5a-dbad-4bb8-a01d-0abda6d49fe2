<?php
declare(strict_types=1);

require_once('/home/<USER>/www/src/lib/connection.php');
require_once('/home/<USER>/www/src/lib/Payments/PaymentsProcessor.class.php');
require_once('/home/<USER>/www/src/lib/Payments/Exceptions/PaymentProcessingException.php');
require_once('/home/<USER>/www/src/lib/Payments/Exceptions/DuplicatePaymentException.php');
require_once('/home/<USER>/www/src/lib/Payments/Exceptions/InvalidPaymentDataException.php');
require_once("/home/<USER>/www/conf/settings.php");


if (isset($_SESSION["client"])) {
    $id_fond = $_SESSION["client"]["fondid"];
} elseif ($_SESSION["mode"]["mode"] === "admin") {
    $id_fond = 1;
} else {
    $id_fond = 0;
}

$data = json_decode($_POST["data"]);

try { 
    $pdo = Connection::getConnection()->connect(defaultDB, "");
    $logger = null;
    $processor = new PaymentProcessor();
    $fundId = $id_fond;
    $status = 1;
    $paymentId = $data->id;

    $results = $processor->processIncomingPayments((int) $fundId, $status, $paymentId);
    ?>
    <div id="toast-success"
        class="flex items-center w-full max-w-xs p-4 mb-4 text-gray-500 bg-white rounded-lg shadow-sm dark:text-gray-400 dark:bg-gray-600"
        role="alert">
        <div
            class="inline-flex items-center justify-center shrink-0 w-8 h-8 text-green-500 bg-green-100 rounded-lg dark:bg-green-800 dark:text-green-200">
            <svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                viewBox="0 0 20 20">
                <path
                    d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 8.207-4 4a1 1 0 0 1-1.414 0l-2-2a1 1 0 0 1 1.414-1.414L9 10.586l3.293-3.293a1 1 0 0 1 1.414 1.414Z" />
            </svg>
            <span class="sr-only">Check icon</span>
        </div>
        <div class="ms-3 text-sm font-normal">Úhrada č. <strong><?php echo $results["paymentId"]; ?></strong> bola úspešne
            spracovaná so statusom <strong><?php echo $results["status"]; ?></strong></div>
        <button type="button"
            class="ms-auto -mx-1.5 -my-1.5 bg-white text-gray-400 hover:text-gray-900 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex items-center justify-center h-8 w-8 dark:text-gray-500 dark:hover:text-white dark:bg-gray-800 dark:hover:bg-gray-700"
            data-dismiss-target="#toast-success" aria-label="Close">
            <span class="sr-only">Close</span>
            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
            </svg>
        </button>
    </div>
    <script>
        setTimeout(() => {
            htmx.ajax('GET', window.location.pathname + window.location.search, {
                target: "#pageContentMain",
            });
        }, 1000);
    </script>
    <?php
} catch (\Exception $e) {
    error_log("Unexpected error: " . $e->getMessage()); ?>
    <div id="toast-danger"
        class="flex items-center w-full max-w-md p-4 mb-4 text-gray-500 bg-white rounded-lg shadow-sm dark:text-gray-400 dark:bg-gray-600"
        role="alert">
        <div
            class="inline-flex items-center justify-center shrink-0 w-8 h-8 text-red-500 bg-red-100 rounded-lg dark:bg-red-800 dark:text-red-200">
            <svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                viewBox="0 0 20 20">
                <path
                    d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 11.793a1 1 0 1 1-1.414 1.414L10 11.414l-2.293 2.293a1 1 0 0 1-1.414-1.414L8.586 10 6.293 7.707a1 1 0 0 1 1.414-1.414L10 8.586l2.293-2.293a1 1 0 0 1 1.414 1.414L11.414 10l2.293 2.293Z" />
            </svg>
            <span class="sr-only">Error icon</span>
        </div>
        <div class="ms-3 text-sm font-normal"><strong>Úhradu sa nepodarilo kvôli tejto chybe</strong>:
            <br><?php echo $e->getMessage(); ?>
        </div>
        <button type="button"
            class="ms-auto -mx-1.5 -my-1.5 bg-white text-gray-400 hover:text-gray-900 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex items-center justify-center h-8 w-8 dark:text-gray-500 dark:hover:text-white dark:bg-gray-800 dark:hover:bg-gray-700"
            data-dismiss-target="#toast-danger" aria-label="Close">
            <span class="sr-only">Close</span>
            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
            </svg>
        </button>
    </div>
    <script>
        $(".zapocetSpin").css("display", "none");
        $(".zapocetIcon").css("display", "block");
    </script>
    <?php
}
<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
$entityBody = file_get_contents('php://input');
$data = json_decode($entityBody);
$dataColumns = json_decode($data->columns);

$columns = [];
$values = [];
$last = sizeof($dataColumns) - 1;
$query = "UPDATE $data->table SET ";
$count = 0;
foreach ($dataColumns as $key => $column) {
    $values[] = $column->value;
    $columns[] = $column->name;
    if ($column->name === $data->hidden) {
        $count++;
        continue;
    }
    $count++;
    if ($count === $last) {
        $query .= $column->name . " = ? ";
    } else {
        $query .= $column->name . " = ?, ";
    }
    
}
$query .= " WHERE $columns[$last] = ?";
$statesRes = Connection::InsertUpdateCreateDelete($query, $values, defaultDB);
if (!is_string($stateRes)) {
    header('Content-Type: application/json');
    echo json_encode(["error" => false, "msg" => "Štát bol úspešne aktualizovaný.", "id" => $statesRes]);
} else {
    header('Content-Type: application/json');
    echo json_encode(["error" => true, "msg" => "Štát sa nepodarilo aktualizovať.", "id" => $stateRes]);
}

<?php
use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ailer\PHPMailer\Exception;
use <PERSON><PERSON><PERSON>ailer\PHPMailer\SMTP;
require_once('/home/<USER>/www/src/lib/connection.php');
require_once('/home/<USER>/www/lib/phpmailer/PHPMailer.php');
require_once('/home/<USER>/www/lib/phpmailer/Exception.php');
require_once('/home/<USER>/www/lib/phpmailer/SMTP.php');
require_once "/home/<USER>/www/conf/settings.php";
$entityBody = file_get_contents('php://input');
$data = json_decode($entityBody);
$dataColumns = json_decode($data->columns);

$columns = [];
$values = [];
$last = sizeof($dataColumns);

$sequence = $data->hidden;
$nextID = Connection::getDataFromDatabase("SELECT nextval('" . $sequence . "')", defaultDB);
$nextID = $nextID[1][0]["nextval"];

$query = "INSERT INTO $data->table(";
$count = 0;
foreach ($dataColumns as $key => $column) {

    $columns[] = $column->name;
    if ($column->name === $data->hidden) {
        $count++;
        continue;
    }
    if ($column->name === "povoleniavalues") {
        $count++;
        continue;
    }
    $values[] = $column->value;
    $count++;
    if ($count !== $last) {
        $query .= $column->name . ", ";
    } else {
        $query .= $column->name;
    }
}
$count = 0;
$povoleniaValues = "";
$query .= ") VALUES(";
foreach ($dataColumns as $key => $column) {
    if ($column->name === $data->hidden) {
        $count++;
        continue;
    }
    if($column->name === "povoleniavalues"){
        $povoleniaValues = explode(",", $column->value);
        $count++;
        continue;
    }
    if ($column->name === "email") {
        if ($column->value !== "") {
            $emailToSend = $column->value;
        } else {
            $error = "Email nesmie byť prázdny!";
            break;
        }
    }
    $count++;
    if ($count !== $last) {
        $query .= "?, ";
    } else {
        $query .= "?) ";
    }

}

$values[] = $nextID;
$values = array_values(array_filter($values, function ($k) {
    return $k !== '' && $k !== 0;
}));
if (Connection::InsertUpdateCreateDelete($query, $values, defaultDB)) {
    if ($emailToSend !== "") {
        date_default_timezone_set("Europe/Bratislava");
        $currentDateAndTime = date('Y-m-d H:i:s');
        $expirationDate = date('Y-m-d H:i:s', strtotime("+10 minutes"));
        $token = bin2hex(random_bytes(8));
        try {
            $melp = Connection::InsertUpdateCreateDelete("INSERT INTO passwordtokens (userid, date, token, expiration) VALUES (?, ?, ?, ?)", [$nextID, $currentDateAndTime, $token, $expirationDate], defaultDB);
        } catch (Exception $e) {
            $error = "Error: " . $e->getMessage();
        }
    
        $mail = new PHPMailer(true);
    
        try {
            $mail->charSet = "UTF-8";
            $mail->IsSMTP();
            $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
            $mail->SMTPAuth = true;
            $mail->Host = "mail.polarisfinance.eu";
            $mail->Username = "wwwgoldmann";
            $mail->Password = "ASgh34asdgfwTJHYYJT32!23532";
            $mail->Port = "587";
            $mail->addAddress($emailToSend);
            $mail->isHTML(true);
            $mail->Subject = "Vytvorenie nového hesla.";
            $mail->Body = "Vytvorte si nové si heslo pomocou tohto linku <a href='http://sam.sympatia.sk/reset-hesla?token=$token'>http://sam.sympatia.sk/reset-hesla?token=$token</a>";
            $mail->send();
            if($mail){
                $keket = "YEEEEESS";
            } else {
                $keket = "NOOOOOOOOOOO";
            }
        } catch (Exception $e) {
            $error = "Message could not be sent. " . $mail->ErrorInfo;
        }
    }
    if(!empty($povoleniaValues) && $error == ""){
        foreach ($povoleniaValues as $povolenie) {
            try {
                $welp = Connection::InsertUpdateCreateDelete("INSERT INTO usergroupusers (usergroupid, userid) VALUES (?, ?)", [$povolenie, $nextID], defaultDB);
            } catch (Exception $e) {
                $error = "Error: " . $e->getMessage();
            }
        }
    }
    header('Content-Type: application/json');
    echo json_encode(["error" => false, "msg" => "Vytvorenie prebehlo úspešne.", "id" => $keket]);
} else {
    header('Content-Type: application/json');
    echo json_encode(["error" => true, "msg" => $error, "id" => $statesRes === 1]);
}

<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
$entityBody = file_get_contents('php://input');
$data = json_decode($entityBody);
$userID = $data->userid;
$logoutBool = $data->logoutBool;

if($logoutBool === "false"){
    $logoutBool = 1;
} else {
    $logoutBool = 0;
}

$statesRes = Connection::InsertUpdateCreateDelete("UPDATE users SET forcelogout = ?",  [$logoutBool], defaultDB);
$userRes = Connection::InsertUpdateCreateDelete("UPDATE users SET forcelogout = ? WHERE userid = ?",  [0, $userID], defaultDB);

if (!is_string($stateRes)) {
    header('Content-Type: application/json');
    echo json_encode(["error" => false, "msg" => "Štát bol úspešne aktualizovaný.", "id" => $logoutBool]);
} else {
    header('Content-Type: application/json');
    echo json_encode(["error" => true, "msg" => "Štát sa nepodarilo aktualizovať.", "id" => $entityBody]);
}

<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
$statesRes = Connection::InsertUpdateCreateDelete("UPDATE users SET logged = ?",  [0], defaultDB);

if (!is_string($stateRes)) {
    header('Content-Type: application/json');
    echo json_encode(["error" => false, "msg" => "Štát bol úspešne aktualizovaný.", "id" => $statesRes]);
} else {
    header('Content-Type: application/json');
    echo json_encode(["error" => true, "msg" => "Štát sa nepodarilo aktualizovať.", "id" => $stateRes]);
}

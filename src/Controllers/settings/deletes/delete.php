<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

$entityBody = file_get_contents('php://input');
$data = json_decode($entityBody);
$dataColumns = json_decode($data->columns);
$query = "DELETE FROM $data->table WHERE $data->hidden = ?";
$statesRes = Connection::InsertUpdateCreateDelete($query, [$dataColumns[0]->value], defaultDB);
if (!is_string($stateRes)) {
    header('Content-Type: application/json');
    echo json_encode(["error" => false, "msg" => "Úspešne odstránené!.", "id" => $statesRes]);
} else {
    header('Content-Type: application/json');
    echo json_encode(["error" => true, "msg" => "Odstránenie sa nepodarilo.", "id" => $stateRes]);
}

<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
require_once("/home/<USER>/www/src/Components/exports/excel/excelController.php");
require_once("/home/<USER>/www/src/Components/exports/excel/exportToExcelButton.php");

$rok = $_POST["rok"];
$mena = $_POST["mena"];
$offset = $_POST["offset"];

$dateQuery = " and to_char(u.datesplatnost, 'yyyy') = '$rok' ";

$dateQuery2 = " and to_char((SELECT MAx(datum) from today), 'yyyy') = '$rok' ";

$query = "WITH max_today AS (SELECT MAX(datum) AS today_date
                   FROM today),
     paid AS (SELECT DISTINCT u.cub,
                              u.cubpartnera,
                              u.suma,
                              mca.mena,
                              'Zaplatené'     AS stav,
                              u.datesplatnost AS datum,
                              u.datesplatnost,
                              prl.mc_dealid   AS dealid,
                              1               AS detail,
                              p.fondid        AS subjektid,
                              p.cislozmluvy AS cislozmluvy
              FROM poplatok_register popl
                       JOIN portfolio p ON p.fondid = popl.fondid
                       JOIN poplatok_register_links prl ON prl.poplatok_register_id = popl.id
                       JOIN majetokcestaarchiv mca ON prl.mc_dealid = mca.dealid
                       JOIN uhrada u ON u.dealid = mca.dealid
              WHERE EXTRACT(YEAR FROM u.datesplatnost) = 2025),
     prepared AS (SELECT DISTINCT mca.ucetaktiva AS cub,
                                  ''             AS cubpartnera,
                                  mca.pocet      AS suma,
                                  mca.mena,
                                  'Pripravené'   AS stav,
                                  mt.today_date  AS datum,
                                  mt.today_date  AS datesplatnost,
                                  prl.mc_dealid  AS dealid,
                                  2              AS detail,
                                  p.fondid       AS subjektid,
                                  p.cislozmluvy AS cislozmluvy
                  FROM poplatok_register popl
                           JOIN portfolio p ON p.fondid = popl.fondid
                           JOIN poplatok_register_links prl ON prl.poplatok_register_id = popl.id
                           JOIN majetokcesta mca ON prl.mc_dealid = mca.dealid
                           CROSS JOIN max_today mt
                  WHERE prl.uhrada_id IS NULL
                    AND EXTRACT(YEAR FROM mt.today_date) = 2025)
SELECT *
FROM paid
UNION ALL
SELECT *
FROM prepared
ORDER BY datesplatnost DESC";
$dataRes = Connection::getDataFromDatabase($query, defaultDB);
$data = $dataRes[1];
header('Content-Type: application/json');
echo json_encode(["data" => $data]);
?>
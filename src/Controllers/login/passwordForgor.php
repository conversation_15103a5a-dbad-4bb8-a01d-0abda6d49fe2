<?php
$email = $_POST['email'];
use <PERSON><PERSON><PERSON><PERSON><PERSON>\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ailer\PHPMailer\Exception;
use <PERSON><PERSON><PERSON>ailer\PHPMailer\SMTP;
require_once('/home/<USER>/www/src/lib/connection.php');
require_once('/home/<USER>/www/src/lib/phpmailer/PHPMailer.php');
require_once('/home/<USER>/www/src/lib/phpmailer/Exception.php');
require_once('/home/<USER>/www/src/lib/phpmailer/SMTP.php');
require_once("/home/<USER>/www/conf/settings.php");

if (isset($email)) {
    $user;
    try {
        $getUser = Connection::getDataFromDatabase("SELECT * FROM users WHERE email = '$email'", defaultDB);
        $user = $getUser[1][0];
    } catch (Exception $e) {
        echo "Error: " . $e->getMessage();
    }
    if ($user["email"] !== "") {
        date_default_timezone_set("Europe/Bratislava");
        $currentDateAndTime = date('Y-m-d H:i:s');
        $expirationDate = date('Y-m-d H:i:s', strtotime("+10 minutes"));
        $token = bin2hex(random_bytes(8));
        try {
            $welp = Connection::InsertUpdateCreateDelete("INSERT INTO passwordtokens (userid, date, token, expiration) VALUES (?, ?, ?, ?)", [$user["userid"], $currentDateAndTime, $token, $expirationDate], defaultDB);
        } catch (Exception $e) {
            echo "Error: " . $e->getMessage();
        }

        $mail = new PHPMailer(true);

        try {
            $mail->charSet = "UTF-8";
            $mail->IsSMTP();
            $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
            $mail->SMTPAuth = true;
            $mail->Host = "mail.polarisfinance.eu";
            $mail->Username = "wwwgoldmann";
            $mail->Password = "ASgh34asdgfwTJHYYJT32!23532";
            $mail->Port = "587";
            $mail->addAddress($email);
            $mail->isHTML(true);
            $mail->Subject = "Reset hesla";
            $mail->Body = "Resetujte si heslo pomocou tohto linku <a href='http://sam.sympatia.sk/reset-hesla?token=$token'>http://sam.sympatia.sk/reset-hesla?token=$token</a>";
            $mail->send();
            echo "true";
        } catch (Exception $e) {
            echo "Message could not be sent. " . $mail->ErrorInfo;
        }
    } else {
        echo "TUSOM";
    }
} else {
    echo "else";
}
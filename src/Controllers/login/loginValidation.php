<?php
$username = $_POST['username'];
$pass = $_POST['pass'];

require_once('../../../src/lib/connection.php');
require_once("/home/<USER>/www/conf/settings.php");

$wronglogmax = 10;
$showLogout = false;

if (isset($username)) {
    try {
        $user = Connection::getDataFromDatabase("SELECT * FROM users WHERE usernick = '$username'", defaultDB);
        $hash = $user[1][0]["password"];
    } catch (Exception $e) {
        $user = "";
        echo "Error: " . $e->getMessage();
    }
    if (password_verify($pass, $hash) != FALSE) {
        if (password_needs_rehash($pass, algo: PASSWORD_DEFAULT)) {
            $newHash = password_hash($pass, PASSWORD_DEFAULT);
            Connection::InsertUpdateCreateDelete("UPDATE users SET password = ? WHERE usernick = ?", [$newHash, $username], defaultDB);
        }
        // LOGIN PROCESS
        $logged = $user[1][0]['logged'];
        $userID = $user[1][0]['userid'];

        $checkLoginStatus = Connection::getDataFromDatabase("SELECT logged, forcelogout FROM users WHERE userid = $userID", defaultDB);

        if ($checkLoginStatus[1][0]["forcelogout"]) {
            echo "Vaše prihlásenie bolo zablokované administrátorom!";
            die();
        }

        if ($user[1][0]['wronglog'] < $wronglogmax && $logged === false) {
            $logged = false;
        } elseif ($user[1][0]['wronglog'] > $wronglogmax && $logged === false) {
            $logged = false;
            $blockedLogin = true;
        } else {
            $logged = true;
        }

        $logged = checkNoLoginStatus($user[1])[1] ? "true" : "false";
        $blockedLogin = checkNoLoginStatus($user[1])[0];
        $reason = checkNoLoginStatus($user[1])[2];

        if ($blockedLogin) {
            echo "Momentálne nie je možné sa prihlásiť\n Dôvod: " . $reason;
            die();
        }

        if ($logged) {
            $currentDateAndTime = date('Y-m-d H:i:s');
            $userGroups = Connection::getDataFromDatabase("SELECT usergroupid FROM usergroupusers WHERE userid = $userID", defaultDB);
            $permissions = [];
            for ($i = 0; $i < $userGroups[0]; $i++) {
                $permissions[] = $userGroups[1][$i]['usergroupid'];
            }
            try {
                Connection::InsertUpdateCreateDelete("UPDATE users SET wronglog = 0, logged = true, lasttime = LOCALTIMESTAMP, forcelogout = false WHERE userid = ?", [$userID], defaultDB);
                Connection::InsertUpdateCreateDelete("INSERT INTO userlogins (userid, datetimefrom, datetimetill) VALUES (?, ?, ?)", [$userID, $currentDateAndTime, $currentDateAndTime], defaultDB);
            } catch (Exception $e) {
                echo "Error: " . $e->getMessage();
            }
            session_start();
            $_SESSION['user'] = ["data" => $user[1][0], "permissions" => $permissions];
            echo "true";
        }

    } else {
        echo "Používateľ s týmito prihlasovacími údajmi neexistuje!";
        die();
    }
}

/**
 * Returns array of booleans where first index indicates whether all users are blocked from signing in and second index indicates whether user is allowed to log in.
 * @return bool[]
 */
function checkNoLoginStatus($user): array
{
    $blockedAllLogin = false;
    $loggedIn = true;
    $showLogout = false;
    try {
        $noLoginStatus = Connection::getDataFromDatabase("SELECT * FROM nologin", defaultDB);
    } catch (Exception $e) {
        $noLoginStatus = "";
        echo "Error: " . $e->getMessage();
    }
    $reason = $noLoginStatus[1][0]['reason'];
    if ($noLoginStatus[0] !== 0) {
        $stopDate = $noLoginStatus[1][0]['stopdate'];
        $stopAll = $noLoginStatus[1][0]['stopall'];
        $unblockedUserID = $noLoginStatus[1]['userid'];

        if (date('Y-m-d H:i:s') < $stopDate || $stopAll === 1 || $user[0]['userid'] === $unblockedUserID) {
            $showLogout = true;
        }

        if ($showLogout) {
            $blockedAllLogin = true;
            $loggedIn = false;
        }
    }
    return [$blockedAllLogin, $loggedIn, $reason];
}

<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
session_start();
$mode = $_POST["mode"];

$_SESSION['mode']['mode'] = $mode;

if ($mode !== "client") {
    unset($_SESSION["client"]);
}
$url = $_POST["url"];
if (isset($_SESSION["mode"])) {
    header('Content-Type: application/json');
    echo "<span class='hidden'>" . json_encode(["error" => false, "msg" => "Podarilo sa prepnúť módy", "id" => $_SESSION['mode']['mode']]) . "</span>";
} else {
    header('Content-Type: application/json');
    echo "<span class='hidden'>" . json_encode(["error" => true, "msg" => "Nepodarilo sa prepnúť módy", "id" => $mode]) . "</span>";
}

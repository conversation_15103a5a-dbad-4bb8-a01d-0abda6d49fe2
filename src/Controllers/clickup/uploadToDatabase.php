<?php
require_once('../../../src/lib/connection.php');

// Get the JSON data from the POST request
$jsonData = file_get_contents('php://input');
$data = json_decode($jsonData, true);

//print_r($data[0]['list']['fields'][13]);

$listIDs = null;
$fields = null;
$allCustomFields = [];

$IDsToCheck = [];

if ($data === null) {
    echo json_encode(['message' => "Error occurred!"]);
} else {
    try {
        Connection::InsertUpdateCreateDelete("DROP TABLE IF EXISTS custom_fields_options", "host");
        Connection::InsertUpdateCreateDelete("DROP TABLE IF EXISTS custom_fields" , "host");
        Connection::InsertUpdateCreateDelete("CREATE TABLE custom_fields (name TEXT NOT NULL, poradie SMALLINT, listid TEXT, id TEXT)", "host");
        Connection::InsertUpdateCreateDelete("CREATE TABLE custom_fields_options (custom_field_id TEXT NOT NULL, option_id SMALLINT, option_label TEXT, poradie SMALLINT)", "host");
    } catch (Exception $e) {
        $user = "";
        echo json_encode(['message' => `Error: ${$e->getMessage()}`]);
        die();
    }

    for ($i = 0; $i < sizeof($data); $i++) {
        $listIDs[$i] = $data[$i]['list']['listID'];
        $fields = $data[$i]['list']['fields'];
        for ($y = 0; $y < sizeof($fields); $y++) {
            if (!in_array($fields[$y]['id'], $IDsToCheck)) {
                array_push($IDsToCheck, $fields[$y]['id']);
                array_push($allCustomFields, $fields[$y]);
            }
        }
        for ($x = 0; $x < sizeof($allCustomFields); $x++) {
            $name = $allCustomFields[$x]['name'];
            $listID = $listIDs[$i];
            $fieldId = $allCustomFields[$x]['id'];
            try {
                Connection::InsertUpdateCreateDelete("INSERT INTO custom_fields (name, poradie, listid, id) VALUES (?, ?, ?, ?)", [$name, $x, $listID, $fieldId], "host");
            } catch (Exception $e) {
                echo json_encode(['message' => "Error: ".$e->getMessage()]);
                die();
            }
        }
    }
    print_r(json_encode(['message' => "Data succesfully uploaded", 'data' => $allCustomFields]));
}


<?php
$listId = $_GET['list'];
$curl = curl_init();

curl_setopt_array($curl, [
    CURLOPT_HTTPHEADER => [
        "Authorization: pk_6761314_CYI9PD1DUQ6MRM6LF8661M3A5JQ2H602",
        "Content-Type: application/json"
    ],
    CURLOPT_URL => "https://api.clickup.com/api/v2/list/" . $listId . "/field",
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_CUSTOMREQUEST => "GET",
]);

$response = curl_exec($curl);
$error = curl_error($curl);

curl_close($curl);

if ($error) {
    echo "cURL Error #:" . $error;
} else {
    print_r(json_encode($response ,true));
}

//echo gettype($response);
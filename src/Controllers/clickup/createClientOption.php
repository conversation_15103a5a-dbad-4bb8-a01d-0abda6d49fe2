<?php
$postData = json_decode(file_get_contents("php://input"), true);
define("fieldId", $postData['fieldId']);
define("bodyPayload", $postData["bodyPayload"]);

$apiToken = 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6InNiVkFxWkNGdVJBPSJ9.eyJ1c2VyIjo2NzYxMzE0LCJ2YWxpZGF0ZWQiOnRydWUsIndzX2tleSI6Ijg1NjkwNTc3NTIiLCJzZXNzaW9uX3Rva2VuIjp0cnVlLCJpYXQiOjE3MTY0NDkzMTgsImV4cCI6MTcxNjYyMjExOH0.0_31aYh2eDtIg-QsVlokCrkm_GPsB8qCt-LawUpwzNA';
$workspaceId = '763867';

$curl = curl_init();
curl_setopt_array($curl, [
    CURLOPT_URL => "https://prod-eu-west-1-2.clickup.com/customFields/v2/field/".fieldId,
    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
    CURLOPT_CUSTOMREQUEST => "PUT",
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_POSTFIELDS => bodyPayload,
    CURLINFO_HEADER_OUT => true,
    CURLOPT_HTTPHEADER => array(
    'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6InNiVkFxWkNGdVJBPSJ9.eyJ1c2VyIjo1NjYzNTk0NiwidmFsaWRhdGVkIjp0cnVlLCJ3c19rZXkiOiI4NzY2MzExNjM5IiwiZm9ybSI6dHJ1ZSwic2Vzc2lvbl90b2tlbiI6dHJ1ZSwiaWF0IjoxNzE2ODEwNDI2LCJleHAiOjE3MTY5ODMyMjZ9.F8e_g4BtBfZ6u6nkhijGXfy9aVigfjyKdDvSe33AOZQ',
    'X-Worskpace-ID: 763867',
    'Content-Type: application/json'
    ),
]);
$response = curl_exec($curl);
$err = curl_error($curl);
curl_close($curl);

if ($err) {
    echo json_encode(["ok" => false, "error" =>  $err]);
} else {
    echo json_encode(["ok" => true, "response" => $response]);
}
?>

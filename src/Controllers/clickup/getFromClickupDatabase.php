<?php

require_once('../../../src/lib/connection.php');
require_once("/home/<USER>/www/conf/settings.php");

$podielnici = Connection::getDataFromDatabase("select * from podielnik
where datum=(select max(datum) from podielnik)", clickupDB);

$pozicie = Connection::getDataFromDatabase("select p.*, CONCAT(po.prieznaz, CASE WHEN po.meno IS NOT NULL THEN CONCAT(' ',po.meno) ELSE '' END, CASE WHEN po.titulpred IS NOT NULL THEN CONCAT(' ',po.titulpred) ELSE '' END) as meno from pozicie p INNER JOIN podielnik po ON (p.klientid = po.polarisid OR p.klientid = po.goldmannid) AND po.datum=(select max(datum) from podielnik) where p.datum=(select max(datum) from pozicie)", clickupDB);

echo json_encode(['podielnici' => $podielnici[1], 'pozicie' => $pozicie[1]]);

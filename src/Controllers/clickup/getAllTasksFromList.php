<?php
$list = $_GET['list'];
$page = $_GET['page'];

$query = array(
  "page" => $page,
  "include_closed" => true, 
);


$curl = curl_init();
curl_setopt_array($curl, [
    CURLOPT_HTTPHEADER => [
        "Authorization: pk_6761314_CYI9PD1DUQ6MRM6LF8661M3A5JQ2H602"
    ],
    CURLOPT_URL => "https://api.clickup.com/api/v2/list/" . $list . "/task?". http_build_query($query),
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_CUSTOMREQUEST => "GET",
]);

$response = curl_exec($curl);
$error = curl_error($curl);

curl_close($curl);

if ($error) {
    echo "cURL Error #:" . $error;
} else {
    print_r(json_encode($response ,true));
}

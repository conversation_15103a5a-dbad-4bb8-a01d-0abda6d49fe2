<?php
$postData = json_decode(file_get_contents("php://input"), true);
define("taskID", $postData['taskID']);
$taskName = $postData['name'];
$description = $postData['description'];


$curl = curl_init();

$payload = array(
    "name" => $taskName,
    "description" => $description,
);

curl_setopt_array($curl, [
    CURLOPT_HTTPHEADER => [
        "Authorization: pk_6761314_CYI9PD1DUQ6MRM6LF8661M3A5JQ2H602",
        "Content-Type: application/json"
    ],
    CURLOPT_POSTFIELDS => json_encode($payload),
    CURLOPT_URL => "https://api.clickup.com/api/v2/task/" . taskID,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_CUSTOMREQUEST => "PUT",
]);

$response = curl_exec($curl);
$error = curl_error($curl);

curl_close($curl);

if ($error) {
    echo "cURL Error #:" . $error;
} else {
    echo $response;
}
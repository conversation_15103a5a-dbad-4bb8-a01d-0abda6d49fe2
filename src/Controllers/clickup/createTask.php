<?php
$postData = json_decode(file_get_contents("php://input"), true);
define("listId", $postData['list']);
$name = $postData['name'];
$description = $postData['description'];
$status = $postData['status'];
$checkRequiredFields = $postData['check_required_custom_fields'];
$customFields = $postData['custom_fields'];

$curl = curl_init();

$payload = array(
    "name" => $name,
    "description" => $description,
    "status" => $status,
    "check_required_custom_fields" => true,
    "custom_fields" => $customFields

);

curl_setopt_array($curl, [
    CURLOPT_HTTPHEADER => [
        "Authorization: pk_6761314_CYI9PD1DUQ6MRM6LF8661M3A5JQ2H602",
        "Content-Type: application/json"
    ],
    CURLOPT_POSTFIELDS => json_encode($payload),
    CURLOPT_URL => "https://api.clickup.com/api/v2/list/" . listId . "/task",
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_CUSTOMREQUEST => "POST",
]);

$response = curl_exec($curl);
$error = curl_error($curl);

curl_close($curl);

if ($error) {
    echo "cURL Error #:" . $error;
} else {
    print_r(json_encode($response ,true));
}
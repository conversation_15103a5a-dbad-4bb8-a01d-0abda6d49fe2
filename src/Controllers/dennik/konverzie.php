<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
require_once("/home/<USER>/www/src/Components/exports/excel/excelController.php");
require_once("/home/<USER>/www/src/Components/exports/excel/exportToExcelButton.php");

$rok = $_POST["rok"];
$usporiadanie = $_POST["usporiadanie"];

$query = "SELECT k.dealid as dealid, 
				pa.skratka, 
				k.subjektid,
				DAT_POKYN as pokyn, 
				DAT_REKONFIRMACIA as rekonfirmacia,
				DAT_vysporiadaniecukredit as vysporiadanie,  
				SUMAKREDIT, 
				SUMADEBET, 
				MENAKREDIT, 
				MENADEBET, 
				MENOVYPAR, 
				KURZ, 
				DATUM_CAS_OBCHODU,
				'<PERSON><PERSON><PERSON>' as stav, 
				'' as TYP_POKYNU,
				6 as detail 
			from 
				konverzia k, partner pa	
			where 
				to_char(k.Dat_konfirmacia, 'yyyy') = '$rok'
				and k.logactivityid <>3
				and pa.partnerid = k.partnerid
				AND k.logactivityid = 4
			UNION ALL
			select 
				k.dealid as dealid, 
				pa.skratka, 
				k.subjektid,
				DAT_POKYN as pokyn, 
				DAT_REKONFIRMACIA as rekonfirmacia,
				DAT_vysporiadaniecukredit as vysporiadanie,  
				SUMAKREDIT, 
				SUMADEBET, 
				MENAKREDIT, 
				MENADEBET, 
				MENOVYPAR, 
				KURZ, 
				DATUM_CAS_OBCHODU,
				'Zobchodované' as stav, 
				tp.TYP_POKYNU,
				6 as detail 
			from 
				konverzia k, partner pa, TYPY_POKYNOV tp	
			where 
				to_char(k.Dat_pokyn, 'yyyy') = '$rok'
				and k.logactivityid <>3
				and pa.partnerid = k.partnerid
				AND TP.TYPID = k.typ_pokynu
				AND k.logactivityid = 10
			UNION ALL
			select 
				k.dealid as dealid, 
				pa.skratka, 
				k.subjektid,
				DAT_POKYN as pokyn, 
				DAT_REKONFIRMACIA as rekonfirmacia,
				DAT_vysporiadaniecukredit as vysporiadanie,  
				SUMAKREDIT, 
				SUMADEBET, 
				MENAKREDIT, 
				MENADEBET, 
				MENOVYPAR, 
				KURZ, 
				DATUM_CAS_OBCHODU,
				'Potvrdené' as stav, 
				tp.TYP_POKYNU,
				6 as detail 
			from 
				konverzia k, partner pa, TYPY_POKYNOV tp	
			where 
				to_char(k.Dat_pokyn, 'yyyy') = '$rok'
				and k.logactivityid <>3
				and pa.partnerid = k.partnerid
				AND TP.TYPID = k.typ_pokynu
				AND k.logactivityid = 12
			UNION ALL
			select 
				k.dealid as dealid, 
				pa.skratka, 
				k.subjektid,
				DAT_POKYN as pokyn, 
				DAT_REKONFIRMACIA as rekonfirmacia,
				DAT_vysporiadaniecukredit as vysporiadanie,  
				SUMAKREDIT, 
				SUMADEBET, 
				MENAKREDIT, 
				MENADEBET, 
				MENOVYPAR, 
				KURZ, 
				DATUM_CAS_OBCHODU,
				'Vysporiadané' as stav, 
				tp.TYP_POKYNU,
				6 as detail 
			from 
				konverzia k, partner pa, TYPY_POKYNOV tp	
			where 
				to_char(k.Dat_pokyn, 'yyyy') = '$rok'
				and k.logactivityid <>3
				and pa.partnerid = k.partnerid
				AND TP.TYPID = k.typ_pokynu
				AND k.logactivityid = 18
				
			order by $usporiadanie
";

$dataRes = Connection::getDataFromDatabase($query, defaultDB);
$data = $dataRes[1];
$dataToExport = [];

foreach ($data as $item) {
    $dataToExport[] = [
        "datum_cas_obchodu" => $item["datum_cas_obchodu"],
        "banka" => $item["skratka"],
        "datum_vysporiadania" => $item["vysporiadanie"],
        "menovy_par" => $item["menovypar"],
        "kurz" => $item["kurz"],
        "suma_debet" => $item["sumadebet"],
        "mena_debet" => $item["menadebet"],
        "cena_bez_auv" => $item["sumakredit"],
        "mena_kredit" => $item["menakredit"],
        "typ_pokynu" => $item["typ_pokynu"],
        "klient" => $item["klient"],
        "stav" => $item["stav"]
    ];
}

$columns = [
    "Dátum a čas obchodu",
    "Banka",
    "Dátum vysporiadania",
    "Menový pár",
    "Kurz",
    "Suma debet",
    "Mena debet",
    "Cena bez AUV",
    "Mena kredit",
    "Typ pokynu",
    "Klient",
    "Stav"
];
?>
<section style="margin-bottom: 8rem">
    <div class="mx-auto">
        <div class="flex items-center py-3 px-1 justify-between">
                <h2 class="font-bold my-4 text-2xl dark:text-gray-100 text-gray-800">Konverzie za rok
                    <?php echo $rok; ?>
                </h2>
                <div class="flex items-center gap-2">
                    <?php $button = new ExportToExcelButton($dataToExport, $columns, "konverzie-obchodny-dennik-" . $rok . "-" . $mena, "nvm");
                    echo $button->render();
                    ?>
                </div>
            </div>
        <div class="relative bg-white shadow-md dark:bg-gray-800 sm:rounded-lg">
            <div class="overflow-x-visible">
                <table class="w-full text-xs text-left text-gray-500 dark:text-gray-400">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                        <tr>
                            <th scope="col" class="px-4 py-3">Dátum a čas obchodu</th>
                            <th scope="col" class="px-4 py-3">Banka</th>
                            <th scope="col" class="px-4 py-3">Dátum vysporiadania</th>
                            <th scope="col" class="px-4 py-3">Menový pár</th>
                            <th scope="col" class="px-4 py-3">Kurz</th>
                            <th scope="col" class="px-4 py-3">Suma debet</th>
                            <th scope="col" class="px-4 py-3">Mena debet</th>
                            <th scope="col" class="px-4 py-3">Cena bez AUV</th>
                            <th scope="col" class="px-4 py-3">Mena kredit</th>
                            <th scope="col" class="px-4 py-3">Typ pokynu</th>
                            <th scope="col" class="px-4 py-3">Klient</th>
                            <th scope="col" class="px-4 py-3">Stav</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        foreach ($data as $item) {
                            $retazec4 = "";
                            $subjektid = $item["subjektid"];

                            if ($subjektid == 0) {
                                $retazec = '<button data-modal-target="modal' . $item["dealid"] . '" data-modal-toggle="modal' . $item["dealid"] . '" 
                                    class="block text-white w-full bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 
                                    font-bold rounded-md text-xs px-2 py-1 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800" type="button">
                                    Pool
                                    </button>';
                            } else {
                                $query2 = "select cislozmluvy from portfolio where fondid = $subjektid";
                                $cisloZmluvyRes = Connection::getDataFromDatabase($query2, defaultDB);
                                $cisloZmluvy = $cisloZmluvyRes[1][0];
                                $retazec = $cisloZmluvy["cislozmluvy"];
                            }

                            switch ($item["stav"]) {
                                case "Potvrdené":
                                    $FV = "";
                                    $MV = "";
                                    break;
                                case "Iba finančne":
                                    $FV = $item["fv"];
                                    $MV = "";
                                    break;
                                case "Iba majetkovo":
                                    $FV = "";
                                    $MV = $item["mv"];
                                    break;
                                case "Vysporiadané":

                                    $FV = $item["fv"];
                                    $MV = $item["mv"];
                                    break;
                            }
                            ?>
                            <tr
                                class="bg-white dark:bg-gray-800 border-b transition-all dark:hover:bg-gray-600 hover:bg-gray-300 cursor-pointer dark:border-gray-700">
                                <td class="px-4 py-2">
                                    <span
                                        class="text-xs text-gray-900 dark:text-gray-100 font-bold px-2 py-0.5 rounded"><?php echo $item['datum_cas_obchodu'] ?></span>
                                </td>
                                <td class="px-4 py-2">
                                    <span
                                        class="text-xs text-gray-900 dark:text-gray-100 font-bold px-2 py-0.5 rounded"><?php echo $item['skratka']; ?></span>
                                </td>
                                <td class="px-4 py-2">
                                    <span
                                        class="text-xs text-gray-900 dark:text-gray-100 font-bold px-2 py-0.5 rounded"><?php echo $item['vysporiadanie']; ?></span>
                                </td>
                                <td class="px-4 py-2">
                                    <span
                                        class="text-xs text-gray-900 dark:text-gray-100 font-bold px-2 py-0.5 rounded"><?php echo $item['menovypar']; ?></span>
                                </td>
                                <td class="px-4 py-2">
                                    <span
                                        class="text-xs text-gray-900 dark:text-gray-100 font-bold px-2 py-0.5 rounded"><?php echo round($item['kurz'], 5); ?></span>
                                </td>
                                <td class="px-4 py-2">
                                    <span
                                        class="text-xs text-gray-900 dark:text-gray-100 font-bold px-2 py-0.5 rounded"><?php echo round($item['sumadebet'], 2); ?></span>
                                </td>
                                <td class="px-4 py-2">
                                    <span
                                        class="text-xs text-gray-900 dark:text-gray-100 font-bold px-2 py-0.5 rounded"><?php echo $item['menadebet']; ?></span>
                                </td>
                                <td class="px-4 py-2">
                                    <span
                                        class="text-xs text-gray-900 dark:text-gray-100 font-bold px-2 py-0.5 rounded"><?php echo round($item['sumakredit'], 2); ?></span>
                                </td>
                                <td class="px-4 py-2">
                                    <span
                                        class="text-xs text-gray-900 dark:text-gray-100 font-bold px-2 py-0.5 rounded"><?php echo $item['menakredit']; ?></span>
                                </td>
                                <td class="px-4 py-2">
                                    <span
                                        class="text-xs text-gray-900 dark:text-gray-100 font-bold px-2 py-0.5 rounded"><?php echo $item['typ_pokynu']; ?></span>
                                </td>
                                <td class="px-4 py-2">
                                    <?php echo $retazec; ?>
                                </td>
                                <td class="p-1 text-center">
                                    <?php if ($item["stav"] === "Vysporiadané") { ?>
                                        <span
                                            class="inline-flex items-center gap-2 bg-green-500 text-white font-bold px-2 py-1 rounded-md"
                                            style="font-size: 0.7rem;"><?php echo $item['stav']; ?>
                                            <svg class="w-4 h-4 text-white" aria-hidden="true"
                                                xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor"
                                                viewBox="0 0 24 24">
                                                <path fill-rule="evenodd"
                                                    d="M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10-4.477 10-10 10S2 17.523 2 12Zm13.707-1.293a1 1 0 0 0-1.414-1.414L11 12.586l-1.793-1.793a1 1 0 0 0-1.414 1.414l2.5 2.5a1 1 0 0 0 1.414 0l4-4Z"
                                                    clip-rule="evenodd" />
                                            </svg>
                                        </span>
                                    <?php } else if ($item["stav"] === "Potvrdené") { ?>
                                            <span
                                                class="inline-flex items-center gap-2 bg-green-800 text-white font-bold px-2 py-1 rounded-md"
                                                style="font-size: 0.7rem;"><?php echo $item['stav']; ?>
                                                <svg class="w-4 h-4 text-white" aria-hidden="true"
                                                    xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none"
                                                    viewBox="0 0 24 24">
                                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                                        stroke-width="2"
                                                        d="M8.5 11.5 11 14l4-4m6 2a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                                                </svg>
                                            </span>
                                    <?php } else { ?>
                                            <span
                                                class="inline-flex items-center gap-2 bg-blue-400 text-white font-bold px-2 py-1 rounded-md"
                                                style="font-size: 0.7rem;"><?php echo $item['stav']; ?>
                                                <svg class="w-4 h-4 text-white" aria-hidden="true"
                                                    xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none"
                                                    viewBox="0 0 24 24">
                                                    <path stroke="currentColor" stroke-linecap="round" stroke-width="2"
                                                        d="M8 7V6a1 1 0 0 1 1-1h11a1 1 0 0 1 1 1v7a1 1 0 0 1-1 1h-1M3 18v-7a1 1 0 0 1 1-1h11a1 1 0 0 1 1 1v7a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1Zm8-3.5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0Z" />
                                                </svg>
                                            </span>
                                    <?php } ?>
                                </td>
                            </tr>
                            <?php
                        }
                        ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</section>

<?php
$count = 0;
foreach ($data as $item) {
    $subjektid = $item["subjektid"];
    if ($subjektid == 0) {
        $id = $item["dealid"];
        $detail = $item["detail"];
        if ($detail == 6) {
            $query = "
                SELECT  
                        po.cislozmluvy,
                    pdr.transsumareal,
                    pdr.auvreal,
                    pdr.dan,
                    m.pocet
                FROM
                        pool p,
                        pooldetailreal pdr,
                        portfolio po,
                    konverzia k,
                    konverziaobratid ko,
                    majetokarchiv m
                WHERE 
                        p.poolid = pdr.poolid
                        and pdr.subjektid = po.fondid
                        and p.dealid = $id
                    and m.subjektid=pdr.subjektid  
                    and m.obratid=ko.obratid  
                    and m.uctovnykod in (315160,315161)
                    and m.mena=k.menakredit 
                    and ko.dealid = k.dealid
                    and m.destinacia = 'konverziaobratid' 
                    and k.dealid = $id
                ORDER BY
                    po.cislozmluvy";
        } else {
            $query = "
            SELECT  
                    po.cislozmluvy,
                pd.transsuma
                
            FROM
                    pool p,
                    pooldetail pd,
                    portfolio po
            WHERE 
                    p.poolid = pd.poolid
                    and pd.subjektid = po.fondid
                    and p.dealid = $id
            ORDER BY
                po.cislozmluvy";
        }
        $modalDataRes = Connection::getDataFromDatabase($query, defaultDB);
        $modalData = $modalDataRes[1];
        $retazec = '<div id="modal' . $item["dealid"] . '" tabindex="-1" aria-hidden="true" class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
        <div class="relative p-4 w-full max-w-2xl max-h-full">
            <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
                <!-- Modal header -->
                <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                        Konverzie
                    </h3>
                    <button type="button" class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-xs w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white" data-modal-hide="modal' . $item["dealid"] . '">
                        <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                        </svg>
                        <span class="sr-only">Close modal</span>
                    </button>
                </div>
                <div class="p-4 md:p-5 space-y-4">
                    <table class="w-full text-xs text-left text-gray-500 dark:text-gray-400">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">';
        if ($detail === 6) {
            $retazec .= '<tr>
                            <th scope="col" class="px-4 py-3">Portfólio</th>
                            <th scope="col" class="px-4 py-3">Kurz</th>
                            <th scope="col" class="px-4 py-3">Suma debet</th>
                            <th scope="col" class="px-4 py-3">Mena debet</th>
                            <th scope="col" class="px-4 py-3">Suma kredit</th>
                            <th scope="col" class="px-4 py-3">Mena kredit</th>
                        </tr>';
        } else {
            $retazec .= '<tr>
                            <th scope="col" class="px-4 py-3">Portfólio</th>
                            <th scope="col" class="px-4 py-3">Istina</th>
                        </tr>';
        }

        $retazec .= '</thead>
                    <tbody>';
        foreach ($modalData as $modal) {
            if ($detail === 6) {
                $retazec .= '<tr
                class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b dark:border-gray-700">
                <td class="px-4 py-2">
                    <span
                        class="text-xs text-gray-900 dark:text-gray-100 font-bold px-2 py-0.5 rounded">' . $modal['cislozmluvy'] . '</span>
                </td>
                <td class="px-4 py-2">
                    <span
                        class="text-xs text-gray-900 dark:text-gray-100 font-bold px-2 py-0.5 rounded">' . round($item['kurz'], 5) . '</span>
                </td>
                <td class="px-4 py-2">
                    <span
                        class="text-xs text-gray-900 dark:text-gray-100 font-bold px-2 py-0.5 rounded">' . round($modal['transsumareal'], 2) . '</span>
                </td>
                <td class="px-4 py-2">
                    <span
                        class="text-xs text-gray-900 dark:text-gray-100 font-bold px-2 py-0.5 rounded">' . $item['menadebet'] . '</span>
                </td>
                <td class="px-4 py-2">
                    <span
                        class="text-xs text-gray-900 dark:text-gray-100 font-bold px-2 py-0.5 rounded">' . round($modal['pocet'], 2) . '</span>
                </td>
                <td class="px-4 py-2">
                    <span
                        class="text-xs text-gray-900 dark:text-gray-100 font-bold px-2 py-0.5 rounded">' . $item['menakredit'] . '</span>
                </td></tr>
                ';
            } else {
                $retazec .= '<tr
                class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b dark:border-gray-700">
                    <td class="px-4 py-2">
                        <span
                            class="text-xs text-gray-900 dark:text-gray-100 font-bold px-2 py-0.5 rounded">' . $modal['cislozmluvy'] . '</span>
                    </td>
                    <td class="px-4 py-2">
                        <span
                            class="text-xs text-gray-900 dark:text-gray-100 font-bold px-2 py-0.5 rounded">' . round($item['transsuma'], 2) . '</span>
                    </td>
                </tr>';
            }

        }
        $retazec .= '
                    </tbody>
                    </table>
                </div>
            </div>
        </div></div>';
        echo $retazec;
    }
}
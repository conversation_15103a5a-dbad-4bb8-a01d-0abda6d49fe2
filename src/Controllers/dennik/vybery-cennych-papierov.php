<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
require_once("/home/<USER>/www/src/Components/exports/excel/excelController.php");
require_once("/home/<USER>/www/src/Components/exports/excel/exportToExcelButton.php");

$mesiac = $_POST["mesiac"];
$usporiadanie = $_POST["usporiadanie"];
$stvrtrok = $_POST["stvrtrok"];
$rok = $_POST["rok"];
$zmluva = $_POST["zmluva"];

if ($stvrtrok == 1) {
    $datefrom = "01.01." . $rok;
    $dateto = "31.03." . $rok;
}
if ($stvrtrok == 2) {
    $datefrom = "01.04." . $rok;
    $dateto = "30.06." . $rok;
}
if ($stvrtrok == 3) {
    $datefrom = "01.07." . $rok;
    $dateto = "30.09." . $rok;
}
if ($stvrtrok == 4) {
    $datefrom = "01.10." . $rok;
    $dateto = "31.12." . $rok;
}

if ($stvrtrok != 0) {
    $dateQuery = " and rcp.datvysporiadaniamureal >= to_date('$datefrom','dd.mm.yyyy')
                 and rcp.datvysporiadaniamureal <= to_date('$dateto','dd.mm.yyyy') ";
}

if ($month != 0) {
    $dateQuery = " and to_char(rcp.datvysporiadaniamureal,'mm') = '$month'
                 and to_char(rcp.datvysporiadaniamureal, 'yyyy') = '$rok' ";
}

if ($month == 0 && $stvrtrok == 0) {
    $dateQuery = " and to_char(rcp.datvysporiadaniamureal, 'yyyy') = '$rok' ";

}
//---------------------------------------

if ($rok > 2008) {
    $ref = '\'EUR\'';
    $reftext = 'EUR';
} else {
    $ref = '\'SKK\'';
    $reftext = 'SKK';
}

$query = "SELECT 
        p.cislozmluvy as Klient,
        kcp.kusov as Pocet,
        rcp.datvysporiadaniamureal as Datum,
        kcp.currencyidtrade as Mena,
        d.isinreal as Isin, 
        d.cpnaz as Nazov,
        rcp.transsuma as Suma,
        rcp.transsuma*f_menovy_kurz_lot(kcp.currencyidtrade,$ref,rcp.datvysporiadaniamureal) as LokalnaMena

        from
        konfirmaciacp kcp,
        rekonfirmaciacp rcp,
        portfolio p,
        dbequitycurrric dcuric,
        dbequitycurr dcur,
        dbequity d

        where
        kcp.druhobchodu = 'vyber'
        and kcp.subjektid = p.fondid
        and kcp.ric = dcuric.ric
        $dateQuery
        and dcuric.isincurr = dcur.isincurr
        and dcur.isin = d.isin
        and kcp.dealid = rcp.dealid
        Order by 
        $usporiadanie
        ";
$dataRes = Connection::getDataFromDatabase($query, defaultDB);
$data = $dataRes[1];

$dataToExport = [];

foreach ($data as $item) {
    $dataToExport[] = [
        "datum" => $item["datum"],
        "klient" => $item["klient"],
        "nazov" => $item["nazov"],
        "isin" => $item["isin"],
        "pocet" => $item["pocet"],
        "suma" => $item["suma"],
        "mena" => $item["mena"],
        "lokalnamena" => $item["lokalnamena"]
    ];
}

$columns = [
    "Dátum",
    "Portfólio",
    "Názov",
    "ISIN",
    "Počet",
    "Suma",
    "Mena",
    "V " . $reftext
];
?>
<section style="margin-bottom: 8rem">
    <div class="mx-auto">
        <h2 class="font-bold my-4 text-2xl dark:text-gray-100 text-gray-800">Výbery cenných papierov</h2>
        <?php if ($dataRes[0] === 0) { ?>
            <div class="p-4 mb-4 text-sm text-blue-800 rounded-lg bg-blue-50 dark:bg-gray-800 dark:text-blue-400"
                role="alert">
                <span class="font-bold">Informácia!</span> Nenašli sa žiadne údaje podľa zadaných kritérií
            </div>
        <?php } ?>
        <div class="relative bg-white shadow-md dark:bg-gray-800 sm:rounded-lg">
            <div class="flex items-center py-3 px-1 justify-between">
                <h2 class="font-bold my-4 text-2xl dark:text-gray-100 text-gray-800">Výbery cenných papierov za rok
                    <?php echo $rok; ?>
                </h2>
                <div class="flex items-center gap-2">
                    <?php $button = new ExportToExcelButton($dataToExport, $columns, "vybery-cennych-papierov-obchodny-dennik-" . $rok . "-" . $mena, "nvm");
                    echo $button->render();
                    ?>
                </div>
            </div>
            <div class="overflow-x-visible">
                <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                        <tr>
                            <th scope="col" class="px-4 py-3">Dátum</th>
                            <th scope="col" class="px-4 py-3">Portfólio</th>
                            <th scope="col" class="px-4 py-3">Názov CP</th>
                            <th scope="col" class="px-4 py-3">ISIN</th>
                            <th scope="col" class="px-4 py-3">Počet</th>
                            <th scope="col" class="px-4 py-3">V cene</th>
                            <th scope="col" class="px-4 py-3">Mena</th>
                            <th scope="col" class="px-4 py-3">V <?php echo $reftext ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        foreach ($data as $item) { ?>
                            <tr
                                class="bg-white dark:bg-gray-800 dark:hover:bg-gray-600 transition-all cursor-pointer hover:bg-gray-200 border-b dark:border-gray-700">
                                <td class="px-4 py-2">
                                    <span
                                        class="text-sm text-gray-900 dark:text-gray-200 font-bold px-2 py-0.5 rounded"><?php echo $item['datum'] ?></span>
                                </td>
                                <td class="px-4 py-2">
                                    <span
                                        class="text-sm text-gray-900 dark:text-gray-200 font-bold px-2 py-0.5 rounded"><?php echo $item['klient']; ?></span>
                                </td>
                                <td class="px-4 py-2">
                                    <span
                                        class="text-sm text-gray-900 dark:text-gray-200 font-bold px-2 py-0.5 rounded"><?php echo $item['nazov']; ?></span>
                                </td>
                                <td class="px-4 py-2">
                                    <span
                                        class="text-sm text-gray-900 dark:text-gray-200 font-bold px-2 py-0.5 rounded"><?php echo $item['isin']; ?></span>
                                </td>
                                <td class="px-4 py-2">
                                    <span
                                        class="text-sm text-gray-900 dark:text-gray-200 font-bold px-2 py-0.5 rounded"><?php echo round($item['pocet'], 0); ?></span>
                                </td>
                                <td class="px-4 py-2">
                                    <span
                                        class="text-sm text-gray-900 dark:text-gray-200 font-bold px-2 py-0.5 rounded"><?php echo round($item['suma'], 2); ?></span>
                                </td>
                                <td class="px-4 py-2">
                                    <span
                                        class="text-sm text-gray-900 dark:text-gray-200 font-bold px-2 py-0.5 rounded"><?php echo $item['mena']; ?></span>
                                </td>
                                <td class="px-4 py-2 w-48">
                                    <span
                                        class="text-sm text-gray-900 dark:text-gray-200 font-bold px-2 py-0.5 rounded"><?php echo number_format(round($item['lokalnamena'], 2), 2, ".", " "); ?></span>
                                </td>
                            </tr>
                            <?php
                        }
                        ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</section>
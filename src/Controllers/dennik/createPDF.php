<?php
require_once("/home/<USER>/www/src/lib/tcpdf/tcpdf.php");

// Create new PDF document
$pdf = new TCPDF();
$pdf->AddPage();
$pdf->SetFont('dejavusans', '', 10);

$uniqueFilename = $_POST["dealid"];
$today = date("d.m.Y");
$username = $_SESSION["user"]["data"]["username"];

// Sample values (replace with your actual data from DB)
$data = [
    'datum_prijatia' => $_POST["datum_prijatia"],
    'cas_prijatia' => $_POST["cas_prijatia"],
    'datum_realizacie' => $_POST["datum_realizacie"],
    'cas_alokacie' => $_POST["cas_alokacie"],
    'datum_vysporiadania' => $_POST["datum_vysporiadania"],
    'druh' => $_POST["druh"],
    'nazov' => $_POST["nazov"],
    'kusy' => $_POST["kusov"],
    'isin' => $_POST["isin"],
    'kupon' => $_POST["urok"] . "%",
    'mena' => $_POST["mena"],
    'splatnost' => $_POST["splatnost"],
    'trh' => $_POST["trh"],
    'klient' => $_POST["klient"],
    'dealer' => $_POST["dealer"],
    'zadany_kurz' => $_POST["zadany_kurz"] . ($_POST["druh"] === "Bonds" ? "%" : ""),
    'zadany_cas' => $_POST["cas_prijatia"],
    'zadany_platny_do' => $_POST["platny_do"],
    'zadany_zadal' => $_POST["zadany_zadal"],
    'realizovany_meno' => 'auto exe',
    'realizovany_cas' => $_POST["cas_alokacie"],
    'realizovany_kurz' => $_POST["zadany_kurz"] . "%",
    'realizovany_objem' => $_POST["cena_spolu"],
    'realizoval' => $_POST["dealer"],
    'objem_nominalny' => $_POST["objem_nominalny"],
    'auvk_1ks' => $_POST["auvk_1ks"],
    'auvk_celkom' => $_POST["auvk_celkom"],
    'nh_auvk' => $_POST["nh_auvk"],
];

// Title
$pdf->SetFont('', 'B', 14);
$pdf->Cell(0, 10, 'POKYN NA OBCHODOVANIE', 0, 1, 'C');
$pdf->SetFont('', '', 10);
$pdf->Cell(0, 5, '(interný dokument)', 0, 1, 'C');
$pdf->Ln(5);

// Output layout
$html = '
<style>
    td { border: 0.5px solid #000; padding: 4px; }
    .header { background-color: #f2f2f2; font-weight: bold; }
</style>

<table cellspacing="0" cellpadding="3" border="1">
<tr>
    <td colspan="2"><b>Dátum prijatia pokynu</b><br>' . $data['datum_prijatia'] . '</td>
    <td><b>Čas prijatia pokynu</b><br>' . $data['cas_prijatia'] . '</td>
    <td colspan="2"><b>Dátum realizácie obchodu</b><br>' . $data['datum_realizacie'] . '</td>
    <td><b>Čas alokácie</b><br>' . $data['cas_alokacie'] . '</td>
</tr>
<tr>
    <td colspan="6"><b>Dátum vysporiadania:</b> ' . $data['datum_vysporiadania'] . '</td>
</tr>
<tr>
    <td colspan="6"><b>Druh:</b> ' . $data['druh'] . '</td>
</tr>
<tr>
    <td><b>Názov</b><br>' . $data['nazov'] . '</td>
    <td><b>Počet kusov</b><br>' . $data['kusy'] . '</td>
    <td><b>ISIN</b><br>' . $data['isin'] . '</td>
    <td><b>Nominálna hodnota</b><br>100000</td>
    <td><b>Mena</b><br>' . $data['mena'] . '</td>
    <td><b>TRH</b><br>' . $data['trh'] . '</td>
</tr>
<tr>';
if ($data['druh'] === "Bonds") {
    $html .= '<td><b>Ročná úroková sadzba</b><br>' . $data['kupon'] . '</td>
    <td><b>Splatnosť</b><br>' . $data['splatnost'] . '</td>';
}
$html .= '
    <td ' . ($data['druh'] !== "Bonds" ? 'colspan="3"' : '') . '><b>Klient</b><br>' . $data['klient'] . '</td>
    <td colspan="3"><b>Obchod uskutočnil</b><br>' . $data['dealer'] . '</td>
</tr>

<tr><td colspan="6" class="header">Zadanie pokynu</td></tr>
<tr>
    <td><b>Zadaný limitný kurz</b><br>' . $data['zadany_kurz'] . '</td>
    <td><b>Zadané o hod.</b><br>' . $data['zadany_cas'] . '</td>
    <td><b>Príkaz platný do</b><br>' . $data['zadany_platny_do'] . '</td>
    <td><b>Zadal</b><br>' . $data['zadany_zadal'] . '</td>
    <td colspan="2"><b>Podpis</b><br>
    <span>' . $data['zadany_zadal'] . '</span></td>
</tr>

<tr><td colspan="6" class="header">Realizácia pokynu</td></tr>
<tr>
    <td><b>Protistrana</b><br>' . $data['protistrana'] . '</td>
    <td><b>Meno</b><br>' . $data['realizovany_meno'] . '</td>
    <td><b>Zadaný limitný kurz</b><br>' . $data['zadany_kurz'] . '</td>
    <td><b>Zadané o hod.</b><br>' . $data['realizovany_cas'] . '</td>
    <td><b>Realizovaný kurz</b><br>' . $data['realizovany_kurz'] . '</td>
    <td><b>Realizoval</b><br>' . $data['realizoval'] . '</td>
</tr>

<tr>
    <td colspan="' . ($data['druh'] === "Bonds" ? "2" : "3") . '"><b>Realizovaný objem</b><br>' . $data['realizovany_objem'] . '</td>
    <td colspan="' . ($data['druh'] === "Bonds" ? "2" : "3") . '"><b>' . ($data['druh'] === "Bonds" ? "Objem nominálnej hodnoty" : "Objem obchodu") . '</b><br>' . $data['objem_nominalny'] . '</td>';
if ($data['druh'] === "Bonds") {
    $html .= '<td><b>AUv na 1 ks</b><br>' . $data['auvk_1ks'] . '</td>
    <td><b>AUv celkom</b><br>' . $data['auvk_celkom'] . '</td>';
}
$html .= '
</tr>';
if ($data['druh'] === "Bonds") {
    $html .= '
<tr>
    <td colspan="6"><b>Objem NH a AUv v mene CP</b><br>' . $data['nh_auvk'] . '</td>
</tr>';
}
$html .= '
<tr><td colspan="6" class="header">Kontrola</td></tr>
<tr>
    <td colspan="3"><b>Kontroloval</b><br><span>' . $username . '</span></td>
    <td><b>Dňa</b><br><span>' . $today . '</span></td>
    <td colspan="2"><b>Podpis Dealera</b><br><span>' . $username . '</span></td>
</tr>
</table>
';

$pdf->writeHTML($html, true, false, true, false, '');
$pdf->Output('/home/<USER>/www/temp/pdf/pokyn_' . $uniqueFilename . '.pdf', 'F');

?>
<a href="/temp/pdf/pokyn_<?php echo $uniqueFilename; ?>.pdf" target="_blank" class="hidden" id="pdfLink">-</a>
<script>
    document.getElementById("pdfLink").click();
</script>
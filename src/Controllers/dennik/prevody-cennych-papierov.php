<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
require_once("/home/<USER>/www/src/Components/exports/excel/excelController.php");
require_once("/home/<USER>/www/src/Components/exports/excel/exportToExcelButton.php");

$mesiac = $_POST["mesiac"];
$usporiadanie = $_POST["usporiadanie"];
$stvrtrok = $_POST["stvrtrok"];
$rok = $_POST["rok"];
$zmluva = $_POST["zmluva"];

if ($stvrtrok == 1) {
    $datefrom = "01.01." . $rok;
    $dateto = "31.03." . $rok;
}
if ($stvrtrok == 2) {
    $datefrom = "01.04." . $rok;
    $dateto = "30.06." . $rok;
}
if ($stvrtrok == 3) {
    $datefrom = "01.07." . $rok;
    $dateto = "30.09." . $rok;
}
if ($stvrtrok == 4) {
    $datefrom = "01.10." . $rok;
    $dateto = "31.12." . $rok;
}

if ($stvrtrok != 0) {
    $dateQuery = " and kcpod.datummv >= to_date('$datefrom','dd.mm.yyyy')
                 and kcpod.datummv <= to_date('$dateto','dd.mm.yyyy') ";
}

if ($month != 0) {
    $dateQuery = " and to_char(kcpod.datummv,'mm') = '$month'
                 and to_char(kcpod.datummv, 'yyyy') = '$rok' ";
}

if ($month == 0 && $stvrtrok == 0) {
    $dateQuery = " and to_char(kcpod.datummv, 'yyyy') = '$rok' ";

}
//---------------------------------------

if ($rok > 2008) {
    $ref = '\'EUR\'';
    $reftext = 'EUR';
} else {
    $ref = '\'SKK\'';
    $reftext = 'SKK';
}

$query = "SELECT 
            pod.cislozmluvy as Klientod,
            pk.cislozmluvy as Klientk,
            kcpod.kusov as Pocet,
            kcpod.datummv as Datum,
            kcpod.currencyidtrade as Mena,
            d.isinreal as Isin, 
            d.cpnaz as Nazov
            from
            konfirmaciacp kcpod,
            portfolio pod,
            konfirmaciacp kcpk,
            portfolio pk,
            dbequitycurrric dcuric,
            dbequitycurr dcur,
            dbequity d
            where
            kcpod.druhobchodu = 'vyber-pr'
            and kcpod.subjektid = pod.fondid
            and kcpk.druhobchodu = 'vklad-pr'
            and kcpk.subjektid = pk.fondid
            and kcpk.dealidseq = kcpod.dealidseq + 1
            and kcpod.ric = dcuric.ric
            $dateQuery
            and dcuric.isincurr = dcur.isincurr
            and dcur.isin = d.isin
            Order by 
            $usporiadanie
            ";
$dataRes = Connection::getDataFromDatabase($query, defaultDB);
$data = $dataRes[1];
$dataToExport = [];

foreach ($data as $item) {
    $dataToExport[] = [
        "datum" => $item["datum"],
        "klient" => $item["klient"],
        "nazov" => $item["nazov"],
        "isin" => $item["isin"],
        "pocet" => $item["pocet"],
        "suma" => $item["suma"],
        "mena" => $item["mena"],
        "lokalnamena" => $item["lokalnamena"]
    ];
}

$columns = [
    "Dátum",
    "Portfólio",
    "Názov",
    "ISIN",
    "Počet",
    "Suma",
    "Mena",
    "V " . $reftext
];
?>
<section style="margin-bottom: 8rem">
    <div class="mx-auto">
        <?php if ($dataRes[0] === 0) { ?>
            <div class="p-4 mb-4 text-sm text-blue-800 rounded-lg bg-blue-50 dark:bg-gray-800 dark:text-blue-400"
                role="alert">
                <span class="font-bold">Informácia!</span> Nenašli sa žiadne údaje podľa zadaných kritérií
            </div>
        <?php } ?>
        <div class="relative bg-white shadow-md dark:bg-gray-800 sm:rounded-lg">
            <div class="flex items-center py-3 px-1 justify-between">
                <h2 class="font-bold my-4 text-2xl dark:text-gray-100 text-gray-800">Prevody cenných papierov za rok
                    <?php echo $rok; ?>
                </h2>
                <div class="flex items-center gap-2">
                    <?php $button = new ExportToExcelButton($dataToExport, $columns, "prevody-cennych-papierov-obchodny-dennik-" . $rok . "-" . $mena, "nvm");
                    echo $button->render();
                    ?>
                </div>
            </div>
            <div class="overflow-x-visible">
                <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                        <tr>
                            <th scope="col" class="px-4 py-3">Dátum</th>
                            <th scope="col" class="px-4 py-3">Portfólio</th>
                            <th scope="col" class="px-4 py-3">Názov CP</th>
                            <th scope="col" class="px-4 py-3">ISIN</th>
                            <th scope="col" class="px-4 py-3">Počet</th>
                            <th scope="col" class="px-4 py-3">V cene</th>
                            <th scope="col" class="px-4 py-3">Mena</th>
                            <th scope="col" class="px-4 py-3">V <?php echo $reftext ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        foreach ($data as $item) { ?>
                            <tr
                                class="bg-white dark:bg-gray-800 dark:hover:bg-gray-600 transition-all cursor-pointer hover:bg-gray-200 border-b dark:border-gray-700">
                                <td class="px-4 py-2">
                                    <span
                                        class="text-sm text-gray-900 dark:text-gray-200 font-bold px-2 py-0.5 rounded"><?php echo $item['datum'] ?></span>
                                </td>
                                <td class="px-4 py-2">
                                    <span
                                        class="text-sm text-gray-900 dark:text-gray-200 font-bold px-2 py-0.5 rounded"><?php echo $item['klient']; ?></span>
                                </td>
                                <td class="px-4 py-2">
                                    <span
                                        class="text-sm text-gray-900 dark:text-gray-200 font-bold px-2 py-0.5 rounded"><?php echo $item['nazov']; ?></span>
                                </td>
                                <td class="px-4 py-2">
                                    <span
                                        class="text-sm text-gray-900 dark:text-gray-200 font-bold px-2 py-0.5 rounded"><?php echo $item['isin']; ?></span>
                                </td>
                                <td class="px-4 py-2">
                                    <span
                                        class="text-sm text-gray-900 dark:text-gray-200 font-bold px-2 py-0.5 rounded"><?php echo round($item['pocet'], 0); ?></span>
                                </td>
                                <td class="px-4 py-2">
                                    <span
                                        class="text-sm text-gray-900 dark:text-gray-200 font-bold px-2 py-0.5 rounded"><?php echo round($item['suma'], 2); ?></span>
                                </td>
                                <td class="px-4 py-2">
                                    <span
                                        class="text-sm text-gray-900 dark:text-gray-200 font-bold px-2 py-0.5 rounded"><?php echo $item['mena']; ?></span>
                                </td>
                                <td class="px-4 py-2">
                                    <span
                                        class="text-sm text-gray-900 dark:text-gray-200 font-bold px-2 py-0.5 rounded"><?php echo number_format(round($item['lokalnamena'], 2), 2, ".", " "); ?></span>
                                </td>
                            </tr>
                            <?php
                        }
                        ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</section>
<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
require_once("/home/<USER>/www/src/Components/exports/excel/excelController.php");
require_once("/home/<USER>/www/src/Components/exports/excel/exportToExcelButton.php");

$rok = $_POST["rok"];
$mena = $_POST["mena"];
$query = "WITH subjektid_lookup AS (SELECT p1.dealid,
                                 CASE
                                     WHEN COUNT(po1.cislozmluvy) = 1 THEN MAX(po1.cislozmluvy)
                                     ELSE ''
                                     END AS subjektid
                          FROM pool p1
                                   JOIN pooldetail pd1 ON p1.poolid = pd1.poolid
                                   JOIN portfolio po1 ON pd1.subjektid = po1.fondid
                          GROUP BY p1.dealid)

SELECT ktv.dealid,
       pa.skratka,
       ktv.z_td                                          AS zaciatok,
       ktv.k_td                                          AS koniec,
       ktv.dk_td,
       ktv.sum_td,
       ktv.pd_td,
       ktv.ir_td,
       ktv.iv_b,
       ktv.sum_td,
       ktv.iv_n,
       ktv.mena,
       ktv.cond1,
       ktv.dealer,
       ktv.datum_cas_obchodu,
       tp.typ_pokynu,
       ktv.suma_dane,
       CASE
           WHEN ktv.logactivityid = 25 THEN 'Splatené'
           WHEN ktv.logactivityid IN (4, 8) THEN 'Zámer'
           WHEN ktv.logactivityid IN (12, 13, 14, 15, 16, 17) THEN 'Potvrdené'
           ELSE 'Založené'
           END                                           AS stav,
       2                                                 AS detail,
       COALESCE(sl.subjektid, 'Sympatia', p.cislozmluvy) AS subjektid
FROM konfirmaciaktv ktv
         JOIN partner pa ON pa.partnerid = ktv.partnerid
         LEFT JOIN typy_pokynov tp ON tp.typid = ktv.typ_pokynu
         LEFT JOIN portfolio p ON ktv.subjektid = p.fondid
         LEFT JOIN subjektid_lookup sl ON ktv.dealid = sl.dealid
WHERE TO_CHAR(ktv.z_td, 'yyyy') = '$rok'
  AND ktv.logactivityid <> 3 AND mena = '$mena'
ORDER BY ktv.dealid DESC";

$dataRes = Connection::getDataFromDatabase($query, defaultDB);
$data = $dataRes[1];

$columns = [
    "Dátum a čas obchodu",
    "Banka",
    "Od",
    "Do",
    "Počet dní",
    "Konvencia",
    "Istina",
    "Mena",
    "Úroková sadzba",
    "Úrok brutto",
    "Daň",
    "Úrok netto",
    "Typ pokynu",
    "Dealer",
    "Klient",
    "Stav"
];
?>
<section style="margin-bottom: 8rem">
    <div class="mx-auto">
        <div class="flex items-center py-3 px-1 justify-between">
            <h2 class="font-bold my-4 text-2xl dark:text-gray-100 text-gray-800">Termínované vklady za rok
                <?php echo $rok; ?>
            </h2>
            <div class="flex items-center gap-2">
                <?php $button = new ExportToExcelButton($data, $columns, "terminovane-vklady-" . $rok . "-" . $mena, "nvm");
                echo $button->render();
                ?>
            </div>
        </div>
        <div class="relative bg-white shadow-md dark:bg-gray-800 sm:rounded-lg">
            <div class="overflow-x-visible">
                <table class="w-full text-xs text-left text-gray-500 dark:text-gray-400">
                    <thead
                        class="text-xs sticky top-[4.5rem] text-gray-700 uppercase bg-gray-50 dark:bg-gray-900 dark:text-gray-200">
                        <tr>
                            <th scope="col" class="px-2 w-10 py-1" style="font-size: 0.65rem">Dátum a čas obchodu</th>
                            <th scope="col" class="px-2 w-10 py-1" style="font-size: 0.65rem">Banka</th>
                            <th scope="col" class="px-2 w-10 py-1" style="font-size: 0.65rem">Od</th>
                            <th scope="col" class="px-2 w-10 py-1" style="font-size: 0.65rem">Do</th>
                            <th scope="col" class="px-2 w-10 py-1" style="font-size: 0.65rem">Počet dní</th>
                            <th scope="col" class="px-2 w-10 py-1" style="font-size: 0.65rem">Konvencia</th>
                            <th scope="col" class="px-2 w-10 py-1" style="font-size: 0.65rem">Istina</th>
                            <th scope="col" class="px-2 w-10 py-1" style="font-size: 0.65rem">Mena</th>
                            <th scope="col" class="px-2 w-10 py-1" style="font-size: 0.65rem">Úroková sadzba</th>
                            <th scope="col" class="px-2 w-10 py-1" style="font-size: 0.65rem">Úrok brutto</th>
                            <th scope="col" class="px-2 w-10 py-1" style="font-size: 0.65rem">Daň</th>
                            <th scope="col" class="px-2 w-10 py-1" style="font-size: 0.65rem">Úrok netto</th>
                            <th scope="col" class="px-2 w-10 py-1" style="font-size: 0.65rem">Typ pokynu</th>
                            <th scope="col" class="px-2 w-10 py-1" style="font-size: 0.65rem">Dealer</th>
                            <th scope="col" class="px-2 w-10 py-1" style="font-size: 0.65rem">Klient</th>
                            <th scope="col" class="px-2 w-10 py-1" style="font-size: 0.65rem">Stav</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        foreach ($data as $item) {
                            ?>
                            <tr
                                class="bg-white dark:bg-gray-800 dark:hover:bg-gray-600 border-b transition-all hover:bg-gray-300 cursor-pointer dark:border-gray-700">
                                <td class="px-2 py-2 w-10">
                                    <span
                                        class="text-xs text-gray-900 dark:text-gray-300 font-bold px-2 py-0.5 rounded"><?php echo $item['datum_cas_obchodu'] ?></span>
                                </td>
                                <td class="p-1 w-10">
                                    <span
                                        class="text-xs text-gray-900 dark:text-gray-300 font-bold px-2 py-0.5 rounded"><?php echo $item['skratka']; ?></span>
                                </td>
                                <td class="p-1 w-10">
                                    <span
                                        class="text-xs text-gray-900 dark:text-gray-300 font-bold px-2 py-0.5 rounded"><?php echo $item['zaciatok']; ?></span>
                                </td>
                                <td class="p-1 w-10">
                                    <span
                                        class="text-xs text-gray-900 dark:text-gray-300 font-bold px-2 py-0.5 rounded"><?php echo $item['koniec']; ?></span>
                                </td>
                                <td class="p-1 w-10">
                                    <span
                                        class="text-xs text-gray-900 dark:text-gray-300 font-bold px-2 py-0.5 rounded"><?php echo $item['pd_td']; ?></span>
                                </td>
                                <td class="p-1 w-10">
                                    <span
                                        class="text-xs text-gray-900 dark:text-gray-300 font-bold px-2 py-0.5 rounded"><?php echo $item['cond1']; ?></span>
                                </td>
                                <td class="p-1 flex w-32">
                                    <span
                                        class="text-xs text-gray-900 dark:text-gray-300 font-bold px-2 py-0.5 rounded"><?php echo number_format($item['sum_td'], 2, ".", " "); ?></span>
                                </td>
                                <td class="p-1 w-10">
                                    <span
                                        class="text-xs text-gray-900 dark:text-gray-300 font-bold px-2 py-0.5 rounded"><?php echo $item['mena']; ?></span>
                                </td>
                                <td class="p-1 w-10">
                                    <span
                                        class="text-xs text-gray-900 dark:text-gray-300 font-bold px-2 py-0.5 rounded"><?php echo $item['ir_td']; ?></span>
                                </td>
                                <td class="p-1 w-10">
                                    <span
                                        class="text-xs text-gray-900 dark:text-gray-300 font-bold px-2 py-0.5 rounded"><?php echo $item['iv_b']; ?></span>
                                </td>
                                <td class="p-1 w-10">
                                    <span
                                        class="text-xs text-gray-900 dark:text-gray-300 font-bold px-2 py-0.5 rounded"><?php echo $item['suma_dane']; ?></span>
                                </td>
                                <td class="p-1 w-10">
                                    <span
                                        class="text-xs text-gray-900 dark:text-gray-300 font-bold px-2 py-0.5 rounded"><?php echo $item['iv_n']; ?></span>
                                </td>
                                <td class="p-1 w-10">
                                    <span
                                        class="text-xs text-gray-900 dark:text-gray-300 font-bold px-2 py-0.5 rounded"><?php echo $item['typ_pokynu']; ?></span>
                                </td>
                                <td class="p-1 w-10">
                                    <span
                                        class="text-xs text-gray-900 dark:text-gray-300 font-bold px-2 py-0.5 rounded"><?php echo $item['dealer']; ?></span>
                                </td>
                                <td class="p-1 w-10">
                                    <span
                                        class="text-xs text-gray-900 dark:text-gray-300 font-bold px-2 py-0.5 rounded"><?php echo $item['subjektid']; ?></span>
                                </td>
                                <td class="p-1 text-center">
                                    <?php if ($item["stav"] === "Splatené") { ?>
                                        <span
                                            class="inline-flex items-center gap-2 bg-green-500 text-white font-bold px-2 py-1 rounded-md"
                                            style="font-size: 0.7rem;"><?php echo $item['stav']; ?>
                                            <svg class="w-4 h-4 text-white" aria-hidden="true"
                                                xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor"
                                                viewBox="0 0 24 24">
                                                <path fill-rule="evenodd"
                                                    d="M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10-4.477 10-10 10S2 17.523 2 12Zm13.707-1.293a1 1 0 0 0-1.414-1.414L11 12.586l-1.793-1.793a1 1 0 0 0-1.414 1.414l2.5 2.5a1 1 0 0 0 1.414 0l4-4Z"
                                                    clip-rule="evenodd" />
                                            </svg>
                                        </span>
                                    <?php } else if ($item["stav"] === "Potvrdené") { ?>
                                            <span
                                                class="inline-flex items-center gap-2 bg-green-800 text-white font-bold px-2 py-1 rounded-md"
                                                style="font-size: 0.7rem;"><?php echo $item['stav']; ?>
                                                <svg class="w-4 h-4 text-white" aria-hidden="true"
                                                    xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none"
                                                    viewBox="0 0 24 24">
                                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                                        stroke-width="2"
                                                        d="M8.5 11.5 11 14l4-4m6 2a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                                                </svg>
                                            </span>
                                    <?php } else { ?>
                                            <span
                                                class="inline-flex items-center gap-2 bg-blue-400 text-white font-bold px-2 py-1 rounded-md"
                                                style="font-size: 0.7rem;"><?php echo $item['stav']; ?>
                                                <svg class="w-4 h-4 text-white" aria-hidden="true"
                                                    xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none"
                                                    viewBox="0 0 24 24">
                                                    <path stroke="currentColor" stroke-linecap="round" stroke-width="2"
                                                        d="M8 7V6a1 1 0 0 1 1-1h11a1 1 0 0 1 1 1v7a1 1 0 0 1-1 1h-1M3 18v-7a1 1 0 0 1 1-1h11a1 1 0 0 1 1 1v7a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1Zm8-3.5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0Z" />
                                                </svg>
                                            </span>
                                    <?php } ?>
                                </td>
                            </tr>
                            <?php
                        }
                        ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</section>
<script src="/src/assets/js/prehlady/nbs-gfi/excel.js"></script>
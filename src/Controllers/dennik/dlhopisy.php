<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
require_once("/home/<USER>/www/src/Components/exports/excel/excelController.php");
require_once("/home/<USER>/www/src/Components/exports/excel/exportToExcelButton.php");

$rok = $_POST["rok"];
$rokplus = $rok + 1;

$query = "WITH base_kcp AS (SELECT *
                  FROM konfirmaciacp
                  WHERE datpok >= DATE '$rok-01-01'
                    AND datpok < DATE '$rokplus-01-01'
                    AND logactivityid <> 3
                    AND eqid = 'Bonds'
                    AND druhobchodu IN ('nakup', 'predaj')),
     core AS (SELECT kcp.dealid,
                     kcp.datpok,
                     kcp.subjektid,
                     kcp.druhobchodu,
                     kcp.currencyidtrade AS mena,
                     kcp.datum_cas_obcho<PERSON>,
                     kcp.datum_pokynu,
                     kcp.assigneduserid,
                     kcp.pokyn_ako,
                     kcp.kusov,
                     kcp.limitkurz,
                     kcp.auvwork,
                     kcp.limitprice,
                     kcp.partnerid,
                     kcp.typ_pokynu,
                     kcp.ric,
                     kcp.logactivityid,
                     d.isinreal,
                     d.cpnazskratka,
                     d.kupon,
                     d.maturitydate,
                     d.nominalemisie,
                     pa.skratka,
                     fk.faktor
              FROM base_kcp kcp
                       JOIN partner pa ON pa.partnerid = kcp.partnerid
                       JOIN typy_pokynov tp ON tp.typid = kcp.typ_pokynu
                       JOIN dbequitycurrric dcuric ON dcuric.ric = kcp.ric
                       JOIN dbequitycurr dcur ON dcur.isincurr = dcuric.isincurr
                       JOIN dbequity d ON dcur.isin = d.isin
                       LEFT JOIN floatkupon fk ON fk.isincurrric = dcuric.isincurrric),
     intent AS (SELECT dealid,
                       skratka,
                       datpok,
                       subjektid,
                       isinreal                          AS isin,
                       cpnazskratka                      AS nazov,
                       CASE
                           WHEN druhobchodu = 'nakup' THEN 'Nákup'
                           WHEN druhobchodu = 'predaj' THEN 'Predaj'
                           END                           AS druh,
                       mena,
                       kusov                             AS pocet,
                       limitkurz                         AS kurz,
                       auvwork                           AS auv,
                       limitprice                        AS cena,
                       limitprice + COALESCE(auvwork, 0) AS sumaspolu,
                       0                                 AS poplatok,
                       NULL::date                        AS predFV,
                       NULL::date                        AS predMV,
                       NULL::date                        AS FV,
                       NULL::date                        AS MV,
                       datum_cas_obchodu,
                       typ_pokynu,
                       'Zámer'                           AS stav,
                       3                                 AS detail,
                       datum_pokynu,
                       assigneduserid,
                       pokyn_ako,
                       kupon,
                       maturitydate,
                       nominalemisie,
                       faktor
                FROM core
                WHERE logactivityid = 4),
     traded AS (SELECT dealid,
                       skratka,
                       datpok,
                       subjektid,
                       isinreal,
                       cpnazskratka                      AS nazov,
                       CASE
                           WHEN druhobchodu = 'nakup' THEN 'Nákup'
                           WHEN druhobchodu = 'predaj' THEN 'Predaj'
                           END                           AS druh,
                       mena,
                       kusov                             AS pocet,
                       limitkurz                         AS kurz,
                       auvwork                           AS auv,
                       limitprice                        AS cena,
                       limitprice + COALESCE(auvwork, 0) AS sumaspolu,
                       0                                 AS poplatok,
                       NULL::date                        AS predFV,
                       NULL::date                        AS predMV,
                       NULL::date                        AS FV,
                       NULL::date                        AS MV,
                       datum_cas_obchodu,
                       typ_pokynu,
                       'Zobchodované'                    AS stav,
                       3                                 AS detail,
                       datum_pokynu,
                       assigneduserid,
                       pokyn_ako,
                       kupon,
                       maturitydate,
                       nominalemisie,
                       faktor
                FROM core
                WHERE logactivityid = 10),
     reconf AS (SELECT k.dealid,
                       k.skratka,
                       k.datpok,
                       k.subjektid,
                       k.isinreal,
                       k.cpnazskratka             AS nazov,
                       CASE
                           WHEN k.druhobchodu = 'nakup' THEN 'Nákup'
                           WHEN k.druhobchodu = 'predaj' THEN 'Predaj'
                           END                    AS druh,
                       k.mena,
                       rcp.kusovreal              AS pocet,
                       rcp.kurzreal               AS kurz,
                       rcp.auvreal                AS auv,
                       rcp.cenaobchodu            AS cena,
                       rcp.transsuma              AS sumaspolu,
                       rcp.poplatok,
                       rcp.datvysporiadaniabu     AS predFV,
                       rcp.datvysporiadaniamu     AS predMV,
                       rcp.datvysporiadaniabureal AS FV,
                       rcp.datvysporiadaniamureal AS MV,
                       k.datum_cas_obchodu,
                       k.typ_pokynu,
                       CASE
                           WHEN rcp.datvysporiadaniabureal IS NULL
                               AND rcp.datvysporiadaniamureal IS NULL THEN 'Potvrdené'
                           WHEN rcp.datvysporiadaniabureal IS NOT NULL
                               AND rcp.datvysporiadaniamureal IS NULL THEN 'Iba finančne'
                           WHEN rcp.datvysporiadaniabureal IS NULL
                               AND rcp.datvysporiadaniamureal IS NOT NULL THEN 'Iba majetkovo'
                           WHEN rcp.datvysporiadaniabureal IS NOT NULL
                               AND rcp.datvysporiadaniamureal IS NOT NULL THEN 'Vysporiadané'
                           END                    AS stav,
                       5                          AS detail,
                       k.datum_pokynu,
                       k.assigneduserid,
                       k.pokyn_ako,
                       k.kupon,
                       k.maturitydate,
                       k.nominalemisie,
                       k.faktor
                FROM core k
                         JOIN rekonfirmaciacp rcp ON k.dealid = rcp.dealid
                WHERE k.logactivityid = 12),
     combined AS (SELECT *
                  FROM intent
                  UNION ALL
                  SELECT *
                  FROM traded
                  UNION ALL
                  SELECT *
                  FROM reconf)
SELECT *
FROM combined
GROUP BY dealid, skratka, datpok, subjektid, isin, nazov, druh, mena, pocet, kurz, auv, cena, sumaspolu, poplatok,
         predfv, predMV, fv, mv, datum_cas_obchodu, typ_pokynu, stav, detail, datum_pokynu, assigneduserid, pokyn_ako, kupon, maturitydate, faktor, nominalemisie
ORDER BY datum_cas_obchodu DESC";
//echo $query;
$dataRes = Connection::getDataFromDatabase($query, defaultDB);
$data = $dataRes[1];

$dataToExport = [];

foreach ($data as $item) {
    $subjektid = $item["subjektid"];
    $id = $item["dealid"];

    $klientiText = "";
    if ($subjektid == 0) {
        $query = "SELECT 
                po.cislozmluvy,
            pd.ks
            
        FROM
                pool p,
                pooldetail pd,
                portfolio po
        WHERE 
                p.poolid = pd.poolid
                and pd.subjektid = po.fondid
                and p.dealid = $id
        ORDER BY
            po.cislozmluvy";
        $cisloZmluvyRes = Connection::getDataFromDatabase($query, defaultDB);
        $cisloZmluvy = $cisloZmluvyRes[1];
        foreach ($cisloZmluvy as $key => $value) {
            $klientiText .= $value["cislozmluvy"] . " " . $value["ks"] . " ks" . ($key < sizeof($cisloZmluvy) - 1 ? "; " : "");
        }
    } else {
        $query2 = "select cislozmluvy from portfolio where fondid = $subjektid";
        $cisloZmluvyRes = Connection::getDataFromDatabase($query2, defaultDB);
        $cisloZmluvy = $cisloZmluvyRes[1][0];
        $klientiText = $cisloZmluvy["cislozmluvy"];
    }

    switch ($item["stav"]) {
        case "Potvrdené":
            $FV = "";
            $MV = "";
            break;
        case "Iba finančne":
            $FV = $item["fv"];
            $MV = "";
            break;
        case "Iba majetkovo":
            $FV = "";
            $MV = $item["mv"];
            break;
        case "Vysporiadané":
            $FV = $item["fv"];
            $MV = $item["mv"];
            break;
    }
    $dataToExport[] = [
        "datum_cas_obchodu" => $item["datum_cas_obchodu"],
        "obchod" => $item["obchod"],
        "nazov" => $item["nazov"],
        "isin" => $item["isin"],
        "mena" => $item["mena"],
        "pocet" => $item["pocet"],
        "kurz" => $item["kurz"],
        "cena_bez_auv" => $item["cena"],
        "auv" => $item["auv"],
        "cena_spolu" => $item["sumaspolu"],
        "dohodnuty_datum_fin_vys" => $item["predfv"],
        "dohodnuty_datum_maj_vys" => $item["predmv"],
        "realne_datum_fin_vys" => $FV,
        "realne_datum_maj_vys" => $MV,
        "klient" => $klientiText,
        "stav" => $item["stav"]
    ];
}

$columns = [
    "Dátum a čas obchodu",
    "Obchod",
    "Názov",
    "ISIN",
    "Mena",
    "Počet",
    "Kurz",
    "Cena bez AUV",
    "AUV",
    "Cena spolu",
    "Dohodnutý dátum fin. vys.",
    "Dohodnutý dátum maj. vys.",
    "Reálne fin. vysporiadanie",
    "Reálne maj. vysporiadanie",
    "Klient",
    "Stav"
];
?>
<section style="margin-bottom: 8rem">
    <div id="toast"></div>
    <div class="mx-auto">
        <div class="flex items-center py-3 px-1 justify-between">
            <h2 class="font-bold my-4 text-2xl dark:text-gray-100 text-gray-800">Dlhopisy za rok
                <?php echo $rok; ?>
            </h2>
            <div class="flex items-center gap-2">
                <?php $button = new ExportToExcelButton($dataToExport, $columns, "dhopisy-obchodny-dennik-" . $rok . "-" . $mena, "nvm");
                echo $button->render();
                ?>
            </div>
        </div>
        <div class="relative bg-white shadow-md dark:bg-gray-800 sm:rounded-lg">
            <div class="overflow-x-visible">
                <table class="w-full text-xs text-left text-gray-500 dark:text-gray-400">
                    <thead
                        class="text-xs sticky top-[4.5rem] text-gray-700 uppercase bg-gray-50 dark:bg-gray-900 dark:text-gray-200">
                        <tr>
                            <th scope="col" class="px-2 w-10 py-1" style="font-size: 0.65rem">Dátum a čas obchodu</th>
                            <th scope="col" class="px-2 w-10 py-1" style="font-size: 0.65rem">Obchod</th>
                            <th scope="col" class="px-2 w-10 py-1" style="font-size: 0.65rem">Názov</th>
                            <th scope="col" class="px-2 w-10 py-1" style="font-size: 0.65rem">ISIN</th>
                            <th scope="col" class="px-2 w-10 py-1" style="font-size: 0.65rem">Mena</th>
                            <th scope="col" class="px-2 w-10 py-1" style="font-size: 0.65rem">Počet</th>
                            <th scope="col" class="px-2 w-10 py-1" style="font-size: 0.65rem">Kurz</th>
                            <th scope="col" class="px-2 w-10 py-1" style="font-size: 0.65rem">Cena bez AUV</th>
                            <th scope="col" class="px-2 w-10 py-1" style="font-size: 0.65rem">AUV</th>
                            <th scope="col" class="px-2 w-10 py-1" style="font-size: 0.65rem">Cena spolu</th>
                            <th scope="col" class="px-2 w-10 py-1" style="font-size: 0.65rem">Dohodnutý dátum fin. vys.
                            </th>
                            <th scope="col" class="px-2 w-10 py-1" style="font-size: 0.65rem">Dohodnutý dátum maj. vys.
                            </th>
                            <th scope="col" class="px-2 w-10 py-1" style="font-size: 0.65rem">Skutočný dátum fin. vys.
                            </th>
                            <th scope="col" class="px-2 w-10 py-1" style="font-size: 0.65rem">Skutočný dátum maj. vys.
                            </th>
                            <th scope="col" class="px-2 w-10 py-1" style="font-size: 0.65rem">Klient</th>
                            <th scope="col" class="px-2 w-10 py-1" style="font-size: 0.65rem">Stav</th>
                            <th scope="col">-</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        foreach ($data as $item) {
                            $subjektid = $item["subjektid"];

                            if ($subjektid == 0) {
                                $retazec = '<button data-modal-target="modal' . $item["dealid"] . '" data-modal-toggle="modal' . $item["dealid"] . '" 
                                    class="block text-white w-full bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 
                                    font-bold rounded-md text-xs px-2 py-1 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800" type="button">
                                    Pool
                                    </button>';
                            } else {
                                $query2 = "select cislozmluvy from portfolio where fondid = $subjektid";
                                $cisloZmluvyRes = Connection::getDataFromDatabase($query2, defaultDB);
                                $cisloZmluvy = $cisloZmluvyRes[1][0];
                                $retazec = $cisloZmluvy["cislozmluvy"];
                            }

                            switch ($item["stav"]) {
                                case "Potvrdené":
                                    $FV = "";
                                    $MV = "";
                                    break;
                                case "Iba finančne":
                                    $FV = $item["fv"];
                                    $MV = "";
                                    break;
                                case "Iba majetkovo":
                                    $FV = "";
                                    $MV = $item["mv"];
                                    break;
                                case "Vysporiadané":
                                    $FV = $item["fv"];
                                    $MV = $item["mv"];
                                    break;
                            }
                            ?>
                            <tr
                                class="bg-white dark:bg-gray-800 border-b transition-all dark:hover:bg-gray-600 hover:bg-gray-300 cursor-pointer dark:border-gray-700">
                                <td class="px-2 pl-0 py-2 w-10">
                                    <span
                                        class="text-xs text-red-900 dark:text-gray-100 font-bold px-2 py-0.5 rounded"><?php echo $item['datum_cas_obchodu'] ?></span>
                                </td>
                                <td class="p-1 w-10">
                                    <?php if ($item["druh"] === "Predaj") { ?>
                                        <span
                                            class="text-xs bg-red-300 text-red-900 font-bold px-2 py-0.5 rounded"><?php echo $item['druh']; ?></span>
                                    <?php } else { ?>
                                        <span
                                            class="text-xs bg-green-300 text-green-900 font-bold px-2 py-0.5 rounded"><?php echo $item['druh']; ?></span>
                                    <?php } ?>
                                </td>
                                <td class="p-1 w-10">
                                    <span
                                        class="text-xs text-red-900 dark:text-gray-100 font-bold px-2 py-0.5 rounded"><?php echo $item['nazov']; ?></span>
                                </td>
                                <td class="p-1 w-10">
                                    <span
                                        class="text-xs text-red-900 dark:text-gray-100 font-bold px-2 py-0.5 rounded"><?php echo $item['isin']; ?></span>
                                </td>
                                <td class="p-1 w-10">
                                    <span
                                        class="text-xs text-red-900 dark:text-gray-100 font-bold px-2 py-0.5 rounded"><?php echo $item['mena']; ?></span>
                                </td>
                                <td class="p-1 w-10">
                                    <span
                                        class="text-xs text-red-900 dark:text-gray-100 font-bold px-2 py-0.5 rounded"><?php echo round($item['pocet'], 0); ?></span>
                                </td>
                                <td class="p-1 w-10">
                                    <span
                                        class="text-xs text-red-900 dark:text-gray-100 font-bold px-2 py-0.5 rounded"><?php echo round($item['kurz'], 5); ?></span>
                                </td>
                                <td class="p-1 w-10">
                                    <span
                                        class="text-xs text-red-900 dark:text-gray-100 font-bold px-2 py-0.5 rounded"><?php echo round($item['cena'], 2); ?></span>
                                </td>
                                <td class="p-1 w-10">
                                    <span
                                        class="text-xs text-red-900 dark:text-gray-100 font-bold px-2 py-0.5 rounded"><?php echo round($item['AUV'], 2); ?></span>
                                </td>
                                <td class="p-1 w-10">
                                    <span
                                        class="text-xs text-red-900 dark:text-gray-100 font-bold px-2 py-0.5 rounded"><?php echo round($item['sumaspolu'], 2); ?></span>
                                </td>
                                <td class="p-1 w-10">
                                    <span
                                        class="text-xs text-red-900 dark:text-gray-100 font-bold px-2 py-0.5 rounded"><?php echo $item['predfv']; ?></span>
                                </td>
                                <td class="p-1 w-10">
                                    <span
                                        class="text-xs text-red-900 dark:text-gray-100 font-bold px-2 py-0.5 rounded"><?php echo $item['predmv']; ?></span>
                                </td>
                                <td class="p-1 w-10">
                                    <span
                                        class="text-xs text-red-900 dark:text-gray-100 font-bold px-2 py-0.5 rounded"><?php echo $FV; ?></span>
                                </td>
                                <td class="p-1 w-10">
                                    <span
                                        class="text-xs text-red-900 dark:text-gray-100 font-bold px-2 py-0.5 rounded"><?php echo $MV; ?></span>
                                </td>
                                <td class="p-1 w-10">
                                    <?php echo $retazec; ?>
                                </td>
                                <td class="p-1">
                                    <?php if ($item["stav"] === "Vysporiadané") { ?>
                                        <span
                                            class="inline-flex items-center gap-2 bg-green-500 text-white font-bold px-2 py-1 rounded-md"
                                            style="font-size: 0.7rem;"><?php echo $item['stav']; ?>
                                            <svg class="w-4 h-4 text-white" aria-hidden="true"
                                                xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor"
                                                viewBox="0 0 24 24">
                                                <path fill-rule="evenodd"
                                                    d="M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10-4.477 10-10 10S2 17.523 2 12Zm13.707-1.293a1 1 0 0 0-1.414-1.414L11 12.586l-1.793-1.793a1 1 0 0 0-1.414 1.414l2.5 2.5a1 1 0 0 0 1.414 0l4-4Z"
                                                    clip-rule="evenodd" />
                                            </svg>
                                        </span>
                                    <?php } else if ($item["stav"] === "Potvrdené") { ?>
                                            <span
                                                class="inline-flex items-center gap-2 bg-green-800 text-white font-bold px-2 py-1 rounded-md"
                                                style="font-size: 0.7rem;"><?php echo $item['stav']; ?>
                                                <svg class="w-4 h-4 text-white" aria-hidden="true"
                                                    xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none"
                                                    viewBox="0 0 24 24">
                                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                                        stroke-width="2"
                                                        d="M8.5 11.5 11 14l4-4m6 2a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                                                </svg>
                                            </span>
                                    <?php } else { ?>
                                            <span
                                                class="inline-flex items-center gap-2 bg-blue-400 text-white font-bold px-2 py-1 rounded-md"
                                                style="font-size: 0.7rem;"><?php echo $item['stav']; ?>
                                                <svg class="w-4 h-4 text-white" aria-hidden="true"
                                                    xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none"
                                                    viewBox="0 0 24 24">
                                                    <path stroke="currentColor" stroke-linecap="round" stroke-width="2"
                                                        d="M8 7V6a1 1 0 0 1 1-1h11a1 1 0 0 1 1 1v7a1 1 0 0 1-1 1h-1M3 18v-7a1 1 0 0 1 1-1h11a1 1 0 0 1 1 1v7a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1Zm8-3.5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0Z" />
                                                </svg>
                                            </span>
                                    <?php } ?>
                                </td>
                                <td class="p-1 w-10">
                                    <?php
                                    $eqid = "Bonds";
                                    include "src/Components/buttons/generatePDFButton.php"; ?>
                                </td>
                            </tr>
                            <?php
                        }
                        ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</section>

<?php
foreach ($data as $item) {
    $subjektid = $item["subjektid"];
    if ($subjektid == 0) {
        $id = $item["dealid"];
        $detail = $item["detail"];
        $query = "SELECT  
                po.cislozmluvy,
            pd.ks
            
        FROM
                pool p,
                pooldetail pd,
                portfolio po
        WHERE 
                p.poolid = pd.poolid
                and pd.subjektid = po.fondid
                and p.dealid = $id
        ORDER BY
            po.cislozmluvy";
        $modalDataRes = Connection::getDataFromDatabase($query, defaultDB);
        $modalData = $modalDataRes[1];
        $retazec = '<div id="modal' . $item["dealid"] . '" tabindex="-1" aria-hidden="true" class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
        <div class="relative p-4 w-full max-w-2xl max-h-full">
            <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
                <!-- Modal header -->
                <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                        Dlhopisy
                    </h3>
                    <button type="button" class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-xs w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white" data-modal-hide="modal' . $item["dealid"] . '">
                        <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                        </svg>
                        <span class="sr-only">Close modal</span>
                    </button>
                </div>
                <div class="p-4 md:p-5 space-y-4">
                    <table class="w-full text-xs text-left text-gray-500 dark:text-gray-400">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">';
        if ($detail === 5) {
            $retazec .= '<tr>
                            <th scope="col" class="px-4 py-3">Portfólio</th>
                            <th scope="col" class="px-4 py-3">Počet kusov</th>
                            <th scope="col" class="px-4 py-3">Cena bez AÚV</th>
                            <th scope="col" class="px-4 py-3">AÚV</th>
                            <th scope="col" class="px-4 py-3">Cena s AÚV</th>
                        </tr>';
        } else {
            $retazec .= '<tr>
                            <th scope="col" class="px-4 py-3">Portfólio</th>
                            <th scope="col" class="px-4 py-3">Počet kusov</th>
                        </tr>';
        }

        $retazec .= '</thead>
                    <tbody>';
        foreach ($modalData as $modal) {
            if ($detail === 5) {
                $retazec .= '<tr
                class="bg-white dark:bg-gray-800 border-b dark:border-gray-700">
                <td class="px-4 py-2">
                    <span
                        class="text-xs text-red-900 dark:text-gray-100 font-bold px-2 py-0.5 rounded">' . $modal['cislozmluvy'] . '</span>
                </td>
                <td class="px-4 py-2">
                    <span
                        class="text-xs text-red-900 dark:text-gray-100 font-bold px-2 py-0.5 rounded">' . round($modal['ksreal'], 0) . '</span>
                </td>
                <td class="px-4 py-2">
                    <span
                        class="text-xs text-red-900 dark:text-gray-100 font-bold px-2 py-0.5 rounded">' . round($modal['transsumareal'] - $modal["auvreal"], 2) . '</span>
                </td>
                <td class="px-4 py-2">
                    <span
                        class="text-xs text-red-900 dark:text-gray-100 font-bold px-2 py-0.5 rounded">' . round($modal['auvreal'], 0) . '</span>
                </td>
                <td class="px-4 py-2">
                    <span
                        class="text-xs text-red-900 dark:text-gray-100 font-bold px-2 py-0.5 rounded">' . round($modal['transsumareal'], 0) . '</span>
                </td>
                ';
            } else {

            }

        }
        $retazec .= '
                    </tbody>
                    </table>
                </div>
            </div>
        </div></div>';
        echo $retazec;
    }
}
<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
require_once("/home/<USER>/www/src/Components/exports/excel/excelController.php");
require_once("/home/<USER>/www/src/Components/exports/excel/exportToExcelButton.php");

$mesiac = $_POST["mesiac"];
$usporiadanie = $_POST["usporiadanie"];
$stvrtrok = $_POST["stvrtrok"];
$rok = $_POST["rok"];
$zmluva = $_POST["zmluva"];

if ($stvrtrok == 1) {
    $datefrom = "01.01." . $rok;
    $dateto = "31.03." . $rok;
}
if ($stvrtrok == 2) {
    $datefrom = "01.04." . $rok;
    $dateto = "30.06." . $rok;
}
if ($stvrtrok == 3) {
    $datefrom = "01.07." . $rok;
    $dateto = "30.09." . $rok;
}
if ($stvrtrok == 4) {
    $datefrom = "01.10." . $rok;
    $dateto = "31.12." . $rok;
}

if ($stvrtrok != 0) {
    $dateQuery = " and k.datum_zauctovania >= to_date('$datefrom','dd.mm.yyyy')
                 and k.datum_zauctovania <= to_date('$dateto','dd.mm.yyyy') ";
}

if ($month != 0) {
    $dateQuery = " and to_char(k.datum_zauctovania,'mm') = '$month'
                 and to_char(k.datum_zauctovania, 'yyyy') = '$rok' ";
}

if ($month == 0 && $stvrtrok == 0) {
    $dateQuery = " and to_char(k.datum_zauctovania, 'yyyy') = '$rok' ";

}

$query = "SELECT 
            od.suma as Suma, 
            od.mena as Mena, 
            odp.cislozmluvy as Od, 
            kp.cislozmluvy as Komu,
            od.ucet as UcetZ,
            k.ucet as UcetNa, 
            od.datum_zauctovania as Datum
        from 
            konfirmaciapp od,
            konfirmaciapp k,
            portfolio odp,
            portfolio kp
        where 
            od.subjektid = odp.fondid
            and k.subjektid = kp.fondid
            and k.subjektid = od.subjektid
            and od.druhobchodu = 'presun'
            and k.druhobchodu = 'presun'
            and od.dealid_related = k.dealid
            $dateQuery
            and od.logactivityid = 12
        union all
        select 
            od.suma as Suma, 
            od.mena as Mena, 
            'Pool' as Od, 
            'Pool' as Komu,
            od.ucet as UcetZ,
            k.ucet as UcetNa, 
            od.datum_zauctovania as Datum
        from 
            konfirmaciapp od,
            konfirmaciapp k
        where 
            od.druhobchodu = 'presun'
            and k.druhobchodu = 'presun'
            and od.dealid_related = k.dealid
            and od.subjektid = 0
            and k.subjektid = 0
            $dateQuery
            and od.logactivityid = 12
        Order by 
            $usporiadanie
";

$dataRes = Connection::getDataFromDatabase($query, defaultDB);
$data = $dataRes[1];
$dataToExport = [];

foreach ($data as $item) {
    $dataToExport[] = [
        "datum" => $item["datum"],
        "od" => $item["od"],
        "komu" => $item["komu"],
        "ucetz" => $item["ucetz"],
        "ucetna" => $item["ucetna"],
        "suma" => $item["suma"],
        "mena" => $item["mena"]
    ];
}

$columns = [
    "Dátum",
    "Portfólio",
    "Z účtu",
    "Na účet",
    "Suma",
    "Mena"
];

?>
<section style="margin-bottom: 8rem">
    <div class="mx-auto px-3">
        <div class="relative bg-white shadow-md dark:bg-gray-800 sm:rounded-lg">
            <div class="flex items-center py-3 px-1 justify-between">
                <h2 class="font-bold my-4 text-2xl dark:text-gray-100 text-gray-800">Vysporiadané výbery peňažných
                    prostriedkov za rok
                    <?php echo $rok; ?>
                </h2>
                <div class="flex items-center gap-2">
                    <?php $button = new ExportToExcelButton($dataToExport, $columns, "vysporiadane-presuny-penaznych-prostriedkov-obchodny-dennik-" . $rok . "-" . $mena, "nvm");
                    echo $button->render();
                    ?>
                </div>
            </div>
            <div class="overflow-x-visible">
                <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                        <tr>
                            <th scope="col" class="px-4 py-3">Dátum</th>
                            <th scope="col" class="px-4 py-3">Portfólio</th>
                            <th scope="col" class="px-4 py-3">Z účtu</th>
                            <th scope="col" class="px-4 py-3">Na účet</th>
                            <th scope="col" class="px-4 py-3">Suma</th>
                            <th scope="col" class="px-4 py-3">Mena</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        foreach ($data as $item) { ?>
                            <tr class="bg-white dark:bg-gray-800 border-b hover:dark:bg-gray-600 dark:border-gray-700">
                                <td class="px-4 py-2">
                                    <span
                                        class="text-sm text-gray-900 dark:text-gray-100 font-bold px-2 py-0.5 rounded"><?php echo $item['datum'] ?></span>
                                </td>
                                <td class="px-4 py-2">
                                    <span
                                        class="text-sm text-gray-900 dark:text-gray-100 font-bold px-2 py-0.5 rounded"><?php echo $item['od']; ?></span>
                                </td>

                                <td class="px-4 py-2">
                                    <span
                                        class="text-sm text-gray-900 dark:text-gray-100 font-bold px-2 py-0.5 rounded"><?php echo $item['ucetz']; ?></span>
                                </td>
                                <td class="px-4 py-2">
                                    <span
                                        class="text-sm text-gray-900 dark:text-gray-100 font-bold px-2 py-0.5 rounded"><?php echo $item['ucetna']; ?></span>
                                </td>
                                <td class="px-4 py-2">
                                    <span
                                        class="text-sm text-gray-900 dark:text-gray-100 font-bold px-2 py-0.5 rounded"><?php echo round($item['suma'], 2); ?></span>
                                </td>
                                <td class="px-4 py-2">
                                    <span
                                        class="text-sm text-gray-900 dark:text-gray-100 font-bold px-2 py-0.5 rounded"><?php echo $item['mena']; ?></span>
                                </td>
                            </tr>
                            <?php
                        }
                        ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</section>
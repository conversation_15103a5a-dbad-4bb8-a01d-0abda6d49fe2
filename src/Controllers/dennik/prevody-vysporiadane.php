<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
require_once("/home/<USER>/www/src/Components/exports/excel/excelController.php");
require_once("/home/<USER>/www/src/Components/exports/excel/exportToExcelButton.php");

$mesiac = $_POST["mesiac"];
$usporiadanie = $_POST["usporiadanie"];
$stvrtrok = $_POST["stvrtrok"];
$rok = $_POST["rok"];
$zmluva = $_POST["zmluva"];

if ($stvrtrok == 1) {
    $datefrom = "01.01." . $rok;
    $dateto = "31.03." . $rok;
}
if ($stvrtrok == 2) {
    $datefrom = "01.04." . $rok;
    $dateto = "30.06." . $rok;
}
if ($stvrtrok == 3) {
    $datefrom = "01.07." . $rok;
    $dateto = "30.09." . $rok;
}
if ($stvrtrok == 4) {
    $datefrom = "01.10." . $rok;
    $dateto = "31.12." . $rok;
}

if ($stvrtrok != 0) {
    $dateQuery = " and od.datum_zauctovania >= to_date('$datefrom','dd.mm.yyyy')
                 and od.datum_zauctovania <= to_date('$dateto','dd.mm.yyyy') ";
}

if ($month != 0) {
    $dateQuery = " and to_char(od.datum_zauctovania,'mm') = '$month'
                 and to_char(od.datum_zauctovania, 'yyyy') = '$rok' ";
}

if ($month == 0 && $stvrtrok == 0) {
    $dateQuery = " and to_char(od.datum_zauctovania, 'yyyy') = '$rok' ";

}

$query = "SELECT od.suma as Suma, od.mena as Mena, odp.cislozmluvy as Od, kp.cislozmluvy as Komu, od.datum_zauctovania as Datum
            from konfirmaciapp od,
            konfirmaciapp k,
            portfolio odp,
            portfolio kp
            where od.subjektid = odp.fondid
            and k.subjektid = kp.fondid
            and od.druhobchodu = 'prevod'
            and k.druhobchodu = 'prevod'
            and od.dealid_related = k.dealid
            $dateQuery
            and od.logactivityid = 12
            Order by 
            $usporiadanie
            ";

$dataRes = Connection::getDataFromDatabase($query, defaultDB);
$data = $dataRes[1];
$dataToExport = [];

foreach ($data as $item) {
    $dataToExport[] = [
        "datum" => $item["datum"],
        "od" => $item["od"],
        "komu" => $item["komu"],
        "suma" => $item["suma"],
        "mena" => $item["mena"]
    ];
}

$columns = [
    "Dátum",
    "Z portfólia",
    "Na portfólio",
    "Suma",
    "Mena"
];
?>
<section style="margin-bottom: 8rem">
    <div class="mx-auto p-3">
        <div class="relative bg-white shadow-md dark:bg-gray-800 sm:rounded-lg">
            <div class="flex items-center py-3 px-1 justify-between">
                <h2 class="font-bold my-4 text-2xl dark:text-gray-100 text-gray-800">Vysporiadané prevody peňažných
                    prostriedkov za rok
                    <?php echo $rok; ?>
                </h2>
                <div class="flex items-center gap-2">
                    <?php $button = new ExportToExcelButton($dataToExport, $columns, "vysporiadane-prevody-penaznych-prostriedkov-obchodny-dennik-" . $rok . "-" . $mena, "nvm");
                    echo $button->render();
                    ?>
                </div>
            </div>
            <div class="overflow-x-visible">
                <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                        <tr>
                            <th scope="col" class="px-4 py-3">Zadaný dátum</th>
                            <th scope="col" class="px-4 py-3">Z portfólia</th>
                            <th scope="col" class="px-4 py-3"></th>
                            <th scope="col" class="px-4 py-3">Na portfólio</th>
                            <th scope="col" class="px-4 py-3">Suma</th>
                            <th scope="col" class="px-4 py-3">Mena</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        foreach ($data as $item) { ?>
                            <tr
                                class="bg-white dark:bg-gray-800 dark:hover:bg-gray-600 transition-all cursor-pointer hover:bg-gray-200 border-b dark:border-gray-700">
                                <td class="px-4 py-2">
                                    <span
                                        class="text-sm text-gray-900 dark:text-gray-100 font-bold px-2 py-0.5 rounded"><?php echo $item['datum'] ?></span>
                                </td>
                                <td class="px-4 py-2 w-1">
                                    <span
                                        class="text-sm text-gray-900  bg-red-300 text-red-700 font-bold px-2 py-0.5 rounded"><?php echo $item['od']; ?></span>
                                </td>
                                <td class="px-4 py-2 w-1">
                                    <svg class="w-6 h-6 text-green-800 dark:text-white" aria-hidden="true"
                                        xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none"
                                        viewBox="0 0 24 24">
                                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                            stroke-width="2" d="M19 12H5m14 0-4 4m4-4-4-4" />
                                    </svg>

                                </td>
                                <td class="px-4 py-2">
                                    <span
                                        class="text-sm text-gray-900  bg-green-300 text-green-700 shadow-green-300 shadow-md font-bold px-2 py-0.5 rounded"><?php echo $item['komu']; ?></span>
                                </td>
                                <td class="px-4 py-2">
                                    <span
                                        class="text-sm text-gray-900 dark:text-gray-100 font-bold px-2 py-0.5 rounded"><?php echo round($item['suma'], 2); ?></span>
                                </td>
                                <td class="px-4 py-2">
                                    <span
                                        class="text-sm text-gray-900 dark:text-gray-100 font-bold px-2 py-0.5 rounded"><?php echo $item['mena']; ?></span>
                                </td>
                            </tr>
                            <?php
                        }
                        ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</section>
<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
require_once("/home/<USER>/www/src/Components/exports/excel/excelController.php");
require_once("/home/<USER>/www/src/Components/exports/excel/exportToExcelButton.php");

$rok = $_GET["rok"];
$mena = $_GET["mena"];
$offset = $_GET["offset"];

$query = "WITH max_today AS (SELECT MAX(datum) AS today_date
                   FROM today),
     paid AS (SELECT DISTINCT u.cub,
                              u.cubpartnera,
                              u.suma,
                              mca.mena,
                              'Zaplatené'     AS stav,
                              u.datesplatnost AS datum,
                              u.datesplatnost,
                              prl.mc_dealid   AS dealid,
                              1               AS detail,
                              p.fondid        AS subjektid,
                              p.cislozmluvy AS cislozmluvy
              FROM poplatok_register popl
                       JOIN portfolio p ON p.fondid = popl.fondid
                       JOIN poplatok_register_links prl ON prl.poplatok_register_id = popl.id
                       JOIN majetokcestaarchiv mca ON prl.mc_dealid = mca.dealid AND mca.mena = '$mena'
                       JOIN uhrada u ON u.dealid = mca.dealid
              WHERE EXTRACT(YEAR FROM u.datesplatnost) = $rok),
     prepared AS (SELECT DISTINCT mca.ucetaktiva AS cub,
                                  ''             AS cubpartnera,
                                  mca.pocet      AS suma,
                                  mca.mena,
                                  'Pripravené'   AS stav,
                                  mt.today_date  AS datum,
                                  mt.today_date  AS datesplatnost,
                                  prl.mc_dealid  AS dealid,
                                  2              AS detail,
                                  p.fondid       AS subjektid,
                                  p.cislozmluvy AS cislozmluvy
                  FROM poplatok_register popl
                           JOIN portfolio p ON p.fondid = popl.fondid
                           JOIN poplatok_register_links prl ON prl.poplatok_register_id = popl.id
                           JOIN majetokcesta mca ON prl.mc_dealid = mca.dealid AND mca.mena = '$mena'
                           CROSS JOIN max_today mt
                  WHERE prl.uhrada_id IS NULL
                    AND EXTRACT(YEAR FROM mt.today_date) = $rok)
SELECT *
FROM paid
UNION ALL
SELECT *
FROM prepared
ORDER BY datesplatnost DESC
LIMIT 50
OFFSET $offset";
$dataRes = Connection::getDataFromDatabase($query, defaultDB);
$data = $dataRes[1];
$offsetplus = $offset + 50;
foreach ($data as $key => $item) {
    if ($item["subjektid"] == 0) {
        $retazec = '<button data-modal-target="modal' . $item["dealid"] . '" data-modal-toggle="modal' . $item["dealid"] . '" 
                                    class="block text-white w-full bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 
                                    font-medium rounded-lg text-xs px-2 py-0.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800" type="button">
                                    Pool
                                    </button>';
    } else {
        $retazec = $item["cislozmluvy"];
    }
    ?>
    <tr <?php if ($key === sizeof($data) - 1)
        echo "hx-get='/api/obchodny-dennik/platby-poplatkov/data?offset=$offsetplus&rok=$rok&mena=$mena' hx-target='#poplatkyTBODY' hx-swap='beforeend' hx-trigger='revealed'" ?> class="bg-white dark:bg-gray-800 dark:hover:bg-gray-600 transition-all cursor-pointer
                                    hover:bg-gray-200 border-b dark:border-gray-700">
            <td class="px-4 py-2">
                <span
                    class="text-sm text-gray-900 dark:text-gray-200 font-bold px-2 py-0.5 rounded"><?php echo $item['datum'] ?></span>
        </td>
        <td class="px-4 py-2">
            <span
                class="text-sm text-gray-900 dark:text-gray-200 font-bold px-2 py-0.5 rounded"><?php echo $item['cub']; ?></span>
        </td>
        <td class="px-4 py-2">
            <span
                class="text-sm text-gray-900 dark:text-gray-200 font-bold px-2 py-0.5 rounded"><?php echo $item['cubpartnera']; ?></span>
        </td>
        <td class="px-4 py-2">
            <span
                class="text-sm text-gray-900 dark:text-gray-200 font-bold px-2 py-0.5 rounded"><?php echo round($item['suma'], 2); ?></span>
        </td>
        <td class="px-4 py-2">
            <span
                class="text-sm text-gray-900 dark:text-gray-200 font-bold px-2 py-0.5 rounded"><?php echo $item['mena']; ?></span>
        </td>
        <td class="px-4 py-2">
            <?php echo $retazec; ?>
        </td>
        <td class="p-1 text-center">
            <?php if ($item["stav"] === "Zaplatené") { ?>
                <span class="inline-flex items-center gap-2 bg-green-500 text-white font-bold px-2 py-1 rounded-md"
                    style="font-size: 0.7rem;"><?php echo $item['stav']; ?>
                    <svg class="w-4 h-4 text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                        fill="currentColor" viewBox="0 0 24 24">
                        <path fill-rule="evenodd"
                            d="M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10-4.477 10-10 10S2 17.523 2 12Zm13.707-1.293a1 1 0 0 0-1.414-1.414L11 12.586l-1.793-1.793a1 1 0 0 0-1.414 1.414l2.5 2.5a1 1 0 0 0 1.414 0l4-4Z"
                            clip-rule="evenodd" />
                    </svg>
                </span>
            <?php } else { ?>
                <span
                    class="inline-flex items-center gap-2 bg-yellow-300 text-gray-900 dark:text-gray-200 font-bold px-2 py-1 rounded-md"
                    style="font-size: 0.7rem;"><?php echo $item['stav']; ?>
                    <svg class="w-4 h-4 text-black" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                        fill="currentColor" viewBox="0 0 24 24">
                        <path fill-rule="evenodd"
                            d="M20.337 3.664c.213.212.354.486.404.782.294 1.711.657 5.195-.906 6.76-1.77 1.768-8.485 5.517-10.611 6.683a.987.987 0 0 1-1.176-.173l-.882-.88-.877-.884a.988.988 0 0 1-.173-1.177c1.165-2.126 4.913-8.841 6.682-10.611 1.562-1.563 5.046-1.198 6.757-.904.296.05.57.191.782.404ZM5.407 7.576l4-.341-2.69 4.48-2.857-.334a.996.996 0 0 1-.565-1.694l2.112-2.111Zm11.357 7.02-.34 4-2.111 2.113a.996.996 0 0 1-1.69-.565l-.422-2.807 4.563-2.74Zm.84-6.21a1.99 1.99 0 1 1-3.98 0 1.99 1.99 0 0 1 3.98 0Z"
                            clip-rule="evenodd" />
                    </svg>
                </span>
            <?php } ?>
        </td>
    </tr>
    <?php
}
?>
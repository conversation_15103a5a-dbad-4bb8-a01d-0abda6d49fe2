function enableEditing() {
    let inputs = document.querySelectorAll('#zakladneUdajeForm input, select, textarea');
    for (let i = 0; i < inputs.length; i++) {
        inputs[i].removeAttribute('disabled');
        inputs[i].classList.remove("bg-gray-50")
    }
    document.getElementById("formBtn").remove()
    const submitBtn = document.createElement("button");
    submitBtn.innerHTML = "Aktualizovať dáta";
    submitBtn.className = "focus:outline-none flex mb-8 items-center text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-green-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2"
    submitBtn.setAttribute("type", "submit");
    submitBtn.addEventListener("click", () => {
        setTimeout(() => {
            document.getElementById('resultofpost').style.display = 'none';
            window.location.reload();
        }, 5000);
    })
    document.getElementById("btnWrapper").appendChild(submitBtn)
}
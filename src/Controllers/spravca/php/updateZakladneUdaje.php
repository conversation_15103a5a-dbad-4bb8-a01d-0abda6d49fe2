<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

if (isset($_POST)) {
    $params = [$_POST["name"], $_POST["shortname"], $_POST["street"], $_POST["city"], $_POST["state"], $_POST["psc"], $_POST["ico"], $_POST["dic"], $_POST["datumvznik"], $_POST["kontaktnazov"], $_POST["kontaktphone"], $_POST["kontaktphone"], $_POST["kontaktfax"], $_POST["kontaktemail"], $_POST["poznamka"], $_POST["mena"], $_POST["datumzalozenia"]];
    Connection::InsertUpdateCreateDelete("UPDATE spravca SET NAZOV = ?, 
                    NAZOVSHORT = ?, 
                    ADDRESS = ?, 
                    CITY = ?, 
                    STATEID = ?, 
                    POSTALCODE = ?, 
                    ICO = ?, 
                    DIC = ?, 
                    VZNIKDATE = ?, 
                    KONTAKTNAME = ?, 
                    KONTAKTPHONENUMBER = ?, 
                    KONTAKTMOBILNUMBER = ?, 
                    KONTAKTFAXNUMBER = ?, 
                    KONTAKTEMAIL = ?,
                    NOTE = ?, 
                    REFMENA = ?, 
                    EXDATE = ?
                    WHERE SPRAVCAID = 1", $params, defaultDB);
    echo '<div id="toast-simple" class="flex items-center w-full max-w-xs p-4 space-x-4 rtl:space-x-reverse text-gray-500 bg-white divide-x rtl:divide-x-reverse divide-gray-200 rounded-lg shadow dark:text-gray-400 dark:divide-gray-700 space-x dark:bg-gray-800" role="alert">
            <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 8.207-4 4a1 1 0 0 1-1.414 0l-2-2a1 1 0 0 1 1.414-1.414L9 10.586l3.293-3.293a1 1 0 0 1 1.414 1.414Z"/>
              </svg>
            <div class="ps-4 text-sm font-normal">Dáta úspešne aktualizované!</div>
        </div>';
} else {
    echo "EROROO";
}



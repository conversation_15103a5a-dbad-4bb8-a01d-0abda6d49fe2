<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
require_once "/home/<USER>/www/src/lib/notifications/Notification.class.php";

$sess_userid = $_SESSION["user"]["data"]["userid"];
$username = $_SESSION["user"]["data"]["username"];
$data = json_decode($_POST["data"], true);
$queryPool = [];

foreach ($data as $key => $item) {
    $kodobratu = 283;
    $obratID = Connection::getDataFromDatabase("SELECT nextval('majetok_today_obratid')", defaultDB)[1][0]["nextval"];
    $suma = str_replace(",", ".", $data[$key]["suma"]);
    $povodnaSuma = str_replace(",", ".", $data[$key]["suma"]);
    $hrubyVynos = str_replace(",", ".", $data[$key]["hrubyVynos"]);
    $cistyVynos = str_replace(",", ".", $data[$key]["cistyVynos"]);
    $dan = str_replace(",", ".", $data[$key]["dan"]);
    $ucetaktiva = $data[$key]["ucetaktiva"];
    $kusovreal = $data[$key]["pocetkusov"];
    $vs = $data[$key]["vs"];
    $mena = $data[$key]["mena"];
    $cubFond = $data[$key]["cubFond"];
    $danSadzba = str_replace(",", ".", $data[$key]["danSazdba"]);
    $nakus = str_replace(",", ".", $data[$key]["nakus"]);
    $krajinaPovodu = $data[$key]["krajinaPovodu"];
    $kodaktiva = $data[$key]["kodaktiva"];
    $hotovostna = $data[$key]["hotovostna"];
    $realneSplatenie = $data[$key]["realneSplatenie"];
    $dividendaTyp = $data[$key]["dividendaTyp"];
    $fond_id = $data[$key]["fond_id"];
    if ($hotovostna == 0) {
        $cubFond = $data[$key]["cubFond"];
    }

    if ($hotovostna) {
        $queryUcet = "(select cub from fondsbu where mena = '$mena' and fondid=$fond_id LIMIT 1)";
        $jednotka = $mena;
    } else {
        $queryUcet = "'" . $ucetaktiva . "'";
        $jednotka = "ks";
    }

    if ($action == "splatenie" and $suma != $cistyVynos && $hotovostna) {
        $queryPool[] = [
            "query" => "INSERT INTO majetoktoday
        (obratid,destinacia,subjektid,kodobratu,
			 uctovnykod,eqid,kodaktiva,ucetaktiva,jednotka,
			 pocet,mena,md_d,obratdatetimereal,obratdatatimezauctovane)
			select
			 $obratID,'splatenieakcia',$fond_id,kodobratu,
			 uctovnykod,equid,'$kodaktiva', '$ucetaktiva', '$jednotka',
			 $povodnaSuma,'$mena',md_d,(select datum from today where fondid=$fond_id),(select datum from today where fondid=$fond_id)
			from kodobratumd_d where kodobratu in (281) and subkodobratu=$dividendaTyp",
            "params" => [],
            "db" => defaultDB,
            "name" => "Insert into majetoktoday"
        ];

        $queryPool[] = [
            "query" => "INSERT INTO majetoktoday
        (obratid,destinacia,subjektid,kodobratu,
			 uctovnykod,eqid,kodaktiva,ucetaktiva,jednotka,
			 pocet,mena,md_d,obratdatetimereal,obratdatatimezauctovane)
			select
			 $obratID,'splatenieakcia',$fond_id,kodobratu,
			 uctovnykod,equid,'$kodaktiva', '$ucetaktiva', '$jednotka',
			 $cistyVynos,'$mena',md_d,(select datum from today where fondid=$fond_id),(select datum from today where fondid=$fond_id)
			from kodobratumd_d where kodobratu in (282) and subkodobratu=$dividendaTyp",
            "params" => [],
            "db" => defaultDB,
            "name" => "Insert into majetoktoday2"
        ];

        $zapisaneZmeny = true;
        $suma = $cistyVynos;
    }

    if ($action == "splatenie" and $suma != $povodnaSuma and !$hotovostna) {
        $queryPool[] = [
            "query" => "INSERT INTO majetoktoday
        (obratid,destinacia,subjektid,kodobratu,
					uctovnykod,eqid,kodaktiva,ucetaktiva,jednotka,
					pocet,mena,md_d,obratdatetimereal,obratdatatimezauctovane)
					select
					$obratID,'splatenieakcia',$fond_id,kodobratu,
					uctovnykod,equid,'$kodaktiva', $queryUcet, '$jednotka',
					$povodnaSuma,'$mena',md_d,(select datum from today where fondid=$fond_id),(select datum from today where fondid=$fond_id)
					from kodobratumd_d where kodobratu in (281) and subkodobratu=$dividendaTyp",
            "params" => [],
            "db" => defaultDB,
            "name" => "Insert into majetoktoday3"
        ];

        $queryPool[] = [
            "query" => "INSERT INTO majetoktoday
        (obratid,destinacia,subjektid,kodobratu,
					uctovnykod,eqid,kodaktiva,ucetaktiva,jednotka,
					pocet,mena,md_d,obratdatetimereal,obratdatatimezauctovane)
					select
					$obratID,'splatenieakcia',$fond_id,kodobratu,
					uctovnykod,equid,'$kodaktiva', $queryUcet, '$jednotka',
					$suma,'$mena',md_d,(select datum from today where fondid=$fond_id),(select datum from today where fondid=$fond_id)
					from kodobratumd_d where kodobratu in (282) and subkodobratu=$dividendaTyp",
            "params" => [],
            "db" => defaultDB,
            "name" => "Insert into majetoktoday4"
        ];

        $zapisaneZmeny = true;
    }

    if ($zapisaneZmeny) {
        $obratID = Connection::getDataFromDatabase("SELECT nextval('majetok_today_obratid')", defaultDB)[1][0]["nextval"];
    }

    //TU BOLO ODSTRANENE REALNE SPLATENIE Z PODMIENKY KEBY DACO LEBO SAK KAZDE SPLATENIE JE REALNE
    if ($action == "splatenie" && $hotovostna == 1) {
        $queryPool[] = [
            "query" => "INSERT INTO majetoktoday
        (obratid,destinacia,subjektid,kodobratu,
					uctovnykod,eqid,kodaktiva,ucetaktiva,jednotka,
					pocet,mena,md_d,obratdatetimereal,obratdatatimezauctovane)
					select
					$obratID,'splatenieakcia',$fond_id,kodobratu,
					uctovnykod,equid,'$mena', '$cubFond', '$jednotka',
					$suma,'$mena',md_d,(select datum from today where fondid=$fond_id), (select datum from today where fondid=$fond_id)
					from kodobratumd_d where kodobratu=283 and subkodobratu=$dividendaTyp and md_d=0",
            "params" => [],
            "db" => defaultDB,
            "name" => "Insert into majetoktoday5"
        ];

        $queryPool[] = [
            "query" => "INSERT INTO majetoktoday
        (obratid,destinacia,subjektid,kodobratu,
					uctovnykod,eqid,kodaktiva,ucetaktiva,jednotka,
					pocet,mena,md_d,obratdatetimereal,obratdatatimezauctovane)
					select
					$obratID,'splatenieakcia',$fond_id,kodobratu,
					uctovnykod,equid,'$kodaktiva', '$ucetaktiva' , '$jednotka',
					$suma,'$mena',md_d,(select datum from today where fondid=$fond_id), (select datum from today where fondid=$fond_id)
					from kodobratumd_d where kodobratu=283 and subkodobratu=$dividendaTyp and md_d=1",
            "params" => [],
            "db" => defaultDB,
            "name" => "Insert into majetoktoday6"
        ];
    } else {
        $queryPool[] = [
            "query" => "INSERT INTO majetoktoday
        (obratid,destinacia,subjektid,kodobratu,
					uctovnykod,eqid,kodaktiva,ucetaktiva,jednotka,
					pocet,mena,md_d,obratdatetimereal,obratdatatimezauctovane)
					select $obratID,'splatenieakcia',$fond_id,kodobratu,
		 uctovnykod,equid,'$kodaktiva', $queryUcet, '$jednotka',
		 $suma,'$mena',md_d,(select datum from today where fondid=$fond_id), (select datum from today where fondid=$fond_id)
		from kodobratumd_d where kodobratu in ($kodobratu) and subkodobratu=$dividendaTyp",
            "params" => [],
            "db" => defaultDB,
            "name" => "Insert into majetoktoday7"
        ];
    }

    if ($hotovostna == 1) {
        $queryPool[] = [
            "query" => "INSERT INTO majetoktoday
        (obratid,destinacia,subjektid,kodobratu,
							uctovnykod,eqid,kodaktiva,ucetaktiva,jednotka,
							pocet,mena,md_d,obratdatetimereal,obratdatatimezauctovane)
							select
							$obratID,'obratybu',$fond_id,kodobratu,
							uctovnykod,equid,'$kodaktiva', '$ucetaktiva', '$jednotka',
							$suma,'$mena',md_d,(select datum from today where fondid=$fond_id), (select datum from today where fondid=$fond_id)
							from kodobratumd_d where kodobratu in (284) and subkodobratu=$dividendaTyp and md_d=0",
            "params" => [],
            "db" => defaultDB,
            "name" => "Insert into majetoktoday8"
        ];

        $queryPool[] = [
            "query" => "INSERT INTO majetoktoday
        (obratid,destinacia,subjektid,kodobratu,
							uctovnykod,eqid,kodaktiva,ucetaktiva,jednotka,
							pocet,mena,md_d,obratdatetimereal,obratdatatimezauctovane)
							select $obratID,'obratybu',$fond_id,kodobratu,
							uctovnykod,equid,'$mena', '$cubFond', '$jednotka',
							$suma,'$mena',md_d,(select datum from today where fondid=$fond_id), (select datum from today where fondid=$fond_id)
							from kodobratumd_d where kodobratu in (284) and subkodobratu=$dividendaTyp and md_d=1",
            "params" => [],
            "db" => defaultDB,
            "name" => "Insert into majetoktoday9"
        ];
    } else {
        if ($cubFond == "") {
            $cubFond = str_replace("'", "", $queryUcet);
        }

        $queryPool[] = [
            "query" => "INSERT INTO majetoktoday
        (obratid,destinacia,subjektid,kodobratu,
						uctovnykod,eqid,kodaktiva,ucetaktiva,jednotka,
						pocet,mena,md_d,obratdatetimereal,obratdatatimezauctovane)
						select
						$obratID,'splatenieakcia',$fond_id,kodobratu,
						uctovnykod,equid,'$kodaktiva', '$cubFond', '$jednotka',
						$suma,'$mena',md_d,(select datum from today where fondid=$fond_id), (select datum from today where fondid=$fond_id)
						from kodobratumd_d where kodobratu in (284,285) and subkodobratu=$dividendaTyp",
            "params" => [],
            "db" => defaultDB,
            "name" => "Insert into majetoktoday10"
        ];
    }

    if ($hotovostna == 1) {
        $dealid = Connection::getDataFromDatabase("SELECT nextval('S_DIVIDENDA')", defaultDB)[1][0]["nextval"] . "909";
        $splateniePocet = $kusovreal;
        $splatenieSuma = $suma;
    } else {
        $dealid = Connection::getDataFromDatabase("SELECT nextval('S_DIVIDENDA_V_AKCIACH')", defaultDB)[1][0]["nextval"] . "910";
        $splateniePocet = $suma;
        $splatenieSuma = 0;
    }

    $datumVyplaty = Connection::getDataFromDatabase("SELECT datum from today where fondid = $fond_id", defaultDB)[1][0]["datum"];
    $queryPool[] = [
        "query" => "INSERT INTO splatenieakcia (dealid, subjektid, kodaktiva, subkodobratu,
					mena, pocet, suma, datum,
					fiktivne, datumvyplaty,datum_naroku) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
        "params" => [$dealid, $fond_id, $kodaktiva, $dividendaTyp, $mena, $splateniePocet, $splatenieSuma, $datumVyplaty, 0, $datumVyplaty, $datumVyplaty],
        "db" => defaultDB,
        "name" => "Insert into splatenieakcia"
    ];

    $queryPool[] = [
        "query" => "INSERT INTO splatenieakciaobratid (dealid, obratid) VALUES (?, ?)",
        "params" => [$dealid, $obratID],
        "db" => defaultDB,
        "name" => "Insert into splatenieakciaobratid"
    ];

    if ($hotovostna == 1) {
        $spracujPlatbu = true;
        $queryPool[] = [
            "query" => "INSERT INTO danvynosy (dealid, destinacia, dansadzba, danzaklad, dan,
					 mena, cub, stateid, vynoskus) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)",
            "params" => [
                $dealid,
                'splatenieakcia',
                (float) $danSadzba,
                $hrubyVynos,
                $dan,
                $mena,
                $cubFond,
                $krajinaPovodu,
                $nakus
            ],
            "db" => defaultDB,
            "name" => "Insert into danvynosy"
        ];

        $ss = str_replace("'", "", $ss);
        $ks = str_replace("'", "", $ks);
        $vs = str_replace("'", "", $vs);

        //parovanie na zaklade VS
        $queryPool[] = [
            "query" => "INSERT INTO majetokcesta
						(dealid,tranza,in_out,sparovanie,destinacia,
					 	 subjektid,eqid,kodaktiva,ucetaktiva,jednotka,
					 	 mena,pocet,cestafrom,cestatill,popis)
					values
						($dealid,0,1,'$vs','splatenieakcia',
						 $fond_id,'BU','$mena','$cubFond','$mena',
						 '$mena',$suma,(select datum from today where fondid = $fond_id), (select datum+15 from today where fondid = $fond_id),'splatenieakcia' 
						) ",
            "params" => [],
            "db" => defaultDB,
            "name" => "Insert into majetokcesta"
        ];

        if ($ss == '' or $ss == "null")
            $ss = 'null';
        else
            $ss = "'$ss'";
        if ($ks == '' or $ks == "null")
            $ks = 'null';
        else
            $ks = "'$ks'";
        if ($vs == '' or $vs == "null")
            $vs = 'null';
        else
            $vs = "'$vs'";

        $id = Connection::getDataFromDatabase("SELECT nextval('s_obratybu')", defaultDB)[1][0]["nextval"];
        $queryPool[] = [
            "query" => "INSERT INTO obratybu
        (id,cub,cubpartnera,forma,
						 ks,mena,nazpartnera,obratdatetime,
						 ss,subjektid,suma,vs,krdb,
						 logdatatimeactivity,logactivityid,loguserid
						)
						values
						($id,'$cubFond',null,0,
						 $ks,'$mena',null,(select datum from today where fondid=$fond_id),
						 $ss,$fond_id,$suma,$vs,1,
						 CURRENT_DATE,1,$sess_userid
						)",
            "params" => [],
            "db" => defaultDB,
            "name" => "Insert into obratybu"
        ];

        $queryPool[] = [
            "query" => "INSERT INTO obratybuobratid
        (obratid, id, atribut)
        values
        ($obratID, $id, 0)",
            "params" => [],
            "db" => defaultDB,
            "name" => "Insert into obratybuobratid"
        ];

        //NOTIFIKACE
    }
}
$transaction = Connection::runTransaction(defaultDB, null, $queryPool);
if (str_contains($transaction, "poriadku")) {
    $notification = new Notification(1, 'splatenieakcia', $dealid, 'splatenieDividendy', $sess_userid, $username, "splatenieDividendy", json_encode(["dealid", $dealid]), false, 0);
    $notification->createNotifcation(); ?>
    <div class="p-4 mb-4 text-sm text-green-800 rounded-lg bg-green-50 dark:bg-gray-600 dark:text-green-400" role="alert">
        <span class="font-medium">Dividendy boli úspešne splatné!</span> Bez chýb tentokrát :)
    </div>
    <?php
} else {
    echo $transaction;
}
<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

$dividendaTyp = $_GET["typ"];

$result = Connection::getDataFromDatabase("SELECT hotovost, md_d, (select to_char(max(datum),'dd.mm.yyyy') from today) as todayDate
 FROM dividendaakciatyp WHERE subkodobratu = $dividendaTyp", defaultDB);

header('Content-Type: application/json');

if ($result[0] === 0 || empty($result[1])) {
    echo json_encode([
        "success" => false,
        "message" => "No data found for the specified type"
    ]);
} else {
    $harakterDividendy = $result[1][0];
    echo json_encode([
        "success" => true,
        "hotovost" => $harakterDividendy["hotovost"],
        "md_d" => $harakterDividendy["md_d"],
        "todayDate" => $harakterDividendy["todayDate"],
    ]);
}
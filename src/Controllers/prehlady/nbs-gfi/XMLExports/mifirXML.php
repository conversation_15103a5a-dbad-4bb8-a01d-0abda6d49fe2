<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

$data = json_decode(file_get_contents('php://input'), true);
$dbdate = $data["date"];
$leiCodes = implode(',', $data["leiCodes"]);
if (sizeof($data["dealids"]) > 1) {
    $dealids = implode(',', $data["dealids"]);
} else {
    $dealids = $data["dealids"][0];
}

$query = "
			select subjektid, datum_cas_obchodu, hh.userid as userid, ha.eqid, druhobchodu, typ_zdanenia,hh.dealid as dealid, lei_kod, replace(rcico, '/', '') as rcico, /*upper(meno) as*/ meno, /*upper(prieznaz) as*/ prieznaz,
					datpok, case ha.eqid when 'Bonds' then pocet*nominalemisie else pocet end as pocet, currencynom, currencyidtrade, limitkurz, currencyidtrade,  
					case hh.eqid when 'Bonds' then round((pocet * limitkurz/100 * nominalemisie),2) + COALESCE(auvreal, auvreal1) else round((pocet * limitkurz),2) + COALESCE(auvreal, auvreal1) end as objem, 
					skratka, ha.isinreal as isin,cpnaz,'a' as LEIcode
			from	(
				select datum_cas_obchodu, userid, k.dealid, case when k.subjektid = 0 then pool.subjektid else k.subjektid end as subjektid,
						eqid, isin, druhobchodu, case when k.subjektid = 0 then ksreal else kusov end as pocet, currencyidtrade, limitkurz, datpok, datumfv, datummv, partnerid, 
						pool.auvreal, r.auvreal as auvreal1
				from konfirmaciacp k
				left join 	(
					select dealid, auvreal 
					from rekonfirmaciacp
							) r on k.dealid = r.dealid 
				left join 	(
					select dealid, subjektid, ksreal, transsumareal, auvreal, cenaobchodu 
					from pool p
					left join pooldetailreal pd on p.poolid = pd.poolid
							) pool on pool.dealid = k.dealid
				where kusovreal != 0 and druhobchodu = 'nakup' and datpok = '" . $dbdate . "' and k.dealid in (" . $dealids . ") 
					) hh 
			left join portfolio p on hh.subjektid = p.fondid
			left join podielnik po on po.podielnikid = p.podielnikid
			left join 	(
				select cpnaz, eqid, isin, maturitydate, currencynom, nominalemisie,isinreal
				from dbequity
						) ha on ha.isin = hh.isin
			left join partner pa on pa.partnerid = hh.partnerid
			
			union all
			
			select subjektid, datum_cas_obchodu, hh.userid as userid, ha.eqid, druhobchodu, typ_zdanenia, hh.dealid as dealid, lei_kod, replace(rcico, '/', '') as rcico, /*upper(meno) as*/ meno, /*upper(prieznaz) as*/ prieznaz, 
					datpok, case ha.eqid when 'Bonds' then pocet*nominalemisie else pocet end as pocet, currencynom, currencyidtrade, limitkurz, currencyidtrade,
					case hh.eqid when 'Bonds' then round((pocet * limitkurz/100 * nominalemisie),2) + COALESCE(auvreal, auvreal1) else round((pocet * limitkurz),2) + COALESCE(auvreal, auvreal1) end as objem, 					 
					skratka, ha.isinreal as isin,cpnaz,'a' as LEIcode
			from 	(
				select datum_cas_obchodu, userid, k.dealid, case when k.subjektid = 0 then pool.subjektid else k.subjektid end as subjektid, 
						eqid, isin, druhobchodu, case when k.subjektid = 0 then ksreal else kusov end as pocet, currencyidtrade, limitkurz, datpok, datumfv, datummv, partnerid, 
						pool.auvreal, r.auvreal as auvreal1
				from konfirmaciacp k
				left join 	(
					select dealid, auvreal 
					from rekonfirmaciacp
							) r on k.dealid = r.dealid 
				left join 	(
					select dealid, subjektid, ksreal, transsumareal, auvreal, cenaobchodu 
					from pool p 
					left join pooldetailreal pd on p.poolid = pd.poolid
							) pool on pool.dealid = k.dealid
				where kusovreal != 0 and druhobchodu = 'predaj' and datpok = '" . $dbdate . "' and k.dealid in (" . $dealids . ") 
					) hh 
			left join portfolio p on hh.subjektid = p.fondid
			left join podielnik po on po.podielnikid = p.podielnikid
			left join 	(
				select cpnaz, eqid, isin, maturitydate, currencynom, nominalemisie,isinreal
				from dbequity
						) ha on ha.isin = hh.isin
			left join partner pa on pa.partnerid = hh.partnerid
			
			order by datpok desc, dealid desc	
		";

$dataRes = Connection::getDataFromDatabase($query, defaultDB);
$data = $dataRes[1];
$xmldata = new DOMDocument('1.0', 'UTF-8');
if ($dataRes[0] != 0) {
    $i = 0;
    $dealids = "";
    $docasneLEI = 0;
    $docasneID = 0;

    while ($i <= $export) {
        $docasneLEI = $leiCodes[0];
        $pos = strpos($docasneLEI, ',');
        $docasneID = substr($docasneLEI, $pos + 1);
        $docasneLEI = substr($docasneLEI, 0, $pos);
        $dealidspole [] = array($docasneID, $docasneLEI);
        if ($dealids == "") $dealids = $docasneID;
        else $dealids = $dealids . "," . $docasneID;

        $i = $i + 1;
    }

    $protistrany = array
    (
        array("", ""),
        array("Adamant Capital", "2138003J5QNXXY2V3W55"),
        array("Banca Promos", "815600B43BECA3919584"),
        array("Banca Zarattini", "213800INM3OFHF9QS122"),
        array("Continental Capital Markets", "213800M3I5GUTKCDBU47"),
        array("�SOB", "52990096Q5LMCH1WU462"),
        array("ERSTE", "PQOH26KWDF7CG10L6792"),
        array("EXANTE", "635400MMGYK7HLRQGV31"),
        array("Intesa Sanpaolo S.p.A.", "2W8N8UU78PMDQKZENC08"),
        array("I� Yatirim", "7890005YQXBTRIYDY554"),
        array("Jay Capital", "213800OM9UEOFXJU2R36"),
        array("KBC Bank", "6B2PBRV1FCJDMR45RZ53"),
        array("Komerčná banka", "IYKCAVNFR8QGF00HV840"),
        array("Leonteq Securities AG", "ML61HP3A4MKTTA1ZB671"),
        array("Leonteq Securities (Europe) GmbH", "254900P87ORVJYW21N84"),
        array("Patria Finance", "31570010000000009213"),
        array("Polaris Alpha", "315700DD8NCJQOV6YX13"),
        array("Polaris Omega", "315700CXSHTD5UH5FF68"),
        array("Raiffeisen Bank", "9ZHRYM6F437SQJ6OUG95"),
        array("Slovenská sporiteľňa", "549300S2T3FWVVXWJI89"),
        array("Sympatia Financie", "315700ALQMS9PZOIQ774"),
        array("UniCredit Bank AG", "2ZCNRR8UK83OBTEK2170"),
        array("ViTrade", "529900MKYC1FZ83V3121")
    );

    $RCtraderaZoznam = array
    (
        array("905", "SK8906128407"),
        array("685", "SK8311236263"),
        array("925", "SK8905146360"),
        array("1085", "SK9409236518")
    );
    $Bizdata = $xmldata->appendChild(new DOMElement('BizData'));
    $attr = $Bizdata->setAttributeNode(new DOMAttr('xmlns', 'urn:iso:std:iso:20022:tech:xsd:head.003.001.01'));
    $attr = $Bizdata->setAttributeNode(new DOMAttr('xmlns:xsi', 'http://www.w3.org/2001/XMLSchema-instance'));
    $attr = $Bizdata->setAttributeNode(new DOMAttr('xsi:schemaLocation', 'urn:iso:std:iso:20022:tech:xsd:head.003.001.01 head.003.001.01.xsd'));
    $element = $Bizdata->appendChild(new DOMElement('Hdr'));
    $AppHdr = $element->appendChild(new DOMElement('AppHdr'));
    $attr = $AppHdr->setAttributeNode(new DOMAttr('xmlns', 'urn:iso:std:iso:20022:tech:xsd:head.001.001.01'));
    $attr = $AppHdr->setAttributeNode(new DOMAttr('xmlns:xsi', 'http://www.w3.org/2001/XMLSchema-instance'));
    $attr = $AppHdr->setAttributeNode(new DOMAttr('xsi:schemaLocation', 'urn:iso:std:iso:20022:tech:xsd:head.001.001.01 head.001.001.01_ESMAUG_1.0.0.xsd'));
    $element = $AppHdr->appendChild(new DOMElement('Fr'));
    $element = $element->appendChild(new DOMElement('OrgId'));
    $element = $element->appendChild(new DOMElement('Id'));
    $element = $element->appendChild(new DOMElement('OrgId'));
    $element = $element->appendChild(new DOMElement('Othr'));
    $element = $element->appendChild(new DOMElement('Id'));
    $element->appendChild($xmldata->createTextNode('315700ALQMS9PZOIQ774'));
    $element = $AppHdr->appendChild(new DOMElement('To'));
    $element = $element->appendChild(new DOMElement('OrgId'));
    $element = $element->appendChild(new DOMElement('Id'));
    $element = $element->appendChild(new DOMElement('OrgId'));
    $element = $element->appendChild(new DOMElement('Othr'));
    $element = $element->appendChild(new DOMElement('Id'));
    $element->appendChild($xmldata->createTextNode('SK'));
    $element = $AppHdr->appendChild(new DOMElement('BizMsgIdr'));
    $element->appendChild($xmldata->createTextNode('Report_MIFID'));
    $element = $AppHdr->appendChild(new DOMElement('MsgDefIdr'));
    $element->appendChild($xmldata->createTextNode('auth.016.001.01'));
    $teraz = time();
    $cas = date('Y-m-d', $teraz) . 'T' . date('H:i:s', $teraz) . 'Z';
    $element = $AppHdr->appendChild(new DOMElement('CreDt'));
    $element->appendChild($xmldata->createTextNode($cas));
    $element = $Bizdata->appendChild(new DOMElement('PyId'));
    $element = $element->appendChild(new DOMElement('Document'));
    $attr = $element->setAttributeNode(new DOMAttr('xmlns', 'urn:iso:std:iso:20022:tech:xsd:auth.016.001.01'));
    $attr = $element->setAttributeNode(new DOMAttr('xmlns:xsi', 'http://www.w3.org/2001/XMLSchema-instance'));
    $attr = $element->setAttributeNode(new DOMAttr('xsi:schemaLocation', 'urn:iso:std:iso:20022:tech:xsd:auth.016.001.01 auth.016.001.01_ESMAUG_Reporting_1.1.0.xsd'));
    $FinInstrmRptgTxRpt = $element->appendChild(new DOMElement('FinInstrmRptgTxRpt'));
    foreach ($data as $item) {
        $i = 0;
        $leiprotistrany = '';
        while ($i <= count($dealidspole)) {
            $pomocna = $item["dealid"];
            if ($dealidspole[$i][0] == $pomocna) $leiprotistrany = $dealidspole[$i][1];
            $i++;
        }

        $element = $FinInstrmRptgTxRpt->appendChild(new DOMElement('Tx'));
        $New = $element->appendChild(new DOMElement('New'));
        if ($item["druhobchodu"] == 'predaj') $bs = 'S';
        else $bs = 'B';
        $datumObchodu = substr($item["datum_cas_obchodu"], 6, 4) . substr($item["datum_cas_obchodu"], 3, 2) . substr($item["datum_cas_obchodu"], 0, 2);
        $idelement = $item["dealid"] . $bs . $datumObchodu . '1' . $item["subjektid"];
        $element = $New->appendChild(new DOMElement('TxId'));
        $element->appendChild($xmldata->createTextNode($idelement)); //nase ID obchodu v hlaseni (dealid+B/S+datum_obchodu+'1'+subjektid
        $element = $New->appendChild(new DOMElement('ExctgPty'));
        $element->appendChild($xmldata->createTextNode('315700ALQMS9PZOIQ774'));    //LEI sympatia
        $element = $New->appendChild(new DOMElement('InvstmtPtyInd'));
        $element->appendChild($xmldata->createTextNode('true'));                     //Investment firm covered by directive 2014/65/EU
        $element = $New->appendChild(new DOMElement('SubmitgPty'));
        $element->appendChild($xmldata->createTextNode('315700ALQMS9PZOIQ774'));    //LEI sympatia
        $Buyr = $New->appendChild(new DOMElement('Buyr'));
        $AcctOwnr = $Buyr->appendChild(new DOMElement('AcctOwnr'));                    //Buyer
        if ($bs == 'S') {
            $element = $AcctOwnr->appendChild(new DOMElement('Id'));
            $element = $element->appendChild(new DOMElement('LEI'));
            $element->appendChild($xmldata->createTextNode($leiprotistrany));                            //LEI predavajuceho
        } else if (($item["typ_zdanenia"] == 'PO' || $item["typ_zdanenia"] == 'NO') && $bs == 'B')                //ak je pravnicka dam LEI, ak fyzicka meno, priez, RC...
        {
            $element = $AcctOwnr->appendChild(new DOMElement('Id'));
            $element = $element->appendChild(new DOMElement('LEI'));
            $element->appendChild($xmldata->createTextNode($item["lei_kod"]));
            $element = $AcctOwnr->appendChild(new DOMElement('CtryOfBrnch'));
            $element->appendChild($xmldata->createTextNode('SK'));
        } else if ($item["typ_zdanenia"] == 'FO' && $bs == 'B')                                                    //fyzicka osoba
        {
            $element = $AcctOwnr->appendChild(new DOMElement('Id'));
            $Prsn = $element->appendChild(new DOMElement('Prsn'));
            $element = $Prsn->appendChild(new DOMElement('FrstNm'));
            $element->appendChild($xmldata->createTextNode(str_replace($search, $replace, ($item["meno"]))));            //meno
            $element = $Prsn->appendChild(new DOMElement('Nm'));
            $element->appendChild($xmldata->createTextNode(str_replace($search, $replace, ($item["prieznaz"]))));        //priezvisko
            $element = $Prsn->appendChild(new DOMElement('BirthDt'));
            if (strlen($item["rcico"]) == 10 && substr($item["rcico"], 0, 2) < 54) {
                $datnar = '20' . substr($item["rcico"], 0, 2) . '-';
            } else {
                $datnar = '19' . substr($item["rcico"], 0, 2) . '-';
            }
            if (substr($item["rcico"], 2, 2) > 50) {
                $mesnar = substr($item["rcico"], 2, 2) - 50;
                if ($mesnar < 10) {
                    $mesnar = '0' . $mesnar;
                }
            } else {
                $mesnar = substr($item["rcico"], 2, 2);
            }
            $datnar = $datnar . $mesnar . '-' . substr($item["rcico"], 4, 2);
            $element->appendChild($xmldata->createTextNode($datnar));
            $othr = $Prsn->appendChild(new DOMElement('Othr'));
            $element = $othr->appendChild(new DOMElement('Id'));
            $element->appendChild($xmldata->createTextNode('SK' . $item["rcico"]));        //national ID
            $element = $othr->appendChild(new DOMElement('SchmeNm'));
            $element = $element->appendChild(new DOMElement('Cd'));
            $element->appendChild($xmldata->createTextNode('NIDN'));    //ak rodne cislo - NIDN, ak cislo pasu CCPT, ostatne - CONCAT
        }
        if ($bs == 'B') {
            $element = $Buyr->appendChild(new DOMElement('DcsnMakr'));
            $element = $element->appendChild(new DOMElement('LEI'));
            $element->appendChild($xmldata->createTextNode('315700ALQMS9PZOIQ774'));    //decision maker - Sympatia
        }
        $Sellr = $New->appendChild(new DOMElement('Sellr'));
        $AcctOwnr = $Sellr->appendChild(new DOMElement('AcctOwnr'));        //Seller
        if ($bs == 'B') {
            $element = $AcctOwnr->appendChild(new DOMElement('Id'));
            $element = $element->appendChild(new DOMElement('LEI'));
            $element->appendChild($xmldata->createTextNode($leiprotistrany));                            //LEI Kupujuceho
        } else if (($item["typ_zdanenia"] == 'PO' || $item["typ_zdanenia"] == 'NO') && $bs == 'S')                //ak je pravnicka dam LEI, ak fyzicka meno, priez, RC...
        {
            $element = $AcctOwnr->appendChild(new DOMElement('Id'));
            $element = $element->appendChild(new DOMElement('LEI'));
            $element->appendChild($xmldata->createTextNode($item["lei_kod"]));
            $element = $AcctOwnr->appendChild(new DOMElement('CtryOfBrnch'));
            $element->appendChild($xmldata->createTextNode('SK'));
        } else if ($item["typ_zdanenia"] == 'FO' && $bs == 'S')                                                    //fyzicka osoba
        {
            $element = $AcctOwnr->appendChild(new DOMElement('Id'));
            $Prsn = $element->appendChild(new DOMElement('Prsn'));
            $element = $Prsn->appendChild(new DOMElement('FrstNm'));
            $element->appendChild($xmldata->createTextNode(str_replace($search, $replace, ($item["meno"]))));            //meno
            $element = $Prsn->appendChild(new DOMElement('Nm'));
            $element->appendChild($xmldata->createTextNode(str_replace($search, $replace, ($item["prieznaz"]))));        //priezvisko
            $element = $Prsn->appendChild(new DOMElement('BirthDt'));
            if (strlen($item["rcico"]) == 10 && substr($item["rcico"], 0, 2) < 54) {
                $datnar = '20' . substr($item["rcico"], 0, 2) . '-';
            } else {
                $datnar = '19' . substr($item["rcico"], 0, 2) . '-';
            }
            if (substr($item["rcico"], 2, 2) > 50) {
                $mesnar = substr($item["rcico"], 2, 2) - 50;
                if ($mesnar < 10) {
                    $mesnar = '0' . $mesnar;
                }
            } else {
                $mesnar = substr($item["rcico"], 2, 2);
            }
            $datnar = $datnar . $mesnar . '-' . substr($item["rcico"], 4, 2);
            $element->appendChild($xmldata->createTextNode($datnar));
            $othr = $Prsn->appendChild(new DOMElement('Othr'));
            $element = $othr->appendChild(new DOMElement('Id'));
            $element->appendChild($xmldata->createTextNode('SK' . $item["rcico"]));        //national ID
            $element = $othr->appendChild(new DOMElement('SchmeNm'));
            $element = $element->appendChild(new DOMElement('Cd'));
            $element->appendChild($xmldata->createTextNode('NIDN'));    //ak rodne cislo - NIDN, ak cislo pasu CCPT, ostatne - CONCAT
        }
        if ($bs == 'S') {
            $element = $Sellr->appendChild(new DOMElement('DcsnMakr'));
            $element = $element->appendChild(new DOMElement('LEI'));
            $element->appendChild($xmldata->createTextNode('315700ALQMS9PZOIQ774'));    //decision maker - Sympatia
        }
        $element = $New->appendChild(new DOMElement('OrdrTrnsmssn'));
        $element = $element->appendChild(new DOMElement('TrnsmssnInd'));
        $element->appendChild($xmldata->createTextNode('false'));                            //postupeny pokyn - false
        $Tx = $New->appendChild(new DOMElement('Tx'));
        $element = $Tx->appendChild(new DOMElement('TradDt'));
        $datumObchodu = substr($item["datum_cas_obchodu"], 6, 4) . '-' . substr($item["datum_cas_obchodu"], 3, 2) . '-' . substr($item["datum_cas_obchodu"], 0, 2) . 'T' . substr($item["datum_cas_obchodu"], 11, 5) . ':00Z';
        $element->appendChild($xmldata->createTextNode($datumObchodu));                            //datum cas obchodu
        $element = $Tx->appendChild(new DOMElement('TradgCpcty'));
        $element->appendChild($xmldata->createTextNode('MTCH'));
        $element = $Tx->appendChild(new DOMElement('Qty'));
        if ($item["eqid"] == 'Bonds') {
            $element = $element->appendChild(new DOMElement('NmnlVal'));
            $attr = $element->setAttributeNode(new DOMAttr('Ccy', $item["currencyidtrade"]));        //pocet
            $element->appendChild($xmldata->createTextNode($item["pocet"]));
        } else {
            $element = $element->appendChild(new DOMElement('Unit'));
            $element->appendChild($xmldata->createTextNode($item["pocet"]));                        //pocet
        }
        $element = $Tx->appendChild(new DOMElement('Pric'));
        $element = $element->appendChild(new DOMElement('Pric'));
        if ($item["eqid"] == 'Bonds') {
            $element = $element->appendChild(new DOMElement('Pctg'));                                //cena
            $element->appendChild($xmldata->createTextNode($item["limitkurz"]));
        } else {
            $element = $element->appendChild(new DOMElement('MntryVal'));
            $element = $element->appendChild(new DOMElement('Amt'));
            $attr = $element->setAttributeNode(new DOMAttr('Ccy', $item["currencyidtrade"]));        //cena
            $element->appendChild($xmldata->createTextNode($item["limitkurz"]));
        }
        $element = $Tx->appendChild(new DOMElement('NetAmt'));
        $element->appendChild($xmldata->createTextNode($item["objem"]));                             //objem s AUV
        $element = $Tx->appendChild(new DOMElement('TradVn'));
        $element->appendChild($xmldata->createTextNode("XOFF"));
        $element = $Tx->appendChild(new DOMElement('UpFrntPmt'));
        $element = $element->appendChild(new DOMElement('Amt'));
        $attr = $element->setAttributeNode(new DOMAttr('Ccy', $item["currencyidtrade"]));        //cena
        $element->appendChild($xmldata->createTextNode('0'));
        $element = $New->appendChild(new DOMElement('FinInstrm'));
        $element = $element->appendChild(new DOMElement('Id'));                                            //ak je isin, nic ine sa nevyplna
        $element->appendChild($xmldata->createTextNode($item["isin"]));
        $element = $New->appendChild(new DOMElement('InvstmtDcsnPrsn'));
        $Prsn = $element->appendChild(new DOMElement('Prsn'));
        $element = $Prsn->appendChild(new DOMElement('CtryOfBrnch'));
        $element->appendChild($xmldata->createTextNode('SK'));
        $Othr = $Prsn->appendChild(new DOMElement('Othr'));
        $element = $Othr->appendChild(new DOMElement('Id'));
        $element->appendChild($xmldata->createTextNode('SK7705076126'));                        //Brano
        $element = $Othr->appendChild(new DOMElement('SchmeNm'));
        $element = $element->appendChild(new DOMElement('Cd'));
        $element->appendChild($xmldata->createTextNode('NIDN'));
        $element = $New->appendChild(new DOMElement('ExctgPrsn'));
        $Prsn = $element->appendChild(new DOMElement('Prsn'));
        $element = $Prsn->appendChild(new DOMElement('CtryOfBrnch'));
        $element->appendChild($xmldata->createTextNode('SK'));
        $Othr = $Prsn->appendChild(new DOMElement('Othr'));
        $element = $Othr->appendChild(new DOMElement('Id'));
        $i = 0;
        while ($i < 4) {
            if ($RCtraderaZoznam[$i][0] == $item["userid"]) $RCtradera = $RCtraderaZoznam[$i][1];
            $i++;
        }
        $element->appendChild($xmldata->createTextNode($RCtradera));                        //trader
        $element = $Othr->appendChild(new DOMElement('SchmeNm'));
        $element = $element->appendChild(new DOMElement('Cd'));
        $element->appendChild($xmldata->createTextNode('NIDN'));
        $AddtlAttrbts = $New->appendChild(new DOMElement('AddtlAttrbts'));
        if ($bs == 'S') {
            $element = $AddtlAttrbts->appendChild(new DOMElement('ShrtSellgInd'));
            $element->appendChild($xmldata->createTextNode('SELL'));
        }
        $element = $AddtlAttrbts->appendChild(new DOMElement('OTCPstTradInd'));
        $element->appendChild($xmldata->createTextNode('BENC'));
        $element = $AddtlAttrbts->appendChild(new DOMElement('RskRdcgTx'));
        $element->appendChild($xmldata->createTextNode('false'));
        $element = $AddtlAttrbts->appendChild(new DOMElement('SctiesFincgTxInd'));
        $element->appendChild($xmldata->createTextNode('false'));
    }
    $xmldata->formatOutput = true;
    //echo $xmldata->saveXML();
    $xmldata->save("/home/<USER>/www/temp/mifirExport.xml");
}
header('Content-Type: application/json');
echo json_encode(["data" => $data, "link" => '/home/<USER>/www/temp/mifirExport.xml']);


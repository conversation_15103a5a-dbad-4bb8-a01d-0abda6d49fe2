<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

$stvrtrok = $_POST["stvrtrok"];
$rok = $_POST["rok"];

if ($stvrtrok == 1) {
    $datefrom = "01.01." . $rok;
    $dateto = "31.03." . $rok;
}
if ($stvrtrok == 2) {
    $datefrom = "01.04." . $rok;
    $dateto = "30.06." . $rok;
}
if ($stvrtrok == 3) {
    $datefrom = "01.07." . $rok;
    $dateto = "30.09." . $rok;
}
if ($stvrtrok == 4) {
    $datefrom = "01.10." . $rok;
    $dateto = "31.12." . $rok;
}

$dateQuery = " and mt.datum >= to_date('$datefrom','dd.mm.yyyy')
                 and mt.datum <= to_date('$dateto','dd.mm.yyyy') ";
$lastdate = "and mt.datum = (to_date('$datefrom','dd.mm.yyyy')-1)";
$lastdate2 = "and mt.datum = (to_date('$dateto','dd.mm.yyyy'))";

$dateQuery2 = " and kcp.datpok >= to_date('$datefrom','dd.mm.yyyy')
                 and kcp.datpok <= to_date('$dateto','dd.mm.yyyy') ";

$dateQuery3 = " and k.dat_realizacia >= to_date('$datefrom','dd.mm.yyyy')
                and k.dat_realizacia <= to_date('$dateto','dd.mm.yyyy') ";


$partA = [];
$partB = [];
$partC = [];

include "nbsHlasenieQueries/A_1R_akcie.php";

include "nbsHlasenieQueries/B_1R_pocetSpolu.php";
include "nbsHlasenieQueries/B_2R_pocetZmluv.php";
include "nbsHlasenieQueries/B_3R_pocetVypovedanych.php";
include "nbsHlasenieQueries/B_4R_objemMajetkuSpolu.php";
include "nbsHlasenieQueries/B_7R_predvolitelnecp.php";
include "nbsHlasenieQueries/B_8R_cpZahranicne.php";
include "nbsHlasenieQueries/B_9R_peniaze.php";
include "nbsHlasenieQueries/B_10R_pohladavky.php";
include "nbsHlasenieQueries/B_11_zavazky.php";

include "nbsHlasenieQueries/D_1R_pocetK.php";
include "nbsHlasenieQueries/D_2R.php";

$columns = [
    ["A. Prehľad o uskutočnených obchodoch", "Druh", "Rezidenti", "Nerezidenti", "Spolu", "Burzové obchody", "Mimoburzové obchody"],
    ["B. Prehľd o riadení portfólia", "Rezidenti", "Nerezidenti", "Spolu"],
    ["C. Úschova a správa o finančných nástrojov", "Rezidenti", "Nerezidenti", "Spolu"]
];

$items = [
    $partA, $partB, $partC
];

$property = ["Akcie","objemMajetekuSpolu"];

echo '<div class="flex gap-4 items-center my-2 absolute top-0 right-5">
<h2>Exportovať do Excelu (.xlsx): </h2>
<form class="mb-0 excelForm" method="post" action="">
                <input type="hidden" name="data" ' . "value='" . json_encode($items) . "'" . '/>
                <input type="hidden" name="columns" ' . "value='" . json_encode($columns) . "'" . '/>
                <input type="hidden" name="filename" value="Hlasenie_NBS_o_investicnych_sluzbach"/>
                <input type="hidden" name="property" value="' .  json_encode($property) . '"/>
                <button id="exportToExcelTrigger" type="submit"
                        class="p-4 bg-gray-200 flex items-center justify-center gap-2 rounded-xl text-center font-semibold text-gray-600 hover:bg-green-200 transition-all cursor-pointer hover:text-white">
                    <svg id="excelIcon" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="25" height="25"
                         viewBox="0 0 48 48">
                        <path fill="#169154" d="M29,6H15.744C14.781,6,14,6.781,14,7.744v7.259h15V6z"></path>
                        <path fill="#18482a"
                              d="M14,33.054v7.202C14,41.219,14.781,42,15.743,42H29v-8.946H14z"></path>
                        <path fill="#0c8045" d="M14 15.003H29V24.005000000000003H14z"></path>
                        <path fill="#17472a" d="M14 24.005H29V33.055H14z"></path>
                        <g>
                            <path fill="#29c27f" d="M42.256,6H29v9.003h15V7.744C44,6.781,43.219,6,42.256,6z"></path>
                            <path fill="#27663f"
                                  d="M29,33.054V42h13.257C43.219,42,44,41.219,44,40.257v-7.202H29z"></path>
                            <path fill="#19ac65" d="M29 15.003H44V24.005000000000003H29z"></path>
                            <path fill="#129652" d="M29 24.005H44V33.055H29z"></path>
                        </g>
                        <path fill="#0c7238"
                              d="M22.319,34H5.681C4.753,34,4,33.247,4,32.319V15.681C4,14.753,4.753,14,5.681,14h16.638 C23.247,14,24,14.753,24,15.681v16.638C24,33.247,23.247,34,22.319,34z"></path>
                        <path fill="#fff"
                              d="M9.807 19L12.193 19 14.129 22.754 16.175 19 18.404 19 15.333 24 18.474 29 16.123 29 14.013 25.07 11.912 29 9.526 29 12.719 23.982z"></path>
                    </svg>
                    <svg aria-hidden="true" id="spinnerExcel"
                         class="w-6 h-6 hidden text-gray-200 animate-spin dark:text-gray-600 fill-blue-600"
                         viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                              fill="currentColor"/>
                        <path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                              fill="currentFill"/>
                    </svg>
                </button>
                </form></div>';
$reportTable = '
        <div class="relative mt-4 mb-6 overflow-x-auto shadow-md sm:rounded-lg">
        <div class="p-5 text-xl font-semibold text-left rtl:text-right text-white bg-purple-800 dark:text-white dark:bg-gray-800">
            <section class="flex w-full justify-between items-center p-1">
                <div><strong>A.</strong> Prehľad o uskutočnených obchodoch
                    <p class="mt-1 text-sm font-normal text-gray-500 dark:text-gray-400"><span
                                class="flex items-center gap-2">' . $property . '
                        </span>
                    </p>
                </div>
            </section>
            </div>
                <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
        <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
            <tr>
                <th scope="col" class="px-4 py-3">Popis</th>
                <th scope="col" class="px-4 py-3">Druh</th>
                <th scope="col" class="px-4 py-3">Rezidenti</th>
                <th scope="col" class="px-4 py-3">Nerezidenti</th>
                <th scope="col" class="px-4 py-3">Spolu</th>
                <th scope="col" class="px-4 py-3">Burzové obchody</th>
                <th scope="col" class="px-4 py-3">Mimoburzové obchody</th>
            </tr>
        </thead>
        <tbody>';

foreach ($partA as $section => $sections) {
    $count = 0;
    foreach ($sections as $key => $item) {
        $reportTable .= '
                    <tr class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b dark:border-gray-700">';
        if ($count === 0) {
            $reportTable .= '<td rowspan="3" class="px-6 py-4 font-semibold text-md">' . $section . '</td>';
        }
        $reportTable .= '<td class="px-6 py-4 font-semibold text-md">' . $key . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["rezidenti"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["nerezidenti"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["spolu"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["burzoveObchody"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["mimoburzoveObchody"] . '</td>
                    </tr>
                    ';
        $count++;
    }
}
$reportTable .= '</tbody></table></div>';
echo $reportTable;

$reportTable = '
        <div class="relative mt-4 mb-6 overflow-x-auto shadow-md sm:rounded-lg">
        <div class="p-5 text-xl font-semibold text-left rtl:text-right text-white bg-purple-800 dark:text-white dark:bg-gray-800">
            <section class="flex w-full justify-between items-center p-1">
                <div><strong>B.</strong> Prehľad o riadení portfólia
                    <p class="mt-1 text-sm font-normal text-gray-500 dark:text-gray-400"><span
                                class="flex items-center gap-2">' . $property . '
                        </span>
                    </p>
                </div>
            </section>
            </div>
                <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
        <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
            <tr>
                <th scope="col" class="px-4 py-3">Popis</th>
                <th scope="col" class="px-4 py-3">Rezidenti</th>
                <th scope="col" class="px-4 py-3">Nerezidenti</th>
                <th scope="col" class="px-4 py-3">Spolu</th>
            </tr>
        </thead>
        <tbody>';

foreach ($partB as $section => $item) {
    if ($section !== "objemMajetekuSpolu") {
        $reportTable .= '
                    <tr class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b dark:border-gray-700">
                        <td class="px-6 py-4 font-semibold text-md">' . $item["popis"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["rezidenti"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["nerezidenti"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["spolu"] . '</td>
                    </tr>
                    ';
    }
}

$reportTable .= '
                    <tr class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b dark:border-gray-700">
                        <td class="px-6 py-4 font-semibold text-md">' . $partB["objemMajetekuSpolu"]["popis"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $partB["objemMajetekuSpolu"]["rezidenti"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $partB["objemMajetekuSpolu"]["nerezidenti"] . '</td>
                    </tr>
                    ';
$reportTable .= '
                    <tr class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b dark:border-gray-700">
                        <td class="py-4 font-bold text-md" style="padding-left: 3rem">' . $partB["objemMajetekuSpolu"]["predvolitelnecps"]["popis"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $partB["objemMajetekuSpolu"]["predvolitelnecps"]["rezidenti"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $partB["objemMajetekuSpolu"]["predvolitelnecps"]["nerezidenti"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $partB["objemMajetekuSpolu"]["predvolitelnecps"]["spolu"] . '</td>
                    </tr>
                    <tr class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b dark:border-gray-700">
                        <td class="py-4 font-semibold text-md" style="padding-left: 5rem">' . $partB["objemMajetekuSpolu"]["predvolitenlnecp"]["akcie"]["popis"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $partB["objemMajetekuSpolu"]["predvolitenlnecp"]["akcie"]["rezidenti"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $partB["objemMajetekuSpolu"]["predvolitenlnecp"]["akcie"]["nerezidenti"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $partB["objemMajetekuSpolu"]["predvolitenlnecp"]["akcie"]["spolu"] . '</td>
                    </tr>
                    <tr class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b dark:border-gray-700">
                        <td class="py-4 font-semibold text-md" style="padding-left: 5rem">' . $partB["objemMajetekuSpolu"]["predvolitenlnecp"]["dlhopisy"]["popis"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $partB["objemMajetekuSpolu"]["predvolitenlnecp"]["dlhopisy"]["rezidenti"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $partB["objemMajetekuSpolu"]["predvolitenlnecp"]["dlhopisy"]["nerezidenti"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $partB["objemMajetekuSpolu"]["predvolitenlnecp"]["dlhopisy"]["spolu"] . '</td>
                    </tr>
                    <tr class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b dark:border-gray-700">
                        <td class="py-4 font-bold text-md" style="padding-left: 3rem">' . $partB["objemMajetekuSpolu"]["cpZahranicne"]["popis"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $partB["objemMajetekuSpolu"]["cpZahranicne"]["rezidenti"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $partB["objemMajetekuSpolu"]["cpZahranicne"]["nerezidenti"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $partB["objemMajetekuSpolu"]["cpZahranicne"]["spolu"] . '</td>
                    </tr>
                    <tr class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b dark:border-gray-700">
                        <td class="py-4 font-bold text-md" style="padding-left: 3rem">' . $partB["objemMajetekuSpolu"]["peniaze"]["popis"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $partB["objemMajetekuSpolu"]["peniaze"]["rezidenti"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $partB["objemMajetekuSpolu"]["peniaze"]["nerezidenti"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $partB["objemMajetekuSpolu"]["peniaze"]["spolu"] . '</td>
                    </tr>
                    <tr class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b dark:border-gray-700">
                        <td class="py-4 font-bold text-md" style="padding-left: 3rem">' . $partB["objemMajetekuSpolu"]["pohladavky"]["popis"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $partB["objemMajetekuSpolu"]["pohladavky"]["rezidenti"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $partB["objemMajetekuSpolu"]["pohladavky"]["nerezidenti"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $partB["objemMajetekuSpolu"]["pohladavky"]["spolu"] . '</td>
                    </tr>
                    <tr class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b dark:border-gray-700">
                        <td class="py-4 font-bold text-md" style="padding-left: 3rem">' . $partB["objemMajetekuSpolu"]["zavazky"]["popis"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $partB["objemMajetekuSpolu"]["zavazky"]["rezidenti"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $partB["objemMajetekuSpolu"]["zavazky"]["nerezidenti"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $partB["objemMajetekuSpolu"]["zavazky"]["spolu"] . '</td>
                    </tr>
                    ';

$reportTable .= '</tbody></table></div>';
echo $reportTable;

$reportTable = '
        <div class="relative mt-4 mb-6 overflow-x-auto shadow-md sm:rounded-lg">
        <div class="p-5 text-xl font-semibold text-left rtl:text-right text-white bg-purple-800 dark:text-white dark:bg-gray-800">
            <section class="flex w-full justify-between items-center p-1">
                <div><strong>C.</strong> Úschova a správa finančných nástrojov
                    <p class="mt-1 text-sm font-normal text-gray-500 dark:text-gray-400"><span
                                class="flex items-center gap-2">' . $property . '
                        </span>
                    </p>
                </div>
            </section>
            </div>
                <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
        <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
            <tr>
                <th scope="col" class="px-4 py-3">Popis</th>
                <th scope="col" class="px-4 py-3">Rezidenti</th>
                <th scope="col" class="px-4 py-3">Nerezidenti</th>
                <th scope="col" class="px-4 py-3">Spolu</th>
            </tr>
        </thead>
        <tbody>';
$reportTable .= '
                    <tr class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b dark:border-gray-700">
                        <td class="py-4 font-bold text-md" style="padding-left: 1rem">' . $partC["uschovaAsprava"][0]["popis"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $partC["uschovaAsprava"][0]["rezidenti"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $partC["uschovaAsprava"][0]["nerezidenti"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $partC["uschovaAsprava"][0]["spolu"] . '</td>
                    </tr>
                    <tr class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b dark:border-gray-700">
                        <td class="py-4 font-semibold text-md" style="padding-left: 1rem">' . $partC["uschovaAsprava"][1]["popis"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $partC["uschovaAsprava"][1]["rezidenti"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $partC["uschovaAsprava"][1]["nerezidenti"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $partC["uschovaAsprava"][1]["spolu"] . '</td>
                    </tr>
                    <tr class="bg-gray-900 border-b text-white">
                        <td class="py-4 font-semibold text-md" style="padding-left: 1rem">Obchody s devízovými hodnotami</td>
                        <td class="px-6 py-4 font-semibold text-md">Fyzické osoby</td>
                        <td class="px-6 py-4 font-semibold text-md">Právnické osoby</td>
                        <td class="px-6 py-4 font-semibold text-md">Spolu</td>
                    </tr>
                    <tr class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b dark:border-gray-700">
                        <td class="py-4 font-semibold text-md" style="padding-left: 1rem">' . $partC["obchodyDevizove"][0]["popis"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $partC["obchodyDevizove"][0]["rezidenti"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $partC["obchodyDevizove"][0]["nerezidenti"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $partC["obchodyDevizove"][0]["spolu"] . '</td>
                    </tr>
                    <tr class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b dark:border-gray-700">
                        <td class="py-4 font-bold text-md" style="padding-left: 1rem">' . $partC["obchodyDevizove"][1]["popis"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $partC["obchodyDevizove"][1]["rezidenti"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $partC["obchodyDevizove"][1]["nerezidenti"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $partC["obchodyDevizove"][1]["spolu"] . '</td>
                    </tr>
                    ';

$reportTable .= '</tbody></table></div>';
echo $reportTable;
echo '<script src="/src/assets/js/prehlady/nbs-gfi/excel.js"></script>';
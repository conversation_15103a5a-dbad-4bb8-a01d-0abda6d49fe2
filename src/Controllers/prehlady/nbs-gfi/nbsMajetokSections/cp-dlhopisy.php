<?php
$query = "WITH CTE_Bonds AS (
    SELECT 
        d.isinreal AS ISIN,
        d.cpnaz AS NaCP,
        d.currencynom AS MenaD,
        d.dateemisie AS DatumEmisie,
        d.maturitydate AS DatumMaturity,
        d.nominalemisie AS MenovitaHodnota,
        ed.s<PERSON>t<PERSON> AS Druh,
        ee.emitentnazov AS EmNaz,
        se.stateshort AS EmStat,
        s95e.esa95_sektoroznacenie AS EmSek,
        ee.emitentico AS EmIco,
        sm.stateshort AS KM,
        s95m.esa95_sektoroznacenie AS SeM,
        mt.pocet AS Pocet,
        mt.kurzaktiva AS Cena,
        mt.sumadenom AS SumaAUV,
        f.typpredlohy,
        po.prieznaz AS M,
        po.rcico AS IM,
        po.stateid AS PoStateID,
        CASE
        WHEN dcr.kurz = 1 THEN 'MV'
        WHEN dcr.kurz = 0 THEN 'EP'
        ELSE '!!!'
        END AS ZdrojCeny,
       
        CASE
            WHEN f.typpredlohy = 2 THEN 'RP'
            WHEN f.typpredlohy = 3 THEN 'RP'
            WHEN f.typpredlohy = 4 THEN 'VUM'
            ELSE '!!!'
        END AS VedSluz

    FROM dbequity d
    JOIN dbequitycurr dc ON dc.isin = d.isin
    JOIN dbequitycurrric dcr ON dcr.isincurr = dc.isincurr
    JOIN majetoktotal mt ON mt.kodaktiva = dcr.isincurrric AND mt.datum = TO_DATE('$dbdate', 'YYYY-MM-DD') AND mt.uctovnykod = 251120
    JOIN portfolio p ON p.fondid = mt.subjektid
    JOIN podielnik po ON po.podielnikid = p.podielnikid
    JOIN fonds f ON f.fondid = p.fondid
    JOIN state sm ON sm.stateid = po.stateid
    JOIN equityemitent ee ON ee.emitentid = d.emitentid
    JOIN state se ON se.stateid = ee.emitentstateid
    JOIN fondsmu fm ON fm.fondid = mt.subjektid AND fm.cum = mt.ucetaktiva
    LEFT JOIN sektor_esa95 s95m ON s95m.esa95_sektorid = po.sektor_esa95
    LEFT JOIN sektor_esa95 s95e ON s95e.esa95_sektorid = ee.emitentsektor
    JOIN equitydruh ed ON ed.druheqid = d.druheqid
    WHERE d.eqid = 'Bonds' AND f.typpredlohy < 5 AND po.fpo IN (0, 2)
)
SELECT 
    IM,
    M,
    KM,
    SeM,
    ISIN,
    'ISIN' AS PoK,
    NaCP,
    Druh,
    MenaD,
    TO_CHAR(DatumEmisie, 'DD.MM.YYYY') AS DatumEmisie,
    TO_CHAR(DatumMaturity, 'DD.MM.YYYY') AS DatumMaturity,
    COALESCE(EmIco, 99999999) AS EmIco,
    EmNaz, 
    EmStat,
    EmSek,
    MenovitaHodnota,
    SUM(Pocet) AS Pocet,
    Cena,
    'PCL' AS CenovaBaza,
    ZdrojCeny,
    SUM(SumaAUV) AS SumaAUV,
    VedSluz
FROM CTE_Bonds
WHERE (PoStateID = 1 AND IM != '99999999') OR (PoStateID > 1)
GROUP BY 
    IM, M, KM, SeM, ISIN, NaCP, Druh, MenaD, DatumEmisie, DatumMaturity,
    COALESCE(EmIco, 99999999), EmNaz, EmStat, EmSek, MenovitaHodnota, Cena, ZdrojCeny, VedSluz
ORDER BY ISIN, MenaD;
";
$cpdlhopisyRes = Connection::getDataFromDatabase($query, defaultDB);
$cpdlhopisy = $cpdlhopisyRes[1];
foreach ($cpdlhopisy as $item) {
    $cislozmluvy = $item["cislozmluvy"];
    $fondids[$cislozmluvy]['cpdlhopisy'][] = [
        "im" => $item["im"],
        "majitel" => $item["m"],
        "krajina" => $item["km"],
        "sektor" => $item["sem"],
        "isin" => $item["isin"],
        "popis" => $item["pok"],
        "cpnaz" => $item["nacp"],
        "druh" => $item["druh"],
        "mena" => $item["menad"],
        "datumemisie" => $item["datumemisie"],
        "datummaturity" => $item["datummaturity"],
        "emitent" => $item["emico"],
        "emitentnaz" => $item["emnaz"],
        "emitentkrajina" => $item["emstat"],
        "emitentsektor" => $item["emsek"],
        "menovitahodnota" => round($item["menovitahodnota"], 0),
        "pocet" => $item["pocet"],
        "cena" => $item["cena"],
        "cenovabaza" => $item["cenovabaza"],
        "zdrojceny" => $item["zdrojceny"],
        "sumaauv" => $item["sumaauv"],
        "vedsluz" => $item["vedsluz"],
    ];
}
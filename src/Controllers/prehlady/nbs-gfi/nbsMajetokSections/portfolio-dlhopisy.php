<?php
$query = "WITH bond_data AS (
    SELECT
        d.isinreal AS pISIN, 
        CASE 
            WHEN d.druheqid IN ('0') THEN 'POKPOU'
            WHEN d.druheqid IN ('1', '2', '5', '6', '7') THEN 'DLH'
            WHEN d.druheqid = '12' THEN 'HZL'
            ELSE 'N!' 
        END AS pDruh, 
        SUM(mt.pocet * d.nominalemisie * f.faktor) AS pNominal, 
        dc.currencytrade AS pMena,
        MIN(mt.kurzaktiva) AS pCena,
        0 AS pAUV, 
        CASE 
            WHEN po.fpo IN ('0', '2') THEN 'FO'
            WHEN po.fpo = '1' THEN 'PO'
            ELSE 'N!' 
        END AS pPortfolio
    FROM
        dbequity d
    JOIN dbequitycurr dc ON dc.isin = d.isin
    JOIN dbequitycurrric dcr ON dcr.isincurr = dc.isincurr
    JOIN majetoktotal mt ON mt.datum = TO_DATE('$dbdate', 'YYYY-MM-DD') AND mt.uctovnykod = 251110
    JOIN portfolio p ON p.fondid = mt.subjektid
    JOIN podielnik po ON po.podielnikid = p.podielnikid
    JOIN floatkupon f ON f.isincurrric = dcr.isincurrric AND f.datefrom <= mt.datum AND f.datetill >= mt.datum
    JOIN fonds fo ON fo.fondid = p.fondid
    WHERE
        d.eqid = 'Bonds'
        AND fo.typpredlohy < 4
    GROUP BY
        d.isinreal, 
        d.druheqid, 
        dc.currencytrade, 
        po.fpo

    UNION ALL

    SELECT
        d.isinreal AS pISIN, 
        CASE 
            WHEN d.druheqid IN ('0') THEN 'POKPOU'
            WHEN d.druheqid IN ('1', '2', '5', '6', '7') THEN 'DLH'
            WHEN d.druheqid = '12' THEN 'HZL'
            ELSE 'N!' 
        END AS pDruh, 
        0 AS pNominal,
        dc.currencytrade AS pMena,
        MIN(mt.kurzaktiva) AS pCena,
        SUM(mt.sumadenom) AS pAUV, 
        CASE 
            WHEN po.fpo IN ('0', '2') THEN 'FO'
            WHEN po.fpo = '1' THEN 'PO'
            ELSE 'N!' 
        END AS pPortfolio
    FROM
        dbequity d
    JOIN dbequitycurr dc ON dc.isin = d.isin
    JOIN dbequitycurrric dcr ON dcr.isincurr = dc.isincurr
    JOIN majetoktotal mt ON mt.datum = TO_DATE('$dbdate', 'YYYY-MM-DD') AND mt.uctovnykod IN (251120, 315124)
    JOIN portfolio p ON p.fondid = mt.subjektid
    JOIN podielnik po ON po.podielnikid = p.podielnikid
    JOIN floatkupon f ON f.isincurrric = dcr.isincurrric AND f.datefrom <= mt.datum AND f.datetill >= mt.datum
    JOIN fonds fo ON fo.fondid = p.fondid
    WHERE
        d.eqid = 'Bonds'
        AND fo.typpredlohy < 4
    GROUP BY
        d.isinreal, 
        d.druheqid, 
        dc.currencytrade, 
        po.fpo
)

SELECT
    pomoc.pISIN AS ISIN,
    pomoc.pDruh AS Druh,
    SUM(pomoc.pNominal) AS Nominal,
    pomoc.pMena AS Mena,
    pomoc.pCena AS Cena,
    SUM(pomoc.pAUV) AS AUV,
    pomoc.pPortfolio AS Portfolio
FROM
    bond_data pomoc
GROUP BY
    pomoc.pISIN, 
    pomoc.pDruh, 
    pomoc.pMena, 
    pomoc.pCena, 
    pomoc.pPortfolio
ORDER BY 
    ISIN,  
    Portfolio;
";
$portfolioDlhopisyRes = Connection::getDataFromDatabase($query, defaultDB);
$portfolioDlhopisy = $portfolioDlhopisyRes[1];

foreach ($portfolioDlhopisy as $item) {

    $cislozmluvy = $item["cislozmluvy"];
    $fondids[$cislozmluvy]['portfolioDlhopisy'][] = [
        "isin" => $item["isin"],
        "druh" => $item["druh"],
        "nominal" => $item["nominal"],
        "mena" => $item["mena"],
        "cena" => $item["cena"],
        "auv" => $item["auv"],
        "portfolio" => $item["portfolio"],
        "zocp" => $item["n"],
    ];
}
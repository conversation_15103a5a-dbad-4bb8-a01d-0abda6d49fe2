<?php
$query = "SELECT
    CASE 
        WHEN mt.uctovnykod = '221110' THEN '<PERSON><PERSON><PERSON><PERSON>'
        WHEN mt.uctovnykod = '221210' THEN 'Termínované vklady - istina'
        WHEN mt.uctovnykod = '315113' THEN 'Termínované vklady - úroky'
        ELSE '!!!'
    END AS Druh,
    mt.uctovnykod AS Kod, 
    SUM(mt.sumadenom * f_menovy_kurz.value) AS Suma
FROM
    majetoktotal mt
JOIN portfolio p ON p.fondid = mt.subjektid
JOIN podielnik po ON po.podielnikid = p.podielnikid
JOIN LATERAL (
        SELECT f_menovy_kurz_lot(mt.menadenom, 'EUR', mt.datum) AS value
    ) f_menovy_kurz ON true
WHERE
    mt.datum = TO_DATE('$dbdate', 'YYYY-MM-DD')
    AND mt.uctovnykod IN (221110, 221210, 315113)
    AND p.cislozmluvy < '4000000000'
GROUP BY
    mt.uctovnykod
ORDER BY 
    mt.uctovnykod;
";

$portfolioPenazneRes = Connection::getDataFromDatabase($query, defaultDB);
$portfolioPenazne = $portfolioPenazneRes[1];

$sumKTV = 0.0;
$sumBU = 0.0;
$sum = 0.0;

foreach ($portfolioPenazne as $item) {
    $cislozmluvy = $item["cislozmluvy"];
    if ($item["Kod"] != 221110) {
        $sumKTV += $item["suma"];
    } else {
        $sumBU += $item["suma"];
    }
    $fondids[$cislozmluvy]['portfolioPenazne'][] = [
        "druh" => $item["druh"],
        "suma" => round($item["suma"], 2)
    ];
    $sum = $sumBU + $sumKTV;
}

$fondids[$cislozmluvy]['portfolioPenazne']["spolu"] = [
    "spolu" => $sum
];
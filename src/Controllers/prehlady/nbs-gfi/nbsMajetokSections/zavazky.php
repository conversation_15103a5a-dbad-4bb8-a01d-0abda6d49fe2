<?php
$query = "WITH asset_totals AS (
    SELECT 
        SUM(mt.suma) AS suma,
        mt.mena,
        po.stateid,
        b.cub
    FROM (
        SELECT 
            COUNT(p.podielnikid) AS podielnik_count, 
            b.cub, 
            b.banka 
        FROM fondsbu b
        LEFT JOIN portfolio p ON b.fondid = p.fondid
        GROUP BY b.cub, b.banka
    ) b
    LEFT JOIN (
        SELECT 
            ucetaktiva,
            (((0.5 - md_d) / 0.5) * sumadenom) AS suma,
            menadenom AS mena,
            subjektid 
        FROM majetoktotal 
        WHERE datum = TO_DATE('$dbdate', 'YYYY-MM-DD') 
          AND subjektid > 1
        GROUP BY ucetaktiva, menadenom, subjektid, md_d, sumadenom
    ) mt ON b.cub = mt.ucetaktiva
    LEFT JOIN portfolio p ON mt.subjektid = p.fondid
    LEFT JOIN podielnik po ON p.podielnikid = po.podielnikid
    WHERE mt.suma IS NOT NULL
    GROUP BY mt.mena, po.stateid, b.cub
),
currency_rates AS (
    SELECT * 
    FROM kurzyaktivarchiv 
    WHERE datum = TO_DATE('$dbdate', 'YYYY-MM-DD')
),
final_totals AS (
    SELECT 
        SUM(ROUND(at.suma / COALESCE(cr.kurz, 1), 2)) AS suma,
        CASE 
            WHEN at.mena = 'EUR' THEN 'EUR' 
            ELSE 'iná mena' 
        END AS mena,
        CASE 
            WHEN at.stateid = 1 THEN 'rezident' 
            ELSE 'nerezident' 
        END AS krajina
    FROM asset_totals at
    LEFT JOIN currency_rates cr ON 'EUR' || at.mena = cr.ric
    GROUP BY at.mena, at.stateid
)

SELECT 
    SUM(ft.suma) AS suma,
    ft.mena,
    ft.krajina AS domicil
FROM final_totals ft
GROUP BY ft.mena, ft.krajina
ORDER BY ft.mena, ft.krajina;
";

$zavazkyRes = Connection::getDataFromDatabase($query, defaultDB);
$zavazky = $zavazkyRes[1];

foreach ($zavazky as $item) {
    $cislozmluvy = $item["cislozmluvy"];
    $fondids[$cislozmluvy]['zavazky'][] = [
        "domicil" => $item["domicil"],
        "mena" => $item["mena"],
        "suma" => round($item["suma"], 2),
    ];
}


$query = "WITH asset_totals AS (
    SELECT 
        b.cub,
        b.banka,
        COALESCE(SUM(mt.suma), 0) AS suma,
        mt.mena,
        CASE 
            WHEN b.cub = 'HVF2168.001-EUR' THEN 67 
            WHEN SUBSTR(b.cub, POSITION('/' IN b.cub) + 1, 4) IN ('5800') THEN 2 
            ELSE 1 
        END AS stateid
    FROM fondsbu b
    LEFT JOIN portfolio p ON b.fondid = p.fondid
    LEFT JOIN (
        SELECT 
            ucetaktiva,
            (((0.5 - md_d) / 0.5) * sumadenom) AS suma,
            menadenom AS mena,
            subjektid
        FROM majetoktotal 
        WHERE datum = TO_DATE('$dbdate', 'YYYY-MM-DD') AND subjektid > 1
        GROUP BY ucetaktiva, menadenom, subjektid, md_d, sumadenom
    ) mt ON b.cub = mt.ucetaktiva
    GROUP BY b.cub, b.banka, mt.mena
),
exchange_rates AS (
    SELECT * 
    FROM kurzyaktivarchiv 
    WHERE datum = TO_DATE('$dbdate', 'YYYY-MM-DD')
),
final_totals AS (
    SELECT 
        ROUND(SUM(at.suma / COALESCE(er.kurz, 1)), 2) AS suma,
        CASE 
            WHEN at.mena = 'EUR' THEN 'EUR' 
            ELSE 'iná mena' 
        END AS mena,
        CASE 
            WHEN at.stateid = 1 THEN 'rezident' 
            ELSE 'nerezident' 
        END AS krajina
    FROM asset_totals at
    LEFT JOIN exchange_rates er ON 'EUR' || at.mena = er.ric
    GROUP BY at.mena, at.stateid
)

SELECT 
    SUM(ft.suma) AS suma,
    ft.mena,
    ft.krajina AS domicil
FROM final_totals ft
GROUP BY ft.mena, ft.krajina
ORDER BY ft.mena, ft.krajina;
";

$pohladavkyRes = Connection::getDataFromDatabase($query, defaultDB);
$pohladavky = $pohladavkyRes[1];


foreach ($pohladavky as $item) {
    $cislozmluvy = $item["cislozmluvy"];
    $fondids[$cislozmluvy]['pohladavky'][] = [
        "domicil" => $item["domicil"],
        "mena" => $item["mena"],
        "suma" => round($item["suma"], 2),
    ];
}
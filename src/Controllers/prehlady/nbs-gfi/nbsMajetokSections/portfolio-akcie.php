<?php
$query = "WITH equity_data AS (
    SELECT
        d.isinreal AS ISIN, 
        CASE 
            WHEN d.druheqid IN ('3', '4', '13', '14') THEN 'A'
            WHEN d.druheqid = '8' THEN 'ETF'
            WHEN d.druheqid IN ('10', '11', '17') THEN 'PODL'
            WHEN d.druheqid = '15' THEN 'ETF'
            WHEN d.druheqid = '16' THEN 'ICP'
            ELSE '!!!' 
        END AS Druh,
        SUM(mt.pocet) AS Pocet,
        d.currencynom AS Mena,
        MIN(mt.kurzaktiva * f_menovy_kurz.value) AS Cena,
        CASE 
            WHEN po.fpo IN ('0', '2') THEN 'FO'
            WHEN po.fpo = '1' THEN 'PO'
            ELSE '!!!' 
        END AS Portfolio
    FROM
        dbequity d
    JOIN dbequitycurr dc ON dc.isin = d.isin
    JOIN dbequitycurrric dcr ON dcr.isincurr = dc.isincurr
    JOIN majetoktotal mt ON mt.kodaktiva = dcr.isincurrric
    JOIN portfolio p ON p.fondid = mt.subjektid
    JOIN podielnik po ON po.podielnikid = p.podielnikid
    JOIN fonds f ON f.fondid = p.fondid
    JOIN LATERAL (
        SELECT f_menovy_kurz_lot(mt.menadenom, d.currencynom, mt.datum) AS value
    ) f_menovy_kurz ON true
    WHERE
        d.eqid IN ('Shares', 'Fonds')
        AND mt.datum = TO_DATE('$dbdate', 'YYYY-MM-DD')
        AND mt.uctovnykod IN (251200, 251300)
        AND f.typpredlohy < 4
    GROUP BY
        d.isinreal, 
        d.druheqid, 
        po.fpo, 
        d.currencynom
)
SELECT *
FROM equity_data
ORDER BY ISIN, Mena, Portfolio;
";

$portfolioAkcieRes = Connection::getDataFromDatabase($query, defaultDB);
$portfolioAkcie = $portfolioAkcieRes[1];

foreach ($portfolioAkcie as $item) {
    $cislozmluvy = $item["cislozmluvy"];
    $fondids[$cislozmluvy]['portfolioAkcie'][] = [
        "isin" => $item["isin"],
        "druh" => $item["druh"],
        "pocet" => $item["pocet"],
        "mena" => $item["mena"],
        "cena" => $item["cena"],
        "portfolio" => $item["portfolio"],
        "zocp" => $item["n"],
    ];
}
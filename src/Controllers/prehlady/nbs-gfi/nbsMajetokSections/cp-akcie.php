<?php
$query = "SELECT IM,
       M,
       KM,
       SeM,
       ISIN,
       PoK,
       NaCP,
       Druh,
       MenaD,
       EmIco,
       EmNaz,
       EmStat,
       EmSek,
       SUM(Pocet) AS Pocet,
       Cena,
       ZdrojCeny,
       VedSluz
FROM (SELECT 88888888                                                                                              AS IM,
             'FO'                                                                                                  AS M,
             sm.stateshort                                                                                         AS KM,
             s95m.esa95_sektoroznacenie                                                                            AS SeM,
             d.isinreal                                                                                            AS ISIN,
             'ISIN'                                                                                                AS PoK,
             d.cpnaz                                                                                               AS NaCP,
             ed.skratkanbs                                                                                         AS Druh,
             dc.currencytrade                                                                                      AS MenaD,
             COALESCE((SELECT ee1.emitentico FROM equityEmitent ee1 WHERE ee1.emitentid = ee.emitentid),
                      99999999)                                                                                    AS EmIco,
             ee.emitentnazov                                                                                       AS EmNaz,
             se.stateshort                                                                                         AS EmStat,
             s95e.esa95_sektoroznacenie                                                                            AS EmSek,
             mt.pocet                                                                                              AS Pocet,
             mt.kurzaktiva                                                                                         AS Cena,
             CASE WHEN dcr.kurz = 1 THEN 'MV' WHEN dcr.kurz = 0 THEN 'EP' ELSE '!!!' END                           AS ZdrojCeny,
             CASE
                 WHEN f.typpredlohy IN (2, 3) THEN 'RP'
                 WHEN f.typpredlohy = 4 THEN 'VUM'
                 ELSE '!!!' END                                                                                    AS VedSluz
      FROM dbequity d
               JOIN
           dbequitycurr dc ON dc.isin = d.isin
              JOIN
           majetoktotal mt ON mt.datum = '$dbdate'::DATE AND mt.uctovnykod IN (251200, 251300)
               JOIN
           dbequitycurrric dcr ON dcr.isincurr = dc.isincurr AND dcr.isincurrric = mt.kodaktiva
              JOIN
           portfolio p ON p.fondid = mt.subjektid
               JOIN
           podielnik po ON po.podielnikid = p.podielnikid
               JOIN
           fonds f ON f.fondid = p.fondid
               JOIN
           state sm ON sm.stateid = po.stateid
               JOIN
           equityemitent ee ON ee.emitentid = d.emitentid
               JOIN
           state se ON se.stateid = ee.emitentstateid
               JOIN
           fondsmu fm ON fm.fondid = mt.subjektid AND fm.cum = mt.ucetaktiva
               LEFT JOIN
           sektor_esa95 s95m ON s95m.esa95_sektorid = po.sektor_esa95
               LEFT JOIN
           sektor_esa95 s95e ON s95e.esa95_sektorid = ee.emitentsektor
               LEFT JOIN
           equitydruh ed ON ed.druheqid = d.druheqid

      UNION ALL

      SELECT 88889999                                                                                              AS IM,
             'FO'                                                                                                  AS M,
             sm.stateshort                                                                                         AS KM,
             s95m.esa95_sektoroznacenie                                                                            AS SeM,
             d.isinreal                                                                                            AS ISIN,
             'ISIN'                                                                                                AS PoK,
             d.cpnaz                                                                                               AS NaCP,
             CASE d.druheqid
                 WHEN '3' THEN 'A'
                 WHEN '4' THEN 'A'
                 WHEN '8' THEN 'ETF'
                 WHEN '10' THEN 'PL'
                 WHEN '11' THEN 'PL'
                 WHEN '13' THEN 'A'
                 WHEN '14' THEN 'A'
                 WHEN '15' THEN 'ETF'
                 WHEN '16' THEN 'ICP'
                 WHEN '17' THEN 'PL'
                 ELSE '!!!'
                 END                                                                                               AS Druh,
             dc.currencytrade                                                                                      AS MenaD,
             COALESCE((SELECT ee1.emitentico FROM equityEmitent ee1 WHERE ee1.emitentid = ee.emitentid),
                      99999999)                                                                                    AS EmIco,
             ee.emitentnazov                                                                                       AS EmNaz,
             se.stateshort                                                                                         AS EmStat,
             s95e.esa95_sektoroznacenie                                                                            AS EmSek,
             mt.pocet                                                                                              AS Pocet,
             mt.kurzaktiva                                                                                         AS Cena,
             CASE WHEN dcr.kurz = 1 THEN 'MV' WHEN dcr.kurz = 0 THEN 'EP' ELSE '!!!' END                           AS ZdrojCeny,
             CASE
                 WHEN f.typpredlohy IN (2, 3) THEN 'RP'
                 WHEN f.typpredlohy = 4 THEN 'VUM'
                 ELSE '!!!' END                                                                                    AS VedSluz
      FROM dbequity d
               JOIN
           dbequitycurr dc ON dc.isin = d.isin
               JOIN
           majetoktotal mt ON mt.datum = '$dbdate'::DATE AND mt.uctovnykod IN (251200, 251300)
           JOIN
           dbequitycurrric dcr ON dcr.isincurr = dc.isincurr AND dcr.isincurrric = mt.kodaktiva
               JOIN
           portfolio p ON p.fondid = mt.subjektid
               JOIN
           podielnik po ON po.podielnikid = p.podielnikid
               JOIN
           fonds f ON f.fondid = p.fondid
               JOIN
           state sm ON sm.stateid = po.stateid
               JOIN
           equityemitent ee ON ee.emitentid = d.emitentid
               JOIN
           state se ON se.stateid = ee.emitentstateid
               LEFT JOIN
           sektor_esa95 s95m ON s95m.esa95_sektorid = po.sektor_esa95
               LEFT JOIN
           sektor_esa95 s95e ON s95e.esa95_sektorid = ee.emitentsektor) AS subquery
GROUP BY IM, M, KM, SeM, ISIN, PoK, NaCP, Druh, MenaD, EmIco, EmNaz, EmStat, EmSek, Cena, ZdrojCeny, VedSluz
ORDER BY ISIN, MenaD;";

$cpakcieRes = Connection::getDataFromDatabase($query, defaultDB);
$cpakcie = $cpakcieRes[1];
foreach ($cpakcie as $item) {
    $cislozmluvy = $item["cislozmluvy"];
    $fondids[$cislozmluvy]['cpakcie'][] = [
        "im" => $item["im"],
        "majitel" => $item["m"],
        "krajina" => $item["km"],
        "sektor" => $item["sem"],
        "isin" => $item["isin"],
        "popis" => $item["pok"],
        "cpnaz" => $item["nacp"],
        "druh" => $item["druh"],
        "mena" => $item["menad"],
        "emitent" => $item["emico"],
        "emitentnaz" => $item["emnaz"],
        "emitentkrajina" => $item["emstat"],
        "emitentsektor" => $item["emsek"],
        "pocet" => $item["pocet"],
        "cena" => $item["cena"],
        "zdrojceny" => $item["zdrojceny"],
        "vedsluz" => $item["vedsluz"],
    ];
}
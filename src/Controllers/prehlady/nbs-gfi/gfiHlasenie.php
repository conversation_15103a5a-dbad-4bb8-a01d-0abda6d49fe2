<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

$rok = $_POST["rok"];
$stvrtrok = $_POST["stvrtrok"];

if ($stvrtrok == 1) {
    $datefrom = "01.01." . $rok;
    $dateto = "31.03." . $rok;
} else if ($stvrtrok == 2) {
    $datefrom = "01.04." . $rok;
    $dateto = "30.06." . $rok;
} else if ($stvrtrok == 3) {
    $datefrom = "01.07." . $rok;
    $dateto = "30.09." . $rok;
} else {
    $datefrom = "01.10." . $rok;
    $dateto = "31.12." . $rok;
}

$vyskaMajetkuQuery = "
WITH kruzik AS (SELECT kurz, datum, ric
                FROM kurzyaktivarchiv
                WHERE LENGTH(ric) = 6),
     aggregated_data AS (SELECT po.gfi,
                                m.datum,
                                SUM(COALESCE(CASE WHEN md_d = 0 THEN md_d0 ELSE md_d1 END * sumadenom, 0)) AS suma_pomocna,
                                m.menadenom
                         FROM majetoktotal m
                                  JOIN navuctovanie nu ON m.uctovnykod = nu.uctovnykod
                                  JOIN portfolio p ON m.subjektid = p.fondid
                                  JOIN podielnik po ON p.podielnikid = po.podielnikid
                         WHERE m.datum <= to_date('$dateto', 'dd.mm.yyyy')
                           and m.datum >= to_date('$datefrom', 'dd.mm.yyyy')
                           AND po.gfi > 0
                         GROUP BY po.gfi, m.datum, m.menadenom)
SELECT (SUM(a.suma_pomocna * k.kurz)) / (MAX(a.datum) - MIN(a.datum) + 1) AS suma
FROM aggregated_data a
         JOIN kruzik k ON k.ric = 'EUR' || a.menadenom AND k.datum = a.datum
";

$vyskaMajetkuRes = Connection::getDataFromDatabase($vyskaMajetkuQuery, defaultDB);
$vyskaMajetku = number_format(round($vyskaMajetkuRes[1][0]["suma"], 2), 2, ".", " ");

$pocetKlientovGFIQuery = "
WITH kruzik AS (SELECT kurz, datum, ric
                FROM kurzyaktivarchiv
                WHERE LENGTH(ric) = 6),
     filtered_data AS (SELECT po.podielnikid,
                              m.datum,
                              m.menadenom,
                              CASE WHEN md_d = 0 THEN md_d0 ELSE md_d1 END * sumadenom AS sum_value
                       FROM majetoktotal m
                                JOIN navuctovanie nu ON m.uctovnykod = nu.uctovnykod
                                JOIN portfolio p ON m.subjektid = p.fondid
                                JOIN podielnik po ON p.podielnikid = po.podielnikid
                       WHERE m.datum BETWEEN TO_DATE('$datefrom', 'dd.mm.yyyy')
                           AND TO_DATE('$dateto', 'dd.mm.yyyy')
                         AND po.gfi = 1),
     summed_data AS (SELECT f.podielnikid,
                            f.datum,
                            f.menadenom,
                            COALESCE(
                                    CASE
                                        WHEN total_sum * k.kurz > 50000 THEN 50000
                                        ELSE total_sum * k.kurz
                                        END,
                                    0) AS suma_datum_klient
                     FROM (SELECT f.podielnikid,
                                  f.datum,
                                  f.menadenom,
                                  SUM(f.sum_value) AS total_sum
                           FROM filtered_data f
                           GROUP BY f.datum, f.podielnikid, f.menadenom) AS f
                              JOIN kruzik k ON f.datum = k.datum),
     final_data AS (SELECT SUM(suma_datum_klient)      AS suma_majetku,
                           MIN(datum)                  AS mindatum,
                           MAX(datum)                  AS maxdatum,
                           COUNT(DISTINCT podielnikid) AS pocet
                    FROM summed_data)
SELECT (suma_majetku / (maxdatum - mindatum + 1)) as priemerny_majetok, pocet
FROM final_data;
";

$pocetKlientovGFIRes = Connection::getDataFromDatabase($pocetKlientovGFIQuery, defaultDB);
$pocetKlientovGFI = $pocetKlientovGFIRes[1];

$pravnickeOsobyQuery = "
			select 
				cislozmluvy
			from
				portfolio p, 
				podielnik po
			WHERE
				po.gfi = 0 and
            			p.PODIELNIKID = po.PODIELNIKID and
				p.fondid in (select distinct subjektid from majetoktotal where datum <= to_date('$dateto','dd.mm.yyyy') and
            				datum >= to_date('$datefrom','dd.mm.yyyy')) 
			ORDER BY
				cislozmluvy
		";

$pravnickeOsobyRes = Connection::getDataFromDatabase($pravnickeOsobyQuery, defaultDB);
$pravnickeOsoby = $pravnickeOsobyRes[1];

$osobyQuery = "
			select 
				cislozmluvy
			from
				portfolio p, 
				podielnik po
			WHERE
				po.gfi = 2 and
            			p.PODIELNIKID = po.PODIELNIKID and
				p.fondid in (select distinct subjektid from majetoktotal where datum <= to_date('$dateto','dd.mm.yyyy') and
            				datum >= to_date('$datefrom','dd.mm.yyyy')) 
			ORDER BY
				cislozmluvy
		";

$osobyRes = Connection::getDataFromDatabase($osobyQuery, defaultDB);
$osoby = $osobyRes[1];

$items = [$vyskaMajetku, $pocetKlientovGFI, $pravnickeOsoby, $osoby];
$columns = [];
$filename = "Podklady_k_hlaseniu_GFI";
$property = "";

$reportTable = '
        <div class="relative mt-4 mb-6 overflow-x-auto shadow-md sm:rounded-lg">
        <div class="p-5 text-xl font-semibold text-left rtl:text-right text-gray-900 bg-white dark:text-white dark:bg-gray-800">
            <section class="flex w-full justify-between items-center p-1">
                <div>Podklady k hláseniu GFI
                    <p class="mt-1 text-sm font-normal text-gray-500 dark:text-gray-400"><span
                                class="flex items-center gap-2">za ' . $stvrtrok . '.štvrťrok ' . $rok . '
                        </span>
                    </p>
                </div>
                <form id="exportToExcelGFI" class="excelForm" method="post" action="">
                <input type="hidden" name="data" ' . "value='" . json_encode($items) . "'" . '/>
                <input type="hidden" name="columns" ' . "value='" . json_encode($columns) . "'" . '/>
                <input type="hidden" name="filename" value="' . $filename . '"/>
                <input type="hidden" name="property" value="' . $property . '"/>
                <button id="exportToExcelTrigger" type="submit"
                        class="p-4 bg-gray-100 flex items-center justify-center gap-2 rounded-xl text-center font-semibold text-gray-600 hover:bg-green-200 transition-all cursor-pointer hover:text-white">
                    <svg id="excelIcon" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="25" height="25"
                         viewBox="0 0 48 48">
                        <path fill="#169154" d="M29,6H15.744C14.781,6,14,6.781,14,7.744v7.259h15V6z"></path>
                        <path fill="#18482a"
                              d="M14,33.054v7.202C14,41.219,14.781,42,15.743,42H29v-8.946H14z"></path>
                        <path fill="#0c8045" d="M14 15.003H29V24.005000000000003H14z"></path>
                        <path fill="#17472a" d="M14 24.005H29V33.055H14z"></path>
                        <g>
                            <path fill="#29c27f" d="M42.256,6H29v9.003h15V7.744C44,6.781,43.219,6,42.256,6z"></path>
                            <path fill="#27663f"
                                  d="M29,33.054V42h13.257C43.219,42,44,41.219,44,40.257v-7.202H29z"></path>
                            <path fill="#19ac65" d="M29 15.003H44V24.005000000000003H29z"></path>
                            <path fill="#129652" d="M29 24.005H44V33.055H29z"></path>
                        </g>
                        <path fill="#0c7238"
                              d="M22.319,34H5.681C4.753,34,4,33.247,4,32.319V15.681C4,14.753,4.753,14,5.681,14h16.638 C23.247,14,24,14.753,24,15.681v16.638C24,33.247,23.247,34,22.319,34z"></path>
                        <path fill="#fff"
                              d="M9.807 19L12.193 19 14.129 22.754 16.175 19 18.404 19 15.333 24 18.474 29 16.123 29 14.013 25.07 11.912 29 9.526 29 12.719 23.982z"></path>
                    </svg>
                    <svg aria-hidden="true" id="spinnerExcel"
                         class="w-6 h-6 hidden text-gray-200 animate-spin dark:text-gray-600 fill-blue-600"
                         viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                              fill="currentColor"/>
                        <path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                              fill="currentFill"/>
                    </svg>
                </button>
                </form>
            </section>
            </div>
                <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
        <tbody>
            <tr class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b dark:border-gray-700">
                        <td class="px-6 py-4 font-semibold text-md">Priemerná výša celkového klientského majetku § 81 zákona 566/2001</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $vyskaMajetku . '</td>
            </tr>
            <tr class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b dark:border-gray-700">
                        <td class="px-6 py-4 font-semibold text-md">Priemerný počet klientov spadajúcich pod ochranu GFI</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $pocetKlientovGFI[0]["pocet"] . '</td>
            </tr>
            <tr class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b dark:border-gray-700">
                        <td class="px-6 py-4 font-semibold text-md">Priemerná výška náhrad za nedostupný klientsky majetok § 87 zákona 566/2001</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $pocetKlientovGFI[0]["priemerny_majetok"] . '</td>
            </tr>
            </tbody>
            </table>
            </div>
        ';

echo $reportTable;
echo '<script src="/src/assets/js/prehlady/nbs-gfi/excel.js"></script>';
?>

<h2 class="text-lg font-bold my-2">Právnické osoby nezahrňované do klientkeho majetku:</h2>
<div class="flex items-center gap-4 flex-wrap">
    <?php foreach ($pravnickeOsoby as $item) { ?>
        <div class="bg-white p-3 px-6 rounded-lg border shadow-lg flex items-center justify-center"><?php echo $item["cislozmluvy"]; ?></div>
    <?php } ?>
</div>

<h2 class="text-lg mt-10 font-bold my-2">
    Osoby s osobitným vzťahom nezahrňované do chráneného majetku:</h2>
<div class="flex items-center gap-4 flex-wrap">
    <?php foreach ($osoby as $item) { ?>
        <div class="bg-white p-3 px-6 rounded-lg border shadow-lg flex items-center justify-center"><?php echo $item["cislozmluvy"]; ?></div>
    <?php } ?>
</div>

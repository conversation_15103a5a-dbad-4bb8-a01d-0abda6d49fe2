<?php
require_once "/home/<USER>/www/lib/SimpleXLSXGen/SimpleXLSXGen.php";


$columns = json_decode($_POST["columns"]);
$data = json_decode($_POST["data"]);
$property = $_POST["property"];

/*$arrayAkcie = get_object_vars($data[0]->Akcie);
$properties = array_keys($arrayAkcie);
$akcieRows = [];*/
$props = [];
$suhrneUdaje = [$columns[0]];

$array = get_object_vars($data[0]);
$properties = array_keys($array);
foreach ($properties as $key => $property) {
    $arrayK = get_object_vars($data[0]->$property);
    $props = array_keys($arrayK);
    foreach ($props as $key => $prop) {
        $suhrneUdaje = array_merge($suhrneUdaje, [["<middle><center>$property</center></middle>", $prop, $arrayK[$prop]->rezidenti, $arrayK[$prop]->nerezidenti, $arrayK[$prop]->rezidenti + $arrayK[$prop]->nerezidenti, $arrayK[$prop]->burzoveObchody, $arrayK[$prop]->mimoburzoveObchody]]);
    }
}

$suhrneUdaje = array_merge($suhrneUdaje, ["", ""]);
$suhrneUdaje = array_merge($suhrneUdaje, [$columns[1]]);

$array = get_object_vars($data[1]);
$properties = array_keys($array);
foreach ($properties as $key => $property) {
    if ($property !== "objemMajetekuSpolu") {
        $arrayK = get_object_vars($data[1]->$property);
        $props = array_keys($arrayK);
        $popis = $arrayK['popis'];
        $suhrneUdaje = array_merge($suhrneUdaje, [["<middle><center>$popis</center></middle>", $arrayK["rezidenti"], $arrayK["nerezidenti"], $arrayK["rezidenti"] + $arrayK["nerezidenti"]]]);
    }
}
$array = get_object_vars($data[1]->objemMajetekuSpolu);
$properties = array_keys($array);
$popisek = $array["popis"];
$suhrneUdaje = array_merge($suhrneUdaje, [["<middle><center>$popisek</center></middle>", $array["rezidenti"], $array["nerezidenti"], $array["spolu"]]]);

$array = get_object_vars($data[1]->objemMajetekuSpolu->predvolitelnecps);
$popisek = $array["popis"];
$suhrneUdaje = array_merge($suhrneUdaje, [["<middle><center>$popisek</center></middle>", $array["rezidenti"], $array["nerezidenti"], $array["spolu"]]]);

$array = get_object_vars($data[1]->objemMajetekuSpolu->predvolitenlnecp->akcie);
$popisek = $array["popis"];
$suhrneUdaje = array_merge($suhrneUdaje, [["<middle><center>$popisek</center></middle>", $array["rezidenti"], $array["nerezidenti"], $array["spolu"]]]);

$array = get_object_vars($data[1]->objemMajetekuSpolu->predvolitenlnecp->dlhopisy);
$popisek = $array["popis"];
$suhrneUdaje = array_merge($suhrneUdaje, [["<middle><center>$popisek</center></middle>", $array["rezidenti"], $array["nerezidenti"], $array["spolu"]]]);

$array = get_object_vars($data[1]->objemMajetekuSpolu->cpZahranicne);
$popisek = $array["popis"];
$suhrneUdaje = array_merge($suhrneUdaje, [["<middle><center>$popisek</center></middle>", $array["rezidenti"], $array["nerezidenti"], $array["spolu"]]]);

$array = get_object_vars($data[1]->objemMajetekuSpolu->peniaze);
$popisek = $array["popis"];
$suhrneUdaje = array_merge($suhrneUdaje, [["<middle><center>$popisek</center></middle>", $array["rezidenti"], $array["nerezidenti"], $array["spolu"]]]);

$array = get_object_vars($data[1]->objemMajetekuSpolu->pohladavky);
$popisek = $array["popis"];
$suhrneUdaje = array_merge($suhrneUdaje, [["<middle><center>$popisek</center></middle>", $array["rezidenti"], $array["nerezidenti"], $array["spolu"]]]);

$array = get_object_vars($data[1]->objemMajetekuSpolu->zavazky);
$popisek = $array["popis"];
$suhrneUdaje = array_merge($suhrneUdaje, [["<middle><center>$popisek</center></middle>", $array["rezidenti"], $array["nerezidenti"], $array["spolu"]]]);

$suhrneUdaje = array_merge($suhrneUdaje, ["", ""]);
$suhrneUdaje = array_merge($suhrneUdaje, [$columns[2]]);

$array = $data[2]->uschovaAsprava;
foreach ($array as $key => $item) {
    $suhrneUdaje = array_merge($suhrneUdaje, [["<middle><center>$item->popis</center></middle>", $item->rezidenti, $item->nerezidenti, $item->spolu]]);
}

$array = $data[2]->obchodyDevizove;
foreach ($array as $key => $item) {
    $suhrneUdaje = array_merge($suhrneUdaje, [["<middle><center>$item->popis</center></middle>", $item->rezidenti, $item->nerezidenti, $item->spolu]]);
}

$xlsx = Shuchkin\SimpleXLSXGen::fromArray($suhrneUdaje, 'Hlásenie NBS o investičných službách');
$xlsx->mergeCells('A2:A4');
$xlsx->mergeCells('A5:A7');
$xlsx->mergeCells('A8:A10');
$xlsx->saveAs("/home/<USER>/www/temp/" . $_POST["filename"] . '.xlsx');

header('Content-Type: application/json');
echo json_encode(["data" => $suhrneUdaje, "link" => '/home/<USER>/www/temp/' . $_POST["filename"] . '.xlsx']);
<?php
require_once "/home/<USER>/www/lib/SimpleXLSXGen/SimpleXLSXGen.php";


$columns = json_decode($_POST["columns"]);
$data = json_decode($_POST["data"]);
$property = $_POST["property"];

$array = (array)$data->$property;

$suhrneUdaje = array_merge(
    [$columns],
    $array
);

$xlsx = Shu<PERSON>kin\SimpleXLSXGen::fromArray($suhrneUdaje, 'Súhrné údaje');
$xlsx->saveAs("/home/<USER>/www/temp/" . $_POST["filename"]);

header('Content-Type: application/json');
echo json_encode(["data" => $suhrneUdaje, "link" => '/home/<USER>/www/temp/' . $_POST["filename"]]);
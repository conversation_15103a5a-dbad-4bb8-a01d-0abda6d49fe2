<?php
require_once "/home/<USER>/www/lib/SimpleXLSXGen/SimpleXLSXGen.php";


$columns = json_decode($_POST["columns"]);
$data = json_decode($_POST["data"]);
$property = $_POST["property"];

$array = (array)$data->$property;

$suhrneUdaje = array_merge(
    [["Priemerná výša celkového klientského majetku § 81 zákona 566/2001", $data[0]]],
    [["Priemerný počet klientov spadajúcich pod ochranu GFI", $data[1][0]->pocet]],
    [["Priemerná výška náhrad za nedostupný klientsky majetok § 87 zákona 566/2001", $data[1][0]->priemerny_majetok]],
    [["Právnické osoby nezahrňované do klientkeho majetku:"]],
    $data[2],
    [["Osoby s osobitným vzťahom nezahrňované do chráneného majetku:"]],
    $data[3]
);

$xlsx = Shuchkin\SimpleXLSXGen::fromArray($suhrneUdaje, 'Súhrné údaje');
$xlsx->saveAs("/home/<USER>/www/temp/" . $_POST["filename"] . ".xlsx");

header('Content-Type: application/json');
echo json_encode(["data" => $data, "link" => '/home/<USER>/www/temp/' . $_POST["filename"] . ".xlsx"]);
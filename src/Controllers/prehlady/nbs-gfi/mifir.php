<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

$date = $_POST["date"];

$protistranyQuery = "SELECT *
FROM protistrana
WHERE Is_Active::integer = 1
  AND Is_visible::integer = 1
ORDER BY nazov";

$protistranyRes = Connection::getDataFromDatabase($protistranyQuery, defaultDB);
$protistrany = $protistranyRes[1];

$tabulkaQuery = "select  ha.eqid, dealid,  meno, prieznaz,case ha.eqid when 'Bonds' then pocet*nominalemisie else pocet end as pocet, 
					currencyidtrade, limitkurz,hh.isin ,cpnaz
			from	(
				select  k.dealid, case when k.subjektid = 0 then pool.subjektid else k.subjektid end as subjektid,
						eqid, isin,  case when k.subjektid = 0 then ksreal else kusov end as pocet, currencyidtrade, limitkurz 
				from konfirmaciacp k				
				left join 	(
					select dealid, subjektid, ksreal, transsumareal, auvreal, cenaob<PERSON><PERSON> 
					from pool p
					left join pooldetailreal pd on p.poolid = pd.poolid
							) pool on pool.dealid = k.dealid
				where kusovreal != 0 and druhobchodu = 'nakup' and datpok = '$date'
					) hh 
			left join portfolio p on hh.subjektid = p.fondid
			left join podielnik po on po.podielnikid = p.podielnikid
			left join 	(
				select cpnaz, eqid, isin, maturitydate, currencynom, nominalemisie
				from dbequity
						) ha on ha.isin = hh.isin
			
			union all
			
			select   ha.eqid, dealid,  meno, prieznaz,case ha.eqid when 'Bonds' then pocet*nominalemisie else pocet end as pocet, 
					currencyidtrade, limitkurz,hh.isin,cpnaz
			from 	(
				select   k.dealid, case when k.subjektid = 0 then pool.subjektid else k.subjektid end as subjektid, 
						eqid, isin,  case when k.subjektid = 0 then ksreal else kusov end as pocet, currencyidtrade, limitkurz
				from konfirmaciacp k				
				left join 	(
					select dealid, subjektid, ksreal, transsumareal, auvreal, cenaobchodu 
					from pool p 
					left join pooldetailreal pd on p.poolid = pd.poolid
							) pool on pool.dealid = k.dealid
				where kusovreal != 0 and druhobchodu = 'predaj' and datpok = '$date'
					) hh 
			left join portfolio p on hh.subjektid = p.fondid
			left join podielnik po on po.podielnikid = p.podielnikid
			left join 	(
				select cpnaz, eqid, isin, maturitydate, currencynom, nominalemisie
				from dbequity
						) ha on ha.isin = hh.isin
			
			order by  dealid desc";

$tabulkaRes = Connection::getDataFromDatabase($tabulkaQuery, defaultDB);
$tabulka = $tabulkaRes[1];

$reportTable = '
    <form class="mifirForm" method="post" action="">
        <div class="relative mt-4 mb-6 overflow-x-auto shadow-md sm:rounded-lg">
        <div class="p-5 text-xl font-semibold text-left rtl:text-right text-gray-900 bg-white dark:text-white dark:bg-gray-800">
            <section class="flex w-full justify-between items-center p-1">
                <div>MiFIR zoznam obchodov k ' . $date . '
                    <p class="mt-1 text-sm font-normal text-gray-500 dark:text-gray-400"><span
                                class="flex items-center gap-2">
                        </span>
                    </p>
                </div>
                <button id="exportToExcelTrigger" type="submit"
                        class="p-4 bg-gray-100 flex items-center text-xs justify-center gap-2 rounded-xl text-center font-semibold text-gray-600 hover:bg-green-200 transition-all cursor-pointer">
                    <svg class="w-5 excelIcon h-5 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                      <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m8 8-4 4 4 4m8 0 4-4-4-4m-2-3-4 14"/>
                    </svg>
                    XML Export
                    <svg aria-hidden="true" id="spinnerExcel"
                         class="w-6 h-6 hidden text-gray-200 animate-spin dark:text-gray-600 fill-blue-600"
                         viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                              fill="currentColor"/>
                        <path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                              fill="currentFill"/>
                    </svg>
                </button>
            </section>
            </div>
                <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400" style="display: block;
  overflow-x: auto;
  white-space: nowrap; width: 100%;">
                <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
            <tr>
                <th scope="col" class="p-4">
                    <div class="flex items-center">
                        <input id="checkbox-all-search" type="checkbox" disabled="disabled" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                        <label for="checkbox-all-search" class="sr-only">checkbox</label>
                    </div>
                </th>
                <th scope="col" class="px-6 py-3">
                    Protistrana
                </th>
                <th scope="col" class="px-6 py-3">
                    ISIN
                </th>
                <th scope="col" class="px-6 py-3">
                    Názov
                </th>
                <th scope="col" class="px-6 py-3">
                    Druh CP
                </th>
                <th scope="col" class="px-6 py-3">
                    Počet kusov / Nominálna hodnota
                </th>
                <th scope="col" class="px-6 py-3">
                    Trhová cena
                </th>
                <th scope="col" class="px-6 py-3">
                    Meno
                </th>
                <th scope="col" class="px-6 py-3">
                    Priezvisko / Názov
                </th>
            </tr>
        </thead>
        <input type="hidden" value="' . $date . '" name="date"/>
        <tbody>';
foreach ($tabulka as $key => $item) {
    $reportTable .= '<tr class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b dark:border-gray-700">
                        <td class="w-4 p-4">
                            <div class="flex items-center">
                                <input id="checkbox-table' . $key . '" type="checkbox" value="' . $item["dealid"] . '" disabled="disabled" class="checkboxStrana w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                                <label for="checkbox-table' . $key . '" class="sr-only">checkbox</label>
                            </div>
                        </td>
                        <td class="px-6 py-4 font-semibold text-md">
                        <select id="protistrana' . $key . '" name="protistrana" class="protistranaSelect bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                        <option class="emptyOption" selected value=""></option>';
    foreach ($protistrany as $strana) {
        $reportTable .= '<option value="' . $strana["lei"] . '">' . $strana["nazov"] . '</option>';
    }
    $reportTable .= '</select></td>
            <td class="px-6 py-4">
                    ' . $item["isin"] . '
            </td>
            <td class="px-6 py-4">
                    ' . $item["cpnaz"] . '
            </td>
            <td class="px-6 py-4">
                    ' . $item["eqid"] . '
            </td>
             <td class="px-6 py-4">
                    ' . $item["pocet"] . '
            </td>
            <td class="px-6 py-4">
                    ' . $item["currencyidtrade"] . '
            </td>
            <td class="px-6 py-4">
                    ' . round($item["limitkurz"], 2) . '
            </td>
            <td class="px-6 py-4">
                    ' . $item["meno"] . '
            </td>
            <td class="px-6 py-4">
                    ' . $item["prieznaz"] . '
            </td>
            </tr>';
}
$reportTable .= '</tbody></table></div></form>';
echo $reportTable;
echo '<script src="/src/assets/js/prehlady/nbs-gfi/mifir.js"></script>';
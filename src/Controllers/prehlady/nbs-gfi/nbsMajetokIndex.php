<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
$dbdate = $_POST["date"];
$errors = [];
$columns = [];
$filename = "";
$property = "";

if ($_POST["portfoliovalues"] === "") {
    $errors[] = "Minimálne jedno portfólio musí byť vybrané zo zoznamu.";
}

if (!isset($_POST["cpakcie"]) && !isset($_POST["cpdlhopisy"]) && !isset($_POST["portfolioakcie"]) && !isset($_POST["portfoliodlhopisy"]) && !isset($_POST["portfoliopenazne"]) && !isset($_POST["zavazky"])) {
    $errors[] = "Minimálne jeden typ výnosu musí byť zvolený.";
}

if (!empty($errors)) {
    $errorDiv = '<div class="p-4 my-4 text-sm text-red-800 rounded-lg bg-red-50" role="alert">
        <strong class="font-bold">Vyskytly sa chyby! Prosím opravte ich a následne skúste znova.</strong>
        <ul class="max-w-md py-2 text-red-500 list-disc list-inside dark:text-gray-400">';
    foreach ($errors as $error) {
        $errorDiv .= '<li>' . $error . '</li>';
    }
    $errorDiv .= '</ul></div>';
    echo $errorDiv;
    exit;
}

$fondids = [];

if (isset($_POST["cpakcie"])) {
    include "nbsMajetokSections/cp-akcie.php";
    $columns = ["Identifikácia majteiľa", "Majiteľ CP", "Krajina majiteľa", "Sektor majiteľa", "ISIN / Kód CP",
        "Popis kódu", "Názov CP", "Druh CP", "Mena", "Identifikácia emitenta", "Názov emitenta", "Krajina emitenta",
        "Sektor emitenta", "Počet ks", "Trhová cena v D/ks", "Zdroj ocenenia", "Vedľajšia služba podľa p. 6 ods. 2 písm. a) zákona"];
    $filename = "export_NBS_cpakcie.xlsx";
    $property = "cpakcie";
}
if (isset($_POST["cpdlhopisy"])) {
    include "nbsMajetokSections/cp-dlhopisy.php";
    $columns = ["Dátum výplaty", "Dátum nároku", "Názov", "ISIN", "Mena", "Počet ks", "Výnos na kus", "Výnos pred zdanením", "Daň", "Výnos po zdanení"];
    $filename = "export_NBS_cpdlhopisy.xlsx";
    $property = "cpdlhopisy";
}
if (isset($_POST["portfolioakcie"])) {
    include "nbsMajetokSections/portfolio-akcie.php";
    $columns = ["ISIN alebo kód CP", "Druh CP", "Nominálna hodnota AKT", "Mena denominácie CP", "Trhová cena CP za kus", "AÚV", "Portfólio", "ZOCP"];
    $filename = "export_NBS_portfolioakcie.xlsx";
    $property = "portfolioAkcie";
}
if (isset($_POST["portfoliodlhopisy"])) {
    include "nbsMajetokSections/portfolio-dlhopisy.php";
    $columns = ["ISIN alebo kód CP", "Druh CP", "Nominálna hodnota AKT", "Mena denominácie CP", "Trhová cena CP za kus", "AÚV", "Portfólio", "ZOCP"];
    $filename = "export_NBS_portfoliodlhopisy.xlsx";
    $property = "portfolioDlhopisy";
}
if (isset($_POST["portfoliopenazne"])) {
    include "nbsMajetokSections/portfolio-penazne.php";
    $columns = ["Nástroj peňažného trhu", "Suma"];
    $filename = "export_NBS_portfoliopenazne.xlsx";
    $property = "portfolioPenazne";
}
if (isset($_POST["zavazky"])) {
    include "nbsMajetokSections/zavazky.php";
    $columns = ["Domicil", "Mena", "Suma"];
    $filename = "export_NBS_zavazky.xlsx";
    $property = "zavazky";
}

if (empty($cpakcie) && empty($cpdlhopisy) && empty($portfolioAkcie) && empty($portfolioDlhopisy) && empty($portfolioPenazne) && (empty($zavazky) && empty($pohladavky))) {
    echo '<div class="p-4 mb-4 text-lg text-blue-800 rounded-lg bg-blue-50 dark:bg-gray-800 dark:text-blue-400" role="alert">
                  <span class="font-bold">Žiadne záznamy!</span> Podľa vami zadaných kritérií sa nenašiel záznam. Skúste upraviť Vaše vyhľadávanie.
                </div>';
    exit;
}
function buildSectionHeader(string $sectionHeading, array $subSectionColumns): string
{
    $sectionHeader .= '<tr class="bg-gray-800 border-b text-white border-gray-700">';
    $sectionHeader .= '<td colspan="' . sizeof($subSectionColumns) . '" class="px-6 py-4 font-extrabold text-sm">' . $sectionHeading . '</td>';
    $sectionHeader .= '</tr> <tr class="border-b">';
    foreach ($subSectionColumns as $column) {
        $sectionHeader .= '<th scope="col" class="px-6 py-3 text-xs">' . $column . '</td>';
    }
    return $sectionHeader;
}

/*echo "<pre>";
print_r($fondids);
echo "</pre>";*/


foreach ($fondids as $cislozmluvy => $items) {
    $reportTable = '
        <div class="relative mt-4 mb-6 overflow-x-auto shadow-md sm:rounded-lg">
        <div class="p-5 text-xl font-semibold text-left rtl:text-right text-gray-900 bg-white dark:text-white dark:bg-gray-800">
            <section class="flex w-full justify-between items-center p-1">
                <div>Prehľad vyplatených výnosov pre portfólio ' . $cislozmluvy . '
                    <p class="mt-1 text-sm font-normal text-gray-500 dark:text-gray-400"><span
                                class="flex items-center gap-2">Prehľad
                        </span>
                    </p>
                </div>
                <form id="exportToExcel" method="post" action="">
                <input type="hidden" name="data" ' . "value='" . json_encode($items) . "'" . '/>
                <input type="hidden" name="columns" ' . "value='" . json_encode($columns) . "'" . '/>
                <input type="hidden" name="filename" value="' . $filename . '"/>
                <input type="hidden" name="property" value="' . $property . '"/>
                <button id="exportToExcelTrigger" type="submit"
                        class="p-4 bg-gray-100 flex items-center justify-center gap-2 rounded-xl text-center font-semibold text-gray-600 hover:bg-green-200 transition-all cursor-pointer hover:text-white">
                    <svg id="excelIcon" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="25" height="25"
                         viewBox="0 0 48 48">
                        <path fill="#169154" d="M29,6H15.744C14.781,6,14,6.781,14,7.744v7.259h15V6z"></path>
                        <path fill="#18482a"
                              d="M14,33.054v7.202C14,41.219,14.781,42,15.743,42H29v-8.946H14z"></path>
                        <path fill="#0c8045" d="M14 15.003H29V24.005000000000003H14z"></path>
                        <path fill="#17472a" d="M14 24.005H29V33.055H14z"></path>
                        <g>
                            <path fill="#29c27f" d="M42.256,6H29v9.003h15V7.744C44,6.781,43.219,6,42.256,6z"></path>
                            <path fill="#27663f"
                                  d="M29,33.054V42h13.257C43.219,42,44,41.219,44,40.257v-7.202H29z"></path>
                            <path fill="#19ac65" d="M29 15.003H44V24.005000000000003H29z"></path>
                            <path fill="#129652" d="M29 24.005H44V33.055H29z"></path>
                        </g>
                        <path fill="#0c7238"
                              d="M22.319,34H5.681C4.753,34,4,33.247,4,32.319V15.681C4,14.753,4.753,14,5.681,14h16.638 C23.247,14,24,14.753,24,15.681v16.638C24,33.247,23.247,34,22.319,34z"></path>
                        <path fill="#fff"
                              d="M9.807 19L12.193 19 14.129 22.754 16.175 19 18.404 19 15.333 24 18.474 29 16.123 29 14.013 25.07 11.912 29 9.526 29 12.719 23.982z"></path>
                    </svg>
                    <svg aria-hidden="true" id="spinnerExcel"
                         class="w-6 h-6 hidden text-gray-200 animate-spin dark:text-gray-600 fill-blue-600"
                         viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                              fill="currentColor"/>
                        <path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                              fill="currentFill"/>
                    </svg>
                </button>
                </form>
            </section>
            </div>
                <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400" style="display: block;
  overflow-x: auto;
  white-space: nowrap; width: 100%;">
        <tbody>';
    foreach ($items as $section => $sections) {
        switch ($section) {
            case "portfolioPenazne":
                $reportTable .= buildSectionHeader("Klientské portfólio (služba riadenie portfólia) - peňažné prostriedky k $dbdate", $columns);
                break;
            case "portfolioDlhopisy":
                $reportTable .= buildSectionHeader("Klientské portfólio - Pdlhopisy k $dbdate", $columns);
                break;
            case "portfolioAkcie":
                $reportTable .= buildSectionHeader("Klientské portfólio - Pakcie k $dbdate", $columns);
                break;
            case "zavazky":
                $reportTable .= buildSectionHeader("Záväzky zo zverených hodnôt k $dbdate pre NBS", $columns);
                break;
            case "pohladavky":
                $reportTable .= buildSectionHeader("Pohľadávky zo zverených hodnôt k $dbdate pre NBS", $columns);
                break;
            case "cpakcie":
                $reportTable .= buildSectionHeader("Klientské portfólio - akcie k $dbdate", $columns);
                break;
            case "cpdlhopisy":
                $reportTable .= buildSectionHeader("Klientské portfólio - dlhopisy k $dbdate", $columns);
                break;
        }

        foreach ($sections as $item) {
            switch ($section) {
                case "portfolioAkcie":
                    $reportTable .= '
                    <tr class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b dark:border-gray-700">
                        <td class="px-6 py-4 font-semibold text-md">' . $item["isin"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["druh"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["pocet"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["mena"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["cena"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["portfolio"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["zocp"] . '</td>
                    </tr>
                    ';
                    break;
                case "portfolioDlhopisy":
                    $reportTable .= '
                    <tr class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b dark:border-gray-700">
                        <td class="px-6 py-4 font-semibold text-md">' . $item["isin"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["druh"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["nominal"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["mena"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["cena"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["auv"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["portfolio"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["zocp"] . '</td>
                    </tr>
                    ';
                    break;
                case "portfolioPenazne":
                    $reportTable .= '
                    <tr class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b dark:border-gray-700">
                        <td class="px-6 py-4 font-semibold text-md">' . $item["druh"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["suma"] . '</td>
                    </tr>
                    ';
                    break;

                case "cpakcie":
                    $reportTable .= '
                    <tr class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b dark:border-gray-700">
                        <td class="px-6 py-4 font-semibold text-md">' . $item["im"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["majitel"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["krajina"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["sektor"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["isin"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["popis"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["cpnaz"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["druh"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["mena"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["emitent"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["emitentnaz"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["emitentkrajina"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["emitentsektor"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["pocet"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["cena"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["zdrojceny"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["vedsluz"] . '</td>
                    </tr>
                    ';
                    break;
                case "cpdlhopisy":
                    $reportTable .= '
                    <tr class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b dark:border-gray-700">
                        <td class="px-6 py-4 font-semibold text-md">' . $item["im"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["majitel"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["krajina"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["sektor"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["isin"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["cpnaz"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["druh"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["mena"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["datumemisie"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["datummaturity"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["emitent"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["emitentnaz"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["emitentkrajina"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["emitentsektor"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["menovitahodnota"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["pocet"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["cena"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["cenovabaza"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["zdrojceny"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["sumaauv"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["vedsluz"] . '</td>
                    </tr>
                    ';
                    break;
                case "zavazky" || "pohladavky":
                    $reportTable .= '
                    <tr class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b dark:border-gray-700">
                        <td class="px-6 py-4 font-semibold text-md">' . $item["domicil"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["mena"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["suma"] . '</td>
                    </tr>
                    ';
                    break;
            }
        }
    }
    $reportTable .= '</tbody></table></div>';
    echo $reportTable;
}
echo '<script src="/src/assets/js/prehlady/nbs-gfi/excel.js"></script>';
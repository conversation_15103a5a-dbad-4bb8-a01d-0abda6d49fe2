<?php
//CAST B - POCET ZMLUV UZAVRETYCH ZA SLEDOVANE OBDOBIE
$query2R = "
		SELECT SUM(CASE WHEN a.stateid = 1 THEN 1 ELSE 0 END) AS rezidenti,
         SUM(CASE WHEN a.stateid = 2 THEN 1 ELSE 0 END) AS nerezidenti,
         COUNT(a.stateid) as spolu
        FROM (SELECT DISTINCT p.cislozmluvy,
                      CASE WHEN po.stateid = 1 THEN 1 ELSE 2 END AS stateid
            FROM majetoktotal mt
                   JOIN portfolio p ON p.fondid = mt.subjektid
                   JOIN fonds f ON p.fondid = f.fondid
                   JOIN podielnik po ON p.podielnikid = po.podielnikid
          WHERE f.typpredlohy IN (2, 3)
            AND mt.datum BETWEEN TO_DATE('01.01.2024', 'dd.mm.yyyy') AND TO_DATE('31.03.2024', 'dd.mm.yyyy')
            AND p.cislozmluvy NOT IN (SELECT DISTINCT p.cislozmluvy
                                      FROM majetoktotal mt
                                               JOIN portfolio p ON p.fondid = mt.subjektid
                                               JOIN fonds f ON p.fondid = f.fondid
                                               JOIN podielnik po ON p.podielnikid = po.podielnikid
                                      WHERE f.typpredlohy IN (2, 3)
                                        AND mt.datum = (TO_DATE('01.01.2024', 'dd.mm.yyyy') - INTERVAL '1 day'))) a;

	";
$queryRes = Connection::getDataFromDatabase($query2R, defaultDB);
$B_2R_PocetZmluv = $queryRes[1];
foreach ($B_2R_PocetZmluv as $item) {
    $partB['pocetUzavretychZmluv'] = [
        "popis" => "Počet zmlúv uzavretých za sledované obdobie",
        "rezidenti" => $item["rezidenti"],
        "nerezidenti" => $item["nerezidenti"],
        "spolu" => $item["spolu"]
    ];
}
<?php
include "B_5R_akcie.php";
include "B_6R_dlhopisy.php";
include "B_8R_cpZahranicne.php";
include "B_9R_peniaze.php";
include "B_10R_pohladavky.php";
include "B_11_zavazky.php";

$RSpolu = $B_6R_Dlhopisy[0]["rezidenti"] + $B_5R_akcie[0]["rezidenti"];
$NRSpolu = $B_6R_Dlhopisy[0]["nerezidenti"] + $B_5R_akcie[0]["nerezidenti"];
$Spolu = $B_6R_Dlhopisy[0]["suma"] + $B_5R_akcie[0]["suma"];

$RAll = $RSpolu + $B_8R_CPZahranicne[0]["rezidenti"] + $B_9R_Peniaze[0]["rezidenti"] + $B_10R_Pohladavky[0]["rezidenti"] + $B_11R_Zavazky[0]["rezidenti"];
$NRAll = $NRSpolu + $B_8R_CPZahranicne[0]["nerezidenti"] + $B_9R_Peniaze[0]["nerezidenti"] + $B_10R_Pohladavky[0]["nerezidenti"] + $B_11R_Zavazky[0]["nerezidenti"];
$All = $Spolu + $B_8R_CPZahranicne[0]["suma"] + $B_9R_Peniaze[0]["suma"] + $B_10R_Pohladavky[0]["suma"] + $B_11R_Zavazky[0]["suma"];

$partB['objemMajetekuSpolu'] = [
    "popis" => "Priemerný objem spravovaného majetku v členení na:",
    "rezidenti" => round($RAll / 1000, 0),
    "nerezidenti" => round($NRAll / 1000, 0),
    "spolu" => round($All / 1000, 0)
];

$partB['objemMajetekuSpolu']['predvolitelnecps'] = [
    "popis" => "Predvoliteľné cenné papiere, z toho:",
    "rezidenti" => round($RSpolu / 1000, 0),
    "nerezidenti" => round($NRSpolu / 1000, 0),
    "spolu" => round($Spolu / 1000, 0)
];

foreach ($B_5R_akcie as $item) {
    $partB['objemMajetekuSpolu']['predvolitenlnecp']['akcie'] = [
        "popis" => "Akcie",
        "rezidenti" => round($item["rezidenti"] / 1000, 0),
        "nerezidenti" => round($item["nerezidenti"] / 1000, 0),
        "spolu" => round($item["suma"] / 1000, 0)
    ];
}

foreach ($B_6R_Dlhopisy as $item) {
    $partB['objemMajetekuSpolu']['predvolitenlnecp']['dlhopisy'] = [
        "popis" => "Dlhopisy",
        "rezidenti" => round($item["rezidenti"] / 1000, 0),
        "nerezidenti" => round($item["nerezidenti"] / 1000, 0),
        "spolu" => round($item["spolu"] / 1000, 0)
    ];
}

foreach ($B_8R_CPZahranicne as $item) {
    $partB['objemMajetekuSpolu']['cpZahranicne'] = [
        "popis" => "Cenné papiere vydané zahraničnými subjektami kolektívneho investovania",
        "rezidenti" => round($item["rezidenti"] / 1000, 0),
        "nerezidenti" => round($item["nerezidenti"] / 1000, 0),
        "spolu" => round($item["suma"] / 1000, 0)
    ];
}

foreach ($B_9R_Peniaze as $item) {
    $partB['objemMajetekuSpolu']['peniaze'] = [
        "popis" => "Peniaze",
        "rezidenti" => round($item["rezidenti"] / 1000, 0),
        "nerezidenti" => round($item["nerezidenti"] / 1000, 0),
        "spolu" => round($item["suma"] / 1000, 0)
    ];
}

foreach ($B_10R_Pohladavky as $item) {
    $partB['objemMajetekuSpolu']['pohladavky'] = [
        "popis" => "Pohľadávky",
        "rezidenti" => $item["rezidenti"],
        "nerezidenti" => $item["nerezidenti"],
        "spolu" => $item["suma"]
    ];
}

foreach ($B_11R_Zavazky as $item) {
    $partB['objemMajetekuSpolu']['zavazky'] = [
        "popis" => "Záväzky",
        "rezidenti" => $item["rezidenti"],
        "nerezidenti" => $item["nerezidenti"],
        "spolu" => $item["suma"]
    ];
}
<?php
$query11R = "SELECT SUM(CASE WHEN stateid = 1 THEN ROUND((suma_pomocna / (maxdatum - mindatum + 1)) / 1000, 0) ELSE 0 END) as rezidenti,
       SUM(CASE WHEN stateid > 1 THEN ROUND((suma_pomocna / (maxdatum - mindatum + 1)) / 1000, 0) ELSE 0 END) as ne<PERSON><PERSON><PERSON>,
       SUM(ROUND((suma_pomocna / (maxdatum - mindatum + 1)) / 1000, 0))                                       as suma
from (select sum(COALESCE(
        CASE
            WHEN mt.md_d = 0 THEN nu.md_d0
            ELSE nu.md_d1
            END * mt.sumadenom * f_menovy_kurz.value,
        0))                                                                 as suma_pomocna,
             min(datum)                                                     as mindatum,
             max(datum)                                                     as maxdatum,
             CASE WHEN po.stateid = 1 THEN 1 WHEN po.stateid > 1 THEN 2 END AS stateid
      from majetoktotal mt,
           navuctovanie nu,
           portfolio p,
           fonds f,
           podielnik po
               JOIN LATERAL (
               SELECT f_menovy_kurz_lot(mt.menadenom, 'EUR', mt.datum) AS value
               ) f_menovy_kurz ON true
      WHERE p.fondid = mt.subjektid
        and p.fondid = f.fondid
        and f.typpredlohy in (2, 3)
        and p.podielnikid = po.podielnikid
        and mt.uctovnykod = nu.uctovnykod
        and mt.uctovnykod in
            (
                (select uctovnykod
                 from navuctovanie u
                 where u.uctovnykod::text like '325%'
                    or u.uctovnykod::text like '315%'
                    or u.uctovnykod::text like '261%'))
        and mt.md_d = 1
        and mt.uctovnykod != 315113
        AND mt.datum BETWEEN '2024-01-01' AND '2024-03-31'
      GROUP BY CASE WHEN po.stateid = 1 THEN 1 WHEN po.stateid > 1 THEN 2 END) a";
$queryRes = Connection::getDataFromDatabase($query11R, defaultDB);
$B_11R_Zavazky = $queryRes[1];


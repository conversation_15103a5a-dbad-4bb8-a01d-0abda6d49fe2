<?php
//CAST C - USCHOVA A SPRAVA FINANCNYCH NASTROJOV
$queryD1 = "select round(avg(f.pocet), 0) as priempocet, round(avg(f.objem) / 1000, 0) as priemobjem, f.domicil
from (select count(e.podielnikid) as pocet, sum(e.objem) as objem, e.datum, e.domicil
      from (select d.podielnikid, sum(d.objem) as objem, d.datum, d.domicil
            from (select p.podielnikid,
                         sum(b.suma)                                                      as objem,
                         b.datum,
                         case when po.stateid = 1 then 'Rezidenti' else 'Nerezidenti' end as domicil
                  from (select a.subjektid,
                               sum(round((((0.5 - mt.md_d) / 0.5) * mt.sumadenom) / COALESCE(k.kurz::integer, 1),
                                         2)) as suma,
                               a.datum
                        from (select mt.subjektid, mt.datum, mt.ucetaktiva
                              from majetoktotal mt
                                       inner join fondsbu fb on mt.subjektid = fb.fondid and mt.ucetaktiva = fb.cub
                              where mt.datum >= to_date('$datefrom', 'dd.mm.yyyy')
                                and mt.datum <= to_date('$dateto', 'dd.mm.yyyy')
                                and subjektid > 1
                              group by mt.subjektid, mt.datum, mt.ucetaktiva) a
                                 left join majetoktotal mt on a.subjektid = mt.subjektid and a.datum = mt.datum and
                                                              a.ucetaktiva = mt.ucetaktiva
                                 left join (select *
                                            from kurzyaktivarchiv
                                            where datum >= to_date('$datefrom', 'dd.mm.yyyy')
                                              and datum <= to_date('$dateto', 'dd.mm.yyyy')) k
                                           on 'EUR' || mt.menadenom = k.ric and a.datum = k.datum
                        group by a.subjektid, a.datum) b
                           left join portfolio p on b.subjektid = p.fondid
                           left join podielnik po on p.podielnikid = po.podielnikid
                  group by b.datum, case when po.stateid = 1 then 'Rezidenti' else 'Nerezidenti' end, p.podielnikid
                  union all
                  select c.podielnikid,
                         sum(round(c.suma / COALESCE(k.kurz::integer, 1), 2))                        as objem,
                         c.datum,
                         case when stateid = 1 then 'Rezidenti' else 'Nerezidenti' end as domicil
                  from (select sum(mt.suma) as suma, mt.mena, po.stateid, b.podielnikid, mt.datum
                        from (select aa.podielnikid, aa.cum
                              from (select a.podielnikid,
                                           a.cum,
                                           count(a.podielnikid) over (partition by a.cum) as pocet
                                    from (select p.podielnikid, m.cum
                                          from fondsmu m
                                                   left join portfolio p on m.fondid = p.fondid
                                          group by p.podielnikid, m.cum, m.dest, m.typ_maj_uctu) a) aa
                              where aa.pocet > 1) b
                                 left join portfolio p on b.podielnikid = p.podielnikid
                                 left join (select ucetaktiva,
                                                   (((0.5 - md_d) / 0.5) * sumadenom) as suma,
                                                   menadenom                          as mena,
                                                   subjektid,
                                                   kodaktiva,
                                                   datum
                                            from majetoktotal
                                            where datum >= to_date('$datefrom', 'dd.mm.yyyy')
                                              and datum <= to_date('$dateto', 'dd.mm.yyyy')
                                              and subjektid > 1) mt on b.cum = mt.ucetaktiva and p.fondid = mt.subjektid
                                 left join podielnik po on p.podielnikid = po.podielnikid
                        group by mt.mena, po.stateid, b.podielnikid, mt.datum
                        having sum(mt.suma) <> 0) c
                           left join (select *
                                      from kurzyaktivarchiv
                                      where datum >= to_date('$datefrom', 'dd.mm.yyyy')
                                        and datum <= to_date('$dateto', 'dd.mm.yyyy')) k
                                     on 'EUR' || c.mena = k.ric and c.datum = k.datum
                  group by c.datum, case when stateid = 1 then 'Rezidenti' else 'Nerezidenti' end, c.podielnikid) d
            group by d.podielnikid, d.datum, d.domicil) e
      group by e.datum, e.domicil) f
group by f.domicil
order by f.domicil
";
$queryRes = Connection::getDataFromDatabase($queryD1, defaultDB);
$D_1R = $queryRes[1];

$partC['uschovaAsprava'][0] = [
    "popis" => "Počet klientov",
    "rezidenti" => $D_1R[0]["priempocet"],
    "nerezidenti" => $D_1R[1]["priempocet"],
    "spolu" => $D_1R[0]["priempocet"] + $D_1R[1]["priempocet"]
];
$partC['uschovaAsprava'][1] = [
    "popis" => "Priemerný objem finančných prostriedkov",
    "rezidenti" => $D_1R[0]["priemobjem"],
    "nerezidenti" => $D_1R[1]["priemobjem"],
    "spolu" => $D_1R[0]["priemobjem"] + $D_1R[1]["priemobjem"]
];

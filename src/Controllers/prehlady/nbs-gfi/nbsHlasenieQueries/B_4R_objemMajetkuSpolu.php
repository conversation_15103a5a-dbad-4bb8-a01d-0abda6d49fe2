<?php
$query4R = "
		SELECT SUM(CASE WHEN a.stateid = 1 THEN 1 ELSE 0 END) AS rezidenti,
         SUM(CASE WHEN a.stateid = 2 THEN 1 ELSE 0 END) AS nerezidenti,
         COUNT(a.stateid) as spolu		
		from
			(
			SELECT
				distinct p.c<PERSON>lu<PERSON>,
				CASE WHEN po.stateid = 1 THEN 1 ELSE 2 END AS stateid
    			FROM
				majetoktotal mt,
				portfolio p,
        			fonds f,
				podielnik po
			WHERE	
				p.fondid = mt.subjektid
        			and p.fondid = f.fondid
				and f.typpredlohy in (2,3)
				and p.podielnikid = po.podielnikid
				$lastdate
			) a
	";
$queryRes = Connection::getDataFromDatabase($query4R, defaultDB);
$B_4R_ObjemMajetkuSpolu = $queryRes[1];

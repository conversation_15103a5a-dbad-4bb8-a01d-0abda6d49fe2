<?php
$query9R = "SELECT
    SUM(CASE WHEN stateid = 1 THEN (suma_pomocna / (maxdatum - mindatum + 1)) ELSE 0 END) as rezidenti,
    SUM(CASE WHEN stateid > 1 THEN (suma_pomocna / (maxdatum - mindatum + 1)) ELSE 0 END) as ne<PERSON><PERSON><PERSON>,
    SUM((suma_pomocna / (maxdatum - mindatum + 1))) as suma
FROM (SELECT SUM(
                     COALESCE(
                             CASE
                                 WHEN mt.md_d = 0 THEN nu.md_d0
                                 ELSE nu.md_d1
                                 END * mt.sumadenom / f_menovy_kurz.value,
                             0)) AS suma_pomocna,
             MIN(mt.datum)       AS mindatum,
             MAX(mt.datum)       AS maxdatum,
             CASE WHEN po.stateid = 1 THEN 1 WHEN po.stateid > 1 THEN 2 END AS stateid
      FROM majetoktotal mt
               JOIN
           navuctovanie nu
           ON mt.uctovnykod = nu.uctovnykod
               JOIN
           portfolio p ON p.fondid = mt.subjektid
               JOIN
           fonds f ON p.fondid = f.fondid
               JOIN
           podielnik po ON p.podielnikid = po.podielnikid
               JOIN LATERAL (
          SELECT f_menovy_kurz_lot(mt.menadenom, 'EUR', mt.datum) AS value
          ) f_menovy_kurz ON true
      WHERE po.stateid > 0
        AND mt.uctovnykod IN (221110, 221210, 315113)
        AND mt.datum BETWEEN '2024-01-01'
          AND '2024-03-31'
      GROUP BY CASE WHEN po.stateid = 1 THEN 1 WHEN po.stateid > 1 THEN 2 END) AS calculated_data;

";
$queryRes = Connection::getDataFromDatabase($query9R, defaultDB);
$B_9R_Peniaze = $queryRes[1];

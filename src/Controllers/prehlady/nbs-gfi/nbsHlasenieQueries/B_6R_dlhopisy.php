<?php
$query6R = "	
		WITH calculated_sums AS (
                SELECT SUM(
                        COALESCE(
                            CASE
                                WHEN mt.md_d = 0 THEN nu.md_d0
                                ELSE nu.md_d1
                            END * mt.sumadenom / f_menovy_kurz.value,
                            0
                        )
                   ) AS suma_pomocna,
                   MIN(mt.datum) AS mindatum,
                   MAX(mt.datum) AS maxdatum,
                   CASE WHEN po.stateid = 1 THEN 1 ELSE 2 END AS stateid
                FROM majetoktotal mt
                         JOIN navuctovanie nu ON mt.uctovnykod = nu.uctovnykod
                         JOIN portfolio p ON p.fondid = mt.subjektid
                         JOIN fonds f ON p.fondid = f.fondid
                         JOIN podielnik po ON p.podielnikid = po.podielnikid
                         JOIN LATERAL (
                             SELECT f_menovy_kurz_lot(mt.menadenom, 'EUR', mt.datum) AS value
                             ) f_menovy_kurz ON true
                WHERE f.typpredlohy IN (2, 3)
                  AND mt.uctovnykod IN (251110, 251120)
                  $dateQuery
                  GROUP BY po.stateid
        )
SELECT SUM(CASE WHEN c.stateid = 1 THEN (c.suma_pomocna / (c.maxdatum - c.mindatum + 1)) ELSE 0 END) AS rezidenti,
         SUM(CASE WHEN c.stateid = 2 THEN (c.suma_pomocna / (c.maxdatum - c.mindatum + 1)) ELSE 0 END) AS nerezidenti,
         SUM(c.suma_pomocna / (c.maxdatum - c.mindatum + 1)) AS suma
FROM calculated_sums as c
	";

$queryRes = Connection::getDataFromDatabase($query6R, defaultDB);
$B_6R_Dlhopisy = $queryRes[1];
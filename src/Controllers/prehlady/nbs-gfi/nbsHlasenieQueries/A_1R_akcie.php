<?php
$queryAr = "WITH subquery AS (
    SELECT 
        COALESCE(SUM(rcp.cenaobchodu * f_menovy_kurz.value), 0) AS suma
    FROM 
        konfirmaciacp kcp
    JOIN 
        rekonfirmaciacp rcp ON kcp.dealid = rcp.dealid
    JOIN 
        dbequitycurrric dcr ON dcr.ric = kcp.ric
    JOIN 
        dbequitycurr dc ON dc.isincurr = dcr.isincurr
    JOIN 
        dbequity d ON d.isin = dc.isin
    JOIN 
        portfolio p ON p.fondid = kcp.subjektid
    JOIN 
        fonds f ON p.fondid = f.fondid
    JOIN 
        podielnik po ON p.podielnikid = po.podielnikid
        JOIN LATERAL ( SELECT f_menovy_kurz_lot(kcp.currencyidtrade, 'EUR', kcp.datpok) AS value ) f_menovy_kurz
                                       ON true
    WHERE 
        f.typpredlohy = 4
        AND po.stateid = 1
        AND kcp.logactivityid = 12
        AND kcp.eqid = 'Shares'
        AND kcp.druhobchodu IN ('nakup', 'predaj')
        AND rcp.datvysporiadaniabureal IS NOT NULL
        AND rcp.datvysporiadaniamureal IS NOT NULL
        AND d.druheqid NOT IN (8, 15, 17)
        $dateQuery2
    UNION ALL
    SELECT 
        COALESCE(SUM(rcp.cenaobchodu * f_menovy_kurz.value), 0) AS suma
    FROM 
        konfirmaciacp kcp
    JOIN 
        rekonfirmaciacp rcp ON kcp.dealid = rcp.dealid
    JOIN 
        dbequitycurrric dcr ON dcr.ric = kcp.ric
    JOIN 
        dbequitycurr dc ON dc.isincurr = dcr.isincurr
    JOIN 
        dbequity d ON d.isin = dc.isin
    JOIN 
        pool pol ON kcp.dealid = pol.dealid
    JOIN 
        pooldetailreal pdr ON pol.poolid = pdr.poolid
    JOIN 
        portfolio p ON pdr.subjektid = p.fondid
    JOIN 
        fonds f ON p.fondid = f.fondid
    JOIN 
        podielnik po ON p.podielnikid = po.podielnikid
    JOIN LATERAL ( SELECT f_menovy_kurz_lot(kcp.currencyidtrade, 'EUR', kcp.datpok) AS value ) f_menovy_kurz
                                       ON true
    WHERE 
        kcp.subjektid = 0
        AND f.typpredlohy = 4
        AND po.stateid = 1
        AND kcp.logactivityid = 12
        AND kcp.eqid = 'Shares'
        AND kcp.druhobchodu IN ('nakup', 'predaj')
        AND rcp.datvysporiadaniabureal IS NOT NULL
        AND rcp.datvysporiadaniamureal IS NOT NULL
        AND d.druheqid NOT IN (8, 15, 17)
        $dateQuery2
)
SELECT COALESCE(SUM(suma), 0) AS suma
FROM subquery;
";
$queryRes = Connection::getDataFromDatabase($queryAr, defaultDB);
$A_1R_Akcie = $queryRes[1];
$partA['Akcie']['IS-1'] = [
    "rezidenti" => 0,
    "nerezidenti" => 0,
    "spolu" => 0,
    "burzoveObchody" => 0,
    "mimoburzoveObchody" => 0
];
$partA['Akcie']['IS-2'] = [
    "rezidenti" => 0,
    "nerezidenti" => 0,
    "spolu" => 0,
    "burzoveObchody" => 0,
    "mimoburzoveObchody" => 0
];
$partA['Akcie']['IS-3'] = [
    "rezidenti" => 0,
    "nerezidenti" => 0,
    "spolu" => 0,
    "burzoveObchody" => 0,
    "mimoburzoveObchody" => 0
];

$partA['Dlhopisy']['IS-1'] = [
    "rezidenti" => 0,
    "nerezidenti" => 0,
    "spolu" => 0,
    "burzoveObchody" => 0,
    "mimoburzoveObchody" => 0
];
$partA['Dlhopisy']['IS-2'] = [
    "rezidenti" => 0,
    "nerezidenti" => 0,
    "spolu" => 0,
    "burzoveObchody" => 0,
    "mimoburzoveObchody" => 0
];
$partA['Dlhopisy']['IS-3'] = [
    "rezidenti" => 0,
    "nerezidenti" => 0,
    "spolu" => 0,
    "burzoveObchody" => 0,
    "mimoburzoveObchody" => 0
];

$partA['Cenné papiere vydané zahraničnými subjektami kolektívneho investovania']['IS-1'] = [
    "rezidenti" => 0,
    "nerezidenti" => 0,
    "spolu" => 0,
    "burzoveObchody" => 0,
    "mimoburzoveObchody" => 0
];
$partA['Cenné papiere vydané zahraničnými subjektami kolektívneho investovania']['IS-2'] = [
    "rezidenti" => 0,
    "nerezidenti" => 0,
    "spolu" => 0,
    "burzoveObchody" => 0,
    "mimoburzoveObchody" => 0
];
$partA['Cenné papiere vydané zahraničnými subjektami kolektívneho investovania']['IS-3'] = [
    "rezidenti" => 0,
    "nerezidenti" => 0,
    "spolu" => 0,
    "burzoveObchody" => 0,
    "mimoburzoveObchody" => 0
];

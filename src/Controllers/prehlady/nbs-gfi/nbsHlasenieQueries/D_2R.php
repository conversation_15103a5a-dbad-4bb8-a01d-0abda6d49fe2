<?php
// CAST C - DRZITELSKA SPRAVA FINANCNYCH NASTROJOV
$queryDHPP = "
		select COALESCE(CASE WHEN re = 2 THEN COUNT(DISTINCT ZMLUVA) END, 0) as poc<PERSON><PERSON><PERSON><PERSON><PERSON>,
       COALESCE(CASE WHEN re = 1 THEN COUNT(DISTINCT ZMLUVA) END, 0) as poc<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
       COALESCE(CASE WHEN re = 2 THEN SUM(SUMA) END, 0)              as ob<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
       COALESCE(CASE WHEN re = 1 THEN SUM(SUMA) END, 0)              as objem<PERSON>ravnicke
from (SELECT po.podielnikid                                               AS ZMLUVA,
             k.sumadebet * f_menovy_kurz_lot(k.menadebet, 'EUR', k.dat_realizacia) AS SUMA,
             CASE WHEN po.fpo in (0, 2) THEN 2 WHEN po.fpo = 1 THEN 1 END as re
      FROM KONVERZIA k,
           PORTFOLIO p,
           podielnik po
      WHERE k.subjektid = p.fondid
        AND p.podielnikid = po.podielnikid
        AND k.logactivityid = 18
        and k.dat_realizacia >= to_date('$datefrom', 'dd.mm.yyyy')
        and k.dat_realizacia <= to_date('$dateto', 'dd.mm.yyyy')
      UNION ALL
      SELECT po.podielnikid                                               AS ZMLUVA,
             pdr.transsumareal * f_menovy_kurz_lot(k.menadebet, 'EUR', k.dat_realizacia) AS SUMA,
             CASE WHEN po.fpo in (0, 2) THEN 2 WHEN po.fpo = 1 THEN 1 END as re
      FROM KONVERZIA k,
           PORTFOLIO p,
           podielnik po,
           pool pol,
           pooldetailreal pdr
      WHERE k.subjektid = 0
        AND k.dealid = pol.dealid
        AND pol.destinacia = 'konverzia'
        AND pol.poolid = pdr.poolid
        AND pdr.subjektid = p.fondid
        AND p.podielnikid = po.podielnikid
        AND k.logactivityid = 18
        and k.dat_realizacia >= to_date('$datefrom', 'dd.mm.yyyy')
        and k.dat_realizacia <= to_date('$dateto', 'dd.mm.yyyy')) a
GROUP BY re";

$queryRes = Connection::getDataFromDatabase($queryDHPP, defaultDB);
$D_2R = $queryRes[1];

$partC['obchodyDevizove'][0] = [
    "popis" => "Počet klientov",
    "rezidenti" => $D_2R[0]["pocetfyzicke"],
    "nerezidenti" => $D_2R[1]["pocetpravnicke"] === 0 ? 0 : $D_2R[1]["pocetpravnicke"],
    "spolu" => $D_2R[0]["pocetfyzicke"] + $D_2R[1]["pocetpravnicke"]
];
$partC['obchodyDevizove'][1] = [
    "popis" => "Priemerný objem finančných prostriedkov",
    "rezidenti" => $D_2R[0]["objemfyzicke"],
    "nerezidenti" => $D_2R[1]["objempravnicke"],
    "spolu" => $D_2R[0]["objemfyzicke"] + $D_2R[1]["objempravnicke"]
];
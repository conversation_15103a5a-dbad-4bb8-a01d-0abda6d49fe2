<?php
//CAST B - POCET ZMLUV SPOLU
$query1R = "
		SELECT SUM(CASE WHEN a.stateid = 1 THEN 1 ELSE 0 END) AS rezidenti,
                SUM(CASE WHEN a.stateid = 2 THEN 1 ELSE 0 END) AS nerezidenti,
                COUNT(a.stateid) as spolu
		FROM (
			SELECT
				distinct p.cislozmluvy,
			    CASE WHEN po.stateid = 1 THEN 1 ELSE 2 END as stateid 
    			FROM
				majetoktotal mt,
				portfolio p,
        			fonds f,
				podielnik po
			WHERE	
				p.fondid = mt.subjektid
        			and p.fondid = f.fondid
				and f.typpredlohy in (2,3)
				and p.podielnikid = po.podielnikid
				$lastdate2
        ) a
	";
$queryRes = Connection::getDataFromDatabase($query1R, defaultDB);
$B_1R_pocetSpolu = $queryRes[1];
foreach ($B_1R_pocetSpolu as $item) {
    $partB['pocetSpolu'] = [
        "popis" => "Počet zmlúv spolu",
        "rezidenti" => $item["rezidenti"],
        "nerezidenti" => $item["nerezidenti"],
        "spolu" => $item["spolu"]
    ];
}
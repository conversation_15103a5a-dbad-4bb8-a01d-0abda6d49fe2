<?php
$query3R="
	    SELECT SUM(CASE WHEN a.stateid = 1 THEN 1 ELSE 0 END) AS rezidenti,
         SUM(CASE WHEN a.stateid = 2 THEN 1 ELSE 0 END) AS nerezidenti,
         COUNT(a.stateid) as spolu		
		from
			(
			SELECT
				distinct p.cislozmluvy,
				CASE WHEN po.stateid = 1 THEN 1 ELSE 2 END AS stateid
    			FROM
				majetoktotal mt,
				portfolio p,
        			fonds f,
				podielnik po
			WHERE	
				p.fondid = mt.subjektid
        			and p.fondid = f.fondid
				and f.typpredlohy in (2,3)
				and p.podielnikid = po.podielnikid
				$lastdate
				and p.cislozmluvy not in 
				(
					SELECT
						distinct p.cislozmluvy	
    					FROM
						majetoktotal mt,
						portfolio p,
        					fonds f,
						podielnik po
					WHERE	
						p.fondid = mt.subjektid
        					and p.fondid = f.fondid
						and f.typpredlohy in (2,3)
						and p.podielnikid = po.podielnikid
						$lastdate2
				)
			) a
	";
$queryRes = Connection::getDataFromDatabase($query3R, defaultDB);
$B_3R_PocetVypovedanych = $queryRes[1];
foreach ($B_3R_PocetVypovedanych as $item) {
    $partB['pocetVypovedanychZmluv'] = [
        "popis" => "Počet zmlúv vypovedaných za sledované obdobie",
        "rezidenti" => $item["rezidenti"],
        "nerezidenti" => $item["nerezidenti"],
        "spolu" => $item["spolu"]
    ];
}
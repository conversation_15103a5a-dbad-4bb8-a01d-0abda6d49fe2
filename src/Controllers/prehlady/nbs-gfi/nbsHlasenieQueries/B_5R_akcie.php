<?php
$query5R = "	
		WITH calculated_sums AS (SELECT SUM(
                                        COALESCE(
                                                CASE
                                                    WHEN mt.md_d = 0 THEN nu.md_d0
                                                    ELSE nu.md_d1
                                                    END * mt.sumadenom /
                                                f_menovy_kurz.value,
                                                0
                                        )
                                )             AS suma_pomocna,
                                MIN(mt.datum) AS mindatum,
                                MAX(mt.datum) AS maxdatum,
                                CASE WHEN po.stateid = 1 THEN 1 ELSE 2 END AS stateid
                         FROM majetoktotal mt
                                  JOIN
                              navuctovanie nu ON mt.uctovnykod = nu.uctovnykod
                                  LEFT JOIN
                              dbequitycurrric dcr ON dcr.isincurrric = mt.kodaktiva
                                  LEFT JOIN
                              dbequitycurr dc ON dc.isincurr = dcr.isincurr
                                  LEFT JOIN
                              dbequity d ON d.isin = dc.isin
                                  JOIN
                              portfolio p ON p.fondid = mt.subjektid
                                  JOIN
                              fonds f ON p.fondid = f.fondid
                                  JOIN
                              podielnik po ON p.podielnikid = po.podielnikid
                                  JOIN LATERAL (
                             SELECT f_menovy_kurz_lot(mt.menadenom, 'EUR', mt.datum) AS value
                             ) f_menovy_kurz ON true
                         WHERE f.typpredlohy IN (2, 3)
                           AND mt.uctovnykod IN (251200)
                           AND d.druheqid NOT IN (8, 15, 17)
                           $dateQuery
                         GROUP BY po.stateid
                         )
SELECT SUM(CASE WHEN c.stateid = 1 THEN (c.suma_pomocna / (c.maxdatum - c.mindatum + 1)) ELSE 0 END) AS rezidenti,
         SUM(CASE WHEN c.stateid = 2 THEN (c.suma_pomocna / (c.maxdatum - c.mindatum + 1)) ELSE 0 END) AS nerezidenti,
         (c.suma_pomocna / (c.maxdatum - c.mindatum + 1)) AS suma
FROM calculated_sums as c
GROUP BY c.suma_pomocna, c.maxdatum, c.mindatum;
	";

$queryRes = Connection::getDataFromDatabase($query5R, defaultDB);
$B_5R_akcie = $queryRes[1];
<?php

require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

$query = $_POST['query'];
$mena = $_POST['mena'];
$date = $_POST["date"];
$spravca = $_POST["spravca"] === "true" ? 1 : "p.fondid";
$summary = $_POST["summary"] === "true" ? true : false;
function buildFilterQuery($query, $mena, $date, $spravca): string
{
  $table = $date !== "" ? "majetoktotal" : "majetoktoday";
  $dateQuery = $date !== "" ? "mt.datum" : "((SELECT MAX(datum) FROM majetoktotal) + INTERVAL '1 day')";
  $menaColumn = $date !== "" ? "mt.menadenom" : "mt.mena";
  if ($query !== "") {
    $queryBase = "SELECT
                mt.ucetaktiva ,
                $menaColumn AS mena,
                SUM(SIGN(md_d - 0.5) * (-1) * mt.pocet) AS pocet,
                d.isinreal,
                      d.cpnaz,
                COALESCE(d.nominalemisie, 1) as nominal,
                'akcia' as typ 
              FROM
                $table mt, dbequity d, dbequitycurr dcur, dbequitycurrric dcuric, portfolio p";
    $queryBase .= " WHERE
                (d.isin LIKE '%$query%' OR d.cpnaz LIKE '%$query%')";
    if ($date !== "") {
      $queryBase .= " and mt.datum = TO_DATE('$date', 'YYYY-MM-DD') ";
    }
    $queryBase .= "
                AND mt.uctovnykod in (251200, 251300)
                AND mt.kodaktiva = dcuric.isincurrric
                      and dcuric.isincurr = dcur.isincurr
                      and dcur.isin = d.isin
                      and mt.subjektid = $spravca ";
    if ($mena != "") {
      $queryBase .= " AND $menaColumn = '$mena'";
    }
    $queryBase .= "GROUP BY
                $menaColumn, mt.ucetaktiva, d.isinreal, d.cpnaz, COALESCE(d.nominalemisie, 1)
              Union all
              SELECT
                mt.ucetaktiva ,
                $menaColumn AS mena,
                SUM(SIGN(md_d - 0.5) * (-1) * mt.pocet) AS pocet,
                d.isinreal,
                      d.cpnaz,
                COALESCE((SELECT faktor FROM floatkupon WHERE isincurrric = mt.kodaktiva AND datefrom >= " . $dateQuery . " AND datetill <= " . $dateQuery . "), 1) * COALESCE(d.nominalemisie, 1) as nominal,
                'dlhopis' as typ
              FROM
                $table mt, dbequity d, dbequitycurr dcur, dbequitycurrric dcuric, portfolio p
              WHERE
              (d.isin LIKE '%$query%' OR d.cpnaz LIKE '%$query%') ";
    if ($date !== "") {
      $queryBase .= " and mt.datum = TO_DATE('$date', 'YYYY-MM-DD') ";
    }
    $queryBase .= "
        and mt.uctovnykod = 251110
        and mt.kodaktiva = dcuric.isincurrric
        and dcuric.isincurr = dcur.isincurr
        and dcur.isin = d.isin
        and mt.subjektid = $spravca ";
    if ($mena != "") {
      $queryBase .= " and $menaColumn = '$mena'";
    }
    $queryBase .= "GROUP BY
                $menaColumn, mt.ucetaktiva, d.isinreal, d.cpnaz, COALESCE((SELECT faktor FROM floatkupon WHERE isincurrric = mt.kodaktiva and datefrom >= $dateQuery and datetill <= $dateQuery), 1) * COALESCE(d.nominalemisie, 1)
              ORDER BY
                ucetaktiva, isinreal";

  } else {
    $queryBase = "SELECT
              mt.ucetaktiva ,
              $menaColumn as mena,
            SUM(SIGN(md_d - 0.5) * (-1) * mt.pocet) as pocet,
              d.isinreal,
                    d.cpnaz,
              COALESCE(d.nominalemisie, 1) as nominal,
              'akcia' as typ
              
            FROM
              $table mt, dbequity d, dbequitycurr dcur, dbequitycurrric dcuric, portfolio p
            WHERE ";
    if ($date !== "") {
      $queryBase .= " mt.datum = TO_DATE('$date', 'YYYY-MM-DD') AND ";
    }
    $queryBase .= " mt.uctovnykod in(251200, 251300)
        and mt.kodaktiva = dcuric.isincurrric
        and dcuric.isincurr = dcur.isincurr
        and dcur.isin = d.isin
        and mt.subjektid = $spravca";
    if ($mena != "") {
      $queryBase .= " and $menaColumn = '$mena'";
    }
    $queryBase .= " GROUP BY
              $menaColumn, mt.ucetaktiva, d.isinreal, d.cpnaz, COALESCE(d.nominalemisie, 1)
            Union all
            SELECT
              mt.ucetaktiva ,
              $menaColumn as mena,
              SUM(SIGN(md_d - 0.5) * (-1) * mt.pocet) as pocet,
              d.isinreal,
                    d.cpnaz,
              COALESCE((SELECT faktor FROM floatkupon WHERE isincurrric = mt.kodaktiva and datefrom >= $dateQuery and datetill <= $dateQuery), 1) * COALESCE(d.nominalemisie, 1) as nominal,
              'dlhopis' as typ
            FROM
              $table mt, dbequity d, dbequitycurr dcur, dbequitycurrric dcuric, portfolio p
            WHERE ";
    if ($date !== "") {
      $queryBase .= " mt.datum = TO_DATE('$date', 'YYYY-MM-DD') AND ";
    }
    $queryBase .= "mt.uctovnykod = 251110
              and mt.kodaktiva = dcuric.isincurrric
              and dcuric.isincurr = dcur.isincurr
              and dcur.isin = d.isin
              and mt.subjektid = $spravca";
    if ($mena != "") {
      $queryBase .= " and $menaColumn = '$mena'";
    }
    $queryBase .= " GROUP BY
              $menaColumn, mt.ucetaktiva, d.isinreal, d.cpnaz, COALESCE((SELECT faktor FROM floatkupon WHERE isincurrric = mt.kodaktiva and datefrom >= $dateQuery and datetill <= $dateQuery), 1) * COALESCE(d.nominalemisie, 1)
            ORDER BY
              ucetaktiva, isinreal";
  }
  return $queryBase;
}
$rows = Connection::getDataFromDatabase(buildFilterQuery($query, $mena, $date, $spravca), defaultDB)[1];
$detail = [];
$sums = [];

foreach ($rows as $record) {
  $detail[$record["ucetaktiva"]][$record["isinreal"]][$record["mena"]]["pocet"] = $record["pocet"];
  $detail[$record["ucetaktiva"]][$record["isinreal"]][$record["mena"]]["nominal"] = $record["nominal"];
  $detail[$record["ucetaktiva"]][$record["isinreal"]][$record["mena"]]["isinreal"] = $record["isinreal"];
  $detail[$record["ucetaktiva"]][$record["isinreal"]][$record["mena"]]["cpnaz"] = $record["cpnaz"];
  $detail[$record["ucetaktiva"]][$record["isinreal"]][$record["mena"]]["mena"] = $record["mena"];
  $detail[$record["ucetaktiva"]][$record["isinreal"]][$record["mena"]]["typ"] = $record["typ"];


  $sums[$record["ucetaktiva"]]["pocet"] += $record["pocet"];
}

foreach ($sums as $ucetaktiva => $sum_values) { ?>
  <tr class="font-bold border-b dark:text-gray-200 bg-gray-200 dark:bg-gray-900" style="border-color: black;">
    <td class="p-2 text-left"><?php echo $ucetaktiva ?></td>
    <td class="vlavo"><?php echo $sum_values["mena"] ?></td>
    <td>&nbsp;</td>
    <td class="vpravo"><?php echo number_format((float) $sum_values["pocet"], 2, '.', ' '); ?></td>
    <td>&nbsp;</td>
    <td>&nbsp;</td>
    <td>&nbsp;</td>
  </tr>
  <?php
  if ($summary) {
    continue;
  }
  foreach ($detail[$ucetaktiva] as $fondid => $detail_values) { ?>
    <tr hx-get="/prehlady/ucty/majetkovy-ucet/<?php echo $fondid; ?>" hx-target="#thisisit" hx-replace-url="true"
      class="border-b text-black dark:text-gray-200 hover:bg-gray-300 dark:hover:bg-gray-600 cursor-pointer transition-all">
      <td class="pl-10 py-3 text-left"><?php echo array_values($detail_values)[0]['cpnaz']; ?>
      </td>
      <td class="vlavo"><a href="/prehlady/ucty/majetkovy-ucet/<?php echo $fondid; ?>"><?php echo $fondid; ?></a>
      </td>
      <td class="vpravo"><?php echo array_values($detail_values)[0]['mena']; ?></td>
      <td class="vpravo"><?php echo array_values($detail_values)[0]['pocet']; ?></td>
      <td class="vpravo"><?php echo array_values($detail_values)[0]['nominal']; ?></td>
      <td class="vpravo">
        <?php echo number_format(intval(array_values($detail_values)[0]["pocet"] * intval(array_values($detail_values)[0]["nominal"])), 2, '.', ' '); ?>
      </td>
    </tr>
  <?php } ?>
<?php }
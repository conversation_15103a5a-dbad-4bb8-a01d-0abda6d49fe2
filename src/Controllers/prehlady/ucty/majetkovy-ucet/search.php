<?php

require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

$query = $_GET['query'];
$isArchived = $_GET['isArchived'];

function buildFilterQuery($query, $isArchived): string
{
    if ($query !== "") {
        $queryBase = "SELECT mt.* FROM majetoktotal mt INNER JOIN dbequitycurrric drc ON s.kodaktiva = drc.isincurrric
                                INNER JOIN dbequitycurr dc ON drc.isincurr = dc.isincurr
                                INNER JOIN dbequity e ON dc.isin = e.isin WHERE (SIMILARITY(e.isin,'%$query%') > 0.3)";
    } else {
        $queryBase = "
        SELECT
          mt.ucetaktiva ,
          mt.menadenom AS mena,
          SUM(SIGN(md_d - 0.5) * (-1) * mt.pocet) AS pocet,
          d.isin<PERSON>,
                d.c<PERSON><PERSON><PERSON>,
          COALESCE(d.nominalemisie, 1) as nominal,
          'akcia' as typ
          
        FROM
          majetoktotal mt, dbequity d, dbequitycurr dcur, dbequitycurrric dcuric, portfolio p
        WHERE
          mt.datum = TO_DATE('2023-12-31','YYYY-MM-DD')
          AND mt.uctovnykod in (251200, 251300)
          AND mt.kodaktiva = dcuric.isincurrric
                and dcuric.isincurr = dcur.isincurr
                and dcur.isin = d.isin
                and mt.subjektid = p.fondid
            
        GROUP BY
          mt.menadenom, mt.ucetaktiva, d.isinreal, d.cpnaz, COALESCE(d.nominalemisie, 1)
        Union all
        SELECT
          mt.ucetaktiva ,
          mt.menadenom AS mena,
          SUM(SIGN(md_d - 0.5) * (-1) * mt.pocet) AS pocet,
          d.isinreal,
                d.cpnaz,
          COALESCE((SELECT faktor FROM floatkupon WHERE isincurrric = mt.kodaktiva AND datefrom >= mt.datum AND datetill <= mt.datum), 1) * COALESCE(d.nominalemisie, 1) as nominal,
          'dlhopis' as typ
        FROM
          majetoktotal mt, dbequity d, dbequitycurr dcur, dbequitycurrric dcuric, portfolio p
        WHERE
          mt.datum = TO_DATE('2023-12-31','YYYY-MM-DD')
          AND mt.uctovnykod = 251110
          AND mt.kodaktiva = dcuric.isincurrric
                and dcuric.isincurr = dcur.isincurr
                and dcur.isin = d.isin
                and mt.subjektid = p.fondid
        GROUP BY
          mt.menadenom, mt.ucetaktiva, d.isinreal, d.cpnaz, COALESCE((SELECT faktor FROM floatkupon WHERE isincurrric = mt.kodaktiva AND datefrom >= mt.datum AND datetill <= mt.datum), 1) * COALESCE(d.nominalemisie, 1)
        ORDER BY
          ucetaktiva, isinreal
      ";
    }
    $queryBase .= " ORDER BY e.isin ASC";
    return $queryBase;
}

$rows = Connection::getDataFromDatabase(buildFilterQuery($query, $isArchived), defaultDB);

header('Content-Type: application/json');
if ($rows[0] === 0) {
    echo json_encode(["attention" => "Nenašli sa žiadny klienti podľa vaších filtrovacích kritérií...", "query" => buildFilterQuery($query, $isArchived)], JSON_UNESCAPED_UNICODE);
} else {
    echo json_encode($rows[1]);
}

<?php

require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

$query = $_POST['query'];
$queryArr = [];
if ($query != "") {
    if (preg_match('~[0-9]+~', $query)) {
        $queryArr = array_map('intval', explode(", ", $query));
    } else {
        $queryArr = [ucfirst($query)];
    }
}
function array_contains_numbers($array)
{
    $filtered = array_filter($array, 'is_numeric');
    return !empty($filtered);
}

$mena = $_POST['mena'];
$summary = $_POST["summary"] === "true" ? true : false;
$date = $_POST["date"];
$spravca = $_POST["spravca"] === "true" ? "'1'" : "p.fondid ";

function buildFilterQuery($query, $mena, $date, $spravca): string
{
    if (!empty($query)) {
        if ($date != "") {
            $queryBase = "SELECT
              mt.ucetaktiva ,
              mt.menadenom AS mena,
              SUM(SIGN(md_d - 0.5) * (-1) * mt.pocet) AS pocet,
              p.fondid,
              p.cislozmluvy,
            po.meno as meno,
            po.prieznaz as priezvisko,
            po.podielnikid as podielnikid
            FROM
                majetoktotal mt
            JOIN portfolio p ON mt.subjektid = p.fondid
            JOIN podielnik po ON p.podielnikid = po.podielnikid";
            if (array_contains_numbers($query)) {
                $queryBase .= " WHERE (p.cislozmluvy LIKE ";
                foreach ($query as $key => $value) {
                    if (($key + 1) === count($query)) {
                        $queryBase .= "'%$value%')";
                    } else {
                        $queryBase .= "'%$value%' OR p.cislozmluvy LIKE ";
                    }
                }
            } else {
                $queryBase .= " WHERE po.prieznaz LIKE '%$query[0]%'";
            }
            if ($mena != "") {
                $queryBase .= " AND mt.menadenom = '$mena'";
            }
            $queryBase .= " AND mt.subjektid = $spravca";
            if ($date != "") {
                $queryBase .= " AND mt.datum = TO_DATE('$date', 'YYYY-MM-DD')";
            }
            $queryBase .= " AND mt.uctovnykod = 221110 ";
            $queryBase .= "GROUP BY
              mt.kodaktiva, p.fondid, mt.ucetaktiva, p.cislozmluvy, mt.menadenom, po.meno, po.prieznaz, po.podielnikid
            ORDER BY
              p.cislozmluvy
      ";
        } else {
            $queryBase = "SELECT
            mt.ucetaktiva,
            mt.mena AS mena,
            SUM(SIGN(md_d - 0.5) * (-1) * mt.pocet) AS pocet,
            p.fondid,
            p.cislozmluvy,
            po.meno as meno,
            po.prieznaz as priezvisko,
            po.podielnikid as podielnikid
        FROM
            majetoktoday mt
        JOIN portfolio p ON mt.subjektid = p.fondid
        JOIN podielnik po ON p.podielnikid = po.podielnikid";
            if (array_contains_numbers($query)) {
                $queryBase .= " WHERE (p.cislozmluvy LIKE ";
                foreach ($query as $key => $value) {
                    if (($key + 1) === count($query)) {
                        $queryBase .= "'%$value%')";
                    } else {
                        $queryBase .= "'%$value%' OR p.cislozmluvy LIKE ";
                    }
                }
            } else {
                $queryBase .= " WHERE po.prieznaz LIKE '%$query[0]%'";
            }
            if ($mena != "") {
                $queryBase .= " AND mena = '$mena'";
            }
            $queryBase .= " AND mt.subjektid = $spravca";
            $queryBase .= " AND mt.uctovnykod = 221110 GROUP BY
            mt.kodaktiva,
            p.fondid,
            mt.ucetaktiva,
            p.cislozmluvy,
            mt.mena,
            po.meno, po.prieznaz, po.podielnikid
        ORDER BY
            p.cislozmluvy;";
        }
    } else {
        if ($date != "") {
            $queryBase = "SELECT
              mt.ucetaktiva ,
              mt.menadenom AS mena,
              SUM(SIGN(md_d - 0.5) * (-1) * mt.pocet) AS pocet,
              p.fondid,
              p.cislozmluvy,
            po.meno as meno,
            po.prieznaz as priezvisko,
            po.podielnikid as podielnikid
            FROM
                majetoktotal mt
            JOIN portfolio p ON mt.subjektid = p.fondid
            JOIN podielnik po ON p.podielnikid = po.podielnikid
            WHERE 1=1 ";
            if ($mena != "") {
                $queryBase .= " AND mt.menadenom = '$mena'";
            }
            $queryBase .= " AND mt.subjektid = $spravca";
            if ($date != "") {
                $queryBase .= " AND mt.datum = TO_DATE('$date', 'YYYY-MM-DD')";
            }
            $queryBase .= " AND mt.uctovnykod = 221110 ";
            $queryBase .= "GROUP BY
              mt.kodaktiva, p.fondid, mt.ucetaktiva, p.cislozmluvy, mt.menadenom, po.meno, po.prieznaz, po.podielnikid
            ORDER BY
              p.cislozmluvy";
        } else {
            $queryBase = "SELECT
              mt.ucetaktiva,
              mt.mena AS mena,
              SUM(SIGN(md_d - 0.5) * (-1) * mt.pocet) AS pocet,
              p.fondid,
              p.cislozmluvy,
            po.meno as meno,
            po.prieznaz as priezvisko,
            po.podielnikid as podielnikid
        FROM
          majetoktoday mt
        JOIN portfolio p ON mt.subjektid = p.fondid
        JOIN podielnik po ON p.podielnikid = po.podielnikid
        WHERE
          mt.uctovnykod = 221110 ";
            if ($mena != "") {
                $queryBase .= " AND mt.mena = '$mena'";
            }
            $queryBase .= " AND mt.subjektid = $spravca";
            if ($date != "") {
                $queryBase .= " AND mt.datum = TO_DATE('$date', 'YYYY-MM-DD')";
            }
            $queryBase .= "GROUP BY
          mt.kodaktiva, p.fondid, mt.ucetaktiva, p.cislozmluvy,mt.mena, po.meno, po.prieznaz, po.podielnikid
        ORDER BY
          p.cislozmluvy ";
        }
    }
    return $queryBase;
}


if ($summary) {
    if ($date == "") {
        $query = "SELECT ucetaktiva,sum(pocet*((0.5-md_d)/0.5)) as objem,mena,eqid from majetoktoday
        where eqid in ('BU','TD') and subjektid>1 and uctovnykod in (221110)
        group by ucetaktiva,mena,eqid;";
    } else {
        $query = "SELECT ucetaktiva,sum(pocet*((0.5-md_d)/0.5)) as objem,menadenom,eqid from majetoktotal
        where datum='$date' and eqid in ('BU','TD') and subjektid>1 and uctovnykod in (221110)
        group by ucetaktiva,menadenom,eqid;";
    }

    $summaryRows = Connection::getDataFromDatabase($query, defaultDB)[1];
    foreach ($summaryRows as $item) { ?>
        <tr class="font-bold border-b bg-gray-200 text-gray-800 dark:bg-gray-900 dark:text-gray-100"
            style="border-color: black;">
            <td class="p-2 text-left"><?php echo $item["ucetaktiva"]; ?></td>
            <td>&nbsp;</td>
            <td class="vlavo"><?php echo $item["menadenom"] ?></td>
            <td class="vpravo">
                <?php echo number_format((float) $item["objem"], 2, '.', ' '); ?>
            </td>
            <td>&nbsp;</td>
        </tr>
        <?php
    }
} else {
    $detail = [];
    $sums = [];

    $rows = Connection::getDataFromDatabase(buildFilterQuery($queryArr, $mena, $date, $spravca), defaultDB)[1];

    if ($date == "") {
        $query = "SELECT ucetaktiva,sum(pocet*((0.5-md_d)/0.5)) as objem,mena,eqid from majetoktoday
        where eqid in ('BU','TD') and subjektid>1 and uctovnykod in (221110)
        group by ucetaktiva,mena,eqid;";
    } else {
        $query = "SELECT ucetaktiva,sum(pocet*((0.5-md_d)/0.5)) as objem,menadenom,eqid from majetoktotal
        where datum='$date' and eqid in ('BU','TD') and subjektid>1 and uctovnykod in (221110)
        group by ucetaktiva,menadenom,eqid;";
    }

    $summaryRows = Connection::getDataFromDatabase($query, defaultDB)[1];

    foreach ($rows as $record) {
        $ucetaktiva = $record["ucetaktiva"];
        $fondid = $record["fondid"];
        $pocet = $record["pocet"];
        $mena = $record["mena"];
        $cislozmluvy = $record["cislozmluvy"];

        // Process detail array
        if (!isset($detail[$ucetaktiva][$fondid])) {
            $detail[$ucetaktiva][$fondid] = [
                "pocet" => 0,
                "mena" => $mena,
                "cislozmluvy" => $cislozmluvy,
            ];
        }
        $detail[$ucetaktiva][$fondid]["pocet"] += $pocet;
        $detail[$ucetaktiva][$fondid]["mena"] = $mena;
        $detail[$ucetaktiva][$fondid]["cislozmluvy"] = $cislozmluvy;
        $detail[$ucetaktiva][$fondid]["meno"] = $record["meno"] . " " . $record["priezvisko"];
        $detail[$ucetaktiva][$fondid]["id"] = $record["podielnikid"];
    }

    foreach ($summaryRows as $item) {
        $ucetaktiva = $item["ucetaktiva"];
        $mena = $item["menadenom"];
        $objem = $item["objem"];
        ?>
        <tr class="font-bold border-b bg-gray-200 text-gray-800 dark:bg-gray-900 dark:text-gray-100"
            style="border-color: black;">
            <td class="p-2 text-left"><?php echo $ucetaktiva ?></td>
            <td>&nbsp;</td>
            <td class="vlavo"><?php echo $mena ?></td>
            <td class="vpravo">
                <?php echo number_format((float) $objem, 2, '.', ' '); ?>
            </td>
            <td>&nbsp;</td>
        </tr>
        <?php
        foreach ($detail[$ucetaktiva] as $fondid => $detail_values) {
            $cislozmluvy = $detail_values["cislozmluvy"];
            $id_fond = $fondid;
            $clientID = $detail_values["id"];
            $clientName = $detail_values["meno"];
            $clientPrieznaz = $detail_values["prieznaz"];
            $podiel = round(floatval($objem), 2) != 0 ? round(floatval($detail_values["pocet"]), 2) / round(floatval($objem), 2) * 100 : 0;
            ?>
            <tr class="border-b dark:text-gray-200 dark:hover:bg-gray-600 transition-all hover:bg-gray-100">
                <td class="pl-10 py-3 text-left font-bold">
                    <span id="client" class="underline hover:no-underline cursor-pointer"
                        onclick="runGoToPortfolio(event, '<?php echo $cislozmluvy; ?>', '<?php echo $id_fond; ?>', '<?php echo $clientID; ?>', '<?php echo $clientName; ?>', '<?php echo $clientPrieznaz; ?>', true)"><?php echo $detail_values["cislozmluvy"]; ?></span>
                </td>
                <td class="vlavo"><a class="underline hover:no-underline"
                        href="/klienti/detail/<?php echo $detail_values["id"]; ?>"><?php echo $detail_values["meno"]; ?>
                </td>
                <td class="vlavo"><?php echo $detail_values["mena"]; ?></td>
                <td class="vpravo"><?php echo number_format($detail_values["pocet"], 2, '.', ' '); ?></td>
                <td class="vpravo">
                    <?php echo number_format($podiel, 2); ?>
                    %
                </td>
            </tr>
        <?php }
    }
}
<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

$query = $_POST['query'];
$queryArr = [];

if ($query != "") {
	if (preg_match('~[0-9]+~', $query)) {
		$queryArr = array_map('intval', explode(", ", $query));
	} else {
		$queryArr = [ucfirst($query)];
	}
}

function array_contains_numbers($array)
{
	$filtered = array_filter($array, 'is_numeric');
	return !empty($filtered);
}

$mena = $_POST['mena'];
$date = $_POST["date"];
$spravca = $_POST["spravca"];
$summary = $_POST["summary"] === "true" ? true : false;


function buildFilterQuery($query, $mena, $date, $spravca): string
{
	if ($spravca == "true") {
		$queryBase = "SELECT
				r.ucetaktiva as ucetaktiva,
				r.mena as mena,
				<PERSON><PERSON>(r.<PERSON>) as <PERSON><PERSON>,
				<PERSON><PERSON>(r.uroky) as uroky,
				r.fondid as fondid,
				r.cislozmluvy as cislozmluvy,
				r.partner as partner
			FROM	(
				SELECT
 					mt.ucetaktiva AS ucetaktiva,
					mt.kodaktiva AS mena,
					(SIGN(md_d - 0.5) * (-1) * mt.pocet) AS KTV,
					0 AS uroky,
					1 as fondid,
					1 as cislozmluvy,
					k.partnerid as partner
				FROM
					majetoktotal mt, konfirmaciaktv k
				WHERE
					mt.datum = TO_DATE('$date','YYYY-MM-DD')
					AND mt.uctovnykod = 221210
					AND mt.subjektid = 1
					AND mt.ucetaktiva = k.cutd
				UNION ALL
				SELECT
 					mt.ucetaktiva AS ucetaktiva,
					mt.kodaktiva AS mena,
					0 AS KTV,
					(SIGN(md_d - 0.5) * (-1) * mt.pocet) AS uroky,
					1 as fondid,
					1 as cislozmluvy,
					k.partnerid as partner
				FROM
					majetoktotal mt, konfirmaciaktv k
				WHERE
					mt.datum = TO_DATE('$date','YYYY-MM-DD')
					AND mt.uctovnykod = 315113
					AND mt.subjektid = 1
					AND mt.ucetaktiva = k.cutd
				)r
		LEFT JOIN portfolio p2 ON r.fondid = p2.fondid
		LEFT JOIN podielnik o ON p2.podielnikid = o.podielnikid";
		if (!empty($query)) {
			if (array_contains_numbers($query)) {
				$queryBase .= " WHERE (r.cislozmluvy LIKE ";
				foreach ($query as $key => $value) {
					if (($key + 1) === count($query)) {
						$queryBase .= "'%$value%')";
					} else {
						$queryBase .= "'%$value%' OR r.cislozmluvy LIKE ";
					}
				}
			} else {
				$queryBase .= " WHERE o.prieznaz LIKE '%$query[0]%'";
			}
		}
		if ($mena != "" && !empty($query)) {
			$queryBase .= " AND r.mena = '$mena'";
		}
		if ($mena != "" && empty($query)) {
			$queryBase .= " WHERE r.mena = '$mena'";
		}
		$queryBase .= " GROUP BY
				r.mena, r.fondid, r.ucetaktiva, r.cislozmluvy, r.partner, o.prieznaz, o.meno
			ORDER BY
				r.partner, r.mena, r.cislozmluvy";
	} else if (!empty($query) || $mena !== "") {
		$queryBase = "SELECT
				r.ucetaktiva as ucetaktiva,
				r.mena as mena,
				SUM(r.KTV) as KTV,
				SUM(r.uroky) as uroky,
				r.fondid as fondid,
				r.cislozmluvy as cislozmluvy,
				r.partner as partner,
			    o.prieznaz as priezvisko,
			    o.meno as meno
			FROM	(
				SELECT
 					mt.ucetaktiva AS ucetaktiva,
					mt.kodaktiva AS mena,
					(SIGN(md_d - 0.5) * (-1) * mt.pocet) AS KTV,
					0 AS uroky,
					p.fondid as fondid,
					p.cislozmluvy as cislozmluvy,
					k.partnerid as partner
				FROM
					majetoktotal mt, portfolio p, konfirmaciaktv k
				WHERE
					mt.datum = TO_DATE('$date','YYYY-MM-DD')
					AND mt.uctovnykod = 221210
					AND mt.subjektid = p.fondid
					AND mt.ucetaktiva = k.cutd
				UNION ALL
				SELECT
 					mt.ucetaktiva AS ucetaktiva,
					mt.kodaktiva AS mena,
					0 AS KTV,
					(SIGN(md_d - 0.5) * (-1) * mt.pocet) AS uroky,
					p.fondid as fondid,
					p.cislozmluvy as cislozmluvy,
					k.partnerid as partner
				FROM
					majetoktotal mt, portfolio p, konfirmaciaktv k
				WHERE
					mt.datum = TO_DATE('$date','YYYY-MM-DD')
					AND mt.uctovnykod = 315113
					AND mt.subjektid = p.fondid
					AND mt.ucetaktiva = k.cutd
				)r
		    LEFT JOIN portfolio p2 ON r.fondid = p2.fondid
			LEFT JOIN podielnik o ON p2.podielnikid = o.podielnikid";
		if (!empty($query)) {
			if (array_contains_numbers($query)) {
				$queryBase .= " WHERE (r.cislozmluvy LIKE ";
				foreach ($query as $key => $value) {
					if (($key + 1) === count($query)) {
						$queryBase .= "'%$value%')";
					} else {
						$queryBase .= "'%$value%' OR r.cislozmluvy LIKE ";
					}
				}
			} else {
				$queryBase .= " WHERE o.prieznaz LIKE '%$query[0]%'";
			}
		}
		if ($mena != "" && !empty($query)) {
			$queryBase .= " AND r.mena = '$mena'";
		}
		if ($mena != "" && empty($query)) {
			$queryBase .= " WHERE r.mena = '$mena'";
		}
		$queryBase .= " GROUP BY
                    r . mena, r . fondid, r . ucetaktiva, r . cislozmluvy, r . partner, o . prieznaz, o . meno
                ORDER BY
                    r . partner, r . mena, r . cislozmluvy
            ";
	} else {
		$queryBase = "SELECT
				r.ucetaktiva as ucetaktiva,
				r.mena as mena,
				SUM(r.KTV) as KTV,
				SUM(r.uroky) as uroky,
				r.fondid as fondid,
				r.cislozmluvy as cislozmluvy,
				r.partner as partner,
			    o.prieznaz as priezvisko,
			    o.meno as meno
			FROM	(
				SELECT
 					mt.ucetaktiva AS ucetaktiva,
					mt.kodaktiva AS mena,
					(SIGN(md_d - 0.5) * (-1) * mt.pocet) AS KTV,
					0 AS uroky,
					p.fondid as fondid,
					p.cislozmluvy as cislozmluvy,
					k.partnerid as partner
				FROM
					majetoktotal mt, portfolio p, konfirmaciaktv k
				WHERE
					mt.datum = TO_DATE('$date','YYYY-MM-DD')
					AND mt.uctovnykod = 221210
					AND mt.subjektid = p.fondid
					AND mt.ucetaktiva = k.cutd
				UNION ALL
				SELECT
 					mt.ucetaktiva AS ucetaktiva,
					mt.kodaktiva AS mena,
					0 AS KTV,
					(SIGN(md_d - 0.5) * (-1) * mt.pocet) AS uroky,
					p.fondid as fondid,
					p.cislozmluvy as cislozmluvy,
					k.partnerid as partner
				FROM
					majetoktotal mt, portfolio p, konfirmaciaktv k
				WHERE
					mt.datum = TO_DATE('$date','YYYY-MM-DD')
					AND mt.uctovnykod = 315113
					AND mt.subjektid = p.fondid
					AND mt.ucetaktiva = k.cutd
				)r
		    LEFT JOIN portfolio p ON r.fondid = p.fondid
			LEFT JOIN podielnik o ON p.podielnikid = o.podielnikid
			GROUP BY
				r.mena, r.fondid, r.ucetaktiva, r.cislozmluvy, r.partner, o.prieznaz, o.meno
			ORDER BY
				r.partner, r.mena, r.cislozmluvy";
	}
	return $queryBase;
}

$rows = Connection::getDataFromDatabase(buildFilterQuery($queryArr, $mena, $date, $spravca), defaultDB);
$result = $rows[1];

$detail = [];
$sums = [];

foreach ($result as $record) {
	$ucetaktiva = $record["ucetaktiva"];
	$fondid = $record["fondid"];

	$detail[$ucetaktiva][$fondid]["KTV"] += $record["ktv"];
	$detail[$ucetaktiva][$fondid]["uroky"] += $record["uroky"];
	$detail[$ucetaktiva][$fondid]["mena"] = $record["mena"];
	$detail[$ucetaktiva][$fondid]["cislozmluvy"] += $record["cislozmluvy"];
	$detail[$ucetaktiva][$fondid]["meno"] = $record["priezvisko"] . " " . $record["meno"];
	$sums[$ucetaktiva]["KTV"] += $record["ktv"];
	$sums[$ucetaktiva]["uroky"] += $record["uroky"];
	$sums[$ucetaktiva]["mena"] = $record["mena"];
}

foreach ($sums as $ucetaktiva => $sum_values) {
	$moreDetails = Connection::getDataFromDatabase("SELECT p.skratka, k.Z_TD as zaciatok, k.K_TD as koniec, IR_TD as sadzb from konfirmaciaktv k, partner p where p.partnerid = k.partnerid and k.cutd = '$ucetaktiva'", defaultDB);
	$moreDetails = $moreDetails[1][0]; ?>
	<tr class="font-bold border-b bg-gray-200 dark:bg-gray-900 dark:text-gray-200" style="border-color: black;">
		<td class="p-2 text-left"><?php echo $moreDetails["skratka"] ?></td>
		<td class="vlavo"><?php echo $moreDetails["zaciatok"] . " - " . $moreDetails["koniec"] ?></td>
		<td class="vlavo"><?php echo $moreDetails["sadzb"] . "%" ?></td>
		<td class="vpravo"><?php echo $sum_values["mena"]; ?></td>
		<td class="vpravo"><?php echo number_format((float) $sum_values["KTV"], 2, '.', ' '); ?></td>
		<td class="vpravo"><?php echo $sum_values["uroky"]; ?></td>
		<td class="vpravo">&nbsp;</td>
	</tr>
	<?php
	if (!$summary) {
		foreach ($detail[$ucetaktiva] as $fondid => $detail_values) {
			$cislozmluvy = $detail_values["cislozmluvy"];
			$id_fond = $fondid;
			$clientID = $detail_values["id"];
			$clientName = $detail_values["meno"];
			$clientPrieznaz = $detail_values["prieznaz"];
			?>
			<tr class="border-b text-gray-800 dark:text-gray-200">
				<td class="pl-10 py-3 text-left"><span id="client" class="underline hover:no-underline cursor-pointer"
						onclick="runGoToPortfolio(event, '<?php echo $cislozmluvy; ?>', '<?php echo $id_fond; ?>', '<?php echo $clientID; ?>', '<?php echo $clientName; ?>', '<?php echo $clientPrieznaz; ?>', true)"><?php echo $detail_values["cislozmluvy"]; ?></span>
				</td>
				<td class="vlavo"><?php echo $detail_values["meno"] ?></td>
				<td>&nbsp;</td>
				<td class="vpravo"><?php echo $detail_values["mena"]; ?></td>
				<td class="vpravo"><?php echo number_format($detail_values["KTV"], 2, ".", " "); ?></td>
				<td class="vpravo"><?php echo $detail_values["uroky"]; ?></td>
				<td class="vpravo">
					<?php echo number_format((float) $sum_values["KTV"] != 0 ? $detail_values["KTV"] / $sum_values["KTV"] * 100 : 0, 2); ?>
					%
				</td>
			</tr>
		<?php }
	}
} ?>
<?php
require_once "/home/<USER>/www/lib/SimpleXLSXGen/SimpleXLSXGen.php";


$columns = json_decode($_POST["columns"]);
$data = json_decode($_POST["data"]);
$property = $_POST["property"];

$suhrneUdaje = array_merge(
    [$columns],
    $data
);

$xlsx = <PERSON><PERSON>kin\SimpleXLSXGen::fromArray($suhrneUdaje, 'Súhrné údaje');
$xlsx->saveAs("/home/<USER>/www/temp/" . $_POST["filename"]);

header('Content-Type: application/json');
echo json_encode(["data" => $data, "link" => '/home/<USER>/www/temp/' . $_POST["filename"]]);
<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

$date = "";
$errors = [];

if (!isset($_POST["date"])) {
    $errors[] = "Dátum nesmie byť prázdny";
} else {
    $date = $_POST["date"];
}

if (!isset($_POST["pocet"]) && !isset($_POST["moreThanOne"]) && !isset($_POST["rozdelenie"]) && !isset($_POST["investicne"])) {
    $errors[] = "Minimálne jeden typ reportu musí byť zvolený.";
}

if (!empty($errors)) {
    $errorDiv = '<div class="p-4 my-4 text-sm text-red-800 rounded-lg bg-red-50" role="alert">
        <strong class="font-bold">Vyskytly sa chyby! Prosím opravte ich a následne skúste znova.</strong>
        <ul class="max-w-md py-2 text-red-500 list-disc list-inside dark:text-gray-400">';
    foreach ($errors as $error) {
        $errorDiv .= '<li>' . $error . '</li>';
    }
    $errorDiv .= '</ul></div>';
    echo $errorDiv;
    exit;
}

$fondids = [];

if (isset($_POST["pocet"])) {
    $query = "SELECT
                COUNT(p.cislozmluvy) AS pocet,
                CASE
                    WHEN f.typpredlohy = 2 THEN 'Riadenie portfólia - fyzické osoby'
                    WHEN f.typpredlohy = 3 THEN 'Riadenie portfólia - fyzické osoby podnikatelia a právnické osoby'
                    WHEN f.typpredlohy = 4 THEN 'Komisionárske zmluvy'
                    ELSE '!!!'
                END AS typ
            FROM
                portfolio p
            JOIN
                fonds f ON p.fondid = f.fondid
            JOIN
                majetoktotal mt ON p.fondid = mt.subjektid
            WHERE
                mt.datum = '$date'::date
            GROUP BY
                f.typpredlohy,
                CASE
                    WHEN f.typpredlohy = 2 THEN 'Riadenie portfólia - fyzické osoby'
                    WHEN f.typpredlohy = 3 THEN 'Riadenie portfólia - fyzické osoby podnikatelia a právnické osoby'
                    WHEN f.typpredlohy = 4 THEN 'Komisionárske zmluvy'
                    ELSE '!!!'
                END
            ORDER BY
                f.typpredlohy;
    ";

    $pocetRes = Connection::getDataFromDatabase($query, defaultDB);
    $pocet = $pocetRes[1];

    $FO = 0;
    $PO = 0;
    $KO = 0;
    $zmluvySpolu = 0;
    $SI = 0;
    $spolu = 0;
    $Ostatne = 0;

    foreach ($pocet as $item) {
        switch ($item["typ"]) {
            case "Riadenie portfólia - fyzické osoby":
                $FO = $item["pocet"];
                break;
            case "Riadenie portfólia - fyzické osoby podnikatelia a právnické osoby":
                $PO = $item["pocet"];
                break;
            case "Komisionárske zmluvy":
                $KO = $item["pocet"];
                break;
            default:
                $Ostatne = $item["pocet"];
                break;
        }

        $zmluvySpolu = (int)$FO + (int)$PO;
        $spolu = $zmluvySpolu + (int)$KO + (int)$Ostatne;
    }

    $columns = ["Typ zmluvy", "Počet zmlúv"];
    $items = [
        ["Zmluvy o riadení portfólia", $zmluvySpolu],
        ["z toho fyzické osoby", $FO],
        ["z toho právnicke osoby a fyzické osoby - podnikatelia", $PO],
        ["Komisionárske zmluvy", $KO],
        ["Nezaradené zmluvy", $Ostatne],
        ["Spolu", $spolu]
    ];
    $filename = "pocet_zmluv_s_nenulovym_zostatkom";


    $table = '
    <section class="bg-gray-100 rounded-lg p-2 mt-4 px-6">
        <h2 class="text-xl"><strong>Počet klientov a zmlúv</strong></h2>
        <div class="relative mt-4 overflow-x-auto shadow-md rounded-t-lg">
        <section class="flex w-full justify-between items-center p-5 text-xl bg-blue-600 font-semibold text-left rtl:text-right text-white">
                <div>Počet zmlúv s nenulovým zostatkom k ' . $date . '
                    <p class="mt-1 text-sm font-normal text-gray-500 dark:text-gray-400"><span
                                class="flex items-center gap-2">
                        </span>
                    </p>
                </div>
                <form id="exportZmluvyKlienti" class="mb-0 excelForm" method="post" action="">
                <input type="hidden" name="data" ' . "value='" . json_encode($items) . "'" . '/>
                <input type="hidden" name="columns" ' . "value='" . json_encode($columns) . "'" . '/>
                <input type="hidden" name="filename" value="' . $filename . ".xlsx" . '"/>
                <button id="exportToExcelTrigger" type="submit"
                        class="p-4 bg-gray-100 flex items-center justify-center gap-2 rounded-xl text-center font-semibold text-gray-600 hover:bg-green-200 transition-all cursor-pointer hover:text-white">
                    <svg id="excelIcon" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="25" height="25"
                         viewBox="0 0 48 48">
                        <path fill="#169154" d="M29,6H15.744C14.781,6,14,6.781,14,7.744v7.259h15V6z"></path>
                        <path fill="#18482a"
                              d="M14,33.054v7.202C14,41.219,14.781,42,15.743,42H29v-8.946H14z"></path>
                        <path fill="#0c8045" d="M14 15.003H29V24.005000000000003H14z"></path>
                        <path fill="#17472a" d="M14 24.005H29V33.055H14z"></path>
                        <g>
                            <path fill="#29c27f" d="M42.256,6H29v9.003h15V7.744C44,6.781,43.219,6,42.256,6z"></path>
                            <path fill="#27663f"
                                  d="M29,33.054V42h13.257C43.219,42,44,41.219,44,40.257v-7.202H29z"></path>
                            <path fill="#19ac65" d="M29 15.003H44V24.005000000000003H29z"></path>
                            <path fill="#129652" d="M29 24.005H44V33.055H29z"></path>
                        </g>
                        <path fill="#0c7238"
                              d="M22.319,34H5.681C4.753,34,4,33.247,4,32.319V15.681C4,14.753,4.753,14,5.681,14h16.638 C23.247,14,24,14.753,24,15.681v16.638C24,33.247,23.247,34,22.319,34z"></path>
                        <path fill="#fff"
                              d="M9.807 19L12.193 19 14.129 22.754 16.175 19 18.404 19 15.333 24 18.474 29 16.123 29 14.013 25.07 11.912 29 9.526 29 12.719 23.982z"></path>
                    </svg>
                    <svg aria-hidden="true" id="spinnerExcel"
                         class="w-6 h-6 hidden text-gray-200 animate-spin dark:text-gray-600 fill-blue-600"
                         viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                              fill="currentColor"/>
                        <path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                              fill="currentFill"/>
                    </svg>
                </button>
                </form>
            </section>
            </div>
                <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
        <thead>
            <tr class="bg-gray-200">
                <th class="px-6 py-4 font-bold text-md">Typ zmluvy</th>
                <th class="px-6 py-4 font-bold text-md">Počet zmlúv</th>
            </tr>
        </thead>
        <tbody>';
    foreach ($items as $key=>$item) {
        $table .= '
                    <tr class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b">
                        <td class="px-6 py-4 font-semibold text-md">' . $item[0] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item[1] . '</td>
                    </tr>';
    }
    $table .= '</tbody></table></div>';
    echo $table;

    $queryKlienti = "SELECT
                COUNT(po.podielnikid) AS pocet,
                CASE
                    WHEN po.fpo = 0 THEN 'Fyzické osoby'
                    WHEN po.fpo = 1 THEN 'Právnické osoby'
                    WHEN po.fpo = 2 THEN 'Fyzické osoby - podnikatelia'
                    ELSE '!!!'
                END AS typ
            FROM
                podielnik po
            JOIN
                portfolio p ON po.podielnikid = p.podielnikid
            JOIN
                majetoktotal mt ON p.fondid = mt.subjektid
            WHERE
                mt.datum = '$date'::date
            GROUP BY
                po.fpo
            ORDER BY
                po.fpo;
            ";

    $pocetKlientovRes = Connection::getDataFromDatabase($queryKlienti, defaultDB);
    $pocetKlientov = $pocetKlientovRes[1];

    $FOK = 0;
    $POK = 0;
    $FOP = 0;
    $klientiSpolu = 0;
    $SI = 0;
    $spolu = 0;
    $Ostatne = 0;

    foreach ($pocetKlientov as $item) {
        switch ($item["typ"]) {
            case "Fyzické osoby":
                $FOK = $item["pocet"];
                break;
            case "Právnické osoby":
                $POK = $item["pocet"];
                break;
            case "Fyzické osoby - podnikatelia":
                $FOP = $item["pocet"];
                break;
            default:
                $Ostatne = $item["pocet"];
                break;
        }

        $klientiSpolu = $FOK + $POK;
        $spoluKlienti = $klientiSpolu + (int)$FOP + (int)$Ostatne;
    }

    $columns = ["Typ zmluvy", "Počet zmlúv"];
    $items = [
        ["Fyzické osoby", $FOK],
        ["Fyzické osoby - podnikatelia", $FOP],
        ["Právnické osoby", $POK],
        ["Nezaradené zmluvy", $Ostatne],
        ["Spolu", $spoluKlienti]
    ];
    $filename = "pocet_klientov_s_nenulovym_zostatkom";

    $table = '
        <div class="relative mt-4 overflow-x-auto shadow-md rounded-t-lg">
        <section class="flex w-full justify-between items-center p-5 text-xl bg-blue-600 font-semibold text-left rtl:text-right text-white">
                <div>Počet klientov s nenulovým zostatkom k ' . $date . '
                    <p class="mt-1 text-sm font-normal text-gray-500 dark:text-gray-400"><span
                                class="flex items-center gap-2">
                        </span>
                    </p>
                </div>
                <form id="exportZmluvyKlienti" class="mb-0 excelForm" method="post" action="">
                <input type="hidden" name="data" ' . "value='" . json_encode($items) . "'" . '/>
                <input type="hidden" name="columns" ' . "value='" . json_encode($columns) . "'" . '/>
                <input type="hidden" name="filename" value="' . $filename . ".xlsx" . '"/>
                <button id="exportToExcelTrigger" type="submit"
                        class="p-4 bg-gray-100 flex items-center justify-center gap-2 rounded-xl text-center font-semibold text-gray-600 hover:bg-green-200 transition-all cursor-pointer hover:text-white">
                    <svg id="excelIcon" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="25" height="25"
                         viewBox="0 0 48 48">
                        <path fill="#169154" d="M29,6H15.744C14.781,6,14,6.781,14,7.744v7.259h15V6z"></path>
                        <path fill="#18482a"
                              d="M14,33.054v7.202C14,41.219,14.781,42,15.743,42H29v-8.946H14z"></path>
                        <path fill="#0c8045" d="M14 15.003H29V24.005000000000003H14z"></path>
                        <path fill="#17472a" d="M14 24.005H29V33.055H14z"></path>
                        <g>
                            <path fill="#29c27f" d="M42.256,6H29v9.003h15V7.744C44,6.781,43.219,6,42.256,6z"></path>
                            <path fill="#27663f"
                                  d="M29,33.054V42h13.257C43.219,42,44,41.219,44,40.257v-7.202H29z"></path>
                            <path fill="#19ac65" d="M29 15.003H44V24.005000000000003H29z"></path>
                            <path fill="#129652" d="M29 24.005H44V33.055H29z"></path>
                        </g>
                        <path fill="#0c7238"
                              d="M22.319,34H5.681C4.753,34,4,33.247,4,32.319V15.681C4,14.753,4.753,14,5.681,14h16.638 C23.247,14,24,14.753,24,15.681v16.638C24,33.247,23.247,34,22.319,34z"></path>
                        <path fill="#fff"
                              d="M9.807 19L12.193 19 14.129 22.754 16.175 19 18.404 19 15.333 24 18.474 29 16.123 29 14.013 25.07 11.912 29 9.526 29 12.719 23.982z"></path>
                    </svg>
                    <svg aria-hidden="true" id="spinnerExcel"
                         class="w-6 h-6 hidden text-gray-200 animate-spin dark:text-gray-600 fill-blue-600"
                         viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                              fill="currentColor"/>
                        <path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                              fill="currentFill"/>
                    </svg>
                </button>
                </form>
            </section>
            </div>
                <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
        <thead>
            <tr class="bg-gray-200">
                <th class="px-6 py-4 font-bold text-md">Typ zmluvy</th>
                <th class="px-6 py-4 font-bold text-md">Počet zmlúv</th>
            </tr>
        </thead>
        <tbody>';
    foreach ($items as $key=>$item) {
        $table .= '
                    <tr class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b">
                        <td class="px-6 py-4 font-semibold text-md">' . $item[0] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item[1] . '</td>
                    </tr>';
    }
    $table .= '</tbody></table></div>';
    echo $table;
}

if (isset($_POST["rozdelenie"])) {
    $query = "
        WITH query AS (SELECT DISTINCT po.podielnikid,
                      sumadenom * sign(0.5 - md_d) *
                      f_menovy_kurz_lot(menadenom, 'EUR', TO_DATE('$date', 'YYYY-MM-DD')) as suma
               from majetoktotal mt,
                    podielnik po,
                    portfolio p,
                    fonds f
               where mt.datum = TO_DATE('$date', 'YYYY-MM-DD')
                 and po.podielnikid = p.podielnikid
                 and p.fondid = mt.subjektid
                 and p.fondid = f.fondid
                 and f.typpredlohy in (2, 3)
               GROUP BY po.podielnikid, mt.sumadenom, md_d, mt.menadenom)
        SELECT query.podielnikid, ROUND(suma, 2) as suma
        FROM query
    ";

    $majetekRes = Connection::getDataFromDatabase($query, defaultDB);
    $majetek = $majetekRes[1];

    $nad1mil = 0.0;
    $t500tis1mil = 0.0;
    $t300tis500tis = 0.0;
    $t10tis300tis = 0.0;
    $pod10tis = 0.0;
    $Ostatne = 0.0;
    $Pnad1mil = 0;
    $P500tis1mil = 0;
    $P300tis500tis = 0;
    $P10tis300tis = 0;
    $Ppod10tis = 0;
    $POstatne = 0;
    $Spolu = 0.0;
    $PSpolu = 0.0;

    foreach ($majetek as $item) {
        if ($item["suma"] > 1000000) {
            $nad1mil += $item["suma"];
            $Pnad1mil++;
        } elseif ($item["suma"] > 500000) {
            $t500tis1mil += $item["suma"];
            $P500tis1mil++;
        } elseif ($item["suma"] > 300000) {
            $t300tis500tis += $item["suma"];
            $P300tis500tis++;
        } elseif ($item["suma"] > 10000) {
            $t10tis300tis += $item["suma"];
            $P10tis300tis++;
        } elseif ($item["suma"] > 0) {
            $pod10tis += $item["suma"];
            $Ppod10tis++;
        } else {
            $Ostatne += $item["suma"];
            $POstatne++;
        }
        $Spolu = $nad1mil + $t500tis1mil + $t300tis500tis + $t10tis300tis + $pod10tis + $Ostatne;
        $PSpolu = $Pnad1mil + $P500tis1mil + $P300tis500tis + $P10tis300tis + $Ppod10tis + $POstatne;
    }

    $columns = ["Majetok klienta", "Počet klientov", "Podiel počtu klientov", "Majetok klientov", "Podiel majetku klientov"];
    $items = [
        ["Nad 1 milión euro", $Pnad1mil, round($Pnad1mil / $PSpolu * 100, 2), round($nad1mil, 2), round($nad1mil / $Spolu * 100, 2)],
        ["Nad 500 tisíc euro", $P500tis1mil, round($P500tis1mil / $PSpolu * 100, 2), round($t500tis1mil, 2), round($t500tis1mil / $Spolu * 100, 2)],
        ["Nad 300 tisíc euro", $P300tis500tis, round($P300tis500tis / $PSpolu * 100, 2), round($t300tis500tis, 2), round($t500tis1mil / $Spolu * 100, 2)],
        ["Nad 10 tisíc euro", $P10tis300tis, round($P10tis300tis / $PSpolu * 100, 2), round($t10tis300tis, 2), round($t10tis300tis / $Spolu * 100, 2)],
        ["Menej ako 10 tisíc euro", $Ppod10tis, round($Ppod10tis / $PSpolu * 100, 2), round($pod10tis, 2), round($pod10tis / $Spolu * 100, 2)],
        ["Nezaradený klienti", $POstatne, round($POstatne / $PSpolu * 100, 2), round($Ostatne, 2), round($Ostatne / $Spolu * 100, 2)],
        ["Spolu", $PSpolu, round($PSpolu / $PSpolu * 100, 2), round($Spolu, 2), round($Spolu / $Spolu * 100, 2)]
    ];
    $filename = "rozdelenie_klientov_podla_majetku_export";

    $table = '
    <section class="bg-gray-100 rounded-lg p-2 mt-4 px-6">
        <h2 class="text-xl"><strong>Rozdelenie klientov podľa majetku</strong></h2>
        <div class="relative mt-4 overflow-x-auto shadow-md rounded-t-lg">
        <section class="flex w-full justify-between items-center p-5 text-xl bg-blue-600 font-semibold text-left rtl:text-right text-white">
                <div>Rozdelenie klientov podľa majetku k ' . $date . '
                    <p class="mt-1 text-sm font-normal text-gray-500 dark:text-gray-400"><span
                                class="flex items-center gap-2">
                        </span>
                    </p>
                </div>
                <form id="exportZmluvyKlienti" class="mb-0 excelForm" method="post" action="">
                <input type="hidden" name="data" ' . "value='" . json_encode($items) . "'" . '/>
                <input type="hidden" name="columns" ' . "value='" . json_encode($columns) . "'" . '/>
                <input type="hidden" name="filename" value="' . $filename . ".xlsx" . '"/>
                <button id="exportToExcelTrigger" type="submit"
                        class="p-4 bg-gray-100 flex items-center justify-center gap-2 rounded-xl text-center font-semibold text-gray-600 hover:bg-green-200 transition-all cursor-pointer hover:text-white">
                    <svg id="excelIcon" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="25" height="25"
                         viewBox="0 0 48 48">
                        <path fill="#169154" d="M29,6H15.744C14.781,6,14,6.781,14,7.744v7.259h15V6z"></path>
                        <path fill="#18482a"
                              d="M14,33.054v7.202C14,41.219,14.781,42,15.743,42H29v-8.946H14z"></path>
                        <path fill="#0c8045" d="M14 15.003H29V24.005000000000003H14z"></path>
                        <path fill="#17472a" d="M14 24.005H29V33.055H14z"></path>
                        <g>
                            <path fill="#29c27f" d="M42.256,6H29v9.003h15V7.744C44,6.781,43.219,6,42.256,6z"></path>
                            <path fill="#27663f"
                                  d="M29,33.054V42h13.257C43.219,42,44,41.219,44,40.257v-7.202H29z"></path>
                            <path fill="#19ac65" d="M29 15.003H44V24.005000000000003H29z"></path>
                            <path fill="#129652" d="M29 24.005H44V33.055H29z"></path>
                        </g>
                        <path fill="#0c7238"
                              d="M22.319,34H5.681C4.753,34,4,33.247,4,32.319V15.681C4,14.753,4.753,14,5.681,14h16.638 C23.247,14,24,14.753,24,15.681v16.638C24,33.247,23.247,34,22.319,34z"></path>
                        <path fill="#fff"
                              d="M9.807 19L12.193 19 14.129 22.754 16.175 19 18.404 19 15.333 24 18.474 29 16.123 29 14.013 25.07 11.912 29 9.526 29 12.719 23.982z"></path>
                    </svg>
                    <svg aria-hidden="true" id="spinnerExcel"
                         class="w-6 h-6 hidden text-gray-200 animate-spin dark:text-gray-600 fill-blue-600"
                         viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                              fill="currentColor"/>
                        <path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                              fill="currentFill"/>
                    </svg>
                </button>
                </form>
            </section>
            </div>
                <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
        <thead>
            <tr class="bg-gray-200">
                <th class="px-6 py-4 font-bold text-md">Majetok klienta</th>
                <th class="px-6 py-4 font-bold text-md">Počet klientov</th>
                <th class="px-6 py-4 font-bold text-md">Podiel počtu klientov</th>
                <th class="px-6 py-4 font-bold text-md">Majetok klientov</th>
                <th class="px-6 py-4 font-bold text-md">Podiel majetku klientov</th>
            </tr>
        </thead>
        <tbody>';
    foreach ($items as $key=>$item) {
        $table .= '
                    <tr class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b">
                        <td class="px-6 py-4 font-semibold text-md">' . $item[0] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item[1] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item[2] . '%</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item[3] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item[4] . '%</td>
                    </tr>';
    }
    $table .= '</tbody></table></div>';
    echo $table;
}
echo '<script src="/src/assets/js/prehlady/nbs-gfi/excel.js"></script>';
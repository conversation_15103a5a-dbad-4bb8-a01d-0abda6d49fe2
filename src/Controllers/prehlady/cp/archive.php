<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

$isin = $_POST["isin"];
$action = $_POST["action"];
$archiv = "";

if ($action === "archive") {
    $archiv = 't';
} else {
    $archiv = 'f';
}

$archiveQuery = "UPDATE dbequity SET archiv = ? WHERE isin = ?";
$archive = Connection::InsertUpdateCreateDelete($archiveQuery, [$archiv, $isin], defaultDB);

print_r($archive);

<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
require_once "/home/<USER>/www/src/Components/tables/klientskyVypis/klientReportTable.php";

$mesiacnazov['01'] = "Január";
$mesiacnazov['02'] = "Február";
$mesiacnazov['03'] = "Marec";
$mesiacnazov['04'] = "Apríl";
$mesiacnazov['05'] = "Máj";
$mesiacnazov['06'] = "Jún";
$mesiacnazov['07'] = "Júl";
$mesiacnazov['08'] = "August";
$mesiacnazov['09'] = "September";
$mesiacnazov['10'] = "Október";
$mesiacnazov['11'] = "November";
$mesiacnazov['12'] = "December";

$poplatkyTyp = [
    "manaz" => "Poplatky za riadenie a správu",
    "vyrov" => "Transakčné poplatky",
    "tran" => "Transakčné poplatky",
    "ostatne" => "Ostatné poplatky"
];

$stavPoplatok = $_POST["stavPoplatok"];
$month = $_POST["month"];
$stvrtrok = $_POST["stvrtrok"];
$rok = $_POST["rok"];
$portfolia = $_POST['portfoliovalues'];
$types = "";
$errors = [];

if ($_POST["portfoliovalues"] === "") {
    $errors[] = "Minimálne jedno portfólio musí byť vybrané zo zoznamu.";
}

if (!isset($_POST["akcie"]) && !isset($_POST["dlhopisy"]) && !isset($_POST["terminovane"]) && !isset($_POST["istiny"]) && !isset($_POST["pripisane"]) && !isset($_POST["odpisane"])) {
    $errors[] = "Minimálne jeden typ výnosu musí byť zvolený.";
}

if (!empty($errors)) {
    $errorDiv = '<div class="p-4 my-4 text-sm text-red-800 rounded-lg bg-red-50" role="alert">
        <strong class="font-bold">Vyskytly sa chyby! Prosím opravte ich a následne skúste znova.</strong>
        <ul class="max-w-md py-2 text-red-500 list-disc list-inside dark:text-gray-400">';
    foreach ($errors as $error) {
        $errorDiv .= '<li>' . $error . '</li>';
    }
    $errorDiv .= '</ul></div>';
    echo $errorDiv;
    exit;
}

if ($stvrtrok == 1) {
    $datefrom = "01.01." . $rok;
    $dateto = "31.03." . $rok;
}
if ($stvrtrok == 2) {
    $datefrom = "01.04." . $rok;
    $dateto = "30.06." . $rok;
}
if ($stvrtrok == 3) {
    $datefrom = "01.07." . $rok;
    $dateto = "30.09." . $rok;
}
if ($stvrtrok == 4) {
    $datefrom = "01.10." . $rok;
    $dateto = "31.12." . $rok;
}

$fondids = [];

if (isset($_POST["dlhopisy"])) {
    if ($stvrtrok != 0) {
        $dateQuery = " and s.datumvyplaty >= to_date('$datefrom','dd.mm.yyyy')
                 and s.datumvyplaty <= to_date('$dateto','dd.mm.yyyy') ";
    }

    if ($month != 0) {
        $dateQuery = " and to_char(s.datumvyplaty,'mm') = '$month'
                 and to_char(s.datumvyplaty, 'yyyy') = '$rok' ";
    }

    if ($month == 0 && $stvrtrok == 0) {
        $dateQuery = " and to_char(s.datumvyplaty, 'yyyy') = '$rok' ";

    }
    $query = "
	select 
		s.datumvyplaty as datumvyplaty,
 		s.datum_naroku as datumnaroku,
		round(1/(f_menovy_kurz_lot(s.mena,'EUR',datum)),5) as kurz,
		round(s.suma*f_menovy_kurz(s.mena,'EUR',datum, 1),2) as sumask,
      		s.mena as mena,
		s.kodaktiva,
		s.suma as suma, 
		s.datum, 
		s.pocet, 
		d.cpnaz, 
		p.cislozmluvy,
		dv.danzaklad,
		dv.dan,
		dv.dansadzba,
		dv.mena as dvmena, 
		dv.vynoskus,
		d.isinreal
	from 
		splatenie s, 
		dbequity d, 
		dbequitycurr dc, 
		dbequitycurrric dcr, 
		portfolio p, 
		danvynosy dv
	where
			
		s.uctovnykod = 251120
		$dateQuery 
		and p.fondid in ($portfolia)
        	and s.dealid = dv.dealid
		and dcr.isincurrric=s.kodaktiva
		and dcr.isincurr=dc.isincurr
		and dc.isin=d.isin
		and p.fondid = s.subjektid
	order by cislozmluvy
		";
    $dlhopisyRes = Connection::getDataFromDatabase($query, defaultDB);
    $dlhopisy = $dlhopisyRes[1];
    foreach ($dlhopisy as $item) {
        $cislozmluvy = $item["cislozmluvy"];
        $fondids[$cislozmluvy]['dlhopisy'][] = [
            "datumvyplaty" => $item["datumvyplaty"],
            "datumnaroku" => $item["datumnaroku"],
            "nazov" => $item["cpnaz"],
            "isin" => $item["isinreal"],
            "mena" => $item["mena"],
            "pocet" => $item["pocet"],
            "vynoskus" => round($item["vynoskus"], 2),
            "brutto" => $item["danzaklad"],
            "dan" => $item["dan"],
            "netto" => $item["suma"],
        ];
    }
}
if (isset($_POST["terminovane"])) {
    if ($stvrtrok != 0) {
        $dateQuery = " and k_td >= to_date('$datefrom','dd.mm.yyyy')
                 and k_td <= to_date('$dateto','dd.mm.yyyy') ";
    }

    if ($month != 0) {
        $dateQuery = " and to_char(k_td,'mm') = '$month'
                 and to_char(k_td, 'yyyy') = '$rok' ";
    }

    if ($month == 0 && $stvrtrok == 0) {
        $dateQuery = " and to_char(k_td, 'yyyy') = '$rok' ";
    }
    $query =
        "SELECT 
				   k.z_td as zaciatok,
				   k.k_td as koniec,
				   k.sum_td as suma,
				   k.ir_td as sadzba,
				   k.iv_b as brutto,
				   k.iv_n as netto,
				   k.mena,
				   k.suma_dane as dan,
				   k.datvysporiadaniarealntd as vysporiadanie,
				   k.cutd,
				   k.subjektid,
				   z_td,
				   k_td,
				   p.nazovpartnera as miesto,
				   k.datum_cas_obchodu as datum_cas,
				   por.cislozmluvy	
				   
			from 
				konfirmaciaktv k,
				partner p,
				portfolio por
			where 
				
				k.logactivityid = 25
				$dateQuery 
				and por.fondid in ($portfolia)
			 	and k.partnerid = p.partnerid
				and k.subjektid > 1
				and por.fondid = k.subjektid
				
					
			
			union all
				
			select 	   
				   k.z_td as zaciatok,
				   k.k_td as koniec,
				   pdr.transsumareal as suma,
				   k.ir_td as sadzba,
				   pdr.auvreal  as brutto,
	   	   		   (pdr.auvreal - pdr.dan) as netto,
				   k.mena,
				   pdr.dan as dan,
				   k.datvysporiadaniarealntd as vysporiadanie,
				   k.cutd,
				   k.subjektid,
				   z_td,
				   k_td,
				   p.nazovpartnera as miesto,
				   k.datum_cas_obchodu as datum_cas,
				   por.cislozmluvy
				  
			from
				konfirmaciaktv k,
				pool po,
				pooldetailreal pdr,
				partner p,
				portfolio por
			where
				
				 k.subjektid = 0
				$dateQuery 
				and por.fondid in ($portfolia)
				 and pdr.poolid = po.poolid 
				 and k.dealid = po.dealid
				 and k.logactivityid = 25
				 and k.partnerid = p.partnerid
				 and pdr.subjektid = por.fondid
			order by cislozmluvy
		";
    $terminovaneRes = Connection::getDataFromDatabase($query, defaultDB);
    $terminovane = $terminovaneRes[1];
    foreach ($terminovane as $item) {
        $cislozmluvy = $item["cislozmluvy"];
        $fondids[$cislozmluvy]['terminovane'][] = [
            "vysporiadanie" => $item["vysporiadanie"],
            "banka" => $item["miesto"],
            "mena" => $item["mena"],
            "zriadenie" => $item["zaciatok"],
            "splatenie" => $item["koniec"],
            "suma" => round($item["suma"], 2),
            "sadzba" => $item["sadzba"],
            "brutto" => $item["brutto"],
            "dan" => $item["dan"],
            "netto" => $item["netto"],
        ];
    }
}

if (isset($_POST["akcie"])) {
    if ($stvrtrok != 0) {
        $dateQuery = " and s.datum >= to_date('$datefrom','dd.mm.yyyy')
                 and s.datum <= to_date('$dateto','dd.mm.yyyy') ";
    }
    if ($month != 0) {
        $dateQuery = " and to_char(s.datum,'mm') = '$month'
                 and to_char(s.datum, 'yyyy') = '$rok' ";
    }
    if ($month == 0 && $stvrtrok == 0) {
        $dateQuery = " and to_char(s.datum, 'yyyy') = '$rok' ";
    }
    $query = "
	select 
		s.datumvyplaty as datumvyplaty,
 		s.datum_naroku as datumnaroku,
		round(1/(f_menovy_kurz_lot(s.mena,'EUR',datum)),5) as kurz,
		round(s.suma*f_menovy_kurz(s.mena,'EUR',datum,1),2) as sumask,
      		s.mena as mena,
		s.kodaktiva,
		s.suma as suma, 
		s.datum, 
		s.pocet, 
		d.cpnaz, 
		p.cislozmluvy,
		dv.danzaklad,
		dv.dan,
		dv.dansadzba,
		dv.mena as dvmena, 
		dv.vynoskus,
		d.isinreal,
		dc.currencytrade,
		dat.nazov as dividenda
	from 
		splatenieakcia s, 
		dbequity d, 
		dbequitycurr dc, 
		dbequitycurrric dcr, 
		portfolio p, 
		danvynosy dv,
		dividendaakciatyp dat
	where
			
		dat.hotovost = 1
		and dat.subkodobratu = s.subkodobratu
		$dateQuery 
		and p.fondid in ($portfolia)
        	and s.dealid = dv.dealid
		and dcr.isincurrric=s.kodaktiva
		and dcr.isincurr=dc.isincurr
		and dc.isin=d.isin
		and p.fondid = s.subjektid
	order by 
		cislozmluvy
		";
    $akcieRes = Connection::getDataFromDatabase($query, defaultDB);
    $akcie = $akcieRes[1];
    foreach ($akcie as $item) {
        $cislozmluvy = $item["cislozmluvy"];
        $fondids[$cislozmluvy]['akcie'][] = [
            "datumvyplaty" => $item["datumvyplaty"],
            "datumnaroku" => $item["datumnaroku"],
            "nazov" => $item["cpnaz"],
            "isin" => $item["isinreal"],
            "mena" => $item["mena"],
            "pocet" => $item["pocet"],
            "vynoskus" => round($item["vynoskus"], 2),
            "brutto" => $item["danzaklad"],
            "dan" => $item["dan"],
            "poplatok" => $item["danzaklad"] - $item["suma"],
            "netto" => $item["suma"],
        ];
    }
}

if (isset($_POST["istiny"])) {
    if ($stvrtrok != 0) {
        $dateQuery = " and s.datumvyplaty >= to_date('$datefrom','dd.mm.yyyy')
                 and s.datumvyplaty <= to_date('$dateto','dd.mm.yyyy') ";
    }
    if ($month != 0) {
        $dateQuery = " and to_char(s.datumvyplaty,'mm') = '$month'
                 and to_char(s.datumvyplaty, 'yyyy') = '$rok' ";
    }
    if ($month == 0 && $stvrtrok == 0) {
        $dateQuery = " and to_char(s.datum, 'yyyy') = '$rok' ";
    }
    $query = "
	select 
		s.datumvyplaty as datumvyplaty,
 		s.datum_naroku as datumnaroku,
		round(1/(f_menovy_kurz_lot(s.mena,'EUR',datum)),5) as kurz,
		round(s.suma*f_menovy_kurz_lot(s.mena,'EUR',datum),2) as sumask,
      	s.mena as mena,
		s.kodaktiva,
		s.suma as suma, 
		s.datum, 
		s.pocet, 
		d.cpnaz, 
		p.cislozmluvy,
		'Jednorazové' as splat,
		d.isinreal
	from 
		splatenie s, 
		dbequity d, 
		dbequitycurr dc, 
		dbequitycurrric dcr, 
		portfolio p
	where		
		s.uctovnykod = 251110
		$dateQuery 
		and p.fondid in ($portfolia)
        	and s.datum_naroku = (select max(datesplatnost) from floatkupon where s.kodaktiva = isincurrric and istina = 100)
		and dcr.isincurrric=s.kodaktiva
		and dcr.isincurr=dc.isincurr
		and dc.isin=d.isin
		and p.fondid = s.subjektid
	union all
	select 
		s.datumvyplaty as datumvyplaty,
 		s.datum_naroku as datumnaroku,
		round(1/(f_menovy_kurz_lot(s.mena,'EUR',datum)),5) as kurz,
		round(s.suma*f_menovy_kurz_lot(s.mena,'EUR',datum),2) as sumask,
      		s.mena as mena,
		s.kodaktiva,
		s.suma as suma, 
		s.datum, 
		s.pocet, 
		d.cpnaz, 
		p.cislozmluvy,
		'Posledné čiastočné' as splat,
		d.isinreal
	from 
		splatenie s, 
		dbequity d, 
		dbequitycurr dc, 
		dbequitycurrric dcr, 
		portfolio p
	where		
		s.uctovnykod = 251110
		$dateQuery 
		and p.fondid in ($portfolia)
        	and s.datum_naroku = (select max(datesplatnost) from floatkupon where s.kodaktiva = isincurrric and istina < 100)
		and dcr.isincurrric=s.kodaktiva
		and dcr.isincurr=dc.isincurr
		and dc.isin=d.isin
		and p.fondid = s.subjektid
	union all
	select 
		s.datumvyplaty as datumvyplaty,
 		s.datum_naroku as datumnaroku,
		round(1/(f_menovy_kurz_lot(s.mena,'EUR',datum)),5) as kurz,
		round(s.suma*f_menovy_kurz_lot(s.mena,'EUR',datum),2) as sumask,
      	s.mena as mena,
		s.kodaktiva,
		s.suma as suma, 
		s.datum, 
		s.pocet, 
		d.cpnaz, 
		p.cislozmluvy,
		'Priebežné čiastočné' as splat,
		d.isinreal
	from 
		splatenie s, 
		dbequity d, 
		dbequitycurr dc, 
		dbequitycurrric dcr, 
		portfolio p
	where		
		s.uctovnykod = 251110
		$dateQuery 
		and p.fondid in ($portfolia)
        	and s.datum_naroku < (select max(datesplatnost) from floatkupon where s.kodaktiva = isincurrric and istina < 100)
		and dcr.isincurrric=s.kodaktiva
		and dcr.isincurr=dc.isincurr
		and dc.isin=d.isin
		and p.fondid = s.subjektid
		";
    $isitnyRes = Connection::getDataFromDatabase($query, defaultDB);
    $istiny = $isitnyRes[1];
    foreach ($istiny as $item) {
        $cislozmluvy = $item["cislozmluvy"];
        $fondids[$cislozmluvy]['istiny'][] = [
            "datumvyplaty" => $item["datumvyplaty"],
            "datumnaroku" => $item["datumnaroku"],
            "splat" => $item["splat"],
            "cpnaz" => $item["cpnaz"],
            "isinreal" => $item["isinreal"],
            "mena" => $item["mena"],
            "pocet" => $item["pocet"],
            "sumakus" => round($item["suma"] / $item["pocet"], 2),
            "suma" => round($item["suma"], 2)
        ];
    }
}

if (isset($_POST["odpisane"])) {
    if ($stvrtrok != 0) {
        $dateQuery = " and s.datumvyplaty >= to_date('$datefrom','dd.mm.yyyy')
                 and s.datumvyplaty <= to_date('$dateto','dd.mm.yyyy') ";
    }
    if ($month != 0) {
        $dateQuery = " and to_char(s.datumvyplaty,'mm') = '$month'
                 and to_char(s.datumvyplaty, 'yyyy') = '$rok' ";
    }
    if ($month == 0 && $stvrtrok == 0) {
        $dateQuery = " and to_char(s.datum, 'yyyy') = '$rok' ";
    }
    $query = "
	select 
		s.datumvyplaty as datumvyplaty,
 		s.datum_naroku as datumnaroku,
		round(1/(f_menovy_kurz_lot(s.mena,'EUR',datum)),5) as kurz,
		round(s.suma*f_menovy_kurz_lot(s.mena,'EUR',datum),2) as sumask,
      		
		s.kodaktiva,
		s.suma as suma, 
		s.datum, 
		s.pocet, 
		d.cpnaz, 
		p.cislozmluvy,
		dat.nazov as splat,
		d.isinreal,
		dc.currencytrade as mena,
		dat.nazov as dividenda
	from 
		splatenieakcia s, 
		dbequity d, 
		dbequitycurr dc, 
		dbequitycurrric dcr, 
		portfolio p,
		dividendaakciatyp dat
	where		
		dat.hotovost = 0
		and dat.MD_D = 1
		and dat.subkodobratu = s.subkodobratu
		$dateQuery 
		and p.fondid in ($portfolia)
        	and dcr.isincurrric=s.kodaktiva
		and dcr.isincurr=dc.isincurr
		and dc.isin=d.isin
		and p.fondid = s.subjektid
		";
    $odpisaneRes = Connection::getDataFromDatabase($query, defaultDB);
    $odpisane = $odpisaneRes[1];
    foreach ($odpisane as $item) {
        $cislozmluvy = $item["cislozmluvy"];
        $fondids[$cislozmluvy]['odpisane'][] = [
            "datumvyplaty" => $item["datumvyplaty"],
            "datumnaroku" => $item["datumnaroku"],
            "splat" => $item["splat"],
            "cpnaz" => $item["cpnaz"],
            "isinreal" => $item["isinreal"],
            "mena" => $item["mena"],
            "pocet" => $item["pocet"]
        ];
    }
}

if(isset($_POST["pripisane"])){
    if ($stvrtrok != 0) {
        $dateQuery = " and s.datumvyplaty >= to_date('$datefrom','dd.mm.yyyy')
                 and s.datumvyplaty <= to_date('$dateto','dd.mm.yyyy') ";
    }
    if ($month != 0) {
        $dateQuery = " and to_char(s.datumvyplaty,'mm') = '$month'
                 and to_char(s.datumvyplaty, 'yyyy') = '$rok' ";
    }
    if ($month == 0 && $stvrtrok == 0) {
        $dateQuery = " and to_char(s.datum, 'yyyy') = '$rok' ";
    }
    $query = "
	select 
		s.datumvyplaty as datumvyplaty,
 		s.datum_naroku as datumnaroku,
		round(1/(f_menovy_kurz_lot(s.mena,'EUR',datum)),5) as kurz,
		round(s.suma*f_menovy_kurz_lot(s.mena,'EUR',datum),2) as sumask,
      		
		s.kodaktiva,
		s.suma as suma, 
		s.datum, 
		s.pocet, 
		d.cpnaz, 
		p.cislozmluvy,
		dat.nazov as splat,
		d.isinreal,
		dc.currencytrade as mena,
		dat.nazov as dividenda
	from 
		splatenieakcia s, 
		dbequity d, 
		dbequitycurr dc, 
		dbequitycurrric dcr, 
		portfolio p,
		dividendaakciatyp dat
	where		
		dat.hotovost = 0
		and dat.MD_D = 0
		and dat.subkodobratu = s.subkodobratu
		$dateQuery 
		and p.fondid in ($portfolia)
        	and dcr.isincurrric=s.kodaktiva
		and dcr.isincurr=dc.isincurr
		and dc.isin=d.isin
		and p.fondid = s.subjektid
		";
    $pripisaneRes = Connection::getDataFromDatabase($query, defaultDB);
    $pripisane = $pripisaneRes[1];
    foreach ($pripisane as $item) {
        $cislozmluvy = $item["cislozmluvy"];
        $fondids[$cislozmluvy]['pripisane'][] = [
            "datumvyplaty" => $item["datumvyplaty"],
            "datumnaroku" => $item["datumnaroku"],
            "splat" => $item["splat"],
            "cpnaz" => $item["cpnaz"],
            "isinreal" => $item["isinreal"],
            "mena" => $item["mena"],
            "pocet" => $item["pocet"]
        ];
    }
}

if (empty($akcie) && empty($terminovane) && empty($dlhopisy) && empty($istiny) && empty($odpisane)) {
    echo '<div class="p-4 mb-4 text-lg text-blue-800 rounded-lg bg-blue-50 dark:bg-gray-800 dark:text-blue-400" role="alert">
                  <span class="font-bold">Žiadne záznamy!</span> Podľa vami zadaných kritérií sa nenašiel záznam. Skúste upraviť Vaše vyhľadávanie.
                </div>';
    exit;
}
function buildSectionHeader(string $sectionHeading, array $subSectionColumns): string
{
    $sectionHeader .= '<tr class="bg-gray-800 border-b text-white border-gray-700">';
    $sectionHeader .= '<td colspan="' . sizeof($subSectionColumns) . '" class="px-6 py-4 font-extrabold text-md">' . $sectionHeading . '</td>';
    $sectionHeader .= '</tr> <tr class="border-b">';
    foreach ($subSectionColumns as $column) {
        $sectionHeader .= '<th scope="col" class="px-6 py-3">' . $column . '</td>';
    }
    return $sectionHeader;
}

/*echo "<pre>";
print_r($fondids);
echo "</pre>";*/


foreach ($fondids as $cislozmluvy => $items) {
    $reportTable = '
        <div class="relative mt-4 mb-6 overflow-x-auto shadow-md sm:rounded-lg">
                <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
                <caption
                class="p-5 text-xl font-semibold text-left rtl:text-right text-gray-900 bg-white dark:text-white dark:bg-gray-800">
            <section class="flex w-full justify-between items-center p-1">
                <div>Prehľad vyplatených výnosov pre portfólio ' . $cislozmluvy . '
                    <p class="mt-1 text-sm font-normal text-gray-500 dark:text-gray-400"><span
                                class="flex items-center gap-2">Prehľad
                        </span>
                    </p>
                </div>
            </section>
        </caption>
        <tbody>';
    foreach ($items as $section => $sections) {
        switch ($section) {
            case "akcie":
                $reportTable .= buildSectionHeader("Výnosy z akcií",
                    ["Dátum výplaty", "	Dátum nároku", "Názov", "ISIN", "Druh výnosu", "Mena", "Počet ks", "Výnos na kus", "Výnos pred zdanením", "Daň", "Poplatok", "Výnos po zdanení"]);
                break;
            case "dlhopisy":
                $reportTable .= buildSectionHeader("Výnosy z dlhopisov",
                    ["Dátum výplaty", "Dátum nároku", "Názov", "ISIN", "Mena", "Počet ks", "Výnos na kus", "Výnos pred zdanením", "Daň", "Výnos po zdanení"]);
                break;
            case "terminovane":
                $reportTable .= buildSectionHeader("Výnosy z termínovaných vkladov",
                    ["Dátum výplaty", "Banka", "Mena", "Zriadenie KTV", "Splatenie KTV", "Suma", "Sadzba", "Výnos pred zdanením", "Daň", "Výnos po zdanení"]);
                break;
            case "istiny":
                $reportTable .= buildSectionHeader("Splatenie istiny u dlhopisov",
                    ["Dátum výplaty", "Dátum nároku", "Typ splatenia", "Názov", "ISIN", "Mena", "Počet ks", "Suma na kus", "Suma spolu"]);
                break;
            case "odpisane":
                $reportTable .= buildSectionHeader("Odpísané cenné papiere",
                    ["Dátum odpísania", "Dátum nároku", "Dôvod odpísania", "Názov", "ISIN", "Mena", "Počet ks"]);
                break;
            case "pripisane":
                $reportTable .= buildSectionHeader("Pripísané cenné papiere",
                    ["Dátum odpísania", "Dátum nároku", "Dôvod odpísania", "Názov", "ISIN", "Mena", "Počet ks"]);
                break;
        }

        foreach ($sections as $item) {
            switch ($section) {
                case "akcie":
                    $reportTable .= '
                    <tr class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b dark:border-gray-700">
                        <td class="px-6 py-4 font-semibold text-md">' . $item["datumvyplaty"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["datumnaroku"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["cislozmluvy"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["dividenda"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["mena"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["pocet"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["vynoskus"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["brutto"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["dan"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["poplatok"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["netto"] . '</td>
                    </tr>
                    ';
                    break;
                case "terminovane":
                    $reportTable .= '
                    <tr class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b dark:border-gray-700">
                        <td class="px-6 py-4 font-semibold text-md">' . $item["vysporiadanie"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["banka"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["mena"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["zriadenie"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["splatenie"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["suma"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["sadzba"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["brutto"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["dan"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["netto"] . '</td>
                    </tr>
                    ';
                    break;
                case "dlhopisy":
                    $reportTable .= '
                    <tr class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b dark:border-gray-700">
                        <td class="px-6 py-4 font-semibold text-md">' . $item["datumvyplaty"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["datumnaroku"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["nazov"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["isin"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["mena"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["pocet"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["vynoskus"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["brutto"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["dan"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["netto"] . '</td>
                    </tr>
                    ';
                    break;
                case "istiny":
                    $reportTable .= '
                    <tr class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b dark:border-gray-700">
                        <td class="px-6 py-4 font-semibold text-md">' . $item["datumvyplaty"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["datumnaroku"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["splat"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["cpnaz"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["isinreal"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["mena"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["pocet"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["sumakus"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["suma"] . '</td>
                    </tr>
                    ';
                    break;
                case "odpisane" || "pripisane":
                    $reportTable .= '
                    <tr class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b dark:border-gray-700">
                        <td class="px-6 py-4 font-semibold text-md">' . $item["datumvyplaty"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["datumnaroku"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["splat"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["cpnaz"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["isinreal"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["mena"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["pocet"] . '</td>
                    </tr>
                    ';
                    break;
            }
        }
    }
    $reportTable .= '</tbody></table></div>';
    echo $reportTable;
}

<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
require_once "/home/<USER>/www/src/Components/tables/klientskyVypis/klientReportTable.php";

$mesiacnazov['01'] = "Január";
$mesiacnazov['02'] = "Február";
$mesiacnazov['03'] = "Marec";
$mesiacnazov['04'] = "Apríl";
$mesiacnazov['05'] = "Máj";
$mesiacnazov['06'] = "Jún";
$mesiacnazov['07'] = "Júl";
$mesiacnazov['08'] = "August";
$mesiacnazov['09'] = "September";
$mesiacnazov['10'] = "Október";
$mesiacnazov['11'] = "November";
$mesiacnazov['12'] = "December";

$poplatkyTyp = [
    "manaz" => "Poplatky za riadenie a správu",
    "vyrov" => "Transakčné poplatky",
    "tran" => "Transakčné poplatky",
    "ostatne" => "Ostatné poplatky"
];

$stavPoplatok = $_POST["stavPoplatok"];
$month = $_POST["month"];
$stvrtrok = $_POST["stvrtrok"];
$rok = $_POST["rok"];
$portfolia = $_POST['portfoliovalues'];
$types = "";
$errors = [];

if ($_POST["portfoliovalues"] === "") {
    $errors[] = "Minimálne jedno portfólio musí byť vybrané zo zoznamu.";
}

if (!isset($_POST["uhradeneRiadenie"]) && !isset($_POST["uhradeneTransakcne"]) && !isset($_POST["uhradeneOstatne"]) && !isset($_POST["uhradeneAll"])) {
    $errors[] = "Minimálne jeden typ poplatku musí byť zvolený.";
}

if (!empty($errors)) {
    $errorDiv = '<div class="p-4 my-4 text-sm text-red-800 rounded-lg bg-red-50" role="alert">
        <strong class="font-bold">Vyskytly sa chyby! Prosím opravte ich a následne skúste znova.</strong>
        <ul class="max-w-md py-2 text-red-500 list-disc list-inside dark:text-gray-400">';
    foreach ($errors as $error) {
        $errorDiv .= '<li>' . $error . '</li>';
    }
    $errorDiv .= '</ul></div>';
    echo $errorDiv;
    exit;
}

if (isset($_POST["uhradeneRiadenie"])) {
    $types = "'MANAZ', 'SPRAVA'";
}
if (isset($_POST["uhradeneTransakcne"])) {
    if ($types != "") {
        $types .= ", 'TRAN', 'VYROV'";
    } else {
        $types = "'TRAN', 'VYROV'";
    }
}

if (isset($_POST["uhradeneOstatne"])) {
    if ($types != "") {
        $types .= ", 'OSTATNE'";
    } else {
        $types = "'OSTATNE'";
    }
}


if ($stvrtrok == 1) {
    $datefrom = "01.01." . $rok;
    $dateto = "31.03." . $rok;
}
if ($stvrtrok == 2) {
    $datefrom = "01.04." . $rok;
    $dateto = "30.06." . $rok;
}
if ($stvrtrok == 3) {
    $datefrom = "01.07." . $rok;
    $dateto = "30.09." . $rok;
}
if ($stvrtrok == 4) {
    $datefrom = "01.10." . $rok;
    $dateto = "31.12." . $rok;
}

if ($stvrtrok != 0) {
    $dateQuery = " and p.datum >= to_date('$datefrom','dd.mm.yyyy')
                 and p.datum <= to_date('$dateto','dd.mm.yyyy') ";
}

if ($month != 0) {
    $dateQuery = " and to_char(p.datum,'mm') = '$month'
                 and to_char(p.datum, 'yyyy') = '$rok' ";
}

if ($month == 0 && $stvrtrok == 0) {
    $dateQuery = " and to_char(p.datum, 'yyyy') = '$rok' ";
}

if (isset($_POST["neuhradene"])) {
    $query = "SELECT mt.kodaktiva AS mena,
       SUM(CASE
               WHEN mt.uctovnykod = 221110 THEN SIGN(md_d - 0.5) * (-1) * mt.pocet
               ELSE 0
           END) AS banka,
       SUM(CASE
               WHEN mt.uctovnykod IN (325421, 325422) THEN SIGN(md_d - 0.5) * (-1) * mt.pocet
               ELSE 0
           END) AS riadeniesprava,
       SUM(CASE
               WHEN mt.uctovnykod IN (325435, 325437) THEN SIGN(md_d - 0.5) * (-1) * mt.pocet
               ELSE 0
           END) AS transakcie,
       SUM(CASE
               WHEN mt.uctovnykod = 325443 THEN SIGN(md_d - 0.5) * (-1) * mt.pocet
               ELSE 0
           END) AS ostatne,
       po.cislozmluvy AS zmluva
FROM majetoktoday mt
         JOIN portfolio po
              ON mt.subjektid = po.fondid
JOIN poplatok_register p ON p.fondid = mt.subjektid AND p.stav IN (1, 3)
WHERE mt.subjektid in ($portfolia) $dateQuery
GROUP BY mt.kodaktiva, po.cislozmluvy
HAVING SUM(CASE
               WHEN mt.uctovnykod IN (325421, 325422) THEN SIGN(md_d - 0.5) * (-1) * mt.pocet
               ELSE 0
    END) < 0
    OR SUM(CASE
               WHEN mt.uctovnykod IN (325435, 325437) THEN SIGN(md_d - 0.5) * (-1) * mt.pocet
               ELSE 0
    END) < 0
    OR SUM(CASE
               WHEN mt.uctovnykod = 325443 THEN SIGN(md_d - 0.5) * (-1) * mt.pocet
               ELSE 0
    END) < 0
ORDER BY mt.kodaktiva, po.cislozmluvy;
		";

    $neuhradeneRes = Connection::getDataFromDatabase($query, defaultDB);
    $neuhradene = $neuhradeneRes[1];

    if (empty($neuhradene)) {
        echo '<div class="p-4 mb-4 text-sm text-blue-800 rounded-lg bg-blue-50 dark:bg-gray-800 dark:text-blue-400" role="alert">
                  <span class="font-medium">Žiadne záznamy!</span> Podľa vami zadaných kritérií sa nenašiel záznam. Skúste upraviť Vaše vyhľadávanie.
                </div>';
        exit;
    }

    $reportTable = '
            <div class="relative mb-10 overflow-x-auto shadow-md sm:rounded-lg">
                <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
                <caption
                class="p-5 text-xl font-semibold text-left rtl:text-right text-gray-900 bg-white dark:text-white dark:bg-gray-800">
            <section class="flex w-full justify-between items-center p-1">
                <div>Report neuhradených poplatkov za ' . ($month != 0 ? $mesiacnazov["0" . $month] : "všetky mesiace ") . ' roku ' . $rok . '
                    <p class="mt-1 text-sm font-normal text-gray-500 dark:text-gray-400"><span
                                class="flex items-center gap-2">
                        </span>
                    </p>
                </div>
            </section>
        </caption>
        <thead>
            <tr class="text-xs text-gray-700 uppercase bg-gray-50 border-b">
                <th scope="col" class="px-6 py-3 font-extrabold">Portfólio</th>
                <th scope="col" class="px-6 py-3 font-extrabold">Mena</th>
                <th scope="col" class="px-6 py-3 font-extrabold">Stav na účte</th>';
    if (isset($_POST["uhradeneRiadenie"])) {
        $reportTable .= '<th scope="col" class="px-6 py-3 font-extrabold">Riadenie a správa</th>';
    }
    if (isset($_POST["uhradeneTransakcne"])) {
        $reportTable .= '<th scope="col" class="px-6 py-3 font-extrabold">Transakcie</th>';
    }
    if (isset($_POST["uhradeneOstatne"])) {
        $reportTable .= '<th scope="col" class="px-6 py-3 font-extrabold">Ostatné</th>';
    }
    $reportTable .= '<th scope="col" class="px-6 py-3 font-extrabold">Spolu</th>
            </tr>
        </thead>
        <tbody>';

    $poplatekRiadenie = 0;
    $poplatekTransakcne = 0;
    $poplatekOstatne = 0;

    foreach ($neuhradene as $poplatok) {
        $reportTable .= '<tr class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b dark:border-gray-700">
                        <td class="px-6 py-4 font-semibold text-md"><span class="bg-gray-500 text-white rounded-lg w-full p-2">' . $poplatok["zmluva"] . '</span></td>
                        <td class="px-6 py-4 font-semibold text-md">' . $poplatok["mena"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . number_format($poplatok["banka"], 2, ".", " ") . '</td>';
        if (isset($_POST["uhradeneRiadenie"])) {
            $poplatekRiadenie = $poplatok["riadeniesprava"];
            $reportTable .= '<td class="px-6 py-4 font-semibold text-md"><span class="' . ($poplatok["riadeniesprava"] < 0 ? "bg-red-100 font-bold p-1 px-2 rounded-lg text-red-500" : "bg-gray-100 p-1 px-2 rounded-lg") . '">' . number_format($poplatok["riadeniesprava"], 2, ".", " ") . '<span></td>';
        }
        if (isset($_POST["uhradeneTransakcne"])) {
            $poplatekTransakcne = $poplatok["transakcie"];
            $reportTable .= '<td class="px-6 py-4 font-semibold text-md"><span class="' . ($poplatok["transakcie"] < 0 ? "bg-red-100 font-bold p-1 px-2 rounded-lg text-red-500" : "bg-gray-100 p-1 px-2 rounded-lg") . '">' . number_format($poplatok["transakcie"], 2, ".", " ") . '</span></td>';
        }
        if (isset($_POST["uhradeneOstatne"])) {
            $poplatekOstatne = $poplatok["ostatne"];
            $reportTable .= '<td class="px-6 py-4 font-semibold text-md"><span class="' . ($poplatok["ostatne"] < 0 ? "bg-red-100 font-bold p-1 px-2 rounded-lg text-red-500" : "bg-gray-100 p-1 px-2 rounded-lg") . '">' . number_format($poplatok["ostatne"], 2, ".", " ") . '</span></td>';
        }
        $reportTable .= '<td class="px-6 py-4 font-semibold text-md">' . number_format(($poplatok["banka"] + $poplatekRiadenie + $poplatekTransakcne + $poplatekOstatne), 2, ".", " ") . '</td>
                    </tr>';
    }
    $reportTable .= '</tbody></table></div>';
    echo $reportTable;

} else {
    $query = "select po.cislozmluvy, p.fondid, max(to_char(p.datum,'mm')) as mesiac, p.mena as mena, 
sum(CASE WHEN p.typ='SPRAVA' THEN p.suma ELSE 0 END) as sprava,
sum(CASE WHEN p.typ='MANAZ'  THEN p.suma ELSE 0 END) as manaz,
sum(CASE WHEN p.typ='SPRAVA' THEN p.dansadzba ELSE 0 END) as sprava_dansadzba,
sum(CASE WHEN p.typ='MANAZ' THEN p.dansadzba ELSE 0 END) as riadenie_dansadzba,
sum(CASE WHEN p.typ='SPRAVA' THEN p.dan ELSE 0 END) as sprava_dan,
sum(CASE WHEN p.typ='MANAZ' THEN p.dan ELSE 0 END) as manaz_dan,
sum(CASE WHEN p.typ='TRAN' THEN p.suma ELSE 0 END) as transakcia,
sum(CASE WHEN p.typ='VYROV' THEN p.suma ELSE 0 END) as vyrovnanie,
p.suma as poplatok,
p.dan as dan,
p.dansadzba as dansadzba,
p.typ as typ,
p.dealid as dealid,
p.dovod as dovod,
CASE 
    WHEN stav = 0 THEN 'nerekonfirmované'
    WHEN stav = 1 THEN 'nezaplatené inkaso'
    WHEN stav = 2 THEN 'zaplatené inkasom'
    WHEN stav = 3 THEN 'faktúra'
END AS Stav
from poplatok_register p, portfolio po 
 where 
    p.typ in ($types)
    and p.fondid in ($portfolia) 
    $dateQuery
    and p.fondid = po.fondid
    and p.stav in ($stavPoplatok)
 group by  po.cislozmluvy, to_char(p.datum,'mm'), p.mena, p.fondid, p.stav, p.typ, p.dealid, p.dovod, p.suma, p.dan, p.dansadzba
 order by po.cislozmluvy, to_char(p.datum,'mm'), p.mena";
    $dbConn = Connection::getDataFromDatabase($query, defaultDB);
    $result = $dbConn[1];

    if (empty($result)) {
        echo '<div class="p-4 mb-4 text-lg text-blue-800 rounded-lg bg-blue-50 dark:bg-gray-800 dark:text-blue-400" role="alert">
                  <span class="font-bold">Žiadne záznamy!</span> Podľa vami zadaných kritérií sa nenašiel záznam. Skúste upraviť Vaše vyhľadávanie.
                </div>';
        exit;
    }

    $fondids = [];
    foreach ($result as $item) {
        $cislozmluvy = $item["cislozmluvy"];
        $fondid = $item["fondid"];
        $typ = $item["typ"];
        if ($typ === "MANAZ") {
            $fondids[$cislozmluvy]["manaz"][] = [
                "mesiac" => $item["mesiac"],
                "riadenie" => $item["manaz"],
                "sprava" => $item["sprava"],
                "mena" => $item["mena"],
                "riadenie_dansazdba" => $item["riadenie_dansadzba"],
                "sprava_dansadzba" => $item["sprava_dansadzba"],
                "riadenie_dan" => $item["manaz_dan"],
                "sprava_dan" => $item["sprava_dan"]
            ];
        }
        if ($typ === "OSTATNE") {
            $fondids[$cislozmluvy]["ostatne"][] = [
                "mesiac" => $mesiacnazov[$item["mesiac"]],
                "popis" => $item["dovod"],
                "mena" => $item["mena"],
                "zaklad" => ($item["poplatok"] - $item["dan"]),
                "dan" => $item["dan"],
                "dansadzba" => $item["dansadzba"],
                "spolu" => $item["poplatok"]
            ];
        }
        if ($typ === "VYROV" || $typ === "TRAN") {
            $mena = $item["mena"];
            $dealid = $item["dealid"];
            $queryTransakcie = "select fondid, to_char(p.datum,'dd.mm.yyyy') as datumvysp, to_char(r.dattransakcie,'dd.mm.yyyy') as datumtran, d.cpnaz as nazovcp, 
              k.isin as isin, k.druhobchodu as transakcia,
              p.mena as mena, sum((CASE WHEN p.typ='TRAN' THEN ( round(p.suma,2) ) ELSE 0 END)) as tran,
              sum((CASE WHEN p.typ='VYROV' THEN ( round(p.suma,2) ) ELSE 0 END)) as vyrov, p.dealid, p.tranza
            from dbequity d,poplatok_register p, konfirmaciacp k, rekonfirmaciacp r 
            where 
               p.typ = '$typ'
               AND p.dealid = $dealid
               $dateQuery
               and k.dealid = p.dealid 
               and d.isin=k.isin 
               and p.dealid=r.dealid 
               and p.tranza = r.tranza 
               and p.fondid = $fondid and p.mena = '$mena'
	       and p.stav in ($stavPoplatok) 
            group by p.mena, p.dealid, p.tranza, p.fondid, k.isin, k.druhobchodu, d.cpnaz, p.datum, r.dattransakcie
            order by p.datum, r.dattransakcie";
            $transakciaRes = Connection::getDataFromDatabase($queryTransakcie, defaultDB);
            $transakcia = $transakciaRes[1];
            foreach ($transakcia as $tt) {
                $fondids[$cislozmluvy]["tran"][] = [
                    "datumvysp" => $tt["datumvysp"],
                    "datumtran" => $tt["datumtran"],
                    "nazovcp" => $tt["nazovcp"],
                    "isin" => $tt["isin"],
                    "transakcia" => $tt["transakcia"],
                    "mena" => $tt["mena"],
                    "poplTran" => $tt["tran"],
                    "poplVysp" => $tt["vyrov"],
                ];
            }
        }
    }

    function buildSectionHeader(array $topColumns, array $sums, array $subSectionColumns): string
    {
        $sectionHeader = '<tr class="text-xs text-gray-700 uppercase bg-gray-50 border-b">';
        foreach ($topColumns as $column) {
            $sectionHeader .= '<th scope="col" class="px-6 py-3 font-extrabold">' . $column . '</th>';
        }
        $sectionHeader .= '</tr>';
        $sectionHeader .= '<tr class="bg-gray-800 border-b text-white border-gray-700">';
        foreach ($sums as $column) {
            $sectionHeader .= '<td class="px-6 py-4 font-extrabold text-md">' . $column . '</td>';
        }
        $sectionHeader .= '</tr> <tr class="border-b">';
        foreach ($subSectionColumns as $column) {
            $sectionHeader .= '<th scope="col" class="px-6 py-3">' . $column . '</td>';
        }
        return $sectionHeader;
    }

    foreach ($fondids as $cislozmluvy => $items) {
        $reportTable = '
<div class="relative mb-10 overflow-x-auto shadow-md sm:rounded-lg">
                <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
                <caption
                class="p-5 text-xl font-semibold text-left rtl:text-right text-gray-900 bg-white dark:text-white dark:bg-gray-800">
            <section class="flex w-full justify-between items-center p-1">
                <div>Report poplatkov pre portfólio ' . $cislozmluvy . '
                    <p class="mt-1 text-sm font-normal text-gray-500 dark:text-gray-400"><span
                                class="flex items-center gap-2">Report poplatkov ' . $section . '
                        </span>
                    </p>
                </div>
            </section>
        </caption>
        <tbody>';
        foreach ($items as $section => $sections) {
            $riadenieSpolu = 0;
            $zakladSpolu = 0;
            $danSpolu = 0;
            $spolu = 0;
            $tranSpolu = 0;
            $vyspSpolu = 0;
            $ostatneSpolu = 0;
            $ostatneZakladSpolu = 0;
            $ostatneDanSpolu = 0;

            foreach ($sections as $item) {
                $mena = $item["mena"];
                $riadenieSpolu += round(($item["riadenie"] - $item["riadenie_dan"]), 2);
                $danSpolu += round(($item["riadenie_dan"] + $item["sprava_dan"]), 2);
                $spolu += round(($item["riadenie"] + $item["sprava"]), 2);
                $tranSpolu += round(($item["poplTran"]), 2);
                $vyspSpolu += round(($item["poplVysp"]), 2);
                $ostatneZakladSpolu += round($item["zaklad"], 2);
                $ostatneDanSpolu += round($item["dan"], 2);
                $ostatneSpolu += round($item["spolu"], 2);
            }

            switch ($section) {
                case "manaz":
                    $reportTable .= buildSectionHeader(["Typ poplatku", "", "Mena", "Základ", "Daň", "", "", "Spolu"],
                        [$poplatkyTyp[$section], "", $mena, round($riadenieSpolu, 2), round($danSpolu, 2), "", "", round($spolu, 2)],
                        ["Mesiac", "Popis", "Mena", "Základ", "Daň", "Sadzba dane", "", "Spolu"]);
                    break;
                case "ostatne":
                    $reportTable .= buildSectionHeader(["Typ poplatku", "", "Mena", "Základ", "Daň", "", "", "Spolu"],
                        [$poplatkyTyp[$section], "", $mena, $ostatneZakladSpolu, round($ostatneDanSpolu, 2), "", "", round($ostatneSpolu, 2)],
                        ["Mesiac", "Popis", "Mena", "Základ", "Daň", "Sadzba dane", "", "Spolu"]);
                    break;
                case "vyorv" || "tran":
                    $reportTable .= buildSectionHeader(["Typ poplatku", "", "", "", "", "Mena", "Poplatky za tran. spolu", "Poplatky za vysp. spolu"],
                        [$poplatkyTyp[$section], "", "", "", "", $mena, round($tranSpolu, 2), round($vyspSpolu, 2)],
                        ["Dátum vyps.", "Dátum tran.", "Názov CP", "ISIN", "Transakcia", "Mena", "Popl. za tran.", "Popl. za vysp."]);
                    break;
            }

            foreach ($sections as $item) {
                switch ($section) {
                    case "manaz":
                        $reportTable .= '
                    <tr class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b dark:border-gray-700">
                        <td class="px-6 py-4 font-semibold text-md">' . $mesiacnazov[$item["mesiac"]] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">Riadenie</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["mena"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . round(($item["riadenie"] - $item["riadenie_dan"]), 2) . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["riadenie_dan"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["riadenie_dansazdba"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md"></td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["riadenie"] . '</td>
                    </tr>
                    <tr class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b dark:border-gray-700">
                        <td class="px-6 py-4 font-semibold text-md">' . $mesiacnazov[$item["mesiac"]] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">Správa</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["mena"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . round(($item["sprava"] - $item["sprava_dan"]), 2) . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["sprava_dan"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["sprava_dansadzba"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md"></td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["sprava"] . '</td>
                    </tr>
                    ';
                        break;
                    case "ostatne":
                        $reportTable .= '
                    <tr class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b dark:border-gray-700">
                        <td class="px-6 py-4 font-semibold text-md">' . $item["mesiac"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["popis"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["mena"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["zaklad"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["dan"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["dansadzba"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md"></td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["spolu"] . '</td>
                    </tr>
                    ';
                        break;
                    case "tran" || "vyrov":
                        $reportTable .= '
                    <tr class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b dark:border-gray-700">
                        <td class="px-6 py-4 font-semibold text-md">' . $item["datumvysp"] . '</td>
                         <td class="px-6 py-4 font-semibold text-md">' . $item["datumtran"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["nazovcp"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["isin"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["transakcia"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["mena"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["poplTran"] . '</td>
                        <td class="px-6 py-4 font-semibold text-md">' . $item["poplVysp"] . '</td>
                    </tr>
                    ';
                        break;
                }
            }
        }
        $reportTable .= '</tbody></table></div>';
        echo $reportTable;
    }
}
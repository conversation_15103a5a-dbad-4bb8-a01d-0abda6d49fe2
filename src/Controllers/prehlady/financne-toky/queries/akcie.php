<?php
if ($fond) {
    $query = "WITH filtered_majetoktotal AS (
            SELECT
                mt.kodaktiva,
                mt.menadenom,
                SUM(
                    CASE
                        WHEN mt.md_d = 0 THEN mt.pocet
                        ELSE (-1) * mt.pocet
                    END
                ) AS pocet,
                SUM(
                    CASE
                        WHEN mt.md_d = 0 THEN mt.sumadenom
                        ELSE (-1) * mt.sumadenom
                    END
                ) AS objem,
                MAX(mt.kurzaktiva) AS kurz,
                f_menovy_kurz_lot(mt.menadenom, 'EUR', mt.datum) AS kurz_lot,
                MAX(f_menovy_kurz(mt.menadenom, 'EUR', mt.datum, 1)) AS kurz_mena
            FROM
                majetoktotal mt
            WHERE
                mt.datum = to_date('$dbdate', 'YYYY-MM-DD')
                AND mt.uctovnykod IN (251200)
                AND mt.subjektid = $fond
            GROUP BY
                mt.kodaktiva,
                mt.menadenom,
                mt.datum
        ),
        pocty AS (
            SELECT
                f.kodaktiva,
                f.menadenom,
                f.pocet,
                f.kurz,
                f.objem,
                (f.objem * f.kurz_lot) AS objemref,
                f.kurz_mena
            FROM
                filtered_majetoktotal f
        )
        SELECT
            d.cpnaz,
            pocty.menadenom AS mena,
            d.isinreal,
            pocty.pocet,
            pocty.kurz,
            pocty.objem,
            pocty.objemref,
            pocty.kurz_mena
        FROM
            pocty
            JOIN dbequitycurrric r ON pocty.kodaktiva = r.isincurrric
            JOIN dbequitycurr c ON r.isincurr = c.isincurr
            JOIN dbequity d ON c.isin = d.isin
        WHERE
            d.druheqid NOT IN (8, 15, 17)
        ORDER BY
            d.isinreal";
} else if ($fond != "") {
    $query = "select d.cpnaz, menadenom as mena, d.isinreal, pocty.pocet, pocty.kurz,
                pocty.objem, pocty.objemref, pocty.kurz_mena
            --	f_obstar_hodnota_full(to_date('$dbdate','YYYY-MM-DD'), '$fond', d.isin) as obstar_hodnota
        from 
        (SELECT sum(case when md_d=0 then pocet else (-1)*pocet end) as pocet, 
                sum(case when md_d=0 then sumadenom else (-1)*sumadenom end) as objem, 
                f_menovy_kurz_lot(menadenom, '$refmena', datum)) as kurz,
                sum((case when md_d=0 then sumadenom else (-1)*sumadenom end) * kurz as objemref,
                kodaktiva,menadenom,max(kurzaktiva) as kurz, max(kurz) as kurz_mena
        from
        majetoktotal mt,
        dbequitycurrric dcr,
        dbequitycurr dc,
        dbequity d
        where
        mt.datum=to_date('$dbdate','YYYY-MM-DD') and
        mt.uctovnykod in (251200) and
        mt.subjektid in ($fond) and
        dcr.isincurrric = mt.kodaktiva and
        dc.isincurr = dcr.isincurr and
        d.isin = dc.isin and
        d.druheqid not in (8,15,17) -- neberieme tu ETF a index certif
        group by mt.kodaktiva,menadenom) pocty, dbequity d, dbequitycurr c, dbequitycurrric r
        where pocty.kodaktiva=r.isincurrric and d.isin=c.isin and r.isincurr=c.isincurr 
        ORDER BY d.isinreal
                ";
}
$resultRes = Connection::getDataFromDatabase($query, defaultDB);
$result = $resultRes[1];

foreach ($result as $key => $item) {
    $nazov = $item["cpnaz"];
    $mena = $item["mena"];
    $isinreal = $item["isinreal"];
    $pocet = $item["pocet"];
    $objem = $item["objem"];
    $kurz = $item["kurz"];
    $kurz_mena = $item["kurz_mena"];
    $obj = $item["objemref"];
    $p = ($obj / $total) * 100;
    $podiel = round($p, 3);
    $objem = round($item["objem"], 2);
    $objemref = round($item["objemref"], 2);
    $obstar_hodnota = round($item["obstar_hodnota"], 2);
    $obstar_hodnotaks = round(($pocet == 0) ? 0 : ($item["obstar_hodnota"] / $pocet), 4);

    ?>
    <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 border-gray-200">
        <td class="py-2 px-6"><?php echo $nazov ?></td>
        <td class="py-2 px-6"><?php echo $isinreal ?></td>
        <td class="py-2 px-6"><?php echo $mena ?></td>
        <td class="py-2 px-6"><?php echo $pocet ?></td>
        <td class="py-2 px-6"><?php echo round($kurz, 4) ?></td>
        <td class="py-2 px-6"><?php echo $objem ?></td>
        <td class="py-2 px-6"><?php echo round($kurz_mena, 4) ?></td>
        <td class="py-2 px-6"><?php echo $objemref ?></td>
        <td class="py-2 px-6"><?php echo $podiel ?></td>
    </tr>
    <?php
}
?>
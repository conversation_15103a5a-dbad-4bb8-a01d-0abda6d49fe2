<?php
$query = "
                WITH majetoktotal_filtered AS (SELECT n.popis,
                                      CASE
                                          WHEN mt.eqid = 'TD' THEN NULL
                                          ELSE mt.pocet
                                          END                                                    AS pocet,
                                      mt.ucetaktiva,
                                      mt.subjektid,
                                      mt.eqid,
                                      mt.menadenom,
                                      mt.sumadenom * CASE WHEN mt.md_d < 0.5 THEN -1 ELSE 1 END  AS sumadenom,
                                      mt.menaref,
                                      f_menovy_kurz_lot(mt.menadenom, 'EUR', '$dbdate'::DATE) AS kurz,
                                      mt.sumadenom * CASE WHEN mt.md_d < 0.5 THEN -1 ELSE 1 END *
                                      f_menovy_kurz_lot(mt.menadenom, 'EUR', '$dbdate'::DATE) AS sumaref,
                                      CASE
                                          WHEN mt.eqid IN ('Bonds', 'Fonds', 'Shares', 'Depo')
                                              OR mt.uctovnykod IN
                                                 (315601, 315602, 315603, 315604, 315605, 315606, 315607, 315608,
                                                  315609)
                                              THEN d.cpnaz
                                          ELSE NULL
                                          END                                                    AS kodaktiva,
                                      COALESCE(
                                              CASE
                                                  WHEN mt.uctovnykod IN (315121, 325121)
                                                      THEN (SELECT SUM(CASE WHEN mt2.md_d < 0.5 THEN -1 ELSE 1 END * mt2.sumadenom)
                                                            FROM majetoktotal mt2
                                                            WHERE mt.ucetaktiva = mt2.ucetaktiva
                                                              AND mt.kodaktiva = mt2.kodaktiva
                                                              AND mt.datum = mt2.datum
                                                              AND mt.subjektid = mt2.subjektid
                                                              AND mt.uctovnykod + 1 = mt2.uctovnykod
                                                              AND mt.eqid = mt2.eqid)
                                                  ELSE 0
                                                  END, 0
                                      )                                                          AS auvdenom
                               FROM majetoktotal mt
                                        LEFT JOIN dbequity d ON d.isin = SUBSTRING(mt.kodaktiva FROM 1 FOR 12)
                                        LEFT JOIN navuctovanie n ON mt.uctovnykod = n.uctovnykod
                               WHERE mt.datum = '$dbdate'::DATE
                                 AND mt.subjektid in ($fond)
                                 AND ((n.uctovnykod::text) LIKE '315%' OR (n.uctovnykod::text) LIKE '325%' OR (n.uctovnykod::text) LIKE '261%')
                                 AND mt.uctovnykod NOT IN (315122, 325122)
                                 AND NOT EXISTS (SELECT 1
                                                 FROM majetoktotal mt2
                                                 WHERE mt2.datum = mt.datum
                                                   AND mt2.uctovnykod IN (221110, 221210)
                                                   AND mt.uctovnykod IN (315132, 315113)
                                                   AND mt.eqid = mt2.eqid
                                                   AND mt.subjektid = mt2.subjektid
                                                   AND mt.kodaktiva = mt2.kodaktiva
                                                   AND mt.ucetaktiva = mt2.ucetaktiva))
                SELECT popis,
                       pocet,
                       ucetaktiva,
                       subjektid,
                       eqid,
                       menadenom,
                       sumadenom,
                       menaref,
                       kurz,
                       sumaref,
                       kodaktiva,
                       auvdenom
                FROM majetoktotal_filtered
                WHERE subjektid in ($fond)
                    order by popis, menadenom
                ";

$resultRes = Connection::getDataFromDatabase($query, defaultDB);
$result = $resultRes[1];

foreach ($result as $key => $item) {
    $popis = $item["popis"];
    $obj = $item["sumaref"] + $item["auvdenom"] * $item["kurz_lot"];
    $paz = ($obj / $total) * 100;
    $podiel = round($paz, 3);
    $mena = $item["menadenom"];
    $objem = round($item["sumadenom"] + $item["auvdenom"], 2);
    $objem_ref = round($obj, 2);
    $menovykurz = round($item["kurz"], 4);
    $subjektid = $item["subjektid"];
    $ucetaktiva = $item["ucetaktiva"];
    $pocet = $item["pocet"];
    if ($pocet != '')
        $pocet = round($pocet, 0);
    $kodaktiva = $item['kodaktiva'];
    ?>
    <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 border-gray-200">
        <td class="py-2 px-6"><?php echo $popis; ?></td>
        <td class="py-2 px-6"><?php echo $kodaktiva; ?></td>
        <td class="py-2 px-6"><?php echo $mena; ?></td>
        <td class="py-2 px-6"><?php echo $objem; ?></td>
        <td class="py-2 px-6"><?php echo $pocet; ?></td>
        <td class="py-2 px-6"><?php echo $menovykurz; ?></td>
        <td class="py-2 px-6"><?php echo $objem_ref; ?></td>
        <td class="py-2 px-6"><?php echo $podiel; ?>%</td>
    </tr>
    <?php
}
<?php
$query = "
                SELECT 
                    mt.ucetaktiva, 
                    subjektid,
                    eqid, 
                    menadenom, 
                    sumadenom*sign(0.5-md_d) as sumadenom, 
                    sumadenom * f_menovy_kurz_lot(menadenom,'$refmena',to_date('$dbdate','YYYY-MM-DD')) as sumaref,
                    COALESCE((select sumadenom*sign(0.5-md_d) from majetoktotal where 
                        subjektid=mt.subjektid
                        and datum=mt.datum
                        and uctovnykod in (315132,315113)
                        and eqid = mt.eqid
                        and kodaktiva = mt.kodaktiva
                        and ucetaktiva = mt.ucetaktiva
                        ),0) as auv, 
                    COALESCE((select sumadenom*sign(0.5-md_d)*f_menovy_kurz_lot(mt.menadenom,'$refmena',to_date('$dbdate','YYYY-MM-DD')) from majetoktotal where 
                        subjektid=mt.subjektid
                        and datum=mt.datum
                        and uctovnykod in (315132,315113)
                        and eqid = mt.eqid
                        and kodaktiva = mt.kodaktiva
                        and ucetaktiva = mt.ucetaktiva
                        ),0) as auvref 
                from 
                    majetoktotal mt
                where 
                    datum=to_date('$dbdate','YYYY-MM-DD')
                    and eqid in ('BU','TD')
                    and uctovnykod in (221110, 221210)
            ";
$query .= " and subjektid IN ($fond) order by eqid, menadenom";

$resultRes = Connection::getDataFromDatabase($query, defaultDB);
$result = $resultRes[1];
foreach ($result as $key => $item) {
    if ($item["eqid"] == 'BU') {
        if (substr($item["ucetaktiva"], -9) == 'kolateral') {
            $typ = 'Bežný účet - kolaterál';
        } else {
            $typ = 'Bežný účet';
        }
    } else

        $typ = 'Termínovaný vklad';
    $obj = $item["sumaref"] + $item["auvref"];
    $auv = round($item["auv"], 2);
    $p = ($obj / $total) * 100;
    $podiel = round($p, 3);
    $mena = $item["menadenom"];
    $objem = round($item["sumadenom"], 2);
    $objem_ref = round($obj, 2);
    $menovykurz = round($item["kurz"], 4);
    $subjektid = $item["subjektid"];
    $ucetaktiva = $item["ucetaktiva"];
    $z_td = "";
    $k_td = "";
    $ir_td = "";

    if ($typ == 'Termínovaný vklad') {
        $query3 = " select z_td, k_td, ir_td from konfirmaciaktv 
					where 
						subjektid = $subjektid 
						and Z_TD <= to_date('$dbdate','YYYY-MM-DD')
						and K_TD >= to_date('$dbdate','YYYY-MM-DD') 
						and CUTD = '$ucetaktiva'
                        LIMIT 1";

        $result3Res = Connection::getDataFromDatabase($query3, defaultDB);
        $result3 = $result3Res[1][0];
        if (!$result3)        // ak sme nic nenasli, pozreme este cez pool
        {
            $query3 = "select z_td, k_td, ir_td from konfirmaciaktv k, pool p, pooldetailreal pdr 
					where 
						k.subjektid = 0 
						and k.Z_TD <= to_date('$dbdate','YYYY-MM-DD')
						and k.K_TD >= to_date('$dbdate','YYYY-MM-DD') 
						and k.CUTD = '$ucetaktiva'
						and k.dealid = p.dealid
						and pdr.poolid = p.poolid
						and pdr.subjektid = $subjektid
						LIMIT 1";
            $result3Res = Connection::getDataFromDatabase($query3, defaultDB);
            $result3 = $result3Res[1][0];
        }
        $z_td = $result3["z_td"];
        $k_td = $result3["k_td"];
        $ir_td = $result3["ir_td"];
        $ir_td = round($ir_td, 2);
    }
    ?>
    <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 border-gray-200">
        <td class="py-2 px-6"><?php echo $typ; ?></td>
        <td class="py-2 px-6"><?php echo $mena; ?></td>
        <?php if ($fond < 2 and $typ == 'Bežný účet') { ?>
            <td colspan=2 class="py-2 px-6"><?php echo $ucetaktiva; ?></td>
        <?php } else { ?>
            <td class="py-2 px-6"><?php echo $z_td; ?></td>
            <td class="py-2 px-6"><?php echo $k_td; ?></td>
        <?php } ?>
        <td class="py-2 px-6"><?php echo $objem; ?></td>
        <td class="py-2 px-6"><?php echo $auv; ?></td>
        <td class="py-2 px-6"><?php echo $ir_td; ?></td>
        <td class="py-2 px-6"><?php echo $menovykurz; ?></td>
        <td class="py-2 px-6"><?php echo $objem_ref; ?> €</td>
        <td class="py-2 px-6"><?php echo $podiel; ?>%</td>
    </tr>
    <?php
}
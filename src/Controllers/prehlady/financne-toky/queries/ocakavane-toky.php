<?php
$activity = "12,15,18";
$query = "SELECT
                        sum(	
                            round(
                                CASE 
                                    WHEN md_d = 0 THEN md_d0
                                    WHEN md_d = 1 THEN md_d1
                                END * 
                                COALESCE(d.nominalemisie * COALESCE(f.faktor, 1), 1)*
                                m.pocet*		
                                f.faktor*
                                (f.kupon/100)*
                                f_koef_auv_isincurrric(dcr.isincurrric,d.zaklad,d.dateemisie,d.maturitydate,f.datesplatnost,d.prvy_kupon,d.posledny_kupon,d.istfrek,d.exfrekkup,2)
                            ,COALESCE(d.rounding,25))
                        ) as urok,
                        0 as istina,
                        to_char(f.datesplatnost,'dd.mm.yyyy') as mydatum,
                        f.datesplatnost,
                        d.cpnaz,
                        m.menadenom,
                        min(
                            case
                                when (po.typ_zdanenia = 'FO') then COALESCE(d.sadz<PERSON><PERSON>, 0)
                                when (po.typ_zdanenia = 'PO') then COALESCE(d.sadz<PERSON><PERSON>, 0)
                                when (po.typ_zdanenia = 'NO') then COALESCE(d.sadzbano, 0)			
                                else 0	
                            end
                        ) as dan,
                        'kupon' as typ,
                        m.subjektid,
                        p.cislozmluvy
                    from
                        majetoktotal m,
                        floatkupon f,
                        dbequity d,
                        dbequitycurr dc,
                        dbequitycurrric dcr,
                        navuctovanie n,
                        portfolio p, 
                        podielnik po
                    where
                        m.datum=to_date('$dbdate','YYYY-MM-DD') and
                        m.uctovnykod in (315124) and
                        m.subjektid in ($fond) and
                        f.isincurrric = m.kodaktiva and
                        f.datesplatnost > m.datum and
                        dcr.isincurrric = m.kodaktiva and
                        dc.isincurr = dcr.isincurr and
                        d.isin = dc.isin and
                        m.uctovnykod = n.uctovnykod and	
                        p.fondid = m.subjektid  and 
                        (is_exdate(m.kodaktiva,m.datum, 0) = 1)
                        and ((f.datesplatnost-COALESCE(d.exfrekkup,0)) <= m.datum) 
                        and (is_coupon_date(m.kodaktiva,m.datum) = 0)
                        and p.PODIELNIKID = po.PODIELNIKID 
                    group by
                        m.kodaktiva,
                        f.datesplatnost,
                        d.cpnaz,
                        m.menadenom,
                        d.dan,
                        m.subjektid,
                        p.cislozmluvy,
                        po.typ_zdanenia
                    
                    union all
                    
                    select
                        sum(	
                            round(
                                CASE 
    WHEN md_d = 0 THEN md_d0
    WHEN md_d = 1 THEN md_d1
END * 
                                COALESCE(d.nominalemisie * COALESCE(f.faktor, 1), 1) *
                                m.pocet*		
                                (f.kupon/100)*
                                f_koef_auv_isincurrric(dcr.isincurrric,d.zaklad,d.dateemisie,d.maturitydate,f.datesplatnost,d.prvy_kupon,d.posledny_kupon,d.istfrek,d.exfrekkup,2)
                            ,COALESCE(d.rounding,25))
                        ) as urok,
                        0 as istina,
                        to_char(f.datesplatnost,'dd.mm.yyyy') as mydatum,
                        f.datesplatnost,
                        d.cpnaz,
                        m.menadenom,
                        min(
                            case
                                when (po.typ_zdanenia = 'FO') then COALESCE(d.sadzbafo, 0)
                                when (po.typ_zdanenia = 'PO') then COALESCE(d.sadzbapo, 0)
                                when (po.typ_zdanenia = 'NO') then COALESCE(d.sadzbano, 0)			
                                else 0	
                            end
                        ) as dan,
                        'kupon' as typ,
                        m.subjektid,
                        p.cislozmluvy
                    from
                        majetoktotal m,
                        floatkupon f,
                        dbequity d,
                        dbequitycurr dc,
                        dbequitycurrric dcr,
                        navuctovanie n,
                        portfolio p, 
                        podielnik po
                    where
                        m.datum=to_date('$dbdate','YYYY-MM-DD') and
                        m.uctovnykod in (251110,325121,315121) and
                        m.subjektid in ($fond) and
                        f.isincurrric = m.kodaktiva and
                        f.datesplatnost > m.datum and
                        dcr.isincurrric = m.kodaktiva and
                        dc.isincurr = dcr.isincurr and
                        d.isin = dc.isin and
                        m.uctovnykod = n.uctovnykod and	
                        p.fondid = m.subjektid  and not
                        ((is_exdate(m.kodaktiva,m.datum, 0) = 1)
                        and ((f.datesplatnost-COALESCE(d.exfrekkup,0)) <= m.datum) 
                        and (is_coupon_date(m.kodaktiva,m.datum) = 0))
                        and p.PODIELNIKID = po.PODIELNIKID 
                    group by
                        m.kodaktiva,
                        f.datesplatnost,
                        d.cpnaz,
                        m.menadenom,
                        d.dan,
                        m.subjektid,
                        p.cislozmluvy,
                        po.typ_zdanenia

                    union all

                    select	
                        0 as urok,
                        round(
                        CASE 
    WHEN md_d = 0 THEN md_d0
    WHEN md_d = 1 THEN md_d1
END *
                        COALESCE(d.nominalemisie * COALESCE(f.faktor, 1), 1)*
                        (
                        case
                        when is_exdate(m.kodaktiva,m.datum, 1) = 1
                            then COALESCE((select pocet from majetoktotal m2 where m2.datum=m.datum and m2.subjektid=m.subjektid and m2.kodaktiva = m.kodaktiva and m2.uctovnykod=315123),0)
                        else m.pocet
                        end
                        )*
                        f_istina(m.kodaktiva,f.datesplatnost)/100
                        ,COALESCE(d.rounding,25)) as istina,
                        to_char(f.datesplatnost,'dd.mm.yyyy') as mydatum,
                        f.datesplatnost,	
                        d.cpnaz,
                        m.menadenom,
                        d.dan,
                        'istina' as typ,
                        m.subjektid,
                        p.cislozmluvy
                    from
                        majetoktotal m,
                        floatkupon f,
                        dbequity d,
                        dbequitycurr dc,
                        dbequitycurrric dcr,
                        navuctovanie n,
                        portfolio p
                    where
                        m.datum=to_date('$dbdate','YYYY-MM-DD') and
                        m.uctovnykod in (251110,325121,315121) and
                        m.subjektid in ($fond) and
                        f.isincurrric = m.kodaktiva and
                        f.datesplatnost > m.datum and
                        dcr.isincurrric = m.kodaktiva and
                        dc.isincurr = dcr.isincurr and
                        d.isin = dc.isin and
                        f.istina != 0 and
                        m.uctovnykod = n.uctovnykod and	
                        p.fondid = m.subjektid

                    order by 
                        datesplatnost,
                        subjektid,
                        cpnaz
                    ";
$resultRes = Connection::getDataFromDatabase($query, defaultDB);
$result = $resultRes[1];

foreach ($result as $key => $item) {
    $klient = $item["cislozmluvy"];
    $datum = $item["mydatum"];
    $nastroj = $item["cpnaz"];
    $mena = $item["menadenom"];
    $dan = $item["dan"];
    $urok = $item["urok"];
    $istina = $item["istina"];
    $dan = $urok / 100 * $dan;
    $istina = round($istina, 2);
    $urok = round($urok, 2);
    $dan = round($dan, 2);
    ?>
    <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 border-gray-200">
        <td class="py-2 px-6"><?php echo $datum ?></td>
        <td class="py-2 px-6"><?php echo $klient ?></td>
        <td class="py-2 px-6"><?php echo $nastroj ?></td>
        <td class="py-2 px-6"><?php echo $mena ?></td>
        <td class="py-2 px-6"><?php echo $istina ?></td>
        <td class="py-2 px-6"><?php echo $urok ?></td>
        <td class="py-2 px-6"><?php echo $dan ?></td>
    </tr>
    <?php
}
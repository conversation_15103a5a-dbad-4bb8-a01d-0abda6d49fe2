<?php
$query = "WITH majetoktotal_filtered AS (
    SELECT
        mt.ucetaktiva,
        mt.kodaktiva,
        mt.datum,
        mt.subjektid,
        mt.menadenom,
        SUM(
            CASE
                WHEN mt.md_d = 0 THEN mt.pocet
                ELSE (-1) * mt.pocet
            END
        ) AS pocet,
        MAX(mt.kurzaktiva) AS kurz,
        SUM(
            CASE
                WHEN mt.md_d = 0 THEN mt.sumadenom
                ELSE (-1) * mt.sumadenom
            END
        ) AS sumadenom,
        f_menovy_kurz_lot(mt.menadenom, '$refmena', mt.datum) AS kurz_lot,
        MAX(
            f_menovy_kurz(mt.menadenom, '$refmena', mt.datum, 1)
        ) AS kurz_mena
    FROM
        majetoktotal mt
    WHERE
        mt.datum = to_date('$dbdate', 'YYYY-MM-DD')
        AND mt.eqid = 'Bonds'
        AND mt.uctovnykod = 251110
        AND mt.subjektid in ($fond)
    GROUP BY
        mt.ucetaktiva,
        mt.kodaktiva,
        mt.datum,
        mt.subjektid,
        mt.menadenom
),
pocty AS (
    SELECT
        mtf.kodaktiva,
        mtf.menadenom,
        mtf.pocet,
        mtf.kurz,
        mtf.kurz_lot,
        mtf.kurz_mena,
        SUM(
            CASE
                WHEN mt2.md_d = 0 THEN mt2.sumadenom
                ELSE (-1) * mt2.sumadenom
            END
        ) FILTER (
            WHERE
                mt2.uctovnykod = 251120
        ) AS auv,
        SUM(
            CASE
                WHEN mt2.md_d = 0 THEN mt2.sumadenom
                ELSE (-1) * mt2.sumadenom
            END
        ) FILTER (
            WHERE
                mt2.uctovnykod = 251110
        ) AS objemsauv,
        SUM(
            (
                CASE
                    WHEN mt2.md_d = 0 THEN mt2.sumadenom
                    ELSE (-1) * mt2.sumadenom
                END
            ) * mtf.kurz_lot
        ) AS objemref
    FROM
        majetoktotal_filtered mtf
        LEFT JOIN majetoktotal mt2 ON mtf.ucetaktiva = mt2.ucetaktiva
        AND mtf.kodaktiva = mt2.kodaktiva
        AND mtf.datum = mt2.datum
        AND mtf.subjektid = mt2.subjektid
    WHERE
        mt2.uctovnykod IN (251110, 251120)
    GROUP BY
        mtf.kodaktiva,
        mtf.menadenom,
        mtf.pocet,
        mtf.kurz,
        mtf.kurz_lot,
        mtf.kurz_mena
)
SELECT
    COALESCE(d.nominalemisie * COALESCE(f.faktor, 1), 1) as nominalemisie,
    d.cpnaz,
    pocty.menadenom AS mena,
    d.isinreal,
    d.isin,
    d.maturitydate AS datsplatnosti,
    d.kupon,
    pocty.pocet,
    pocty.objemsauv,
    pocty.objemref,
    pocty.kurz,
    pocty.auv,
    pocty.kurz_mena
FROM
    pocty
    JOIN dbequitycurrric r ON pocty.kodaktiva = r.isincurrric
         JOIN dbequitycurr c ON r.isincurr = c.isincurr
         JOIN dbequity d ON d.isin = c.isin
    LEFT JOIN floatkupon f ON r.isincurrric = f.isincurrric AND f.datefrom <= TO_DATE('$dbdate', 'YYYY-MM-DD') AND f.datetill >= TO_DATE('$dbdate', 'YYYY-MM-DD')
ORDER BY
    d.isinreal";

$resultRes = Connection::getDataFromDatabase($query, defaultDB);
$result = $resultRes[1];

foreach ($result as $key => $item) {
    $nazov = $item["cpnaz"];
    $mena = $item["mena"];
    $kurz = $item["kurz"];
    $kurz_mena = $item["kurz_mena"];
    $isinreal = $item["isinreal"];
    $pocet = $item["pocet"];
    $auv = round($item["auv"], 2);
    $objemsauv = round($item["objemsauv"], 2);
    $obj = $item["objemref"];
    $p = ($obj / $total) * 100;
    $podiel = round($p, 3);
    $objemref = round($item["objemref"], 2);
    ?>
    <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 border-gray-200">
        <td class="py-2 px-6"><?php echo $nazov ?></td>
        <td class="py-2 px-6"><?php echo $isinreal ?></td>
        <td class="py-2 px-6"><?php echo $mena ?></td>
        <td class="py-2 px-6"><?php echo $pocet ?></td>
        <td class="py-2 px-6"><?php echo round($kurz, 4) ?></td>
        <td class="py-2 px-6"><?php echo $objemsauv ?></td>
        <td class="py-2 px-6"><?php echo $auv ?></td>
        <td class="py-2 px-6"><?php echo round($kurz_mena, 4) ?></td>
        <td class="py-2 px-6"><?php echo $objemref ?></td>
        <td class="py-2 px-6"><?php echo $podiel ?>%</td>
    </tr>
    <?php
}
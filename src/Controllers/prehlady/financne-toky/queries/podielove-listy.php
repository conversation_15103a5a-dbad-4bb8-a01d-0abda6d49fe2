<?php
$query = "WITH pocty AS (
    SELECT 
        mt.kodaktiva,
        mt.menadenom AS mena,
        SUM(CASE WHEN mt.md_d = 0 THEN mt.pocet ELSE (-1) * mt.pocet END) AS pocet,
        SUM(CASE WHEN mt.md_d = 0 THEN mt.sumadenom ELSE (-1) * mt.sumadenom END) AS objem,
        f_menovy_kurz_lot(mt.menadenom, '$refmena', mt.datum) AS kurz,
        SUM(CASE WHEN mt.md_d = 0 THEN mt.sumadenom ELSE (-1) * mt.sumadenom END) * f_menovy_kurz_lot(mt.menadenom, 'EUR', mt.datum) AS objemref,
        MAX(mt.kurzaktiva) AS kurz1,
        MAX(kurz) AS kurz_mena
    FROM 
        majetoktotal mt
    JOIN 
        dbequitycurrric dcr ON dcr.isincurrric = mt.kodaktiva
    JOIN 
        dbequitycurr dc ON dc.isincurr = dcr.isincurr
    JOIN 
        dbequity d ON d.isin = dc.isin
    WHERE 
        mt.datum = TO_DATE('$dbdate', 'YYYY-MM-DD') 
        AND mt.uctovnykod IN (251200, 251300)
        AND mt.subjektid = $fond
        AND (
            d.eqid = 'Fonds' 
            OR (d.druheqid IN (8, 15, 17) AND d.eqid = 'Shares')
        )
    GROUP BY 
        mt.kodaktiva, mt.menadenom, mt.datum, dcr.kurz
)
SELECT 
    d.cpnaz, 
    pocty.mena, 
    d.isinreal, 
    pocty.pocet, 
    pocty.kurz, 
    pocty.objem, 
    pocty.objemref, 
    pocty.kurz_mena
FROM 
    pocty
JOIN 
    dbequitycurrric r ON pocty.kodaktiva = r.isincurrric
JOIN 
    dbequitycurr c ON r.isincurr = c.isincurr
JOIN 
    dbequity d ON d.isin = c.isin;";

$resultRes = Connection::getDataFromDatabase($query, defaultDB);
$result = $resultRes[1];

foreach ($result as $key => $item) {
    $nazov = $item["cpnaz"];
    $mena = $item["mena"];
    $isinreal = $item["isinreal"];
    $pocet = $item["pocet"];
    $kurz = $item["kurz"];
    $kurz_mena = $item["kurz_mena"];
    $objem = $item["objem"];
    $obj = $item["objemref"];
    $p = ($obj / $total) * 100;
    $podiel = round($p, 3);
    $objem = round($item["objem"], 2);
    $objemref = round($item["objemref"], 2);
    $obstar_hodnota = round($item["obstar_hodnota"], 2);
    $obstar_hodnotaks = round(($pocet == 0) ? 0 : ($item["obstar_hodnota"] / $pocet), 2);
    ?>
    <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 border-gray-200">
        <td class="py-2 px-6"><?php echo $nazov ?></td>
        <td class="py-2 px-6"><?php echo $isinreal ?></td>
        <td class="py-2 px-6"><?php echo $mena ?></td>
        <td class="py-2 px-6"><?php echo $pocet ?></td>
        <td class="py-2 px-6"><?php echo round($kurz, 4) ?></td>
        <td class="py-2 px-6"><?php echo $objem ?></td>
        <td class="py-2 px-6"><?php echo round($kurz_mena, 4) ?></td>
        <td class="py-2 px-6"><?php echo $objemref ?></td>
        <td class="py-2 px-6"><?php echo $podiel ?>%</td>
    </tr>
    <?php
}
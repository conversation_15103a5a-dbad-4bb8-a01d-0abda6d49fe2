<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";


$par = $_POST["menovypar"];
$menovyPar = Connection::getDataFromDatabase("SELECT * FROM menovypar WHERE par = '$par'", defaultDB)[1][0];

?>


<div class="grid gap-4 sm:grid-cols-2 sm:gap-6">
    <div>
        <label for="menovypar" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Menový pár</label>
        <input type="text" id="menovypar" name="menovypar" readonly="true" value="<?php echo $menovyPar["par"] ?>"
            class="block w-full p-2 text-gray-900 border border-gray-300 rounded-lg bg-gray-50 text-xs focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
    </div>
    <div>
        <label for="nacitatkurz" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Načítať
            kurz</label>
        <select id="nacitatkurz" name="nacitatkurz"
            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
            <option value="1" <?php echo $menovyPar["nacitatkurz"] == 1 ? "selected" : "" ?>>Áno</option>
            <option value="0" <?php echo $menovyPar["nacitatkurz"] == 0 ? "selected" : "" ?>>Nie</option>
        </select>
    </div>
    <div class="sm:col-span-2">
        <label for="lot" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Lot</label>
        <input type="text" id="lot" name="lot" decimal="5" size="12" value="<?php echo $menovyPar["lot"] ?>"
            class="block w-full p-2 text-gray-900 border border-gray-300 rounded-lg bg-gray-50 text-xs focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
    </div>
    <div class="sm:col-span-2">
        <label for="rezim" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Režim</label>
        <select id="rezim" name="rezim"
            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
            <option value="DIV" <?php echo $menovyPar["rezim"] == "DIV" ? "selected" : "" ?>>delenie kurzu
            </option>
            <option value="MUL" <?php echo $menovyPar["rezim"] == "MUL" ? "selected" : "" ?>>násobenie
                kurzu</option>
        </select>
    </div>
</div>
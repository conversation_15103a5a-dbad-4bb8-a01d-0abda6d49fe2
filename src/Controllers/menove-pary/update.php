<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
require_once "/home/<USER>/www/src/lib/notifications/Notification.class.php";

$sess_userid = $_SESSION["user"]["data"]["userid"];
$username = $_SESSION["user"]["data"]["username"];

if ($_POST["menovypar"] === "") { ?>
    <script>
        alert("Menový pár nemôže byť prázdny");
    </script>
    <?php
    exit();
}

$updateMenovyPar = Connection::InsertUpdateCreateDelete(
    "UPDATE menovypar SET kurz = ?, rezim = ?, lot = ? WHERE par = ?",
    [
        $_POST["nacitatkurz"],
        $_POST["rezim"],
        $_POST["lot"],
        $_POST["menovypar"]
    ],
    defaultDB
);
if (gettype($updateMenovyPar) === "integer") {
    $notification = new Notification(
        0,
        'menovypar',
        0,
        "update",
        $sess_userid,
        $username,
        "updateMenovyPar",
        json_encode(["par", $_POST["menovypar"]]),
        false,
        null,
        "user",
        "Aktualizoval menový pár <strong>" . $_POST["menovypar"] . "</strong>"
    );
    $notification->createNotifcation();
    ?>
    <script>
        document.body.style.overflow = "auto";
        htmx.ajax('GET', window.location.pathname + window.location.search, {
            target: "#pageContentMain",
        });
    </script>
    <?php
} else {
    echo "error" . $updateMenovyPar;
}
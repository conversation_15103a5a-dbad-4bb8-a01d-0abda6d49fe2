<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
require_once "/home/<USER>/www/src/lib/notifications/Notification.class.php";

$sess_userid = $_SESSION["user"]["data"]["userid"];
$username = $_SESSION["user"]["data"]["username"];

if ($_POST["menovypar"] === "") { ?>
    <script>
        alert("Menový pár nemôže byť prázdny");
    </script>
    <?php
    exit();
}

$insertIntoMenovyPar = Connection::InsertUpdateCreateDelete(
    "INSERT INTO menovypar (par, kurz, rezim, lot) VALUES (?, ?, ?, ?)",
    [$_POST["menovypar"], $_POST["nacitatkurz"], $_POST["rezim"], $_POST["lot"]],
    defaultDB
);
if (str_contains($insertIntoMenovyPar, "Unique violation")) {
    ?>
    <script>
        alert("Menový pár už existuje");
    </script>
    <?php
    exit();
} else if (gettype($insertIntoMenovyPar) === "integer") {
    $notification = new Notification(
        0,
        'menovypar',
        0,
        "create",
        $sess_userid,
        $username,
        "createMenovyPar",
        json_encode(["par", $_POST["menovypar"]]),
        false,
        null,
        "user",
        "Vytvoril menový pár <strong>" . $_POST["menovypar"] . "</strong>"
    );
    $notification->createNotifcation();
    ?>
        <script>
            document.body.style.overflow = "auto";
            htmx.ajax('GET', window.location.pathname + window.location.search, {
                target: "#pageContentMain",
            });
        </script>
    <?php
} else {
    echo "error" . $insertIntoMenovyPar;
}
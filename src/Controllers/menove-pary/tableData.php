<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

$query = $_POST["search"];
if ($query !== "") {
    $searchQuery = " WHERE par LIKE '%$query%'";
} else {
    $searchQuery = "";
}

$menoveParicky = Connection::getDataFromDatabase("SELECT * FROM menovypar $searchQuery", defaultDB)[1];

foreach ($menoveParicky as $menovePar) { ?>
    <tr class="border-b transition-all cursor-pointer dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-600">
        <th scope="row" class="px-4 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
            <?php echo $menovePar["par"]; ?>
        </th>
        <td class="px-4 py-4">
            <?php echo $menovePar["kurz"]; ?>
        </td>
        <td class="px-4 py-4">
            <?php echo $menovePar["lot"]; ?>
        </td>
        <td class="px-4 py-4 flex items-center gap-2">
            <form hx-post="/api/menove-pary/edit" hx-target="#edit-form" hx-trigger="click">
                <input type="hidden" name="menovypar" value="<?php echo $menovePar["par"]; ?>" />
                <?php $editButton = new Button("", "edit-menove-pary", "submit", "edit-modal", "alternative", "", "pencil", "left");
                $editButton->render();
                ?>
            </form>
            <form hx-post="/api/menove-pary/delete" hx-target="#menove_paricky" hx-trigger="click">
                <input type="hidden" name="menovypar" value="<?php echo $menovePar["par"]; ?>" />
                <?php $deleteButton = new Button("", "delete-menove-pary", "submit", "", "delete", "", "trash", "left");
                $deleteButton->render();
                ?>
            </form>
        </td>
    </tr>
<?php }
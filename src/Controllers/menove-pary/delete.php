<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
require_once "/home/<USER>/www/src/lib/notifications/Notification.class.php";

$sess_userid = $_SESSION["user"]["data"]["userid"];
$username = $_SESSION["user"]["data"]["username"];

$par = $_POST["menovypar"];

$deleteMenovyPar = Connection::InsertUpdateCreateDelete("DELETE FROM menovypar WHERE par = ?", [$par], defaultDB);
if (gettype($deleteMenovyPar) === "integer") {
    $notification = new Notification(
        0,
        'menovypar',
        0,
        "delete",
        $sess_userid,
        $username,
        "deleteMenovyPar",
        json_encode(["par", $par]),
        false,
        null,
        "user",
        "Odstránil menov<PERSON> pár <strong>" . $par . "</strong>"
    );
    $notification->createNotifcation();
    ?>
    <script>
        document.body.style.overflow = "auto";
        htmx.ajax('GET', window.location.pathname + window.location.search, {
            target: "#pageContentMain",
        });
    </script>
    <?php
} else {
    echo "error" . $deleteMenovyPar;
}
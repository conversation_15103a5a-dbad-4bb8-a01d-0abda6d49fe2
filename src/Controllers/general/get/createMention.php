<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
require_once "/home/<USER>/www/src/lib/notifications/Mention.class.php";

$mentionColumn = $_POST["mentioncolumn"];
$mentionData = $_POST["mentiondata"];
$mentionedID = $_POST["mentionedid"];
$initiatorID = $_POST["initiatorid"];
$action = $_POST["action"];
$operation = $_POST["operation"];

echo "SELECT * FROM notifications WHERE $mentionColumn = $mentionData";

$notificationDetail = Connection::getDataFromDatabase("SELECT * FROM notifications WHERE $mentionColumn = $mentionData", defaultDB)[1][0];

$notification = new Mention($notificationDetail["id"], $notificationDetail["destinacia"], $notificationDetail["objektid"], $initiatorID, $mentionedID, $notificationDetail["notification"] . $operation, json_encode(["dealid", $notificationDetail["objektid"]]), $action);
$notification->createMention();

$notifDetail = json_decode($notificationDetail["objektdetail"]);
$column = $notifDetail[0];
$data = "'$notifDetail[1]'";
$table = $notificationDetail["destinacia"];

if ($action === "delete") {
    $updateDataTable = Connection::InsertUpdateCreateDelete("UPDATE $table SET assigneduserid = ? WHERE $column = $data", [NULL], defaultDB);
} else {
    $updateDataTable = Connection::InsertUpdateCreateDelete("UPDATE $table SET assigneduserid = ? WHERE $column = $data", [$mentionedID], defaultDB);
}

if (gettype($updateDataTable) !== "integer") {
    $errors[] = "UpdateDataTable ERROR: " . $updateDataTable;
}

$updateNotification = Connection::InsertUpdateCreateDelete("UPDATE notifications SET mentioneduserid = ? WHERE id = ?", [$mentionedID, $notificationDetail["id"]], defaultDB);
if (gettype($updateNotification) !== "integer") {
    $errors[] = "UpdateNotification ERROR: " . $updateNotification;
}

print_r($notificationDetail);
echo "<br><br>";
echo "UPDATE notifications SET mentioneduserid = $mentionedID WHERE id = " . $notificationDetail["id"];

if (empty($errors)) {
    if ($mentionColumn === "id") {
        include "src/Controllers/general/get/updatedNotifications.php";
    } else { ?>
        <script>
            window.location.reload();
        </script>
    <?php }
} else {
    print_r($errors);
}

<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

$userid = $_SESSION["user"]["data"]["userid"];
$users = Connection::getDataFromDatabase("SELECT userid, username, usernick, logged FROM users WHERE lasttime IS NOT NULL AND userid IS DISTINCT FROM $userid ORDER BY lasttime DESC", defaultDB)[1];
$notifID = isset($_GET["notifID"]) ? $_GET["notifID"] : "";
$operation = $_GET["action"];

if ($notifID === "" && isset($_GET["dealid"])) {
    $mentionColumn = "objektid";
    $mentionData = $_GET["dealid"];
} else if ($notifID !== "" && isset($_GET["notifID"])) {
    $mentionColumn = "id";
    $mentionData = $notifID;
}

?>
<ul class="h-48 p-2 overflow-y-auto no-scrollbar text-gray-700 dark:text-gray-200"
    aria-labelledby="dropdownUsersButton">
    <?php foreach ($users as $key => $user) { ?>
        <li class="cursor-pointer">
            <form hx-post="/api/mentionUserToAction" hx-target="#notificationsWrapper" class="mb-0">
                <input type="hidden" name="initiatorid" id="initiatorid" value="<?php echo $userid; ?>" />
                <input type="hidden" name="mentionedid" id="mentionedid" value="<?php echo $user["userid"]; ?>" />
                <input type="hidden" name="mentioncolumn" id="mentioncolumn" value="<?php echo $mentionColumn; ?>" />
                <input type="hidden" name="mentiondata" id="mentiondata" value="<?php echo $mentionData; ?>" />
                <input type="hidden" name="action" id="action" value="create" />
                <input type="hidden" name="operation" id="operation" value="<?php echo $operation; ?>" />
                <button type="submit"
                    class="flex items-center w-full px-4 rounded-md cursor-pointer py-2 gap-4 transition-all <?php echo $user["logged"] ? "dark:hover:bg-green-700 hover:bg-green-200" : "dark:hover:bg-gray-500 hover:bg-gray-100"; ?>">
                    <div class="relative">
                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-circle-user-round">
                            <path d="M18 20a6 6 0 0 0-12 0" />
                            <circle cx="12" cy="10" r="4" />
                            <circle cx="12" cy="12" r="10" />
                        </svg>
                    </div>
                    <section class="flex items-start w-full justify-between">
                        <div class="text-left">
                            <p class="text-sm font-bold truncate"><?php echo $user["username"]; ?>
                            </p>
                            <p class="text-xs dark:text-gray-300 text-gray-500 truncate">@<?php echo $user["usernick"]; ?>
                            </p>
                        </div>
                        <?php if ($user["logged"]) { ?>
                            <span
                                class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-sm dark:bg-green-900 dark:text-green-300">Online</span>
                        <?php } else { ?>
                            <span
                                class="bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded-sm dark:bg-gray-600 dark:text-gray-300">Offline</span>
                        <?php } ?>
                    </section>
                </button>
            </form>
        </li>
    <?php } ?>
</ul>
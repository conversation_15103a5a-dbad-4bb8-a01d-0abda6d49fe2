<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
require_once "/home/<USER>/www/src/lib/notifications/renderNotification.class.php";
require_once "/home/<USER>/www/src/lib/notifications/renderMention.class.php";

$userid = $_SESSION["user"]["data"]["userid"];
$dateNow = Connection::getDataFromDatabase("SELECT last_readed FROM users WHERE userid = $userid", defaultDB)[1][0]["last_readed"];

$notificationsQuery = "SELECT n.*, u.last_readed, u.username, u.usernick FROM notifications n JOIN users u ON u.last_readed >= '$dateNow' AND u.userid = $userid AND n.datetimeactivity > '$dateNow' ORDER BY n.datetimeactivity DESC";
$notifications = Connection::getDataFromDatabase($notificationsQuery, defaultDB);

$mentionsQuery = "SELECT m.*, u.last_readed, u.username, u.usernick, i.username as initiatorusername, i.usernick as initiatorusernick
FROM mentions m
         JOIN users u ON u.last_readed < '$dateNow' AND u.userid = $userID AND
                         m.datetimeactivity < '$dateNow'
         JOIN users i ON i.userid = m.initiatorid
WHERE mentionedid = $userID
ORDER BY m.datetimeactivity DESC";
$mentions = Connection::getDataFromDatabase($mentionsQuery, defaultDB);

print_r($mentions);

if ($notifications[0] !== 0) { ?>
    <span id="notifCountHiddenAfter" class="hidden"><?php echo $notifications[0]; ?></span>
    <div id="notificationsWrapper" class="px-5 py-3 flex flex-col gap-4">
        <?php foreach ($notifications[1] as $key => $item) {
            $notif = new RenderNotification($item["activityid"], $item["destinacia"], $item["objektid"], $item["action"], $userid, $item["username"], $item["notification"], $item["objektdetail"], $item["needsmentioning"]); ?>
            <div class="w-full max-w-md bg-white shadow-md rounded-lg overflow-hidden">
                <div class="p-3 flex space-x-4">
                    <div class="flex-shrink-0">
                        <img alt="user photo" class="w-8 h-8 rounded-full" src="/src/assets/img/user.png" />
                    </div>
                    <div class="flex-1 min-w-0">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-bold text-gray-900 truncate"><?php echo $item["username"]; ?>
                                </p>
                                <p class="text-xs text-gray-500 truncate">@<?php echo $item["usernick"]; ?></p>
                            </div>
                            <div class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium shadow-md text-white"
                                style="background-color: <?php echo $notif->getNotificationColor(); ?>">
                                <span class="capitalize"><?php echo $item["action"]; ?></span>
                            </div>
                        </div>
                        <p class="mt-1 text-sm text-gray-600"><?php $notif->render($item["id"]); ?></p>
                        <p class="mt-1 text-xs text-gray-400"><?php echo $item["time"]; ?></p>
                    </div>
                </div>
            </div>
        <?php } ?>
    </div>
<?php } else { ?>
    <span id="notifCountHiddenAfter" class="hidden"><?php echo $notifications[0]; ?></span>
    <div class="flex flex-col items-center justify-center p-8 bg-white rounded-lg h-full shadow-md">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
            stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-24 h-24 text-gray-300 mb-4">
            <path
                d="M21.2 8.4c.5.38.8.97.8 1.6v10a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V10a2 2 0 0 1 .8-1.6l8-6a2 2 0 0 1 2.4 0l8 6Z" />
            <path d="m22 10-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 10" />
        </svg>
        <h3 class="text-xl font-semibold text-gray-700 mb-2">Momentálne nemáte žiadne notifikácie</h3>
        <p class="text-gray-500 text-center">
            Všetko máte prečítané! Skontrolujte tento panel neskôr.
        </p>
    </div>
<?php } ?>
<?php
require "/home/<USER>/www/src/lib/connection.php";
require "/home/<USER>/www/conf/settings.php";
$sektory = Connection::getDataFromDatabase("SELECT * FROM sektor_esa95", defaultDB)[1];
$odvetvie = Connection::getDataFromDatabase("SELECT * FROM equityodvetvie", defaultDB)[1];
$meny = Connection::getDataFromDatabase("SELECT id, mena FROM menadb ORDER BY poradie", defaultDB)[1];
$clientID = isset($matches[1]) ? $matches[1] : null;
$dlhopis = Connection::getDataFromDatabase("SELECT * FROM dbequity WHERE isin = '$clientID'", defaultDB)[1][0];
$druhRes = Connection::getDataFromDatabase("SELECT * FROM equitydruh", defaultDB);
$druheqid = $druhRes[1];
?>
<form id="editInfoForm" hx-post="/api/aktiva/updateInfo" hx-target="#assetInfo">
    <input type="hidden" name="isin" value="<?php echo $dlhopis["isin"]; ?>">
    <div class="grid grid-cols-2 gap-6 mb-6">
        <div class="space-y-3">
            <div class="flex flex-col gap-2">
                <span class="text-slate-400 text-sm">Názov aktíva</span>
                <input type="text" id="cpnaz" name="cpnaz" value="<?php echo $dlhopis["cpnaz"]; ?>" required
                    class="block w-full p-2 text-gray-900 border border-gray-300 rounded-lg bg-gray-50 text-xs focus:ring-blue-500 focus:border-blue-500
                     dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
            </div>
            <div class="flex flex-col gap-2">
                <span class="text-slate-400 text-sm">Symbol</span>
                <input type="text" id="cpnazskratka" name="cpnazskratka" value="<?php echo $dlhopis["cpnazskratka"]; ?>"
                    required
                    class="block w-full p-2 text-gray-900 border border-gray-300 rounded-lg bg-gray-50 text-xs focus:ring-blue-500 focus:border-blue-500 
                    dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
            </div>
            <div class="flex flex-col gap-2">
                <span class="text-slate-400 text-sm">ISIN</span>
                <input type="text" id="isin" name="isin" value="<?php echo $dlhopis["isin"]; ?>" required class="block w-full p-2 text-gray-900 border border-gray-300 rounded-lg bg-gray-50 text-xs 
                    focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 
                    dark:focus:border-blue-500">
            </div>
            <div class="flex flex-col gap-2">
                <span class="text-slate-400 text-sm">Typ aktíva</span>
                <select id="druheqid" name="druheqid"
                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                    <?php foreach ($druhRes[1] as $key => $value) { ?>
                        <option value="<?php echo $value['druheqid']; ?>" <?php echo $dlhopis["druheqid"] === $value["druheqid"] ? "selected='selected'" : ""; ?>>
                            <?php echo $value["poddruheq"] ?>
                        </option>
                    <?php } ?>
                </select>
            </div>
            <div class="flex flex-col gap-2">
                <span class="text-slate-400 text-sm">Sektor</span>
                <select id="sektor" name="sektor"
                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                    <?php foreach ($sektory as $key => $value) { ?>
                        <option value="<?php echo $value['esa95_sektorid']; ?>" <?php echo $dlhopis["sektorid"] === $value["esa95_sektorid"] ? "selected='selected'" : ""; ?>>
                            <?php echo $value["esa95_sektorpopis"] ?>
                        </option>
                    <?php } ?>
                </select>
            </div>
            <div class="flex flex-col gap-2">
                <span class="text-slate-400 text-sm">Odvetvie</span>
                <select id="odvetvieid" name="odvetvieid"
                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                    <?php foreach ($odvetvie as $key => $value) { ?>
                        <option value="<?php echo $value['odvetvieid']; ?>" <?php echo $dlhopis["odvetvieid"] === $value["odvetvieid"] ? "selected='selected'" : ""; ?>>
                            <?php echo $value["odvetviepopis"] ?>
                        </option>
                    <?php } ?>
                </select>
            </div>
        </div>
        <div class="space-y-3">
            <div class="flex flex-col gap-2">
                <span class="text-slate-400 text-sm">Mena</span>
                <select id="mena" name="mena"
                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                    <?php foreach ($meny as $key => $value) { ?>
                        <option value="<?php echo $value['mena']; ?>" <?php echo $dlhopis["currencynom"] === $value["mena"] ? "selected='selected'" : ""; ?>>
                            <?php echo $value["mena"] ?>
                        </option>
                    <?php } ?>
                </select>
            </div>
            <div class="flex flex-col gap-2">
                <span class="text-slate-400 text-sm">Dátum splatnosti</span>
                <input type="date" name="datesplatnosti" id="datesplatnosti"
                    value="<?php echo $dlhopis["maturitydate"]; ?>"
                    class="block w-full p-2 text-gray-900 border border-gray-300 rounded-lg bg-gray-50 text-xs focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 
                    dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
            </div>
            <div class="flex flex-col gap-2">
                <span class="text-slate-400 text-sm">Dátum prvého kupónu</span>
                <input type="date" name="prvykupon" id="prvykupon" value="<?php echo $dlhopis["prvy_kupon"]; ?>"
                    class="block w-full p-2 text-gray-900 border border-gray-300 rounded-lg bg-gray-50 text-xs focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 
                    dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
            </div>
            <div class="flex flex-col gap-2">
                <span class="text-slate-400 text-sm">Dátum posledného kupónu</span>
                <input type="date" name="poslednykupon" id="poslednykupon"
                    value="<?php echo $dlhopis["posledny_kupon"]; ?>"
                    class="block w-full p-2 text-gray-900 border border-gray-300 rounded-lg bg-gray-50 text-xs focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 
                    dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
            </div>
        </div>
    </div>
    <button type="submit" class="text-white bg-blue-700 hover:bg-blue-800 w-full focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 dark:bg-blue-600
             dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800">Uložiť</button>
</form>
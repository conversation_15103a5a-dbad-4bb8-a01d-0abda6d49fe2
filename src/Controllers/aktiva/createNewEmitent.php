<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";


$rawData = file_get_contents("php://input");
$data = json_decode($rawData, true);

if($data["nazovEmitenta"] === "" || $data["skratka"] === "" || $data["stat"] === "" || $data["ico"] === "" || $data["sektor"] === ""){
  echo json_encode(["error" => true, "errorMsg" => "Vyplňte prosím všetky polia"]);
} else {
  $emitentIDres = Connection::getDataFromDatabase("SELECT nextval('podielnikID')", defaultDB);
  $emitentID = $emitentIDres[1][0]["nextval"];
  $emitentParams = [$emitentID, $data["nazovEmitenta"], $data["skratka"], $data["stat"], $data["ico"], $data["sektor"]];
  $createQuery = Connection::InsertUpdateCreateDelete("INSERT INTO equityemitent (emitentid, emitentnazov, emitentskratka, emitentstateid, emitentico, emitentsektor) VALUES (?, ?, ?, ?, ?, ?)", $emitentParams,  defaultDB);
  header('Content-Type: application/json');
  echo json_encode(["error" => false, "errorMsg" => "Emitent bol úspešne vytvorený.", "id" => $emitentID]);
}

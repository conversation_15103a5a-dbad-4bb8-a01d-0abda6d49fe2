<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";


$rawData = file_get_contents("php://input");
$data = json_decode($rawData, true);

if($data["id"] === ""){
  echo json_encode(["error" => true, "errorMsg" => "ID účtu, ktorý chcete odstrániť, je neznáme."]);
} else {
  $emitentParams = [$data["id"]];
  $createQuery = Connection::InsertUpdateCreateDelete("DELETE FROM dbequitycurrric WHERE isincurrricid = ?", $emitentParams,  defaultDB);
  header('Content-Type: application/json');
  echo json_encode(["error" => false, "errorMsg" => "Účet bol úspešne odstránený.", "id" => $data["id"]]);
}


<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";


$rawData = file_get_contents("php://input");
$data = json_decode($rawData, true);
$formular = json_decode($data["formular"]);
$uctyArray = $data["uctyArray"];
$parse = parse_str($rawData, $queryArray);
$queryObject = (object) $queryArray;
header('Content-Type: application/json');

if($data["nazovEmitenta"] === "" || $data["skratka"] === "" || $data["stat"] === "" || $data["ico"] === "" || $data["sektor"] === ""){
  echo json_encode(["error" => true, "errorMsg" => "Vyplňte prosím všetky povinné polia"]);
} else {
  $emitentIDres = Connection::getDataFromDatabase("SELECT nextval('podielnikID')", defaultDB);
  $emitentID = $emitentIDres[1][0]["nextval"];

    $dateemisie = $_POST["dateemisie"];
    $dateemisie = new DateTime($dateemisie);
    $datesplatnosti = $_POST["datesplatnosti"];
    $datesplatnosti = new DateTime($datesplatnosti);
    $kupfrek = $_POST["kupfrek"] ? $_POST["kupfrek"] : 6;
    if(!empty($prvy_kupon)){
      $prvy_kupon = new DateTime($prvy_kupon);
    }
    $posledny_kupon = $_POST["posledny_kupon"];
    if(!empty($posledny_kupon)){
      $posledny_kupon = new DateTime($posledny_kupon);
    }

    $interval = new DateInterval('P'.$kupfrek.'M');
    $period   = new DatePeriod((empty($prvy_kupon) ? $dateemisie : $prvy_kupon), $interval, (empty($posledny_kupon) ? $datesplatnosti : $posledny_kupon));

    $endDate = $period->getEndDate()->format("Y-m-d");
    $endDateObject = $period->getEndDate();

    $periodArr = iterator_to_array($period); 

    foreach ($periodArr as $key => $dt) {
      $currentDate = $dt->format("Y-m-d");
      $currentDateObject = new DateTime($currentDate);

      $interval = $currentDateObject->diff($endDateObject);
      $totalMonths = ($interval->y * 12) + $interval->m;
      if($totalMonths > $period->getDateInterval()){
        if ($key === array_key_last($periodArr)){
          $dateTill = date('Y-m-d', strtotime('-1 day', strtotime($endDate)));
        } else {
          $dateTill = date('Y-m-d', strtotime('-1 day', strtotime($currentDate)));
        }
        $dateSplatnost = date('Y-m-d', strtotime('+'.$kupfrek.' month', strtotime($currentDate)));
        //TODO zadat spravne ciselne hodnoty pre istinu faktor a vyplacany_kupon
        $insertToKupon = Connection::InsertUpdateCreateDelete("INSERT INTO floatkupon VALUES (?, ?, ?, ?, ?, ?, ?, ?)", [$_POST["isin"].$_POST["currencynom"], $currentDate, $dateTill, intval($_POST["kupon"]), $dateSplatnost, 0, 1, 0], defaultDB);  
      }
    }  
    $insertionValues = [];
    foreach ($uctyArray as $key => $value) {
      array_push($insertionValues,[$formular->isin.$value["mena"], $value["trh"], $formular->isin.$value["mena"], $formular->isin.$value["mena"].$formular->isin.$value["mena"], intval($value["kod"]), $value["kurz"] === "Áno" ? 1 : 0, $value["ucet"]]);
      $insertIntoDbequitycurrric = Connection::InsertUpdateCreateDelete("INSERT INTO dbequitycurrric VALUES(?, ?, ?, ?, ?, ?, ?)", [$formular->isin.$value["mena"], $value["trh"], $formular->isin.$value["mena"], $formular->isin.$value["mena"].$formular->isin.$value["mena"], intval($value["kod"]), $value["kurz"] === "Áno" ? 1 : 0, $value["ucet"]], defaultDB);    
      if(str_contains($insertIntoDbequitycurrric, "Unique violation")){
        echo json_encode(["error" => true, "errorMsg" => "Takýto účet už existuje"]);
        end();
      }
  }
  $cpParams = [$formular->isin, $formular->cp_type, $formular->emitent, $formular->cpnaz, $formular->cpnazskratka, $formular->druheqid, $formular->odvetvieid ? $formular->odvetvieid : 0, $formular->sektorid, intval($formular->nominalemisie), $formular->currencynom, $formular->objememisie === "" ? 0 : intval($formular->objememisie), $formular->dateemisie, $formular->kupon === "" ? 0 : intval($formular->kupon), $formular->dan === "" ? 0 : intval($formular->dan), $formular->datesplatnosti, $formular->zaklad === "" ? 0 : intval($formular->zaklad), $formular->exfrekkup === "" ? 0 : intval($formular->exfrekkup), $formular->exfrekist === "" ? 0 : intval($formular->exfrekist), $formular->kupfrek === "" ? 0 : intval($formular->kupfrek), $formular->istfrek === "" ? 0 : intval($formular->istfrek), "f", $formular->isin, $formular->poplatokcp === "" ? 0 : intval($formular->poplatokcp), $formular->prvy_kupon === "" ? 0 : intval($formular->prvy_kupon), $formular->posledny_kupon === "" ? 0 : intval($formular->posledny_kupon), $formular->sadzba_fo === "" ? 0 : intval($formular->sadzba_fo), $formular->sadzba_po === "" ? 0 : intval($formular->sadzba_po), $formular->sadzba_no === "" ? 0 : intval($formular->sadzba_no), intval($formular->nominalemisie)];
  $createQuery = Connection::InsertUpdateCreateDelete("INSERT INTO dbequity (isin, eqid, emitentid, cpnaz, cpnazskratka, druheqid, odvetvieid, sektorid, nominalemisie, currencynom, objememisie, dateemisie, kupon, dan, maturitydate, zaklad, exfrekkup, exfrekist, kupfrek, istfrek, archiv, isinreal, poplatokcp, prvy_kupon, posledny_kupon, sadzbafo, sadzbapo, sadzbano, nominalemisieeur) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", $cpParams, defaultDB);
  echo json_encode(["error" => false, "errorMsg" => "", "id" => $createQuery]);
}

  //header('Content-Type: application/json');
  //echo json_encode(["error" => false, "errorMsg" => "", "data" => $data]);


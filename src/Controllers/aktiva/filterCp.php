<?php

require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

$isArchived = $_GET['isArchived'] === "false" ? false : true;

if (isset($_GET['eqid'])) {
    $groups = json_decode($_GET['eqid'], JSON_UNESCAPED_UNICODE);
}

function buildORPartQuery($dataArray, $columnName): string
{
    $queryStr = "$columnName IN (";
    foreach ($dataArray as $key => $element) {
        if ($key === array_key_last($dataArray)) {
            $queryStr .= "'$element')";
        } else {
            $queryStr .= "'$element',";
        }
    }
    return $queryStr;
}

function buildFilterQuery(): string
{
    global $groups, $cities, $isArchived;
  $queryBase = "SELECT * FROM dbequity ";
  if(!empty($groups)){
        $queryBase .= " WHERE ";
            $queryBase .= buildORPartQuery($groups, "eqid");
                if (!$isArchived) {
                    $queryBase .= " AND archiv = 'f'";
                }
  }
    return $queryBase;
}

$rows = Connection::getDataFromDatabase(buildFilterQuery(), defaultDB);
header('Content-Type: application/json');
if ($rows[0] === 0) {
    echo json_encode(["attention" => "Nenašli sa žiadny klienti podľa vaších filtrovacích kritérií...", $isArchived]);
} else {
    echo json_encode($rows[1]);
}


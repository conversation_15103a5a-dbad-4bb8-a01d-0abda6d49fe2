<?php
require "/home/<USER>/www/src/lib/connection.php";
require "/home/<USER>/www/conf/settings.php";

$cpnaz = $_POST["cpnaz"];
$cpnazskratka = $_POST["cpnazskratka"];
$odvetvieid = $_POST["odvetvieid"];
$sektorid = $_POST["sektor"];
$nominalemisie = $_POST["nominalemisie"];
$currencynom = $_POST["mena"];
$isin = $_POST["isin"];
$druheqid = $_POST["druheqid"];
$poslednykupon = $_POST["poslednykupon"];
$prvykupon = $_POST["prvykupon"];

if ($prvykupon === "") {
    $prvykupon = null;
}

if ($poslednykupon === "") {
    $poslednykupon = null;
}

$updateCP = Connection::InsertUpdateCreateDelete("UPDATE dbequity SET cpnaz = ?, cpnazskratka = ?, odvetvieid = ?, sektorid = ?, nominalemisie = ?, currencynom = ?,
 druheqid = ?, prvy_kupon = ?, posledny_kupon = ? WHERE isin = ?", [
    $cpnaz,
    $cpnazskratka,
    $odvetvieid,
    $sektorid,
    $nominalemisie,
    $currencynom,
    $druheqid,
    $prvykupon,
    $poslednykupon,
    $isin
], defaultDB);

if (gettype($updateCP) === "integer") { ?>
    <script>
        htmx.ajax('GET', window.location.pathname + window.location.search, {
            target: "#pageContentMain",
        });
    </script>
    <?php
} else {
    echo "error" . $updateCP;
}
<?php

require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

$query = $_GET['query'];
$isArchived = $_GET['isArchived'];

function buildFilterQuery($query, $isArchived, $explain): string
{
  if ($query !== "") {
    if($explain){
      $queryBase = "EXPLAIN ANALYZE SELECT * FROM dbequity WHERE (SIMILARITY(isinreal,'%$query%') > 0.2 OR SIMILARITY(cpnaz,'%$query%') > 0.2 OR SIMILARITY(cpnazskratka,'%$query%') > 0.2)";
    } else {
      $queryBase = "SELECT * FROM dbequity WHERE (SIMILARITY(isinreal,'%$query%') > 0.2 OR SIMILARITY(cpnaz,'%$query%') > 0.2 OR SIMILARITY(cpnazskratka,'%$query%') > 0.2)";
    }
        if ($isArchived === 'true') {
            $queryBase .= " AND archiv = 't'";
        } else {
            $queryBase .= " AND archiv = 'f'";
        }
    } else {
        $queryBase = "SELECT * FROM dbequity";
    }
    $queryBase .= " ORDER BY cpnaz ASC";
    return $queryBase;
}

$rows = Connection::getDataFromDatabase(buildFilterQuery($query, $isArchived, false), defaultDB);
$explained = Connection::getDataFromDatabase(buildFilterQuery($query, $isArchived, true), defaultDB);

header('Content-Type: application/json');
if ($rows[0] === 0) {
    echo json_encode(["attention" => "Nenašli sa žiadny klienti podľa vaších filtrovacích kritérií..."], JSON_UNESCAPED_UNICODE);
} else {
    echo json_encode(["data" => $rows,"metadata" => $explained]);
}


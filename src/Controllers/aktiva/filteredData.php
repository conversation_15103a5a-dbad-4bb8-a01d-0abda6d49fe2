<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

$archived = $_POST["archived"] ? 1 : 0;
$search = $_POST["search"];
$offset = $_POST["offset"] ? $_POST["offset"] : 0;
$eqids = [];

if ($archived === 1) {
    $archiv = "'t'";
} else {
    $archiv = "'f'";
}

if ($search != "") {
    $searchQuery = " AND unaccent(lower(cpnaz)) LIKE '$search%' OR unaccent(lower(e.isin)) LIKE '$search%'";
} else {
    $searchQuery = "";
}

$postKeys = [
    "bonds" => "'Bonds'",
    "shares" => "'Shares'",
    "depo" => "'Depo'",
    "fonds" => "'Fonds'"
];

foreach ($postKeys as $key => $value) {
    if (isset($_POST[$key]) && $_POST[$key] != "null") {
        array_push($eqids, "$value");
    }
}

if (sizeof($eqids) > 0) {
    $eqids = implode(', ', $eqids);
    $eqidQuery = " AND eqid IN ($eqids)";
    $eqidsJson = json_encode($eqids);
} else {
    $eqidQuery = "";
    $eqidsJson = "null";
}

$vals = [
    "offset" => $offset + 15,
    "shares" => $_POST["shares"],
    "bonds" => $_POST["bonds"],
    "depo" => $_POST["depo"],
    "fonds" => $_POST["fonds"],
    "archived" => $archived,
    "search" => $search
];
$vals = json_encode($vals, true);

// echo "SELECT e.isin, cpnaz, cpnazskratka, eqid FROM dbequity e LEFT JOIN sanctionlist sc ON sc.isin = e.isin 
// WHERE e.archiv = $archiv $searchQuery $eqidQuery LIMIT 15 OFFSET $offset";
$dlhopisyRes = Connection::getDataFromDatabase("SELECT e.isin, cpnaz, cpnazskratka, eqid FROM dbequity e LEFT JOIN sanctionlist sc ON sc.isin = e.isin 
WHERE e.archiv = $archiv $searchQuery $eqidQuery LIMIT 15 OFFSET $offset", defaultDB);

$dlhopisy = $dlhopisyRes[1];

foreach ($dlhopisy as $key => $dlhopis) { ?>
    <tr <?php if ($key === sizeof($dlhopisy) - 1 && sizeof($dlhopisy) >= 15) { ?> hx-trigger='revealed'
            hx-post='/api/cp/getFilteredData' hx-indicator="#cpckaloadingindicator" hx-vals='<?php echo $vals ?>'
            hx-target='#CPCKA' hx-swap='beforeend' <?php } ?> class=" border-b dark:border-gray-600 dark:hover:bg-gray-700
    hover:bg-gray-100">
        <td class="px-4 py-2">
            <span class="text-sm font-bold px-2 py-0.5 rounded"><?php echo $dlhopis["isin"] ?></span>
        </td>
        <td class="px-4 py-2 font-medium whitespace-nowrap text-gray-700 dark:text-white">
            <?php echo $dlhopis["cpnaz"]; ?>
        </td>
        <td class="px-4 py-2 font-medium whitespace-nowrap text-gray-700 dark:text-white">
            <?php echo $dlhopis["cpnazskratka"]; ?>
        </td>
        <td class="px-4 py-2 font-medium whitespace-nowrap text-gray-700 dark:text-white">
            <?php echo $dlhopis["eqid"]; ?>
        </td>
        <td
            class="px-4 py-2 hidden archivedCol hidden font-medium text-success-900 gap-2 flex items-center text-gray-700 justify-center whitespace-nowrap dark:text-white">
            <?php
            if ($dlhopis["archiv"] === "t") {
                ?>
                <svg class="w-5 h-5 text-green-600" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                    fill="currentColor" viewBox="0 0 24 24">
                    <path fill-rule="evenodd"
                        d="M20 10H4v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8ZM9 13v-1h6v1a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1Z"
                        clip-rule="evenodd" />
                    <path d="M2 6a2 2 0 0 1 2-2h16a2 2 0 1 1 0 4H4a2 2 0 0 1-2-2Z" />
                </svg> <span>Archivovaný</span>
                <?php
            }
            ?>
        </td>
        <td class="px-4 py-2 font-medium whitespace-nowrap dark:text-white">
            <div class="flex items-center gap-4">
                <a href="/aktiva/cenne-papiere/detail/<?php echo str_replace(" ", "", $dlhopis["isin"]); ?>">
                    <button data-tooltip-target="view-tooltip"
                        class="p-2 cursor-pointer hover:bg-gray-300 dark:hover:bg-gray-800 transition-all rounded-lg">
                        <svg class="w-5 h-5 text-gray-800 dark:text-white" aria-hidden="true"
                            xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                            <path stroke="currentColor" stroke-width="2"
                                d="M21 12c0 1.2-4.03 6-9 6s-9-4.8-9-6c0-1.2 4.03-6 9-6s9 4.8 9 6Z" />
                            <path stroke="currentColor" stroke-width="2" d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" />
                        </svg>
                    </button>
                </a>
                <div id="view-tooltip" role="tooltip"
                    class="absolute z-10 invisible inline-block px-3 py-2 text-sm font-medium text-white transition-opacity duration-300 bg-gray-900 rounded-lg shadow-sm opacity-0 tooltip dark:bg-gray-700">
                    Zobraziť
                    <div class="tooltip-arrow" data-popper-arrow></div>
                </div>
                <a href="/aktiva/cenne-papiere/edit/<?php echo str_replace(" ", "", $dlhopis["isin"]); ?>">
                    <button data-tooltip-target="edit-tooltip"
                        class="p-2 cursor-pointer hover:bg-gray-300 dark:hover:bg-gray-800 transition-all rounded-lg">
                        <svg class="w-5 h-5 text-gray-800 dark:text-white" aria-hidden="true"
                            xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor"
                            viewBox="0 0 24 24">
                            <path fill-rule="evenodd"
                                d="M5 8a4 4 0 1 1 7.796 1.263l-2.533 2.534A4 4 0 0 1 5 8Zm4.06 5H7a4 4 0 0 0-4 4v1a2 2 0 0 0 2 2h2.172a2.999 2.999 0 0 1-.114-1.588l.674-3.372a3 3 0 0 1 .82-1.533L9.06 13Zm9.032-5a2.907 2.907 0 0 0-2.056.852L9.967 14.92a1 1 0 0 0-.273.51l-.675 3.373a1 1 0 0 0 1.177 1.177l3.372-.675a1 1 0 0 0 .511-.273l6.07-6.07a2.91 2.91 0 0 0-.944-4.742A2.907 2.907 0 0 0 18.092 8Z"
                                clip-rule="evenodd" />
                        </svg>
                    </button>
                </a>
                <div id="edit-tooltip" role="tooltip"
                    class="absolute z-10 invisible inline-block px-3 py-2 text-sm font-medium text-white transition-opacity duration-300 bg-gray-900 rounded-lg shadow-sm opacity-0 tooltip dark:bg-gray-700">
                    Upraviť
                    <div class="tooltip-arrow" data-popper-arrow></div>
                </div>
                <form hx-post="/api/cp/archive" hx-target="#toast" class="mb-0 archiveForm">
                    <input type="hidden" name="action" name="action" value="archive" />
                    <input type="hidden" name="isin" name="isin" value="<?php echo $dlhopis["isin"] ?>" />
                    <button id="archiveBtn" type="submit"
                        class="p-2 cursor-pointer hover:bg-gray-300 dark:hover:bg-gray-800  transition-all rounded-lg">
                        <svg id="archiveButton" class="w-5 h-5 text-gray-800 dark:text-white" aria-hidden="true"
                            xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 11v5m0 0 2-2m-2 2-2-2M3 6v1a1 1 0 0 0 1 1h16a1 1 0 0 0 1-1V6a1 1 0 0 0-1-1H4a1 1 0 0 0-1 1Zm2 2v10a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V8H5Z" />
                        </svg>
                        <svg id="archiveSpinner" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="display: none;"
                            stroke-linecap="round" stroke-linejoin="round" class="lucide animate-spin lucide-loader-circle">
                            <path d="M21 12a9 9 0 1 1-6.219-8.56" />
                        </svg>
                    </button>
                </form>
            </div>
        </td>
    </tr>
    <?php
}
?>
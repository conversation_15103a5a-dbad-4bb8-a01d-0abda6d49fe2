<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
require_once "/home/<USER>/www/src/lib/notifications/Notification.class.php";

$isin = $_POST["isin"];
$datefrom = $_POST["datefrom"];
$dateto = $_POST["dateto"] === NULL ? $_POST["dateto"] : date("Y-m-d");
$reason = $_POST["reason"];
$dateNow = date("Y-m-d");

$userid = $_SESSION["user"]["data"]["userid"];
$username = $_SESSION["user"]["data"]["username"];

$insertIntoSanction = Connection::InsertUpdateCreateDelete("DELETE FROM sanctionlist WHERE isin = ?", [$isin], defaultDB);
$insertIntoSanctionHistory = Connection::InsertUpdateCreateDelete(
    "INSERT INTO sanctionhistory (isin, datefrom, dateto, reason, archiveddate, userid) VALUES (?,?,?,?,?,?)",
    [$isin, $datefrom, $dateto, $reason, $dateNow, $userid],
    defaultDB
);

if (gettype($insertIntoSanction) !== "integer" && gettype($insertIntoSanctionHistory) !== "integer") {
    echo "INSERT INTO sanctionhistory (isin, datefrom, dateto, reason, archiveddate, userid) VALUES ($isin, $datefrom, $dateto, $reason, $dateNow, $userid)";
    print_r($insertIntoSanction);
    print_r($insertIntoSanctionHistory);
    ?>
    <div id="toast-danger"
        class="flex items-center w-full max-w-xs p-4 mb-4 text-gray-500 bg-white rounded-lg shadow dark:text-gray-400 dark:bg-gray-800"
        role="alert">
        <?php ?>
        <div
            class="inline-flex items-center justify-center flex-shrink-0 w-8 h-8 text-red-500 bg-red-100 rounded-lg dark:bg-red-800 dark:text-red-200">
            <svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                viewBox="0 0 20 20">
                <path
                    d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 11.793a1 1 0 1 1-1.414 1.414L10 11.414l-2.293 2.293a1 1 0 0 1-1.414-1.414L8.586 10 6.293 7.707a1 1 0 0 1 1.414-1.414L10 8.586l2.293-2.293a1 1 0 0 1 1.414 1.414L11.414 10l2.293 2.293Z" />
            </svg>
            <span class="sr-only">Error icon</span>
        </div>
        <div class="ms-3 text-sm font-normal">Sankciu sa nepodarilo iniciovať.</div>
        <button type="button"
            class="ms-auto -mx-1.5 -my-1.5 bg-white text-gray-400 hover:text-gray-900 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex items-center justify-center h-8 w-8 dark:text-gray-500 dark:hover:text-white dark:bg-gray-800 dark:hover:bg-gray-700"
            data-dismiss-target="#toast-danger" aria-label="Close">
            <span class="sr-only">Close</span>
            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
            </svg>
        </button>
    </div>
<?php } else {
    $notification = new Notification(102, 'dbequity', 0, 'delete', $userid, $username, "sanctionDelete", json_encode(["isin", $isin]));
    $notification->createNotifcation();
    ?>
    <script>
        window.location.reload();
    </script>
<?php } ?>
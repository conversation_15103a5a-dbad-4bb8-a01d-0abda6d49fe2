<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
$search = $_POST["search"];
$eqids = [];

if ($search !== "") {
    $searchQuery = " AND unaccent(lower(e.cpnaz)) LIKE '$search%' OR unaccent(lower(sc.isin)) LIKE '$search%'";
} else {
    $searchQuery = "";
}

$postKeys = [
    "bonds" => "'Bonds'",
    "shares" => "'Shares'",
    "depo" => "'Depo'",
    "fonds" => "'Fonds'"
];

foreach ($postKeys as $key => $value) {
    if (isset($_POST[$key])) {
        array_push($eqids, "$value");
    }
}

if (sizeof($eqids) > 0) {
    $eqids = implode(', ', $eqids);
    $eqidQuery = " AND e.eqid IN ($eqids)";
} else {
    $eqidQuery = "";
}
//echo "SELECT sc.*, e.cpnaz, e.eqid as eqid, u.username FROM sanctionhistory sc JOIN users u ON u.userid = sc.userid JOIN dbequity e ON e.isin = sc.isin $eqidQuery $searchQuery";
$cp = Connection::getDataFromDatabase("SELECT sc.*, e.cpnaz, e.eqid as eqid, u.username FROM sanctionhistory sc JOIN users u ON u.userid = sc.userid JOIN dbequity e ON e.isin = sc.isin $eqidQuery $searchQuery", defaultDB)[1];

foreach ($cp as $cpItem) { ?>
    <tr class="border-b dark:border-gray-600 hover:bg-gray-100">
        <td class="px-4 py-2">
            <span class="text-sm text-gray-900 font-bold px-2 py-0.5 rounded"><?php echo $cpItem["isin"] ?></span>
        </td>
        <td class="px-4 py-2 font-medium text-gray-900 whitespace-nowrap dark:text-white">
            <?php echo $cpItem["cpnaz"]; ?>
        </td>
        <td class="px-4 py-2 font-medium text-gray-900 whitespace-nowrap dark:text-white">
            <?php echo $cpItem["eqid"]; ?>
        </td>
        <td class="px-4 py-2 font-medium text-gray-900 whitespace-nowrap dark:text-white">
            <?php echo $cpItem["datefrom"]; ?>
        </td>
        <td class="px-4 py-2 font-medium text-gray-900 whitespace-nowrap dark:text-white">
            <?php echo $cpItem["dateto"]; ?>
        </td>
        <td class="px-4 py-2 font-medium text-gray-900 whitespace-nowrap dark:text-white">
            <span
                class="bg-gray-100 text-gray-800 text-xs font-medium me-2 px-2.5 py-0.5 rounded-md dark:bg-gray-700 dark:text-gray-400 border border-gray-500"><?php echo $cpItem["reason"]; ?></span>
        </td>
        <td class="px-4 py-2 font-medium text-gray-900 whitespace-nowrap dark:text-white">
            <a href="/pouzivatelia/detail/<?php echo $cpItem["userid"]; ?>"
                class="flex items-center pl-3 gap-2 p-0.5 rounded-lg hover:bg-gray-200 hover:underline cursor-pointer transition-all">
                <div
                    class="relative w-5 h-5 overflow-hidden bg-gray-200 flex items-center justify-center rounded-full dark:bg-gray-600">
                    <svg class="w-5 h-5 text-white bg-blue-500" fill="currentColor" viewBox="0 0 20 20"
                        xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                            clip-rule="evenodd">
                        </path>
                    </svg>
                </div>
                <small><?php echo $cpItem["username"] ?></small>
            </a>
        </td>
    </tr>
    <?php
} ?>
<?php
foreach ($cp as $cpItem) { ?>
    <tr class="border-b dark:border-gray-600 dark:hover:bg-gray-700 cursror-pointer hover:bg-gray-100">
        <td class="px-4 py-2">
            <span class="text-sm font-bold px-2 py-0.5 rounded"><?php echo $cpItem["isin"] ?></span>
        </td>
        <td class="px-4 py-2 font-medium whitespace-nowrap dark:text-white">
            <?php echo $cpItem["cpnaz"]; ?>
        </td>
        <td class="px-4 py-2 font-medium whitespace-nowrap dark:text-white">
            <?php echo $cpItem["eqid"]; ?>
        </td>
        <td class="px-4 py-2 font-medium whitespace-nowrap dark:text-white">
            <?php echo $cpItem["v"]; ?>
        </td>
        <td class="px-4 py-2 font-medium whitespace-nowrap dark:text-white">
            <?php echo $cpItem["reason"]; ?>
        </td>
        <td class="px-4 py-2 font-medium whitespace-nowrap dark:text-white">
            <div class="flex items-center gap-2">
                <button data-modal-target="editModal<?php echo $cpItem["isin"]; ?>"
                    data-modal-toggle="editModal<?php echo $cpItem["isin"]; ?>"
                    class="p-2 cursor-pointer hover:bg-gray-300 transition-all rounded-lg">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="lucide lucide-pencil">
                        <path
                            d="M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z" />
                        <path d="m15 5 4 4" />
                    </svg>
                </button>
                <form id="deleteSanctionForm" class="mb-0">
                    <input type="hidden" name="datefrom" id="datefrom" value="<?php echo $cpItem["datefrom"]; ?>" />
                    <input type="hidden" name="dateto" id="dateto" value="<?php echo $cpItem["dateto"]; ?>" />
                    <input type="hidden" name="reason" id="reason" value="<?php echo $cpItem["reason"]; ?>" />
                    <input type="hidden" name="isin" id="isin" value="<?php echo $cpItem["isin"]; ?>" />
                    <button class="p-2 cursor-pointer hover:bg-red-200 deleteSanction transition-all rounded-lg"
                        type="submit">
                        <svg id="deleteSanctionIcon" xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                            viewBox="0 0 24 24" fill="none" stroke="red" stroke-width="2" stroke-linecap="round"
                            stroke-linejoin="round" class="lucide lucide-circle-x">
                            <circle cx="12" cy="12" r="10" />
                            <path d="m15 9-6 6" />
                            <path d="m9 9 6 6" />
                        </svg>
                        <svg id="deleteSanctionSpinner" xmlns="http://www.w3.org/2000/svg" width="14" height="14"
                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                            stroke-linejoin="round" style="display: none;"
                            class="lucide deleteSanctionSpinner animate-spin lucide-loader-circle">
                            <path d="M21 12a9 9 0 1 1-6.219-8.56" />
                        </svg>
                    </button>
                </form>
            </div>
        </td>
    </tr>
    <?php
}
?>
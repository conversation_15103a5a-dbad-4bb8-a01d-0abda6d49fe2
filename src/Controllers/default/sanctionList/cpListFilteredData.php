<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
$search = $_POST["search"];
$eqids = [];

if ($search !== "") {
    $searchQuery = " AND unaccent(lower(e.cpnaz)) LIKE '$search%' OR unaccent(lower(e.isin)) LIKE '$search%'";
} else {
    $searchQuery = "";
}

$postKeys = [
    "bonds" => "'Bonds'",
    "shares" => "'Shares'",
    "depo" => "'Depo'",
    "fonds" => "'Fonds'"
];

foreach ($postKeys as $key => $value) {
    if (isset($_POST[$key])) {
        array_push($eqids, "$value");
    }
}

if (sizeof($eqids) > 0) {
    $eqids = implode(', ', $eqids);
    $eqidQuery = " AND e.eqid IN ($eqids)";
} else {
    $eqidQuery = "";
}
$cp = Connection::getDataFromDatabase("SELECT e.isin, cpnaz, cpnazskratka, eqid, sc.reason FROM dbequity e LEFT JOIN sanctionlist sc ON sc.isin = e.isin WHERE archiv = 'f' $searchQuery $eqidQuery LIMIT 50", defaultDB)[1];

foreach ($cp as $key => $cpItem) {
    if ($cpItem["reason"] === NULL) { ?>
        <tr id="rowsanction<?php echo $key; ?>" class="border-b dark:border-gray-600 hover:bg-gray-100">
            <td class="px-4 py-2">
                <span class="text-sm text-gray-900 font-bold px-2 py-0.5 rounded"><?php echo $cpItem["isin"] ?></span>
            </td>
            <td class="px-4 py-2 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                <?php echo $cpItem["cpnaz"]; ?>
            </td>
            <td class="px-4 py-2 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                <?php echo $cpItem["eqid"]; ?>
            </td>
            <td class="px-4 py-2 font-medium text-gray-900 whitespace-nowrap relative dark:text-white">
                <div id="sanction<?php echo $key; ?>tooltip"
                    class="flex z-20 bg-white items-center gap-4 sanctionTooltip absolute shadow-lg px-3 py-2 rounded-lg border"
                    style="width: 21rem; right: 1rem; top: 2.5rem; display: none;">
                    <form class="mb-0 flex w-full flex-col gap-2 items-center sanctionForm">
                        <input type="hidden" name="isin" name="isin" value="<?php echo $dlhopis["isin"] ?>" />
                        <section class="flex gap-2 items-center w-full">
                            <div class="w-full">
                                <label for="small-input"
                                    class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Dátum
                                    od</label>
                                <input type="date" name="datefrom" required
                                    class="block w-full p-2 text-gray-900 border border-gray-300 rounded-lg bg-gray-50 text-xs focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                            </div>
                            <div class="w-full">
                                <label for="small-input"
                                    class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Dátum
                                    do</label>
                                <input type="date" name="dateto"
                                    class="block w-full p-2 text-gray-900 border border-gray-300 rounded-lg bg-gray-50 text-xs focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                            </div>
                        </section>
                        <div class="w-full">
                            <label for="message" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Dôvod
                                sankcie</label>
                            <textarea id="message" rows="4" name="reason" required
                                class="block p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                placeholder="Dôvod sankcie..."></textarea>
                        </div>
                        <button type="submit" id="createSanction"
                            class="btn-primary w-full flex justify-center items-center gap-3"
                            style="font-size: .75rem; line-height: 1rem;">
                            <span>Pridať na sankčný zoznam</span>
                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none"
                                id="sanctionSpinner" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" style="display: none;" class="lucide animate-spin lucide-loader-circle">
                                <path d="M21 12a9 9 0 1 1-6.219-8.56" />
                            </svg>
                        </button>
                    </form>
                </div>
                <button id="sanction<?php echo $key; ?>" type="submit"
                    class="p-2 cursor-pointer sanctionBtn hover:bg-red-200 transition-all rounded-lg">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="red"
                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-ban">
                        <circle cx="12" cy="12" r="10" />
                        <path d="m4.9 4.9 14.2 14.2" />
                    </svg>
                    <svg id="archiveSpinner" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                        fill="none" stroke="currentColor" stroke-width="2" style="display: none;" stroke-linecap="round"
                        stroke-linejoin="round" class="lucide animate-spin lucide-loader-circle">
                        <path d="M21 12a9 9 0 1 1-6.219-8.56" />
                    </svg>
                </button>
            </td>
        </tr>
    <?php }
}
?>
<script src="/src/assets/js/aktiva/cp/sanctionsCPList.js"></script>
<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
$search = $_POST["search"];
$eqids = [];

if ($search !== "") {
    $searchQuery = " AND unaccent(lower(e.cpnaz)) LIKE '$search%' OR unaccent(lower(e.isin)) LIKE '$search%'";
} else {
    $searchQuery = "";
}

$postKeys = [
    "bonds" => "'Bonds'",
    "shares" => "'Shares'",
    "depo" => "'Depo'",
    "fonds" => "'Fonds'"
];

foreach ($postKeys as $key => $value) {
    if (isset($_POST[$key])) {
        array_push($eqids, "$value");
    }
}

if (sizeof($eqids) > 0) {
    $eqids = implode(', ', $eqids);
    $eqidQuery = " AND e.eqid IN ($eqids)";
} else {
    $eqidQuery = "";
}
//echo "SELECT sc.*, e.cpnaz FROM sanctionList sc JOIN dbequity e ON e.isin = sc.isin $eqidQuery $searchQuery";
$cp = Connection::getDataFromDatabase("SELECT sc.*, e.cpnaz, e.eqid as eqid FROM sanctionList sc JOIN dbequity e ON e.isin = sc.isin $eqidQuery $searchQuery", defaultDB)[1];

foreach ($cp as $cpItem) { ?>
    <tr class="border-b dark:border-gray-600 hover:bg-gray-100">
        <td class="px-4 py-2">
            <span class="text-sm text-gray-900 font-bold px-2 py-0.5 rounded"><?php echo $cpItem["isin"] ?></span>
        </td>
        <td class="px-4 py-2 font-medium text-gray-900 whitespace-nowrap dark:text-white">
            <?php echo $cpItem["cpnaz"]; ?>
        </td>
        <td class="px-4 py-2 font-medium text-gray-900 whitespace-nowrap dark:text-white">
            <?php echo $cpItem["eqid"]; ?>
        </td>
        <td class="px-4 py-2 font-medium text-gray-900 whitespace-nowrap dark:text-white">
            <?php echo $cpItem["datefrom"]; ?>
        </td>
        <td class="px-4 py-2 font-medium text-gray-900 whitespace-nowrap dark:text-white">
            <?php echo $cpItem["reason"]; ?>
        </td>
        <td class="px-4 py-2 font-medium text-gray-900 whitespace-nowrap dark:text-white">
            <div class="flex items-center gap-4">
                <a>
                    <button data-modal-target="editModal<?php echo $cpItem["isin"]; ?>"
                        data-modal-toggle="editModal<?php echo $cpItem["isin"]; ?>"
                        class="p-2 cursor-pointer hover:bg-gray-300 transition-all rounded-lg">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-pencil">
                            <path
                                d="M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z" />
                            <path d="m15 5 4 4" />
                        </svg>
                    </button>
                    <button class="p-2 cursor-pointer hover:bg-red-200 deleteSanction transition-all rounded-lg"
                        id="<?php echo $cpItem["isin"] ?>">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                            stroke="red" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-circle-x">
                            <circle cx="12" cy="12" r="10" />
                            <path d="m15 9-6 6" />
                            <path d="m9 9 6 6" />
                        </svg>
                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            style="display: none;" class="lucide deleteSanctionSpinner animate-spin lucide-loader-circle">
                            <path d="M21 12a9 9 0 1 1-6.219-8.56" />
                        </svg>
                    </button>
                </a>
            </div>
        </td>
    </tr>
    <?php
}
?>
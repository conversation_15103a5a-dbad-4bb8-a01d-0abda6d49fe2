<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

header('Content-Type: application/json');

$podielnikIDres = Connection::getDataFromDatabase("SELECT nextval('podielnikID')", defaultDB);
$podielnikSeq = $podielnikIDres[1][0]["nextval"];
$podielnikID = $podielnikSeq . "900";

$rcico = NULL;
$el_onboarding = NULL;
$created_at = NULL;
$email_verified_at = NULL;
$datum_narodenia = NULL;
$general_conditions = NULL;
$pre_contractual_conditions = NULL;
$vs_id = NULL;


if($_POST["onboarding_id"] !== ""){
  $el_onboarding = 1;
} else {
  $el_onboarding = 0;
}

if($_POST["rcico"] === ""){
  $rcico = $_POST["ico"];
} else {
  $rcico = $_POST["rcico"];
}

if($_POST["pre_contractual_conditions"] != ""){
  $pre_contractual_conditions = "'".$_POST["pre_contractual_conditions"]."'";
}
if($_POST["vs_id"] != ""){
  $vs_id = $_POST["vs_id"];
}
if($_POST["general_conditions"] != ""){
  $general_conditions = "'".$_POST["general_conditions"]."'";
}
if($_POST["created_at"] != ""){
  $created_at =  "'".$_POST["created_at"]."'";
}
if($_POST["email_verified_at"] !== ""){
  $email_verified_at = "'".$_POST["email_verified_at"]."'";
}
if($_POST["datum_narodenia"] !== ""){
  $datum_narodenia = "'".$_POST["datum_narodenia"]."'";
}

if($_POST["userid"] !== "" && $_POST["fpo"] !== "" && $rcico !== "" && $_POST["datetimeagreement"] !== ""){
    $vypisyheslo = str_replace("/", "", $_POST["rcico"]);
  $clientParams = [$podielnikID, $podielnikSeq, 'f', $_POST["prieznaz"], $_POST["meno"], $_POST["titulpred"], $_POST["titulza"], $_POST["typklienta"], intval($_POST["fpo"]), intval($_POST["druhid"]), $rcico, $_POST["lei_kod"], $_POST["dic"], $_POST["icdph"], $_POST["pohlavie"], $datum_narodenia, $_POST["cisloid"], intval($_POST["stateid"]), $_POST["sektor_esa95"], $_POST["address"], $_POST["city"], $_POST["postalcode"], $_POST["kontaktphonenumber"], $_POST["kontaktemail"], $_POST["kontaktnapriez"] === null ? "" : $_POST["kontaktnazpriez"], $_POST["uroven_aml_rizika"], $el_onboarding, $_POST["onboarding_id"], $email_verified_at, $_POST["datetimeagreement"], $vs_id, $pre_contractual_conditions, $general_conditions, $_POST["vypisfrekvencia"], $_POST["gfi"], $_POST["dan_domicil"], $_POST["typzdanenia"], $_POST["userid"], $vypisyheslo === null ? "" : $vypisyheslo, $created_at];
  $keket = Connection::InsertUpdateCreateDelete("INSERT INTO podielnik (podielnikid, podielnikidseq, archiv, prieznaz, meno, titulpred, titulza, typklienta, fpo, druhid, rcico, lei_kod, dic, icdph, pohlavie, datum_narodenia, cisloid, stateid, sektor_esa95, address, city, postalcode, kontaktphonenumber, kontaktemail, kontaktnazpriez, uroven_aml_rizika, el_onboarding, onboarding_id, overenie_emailu, datetimeagreement, zastupujuci_vs, pre_cont_cond, gen_cond, vypisfrekvencia, gfi, dan_domicil, typ_zdanenia, userid, vypisy_heslo, gdpr_consent) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", $clientParams, defaultDB);
  echo json_encode(["error" => false, "userid" => $podielnikID]);
} else {
  echo json_encode(["error" => true, "errorMsg" => "Dáta nie sú valídne, skúste to znova"]);
}


<?php 
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

if ($_SERVER['REQUEST_METHOD'] === 'GET') {
  $user_id = $_GET["clientID"];
  if(isset($user_id)){
      $get = Connection::getDataFromDatabase("SELECT encode(file, 'hex'), file_name, user_id FROM investicny_dotaznik WHERE user_id = $user_id;", defaultDB);
      $pdf = $get[1][0];
      if($pdf['encode'] !== null){
        header("Content-type: application/pdf");
        header("Content-Disposition:attachment;filename=".$pdf["file_name"]);
        echo hex2bin($pdf["encode"]);
      } else {
        header("Content-type: application/json");
        echo json_encode(["error" => true, "msg" => "Pre tohto zákazníka nie je nehraný žiaden súbor."]);
      }
  } else {
    header("Content-type: application/json");
    echo json_encode(["error" => true, "msg" => "Pre tohto zákazníka nie je nahraný žiaden súbor.", "sdadas" => $user_id]);
  }
} else {
  echo "Method is not allowed!";
}

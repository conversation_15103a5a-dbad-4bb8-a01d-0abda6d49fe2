<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";


// Get the raw POST data
$rawData = file_get_contents("php://input");

// Debug: Print raw data to error log
error_log("Raw POST data: " . $rawData);

//Decode the JSON data to a PHP associative array
$data = json_decode($rawData, true);

/*$error = "";
$currentDate = date("Y-m-d");
$dateTo = new DateTime('2100-05-16');
$dateTo = $dateTo->format("Y-m-d");
$iban = $data["iban"];
$mena =  $data["mena"];
$userid = $_SESSION["user"]["data"]["userid"]; 
$active = "true";
$podielnikid = "0";
$filesrc = "''";

if(isset($rawData["podielnikid"])){
  $podielnikid = $rawData["podielnikid"];
}

if(isset($rawData["filesrc"])){
  $filesrc = $rawData["filesrc"];
}


if($rawData["iban"] === "" || $rawData["mena"] === ""){
  $error = "Nebolo možné vytvoriť bežný účet. Skúste to znova! Polia nesmú byť prázdne.";
}

header('Content-Type: application/json');
if($error === ""){
  $createQuery = Connection::InsertUpdateCreateDelete("INSERT INTO autorizovane_ucty (iban, mena, timefrom, timeto, userid, active, podielnikid, filesrc) VALUES ('".$iban."', '".$mena."', '$currentDate', '$dateTo', $userid, $active, $podielnikid, $filesrc)", defaultDB);
  echo json_encode($data);
} else {
  echo json_encode(["error" => true, "errorMsg" => $error]);
}*/
header('Content-Type: application/json');
echo json_encode($data);

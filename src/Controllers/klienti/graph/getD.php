<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

$interval = $_GET['interval'];

if ($_GET["datefrom"] != "") {
    $newdate = $_GET["datefrom"];
} else {
    $newdate = date("Y-m-d", strtotime("-" . $interval . " months - 1 day"));
}

if ($_GET["dateto"]) {
    $dbdate = $_GET["dateto"];
} else {
    $dbdate = date("Y-m-d", strtotime("- 1 day"));
}

$fond = $_GET["fondid"];
$object = $_GET["object"];

if ($object === "kupon") {
    $dateQuery = " and s.datumvyplaty >= '$newdate'
                 and s.datumvyplaty <= '$dbdate'";
    $query = "
	select 
		s.datumvyplaty as datumvyplaty,
 		s.datum_naroku as datumnaroku,
		round(1/(f_menovy_kurz_lot(s.mena,'EUR',datum)),5) as kurz,
		round(s.suma*f_menovy_kurz(s.mena,'EUR',datum, 1),2) as sumask,
      		s.mena as mena,
		s.kodaktiva,
		s.suma as suma, 
		s.datum, 
		s.pocet, 
		d.cpnaz, 
		p.cislozmluvy,
		dv.danzaklad,
		dv.dan,
		dv.dansadzba,
		dv.mena as dvmena, 
		dv.vynoskus,
		d.isinreal
	from 
		splatenie s, 
		dbequity d, 
		dbequitycurr dc, 
		dbequitycurrric dcr, 
		portfolio p, 
		danvynosy dv
	where
			
		s.uctovnykod = 251120
		$dateQuery 
		and p.fondid in ($fond)
        	and s.dealid = dv.dealid
		and dcr.isincurrric=s.kodaktiva
		and dcr.isincurr=dc.isincurr
		and dc.isin=d.isin
		and p.fondid = s.subjektid
	order by cislozmluvy
		";
//echo $query;
    $dlhopisyRes = Connection::getDataFromDatabase($query, defaultDB);
    $dlhopisy = $dlhopisyRes[1];

    if (empty($dlhopisy)) {
        echo json_encode(["error" => true, "message" => "Nenašli sa žiadne dáta"]);
    } else {
        echo json_encode(["error" => false, "points" => $dlhopisy]);
    }
}
if ($object === "dividenda") {
    $dateQuery = " and s.datum >= '$newdate'
                 and s.datum <= '$dbdate'";
    $query = "
	select 
		s.datumvyplaty as datumvyplaty,
 		s.datum_naroku as datumnaroku,
		round(1/(f_menovy_kurz_lot(s.mena,'EUR',datum)),5) as kurz,
		round(s.suma*f_menovy_kurz(s.mena,'EUR',datum,1),2) as sumask,
      		s.mena as mena,
		s.kodaktiva,
		s.suma as suma, 
		s.datum, 
		s.pocet, 
		d.cpnaz, 
		p.cislozmluvy,
		dv.danzaklad,
		dv.dan,
		dv.dansadzba,
		dv.mena as dvmena, 
		dv.vynoskus,
		d.isinreal,
		dc.currencytrade,
		dat.nazov as dividenda
	from 
		splatenieakcia s, 
		dbequity d, 
		dbequitycurr dc, 
		dbequitycurrric dcr, 
		portfolio p, 
		danvynosy dv,
		dividendaakciatyp dat
	where
			
		dat.hotovost = 1
		and dat.subkodobratu = s.subkodobratu
		$dateQuery 
		and p.fondid in ($fond)
        	and s.dealid = dv.dealid
		and dcr.isincurrric=s.kodaktiva
		and dcr.isincurr=dc.isincurr
		and dc.isin=d.isin
		and p.fondid = s.subjektid
	order by 
		cislozmluvy
		";
    $akcieRes = Connection::getDataFromDatabase($query, defaultDB);
    $akcie = $akcieRes[1];

    if (empty($akcie)) {
        echo json_encode(["error" => true, "message" => "Nenašli sa žiadne dáta"]);
    } else {
        echo json_encode(["error" => false, "points" => $akcie]);
    }
}
if ($object === "urok") {
    $dateQuery = " and k_td >= '$newdate'
                 and k_td <= '$dbdate'";
    $query =
        "SELECT 
				   k.z_td as zaciatok,
				   k.k_td as koniec,
				   k.sum_td as suma,
				   k.ir_td as sadzba,
				   k.iv_b as brutto,
				   k.iv_n as netto,
				   k.mena,
				   k.suma_dane as dan,
				   k.datvysporiadaniarealntd as vysporiadanie,
				   k.cutd,
				   k.subjektid,
				   z_td,
				   k_td,
				   p.nazovpartnera as miesto,
				   k.datum_cas_obchodu as datum_cas,
				   por.cislozmluvy	
				   
			from 
				konfirmaciaktv k,
				partner p,
				portfolio por
			where 
				
				k.logactivityid = 25
				$dateQuery 
				and por.fondid in ($fond)
			 	and k.partnerid = p.partnerid
				and k.subjektid > 1
				and por.fondid = k.subjektid
				
					
			
			union all
				
			select 	   
				   k.z_td as zaciatok,
				   k.k_td as koniec,
				   pdr.transsumareal as suma,
				   k.ir_td as sadzba,
				   pdr.auvreal  as brutto,
	   	   		   (pdr.auvreal - pdr.dan) as netto,
				   k.mena,
				   pdr.dan as dan,
				   k.datvysporiadaniarealntd as vysporiadanie,
				   k.cutd,
				   k.subjektid,
				   z_td,
				   k_td,
				   p.nazovpartnera as miesto,
				   k.datum_cas_obchodu as datum_cas,
				   por.cislozmluvy
				  
			from
				konfirmaciaktv k,
				pool po,
				pooldetailreal pdr,
				partner p,
				portfolio por
			where
				
				 k.subjektid = 0
				$dateQuery 
				and por.fondid in ($fond)
				 and pdr.poolid = po.poolid 
				 and k.dealid = po.dealid
				 and k.logactivityid = 25
				 and k.partnerid = p.partnerid
				 and pdr.subjektid = por.fondid
			order by cislozmluvy
		";
    $terminovaneRes = Connection::getDataFromDatabase($query, defaultDB);
    $terminovane = $terminovaneRes[1];

    if (empty($akcie)) {
        echo json_encode(["error" => true, "message" => "Nenašli sa žiadne dáta"]);
    } else {
        echo json_encode(["error" => false, "points" => $terminovane]);
    }
}


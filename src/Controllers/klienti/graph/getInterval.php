<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

$interval = $_GET['interval'];

if($_GET["datefrom"] != ""){
    $newdate = $_GET["datefrom"];
} else {
    $newdate = date("Y-m-d", strtotime("-" . $interval . " months - 1 day"));
}

if($_GET["dateto"]){
    $dbdate = $_GET["dateto"];
} else {
    $dbdate = date("Y-m-d", strtotime("- 1 day"));
}

$fond = $_GET["fondid"];
$clientID = $_GET["clientID"];

$query = "SELECT datum, SUM(((0.5 - md_d) / 0.5 ) * sumadenom) as sum
                FROM majetoktotal
                WHERE datum BETWEEN '$newdate'::date AND '$dbdate' AND subjektid IN ( $fond )
                GROUP BY datum ORDER BY datum";


$vykonostRes = Connection::getDataFromDatabase("select g.datum,
       trunc(g.suma_spolu, 3)::numeric                                            as suma,
       trunc(g.vklady_vybery, 3)::numeric                                         as vklady_vybery,
       g.mena::text,
       case
           when g.cumulative_return is null then 0
           else trunc(((g.cumulative_return - 1) * 100)::numeric, 4)::numeric end as cumulative_return
from (select f.datum,
             f.suma_spolu,
             f.vklad_vyber                                                                          as vklady_vybery,
             f.mena,
             trunc(((cumulative_mul(f.daily_return) over (order by f.datum)))::numeric, 4)::numeric as cumulative_return
      from (select a.datum,
                   sum(a.suma_spolu)                                                             as suma_spolu,
                   a.mena,
                   sum(coalesce(a.vklad_vyber, 0))                                               as vklad_vyber,
                   case
                       when sum(a.suma_spolu) = 0 then 1
                       else round(sum(a.daily_return * a.suma_spolu) / sum(a.suma_spolu), 6) end as daily_return
            from (select e.datum,
                         e.suma                     as suma_spolu,
                         e.mena,
                         coalesce(e.vklad_vyber, 0) as vklad_vyber,
                         case
                             when LAG(e.suma, 1) OVER (order by e.datum) = 0 then 1
                             when LAG(e.suma, 1) OVER (order by e.datum) is null and coalesce(e.vklad_vyber, 0) <> 0
                                 then
                                 coalesce(1 + ((e.suma + coalesce(e.vklad_vyber, 0)) /
                                               coalesce(LAG(e.suma, 1) OVER (order by e.datum), e.suma)), 1)
                             else coalesce(
                                     (e.suma + coalesce(e.vklad_vyber, 0)) / LAG(e.suma, 1) OVER (order by e.datum), 1)
                             end                    as daily_return
                  from (select ee.datum, sum(ee.suma) as suma, ee.mena, sum(vv) as vklad_vyber
                        from (select dd.datum, sum(dd.suma) as suma, dd.mena, 0 as vv
                              from (select a.datum                                      as datum,
                                           round(sum(a.suma/*/coalesce(k.kurz,1)*/), 2) as suma,
                                           a.mena
                                    from (select m.datum,
                                                 sum(m.sumaref * ((0.5 - md_d) / 0.5)) as suma,
                                                 m.menaref                             as mena
                                          from majetoktotal m
                                                   left join portfolio p on m.subjektid IN ( p.fondid )
                                          where p.podielnikid = $clientID
                                            and m.datum <= (case
                                                                when '$dbdate' is null
                                                                    then (select max(m.datum) from majetoktotal m)
                                                                else '$dbdate' end)
                                            and m.datum >= (case
                                                                when '$newdate' is null
                                                                    then (select max(m.datum) - interval '36 month' from majetoktotal m)
                                                                else '$newdate' end)
                                    
                                          group by m.menaref, m.datum) a
                                    group by a.datum, a.mena) dd
                              group by dd.datum, dd.mena

                              union all

                              select v.datumdat                                                       as datum,
                                     0                                                                as suma,
                                     rm.refmena                                                       as mena,
                                     round(v.suma / (coalesce(k1.kurz, 1) / coalesce(k2.kurz, 1)), 2) as vv
                              from vklady_vybery_goldmann($clientID, (case
                                                                         when '$dbdate' is null
                                                                             then (select max(m.datum) from majetoktotal m)
                                                                         else '$dbdate' end),
                                                          (case
                                                               when '$newdate' is null
                                                                   then (select max(m.datum) - interval '36 month' from majetoktotal m)
                                                               else '$newdate' end)::date, null) v
                                       left join (select f.refmena
                                                  from fonds f
                                                           left join portfolio p on f.fondid = p.fondid
                                                  where p.podielnikid = $clientID
                                                  group by f.refmena) rm on 1 = 1
                                       left join kurzyaktivarchiv k1
                                                 on 'EUR' || v.mena = k1.ric and v.datumdat = k1.datum
                                                     and k1.datum >= (case
                                                                          when '$newdate' is null
                                                                              then (select max(m.datum) - interval '36 month' from majetoktotal m)
                                                                          else '$newdate' end)
                                       left join kurzyaktivarchiv k2
                                                 on 'EUR' || rm.refmena = k2.ric and v.datumdat = k2.datum
                                                     and k2.datum >= (case
                                                                          when '$newdate' is null
                                                                              then (select max(m.datum) - interval '36 month' from majetoktotal m)
                                                                          else '$newdate' end)) ee
                        group by ee.datum, ee.mena) e) a
            group by a.datum, a.mena) f) g
order by g.datum", defaultDB);
$vykonost = $vykonostRes[1];

$intervalDataRes = Connection::getDataFromDatabase($query, defaultDB);
$intervalData = $intervalDataRes[1];

header('Content-Type: application/json');
echo json_encode(["money" => $intervalData, "return" => $vykonost, "fond" => $fond]);
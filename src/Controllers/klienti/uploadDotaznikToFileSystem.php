<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
$allowed = array("image/jpeg", "image/png", "application/pdf");
$rawData = file_get_contents('php://input');
if (isset($_FILES['fileToUpload']) && $_FILES['fileToUpload']['error'] === UPLOAD_ERR_OK) {
  $file_type = $_FILES['fileToUpload']['type'];
 
    if(in_array($file_type, $allowed)) {
      $fileTmpPath = $_FILES['fileToUpload']['tmp_name'];
      $fileName = $_FILES['fileToUpload']['name'];
      $user_id = $_POST["user_id"];
      $fileData = file_get_contents($fileTmpPath);

      $escapedData = "E'\\\\x".bin2hex($fileData)."'";
      $params = [$fileName, $escapedData, $user_id];
      $query = "UPDATE investicny_dotaznik SET file_name = ?, file = ? WHERE user_id = ?";

      $result = Connection::InsertUpdateCreateDelete($query, $params, defaultDB);
   
      echo json_encode(["error" => false, $escapedData, "result" => $result]);
  } else {
    echo json_encode(["error" => true, "errorMsg" => "Tento typ súboru nie je povolený", "file" => $file_type]);
  }
} else {
    echo json_encode(["error" => true, "errorMsg" => "No file uploaded or there was an upload error.", "kokot" => $file_type]);
}

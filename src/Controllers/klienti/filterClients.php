<?php

require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

$cities = [];
$groups = [];
$isArchived = $_GET['isArchived'];

if (isset($_GET['group'])) {
    $groups = json_decode($_GET['group'], JSON_UNESCAPED_UNICODE);
}

if (isset($_GET['cities'])) {
    $cities = json_decode($_GET['cities'], JSON_UNESCAPED_UNICODE);
}

function buildORPartQuery($dataArray, $columnName): string
{
    $queryStr = "$columnName IN (";
    foreach ($dataArray as $key => $element) {
        if ($key === array_key_last($dataArray)) {
            $queryStr .= "'$element')";
        } else {
            $queryStr .= "'$element',";
        }
    }
    return $queryStr;
}

function buildFilterQuery(): string
{
    global $groups, $cities, $isArchived;
    $queryBase = "SELECT * FROM podielnik ";
    if (!empty($groups)) {
        $queryBase .= "WHERE ";
        $queryBase .= buildORPartQuery($groups, "skupina");
        if (empty($citeis)) {
            if ($isArchived) {
                $queryBase .= " AND archiv = 't'";
            } else {
                $queryBase .= " AND archiv = 'f'";
            }
        }
    }
    if (!empty($cities)) {
        if (!empty($groups)) {
            $queryBase .= " AND ";
        } else {
            $queryBase .= "WHERE ";
        }
        $queryBase .= buildORPartQuery($cities, "city");
        if ($isArchived) {
            $queryBase .= " AND archiv = 't'";
        } else {
            $queryBase .= " AND archiv = 'f'";
        }
    }
    $queryBase .= " ORDER BY prieznaz ASC";
    return $queryBase;
}

$rows = Connection::getDataFromDatabase(buildFilterQuery(), defaultDB);
header('Content-Type: application/json');
if ($rows[0] === 0) {
    echo json_encode(["attention" => "Nenašli sa žiadny klienti podľa vaších filtrovacích kritérií..."]);
} else {
    echo json_encode($rows[1]);
}
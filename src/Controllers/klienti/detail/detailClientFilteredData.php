<?php
require_once('/home/<USER>/www/src/lib/connection.php');
require_once("/home/<USER>/www/conf/settings.php");

$data = json_decode($_POST["data"]);
print_r($data);
$page = $_POST["page"];
$limit = $_POST["limit"];
$clientID = $_POST["id"];

if (isset($_POST["sortingEnabled"]) && $_POST["sortingEnabled"] == true) {
    $sortingConditions = [];

    if (isset($_POST["datumSort"]) && $_POST["datumSort"] !== "") {
        $sortingConditions[] = "a.cislozmluvy " . $_POST["datumSort"];
    }
    if (!empty($sortingConditions)) {
        $sortingQuery = " ORDER BY " . implode(", ", $sortingConditions);
    } else {
        $sortingQuery = "";
    }
}

if (sizeof($data->types) > 0) {
    $typQuery = " AND tr.popis IN (";
    foreach ($data->types as $type) {
        $typQuery .= "'$type',";
    }
    $typQuery = substr($typQuery, 0, -1);
    $typQuery .= ")";
}

//include "transactionList.php";
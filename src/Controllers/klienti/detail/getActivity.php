<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
require_once "/home/<USER>/www/src/lib/notifications/renderNotification.class.php";

$clientID = $_GET["id"];
echo $clientID;

$fondids = Connection::getDataFromDatabase("SELECT fondid FROM portfolio WHERE podielnikid = $clientID", defaultDB)[1];
$fondidsText = implode(', ', array_column($fondids, 'fondid'));
$activityCount = Connection::getDataFromDatabase("SELECT COUNT(*) as count FROM notifications WHERE objektid = $clientID OR objektid IN ($fondidsText)", defaultDB)[1][0]["count"];
$activityNew = Connection::getDataFromDatabase("SELECT n.*, u.username FROM notifications n JOIN users u ON n.userid = u.userid WHERE objektid = $clientID OR objektid IN ($fondidsText) LIMIT 30", defaultDB)[1];
echo "SELECT n.*, u.username FROM notifications n JOIN users u ON n.userid = u.userid WHERE objektid = $clientID OR objektid IN ($fondidsText) LIMIT 30";
$activityPlus = [];
foreach ($fondids as $key => $value) {
    $fondid = $value["fondid"];
    $getActivity = Connection::getDataFromDatabase("SELECT $fondid as fondid, ac.*, a.activitypopis, u.username
        FROM getfondactivityprocesslog($fondid) ac
                LEFT JOIN activity a ON a.activityid = ac.objektid AND ac.action = a.destinacia
                LEFT JOIN users u ON u.userid = ac.userid
        GROUP BY logid, objektid, action, ac.activityid, ac.userid, datetimeactivity, a.activitypopis, u.username
        ORDER BY datetimeactivity DESC
        LIMIT 30
        OFFSET 0;", defaultDB)[1];
    $activityPlus = array_merge($activityPlus, $getActivity);
}
$activityTypes = Connection::getDataFromDatabase("SELECT DISTINCT notification FROM notifications WHERE objektid = $clientID OR objektid IN ($fondidsText)", defaultDB)[1];
$activity = array_merge($activityNew, $activityPlus);
$popisy = [
    "addNote" => [
        "badge" => "Pridanie poznámky",
        "text" => "pridal poznámku"
    ],
    "deleteNote" => [
        "badge" => "Odstránenie poznámky",
        "text" => "odstránil poznámku"
    ],
    "zamerCPCreate" => [
        "badge" => "Vytvorenie investičného zámeru",
        "text" => "vytvoril investičný zámer"
    ],
    "dlhopisPotvrdenieConfirm" => [
        "badge" => "Potvrdenie kupónu",
        "text" => "potvrdil kupón"
    ],
    "dlhopisSplatenieConfirm" => [
        "badge" => "Splatenie kupónu",
        "text" => "splatil kupón"
    ],
    "dlhopisAutomaticPayment" => [
        "badge" => "Automatická splatnosť kupónu",
        "text" => "automaticky splatio kupón"
    ]
];

//print_r($activity);

?>
<div class="mx-auto">
    <div
        class="bg-white rounded-xl shadow-sm overflow-hidden border dark:bg-slate-900 dark:border-slate-600 border-slate-200">
        <div class="p-6 border-b border-slate-100">
            <div class="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                <h2 class="text-xl font-bold dark:text-slate-200 text-slate-800">Aktivita klienta
                    (<?php echo $activityCount; ?>)</h2>
                <form id="filterActivityForm" class="flex flex-wrap gap-2">
                    <input type="hidden" name="id" value="<?php echo $clientID; ?>" />
                    <input type="submit" class="hidden" id="submitFilterForm" />
                    <select class="px-3 py-2 bg-slate-50 dark:bg-slate-800 border filterSelect border-slate-200 rounded-lg text-sm text-slate-600 dark:text-slate-200 focus:outline-none focus:ring-2 focus:ring-primary-500
                         focus:border-primary-500" name="activityType">
                        <option value="all">Všetky aktivity</option>
                        <?php foreach ($activityTypes as $key => $value) { ?>
                            <option value="<?php echo $value["notification"] ?>">
                                <?php echo $popisy[$value["notification"]]["badge"] ?>
                            </option>
                        <?php } ?>
                    </select>
                    <select class="px-3 py-2 bg-slate-50 dark:bg-slate-800 border filterSelect border-slate-200 rounded-lg text-sm text-slate-600 dark:text-slate-200 focus:outline-none focus:ring-2 focus:ring-primary-500
                         focus:border-primary-500" name="timeframe">
                        <option value="30">Posledných 30 dní</option>
                        <option value="90">Posledných 90 dní</option>
                        <option value="180">Posledných 6 mesiacov</option>
                        <option value="365">Posledný rok</option>
                        <option value="all">Všetko</option>
                    </select>
                </form>
            </div>
        </div>
        <div class="p-6">
            <div id="resultMore" class="mb-8">
                <?php foreach ($activity as $key => $item) {
                    $notif = new RenderNotification(
                        $item["activityid"],
                        $item["destinacia"] ? $item["destinacia"] : $item["action"],
                        $item["objektid"],
                        $item["action"],
                        $userid ? $item["userid"] : 0,
                        $item["username"] ? $item["username"] : "System",
                        $item["notification"] ? $item["notification"] : $item["action"],
                        $item["objektdetail"] ? $item["objektdetail"] : json_encode(["dealid", $item["objektid"]]),
                        $item["needsmentioning"] ? $item["needsmentioning"] : 0,
                        $fondidsText
                    );

                    $notificationID = $item["id"];
                    if ($notificationID !== NULL) {
                        $notifDetail = Connection::getDataFromDatabase("SELECT * FROM notifications WHERE id = $notificationID", defaultDB)[1][0]["objektdetail"];
                        $notifDetail = json_decode($notifDetail);
                        $column = $notifDetail[0];
                        $data = "'$notifDetail[1]'";
                        $destinacia = $item["destinacia"];
                        $detail = Connection::getDataFromDatabase("SELECT * FROM $destinacia WHERE $column = $data", defaultDB)[1];
                    }
                    ?>
                    <div class="relative  pl-8 pb-8">
                        <div class="absolute left-3 top-1.5 -bottom-0 w-px dark:bg-gray-800 bg-slate-200"></div>
                        <div
                            class="absolute left-0 top-1.5 w-6 h-6 rounded-full bg-green-100 border-4 border-white flex items-center justify-center">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"
                                class="w-3 h-3 text-green-600">
                                <path
                                    d="M10.75 10.818v2.614A3.13 3.13 0 0011.888 13c.482-.315.612-.648.612-.875 0-.227-.13-.56-.612-.875a3.13 3.13 0 00-1.138-.432zM8.33 8.62c.053.055.115.11.184.164.208.16.46.284.736.363V6.603a2.45 2.45 0 00-.35.13c-.14.065-.27.143-.386.233-.377.292-.514.627-.514.909 0 .184.058.39.202.592.037.051.08.102.128.152z" />
                                <path fill-rule="evenodd"
                                    d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-6a.75.75 0 01.75.75v.316a3.78 3.78 0 011.653.713c.426.33.744.74.925 1.2a.75.75 0 01-1.395.55 1.35 1.35 0 00-.447-.563 2.187 2.187 0 00-.736-.363V9.3c.698.093 1.383.32 1.959.696.787.514 1.29 1.27 1.29 2.13 0 .86-.504 1.616-1.29 2.13-.576.377-1.261.603-1.96.696v.299a.75.75 0 11-1.5 0v-.3c-.697-.092-1.382-.318-1.958-.695-.482-.315-.857-.717-1.078-1.188a.75.75 0 111.359-.636c.08.173.245.376.54.569.313.205.706.353 1.138.432v-2.748a3.782 3.782 0 01-1.653-.713C6.9 9.433 6.5 8.681 6.5 7.875c0-.805.4-1.558 1.097-2.096a3.78 3.78 0 011.653-.713V4.75A.75.75 0 0110 4z"
                                    clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div
                            class="bg-white dark:bg-gray-700 rounded-lg border border-slate-200 p-4 hover:shadow-sm transition-shadow">
                            <div class="flex flex-col sm:flex-row sm:items-center justify-between gap-2 mb-2">
                                <div class="flex items-center gap-2">
                                    <span
                                        class="<?php echo str_contains($item["notification"], "add") || str_contains($item["notification"], "create") ? "bg-green-200 text-green-800" : "bg-red-200 text-red-800"; ?>
                                         text-xs font-medium px-2.5 py-0.5 rounded-full"><?php echo ($popisy[$item["notification"]]["badge"] ? $popisy[$item["notification"]]["badge"] : ($item["notification"] ? $item["notification"] : $item["action"])); ?></span>
                                    <span
                                        class="text-slate-400 dark:text-slate-100 text-sm"><?php echo explode("+", $item["datetimeactivity"])[0]; ?></span>
                                </div>
                                <span
                                    class="text-slate-500 dark:text-slate-200 text-sm"><?php echo $item["username"]; ?></span>
                            </div>

                            <h4 class="font-medium text-slate-800 dark:text-gray-100">
                                <?php echo $item["username"] . " " . $popisy[$item["action"]]["text"]; ?>
                            </h4>
                            <p class="text-slate-600 dark:bg-gray-500 p-2 rounded-lg dark:text-gray-200 mt-1">
                                <?php echo $notif->render($item["id"] ? $item["id"] : $item["logid"], $item["id"] ? false : true); ?>
                            </p>
                        </div>
                    </div>
                <?php } ?>
            </div>
        </div>
        <?php if ($count > 30) { ?>
            <div class="p-6 border-t border-slate-100 flex justify-center">
                <button class="text-primary-600 hover:text-primary-700 font-medium text-sm flex items-center">
                    Načítať viac
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4 ml-1">
                        <path fill-rule="evenodd"
                            d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z"
                            clip-rule="evenodd" />
                    </svg>
                </button>
            </div>
        <?php } ?>
    </div>
</div>
<script>
    $(".filterSelect").on("change", (e) => {
        $("#submitFilterForm").click();
    });

    $("#filterActivityForm").on("submit", (e) => {
        e.preventDefault();
        const formData = new FormData(e.currentTarget);
        const activityType = formData.get("activityType");
        const timeframe = formData.get("timeframe");
        const clientID = formData.get("id");
        htmx.ajax('POST', `/api/klienti/detail/filterActivity`, {
            target: "#resultMore",
            values: { "activityType": activityType, "timeframe": timeframe, id: clientID }
        });
    });
</script>
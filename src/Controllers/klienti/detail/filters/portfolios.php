<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

$clientID = $_POST["id"];
$portfolios = Connection::getDataFromDatabase("SELECT cislozmluvy, fondid FROM portfolio WHERE podielnikid = $clientID", defaultDB)[1];
?>

<ul class="p-3 text-sm text-gray-700 dark:text-gray-200" aria-labelledby="dropdownRadioButton">
    <?php foreach ($portfolios as $key => $portfolio) { ?>
        <li class="mb-0.5">
            <label
                class="w-full text-sm font-medium text-gray-900 rounded-sm dark:text-gray-300 flex cursor-pointer transition-all items-center gap-2 p-2 rounded-sm hover:bg-gray-100 dark:hover:bg-gray-600">
                <input type="checkbox" onchange="submitWholeForm(this)"
                    value="<?php echo $portfolio["fondid"] ? $portfolio["fondid"] : "0"; ?>" name="fondidFilter"
                    class="w-4 h-4 filterInput text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                <?php echo $portfolio["cislozmluvy"] ? $portfolio["cislozmluvy"] : "-"; ?>
            </label>
        </li>
    <?php } ?>
</ul>
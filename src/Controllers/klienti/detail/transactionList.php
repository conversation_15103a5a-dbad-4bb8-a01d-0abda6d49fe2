<?php
$query = "WITH ob_ma_p AS (SELECT ob.id,
                        ob.vs,
                        ob.mena,
                        ma.pocet,
                        ma.obratdatatimezauctovane,
                        ma.md_d,
                        p.c<PERSON><PERSON><PERSON><PERSON>,
                        p.podiel<PERSON>,
                        ma.kod<PERSON><PERSON>,
                        ma.u<PERSON>,
                        ma.o<PERSON>ti<PERSON>,
                        ma.subjektid
                 FROM obratybu ob
                          JOIN obratybuobratid obo ON ob.id = obo.id
                          JOIN majetokarchiv ma ON ma.obratid = obo.obratid
                          JOIN portfolio p ON ma.subjektid = p.fondid
                 WHERE ob.logactivityid = 15
                   AND ob.krdb = 1
                   AND ob.obratdatetime BETWEEN '$datumodvst' AND '$datumodvst'
                   AND p.podielnikid IN ($clientID)),

     konfirmacie AS (SELECT k.dealid::text,
                            k.iv_b,
                            k.suma_dane,
                            k.subjektid
                     FROM konfirmaciaktv k),
     splatenia AS (SELECT s.dealid::text,
                          s.tranza,
                          s.suma,
                          s.mena,
                          s.datum<PERSON><PERSON>,
                          s.uctovny<PERSON>d,
                          s.kodaktiva,
                          s.subjektid,
                          s.datum_naroku
                   FROM splatenie s
                   WHERE s.datumvyplaty BETWEEN '$datumodvst' AND '$datumdovst'),

     float_data AS (SELECT f.datesplatnost,
                           f.isincurrric,
                           f.istina
                    FROM floatkupon f),

     equity_data AS (SELECT e.isin,
                            e.cpnaz,
                            e.isinreal,
                            c.isincurr,
                            r.isincurrric
                     FROM dbequity e
                              JOIN dbequitycurr c ON e.isin = c.isin
                              JOIN dbequitycurrric r ON r.isincurr = c.isincurr),

     pool_data AS (SELECT po.poolid,
                          po.dealid::text,
                          pdr.transsumareal,
                          pdr.subjektid,
                          pdr.auvreal,
                          pdr.dan
                   FROM pool po
                            JOIN pooldetailreal pdr ON po.poolid = pdr.poolid),
     filtered_portfolio AS (SELECT *
                            FROM portfolio p
                            WHERE p.podielnikid IN ($clientID)),
     poplatok_data AS (SELECT CASE pr.typ
                                  WHEN 'TRAN' THEN 320
                                  WHEN 'VYROV' THEN 330
                                  WHEN 'SPRAVA' THEN 310
                                  WHEN 'MANAZ' THEN 300
                                  WHEN 'OSTATNE' THEN 360
                                  END               AS poradie,

                              CASE pr.typ
                                  WHEN 'TRAN' THEN 'Poplatok za transakciu s CP'
                                  WHEN 'VYROV' THEN 'Poplatok za vysporiadanie transakcie s CP'
                                  WHEN 'SPRAVA' THEN 'Poplatok za správu portfólia'
                                  WHEN 'MANAZ' THEN 'Poplatok za riadenie portfólia'
                                  WHEN 'OSTATNE' THEN 'Poplatok'
                                  END               AS popis,

                              pr.mena::varchar      AS mena1,
                              pr.suma::numeric      AS transsuma,
                              u.datesplatnost::date AS datum,
                              (CASE pr.typ
                                   WHEN 'TRAN' THEN 'Dátum obchodu ' || to_char(pr.datum, 'DD.MM.YYYY') || '; ISIN ' ||
                                                    kcp.ISIN
                                   WHEN 'VYROV' THEN 'Dátum obchodu ' || to_char(pr.datum, 'DD.MM.YYYY') || '; ISIN ' ||
                                                     kcp.ISIN
                                   WHEN 'SPRAVA' THEN to_char(pr.datumod, 'DD.MM.YYYY') || ' - ' ||
                                                      to_char(pr.datum, 'DD.MM.YYYY')
                                   WHEN 'MANAZ' THEN to_char(pr.datumod, 'DD.MM.YYYY') || ' - ' ||
                                                     to_char(pr.datum, 'DD.MM.YYYY')
                                   WHEN 'OSTATNE' THEN pr.dovod
                                   ELSE ''
                                  END)::text        AS poznamka,
                              ma.md_d::numeric,
                              p.cislozmluvy::varchar,
                              p.podielnikid::numeric,
                              NULL::numeric         AS idpohybu,
                              NULL                  AS dealid,
                              NULL                  AS eqid
                       FROM uhrada u
                                JOIN uhradaobratid ui ON u.id = ui.id
                                JOIN majetokarchiv ma ON ma.obratid = ui.obratid
                                JOIN filtered_portfolio p ON ma.subjektid = p.fondid
                                JOIN poplatok_register_links prl ON prl.uhrada_id = u.id
                                JOIN poplatok_register pr ON pr.id = prl.poplatok_register_id
                                LEFT JOIN konfirmaciacp kcp ON pr.dealid::text = kcp.dealid::text
                       WHERE u.kodobratu = 350
                         AND ma.uctovnykod = 221110
                         AND u.logactivityid = 15
                         AND u.datesplatnost BETWEEN '$datumodvst' AND '$datumdovst'
                         AND pr.typ IN ('TRAN', 'VYROV', 'SPRAVA', 'MANAZ', 'OSTATNE')
                         AND pr.fondid = ma.subjektid),
     majetokarchiv_data AS (SELECT CASE ma.kodobratu
                                       WHEN 346 THEN 350
                                       WHEN 345 THEN 340
                                       WHEN 310 THEN 370
                                       WHEN 344 THEN 380
                                       END                          AS poradie,

                                   CASE ma.kodobratu
                                       WHEN 346 THEN 'Poplatok za konverziu'
                                       WHEN 345 THEN 'Poplatok za obchod na peňažnom trhu'
                                       WHEN 310 THEN 'Vstupný poplatok'
                                       WHEN 344 THEN 'Poplatok za mimoriadny výpis'
                                       ELSE ''
                                       END                          AS popis,
                                   ma.mena::varchar                 AS mena1,
                                   ma.pocet::numeric                AS transsuma,
                                   ma.obratdatatimezauctovane::date AS datum,
                                   ''::text                         AS poznamka,
                                   ma.md_d::numeric,
                                   p.cislozmluvy::varchar,
                                   p.podielnikid::numeric,
                                   NULL::numeric                    AS idpohybu,
                                   NULL                             AS dealid,
                                   NULL                             AS eqid
                            FROM majetokarchiv ma
                                     JOIN filtered_portfolio p ON ma.subjektid = p.fondid
                            WHERE ma.kodobratu IN (346, 345, 310, 344)
                              AND ma.uctovnykod IN (261992, 261991, 261961, 261995)
                              AND ma.obratdatatimezauctovane BETWEEN '$datumodvst' AND '$datumdovst'),

     pool_detail AS (SELECT p.dealid::text,
                            pd.subjektid,
                            pd.transsumareal
                     FROM pool p
                              JOIN pooldetailreal pd
                                   ON pd.poolid = p.poolid),

     konv_incoming AS (SELECT mak.obratdatatimezauctovane,
                              mak.pocet,
                              mak.mena,
                              mak.subjektid,
                              (CASE
                                   WHEN k.menadebet || k.menakredit = k.menovypar
                                       THEN k.menadebet || '/' || k.menakredit
                                   ELSE k.menakredit || '/' || k.menadebet
                                  END)
                                  || ' kurz = '
                                  || (CASE
                                          WHEN k.kurz < 1
                                              THEN replace(('0' || k.kurz::text), '.', ',')
                                          ELSE replace(k.kurz::text, '.', ',')
                                  END)
                                  AS poznamka
                       FROM konverzia k
                                JOIN konverziaobratid ko ON ko.dealid::text = k.dealid::text
                                JOIN majetokarchiv mak
                                     ON mak.obratid = ko.obratid
                                         AND mak.kodobratu = 237
                                         AND mak.uctovnykod = 315160),
     k1_cte AS (SELECT k1.dealid::text,
                       k1.dealid_related,
                       k1.subjektid,
                       k1.mena,
                       k1.suma,
                       k1.datum_zauctovania,
                       k1.druhobchodu
                FROM konfirmaciapp k1
                WHERE k1.logactivityid = 12
                  AND k1.druhobchodu = 'prevod'
                  AND k1.subjektid <> 0
                  AND k1.datum_zauctovania BETWEEN '$datumodvst' AND '$datumdovst'),
     to_portfolio AS (SELECT k.*,
                             fp.cislozmluvy      AS own_cislozmluvy,
                             fp.podielnikid      AS own_podielnikid,
                             k1_target.subjektid AS target_subjektid
                      FROM k1_cte k
                               JOIN filtered_portfolio fp ON fp.fondid = k.subjektid
                               JOIN konfirmaciapp k1_target
                                    ON k1_target.dealid::text = k.dealid_related::text
                      WHERE k.dealid_related IS NOT NULL),

     from_portfolio AS (SELECT k.*,
                               fp.cislozmluvy      AS own_cislozmluvy,
                               fp.podielnikid      AS own_podielnikid,
                               k2_source.subjektid AS target_subjektid
                        FROM k1_cte k
                                 JOIN filtered_portfolio fp ON fp.fondid = k.subjektid
                                 JOIN konfirmaciapp k2_source
                                      ON k2_source.dealid_related::text = k.dealid::text
                        WHERE k.dealid_related IS NULL),

     combinedPrevody AS (SELECT 410  AS poradie,
                                'na' AS direction,
                                t.mena,
                                t.suma,
                                t.datum_zauctovania,
                                t.own_cislozmluvy,
                                t.own_podielnikid,
                                t.target_subjektid
                         FROM to_portfolio t

                         UNION ALL

                         SELECT 100 AS poradie,
                                'z' AS direction,
                                f.mena,
                                f.suma,
                                f.datum_zauctovania,
                                f.own_cislozmluvy,
                                f.own_podielnikid,
                                f.target_subjektid
                         FROM from_portfolio f),
     konf_obrat AS (SELECT oo.obratid, k.subjektid
                    FROM konfirmaciapp k
                             JOIN obratybu o
                                  ON o.subjektid = k.subjektid
                                      AND o.ss::text = k.dealid::text
                                      AND o.suma = k.suma
                                      AND o.mena = k.mena
                                      AND o.cub = k.ucet
                             JOIN obratybuobratid oo
                                  ON oo.id = o.id
                    WHERE k.logactivityid = 12),
     reverse_m AS (SELECT obratdatatimezauctovane, subjektid
                   FROM majetokarchiv
                   WHERE uctovnykod = 668000
                     AND md_d = 0
                     AND kodobratu = 214),
     filtered_obratybu AS (SELECT id, obratdatetime, suma, mena
                           FROM obratybu
                           WHERE logactivityid = 15
                             AND krdb = 1
                             AND obratdatetime BETWEEN '$datumodvst' AND '$datumdovst'),
     filtered_majetok AS (SELECT obratid, subjektid, md_d
                          FROM majetokarchiv
                          WHERE kodobratu = 201
                            AND uctovnykod = 325300),
     kodobratu_map AS (SELECT *
                       FROM (VALUES (302, 210, 'Nákup dlhopisu', 'Bonds'),
                                    (303, 420, 'Výber klienta', NULL),
                                    (331, 220, 'Nákup akcie', NULL),
                                    (332, 230, 'Nákup fondu', NULL)) AS m(kodobratu, poradie, popis, eqid)),
     transsuma_lookup AS (SELECT pd.transsumareal, po.dealid, pd.subjektid
                          FROM pooldetailreal pd
                                   JOIN pool po ON pd.poolid = po.poolid),
     cp_info AS (SELECT k.dealid, d.cpnaz || '; ISIN ' || d.isinreal AS cp_detail
                 FROM konfirmaciacp k
                          JOIN dbequity d ON d.isin = k.isin),
     filter_prep AS (SELECT $clientID::int        AS klient_id,
                            '$datumdovst'::date AS datum_do)
SELECT * FROM
(SELECT 120                    AS poradie,
       'Vklad klienta'        AS popis,
       ob.mena::varchar       AS mena1,
       ob.suma::numeric       AS transsuma,
       ob.obratdatetime::date AS datum,
       ''::text               AS poznamka,
       ma.md_d::numeric,
       p.cislozmluvy::varchar AS cislozmluvy,
       p.podielnikid::numeric,
       ma.obratid::numeric    AS idpohybu,
       ob.id::text            AS dealid,
       NULL                   AS eqid

FROM filtered_obratybu ob
         JOIN obratybuobratid obo
              ON ob.id = obo.id
         JOIN filtered_majetok ma
              ON ma.obratid = obo.obratid
         JOIN filtered_portfolio p
              ON p.fondid = ma.subjektid
UNION ALL
SELECT m.poradie,
       m.popis,
       u.mena  AS mena1,
       CASE
           WHEN u.kodobratu = 303 THEN u.suma
           ELSE COALESCE(ts.transsumareal, r.transsuma)
           END AS transsuma,
       CASE
           WHEN u.kodobratu = 303 THEN u.datesplatnost
           ELSE ma.obratdatatimezauctovane
           END AS datum,
       CASE
           WHEN u.kodobratu = 303 THEN ''
           ELSE ci.cp_detail
           END AS poznamka,
       ma.md_d,
       p.cislozmluvy::varchar,
       p.podielnikid,
       CASE
           WHEN u.kodobratu = 303 THEN ma.obratid
           ELSE u.dealid
           END AS idpohybu,
       CASE
           WHEN u.kodobratu = 303 THEN u.id::varchar
           ELSE u.dealid::varchar
           END AS dealid,
       m.eqid
FROM uhrada u
         JOIN kodobratu_map m ON u.kodobratu = m.kodobratu
         LEFT JOIN rekonfirmaciacp r ON u.dealid = r.dealid AND u.tranza = r.tranza
         JOIN uhradaobratid ui ON u.id = ui.id
         JOIN majetokarchiv ma ON ma.obratid = ui.obratid
         JOIN portfolio p ON ma.subjektid = p.fondid
         LEFT JOIN cp_info ci ON ci.dealid = r.dealid
         LEFT JOIN transsuma_lookup ts
                   ON ts.dealid = r.dealid AND ts.subjektid = p.fondid
WHERE u.kodobratu IN (302, 303, 331, 332)
  AND ma.kodobratu IN (302, 303, 331, 332)
  AND ma.uctovnykod IN (261920, 261930, 261911, 261912)
  AND u.logactivityid = 15
  AND ma.obratdatatimezauctovane BETWEEN '$datumodvst' AND '$datumdovst'
  AND p.podielnikid IN ($clientID)
UNION ALL
SELECT 240                                                  AS poradie,
       'Nákup fondu Polaris Finance SICAV p.l.c.'           AS popis,
       (CASE fp.mena
            WHEN 1 THEN 'EUR'
            WHEN 5 THEN 'CZK'
           END)::varchar                                    AS mena1,
       ABS(ROUND(mp.suma / NULLIF(mp.kurz, 1), 3))::numeric AS transsuma,
       mp.datum::date,
       ('Emisia podielov fondu ' || fp.nazov || ' ' ||
        REPLACE(mp.pocet::text, '.', ',') || ' kusov @' ||
        REPLACE(ROUND(mp.nav, 4)::text, '.', ','))::text    AS poznamka,
       1.0::numeric                                         AS md_d,
       po.regnumber_polaris::varchar                        AS cislozmluvy,
       po.podielnikid::numeric,
       mp.id_pohyb::numeric                                 AS idpohybu,
       mp.id_pohyb::text                                    AS dealid,
       NULL                                                 AS eqid
FROM majetok_polaris mp
         JOIN podielnik po ON mp.id_podielnik = po.podielnikid_polaris
         JOIN fondy_polaris fp ON mp.druh_podielu = fp.druh_podielu
         JOIN filter_prep f ON true
WHERE po.podielnikid = f.klient_id
  AND mp.typ_pohybu IN (1, 4, 7)
  AND mp.datum <= f.datum_do
UNION ALL
SELECT 240                                                      AS poradie,
       'Stock Split - Nákup fondu Polaris Finance SICAV p.l.c.' AS popis,
       (CASE fp.mena
            WHEN 1 THEN 'EUR'
            WHEN 5 THEN 'CZK'
           END)::varchar                                        AS mena1,
       ABS(ROUND(mp.suma / NULLIF(mp.kurz, 1), 3))::numeric     AS transsuma,
       mp.datum::date,
       ('Emisia podielov fondu ' || fp.nazov || ' ' ||
        REPLACE(mp.pocet::text, '.', ',') || ' kusov @' ||
        REPLACE(ROUND(mp.nav, 4)::text, '.', ','))::text        AS poznamka,
       1.0::numeric                                             AS md_d,
       po.regnumber_polaris::varchar                            AS cislozmluvy,
       po.podielnikid::numeric,
       mp.id_pohyb::numeric                                     AS idpohybu,
       mp.id_pohyb::varchar                                     AS dealid,
       NULL                                                     AS eqid
FROM majetok_polaris mp
         JOIN podielnik po ON mp.id_podielnik = po.podielnikid_polaris
         JOIN fondy_polaris fp ON mp.druh_podielu = fp.druh_podielu
         JOIN filter_prep f ON true
WHERE po.podielnikid = f.klient_id
  AND mp.typ_pohybu = 10 -- Stock Split only
  AND mp.datum <= f.datum_do
UNION ALL
SELECT 70                                               AS poradie,
       'Platba za predaj akcie'                         AS popis,
       ma.jednotka::varchar                             AS mena1,
       COALESCE(pd.transsumareal, r.transsuma)::numeric AS transsuma,
       r.datvysporiadaniabureal::date                   AS datum,
       ci.cp_detail::text                               AS poznamka,
       0::numeric,
       p.cislozmluvy::varchar,
       p.podielnikid::numeric,
       kcp.dealid::numeric                              AS idpohybu,
       r.dealid::text                                   AS dealid,
       NULL                                             AS eqid
FROM rekonfirmaciacpobratid ro
         JOIN rekonfirmaciacp r ON r.dealid = ro.dealid AND r.tranza = ro.tranza
         JOIN konfirmaciacp kcp ON kcp.dealid = r.dealid
         JOIN majetokarchiv ma ON ro.obratid = ma.obratid
         JOIN portfolio p ON ma.subjektid = p.fondid
         LEFT JOIN pool_data pd ON pd.dealid::text = r.dealid::text AND pd.subjektid = ma.subjektid
         LEFT JOIN cp_info ci ON ci.dealid::text = r.dealid::text
WHERE ma.kodobratu = 231
  AND ma.uctovnykod = 261710
  AND r.datvysporiadaniabureal BETWEEN '$datumodvst' AND '$datumdovst'
  AND p.podielnikid = $clientID
UNION ALL
SELECT 60                                               AS poradie,
       'Platba za predaj dlhopisu'                      AS popis,
       ma.jednotka::varchar                             AS mena1,
       COALESCE(pt.transsumareal, r.transsuma)::numeric AS transsuma,
       r.datvysporiadaniabureal::date                   AS datum,
       ci.cp_detail::text                               AS poznamka,
       ma.md_d::numeric,
       p.cislozmluvy::varchar,
       p.podielnikid::numeric,
       kcp.dealid::numeric                              AS idpohybu,
       r.dealid::text                                   AS dealid,
       'Bonds'                                          AS eqid
FROM rekonfirmaciacpobratid ro
         JOIN rekonfirmaciacp r ON r.dealid::text = ro.dealid::text AND r.tranza = ro.tranza
         JOIN konfirmaciacp kcp ON kcp.dealid::text = r.dealid::text
         JOIN majetokarchiv ma ON ro.obratid = ma.obratid
         JOIN portfolio p ON ma.subjektid = p.fondid
         LEFT JOIN pool_data pt ON pt.dealid::text = r.dealid::text AND pt.subjektid = ma.subjektid
         LEFT JOIN cp_info ci ON ci.dealid::text = r.dealid::text
WHERE ma.kodobratu = 134
  AND ma.uctovnykod = 261210
  AND r.datvysporiadaniabureal BETWEEN '$datumodvst' AND '$datumdovst'
  AND p.podielnikid = $clientID
UNION ALL
SELECT 80                                               AS poradie,
       'Platba za predaj fondu'                         AS popis,
       ma.jednotka::varchar                             AS mena1,
       COALESCE(pt.transsumareal, r.transsuma)::numeric AS transsuma,
       ma.obratdatatimezauctovane::date                 AS datum,
       ci.cp_detail::text                               AS poznamka,
       ma.md_d::numeric,
       p.cislozmluvy::varchar,
       p.podielnikid::numeric,
       r.dealid::numeric                                AS idpohybu,
       r.dealid::text                                   AS dealid,
       NULL                                             AS eqid
FROM rekonfirmaciacpobratid ro
         JOIN rekonfirmaciacp r ON r.dealid = ro.dealid AND r.tranza = ro.tranza
         JOIN majetokarchiv ma ON ro.obratid = ma.obratid
         JOIN portfolio p ON ma.subjektid = p.fondid
         LEFT JOIN pool_data pt ON pt.dealid::text = r.dealid::text AND pt.subjektid = ma.subjektid
         LEFT JOIN cp_info ci ON ci.dealid::text = r.dealid::text
WHERE ma.kodobratu = 233
  AND ma.uctovnykod = 325300
  AND ma.obratdatatimezauctovane BETWEEN '$datumodvst' AND '$datumdovst'
  AND p.podielnikid = $clientID
UNION ALL
SELECT 81                                                   AS poradie,
       'Predaj fondu Polaris Finance SICAV p.l.c.'          AS popis,
       (CASE fp.mena
            WHEN 1 THEN 'EUR'
            WHEN 5 THEN 'CZK'
           END)::varchar                                    AS mena1,
       ABS(ROUND(mp.suma / NULLIF(mp.kurz, 1), 3))::numeric AS transsuma,
       mp.datum::date,
       ('Redemácia podielov fondu ' || fp.nazov || ' ' ||
        REPLACE(mp.pocet::text, '.', ',') || ' kusov @' ||
        REPLACE(ROUND(mp.nav, 4)::text, '.', ','))::text    AS poznamka,
       0.0::numeric                                         AS md_d,
       po.regnumber_polaris::varchar                        AS cislozmluvy,
       po.podielnikid::numeric,
       mp.id_pohyb::numeric                                 AS idpohybu,
       mp.id_pohyb::text                                    AS dealid,
       NULL                                                 AS eqid
FROM majetok_polaris mp
         JOIN podielnik po ON mp.id_podielnik = po.podielnikid_polaris
         JOIN fondy_polaris fp ON mp.druh_podielu = fp.druh_podielu
         JOIN filter_prep p ON true
WHERE po.podielnikid = p.klient_id
  AND mp.typ_pohybu IN (2, 5, 8)
  AND mp.datum <= p.datum_do
UNION ALL
SELECT 81                                                        AS poradie,
       'Stock Split - Predaj fondu Polaris Finance SICAV p.l.c.' AS popis,
       (CASE fp.mena
            WHEN 1 THEN 'EUR'
            WHEN 5 THEN 'CZK'
           END)::varchar                                         AS mena1,
       ABS(ROUND(mp.suma / NULLIF(mp.kurz, 1), 3))::numeric      AS transsuma,
       mp.datum::date,
       ('Redemácia podielov fondu ' || fp.nazov || ' ' ||
        REPLACE(mp.pocet::text, '.', ',') || ' kusov @' ||
        REPLACE(ROUND(mp.nav, 4)::text, '.', ','))::text         AS poznamka,
       0.0::numeric                                              AS md_d,
       po.regnumber_polaris::varchar                             AS cislozmluvy,
       po.podielnikid::numeric,
       mp.id_pohyb::numeric                                      AS idpohybu,
       mp.id_pohyb::text                                         AS dealid,
       NULL                                                      AS eqid
FROM majetok_polaris mp
         JOIN podielnik po ON mp.id_podielnik = po.podielnikid_polaris
         JOIN fondy_polaris fp ON mp.druh_podielu = fp.druh_podielu
         JOIN filter_prep p ON true
WHERE po.podielnikid = p.klient_id
  AND mp.typ_pohybu = 9
  AND mp.datum <= p.datum_do
UNION ALL
SELECT 430                                                          AS poradie,
       'Zriadenie termínovaného vkladu'                             AS popis,
       u.mena                                                       AS mena1,
       COALESCE(pdr.transsumareal_pov, pdr.transsumareal, r.sum_td) AS transsuma,
       ma.obratdatatimezauctovane                                   AS datum,
       ''                                                           AS poznamka,
       ma.md_d,
       p.cislozmluvy::varchar,
       p.podielnikid,
       r.dealid                                                     AS idpohybu,
       r.dealid::text                                               AS dealid,
       NULL                                                         AS eqid
FROM uhrada u
         JOIN uhradaobratid ui
              ON u.id = ui.id
                  AND u.kodobratu = 301
                  AND u.logactivityid = 15
         JOIN majetokarchiv ma
              ON ma.obratid = ui.obratid
                  AND ma.kodobratu = 301
                  AND ma.uctovnykod = 261910
                  AND ma.obratdatatimezauctovane BETWEEN '$datumodvst' AND '$datumdovst'
         JOIN portfolio p
              ON ma.subjektid = p.fondid
                  AND p.podielnikid = $clientID
         JOIN konfirmaciaktv r
              ON u.dealid = r.dealid
         LEFT JOIN pool po
                   ON po.dealid = r.dealid
         LEFT JOIN pooldetailreal pdr
                   ON pdr.poolid = po.poolid
                       AND pdr.subjektid = ma.subjektid
UNION ALL
SELECT 10                               AS poradie,
       'Splatenie termínovaného vkladu' AS popis,
       ob.mena::varchar,
       ma.pocet::numeric,
       ma.obratdatatimezauctovane::date AS datum,
       ''::text                         AS poznamka,
       ma.md_d::numeric,
       p.cislozmluvy::varchar,
       p.podielnikid::numeric,
       NULL::numeric                    AS idpohybu,
       ob.vs::text                      AS dealid,
       NULL                             AS eqid
FROM ob_ma_p ob
         JOIN majetokarchiv ma ON ob.obratid = ma.obratid
         JOIN portfolio p ON ob.subjektid = p.fondid
WHERE ob.kodobratu = 203
  AND ob.uctovnykod = 325300

UNION ALL

SELECT 20,
       'Splatenie úroku z termínovaného vkladu',
       ob.mena::varchar,
       ob.pocet::numeric,
       ob.obratdatatimezauctovane::date,
       'Úrok brutto = ' || REPLACE(REPLACE(k.iv_b::text, ',', ' '), '.', ',') ||
       '; Daň = ' || REPLACE(REPLACE(k.suma_dane::text, ',', ' '), '.', ',')::text,
       ob.md_d::numeric,
       ob.cislozmluvy::varchar,
       ob.podielnikid::numeric,
       NULL::numeric,
       k.dealid::text,
       NULL
FROM ob_ma_p ob
         JOIN konfirmacie k ON k.dealid::text = ob.vs::text AND k.subjektid = 1 -- assumed active match

WHERE ob.kodobratu = 204
  AND ob.uctovnykod = 325300
  AND ob.obratdatatimezauctovane BETWEEN '$datumodvst' AND '$datumdovst'

UNION ALL

SELECT 20,
       'Splatenie úroku z termínovaného vkladu',
       ob.mena::varchar,
       ob.pocet::numeric,
       ob.obratdatatimezauctovane::date,
       'Úrok brutto = ' || REPLACE(REPLACE(pd.auvreal::text, ',', ' '), '.', ',') ||
       '; Daň = ' || REPLACE(REPLACE(pd.dan::text, ',', ' '), '.', ',')::text,
       ob.md_d::numeric,
       ob.cislozmluvy::varchar,
       ob.podielnikid::numeric,
       NULL::numeric,
       k.dealid::text,
       NULL
FROM ob_ma_p ob
         JOIN konfirmacie k ON k.dealid::text = ob.vs::text AND k.subjektid = 0
         JOIN pool_data pd ON k.dealid::text = pd.dealid::text AND pd.subjektid = ob.subjektid
WHERE ob.kodobratu = 204
  AND ob.uctovnykod = 325300
  AND ob.obratdatatimezauctovane BETWEEN '$datumodvst' AND '$datumdovst'

UNION ALL

SELECT 40,
       'Splatenie kupónu',
       ma.mena::varchar,
       s.suma::numeric,
       ma.obratdatatimezauctovane::date,
       (ed.cpnaz || '; ISIN ' || ed.isinreal)::text,
       0::numeric,
       p.cislozmluvy::varchar,
       p.podielnikid::numeric,
       NULL::numeric,
       ma.kodaktiva::text,
       'Bonds'
FROM splatenia s
         JOIN splatenieobratid so ON s.dealid::text = so.dealid::text AND s.tranza = so.tranza
         JOIN majetokarchiv ma ON so.obratid = ma.obratid
         JOIN portfolio p ON ma.subjektid = p.fondid
         JOIN equity_data ed ON ed.isincurrric = ma.kodaktiva
WHERE ma.kodobratu = 137
  AND ma.uctovnykod = 315124
  AND ma.destinacia = 'dbequity'
  AND p.podielnikid IN ($clientID)

UNION ALL

SELECT 35,
       'Splatenie dlhopisu',
       s.mena::varchar,
       s.suma::numeric,
       s.datumvyplaty::date,
       (ed.cpnaz || '; ISIN ' || ed.isinreal)::text,
       0::numeric,
       p.cislozmluvy::varchar,
       p.podielnikid::numeric,
       NULL::numeric,
       s.kodaktiva::text,
       'Bonds'
FROM splatenia s
         JOIN float_data f ON s.kodaktiva = f.isincurrric AND s.datum_naroku = f.datesplatnost AND f.istina = 100
         JOIN equity_data ed ON ed.isincurrric = s.kodaktiva
         JOIN portfolio p ON s.subjektid = p.fondid
WHERE s.uctovnykod = 251110
  AND p.podielnikid IN ($clientID)

UNION ALL

SELECT 36,
       'Čiastočné splatenie istiny',
       s.mena::varchar,
       s.suma::numeric,
       s.datumvyplaty::date,
       (ed.cpnaz || '; ISIN ' || ed.isinreal || '; emitent splatil ' || f.istina || '% z nominálnej hodnoty')::text,
       0::numeric,
       p.cislozmluvy::varchar,
       p.podielnikid::numeric,
       NULL::numeric,
       NULL::text,
       'Bonds'
FROM splatenia s
         JOIN float_data f ON s.kodaktiva = f.isincurrric AND s.datum_naroku = f.datesplatnost AND f.istina < 100
         JOIN equity_data ed ON ed.isincurrric = s.kodaktiva
         JOIN portfolio p ON s.subjektid = p.fondid
WHERE s.uctovnykod = 251110
  AND p.podielnikid IN ($clientID)
UNION ALL
SELECT 50                                           AS poradie,
       d.nazov                                      AS popis,
       sa.mena::varchar                             AS mena1,
       sa.suma::numeric                             AS transsuma,
       sa.datumvyplaty::date                        AS datum,
       (de.cpnaz || '; ISIN ' || de.isinreal)::text AS poznamka,
       0::numeric                                   AS md_d,
       p.cislozmluvy::varchar,
       p.podielnikid::numeric,
       NULL::numeric                                AS idpohybu,
       sa.kodaktiva::text                           AS dealid,
       NULL                                         AS eqid
FROM splatenieakcia sa
         JOIN portfolio p ON sa.subjektid = p.fondid
         JOIN dividendaakciatyp d ON d.subkodobratu = sa.subkodobratu
         JOIN dbequitycurrric drr ON sa.kodaktiva = drr.isincurrric
         JOIN dbequitycurr dr ON drr.isincurr = dr.isincurr
         JOIN dbequity de ON dr.isin = de.isin
WHERE sa.datumvyplaty BETWEEN '$datumodvst' AND '$datumdovst'
  AND d.hotovost = 1
  AND p.podielnikid IN ($clientID)
UNION ALL
SELECT 51                                                                                               AS poradie,
       'Dividenda z fondu Polaris Finance SICAV p.l.c.'                                                 AS popis,
       (CASE
            WHEN fp.mena = 1 THEN 'EUR'
            WHEN fp.mena = 5 THEN 'CZK'
           END)::varchar                                                                                AS mena1,
       (trunc(round((mp.pocet * mp.nav / COALESCE(mp.kurz, 1)), 3), 3))::numeric                        AS transsuma,
       mp.datum::date,
       ('Dividenda fondu ' || fp.nazov || ' - ' || REPLACE(mp.pocet::text, '.', ',') || ' kusov')::text AS poznamka,
       0::numeric                                                                                       AS md_d,
       po.regnumber_polaris::varchar                                                                    AS cislozmluvy,
       po.podielnikid::numeric,
       NULL::numeric                                                                                    AS idpohybu,
       NULL                                                                                             AS dealid,
       NULL                                                                                             AS eqid
FROM majetok_polaris mp
         LEFT JOIN podielnik po ON mp.id_podielnik = po.podielnikid_polaris
         LEFT JOIN fondy_polaris fp ON mp.druh_podielu = fp.druh_podielu
WHERE po.podielnikid = $clientID
  AND mp.typ_pohybu IN (3, 6)
  AND mp.datum <= '$datumdovst'
UNION ALL
SELECT 400                    AS poradie,
       'Ostatné platby'       AS popis,
       ob.mena::varchar       AS mena1,
       ob.suma::numeric       AS transsuma,
       ob.obratdatetime::date AS datum,
       ob.nazpartnera::text   AS poznamka,
       ma.md_d::numeric,
       p.cislozmluvy::varchar,
       p.podielnikid::numeric,
       NULL::numeric          AS idpohybu,
       NULL                   AS dealid,
       NULL                   AS eqid
FROM obratybu ob
         JOIN obratybuobratid obo ON ob.id = obo.id
         JOIN majetokarchiv ma ON ma.obratid = obo.obratid
         JOIN portfolio p ON ma.subjektid = p.fondid
WHERE ma.uctovnykod IN (221110, 325300)
  AND ma.kodobratu IN (219, 226, 228, 266, 287, 288, 610, 620, 621, 622, 623, 624, 625, 626)
  AND ob.logactivityid = 15
  AND ob.obratdatetime BETWEEN '$datumodvst' AND '$datumdovst'
  AND p.podielnikid = $clientID
UNION ALL
SELECT *
FROM poplatok_data
UNION ALL
SELECT *
FROM majetokarchiv_data
UNION ALL
SELECT 200                                         AS poradie,
       'Konverzia - úhrada'                        AS popis,
       k.menadebet::varchar                        AS mena1,
       COALESCE(pd.transsumareal, u.suma)::numeric AS transsuma,
       u.datesplatnost::date                       AS datum,
       ((CASE
             WHEN k.menadebet || k.menakredit = k.menovypar
                 THEN k.menadebet || '/' || k.menakredit
             ELSE k.menakredit || '/' || k.menadebet
           END)
            || ' kurz = '
           || (CASE
                   WHEN k.kurz < 1
                       THEN replace(('0' || k.kurz), '.', ',')
                   ELSE replace(k.kurz::text, '.', ',')
               END))::text
                                                   AS poznamka,
       ma.md_d::numeric,
       p.cislozmluvy::varchar,
       p.podielnikid::numeric,
       NULL::numeric                               AS idpohybu,
       NULL                                        AS dealid,
       NULL                                        AS eqid
FROM uhrada u
         JOIN uhradaobratid ui ON ui.id = u.id
         JOIN majetokarchiv ma ON ma.obratid = ui.obratid
         JOIN filtered_portfolio p ON p.fondid = ma.subjektid
         JOIN konverzia k ON k.dealid::text = u.dealid::text
         LEFT JOIN pool_detail pd ON pd.dealid::text = k.dealid::text
    AND pd.subjektid = ma.subjektid
WHERE u.kodobratu = 334
  AND ma.kodobratu = 334
  AND ma.uctovnykod = 261914
  AND u.logactivityid = 15
  AND u.datesplatnost BETWEEN '$datumodvst' AND '$datumdovst'

UNION ALL

SELECT 90                               AS poradie,
       'Konverzia - došlá platba'       AS popis,
       ki.mena::varchar                 AS mena1,
       ma.pocet::numeric                AS transsuma,
       ma.obratdatatimezauctovane::date AS datum,
       ki.poznamka::text,
       ma.md_d::numeric,
       p.cislozmluvy::varchar,
       p.podielnikid::numeric,
       NULL::numeric                    AS idpohybu,
       NULL                             AS dealid,
       NULL                             AS eqid
FROM obratybu ob
         JOIN obratybuobratid obo ON obo.id = ob.id
         JOIN majetokarchiv ma ON ma.obratid = obo.obratid
         JOIN filtered_portfolio p ON p.fondid = ma.subjektid
         LEFT JOIN konv_incoming ki
                   ON ki.pocet = ma.pocet
                       AND ki.mena = ma.mena
                       AND ki.obratdatatimezauctovane = ma.obratdatatimezauctovane
                       AND ki.subjektid = ma.subjektid
WHERE ma.kodobratu = 237
  AND ma.uctovnykod = 325300
  AND ob.logactivityid = 15
  AND ob.obratdatetime BETWEEN '$datumodvst' AND '$datumdovst'
  AND ob.krdb = 1
UNION ALL
SELECT c.poradie,
       'Prevod '
           || CASE WHEN c.direction = 'na' THEN 'na portfólio č. ' ELSE 'z portfólia č. ' END
           || po2.cislozmluvy                                    AS popis,
       c.mena::varchar                                           AS mena1,
       c.suma::numeric                                           AS transsuma,
       c.datum_zauctovania::date                                 AS datum,
       ''::text                                                  AS poznamka,
       (CASE WHEN c.direction = 'na' THEN 1 ELSE 0 END)::numeric AS md_d,
       c.own_cislozmluvy::varchar                                AS cislozmluvy,
       c.own_podielnikid::numeric                                AS podielnikid,
       NULL::numeric                                             AS idpohybu,
       NULL                                                      AS dealid,
       NULL                                                      AS eqid
FROM combinedPrevody c
         JOIN portfolio po2
              ON po2.fondid = c.target_subjektid
UNION ALL
SELECT 110                             AS poradie,
       'Ostatné platby'                AS popis,
       m.mena::varchar                 AS mena1,
       m.pocet::numeric                AS transsuma,
       m.obratdatatimezauctovane::date AS datum,
       ob.nazpartnera::text            AS poznamka,
       0::numeric                      AS md_d,
       p.cislozmluvy::varchar          AS cislozmluvy,
       p.podielnikid::numeric,
       NULL::numeric                   AS idpohybu,
       NULL                            AS dealid,
       NULL                            AS eqid
FROM majetokarchiv m
         JOIN obratybuobratid obo
              ON obo.obratid = m.obratid
         JOIN obratybu ob
              ON ob.id = obo.id
         JOIN filtered_portfolio p
              ON p.fondid = m.subjektid
         LEFT JOIN konf_obrat ko
                   ON ko.obratid = m.obratid
                       AND ko.subjektid = m.subjektid
         LEFT JOIN reverse_m rm
                   ON rm.obratdatatimezauctovane = m.obratdatatimezauctovane
                       AND rm.subjektid = m.subjektid

WHERE m.obratdatatimezauctovane BETWEEN '$datumodvst' AND '$datumdovst'
  AND m.uctovnykod = 668000
  AND m.md_d = 1
  AND m.kodobratu = 214
  AND ko.obratid IS NULL
  AND rm.subjektid IS NULL
UNION ALL
SELECT CASE k.druhobchodu
           WHEN 'vyber' THEN 421
           WHEN 'vklad' THEN 121
           END                   AS poradie,

       CASE k.druhobchodu
           WHEN 'vyber' THEN 'Výber klienta'
           WHEN 'vklad' THEN 'Vklad klienta'
           END                   AS popis,

       k.mena::varchar           AS mena1,
       k.suma::numeric           AS transsuma,
       k.datum_zauctovania::date AS datum,
       k.externy_ucet::text      AS poznamka,
       (CASE k.druhobchodu
            WHEN 'vyber' THEN 1
            WHEN 'vklad' THEN 0
           END)::numeric         AS md_d,
       p.cislozmluvy::varchar    AS cislozmluvy,
       p.podielnikid::numeric,
       NULL::numeric             AS idpohybu,
       k.dealid::text            AS dealid,
       NULL                      AS eqid

FROM konfirmaciapp k
         JOIN filtered_portfolio p
              ON p.fondid = k.subjektid

WHERE k.logactivityid = 12
  AND k.druhobchodu IN ('vyber', 'vklad')
  AND k.datum_zauctovania BETWEEN '$datumodvst' AND '$datumdovst') tr
    WHERE 1 = 1 
    $typQuery
    $sortingQuery
LIMIT 30 ;";

$transactions = Connection::getDataFromDatabase($query, defaultDB)[1];
<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

$clientID = isset($_GET["id"]) ? intval($_GET["id"]) : $clientID;
$notes = Connection::getDataFromDatabase("SELECT * FROM clientnotes WHERE podielnikid = $clientID", defaultDB)[1];
?>
<section class="flex items-center justify-between">
    <h2 class="font-bold text-2xl">Zoznam poznámok klienta</h2>
    <button id="addNoteButton" type="button" data-modal-target="add-note-modal" data-modal-toggle="add-note-modal"
        class="text-white bg-gradient-to-r cursor-pointer transition-all from-green-400 via-green-500 to-green-600 hover:bg-gradient-to-br 
        focus:ring-4 focus:outline-none focus:ring-green-300 dark:focus:ring-green-800 shadow-lg shadow-green-500/50 dark:shadow-lg 
        dark:shadow-green-800/80 font-bold flex items-center gap-2 rounded-lg text-sm px-5 py-2.5 text-center">Pridať
        poznámku
    </button>
</section>
<section id="notesList" class="grid grid-cols-3 gap-4 mt-4">
    <?php
    if (count($notes) === 0) { ?>
        <div class="p-4 my-4 text-sm text-blue-800 rounded-lg bg-blue-50 dark:bg-gray-900 dark:text-blue-400" role="alert">
            <span class="font-medium">Žiadne poznámky!</span> Pridajte poznámku pre klienta.
        </div>
    <?php } else {
        foreach ($notes as $key => $note) { ?>
            <div class="block max-w-sm relative group p-6 bg-white border border-gray-200 rounded-lg shadow-sm hover:bg-gray-100 dark:bg-yellow-100 dark:border-gray-700 dark:hover:bg-yellow-300
                 transition-all cursor-pointer">
                <section class="flex justify-between items-center mb-3">
                    <h5 class="text-md font-bold tracking-tight text-gray-900 ">Poznámka č. <?php echo $key + 1; ?>
                    </h5>
                    <small class="text-gray-700"><?php echo explode(".", $note["date_created"])[0]; ?></small>
                </section>
                <span hx-post="/api/klienti/detail/deleteNote"
                    hx-vals='{"noteid": "<?php echo $note["id"]; ?>", "clientid": "<?php echo $clientID; ?>"}'
                    hx-target="#notesList" hx-confirm="Naozaj chcete tento záznam odstrániť?"
                    class="absolute -top-3 -right-3 cursor-pointer bg-red-500 hover:bg-red-700 transition-all rounded-full p-1 hidden group-hover:block">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="lucide lucide-trash2-icon lucide-trash-2">
                        <path d="M3 6h18" />
                        <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6" />
                        <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2" />
                        <line x1="10" x2="10" y1="11" y2="17" />
                        <line x1="14" x2="14" y1="11" y2="17" />
                    </svg>
                </span>
                <p class="font-normal text-gray-700 dark:text-gray-800"><?php echo $note["note"]; ?></p>
            </div>
        <?php }
    }
    ?>
</section>
<div id="add-note-modal" tabindex="-1" aria-hidden="true"
    class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
    <div class="relative p-4 w-full max-w-2xl max-h-full">
        <!-- Modal content -->
        <form hx-post="/api/klienti/detail/addNote" hx-target="#notesList"
            class="relative bg-white rounded-lg shadow-sm dark:bg-gray-700">
            <!-- Modal header -->
            <div
                class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600 border-gray-200">
                <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                    Pridať poznámku
                </h3>
                <button type="button"
                    class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
                    data-modal-hide="add-note-modal">
                    <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                        viewBox="0 0 14 14">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                    </svg>
                    <span class="sr-only">Close modal</span>
                </button>
            </div>
            <!-- Modal body -->
            <div class="p-4 md:p-5 space-y-4">
                <input type="hidden" name="clientid" value="<?php echo $clientID; ?>" />
                <input type="hidden" name="userid" value="<?php echo $_SESSION["user"]["data"]["userid"]; ?>" />
                <label for="message"
                    class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Poznámka</label>
                <textarea id="note" name="note" rows="8" required
                    class="block p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                    placeholder="Sem napíšte poznámku..."></textarea>
            </div>
            <!-- Modal footer -->
            <div class="flex items-center p-4 md:p-5 border-t border-gray-200 rounded-b dark:border-gray-600">
                <button data-modal-hide="add-note-modal" type="submit" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center 
                    dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">Uložiť</button>
                <button data-modal-hide="add-note-modal" type="button" class="py-2.5 px-5 ms-3 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 
                    focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white 
                    dark:hover:bg-gray-700">Zrušiť</button>
            </div>
        </form>
    </div>
</div>
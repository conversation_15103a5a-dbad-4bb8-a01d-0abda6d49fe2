<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
require_once "/home/<USER>/www/src/lib/notifications/Notification.class.php";

$clientID = $_POST["clientid"];
$note = $_POST["note"];
$userid = $_POST["userid"];
$username = $_SESSION["user"]["data"]["username"];

$insertIntoNotes = Connection::InsertUpdateCreateDelete("INSERT INTO clientnotes (podielnikid, userid, note, date_created) VALUES (?, ?, ?, CURRENT_TIMESTAMP)", [$clientID, $userid, $note], defaultDB);
if (gettype($insertIntoNotes) !== "integer") {
    echo "error" . $insertIntoNotes;
} else {
    $notification = new Notification(
        0,
        'clientnotes',
        $clientID,
        "addNote",
        $userid,
        $username,
        "addNote",
        json_encode(["podielnikid", $clientID]),
        false,
        $clientID
    );
    $notification->createNotifcation();
    $notes = Connection::getDataFromDatabase("SELECT * FROM clientnotes WHERE podielnikid = $clientID", defaultDB)[1];
    if (count($notes) === 0) { ?>
        <div class="p-4 my-4 text-sm text-blue-800 rounded-lg bg-blue-50 dark:bg-gray-900 dark:text-blue-400" role="alert">
            <span class="font-medium">Žiadne poznámky!</span> Pridajte poznámku pre klienta.
        </div>
    <?php } else {

        foreach ($notes as $key => $note) { ?>
            <div class="block max-w-sm relative group p-6 bg-white border border-gray-200 rounded-lg shadow-sm hover:bg-gray-100 dark:bg-yellow-100 dark:border-gray-700 dark:hover:bg-yellow-300
                 transition-all cursor-pointer">
                <section class="flex justify-between items-center mb-3">
                    <h5 class="text-md font-bold tracking-tight text-gray-900 ">Poznámka č. <?php echo $key + 1; ?>
                    </h5>
                    <small class="text-gray-700"><?php echo explode(".", $note["date_created"])[0]; ?></small>
                </section>
                <span hx-post="/api/klienti/detail/deleteNote"
                    hx-vals='{"noteid": "<?php echo $note["id"]; ?>", "clientid": "<?php echo $clientID; ?>"}'
                    hx-target="#notesList" hx-confirm="Naozaj chcete tento záznam odstrániť?"
                    class="absolute -top-3 -right-3 cursor-pointer bg-red-500 hover:bg-red-700 transition-all rounded-full p-1 hidden group-hover:block">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="lucide lucide-trash2-icon lucide-trash-2">
                        <path d="M3 6h18" />
                        <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6" />
                        <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2" />
                        <line x1="10" x2="10" y1="11" y2="17" />
                        <line x1="14" x2="14" y1="11" y2="17" />
                    </svg>
                </span>
                <p class="font-normal text-gray-700 dark:text-gray-800"><?php echo $note["note"]; ?></p>
            </div>
        <?php }
    }
}
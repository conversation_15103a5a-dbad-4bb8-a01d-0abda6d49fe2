<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

$clientID = $_POST["id"];
$activityType = $_POST["activityType"];
$timeframe = $_POST["timeframe"];
$offset = $_POST["offset"];

if ($activityType == "all") {
    $typeQuery = "";
} else {
    $typeQuery = " AND notification = '$activityType'";
}

if ($timeframe == "all") {
    $timeQuery = "";
} else {
    $timeQuery = " AND datetimeactivity >= NOW() - INTERVAL '$timeframe days'";
}

$activityCount = Connection::getDataFromDatabase("SELECT COUNT(*) as count FROM notifications WHERE objektid = $clientID $typeQuery $timeQuery", defaultDB)[1][0]["count"];
$activity = Connection::getDataFromDatabase("SELECT n.*, u.username FROM notifications n JOIN users u ON n.userid = u.userid WHERE objektid = $clientID $typeQuery $timeQuery LIMIT 30", defaultDB)[1];

$popisy = [
    "addNote" => [
        "badge" => "Pridanie poznámky",
        "text" => "pridal poznámku"
    ],
    "deleteNote" => [
        "badge" => "Odstránenie poznámky",
        "text" => "odstránil poznámku"
    ]
];
foreach ($activity as $key => $item) {
    $notificationID = $item["id"];
    $notifDetail = Connection::getDataFromDatabase("SELECT * FROM notifications WHERE id = $notificationID", defaultDB)[1][0]["objektdetail"];
    $notifDetail = json_decode($notifDetail);
    $column = $notifDetail[0];
    $data = "'$notifDetail[1]'";
    $destinacia = $item["destinacia"];
    //echo "SELECT * FROM $destinacia WHERE $column = $data";
    $detail = Connection::getDataFromDatabase("SELECT * FROM $destinacia WHERE $column = $data", defaultDB)[1];
    ?>
    <div class="relative  pl-8 pb-8">
        <div class="absolute left-3 top-1.5 -bottom-0 w-px dark:bg-gray-800 bg-slate-200"></div>
        <div
            class="absolute left-0 top-1.5 w-6 h-6 rounded-full bg-green-100 border-4 border-white flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="w-3 h-3 text-green-600">
                <path
                    d="M10.75 10.818v2.614A3.13 3.13 0 0011.888 13c.482-.315.612-.648.612-.875 0-.227-.13-.56-.612-.875a3.13 3.13 0 00-1.138-.432zM8.33 8.62c.053.055.115.11.184.164.208.16.46.284.736.363V6.603a2.45 2.45 0 00-.35.13c-.14.065-.27.143-.386.233-.377.292-.514.627-.514.909 0 .184.058.39.202.592.037.051.08.102.128.152z" />
                <path fill-rule="evenodd"
                    d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-6a.75.75 0 01.75.75v.316a3.78 3.78 0 011.653.713c.426.33.744.74.925 1.2a.75.75 0 01-1.395.55 1.35 1.35 0 00-.447-.563 2.187 2.187 0 00-.736-.363V9.3c.698.093 1.383.32 1.959.696.787.514 1.29 1.27 1.29 2.13 0 .86-.504 1.616-1.29 2.13-.576.377-1.261.603-1.96.696v.299a.75.75 0 11-1.5 0v-.3c-.697-.092-1.382-.318-1.958-.695-.482-.315-.857-.717-1.078-1.188a.75.75 0 111.359-.636c.08.173.245.376.54.569.313.205.706.353 1.138.432v-2.748a3.782 3.782 0 01-1.653-.713C6.9 9.433 6.5 8.681 6.5 7.875c0-.805.4-1.558 1.097-2.096a3.78 3.78 0 011.653-.713V4.75A.75.75 0 0110 4z"
                    clip-rule="evenodd" />
            </svg>
        </div>

        <!-- Activity Content -->
        <div class="bg-white dark:bg-gray-700 rounded-lg border border-slate-200 p-4 hover:shadow-sm transition-shadow">
            <div class="flex flex-col sm:flex-row sm:items-center justify-between gap-2 mb-2">
                <div class="flex items-center gap-2">
                    <span
                        class="<?php echo str_contains($item["action"], "add") ? "bg-green-200 text-green-800" : "bg-red-200 text-red-800"; ?>
                                         text-xs font-medium px-2.5 py-0.5 rounded-full"><?php echo $popisy[$item["action"]]["badge"]; ?></span>
                    <span
                        class="text-slate-400 dark:text-slate-100 text-sm"><?php echo explode("+", $item["datetimeactivity"])[0]; ?></span>
                </div>
                <span class="text-slate-500 dark:text-slate-200 text-sm"><?php echo $item["username"]; ?></span>
            </div>

            <h4 class="font-medium text-slate-800 dark:text-gray-100">
                <?php echo $item["username"] . " " . $popisy[$item["action"]]["text"]; ?>
            </h4>
            <p class="text-slate-600 dark:bg-gray-500 p-2 rounded-lg dark:text-gray-200 mt-1">
                <?php echo $detail[0]["note"]; ?>
            </p>
        </div>
    </div>
<?php } ?>
<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

$clientID = isset($matches[1]) ? intval($matches[1]) : $_GET["id"];
$clientDetail = Connection::getDataFromDatabase("SELECT * FROM podielnik WHERE podielnikid = $clientID", defaultDB)[1][0];
$clientName = $clientDetail["meno"];
$clientPrieznaz = $clientDetail["prieznaz"];
$fondids = Connection::getDataFromDatabase("SELECT fondid FROM portfolio WHERE podielnikid = $clientID", defaultDB)[1];
$fondidsText = implode(', ', array_column($fondids, 'fondid'));

$PDataQuery = "WITH portfolioData AS (SELECT po.fondid, po.cislozmluvy
                       FROM podielnik p
                                JOIN portfolio po ON po.podielnikid = p.podielnikid
                       WHERE p.archiv = 'f'
                         AND po.podielnikid = $clientID),
     holdingData AS (SELECT subjektid AS fondid, SUM(pocet) AS stav
                     FROM majetokarchiv
                     WHERE subjektid IN ($fondidsText)
                     GROUP BY subjektid),
     gainLoss AS (SELECT pd.fondid,
                         pd.cislozmluvy,
                         h.stav
                  FROM portfolioData pd
                           JOIN holdingData h ON h.fondid = pd.fondid)
SELECT *
FROM gainLoss
ORDER BY fondid;";

$portfolioTableData = Connection::getDataFromDatabase($PDataQuery, defaultDB)[1];
$jozoquery = "WITH detailyFondu AS (SELECT CASE WHEN dlhopisy = 1 THEN 'Áno' ELSE 'Nie' END AS dlhopisy,
                             CASE WHEN akcie = 1 THEN 'Áno' ELSE 'Nie' END    AS akcie,
                             refmena,
                             fondid
                      FROM fonds),
     portfolioData AS (SELECT po.fondid,
                              po.cislozmluvy,
                              df.dlhopisy,
                              df.akcie,
                              df.refmena
                       FROM podielnik p
                                JOIN portfolio po ON po.podielnikid = p.podielnikid
                                JOIN detailyFondu df ON df.fondid = po.fondid
                       WHERE p.archiv = 'f'
                         AND po.podielnikid = $clientID),
     navWithPrices AS (SELECT latest.fondid,
                              latest.datum   AS max_datum,
                              latest.nav     AS nav_latest,
                              earliest.datum AS min_datum,
                              earliest.nav   AS nav_initial
                       FROM
                           (SELECT DISTINCT ON (fondid) fondid,
                                                        datum,
                                                        nav
                            FROM pricestore
                            WHERE nav IS NOT NULL
                            ORDER BY fondid, datum DESC) latest
                               JOIN
                               (SELECT DISTINCT ON (fondid) fondid,
                                                            datum,
                                                            nav
                                FROM pricestore
                                WHERE nav > 0
                                ORDER BY fondid, datum ASC) earliest
                           ON latest.fondid = earliest.fondid),
     holdingData AS (SELECT subjektid AS fondid, SUM(pocet) AS stav
                     FROM majetokarchiv
                     GROUP BY subjektid),
     gainLoss AS (SELECT pd.fondid,
                         pd.cislozmluvy,
                         pd.dlhopisy,
                         pd.akcie,
                         TO_CHAR(nw.max_datum, 'DD.MM.YYYY')                        AS aktualny_datum,
                         TO_CHAR(nw.min_datum, 'DD.MM.YYYY')                        AS pociatocny_datum,
                         COALESCE(nw.nav_initial, 0)                                as initial_price,
                         nw.nav_latest,
                         h.stav,
                         ROUND(((nav_latest - nav_initial) * h.stav)::numeric, 2)   AS gain_loss,
                         ROUND((((nav_latest - nav_initial) / nav_initial) * 100)::numeric, 2) AS percent_gain
                  FROM portfolioData pd
                           JOIN navWithPrices nw ON nw.fondid = pd.fondid
                           JOIN holdingData h ON h.fondid = pd.fondid)
SELECT *
FROM gainLoss
ORDER BY fondid;
";
?>
<h2 class="text-3xl font-bold">Zoznam portfólií</h2>
<div class="mt-5">
    <div class="grid <?php count($portfolioTableData) <= 2 ? "grid-cols-1" : "grid-cols-2" ?> gap-6">
        <?php foreach ($portfolioTableData as $portfolio) {
            $id_fond = $portfolio["fondid"];
            $cislozmluvy = $portfolio["cislozmluvy"];
            require "src/Views/klient/detailPortfolia.php";
        } ?>
    </div>
</div>
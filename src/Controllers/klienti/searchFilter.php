<?php

require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

$query = $_POST['search'];

function buildFilterQuerys($query): string
{
    if ($query !== "") {
        $queryBase = "SELECT p.*, po.* FROM podielnik p LEFT JOIN portfolio po ON po.podielnikid = p.podielnikid WHERE (SIMILARITY(p.meno,'%$query%') > 0.3 OR SIMILARITY(p.prieznaz,'%$query%') > 0.3) OR po.cislozmluvy LIKE '%$query%'";
        $queryBase .= " AND archiv = 'f'";
    } else {
        $queryBase = "SELECT * FROM podielnik";
    }
    $queryBase .= " ORDER BY prieznaz ASC";
    return $queryBase;
}

$rows = Connection::getDataFromDatabase(buildFilterQuerys($query), defaultDB);

$podielnici = [];
foreach ($rows[1] as $key => $value) {
    if ($podielnici[$value["podielnikid"]]["podielnikid"] == $value["podielnikid"]) {
        $podielnici[$value["podielnikid"]]["cislozmluvy"][] = $value["cislozmluvy"];
    } else {
        $podielnici[$value["podielnikid"]] = [
            "podielnikid" => $value["podielnikid"],
            "meno" => $value["meno"],
            "prieznaz" => $value["prieznaz"],
            "cislozmluvy" => [$value["cislozmluvy"]],
            "titulpred" => $value["titulpred"],
            "titulza" => $value["titulza"],
            "kontaktcity" => $value["kontaktcity"],
            "city" => $value["city"],
            "address" => $value["address"]
        ];
    }
}
if ($podielnici[0] === 0) {
    echo "neni";
} else {
    foreach ($podielnici as $podienik) {
        ?>
        <tr class="border-b dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-600">
            <td class="px-4 py-2">
                <span
                    class="text-sm text-gray-900 dark:text-gray-200 font-bold px-2 py-0.5 rounded"><?php echo $podienik['titulpred'] . " " . $podienik["meno"] . " " . $podienik["prieznaz"] . " " . $podienik["titulza"] ?></span>
            </td>
            <td class="px-4 py-2 flex flex-wrap items-center gap-2">
                <?php
                if (count($podienik['cislozmluvy']) > 0) {
                    foreach ($podienik['cislozmluvy'] as $cislozmluvy) {
                        if ($cislozmluvy == "")
                            continue;
                        ?>
                        <span hx-get="/portfolia/detail/<?php echo $cislozmluvy ?>" hx-target="#pageContentMain" hx-replace-url="true"
                            hx-push-url="true" preload="always mouseover" hx-trigger="click" hx-boost="true" class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-sm hover:dark:bg-blue-700 transition-all cursor-pointer
                                                 dark:bg-blue-900 dark:text-blue-300"><?php echo $cislozmluvy ?></span>
                    <?php }
                } ?>
            </td>
            <td class="px-4 py-2 font-medium text-gray-900 whitespace-nowrap dark:text-white"><?php
            if ($podienik["kontaktcity"] !== null) {
                echo $podienik["kontaktcity"];
            } else {
                echo $podienik["city"];
            }
            ?></td>
            <td class="px-4 py-2 font-medium text-gray-900 whitespace-nowrap dark:text-white"><?php
            if ($podienik["kontaktaddress"] !== null) {
                echo $podienik["kontaktaddress"];
            } else {
                echo $podienik["address"];
            }
            ?></td>
            <td
                class="px-4 py-2 hidden archivedCol hidden font-medium text-success-900 gap-2 flex items-center justify-center whitespace-nowrap dark:text-white">
                <?php
                if ($podienik["archiv"] === "t") {
                    ?>
                    <svg class="w-5 h-5 text-green-600" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                        fill="currentColor" viewBox="0 0 24 24">
                        <path fill-rule="evenodd"
                            d="M20 10H4v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8ZM9 13v-1h6v1a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1Z"
                            clip-rule="evenodd" />
                        <path d="M2 6a2 2 0 0 1 2-2h16a2 2 0 1 1 0 4H4a2 2 0 0 1-2-2Z" />
                    </svg> <span>Archivovaný</span>
                    <?php
                }
                ?>
            </td>
            <td class="px-4 py-2 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                <div class="flex items-center gap-4">
                    <a href="/klienti/detail/<?php echo $podienik["podielnikid"]; ?>">
                        <button data-tooltip-target="view-tooltip"
                            class="p-2 cursor-pointer hover:bg-gray-300 transition-all rounded-lg">
                            <svg class="w-5 h-5 text-gray-800 dark:text-white" aria-hidden="true"
                                xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                                <path stroke="currentColor" stroke-width="2"
                                    d="M21 12c0 1.2-4.03 6-9 6s-9-4.8-9-6c0-1.2 4.03-6 9-6s9 4.8 9 6Z" />
                                <path stroke="currentColor" stroke-width="2" d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" />
                            </svg>
                        </button>
                    </a>
                    <div id="view-tooltip" role="tooltip"
                        class="absolute z-10 invisible inline-block px-3 py-2 text-sm font-medium text-white transition-opacity duration-300 bg-gray-900 rounded-lg shadow-sm opacity-0 tooltip dark:bg-gray-700">
                        Zobraziť
                        <div class="tooltip-arrow" data-popper-arrow></div>
                    </div>
                    <a href="/klienti/edit/<?php echo $podienik["podielnikid"]; ?>">
                        <button data-tooltip-target="edit-tooltip"
                            class="p-2 cursor-pointer hover:bg-gray-300 transition-all rounded-lg">
                            <svg class="w-5 h-5 text-gray-800 dark:text-white" aria-hidden="true"
                                xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor"
                                viewBox="0 0 24 24">
                                <path fill-rule="evenodd"
                                    d="M5 8a4 4 0 1 1 7.796 1.263l-2.533 2.534A4 4 0 0 1 5 8Zm4.06 5H7a4 4 0 0 0-4 4v1a2 2 0 0 0 2 2h2.172a2.999 2.999 0 0 1-.114-1.588l.674-3.372a3 3 0 0 1 .82-1.533L9.06 13Zm9.032-5a2.907 2.907 0 0 0-2.056.852L9.967 14.92a1 1 0 0 0-.273.51l-.675 3.373a1 1 0 0 0 1.177 1.177l3.372-.675a1 1 0 0 0 .511-.273l6.07-6.07a2.91 2.91 0 0 0-.944-4.742A2.907 2.907 0 0 0 18.092 8Z"
                                    clip-rule="evenodd" />
                            </svg>
                        </button>
                    </a>
                    <div id="edit-tooltip" role="tooltip"
                        class="absolute z-10 invisible inline-block px-3 py-2 text-sm font-medium text-white transition-opacity duration-300 bg-gray-900 rounded-lg shadow-sm opacity-0 tooltip dark:bg-gray-700">
                        Upraviť
                        <div class="tooltip-arrow" data-popper-arrow></div>
                    </div>
                    <button data-tooltip-target="archive-tooltip"
                        class="p-2 cursor-pointer hover:bg-gray-300 transition-all rounded-lg">
                        <svg class="w-5 h-5 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                            width="24" height="24" fill="none" viewBox="0 0 24 24">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 11v5m0 0 2-2m-2 2-2-2M3 6v1a1 1 0 0 0 1 1h16a1 1 0 0 0 1-1V6a1 1 0 0 0-1-1H4a1 1 0 0 0-1 1Zm2 2v10a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V8H5Z" />
                        </svg>
                    </button>
                    <div id="archive-tooltip" role="tooltip"
                        class="absolute z-40 invisible inline-block px-3 py-2 text-sm font-medium text-white transition-opacity duration-300 bg-gray-900 rounded-lg shadow-sm opacity-0 tooltip dark:bg-gray-700">
                        Archivovať
                        <div class="tooltip-arrow" data-popper-arrow></div>
                    </div>
                </div>
            </td>
        </tr>
        <?php
    }

}
<?php
require "/home/<USER>/www/src/lib/connection.php";
require "/home/<USER>/www/conf/settings.php";


$fondid = $_POST["fondid"];
$templateRes = Connection::getDataFromDatabase("SELECT * FROM fonds WHERE fondid = $fondid", defaultDB);
$template = $templateRes[1][0];

$uctyRes = Connection::getDataFromDatabase("SELECT * FROM fondsbu WHERE fondid = $fondid", defaultDB);
$ucty = $uctyRes[1];

$muctyRes = Connection::getDataFromDatabase("SELECT * FROM fondsmu WHERE fondid = $fondid", defaultDB);
$mucty = $muctyRes[1];

header("Content-type: application/json");
echo json_encode(["value" => $template, "mucty" => $mucty, "ucty" => $ucty]);

<?php
require "/home/<USER>/www/src/lib/connection.php";
require "/home/<USER>/www/conf/settings.php";

$rows = Connection::getDataFromDatabase("SELECT * FROM investor_test_answers", onboardingDB, "3312");
$empty_table = Connection::InsertUpdateCreateDelete("TRUNCATE TABLE investicny_dotaznik", defaultDB);


foreach ($rows[1] as $key => $value) {
  $params = [$value["id"], $value["investor_test_id"], $value["user_id"], $value["data"], $value["created_at"], $value["updated_at"]];
  $query = "INSERT INTO investicny_dotaznik (id, investor_test_id, user_id, data, created_at, updated_at) VALUES(?, ?, ?, ?, ?, ?)";
  $insert = Connection::InsertUpdateCreateDelete($query, defaultDB);
  echo $insert;
  echo "<br>";
}

<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

$datum_narodenia = "NULL";
$refundaciedane = $_POST['refundaciadane'] === "1" ? "true" : "false";
$is_pep = $_POST['is_pep'] === "0" ? "true" : "false";

if ($_POST["datum_narodenia"] != "") {
    $datum_narodenia = $_POST["datum_narodenia"];
}

$errors = [];
header('Content-Type: application/json');

if ($_POST["prieznaz"] == "" || $_POST["fpo"] == "" || $_POST['rcico'] == "" || $_POST["datetimeagreement"] == "" || $_POST["userid"] == "" || $_POST["uroven_aml_rizika"] == "")
    $errors["cannotBeEmpty"] = "Toto pole nesmie byť prázdne";

if ($errors != "") {
  $clientParams = [$_POST["prieznaz"], $_POST["meno"], $_POST["titulpred"], $_POST["titulza"], $_POST["typklienta"], intval($_POST["fpo"]), intval($_POST["druhid"]), $_POST["bu"], $_POST["pohlavie"], $_POST["rcico"], $datum_narodenia, $_POST["cisloid"], intval($_POST["stateid"]), $_POST["sektor_esa95"], $_POST["address"], $_POST["city"], $_POST["postalcode"], $_POST["kontaktphonenumber"], $_POST["kontaktmobilnumber"], $_POST["kontaktemail"], intval($_POST["is_pep"]), $_POST["uroven_aml_rizika"], intval($refundaciedane), intval($_POST["vypisfrekvencia"]), intval($_POST["gfi"]), intval($_POST["dan_domicil"]), $_POST["typ_zdanenia"], intval($_POST["userid"]), $_POST["datetimeagreement"], $_POST["podielnikid"]];
    $picha = Connection::InsertUpdateCreateDelete("UPDATE podielnik SET prieznaz = ?, meno = ?, titulpred = ?, titulza = ?, typklienta = ?, fpo = ?, druhid = ?, bu = ?, pohlavie = ?, rcico = ?, datum_narodenia = ?, cisloid = ?, stateid = ?, sektor_esa95 = ?, address = ?, city = ?, postalcode = ?, kontaktphonenumber = ?, kontaktmobilnumber = ?, kontaktemail = ?, is_pep = ?, uroven_aml_rizika = ?, refundaciadane = ?, vypisfrekvencia = ?, gfi = ?, dan_domicil = ?, typ_zdanenia = ?, userid = ?, datetimeagreement = ? WHERE podielnikid = ?", $clientParams, defaultDB);
    echo json_encode($clientParams[20]);
} else {
    echo json_encode($errors);
}




document.addEventListener('htmx:afterSettle', function (evt) {
    document.getElementById("updateChecks").style.display = "block";
    document.getElementById("updateSpinner").style.display = "none";
    document.getElementById("toast").style.display = "flex";
    $(document.getElementById("toast")).animate({ opacity: 1 }, 500);
    setTimeout(() => {
        $(document.getElementById("toast")).animate({ opacity: 0 }, 500);
    }, 3000);
});

$("#dobavysporm").on("keyup", (e) => {
    const days = parseInt(e.target.value);
    const maxToday = document.getElementById("maxToday").value;
    if (Number.isNaN(days) || days < 0) {
        $("#datumvysporm").removeClass("bg-blue-800");
        document.getElementById("datumvysporm").value = "";
        document.getElementById("datumvysporm").style.display = "none";
        e.target.value = "";
    } else {
        const today = new Date(maxToday);
        const futureDate = new Date(today);
        futureDate.setDate(today.getDate() + days);
        document.getElementById("datumvysporm").style.display = "inline-flex";
        $("#datumvysporm").addClass("bg-blue-800");
        document.getElementById("datumvysporm").value = futureDate.toISOString().split('T')[0];
    }
});

$("#datumvysporm").on("change", (e) => {
    const date = e.target.value;
    const maxToday = document.getElementById("maxToday").value;
    const oneDay = 24 * 60 * 60 * 1000; // hours*minutes*seconds*milliseconds
    const firstDate = new Date(maxToday);
    const secondDate = new Date(date);

    const diffDays = Math.round(Math.abs((firstDate - secondDate) / oneDay));
    document.getElementById("dobavysporm").value = diffDays;
});

$("#dobavysporf").on("keyup", (e) => {
    const days = parseInt(e.target.value);
    const maxToday = document.getElementById("maxToday").value;
    if (Number.isNaN(days) || days < 0) {
        $("#datumvysporf").removeClass("bg-blue-800");
        document.getElementById("datumvysporf").value = "";
        document.getElementById("datumvysporf").style.display = "none";
        e.target.value = "";
    } else {
        const today = new Date(maxToday);
        const futureDate = new Date(today);
        futureDate.setDate(today.getDate() + days);
        document.getElementById("datumvysporf").style.display = "inline-flex";
        $("#datumvysporf").addClass("bg-blue-800");
        document.getElementById("datumvysporf").value = futureDate.toISOString().split('T')[0];
    }
});

$("#datumvysporf").on("change", (e) => {
    const date = e.target.value;
    const maxToday = document.getElementById("maxToday").value;
    const oneDay = 24 * 60 * 60 * 1000; // hours*minutes*seconds*milliseconds
    const firstDate = new Date(maxToday);
    const secondDate = new Date(date);

    const diffDays = Math.round(Math.abs((firstDate - secondDate) / oneDay));
    document.getElementById("dobavysporf").value = diffDays;
});

$("#selectHalf").on("click", (e) => {
    console.log(e.target.value);
    document.getElementById("pocetkusov").value = e.target.value;
});

$("#selectAll").on("click", (e) => {
    console.log(e.target.value);
    document.getElementById("pocetkusov").value = e.target.value;
});

$("#ric").on("change", (e) => {
    const values = JSON.parse(e.target.value.replace(/'/g, '"'));
    document.getElementById("nazovpartneraValue").innerHTML = values[1];
    document.getElementById("nazovpartnera").value = values[1];
    document.getElementById("partnerid").value = values[0];
});

$("#limitnykurz").on("change", (e) => {
    enablePooling();
});

$("#aktualnykurz").on("change", (e) => {
    enablePooling();
});

document.getElementById("poolDetailForm").addEventListener("submit", (e) => {
    e.preventDefault();
    let kurzInput;
    let kurz;
    if (document.getElementById("limitnykurz")) {
        kurz = document.getElementById("limitnykurz").value;
        kurzInput = document.getElementById("limitnykurz");
    } else {
        kurz = document.getElementById("aktualnykurz").value;
        kurzInput = document.getElementById("aktualnykurz");
    }
    let pocet = e.target.poolSumaAll.value;
    let poolid = e.target.poolidModal.value;
    let poolData = e.target.poolDetailData.value;
    let eqid = e.target.eqid.value;
    document.getElementById("poolid").value = poolid;
    document.getElementById("pocetkusov").value = pocet;
    let price;
    if (document.getElementById("limitprice")) {
        if (eqid === "Bonds") {
            let nominalko = parseInt(document.getElementById("nominal").value);
            let faktor = parseInt(document.getElementById("faktor").value);
            console.log(kurz / 100 * nominalko * faktor * pocet);
            document.getElementById("limitprice").value = (kurz / 100 * nominalko * faktor * pocet).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }).replace(',', ' ');
        } else {
            document.getElementById("limitprice").value = (pocet * kurz).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }).replace(',', ' ');
        }
        document.getElementById("limitprice").readOnly = true;
        document.getElementById("limitprice").style.background = "#E4E4E4";
        price = document.getElementById("limitprice").value;
    } else {
        document.getElementById("investedprice").value = pocet * kurz;
        document.getElementById("investedprice").readOnly = true;
        document.getElementById("investedprice").style.background = "#E4E4E4";
        price = document.getElementById("investedprice").value;
    }

    document.getElementById("pocetkusov").readOnly = true;
    document.getElementById("pocetkusov").style.background = "#E4E4E4";
    document.getElementById("poolData").value = poolData;

    $("#pocetkusov").prop('readonly', "readonly");
    $("#datumvysporm").prop('readonly', "readonly");
    $("#datumvysporm, #dobavysporm, #datumvysporf, #dobavysporf, #pocetkusov, #limitnykurz, #percentopoplatku").removeClass("bg-gray-50");
    $("#datumvysporm, #dobavysporm, #datumvysporf, #dobavysporf, #pocetkusov, #limitnykurz, #percentopoplatku").addClass("bg-gray-300");
    $("#datumvysporf").prop('readonly', "readonly");
    $("#selectHalf, #selectAll").remove();
    $("#percentopoplatku").prop('readonly', "readonly");
    if (document.getElementById("percentopoplatku").value === "") {
        document.getElementById("percentopoplatku").value = 0;
    }
    $(kurzInput).prop('readonly', "readonly");
    if (kurzInput.value === "") {
        kurzInput.value = 0;
    }
});

function generatePool() {
    const dmv = document.getElementById("datumvysporm").value;
    const dfv = document.getElementById("datumvysporf").value;
    const generated = $("#generated").val();
    const typ = $("#eqid").val();
    let price;
    if (document.getElementById("limitprice")) {
        price = document.getElementById("limitprice").value;
    } else {
        price = document.getElementById("investedprice").value;
    }
    if (dmv !== "" && dfv !== "" && generated !== "1") {
        htmx.ajax('POST', `/api/investicne-zamery/generatePool`,
            {
                target: '#modalWrapperko',
                values: {
                    "mena": document.getElementById("mena").value,
                    "typ": typ,
                    "cub": document.getElementById("spravbu").value,
                    "cum": document.getElementById("cum").value,
                    "limitprice": price,
                    "isin": document.getElementById("isin").value,
                    "action": document.getElementById("action").value
                }
            }).then(() => {

            });
        $("#generated").val(1);
    }
}

$("#poolingBtn").on("click", (e) => {
    generatePool();
});

function enablePooling() {
    if (document.getElementById("poolingBtn")) {
        $("#poolingBtn").css("display", "inline-flex");
        generatePool();
    } else {
        $("#poolingBtn").hide();
    }
}

document.getElementById("createZamer").addEventListener("submit", (e) => {
    document.getElementById("updateChecks").style.display = "none";
    document.getElementById("updateSpinner").style.display = "block";
});
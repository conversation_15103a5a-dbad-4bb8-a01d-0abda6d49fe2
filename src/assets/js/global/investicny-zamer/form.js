htmx.onLoad(function (elt) {
    const div2 = document.getElementById('step2Slide');
    document.querySelectorAll('.ucetButton').forEach((item) => {
        item.addEventListener('click', function (e) {
            console.log(e.currentTarget.id);
        });
    });

    document.getElementById("uctovyFormular")?.addEventListener("submit", () => {
        const div = document.getElementById('step3Slide');
        document.getElementById('step2Slide').classList.remove('moveIn');
        document.getElementById('step2Slide').classList.add('moveOut');
        document.getElementById('step2Slide').style.display = "none";
        div.classList.add('moveIn');
        div.style.display = "block";
        document.getElementById("step2").classList.remove("text-white");
        document.getElementById("step2").classList.remove("bg-purple-400");
        document.getElementById("step2").classList.remove("shadow-md");
        document.getElementById("step2").classList.add("bg-green-400");
        document.getElementById("step3nav").classList.add("text-white");
        document.getElementById("step3nav").classList.add("bg-purple-400");
        document.getElementById("step3nav").classList.add("shadow-md");
        document.getElementById("mainElementForm").classList.remove("overflow-hidden");
    });

    const allAccFromInputs = document.querySelectorAll(".accFromInput");
    const allAccToInputs = document.querySelectorAll(".accToInput");
    const allAccFromForms = document.querySelectorAll(".accFromForm");
    const allAccToForms = document.querySelectorAll(".accToForm");

    function addListnerToInput(item, form) {
        item.addEventListener("change", (e) => {
            e.target.value = e.target.value.replace(',', '.');
            if (e.target.value === "" || e.target.value < 0) {
                e.target.value = 0;
            }
            form.querySelector("button").click();
        });
        item.addEventListener("focus", (e) => {
            e.target.select();
        });
    }

    function handlePPForms(item) {
        item.addEventListener("submit", (e) => {
            e.preventDefault();
            const formData = new FormData(e.currentTarget);
            const suma = formData.get("suma");
            const maxSuma = formData.get("maxPossible");
            const subjektid = formData.get("subjektid");
            const poolDetailData = document.getElementById("poolDetailData").value;
            if (parseInt(maxSuma) < parseInt(suma)) {
                e.target.suma.value = maxSuma
            } else {
                e.target.suma.value = suma;
            }
            let poolDetail = [];

            if (poolDetailData === "" || poolDetailData === undefined) {
                poolDetail.push({
                    subjektid: subjektid,
                    suma: suma,
                });
                document.getElementById("poolDetailData").value = JSON.stringify(poolDetail);
            } else {
                const arrayDetail = JSON.parse(poolDetailData);
                arrayDetail.push({
                    subjektid: subjektid,
                    suma: suma,
                });
                document.getElementById("poolDetailData").value = JSON.stringify(arrayDetail);
            }

            if (suma === null) {
                return;
            }
            console.log(item.querySelector("input"));
            $(item.querySelector("input")).trigger("focusout");
            let sumSell = 0;
            let sumBuy = 0;
            allAccFromInputs.forEach((item) => {
                sumSell = sumSell + parseFloat(item.value);
            });
            allAccToInputs.forEach((item) => {
                sumBuy = sumBuy + parseFloat(item.value);
            });
            document.getElementById("poolSumaFrom").value = sumSell;
            document.getElementById("fromPrice").innerHTML = sumSell.toLocaleString('de-DE', { minimumFractionDigits: 2, maximumFractionDigits: 2 }).replace(/\./g, ' ');
            document.getElementById("poolSumaTo").value = sumBuy;
            document.getElementById("toPrice").innerHTML = sumBuy.toLocaleString('de-DE', { minimumFractionDigits: 2, maximumFractionDigits: 2 }).replace(/\./g, ' ');
        });
    }

    allAccFromInputs.forEach((item, key) => {
        addListnerToInput(item, allAccFromForms[key]);
    });

    allAccToInputs.forEach((item, key) => {
        addListnerToInput(item, allAccToForms[key]);
    });

    allAccFromForms.forEach((item) => {
        handlePPForms(item);
    });

    allAccToForms.forEach((item) => {
        handlePPForms(item);
    });

    const allSumsInputs = document.querySelectorAll(".poolEditInput");
    const allSumsForms = document.querySelectorAll(".inputGroupForm");

    allSumsInputs.forEach((item, key) => {
        item.addEventListener("change", (e) => {
            e.target.value = e.target.value.replace(',', '.');
            if (e.target.value === "" || e.target.value < 0) {
                e.target.value = 0;
            }
            allSumsForms[key].querySelector("button").click();
            console.log(e.target.value);
        });
        item.addEventListener("focus", (e) => {
            e.target.select();
        });
    })

    sum = 0;

    function recalculate() {
        sum = 0;
        allSumsInputs.forEach((item) => {
            sum = sum + parseFloat(item.value);
        });
        document.getElementById("poolSumaAll").value = sum;
        document.getElementById("poolingSumAll").innerHTML = sum.toLocaleString('de-DE', { minimumFractionDigits: 2, maximumFractionDigits: 2 }).replace(/\./g, ' ')
    }

    allSumsForms.forEach((item) => {
        item.addEventListener("submit", (e) => {
            e.preventDefault();
            const formData = new FormData(e.currentTarget);
            console.log(formData);
            const suma = formData.get("suma");
            const mena = formData.get("mena");
            const maxSuma = formData.get("maxPossible");
            const subjektid = formData.get("subjektid");
            const poolDetailData = document.getElementById("poolDetailData").value;
            if (parseInt(maxSuma) < parseInt(suma)) {
                e.target.suma.value = maxSuma
            } else {
                e.target.suma.value = suma;
            }
            let poolDetail = [];

            if (poolDetailData === "" || poolDetailData === undefined) {
                poolDetail.push({
                    subjektid: subjektid,
                    suma: suma,
                    mena: mena
                });
                document.getElementById("poolDetailData").value = JSON.stringify(poolDetail);
            } else {
                const arrayDetail = JSON.parse(poolDetailData);
                arrayDetail.push({
                    subjektid: subjektid,
                    suma: suma,
                    mena: mena
                });
                document.getElementById("poolDetailData").value = JSON.stringify(arrayDetail);
            }

            if (suma === null) {
                return;
            }
            console.log(item.querySelector("input"));
            $(item.querySelector("input")).trigger("focusout");
            recalculate();
        });
    });

    document.querySelector("#poolFilterForm")?.addEventListener("submit", (e) => {
        e.preventDefault();
    });

    let filterInputs = document.querySelectorAll("#poolFilterForm input, select");
    filterInputs?.forEach((item) => {
        item.addEventListener("change", (e) => {
            recalculate();
            document.querySelector("#submitPoolFilterForm").click();
        });
    });

    if (document.querySelector(".poolKonvSellForm")) {
        const allKonvSellingInputs = document.querySelectorAll(".poolKonvSellInput");
        const allKonvBuyingInputs = document.querySelectorAll(".poolKonvBuyInput");
        const allKonvSellForms = document.querySelectorAll(".poolKonvSellForm");
        const allKonvBuyForms = document.querySelectorAll(".poolKonvBuyForm");
        const kurz = document.getElementById("exchangeRate").value;

        allSumsInputs.forEach((item) => {
            item.addEventListener("keyup", (e) => {
                e.target.value = e.target.value;
            })
        })

        let sumSell = 0;
        let sumBuy = 0;

        function recalculate() {
            sumSell = 0;
            sumBuy = 0;
            allKonvSellingInputs.forEach((item) => {
                sumSell = sumSell + parseFloat(item.value);
            });
            allKonvBuyingInputs.forEach((item) => {
                sumBuy = sumBuy + parseFloat(item.value);
            });
            document.getElementById("poolSellingAllInput").value = sumSell;
            document.getElementById("poolSellingAll").innerHTML = sumSell.toLocaleString('de-DE', { minimumFractionDigits: 2, maximumFractionDigits: 2 }).replace(/\./g, ' ');
            document.getElementById("poolBuyingAllInput").value = sumBuy;
            document.getElementById("poolBuyingAll").innerHTML = sumBuy.toLocaleString('de-DE', { minimumFractionDigits: 2, maximumFractionDigits: 2 }).replace(/\./g, ' ');
        }

        allKonvSellForms.forEach((item, key) => {
            console.log(key);
            item.addEventListener("submit", (e) => {
                e.preventDefault();
                const formData = new FormData(e.currentTarget);
                let suma = formData.get("suma");
                if (suma === null) {
                    return;
                }
                const maxSuma = formData.get("maxPossible");
                const subjektid = formData.get("subjektid");
                const menaSell = formData.get("menaSell");
                const menaBuy = formData.get("menaBuy");
                const poolDetailData = document.getElementById("poolDetailData").value;
                const currencyPair = document.getElementById("currencyPair").value.substring(0, 3);
                const creditAccountCurrency = document.getElementById("creditAccountCurrency").value;

                let value = 0;
                let valueMax = 0;
                if (currencyPair === creditAccountCurrency) {
                    value = suma / kurz;
                    valueMax = maxSuma * kurz;
                } else {
                    value = suma * kurz;
                    valueMax = maxSuma / kurz;
                }

                const poolDetail = [];
                if (parseInt(maxSuma) < parseInt(suma)) {
                    e.target.suma.value = maxSuma;
                    suma = maxSuma;
                    allKonvBuyingInputs[key].value = valueMax;
                    allKonvSellingInputs[key].value = maxSuma;
                } else {
                    e.target.suma.value = suma;
                    allKonvBuyingInputs[key].value = value;
                    allKonvSellingInputs[key].value = suma;
                }

                if (poolDetailData === "" || poolDetailData === undefined) {
                    poolDetail.push({
                        subjektid: subjektid,
                        sumaSell: suma,
                        sumaBuy: allKonvBuyingInputs[key].value,
                        menaSell: menaSell,
                        menaBuy: menaBuy
                    });
                    document.getElementById("poolDetailData").value = JSON.stringify(poolDetail);
                } else {
                    const arrayDetail = JSON.parse(poolDetailData);
                    arrayDetail.push({
                        subjektid: subjektid,
                        sumaSell: suma,
                        sumaBuy: allKonvBuyingInputs[key].value,
                        menaSell: menaSell,
                        menaBuy: menaBuy
                    });
                    document.getElementById("poolDetailData").value = JSON.stringify(arrayDetail);
                }

                $(item.querySelector("input")).trigger("focusout");
                recalculate();
            });
        });

        allKonvBuyForms.forEach((item, key) => {
            console.log(item);
            item.addEventListener("submit", (e) => {
                e.preventDefault();
                const formData = new FormData(e.currentTarget);
                let suma = formData.get("suma");
                if (suma === null) {
                    return;
                }
                const maxSuma = formData.get("maxPossible");
                const subjektid = formData.get("subjektid");
                const menaSell = formData.get("menaSell");
                const menaBuy = formData.get("menaBuy");
                const poolDetailData = document.getElementById("poolDetailData").value;
                const poolDetail = [];
                const currencyPair = document.getElementById("currencyPair").value.substring(0, 3);
                const creditAccountCurrency = document.getElementById("creditAccountCurrency").value;
                let value = 0;
                let valueMax = 0;
                if (currencyPair !== creditAccountCurrency) {
                    value = suma / kurz;
                    valueMax = maxSuma / kurz;
                } else {
                    value = suma * kurz;
                    valueMax = maxSuma * kurz;
                }
                if (parseInt(maxSuma) < parseInt(suma)) {
                    e.target.suma.value = maxSuma;
                    suma = maxSuma;
                    allKonvBuyingInputs[key].value = maxSuma;
                    allKonvSellingInputs[key].value = valueMax;
                } else {
                    e.target.suma.value = suma;
                    allKonvBuyingInputs[key].value = suma;
                    allKonvSellingInputs[key].value = value;
                }

                if (poolDetailData === "" || poolDetailData === undefined) {
                    poolDetail.push({
                        subjektid: subjektid,
                        sumaSell: allKonvSellingInputs[key].value,
                        sumaBuy: suma,
                        menaSell: menaSell,
                        menaBuy: menaBuy
                    });
                    document.getElementById("poolDetailData").value = JSON.stringify(poolDetail);
                } else {
                    const arrayDetail = JSON.parse(poolDetailData);
                    arrayDetail.push({
                        subjektid: subjektid,
                        sumaSell: allKonvSellingInputs[key].value,
                        sumaBuy: suma,
                        menaSell: menaSell,
                        menaBuy: menaBuy
                    });
                    document.getElementById("poolDetailData").value = JSON.stringify(arrayDetail);
                }


                $(item.querySelector("input")).trigger("focusout");
                recalculate();
            });
        });
    }
});
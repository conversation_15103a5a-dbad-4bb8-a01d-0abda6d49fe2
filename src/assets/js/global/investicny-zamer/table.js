htmx.onLoad(function (content) {
    document.querySelectorAll(".deleteInvestmentIntention").forEach((item) => {
        item.addEventListener("submit", (e) => {
            e.stopPropagation();
            document.getElementById("deleteText").innerHTML = "Odstraňujem...";
            $(".deleteSpin").removeClass("hidden");
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        });
    });

    document.querySelectorAll(".detailEnabler").forEach((item) => {
        item.addEventListener("click", (e) => {
            e.stopPropagation();
            $(document.getElementById("editConfirm" + item.id)).toggle();
        });
    });
    
    document.querySelectorAll(".confirmKTVForm").forEach((item) => {
        item.addEventListener("submit", (e) => {
            e.preventDefault();
            const formData = new FormData(e.currentTarget);
            console.log(formData);
            $.ajax({
                url: `/src/Controllers/global/investicne-zamery/terminovany-vklad/update/konfTVZamer.php`,
                method: "POST",
                data: formData,
                processData: false,
                contentType: false,
                success: function (data) {
                    if (data === "success") {
                        window.location.reload();
                    } else {
                        alert("Plano daco!");
                    }
                },
                error: function (xhr, status, error) {
                    console.error("Error:", error);
                },
            });
        });
    });

    document.querySelectorAll(".confirmCPForm").forEach((item) => {
        item.addEventListener("submit", (e) => {
            e.preventDefault();
            const formData = new FormData(e.currentTarget);
            $.ajax({
                url: `/src/Controllers/global/investicne-zamery/terminovany-vklad/update/ConfirmCP.php`,
                method: "POST",
                data: formData,
                processData: false,
                contentType: false,
                success: function (data) {
                    if (data === "success") {
                        window.location.reload();
                    } else {
                        alert("Plano daco!");
                    }
                },
                error: function (xhr, status, error) {
                    console.error("Error:", error);
                },
            });
        });
    });

    $(".editIZButton").on("click", (e) => {
        e.stopPropagation();
        $(e.currentTarget).addClass("bg-gray-100");
        $(e.currentTarget).addClass("animate-pulse");
    });

    $(".assignedUsersOpen").on("click", (e) => {
        e.stopPropagation();
    });

    $(".removeMention").on("click", (e) => {
        e.currentTarget.querySelector(".removeMentionIcon").style.display = "none";
        e.currentTarget.querySelector(".removeMentionSpinner").style.display = "block";
    })

    document.querySelectorAll(".confirmKonverzia").forEach((item) => {
        item.addEventListener("submit", (e) => {
            item.querySelector("button .chevronConfirm").style.display = "none"
            item.querySelector("button .confirmSpinner").style.display = "inline-flex"
            item.querySelector("button span").innerHTML = "Potvrdzujem...";
            e.preventDefault();
            const formData = new FormData(e.currentTarget);
            $.ajax({
                url: `/src/Controllers/global/investicne-zamery/terminovany-vklad/update/ConfirmKonv.php`,
                method: "POST",
                data: formData,
                processData: false,
                contentType: false,
                success: function (data) {
                    if (data === "success") {
                        window.location.reload();
                    } else {
                        alert("Plano daco!");
                    }
                },
                error: function (xhr, status, error) {
                    console.error("Error:", error);
                },
            });
        });
    });
});
document.addEventListener("htmx:afterSwap", (event) => {
    document.querySelectorAll(".deleteInvestmentIntention").forEach((item) => {
        item.addEventListener("submit", (e) => {
            e.stopPropagation();
            document.getElementById("deleteText").innerHTML = "Odstraňujem...";
            $(".deleteSpin").removeClass("hidden");
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        });
    });

    document.querySelectorAll(".detailEnabler").forEach((item) => {
        item.addEventListener("click", (e) => {
            e.stopPropagation();
            $(document.getElementById("editConfirm" + item.id)).toggle();
        });
    });

    document.querySelectorAll(".confirmKTVForm").forEach((item) => {
        item.addEventListener("submit", (e) => {
            e.preventDefault();
            const formData = new FormData(e.currentTarget);
            $.ajax({
                url: `/src/Controllers/global/investicne-zamery/terminovany-vklad/update/konfTVZamer.php`,
                method: "POST",
                data: formData,
                processData: false,
                contentType: false,
                success: function (data) {
                    if (data === "success") {
                        window.location.reload();
                    } else {
                        alert("Plano daco!");
                    }
                },
                error: function (xhr, status, error) {
                    console.error("Error:", error);
                },
            });
        });
    });
    document.querySelectorAll(".confirmCPForm").forEach((item) => {
        item.addEventListener("submit", (e) => {
            e.preventDefault();
            const formData = new FormData(e.currentTarget);
            $.ajax({
                url: `/src/Controllers/global/investicne-zamery/terminovany-vklad/update/ConfirmCP.php`,
                method: "POST",
                data: formData,
                processData: false,
                contentType: false,
                success: function (data) {
                    if (data === "success") {
                        window.location.reload();
                    } else {
                        alert("Plano daco!");
                    }
                },
                error: function (xhr, status, error) {
                    console.error("Error:", error);
                },
            });
        });
    });
    document.querySelectorAll(".confirmKonverzia").forEach((item) => {
        item.addEventListener("submit", (e) => {
            item.querySelector("button .chevronConfirm").style.display = "none"
            item.querySelector("button .confirmSpinner").style.display = "inline-flex"
            item.querySelector("button span").innerHTML = "Potvrdzujem...";
            e.preventDefault();
            const formData = new FormData(e.currentTarget);
            $.ajax({
                url: `/src/Controllers/global/investicne-zamery/terminovany-vklad/update/ConfirmKonv.php`,
                method: "POST",
                data: formData,
                processData: false,
                contentType: false,
                success: function (data) {
                    if (data === "success") {
                        window.location.reload();
                    } else {
                        alert("Plano daco!");
                    }
                },
                error: function (xhr, status, error) {
                    console.error("Error:", error);
                },
            });
        });
    });
});
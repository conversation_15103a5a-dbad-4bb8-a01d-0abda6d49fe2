
document.getElementById("sellOrBuy").addEventListener("submit", (e) => {
    e.preventDefault();
    if (e.submitter.id === "buy") {
        document.getElementById("sell").classList.add("bg-red-300");
        document.getElementById("sell").classList.remove("bg-red-800");
        document.getElementById("sell").classList.add("text-red-900");
        document.getElementById("sell").classList.remove("shadow-xl");
        document.getElementById("sell").classList.remove("text-white");
        document.getElementById("buy").classList.remove("bg-green-300");
        document.getElementById("buy").classList.add("bg-green-800");
        document.getElementById("buy").classList.add("shadow-xl");
        document.getElementById("buy").classList.remove("text-green-900");
        document.getElementById("buy").classList.add("text-white");
        document.getElementById("buy").classList.add("animate-pulse");
    }
    if (e.submitter.id === "sell") {
        document.getElementById("buy").classList.add("bg-green-300");
        document.getElementById("buy").classList.remove("bg-green-800");
        document.getElementById("buy").classList.add("text-green-900");
        document.getElementById("buy").classList.remove("shadow-xl");
        document.getElementById("buy").classList.remove("text-white");
        document.getElementById("sell").classList.remove("bg-red-300");
        document.getElementById("sell").classList.add("bg-red-800");
        document.getElementById("sell").classList.add("shadow-xl");
        document.getElementById("sell").classList.remove("text-red-900");
        document.getElementById("sell").classList.add("text-white");
        document.getElementById("sell").classList.add("animate-pulse");
    }
});

let typingTimer;
let doneTyping = 600;

$("#simple-search").on("keyup", () => {
    clearTimeout(typingTimer);
    typingTimer = setTimeout(() => {
        $("#filterTableDataFast").submit();
    }, doneTyping);

});

$("#simple-search").on("keydown", () => {
    clearTimeout(typingTimer);
});

$(".inpucik").on("change", () => {
    $("#filterTableDataFast").submit();
});

$("#filterTableDataFast").on("submit", (e) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    const mena = formData.getAll("mena");
    const query = formData.get("searchQuery");
    const action = formData.get("action");
    const eqid = formData.get("eqid");
    const result = `'${mena.join("','")}'`;

    htmx.ajax('POST', `/src/Controllers/global/investicne-zamery/cp/get/searchedData.php`,
        {
            target: '#dataSpaceWrapper',
            values: {
                "query": query,
                "params": result,
                "action": action,
                "eqid": eqid
            }
        }).then(() => {

        });
});

document.addEventListener('htmx:afterRequest', function (evt) {
    if ($("#buy").hasClass("animate-pulse")) {
        $("#buy").removeClass("animate-pulse");
    }
    if ($("#sell").hasClass("animate-pulse")) {
        $("#sell").removeClass("animate-pulse");
    }

    let typingTimer;
    let doneTyping = 600;

    $("#simple-search").on("keyup", () => {
        clearTimeout(typingTimer);
        typingTimer = setTimeout(() => {
            $("#filterTableDataFast").submit();
        }, doneTyping);

    });

    $("#simple-search").on("keydown", () => {
        clearTimeout(typingTimer);
    });

    $(".inpucik").on("change", () => {
        $("#filterTableDataFast").submit();
    });

    $("#filterTableDataFast").on("submit", (e) => {
        e.preventDefault();
        const formData = new FormData(e.currentTarget);
        const mena = formData.getAll("mena");
        const query = formData.get("searchQuery");
        const action = formData.get("action");
        const eqid = formData.get("eqid");
        const result = `'${mena.join("','")}'`;

        htmx.ajax('POST', `/src/Controllers/global/investicne-zamery/cp/get/searchedData.php`,
            {
                target: '#dataSpaceWrapper',
                values: {
                    "query": query,
                    "params": result,
                    "action": action,
                    "eqid": eqid
                }
            }).then(() => {

            });
    });
});
document.addEventListener('htmx:afterSettle', function (evt) {
    document.getElementById("paginationForm").addEventListener("submit", (e) => {
        e.preventDefault();
        const link = e.submitter.value;
        const formData = new FormData(e.currentTarget);
        const page = formData.get("page");
        const filterData = $("#filteredValuesInput").val();
        const paginationRequest = htmx.ajax('POST', link, {
            target: '#pageContentMain',
            source: '#paginationForm',
            values: {
                "data": JSON.stringify(filterData)
            }
        });
        Promise.all([paginationRequest]).then(() => {
            console.log('Both requests completed successfully!');
        })
            .catch((error) => {
                console.error('One or both requests failed:', error);
            });
    });

    $("#feeCreateOpenClient").on("click", (e) => {
        e.stopPropagation();
        $("#dropdownklientCreate").css("display", "block");
    });

    document.getElementById("wholeForm").addEventListener("submit", (e) => {
        e.preventDefault();
        submitFormAndHandleData(e);
    });

    $("#dataToExcelBtn").on("click", () => {
        $("#wholeBodySubmitter").click();
    });

    $(".actionShow").on("click", (e) => {
        e.stopPropagation();
    });

    $(".filterToggler").on("click", (e) => {
        e.stopPropagation();
        setTimeout(() => {
            $(e.currentTarget).attr("hx-disable", "true");
        }, 300);
    });

    $("#killClient").on("submit", (e) => {
        e.preventDefault();
        const formData = new FormData(e.currentTarget);
        const data = formData.get("data");
        htmx.ajax('POST', `/api/poplatky/change/global/termination`, {
            target: "#terminationBody",
            values: { "data": data }
        }).then((response) => {

        });
    });
});

$(".actionShow").on("click", (e) => {
    e.stopPropagation();
});

function showActionMenu(e) {
    e.parentNode.parentNode.querySelector(".tableCheckboxSelect").click();
    console.log(e.parentNode.childNodes[0].nextSibling)
    e.parentNode.childNodes[0].nextSibling.style.display = "block";
}

$(".filterToggler").on("click", (e) => {
    e.stopPropagation();
    setTimeout(() => {
        $(e.currentTarget).attr("hx-disable", "true");
    }, 300);
});

$(document).click(function (event) {
    var $target = $(event.target);
    if (!$target.closest('.actionConfirm').length) {
        $(".actionConfirm").css("display", "none");
    }
    if (!$target.closest('#dropdownklientCreate').length) {
        $("#dropdownklientCreate").css("display", "none");
    }
});

$(".sortToggler").on("click", (e) => {
    e.stopPropagation();
    $(e.currentTarget).addClass("hidden");
    $(e.currentTarget).parent().siblings(".hiddenSort").val("DESC");
    $(e.currentTarget).siblings(".sortTogglerDESC").removeClass("hidden");
});

$(".sortTogglerDESC").on("click", (e) => {
    e.stopPropagation();
    $(e.currentTarget).addClass("hidden");
    $(e.currentTarget).parent().siblings(".hiddenSort").val("ASC");
    $(e.currentTarget).siblings(".sortTogglerASC").removeClass("hidden");
});

$(".sortTogglerASC").on("click", (e) => {
    e.stopPropagation();
    $(e.currentTarget).addClass("hidden");
    $(e.currentTarget).parent().siblings(".hiddenSort").val("");
    $(e.currentTarget).siblings(".sortToggler").removeClass("hidden");
});

$("#limit").on("change", () => {
    $("#wholeBodySubmitter").click();
});

$("#resetFilter").on("click", (e) => {
    $("#filteredValues").html("");
    $("#wholeForm").trigger("reset");
    $("#filteredValuesInput2").val("");
    document.querySelectorAll(".klientCheckbox").forEach((item) => {
        item.checked = false;
    });
    $("#pageNumber").val("1");
    $("#wholeBodySubmitter").click();
    window.history.pushState("", "", "/poplatky");
    e.currentTarget.classList.add("hidden");
    $("#filteredValues").html("");
});

function submitWholeForm(e) {
    $("#wholeBodySubmitter").click();
}

let checked = [];

function handleCheckboxSelect(e) {
    const checkboxes = document.querySelectorAll(".tableCheckboxSelect");
    let sum;
    if ($("#sumSumaValue").html() === "") {
        sum = 0;
    } else {
        sum = parseFloat($("#sumSumaValue").html());
    }

    checkboxes.forEach((item) => {
        if (item.checked) {
            checked.push(item.closest("tr").querySelector(".reconfirmedColumnData input").value)
        }
    });
    if (e.checked) {
        const menaItem = $(e).closest("tr")[0].querySelector(".menaColumnData input").value;
        const globalMena = $("#sumSumaMena").val();
        console.log(checked);
        const allAreReconfirmed = checked.every((val, i, arr) => val === arr[0]);

        console.log(allAreReconfirmed);
        if (globalMena === "") {
            $("#sumSumaMena").val(menaItem);
        } else {
            if (menaItem !== globalMena) {
                alert("Nemôžete vybrať poplatky s rôznymi menami");
                e.checked = false;
                return;
            }
            if (!allAreReconfirmed) {
                alert("Nemôžete vybrať nerekonfirmované a rekonfirmované poplatky spoločne!");
                checked = [];
                e.checked = false;
                return;
            }
        }
        $(e).closest("tr").removeClass("bg-white dark:bg-gray-800");
        $(e).closest("tr").addClass("bg-gray-300 dark:bg-gray-600");
        const row = $(e).closest("tr");
        let suma = parseFloat(row[0].querySelector(".sumaColumnData input").value);
        sum = sum + suma;
        $("#sumSumaValue").html(sum.toFixed(2));
    } else {
        $("#sumSumaMena").val("");
        const row = $(e).closest("tr");
        let suma = parseFloat(row[0].querySelector(".sumaColumnData input").value);
        sum = sum - suma;
        $("#sumSumaValue").html(sum.toFixed(2));
        $(e).closest("tr").addClass("bg-white dark:bg-gray-800");
        $(e).closest("tr").removeClass("bg-gray-300 dark:bg-gray-600");
    }

    if (checked.length > 0) {
        if (checked.every((val, i, arr) => val === "0")) {
            $("#payment").css("display", "none");
            $("#changeMena").css("display", "none");
            $("#changeSum").css("display", "none");
        } else {
            $("#changeMena").css("display", "inline-flex");
            $("#changeSum").css("display", "inline-flex");
            $("#payment").css("display", "inline-flex");
        }
        checked = [];
        $("#sumSuma").removeClass("hidden");
    } else {
        $("#changeMena").css("display", "inline-flex");
        $("#changeSum").css("display", "inline-flex");
        $("#payment").css("display", "inline-flex");
        $("#sumSuma").addClass("hidden");
    }
}

$(".tableCheckboxSelect").on("click", (e) => {
    handleCheckboxSelect(e.currentTarget);
});

$("#checkbox-all-search").on("click", (e) => {
    let sum = 0.00;
    const checkboxes = document.querySelectorAll(".tableCheckboxSelect");
    let foundError = false;
    for (const item of checkboxes) {
        if (!item.checked) {
            const menaItem = $(item).closest("tr")[0].querySelector(".menaColumnData input").value;
            const globalMena = $("#sumSumaMena").val();
            const areMinuses = $(checkboxes[0]).closest("tr")[0].querySelector(".sumaColumnData input[name='sumaMinus']").value;
            const isMinus = $(item).closest("tr")[0].querySelector(".sumaColumnData input[name='sumaMinus']").value;
            const areReconfirmed = $(checkboxes[0]).closest("tr")[0].querySelector(".reconfirmedColumnData input").value;
            const isReconfirmed = $(item).closest("tr")[0].querySelector(".reconfirmedColumnData input").value;

            if (areReconfirmed !== isReconfirmed) {
                alert("Nemôžete vybrať nerekonfirmované a rekonfirmované poplatky spoločne!");
                e.checked = false;
                foundError = true;
                break;
            }

            if (areMinuses !== isMinus) {
                alert("Nemôžete vybrať poplatky s mínusovými hodnotami a plusovými spoločne!");
                e.checked = false;
                foundError = true;
                break;
            }
            if (globalMena === "") {
                $("#sumSumaMena").val(menaItem);
            } else {
                if (menaItem !== globalMena) {
                    alert("Nemôžete vybrať poplatky s rôznymi menami");
                    e.checked = false;
                    foundError = true;
                    break;
                }
            }
            $(item).closest("tr").removeClass("bg-white dark:bg-gray-800");
            $(item).closest("tr").addClass("bg-gray-300 dark:bg-gray-600");
            const row = $(item).closest("tr");
            let suma = parseFloat(row[0].querySelector(".sumaColumnData input").value);
            sum += suma;
            $("#sumSumaValue").html(sum.toFixed(2));
            if (sum > 0) {
                $("#sumSuma").removeClass("hidden");
            }
            $(item).closest(".sumaColumnData > input").val();
        } else {
            $("#sumSumaMena").val("");
            const row = $(item).closest("tr");
            let suma = parseFloat(row[0].querySelector(".sumaColumnData input").value);
            sum -= suma;
            $("#sumSumaValue").html(sum.toFixed(2));
            if (sum <= 0) {
                $("#sumSuma").addClass("hidden");
            }
            $(item).closest("tr").addClass("bg-white dark:bg-gray-800");
            $(item).closest("tr").removeClass("bg-gray-300 dark:bg-gray-600");
        }
        item.checked = !item.checked;
    };

    let checked = [];

    checkboxes.forEach((item) => {
        if (item.checked) {
            checked.push(item.closest("tr").querySelector(".reconfirmedColumnData input").value)
        }
    });
    if (checked.length === 0) {
        $("#payment").css("display", "inline-flex");
    }

    if (checked.length > 0 && checked.every((val) => val === "0")) {
        $("#payment").css("display", "none");
    }


    if (foundError) {
        e.currentTarget.checked = false;
        for (const item of checkboxes) {
            item.checked = false;
            $("#sumSumaMena").val("");
            const row = $(item).closest("tr");
            let suma = parseFloat(row[0].querySelector(".sumaColumnData input").value);
            sum -= suma;
            $("#sumSumaValue").html(sum.toFixed(2));
            if (sum <= 0) {
                $("#sumSuma").addClass("hidden");
            }
            $(item).closest("tr").addClass("bg-white dark:bg-gray-800");
            $(item).closest("tr").removeClass("bg-gray-300 dark:bg-gray-600");
        }
    }
    checked = [];
});

$("#feeCreateOpenClient").on("click", (e) => {
    e.stopPropagation();
    $("#dropdownklientCreate").css("display", "block");
});

function executeAction(e, action) {
    const formData = new FormData(e.currentTarget);
    console.log(formData);
    const dataCount = formData.getAll("idcheck");
    const ids = formData.getAll("id");
    const formDataObj = [];
    for (let i = 0; dataCount.length > i; i++) {
        if (ids.includes(formData.getAll("idcheck")[i])) {
            formDataObj.push({
                id: formData.getAll("idcheck")[i],
                cislozmluvy: formData.getAll("cislozmluvy")[i],
                typ: formData.getAll("typ")[i],
                datum: formData.getAll("datum")[i],
                mena: formData.getAll("mena")[i],
                isin: formData.getAll("isin")[i],
                cub: formData.getAll("cub")[i],
                cash: formData.getAll("cash")[i],
                hotovost: formData.getAll("hotovost")[i],
                suma: formData.getAll("suma")[i],
                dansadzba1: formData.getAll("dansadzba1")[i],
                dan: formData.getAll("dan")[i],
                fondid: formData.getAll("fondid")[i],
                uhradapoplatkov: formData.getAll("uhradapoplatkov")[i]
            });
        }
    }

    console.log(dataCount);
    console.log(ids);
    console.log(formDataObj);

    switch (action) {
        case "changeMena":
            $("#modalFormAction").val("changeMena");
            $("#modalHeading").html("Zmeny meny poplatkov");
            htmx.ajax('POST', `/api/poplatky/change/global/currTable`,
                {
                    target: "#amountChangeModalBody",
                    values: { "data": JSON.stringify(formDataObj) }
                }).then((response) => {

                });
            break;
        case "reconfirm":
            $("#modalFormAction").val("reconfirm");
            $("#modalHeading").html("Rekonfirmácia poplatkov");
            htmx.ajax('POST', `/api/poplatky/change/global/rekonfTable`,
                {
                    target: "#amountChangeModalBody",
                    values: { "data": JSON.stringify(formDataObj) }
                }).then((response) => {

                });
            break;
        case "cancelContract":
            htmx.ajax('POST', `/api/poplatky/change/global/terminationQuestion`,
                {
                    target: "#terminationBody",
                    values: { "data": JSON.stringify(formDataObj) }
                }).then((response) => {

                });
            break;
        case "changeSum":
            $("#modalHeading").html("Zmeny sumy poplatkov");
            $("#modalFormAction").val("changeSum");
            htmx.ajax('POST', `/api/poplatky/change/global/sumTable`,
                {
                    target: "#amountChangeModalBody",
                    values: { "data": JSON.stringify(formDataObj) }
                }).then((response) => {

                });
            break;
        case "payment":
            console.log(("Pan Boh zaplac!"));
            htmx.ajax('POST', `/api/poplatky/change/global/payment`,
                {
                    target: "#splatenieModalek",
                    values: { "data": JSON.stringify(formDataObj) }
                }).then((response) => {

                });
            break;
    }

}

function exportToExcel(e, action) {
    const formData = new FormData(e.currentTarget);
    const ids = formData.getAll("cislozmluvy");
    const columns = formData.getAll("column");
    document.getElementById("excelIcon").style.display = "none";
    document.getElementById("spinnerExcel").style.display = "block";
    const formDataObj = [];
    for (let i = 0; ids.length > i; i++) {
        formDataObj.push({
            cislozmluvy: formData.getAll("cislozmluvy")[i],
            typ: formData.getAll("typ")[i],
            reconfirmed: formData.getAll("reconfirmed")[i] === 0 ? "Nerekonfirmovaný" : "Rekonfirmovaný",
            datum: formData.getAll("datum")[i],
            isin: formData.getAll("isin")[i],
            suma: formData.getAll("suma")[i],
            mena: formData.getAll("mena")[i],
            cub: formData.getAll("cub")[i] === "" ? "neexistuje" : formData.getAll("cub")[i],
            hotovost: formData.getAll("hotovost")[i] > 0 ? "Má" : "Nemá",
            fondid: formData.getAll("fondid")[i],
            dan: formData.getAll("dan")[i] === "" ? 0 : formData.getAll("dan")[i]
        });

    }

    if (formDataObj.length > 0) {
        $.ajax({
            url: "/src/Controllers/global/exportToExcel.php",
            method: "POST",
            data: JSON.stringify({ "data": formDataObj, "columns": columns }),
            dataType: "json",
            contentType: "application/json",
            success: function (data) {
                console.log(data);
                fetch(data.link.replace("/home/<USER>/www", ""))
                    .then(response => response.blob())
                    .then(blob => {
                        const url = URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = data.link.replace("/home/<USER>/www", "");
                        a.click();
                        URL.revokeObjectURL(url);
                        document.getElementById("excelIcon").style.display = "block";
                        document.getElementById("spinnerExcel").style.display = "none";
                    })
                    .catch(error => {
                        alert('File download failed:' + error);
                        console.error('File download failed:', error);
                    });
            },
            error: function (xhr, status, error) {
                console.error("Error:", error);
            },
        });
    }
}

function getFilteredFilter(whatToGet, target) {
    const filterData = document.getElementById("filteredValuesInput2").value;
    console.log(target);
    console.log(`/api/poplatky/get/${whatToGet}`);
    htmx.ajax('POST', `/api/poplatky/get/${whatToGet}`, {
        target: `#${target}`,
        values: {
            "data": filterData
        }
    });
}

function filterTable(e) {
    const formData = new FormData(e.currentTarget);
    const clients = formData.getAll("klientFilter");
    const types = formData.getAll("typFilter");
    const dates = formData.getAll("datumFilter");
    const isins = formData.getAll("isinFilter");
    const meny = formData.getAll("menaFilter");
    const hotovost = formData.get("hotovost");
    const reconfirmed = formData.get("rekonfirmedFilter");
    const page = formData.get("page");
    const limit = formData.get("limit");
    //SORTING
    const klientSort = formData.get("klientSort");
    const typSort = formData.get("typSort");
    const dateSort = formData.get("dateSort");
    const isinSort = formData.get("isinSort");
    const menaSort = formData.get("menaSort");
    const hotovostSort = formData.get("hotovostSort");
    let sorting;

    if (klientSort == "" && typSort == "" && dateSort == "" && isinSort == "" && menaSort == "" && hotovostSort == "") {
        sortingEnabled = false;
    } else {
        sortingEnabled = true
    }

    const filterData = {
        "clients": clients,
        "types": types,
        "dates": dates,
        "isins": isins,
        "meny": meny,
        "hotovost": hotovost,
        "reconfirmed": reconfirmed
    }

    $("#filteredValuesInput").val(JSON.stringify(filterData));
    $("#filteredValuesInput2").val(JSON.stringify(filterData));

    if (clients.length === 0 && types.length === 0 && dates.length === 0 && isins.length === 0 && meny.length === 0 && hotovost === "0" && reconfirmed === "") {
        $("#resetFilter").addClass("hidden");
    } else {
        $("#resetFilter").removeClass("hidden");

    }

    const filterRequest = htmx.ajax('POST', `/api/poplatky/get/filteredData`, {
        target: '#poplatkyTBODY',
        values: {
            "data": JSON.stringify(filterData),
            "page": page,
            "klientSort": klientSort,
            "typSort": typSort,
            "dateSort": dateSort,
            "isinSort": isinSort,
            "menaSort": menaSort,
            "hotovostSort": hotovostSort,
            "sortingEnabled": sortingEnabled,
            "limit": limit
        }
    });

    const paginationRequest = htmx.ajax('POST', `/api/poplatky/get/paginationData`, {
        target: '#pagination',
        values: {
            "data": JSON.stringify(filterData),
            "page": page,
            "limit": limit
        }
    });

    Promise.all([filterRequest, paginationRequest]).then(() => {
        setTimeout(() => {
            htmx.ajax('POST', `/api/poplatky/get/displayFilteredData`, {
                target: '#filteredValues',
                values: {
                    "data": JSON.stringify(filterData),
                    "page": page
                }
            }).then(() => {
            });
            htmx.ajax('POST', `src/Controllers/global/poplatky/resultCount.php`, {
                target: '#limitSelectWrapper',
                values: {
                    "data": JSON.stringify(filterData),
                    "limit": limit
                }
            });
        }, 500);
        const resultCount = document.querySelector(".totalCountInput");
        if (resultCount !== null) {
            if (resultCount.value <= 30) {
                window.history.pushState('', '', '/poplatky');
                htmx.ajax('POST', `/api/poplatky/get/paginationData`, {
                    target: '#pagination',
                    values: {
                        "data": JSON.stringify(filterData),
                        "page": 1,
                        "limit": resultCount.value
                    }
                });
            }
            document.getElementById("totalCountStrong").innerHTML = `(${resultCount.value})`;
            document.getElementById("totalCountBottom").innerHTML = resultCount.value;
        } else {
            document.getElementById("totalCountStrong").innerHTML = "(0)";
            document.getElementById("totalCountBottom").innerHTML = "0";
        }
    })
        .catch((error) => {
            console.error('One or both requests failed:', error);
        });
}

function submitFormAndHandleData(e) {
    console.log(e.submitter);
    const action = e.submitter.id;
    switch (action) {
        case "changeMena":
        case "reconfirm":
        case "paymentTrigger":
        case "cancelContract":
        case "changeSum":
            executeAction(e, action === "paymentTrigger" ? "payment" : action);
            break;
        case "dataToExcelBtn":
            exportToExcel(e, "all");
            break;
        default:
            filterTable(e);
            break;
    }
}

document.getElementById("wholeForm").addEventListener("submit", (e) => {
    e.preventDefault();
    submitFormAndHandleData(e);
});

document.getElementById("paginationForm").addEventListener("submit", (e) => {
    e.preventDefault();
    const link = e.submitter.value;
    const formData = new FormData(e.currentTarget);
    const page = formData.get("page");
    const limit = formData.get("limit");
    console.log(limit);
    const filterData = $("#filteredValuesInput").val();
    console.log(filterData, link);
    const paginationRequest = htmx.ajax('POST', link, {
        target: '#pageContentMain',
        source: '#paginationForm',
        values: {
            "data": JSON.stringify(filterData),
            "limit": limit
        }
    });
    Promise.all([paginationRequest]).then(() => {
        console.log('Both requests completed successfully!');
    })
        .catch((error) => {
            console.error('One or both requests failed:', error);
        });
});

$("#globalActionForm").on("submit", (e) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    const dataCount = formData.getAll("id");
    const formDataObj = [];
    const action = formData.get("action");

    $("#changeAmountModalSubmit").html("Potvrdzujem...");
    $("#changeAmountModalSubmitSpinner").removeClass("hidden");

    for (let i = 0; dataCount.length > i; i++) {
        formDataObj.push({
            id: formData.getAll("id")[i],
            cislozmluvy: formData.getAll("cislozmluvy")[i],
            typ: formData.getAll("typ")[i],
            mena: formData.getAll("mena")[i],
            suma: formData.getAll("suma")[i],
            sumaPrevious: formData.getAll("sumaPrevious")[i],
            dansadzba1: formData.getAll("dansadzba1")[i],
            fondid: formData.getAll("fondid")[i],
            novamena: formData.getAll("novamena")[i],
            cub: formData.getAll("cub")[i],
            uhradapoplatkov: formData.getAll("uhradapoplatkov")[i]
        });
    }

    console.log(formDataObj);

    switch (action) {
        case "changeMena":
            htmx.ajax('POST', `/api/poplatky/change/global/curr`,
                {
                    target: "#toast",
                    values: { "data": JSON.stringify(formDataObj) }
                }).then((response) => {

                });
            break;
        case "changeSum":
            htmx.ajax('POST', `/api/poplatky/change/global/sum`,
                {
                    target: "#toast",
                    values: { "data": JSON.stringify(formDataObj) }
                }).then((response) => {

                });
            break;
        case "reconfirm":
            htmx.ajax('POST', `/api/poplatky/change/global/rekonf`,
                {
                    target: "#toast",
                    values: { "data": JSON.stringify(formDataObj) }
                }).then((response) => {

                });
            break;
        case "payment":
            console.log(("Pan Boh zaplac!"));
            htmx.ajax('POST', `/api/poplatky/change/global/payment`,
                {
                    target: "#splatenieModalek",
                    values: { "data": JSON.stringify(formDataObj) }
                }).then((response) => {

                });
            break;
    }

});

$("#changeAmountModal").on("click", () => {
    $(document.body).removeClass("overflow-hidden");
});

function addAccToCreation(e) {
    console.log(e);
    $("#feeCreateOpenClient").html(`
        <span class="bg-indigo-100 text-indigo-800 text-sm font-bold border me-2 px-2.5 py-0.5 rounded-sm dark:bg-indigo-900 dark:text-indigo-300">${e.parentNode.innerText}</span>
    `);
    $("#dropdownklientCreate").css("display", "none");
}

(function () {
    var counter;
    var count = 0;
})();

function start(e) {
    $(e).addClass("buttonPayment");
    $(".buttonPayment").css("background-position", "right");
    counter = setInterval(function () {
        console.log(count);
        count++;
    }, 500);
}
function end(e) {
    console.log(count >= 5, "COUNT AT MOUSEOUT");
    if (count >= 5) {
        const paymentTrigger = document.getElementById("paymentTrigger");
        console.log(paymentTrigger);
        paymentTrigger.click();
    }
    $(".buttonPayment").css("background-position", "left");
    count = 0;
    clearInterval(counter);
}

$("#paymentTrigger").click((e) => {
    console.log("click");
});

function createFee(e){
    const formData = new FormData(e.currentTarget);
    console.log(formData);
    $("#createFeeSubmit").html("Potvrdzujem...");
    $("#createFeeSubmitSpinner").removeClass("hidden");
    const fondid = formData.get("fondid");
    const varRate = formData.get("vatRate");
    const typPoplatku = formData.get("typpoplatku");
    const amountWithoutVat = formData.get("amountWithoutVat");
    const reason = formData.get("reason");
    const mena = formData.get("mena");

    htmx.ajax('POST', `/api/poplatky/create`, {
        target: "#toastCreate",
        values: {
            "fondid": fondid,
            "varRate": varRate,
            "typPoplatku": typPoplatku,
            "amountWithoutVat": amountWithoutVat,
            "reason": reason,
            "mena": mena
        }
    }).then((response) => {

    });
}
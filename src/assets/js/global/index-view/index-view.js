document.addEventListener('htmx:afterSettle', function (evt) {
    document.getElementById("paginationForm")?.addEventListener("submit", (e) => {
        e.preventDefault();
        const link = e.submitter.value;
        const formData = new FormData(e.currentTarget);
        const page = formData.get("page");
        const filterData = $("#filteredValuesInput").val();
        const paginationRequest = htmx.ajax('POST', link, {
            target: '#pageContentMain',
            source: '#paginationForm',
            values: {
                "data": JSON.stringify(filterData)
            }
        });
        Promise.all([paginationRequest]).then(() => {
            console.log('Both requests completed successfully!');
        })
            .catch((error) => {
                console.error('One or both requests failed:', error);
            });
    });

    $("#feeCreateOpenClient").on("click", (e) => {
        e.stopPropagation();
        $("#dropdownklientCreate").css("display", "block");
    });

    document.getElementById("wholeForm").addEventListener("submit", (e) => {
        e.preventDefault();
        submitFormAndHandleData(e);
    });

    $("#dataToExcelBtn").on("click", () => {
        $("#wholeBodySubmitter").click();
    });

    $(".actionShow").on("click", (e) => {
        e.stopPropagation();
    });

    $(".filterToggler").on("click", (e) => {
        e.stopPropagation();
        setTimeout(() => {
            $(e.currentTarget).attr("hx-disable", "true");
        }, 300);
    });

    $("#killClient").on("submit", (e) => {
        e.preventDefault();
        const formData = new FormData(e.currentTarget);
        const data = formData.get("data");
        htmx.ajax('POST', `/api/poplatky/change/global/termination`, {
            target: "#terminationBody",
            values: { "data": data }
        }).then((response) => {

        });
    });
});

$(".actionShow").on("click", (e) => {
    e.stopPropagation();
});

function showActionMenu(e) {
    e.parentNode.parentNode.querySelector(".tableCheckboxSelect").click();
    console.log(e.parentNode.childNodes[0].nextSibling)
    e.parentNode.childNodes[0].nextSibling.style.display = "block";
}

$(".filterToggler").on("click", (e) => {
    e.stopPropagation();
    setTimeout(() => {
        $(e.currentTarget).attr("hx-disable", "true");
    }, 300);
});

$(document).click(function (event) {
    var $target = $(event.target);
    if (!$target.closest('.actionConfirm').length) {
        $(".actionConfirm").css("display", "none");
    }
    if (!$target.closest('#dropdownklientCreate').length) {
        $("#dropdownklientCreate").css("display", "none");
    }
});

$(".sortToggler").on("click", (e) => {
    e.stopPropagation();
    $(e.currentTarget).addClass("hidden");
    $(e.currentTarget).parent().siblings(".hiddenSort").val("DESC");
    $(e.currentTarget).siblings(".sortTogglerDESC").removeClass("hidden");
});

$(".sortTogglerDESC").on("click", (e) => {
    e.stopPropagation();
    $(e.currentTarget).addClass("hidden");
    $(e.currentTarget).parent().siblings(".hiddenSort").val("ASC");
    $(e.currentTarget).siblings(".sortTogglerASC").removeClass("hidden");
});

$(".sortTogglerASC").on("click", (e) => {
    e.stopPropagation();
    $(e.currentTarget).addClass("hidden");
    $(e.currentTarget).parent().siblings(".hiddenSort").val("");
    $(e.currentTarget).siblings(".sortToggler").removeClass("hidden");
});

$("#limit").on("change", () => {
    $("#wholeBodySubmitter").click();
});

$("#resetFilter").on("click", (e) => {
    $("#filteredValues").html("");
    $("#wholeForm").trigger("reset");
    $("#filteredValuesInput2").val("");
    document.querySelectorAll(".klientCheckbox").forEach((item) => {
        item.checked = false;
    });
    $("#pageNumber").val("1");
    $("#wholeBodySubmitter").click();
    window.history.pushState("", "", "/poplatky");
    e.currentTarget.classList.add("hidden");
    $("#filteredValues").html("");
});

function submitWholeForm(e) {
    console.log("SUBMITTED");
    $("#wholeBodySubmitter").click();
}

function getFilteredFilter(whatToGet, target) {
    const filterData = document.getElementById("filteredValuesInput2").value;
    console.log(target);
    console.log(`/api/get/${whatToGet}`);
    htmx.ajax('POST', `/api/get/${whatToGet}`, {
        target: `#${target}`,
        values: {
            "data": filterData
        }
    });
}

function filterTable(e) {
    const formData = new FormData(e.currentTarget);
    console.log(formData);
    const types = formData.getAll("druhFilter");
    const aktiva = formData.getAll("aktivaFilter");
    //SORTING
    //const klientSort = formData.get("klientSort");
    let sorting;

    // if (klientSort == "" && typSort == "" && dateSort == "" && isinSort == "" && menaSort == "" && hotovostSort == "") {
    //     sortingEnabled = false;
    // } else {
    //     sortingEnabled = true
    // }

    const filterData = {
        "types": types,
        "aktiva": aktiva
    }

    $("#filteredValuesInput2").val(JSON.stringify(filterData));

    if (types.length === 0) {
        $("#resetFilter").addClass("hidden");
    } else {
        $("#resetFilter").removeClass("hidden");
    }
    document.getElementById("offset").value = 0;
    htmx.ajax('POST', `/api/index-view/get/filteredData`, {
        target: '#poplatkyTBODY',
        values: {
            "data": JSON.stringify(filterData),
            "offset": 0,
        }
    });
}

function submitFormAndHandleData(e) {
    filterTable(e);
}

document.getElementById("wholeForm").addEventListener("submit", (e) => {
    e.preventDefault();
    submitFormAndHandleData(e);
});
function addRow() {
    last = document.getElementsByClassName("lastInput");
    $("#bulkTbody").append(`<tr id="row${last.length}"></tr>`);
    htmx.ajax('POST', `/api/get/newRowToUhrady`, {
        target: `#row${last.length}`,
        values: {
            "last": last.length
        }
    }).then((response) => {
        const firstInput = document.querySelectorAll(".firstInput")
        console.log(firstInput);
        firstInput[firstInput.length - 1].focus();
        document.querySelectorAll(".lastInput").forEach((element) => {
            element.addEventListener("focusout", (e) => {
                addRow();
            });
            element.addEventListener("click", (e) => {
                addFilledRow(e.target.id);
                $(e.target).addClass("copied");
            });
        });
    });
}



function addFilledRow(id) {
    last = document.getElementsByClassName("lastInput");
    $("#bulkTbody").append(`<tr id="row${last.length}"></tr>`);

    const data = [{
        suma: document.getElementById(`suma${id}`).value,
        vs: document.getElementById(`vs${id}`).value,
        ks: document.getElementById(`ks${id}`).value,
        ss: document.getElementById(`ss${id}`).value,
        ucetpartnera: document.getElementById(`ucetpartnera${id}`).value,
        nazovpartnera: document.getElementById(`nazovpartnera${id}`).value,
        splatnost: document.getElementById(`splatnost${id}`).value,
        forma: document.getElementById(`forma${id}`).value
    }]
    console.log(data);
    htmx.ajax('POST', `/api/get/newFilledRowToUhrady`, {
        target: `#row${last.length}`,
        values: { "data": JSON.stringify(data), "last": last.length }
    }).then((response) => {
        const firstInput = document.querySelectorAll(".firstInput")
        console.log(firstInput);
        firstInput[firstInput.length - 1].focus();
        document.querySelectorAll(".lastInput").forEach((element) => {
            element.addEventListener("focusout", (e) => {
                addRow();
            });
            element.addEventListener("click", (e) => {
                addFilledRow(e.target.id);
                $(e.target).addClass("copied");
            });
        });
    });
}

$(".lastInput").on("click", (e) => {
    addFilledRow(e.target.id);
    $(e.target).addClass("copied");
});

$(".lastInput").on("focusout", (e) => {
    if (e.target.classList.contains("copied")) return;
    addRow();
});

$(".zauctovatBtn").on("click", (e) => {
    $(".zauctovatBtn > span").html("Účtovanie...");
    $(".zauctovatBtn > svg").css("display", "block");
});

$(".zauctujShow").on("click", (e) => {
    e.stopPropagation();
    $(e.currentTarget).siblings(".zauctujConfirm").removeClass("hidden");
});

$(document).on("click", (e) => {
    if (!$(e.target).closest('.zauctujShow').length && !$(e.target).closest('.zauctujConfirm').length) {
        $(".zauctujConfirm").addClass("hidden");
    }
});

async function addPayment(e) {
    const formData = new FormData(e.currentTarget);
    let dataCount = formData.getAll("suma").length;
    if (dataCount === 0) {
        return;
    }
    const formDataObj = [];
    let IVAR = 0;
    console.log(formData);
    document.getElementById("readyPaymentsTbody").childNodes.forEach((e) => {
        if (e.id !== undefined && e.id.includes("uhradanew")) {
            IVAR++;
        }
    });

    console.log('Total rows to process:', dataCount);

    // First create all rows and collect data
    for (let i = 0; dataCount > i; i++) {
        $("#readyPaymentsTbody").append(`<tr id="uhradanew${i + 1 + IVAR}" class="w-full text-md dark:bg-gray-900" colspan="10"><td class="p-1 text-center" colspan="10"><span class="flex gap-2 justify-center p-1 items-center"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide animate-spin lucide-loader-circle"><path d="M21 12a9 9 0 1 1-6.219-8.56"/></svg>Načítávam úhradu...</span></td></tr>`);
        formDataObj.push({
            suma: formData.getAll("suma")[i],
            vs: formData.getAll("vs")[i],
            ks: formData.getAll("ks")[i],
            ss: formData.getAll("ss")[i],
            ucetpartnera: formData.getAll("ucetpartnera")[i],
            nazovpartnera: formData.getAll("nazovpartnera")[i],
            splatnost: formData.getAll("splatnost")[i],
            mena: formData.getAll("mena")[i],
            cub: formData.get("cub"),
            forma: formData.getAll("forma")[i],
            key: i + 1 + IVAR
        });
    }

    for (const data of formDataObj) {
        console.log('Processing row:', data.key);
        try {
            await htmx.ajax('POST', `/api/vysporiadanie/readyPayments`, {
                target: `#uhradanew${data.key}`,
                values: {
                    suma: data.suma,
                    vs: data.vs,
                    ks: data.ks,
                    mena: data.mena,
                    ss: data.ss,
                    ucetpartnera: data.ucetpartnera,
                    nazovpartnera: data.nazovpartnera,
                    splatnost: data.splatnost,
                    forma: data.forma,
                    cub: data.cub,
                    key: data.key
                }
            });
            $("#uhradanew" + data.key).removeAttr("class");
            $("#uhradanew" + data.key).addClass("bg-white border-b dark:bg-gray-900 dark:border-gray-700 border-gray-200");
            console.log('Completed processing row:', data.key);
        } catch (error) {
            console.error('Error processing row:', data.key, error);
        }
    }

    console.log('All rows processed');
    e.target.reset();
}

document.getElementById("paymentForm").addEventListener("submit", async (e) => {
    e.preventDefault();
    await addPayment(e);
});

document.getElementById("paymentForm2").addEventListener("submit", async (e) => {
    e.preventDefault();
    await addPayment(e);
});

$(".moveUhradaFormOriginal").on("submit", (e) => {
    e.preventDefault();
    document.addEventListener('htmx:afterSettle', function (evt) {
        console.log(document.getElementById("ocakavaneUhradyTBody").children.length);
        if (document.getElementById("ocakavaneUhradyTBody").children.length === 0) {
            $("#ocakavaneUhradyTBody").append(`<tr id="waitingNoDataDiv"
                    class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 border-gray-200">
                    <td colspan="6"
                        class="px-6 py-4 text-center font-medium text-gray-900 dark:text-gray-100 whitespace-nowrap dark:text-white">
                        <div class="p-4 my-4 text-sm text-blue-800 rounded-lg bg-blue-50 w-full dark:bg-gray-700 dark:text-blue-400"
                            role="alert">
                            <span class="font-bold">Žiadne záznamy!</span> Podľa zadaných kritérii sme nenašli
                            žiadne
                            očakávané úhrady.
                        </div>
                    </td>
                </tr>
            `);
        } else {
            console.log("tu som");
        }
    })
    $("#noDataDiv").hide();
    const formData = new FormData(e.currentTarget);
    const idOfNewRow = formData.get("uhradaid");
    $("#readyPaymentsTbody").append(`<tr id="uhrada${idOfNewRow}"></tr>`);
    console.log(formData)
    htmx.ajax('POST', `/api/vysporiadanie/readyPayments`, {
        target: `#uhrada${idOfNewRow}`,
        values: {
            "suma": formData.get("suma"),
            "vs": formData.get("vs"),
            "ks": formData.get("ks"),
            "ss": formData.get("ss"),
            "ucetpartnera": formData.get("ucetpartnera"),
            "nazovpartnera": formData.get("nazov"),
            "splatnost": formData.get("splatnost"),
            "forma": formData.get("forma"),
            "dealid": formData.get("dealid"),
            "tranza": formData.get("tranza"),
            "kodobratu": formData.get("kodobratu"),
            "cub": formData.get("cub"),
            "mena": formData.get("mena"),
            "new": 0
        }
    }).then((response) => {

    });
});



$(".ocakavanaUhrada").on("click", (e) => {
    console.log(e.currentTarget.id);
    const height = e.currentTarget.clientHeight;
    const topHeight = height * parseInt(e.currentTarget.id.slice(-1))
    $(e.currentTarget).addClass("relative");
    $(e.currentTarget).animate({ top: "8rem" });
    $("#ocakavaneUhradyTBody").append(`<tr id="loadingShow" class="absolute w-full flex justify-center items-center animate-pulse text-lg font-bold bg-gray-900" style="top: ${topHeight}" colspan="6"><td class="p-1"><span>Presúvam úhradu...</span></td></tr>`)
    e.currentTarget.querySelector("#moveUhradaFormOriginalSubmit").click();

    document.addEventListener('htmx:afterSettle', function (evt) {
        $("#loadingShow").remove();
        $(e.currentTarget).remove();
    });
});

function deleteFromList(e) {
    e.currentTarget.querySelector(".deleteIcon").style.display = "none";
    e.currentTarget.querySelector(".deleteSpinner").style.display = "inline-flex";
    const formData = new FormData(e.currentTarget);
    const uhradaID = formData.get("uhradaID");
    htmx.ajax('POST', `/api/vysporiadanie/deleteUhradaFromList`, {
        target: "#toast",
        values: { "uhradaID": uhradaID }
    }).then((response) => {
        console.log(response);
        //window.location.reload();
    });
}

$(".deleteUhradaFromList").on("submit", (e) => {
    e.preventDefault();
    e.currentTarget.querySelector(".deleteIcon").style.display = "none";
    e.currentTarget.querySelector(".deleteSpinner").style.display = "inline-flex";
    const formData = new FormData(e.currentTarget);
    const uhradaID = formData.get("uhradaid");
    htmx.ajax('POST', `/api/vysporiadanie/deleteUhradaFromList`, {
        target: "#toast",
        values: { "uhradaid": uhradaID }
    });
});

document.addEventListener('htmx:afterSettle', function (evt) {
    $(".deleteUhradaFromList").on("submit", (e) => {
        e.preventDefault();
        e.currentTarget.querySelector(".deleteIcon").style.display = "none";
        e.currentTarget.querySelector(".deleteSpinner").style.display = "inline-flex";
        const formData = new FormData(e.currentTarget);
        const uhradaID = formData.get("uhradaid");
        htmx.ajax('POST', `/api/vysporiadanie/deleteUhradaFromList`, {
            target: "#toast",
            values: { "uhradaid": uhradaID }
        });
    });

    $(".zauctovatBtn").on("click", (e) => {
        e.currentTarget.querySelector("span").innerHTML = "Účtovanie...";
        $(".zauctovatBtn > svg").css("display", "block");
    });

    $(".zauctujShow").on("click", (e) => {
        e.stopPropagation();
        $(e.currentTarget).siblings(".zauctujConfirm").removeClass("hidden");
    });

    $(document).on("click", (e) => {
        if (!$(e.target).closest('.zauctujShow').length && !$(e.target).closest('.zauctujConfirm').length) {
            $(".zauctujConfirm").addClass("hidden");
        }
    });
});
function initializeMultiSelect(id) {
    let optionsArr = [];
    let optionsAll = [];
    window.addEventListener('click', function (e) {
        if (e.target.id !== "") {
            if (!e.target.id.includes(id + "MultiSelect")) {
                document.getElementById(`${id}multiselectDropdown`).style.display = 'none';
            } else {
                document.getElementById(`${id}multiselectDropdown`).style.display = 'block';
            }
        } else {
            document.getElementById(`${id}multiselectDropdown`).style.display = 'none';
        }
    });
    document.getElementById(id + "MultiSelectWrapper").addEventListener('click', function (event) {
        console.log('Element clicked:', event.target);
        let dropdown = document.getElementById(id + "multiselectDropdown");
        $(dropdown).toggle();
        document.getElementById(id + "search").focus();
    });
    document.getElementById(id + "multiselectDropdown").addEventListener('click', function (e) {
        e.stopPropagation();
    });
}

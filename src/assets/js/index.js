document.getElementById("menuToggler").addEventListener("click", () => {
    console.log(document.getElementById("mainElement"));
    document.getElementById("mainElement").style.marginLeft = "4rem";
    document.querySelectorAll(".topLevelNavigation span").forEach(function (element) {
        element.style.display = "none";
    });
});

async function appModeSwitcher(e, preserveUrl) {
    console.log(e.currentTarget);
    let url = "/";
    if (preserveUrl) {
        url = e.currentTarget.attributes[2].nodeValue;
    }
    if (e.currentTarget.querySelector(".modeSpinnner")) {
        e.currentTarget.querySelector("svg").style.display = "none";
    }
    if (e.currentTarget.querySelector(".modeSpinnner")) {
        e.currentTarget.querySelector(".modeSpinnner").style.display = "block";
    }
    if (e.currentTarget.querySelector("div")) {
        e.currentTarget.querySelector("div").innerHTML = "Prepínam...";
    }
    let mode = e.currentTarget.id;

    return htmx.ajax('POST', `/src/Controllers/stateManagement/setAppMode.php`, {
        values: {
            "mode": mode,
            "preserveUrl": preserveUrl,
            "url": url
        },
        target: "#prepinacka",
    }).then(() => {
        htmx.ajax('GET', '/', {
            target: "#pageContentMain",
        });
    });
}

async function switchToPortfolio(cislozmluvy, fondid, clientID, meno, prieznaz) {
    htmx.ajax('POST', '/api/setClientObject', {
        target: "#clientResult",
        values: {
            "cislozmluvy": cislozmluvy,
            "fondid": fondid,
            "clientID": clientID,
            "meno": meno,
            "prieznaz": prieznaz
        },
    });
}

async function runGoToPortfolio(event, cislozmluvy, fondid, clientID, meno, prieznaz, switchMode) {
    event.preventDefault();
    console.log({ cislozmluvy, fondid, clientID, meno, prieznaz });
    console.log("--- START: runGoToPortfolio function ---");
    console.log("runGoToPortfolio: Event object:", event);

    try {
        if (switchMode) {
            console.log("runGoToPortfolio: Awaiting appModeSwitcher...");
            await appModeSwitcher(event, false)
        }
        console.log("runGoToPortfolio: appModeSwitcher completed successfully.");
        console.log("runGoToPortfolio: switchToPortfolio completed successfully."); // Log B
        switchToPortfolio(cislozmluvy, fondid, clientID, meno, prieznaz).then(() => {
            setTimeout(() => {
                window.location.href = "/";
            }, 500);
        });
    } catch (error) {
        console.error("runGoToPortfolio: An ERROR occurred in the chain:", error);
    }
}
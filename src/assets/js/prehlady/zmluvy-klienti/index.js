let selectAll = document.getElementById("typeCheckboxes");
let checkboxes = Array.from(selectAll.children);

selectAll.addEventListener("click", (e) => {
    if (e.target.id === "typeCheckboxes") {
        let checkedCheckboxes = checkboxes.filter((element) => element.lastElementChild.checked);
        for (let checkbox of checkboxes) {
            if (checkedCheckboxes.length === 6) {
                removeElementClass(checkbox);
            } else {
                addElementClass(checkbox);
            }
        }
    }
});

function removeElementClass(element) {
    element.classList.remove("bg-blue-500");
    element.classList.add("bg-gray-100");
    element.classList.remove("text-white");
    element.classList.remove("shadow-lg");
    element.lastElementChild.checked = false;
}

function addElementClass(element) {
    element.classList.remove("bg-gray-100");
    element.classList.add("bg-blue-500");
    element.classList.add("text-white");
    element.classList.add("shadow-lg");
    element.lastElementChild.checked = true;
}

for (let checkbox of checkboxes) {
    checkbox.addEventListener("click", function () {
        if (checkbox.classList.contains("bg-blue-500")) {
            removeElementClass(checkbox);
        } else {
            addElementClass(checkbox);
        }
    });
}
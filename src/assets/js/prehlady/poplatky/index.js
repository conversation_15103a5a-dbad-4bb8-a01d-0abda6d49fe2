let checkboxes = document.getElementById("uhradeneCheckboxes").children;

function removeElementClass(element) {
    element.classList.remove("bg-blue-500");
    element.classList.add("bg-gray-100");
    element.classList.remove("text-white");
    element.classList.remove("shadow-lg");
    element.lastElementChild.checked = false;
}

function addElementClass(element) {
    element.classList.remove("bg-gray-100");
    element.classList.add("bg-blue-500");
    element.classList.add("text-white");
    element.classList.add("shadow-lg");
    element.lastElementChild.checked = true;
}

function checkForAllCheckboxes() {
    if (checkboxes[0].lastElementChild.checked && checkboxes[1].lastElementChild.checked && checkboxes[2].lastElementChild.checked) {
        addElementClass(checkboxes[3]);
    } else {
        removeElementClass(checkboxes[3]);
    }
}

for (let checkbox of checkboxes) {
    checkbox.addEventListener("click", function () {
        if (checkbox.id === "uhradeneAll") {
            if (checkbox.classList.contains("bg-blue-500")) {
                for (let checkbox of checkboxes) {
                    removeElementClass(checkbox);
                }
            } else {
                for (let checkbox of checkboxes) {
                    addElementClass(checkbox);
                }
            }
        } else {
            if (checkbox.classList.contains("bg-blue-500")) {
                removeElementClass(checkbox);
                removeElementClass(document.getElementById("uhradeneAll"))
            } else {
                addElementClass(checkbox);
            }
        }
        checkForAllCheckboxes();
    })
}

let switchBtn = document.getElementById("uhradeneSwitcher");
let switchWrapper = document.getElementById("overallSwitch");
let checkboxick = document.getElementById("neuhradeneCheck");
switchWrapper.addEventListener("click", () => {
    console.log("CLICK");
    let element = $(switchBtn);
    let parent = element.parent();
    let parentWidth = parent.width();
    let elementWidth = element.outerWidth(true);
    if (switchWrapper.classList.contains("switched")) {
        switchWrapper.classList.remove("switched");
        checkboxick.checked = false;
        element.animate({
            left: "0.5rem"
        }, 100);
    } else {
        switchWrapper.classList.add("switched");
        let rightPosition = parentWidth - elementWidth;
        checkboxick.checked = true;
        element.animate({
            left: rightPosition
        }, 100);
    }
});




$("#uctySearch").on("keyup", (e) => {
    if (e.currentTarget.value !== "") {
        $("#resetSearch").removeClass("hidden");
    } else {
        $("#resetSearch").addClass("hidden");
    }
    $("#searchUctyForm").submit();
});

$("#menySelect").on("change", () => {
    $("#searchUctyForm").submit();
});

$("#summary").on("change", () => {
    $("#searchUctyForm").submit();
});

$("#spravca").on("change", () => {
    $("#searchUctyForm").submit();
});

$("#date").on("change", (e) => {
    let date = new Date(e.currentTarget.value);
    $("#toDateHeading").html(date.toLocaleDateString("sk-SK"));
    $("#searchUctyForm").submit();
});

$("#scrollToTop").on("click", () => {
    document.body.scrollTop = document.documentElement.scrollTop = 0;
});

$("#resetFilter").on("click", () => {
    let today = $("#todayInput").val();
    $("#toDateHeading").html(today);
    $("#resetSearch").addClass("hidden");
    $("#searchUctyForm").trigger("reset");
    $("#searchUctyForm").submit();
});

$("#resetSearch").on("click", () => {
    $("#uctySearch").val("");
    $("#searchUctyForm").submit();
    $("#resetSearch").addClass("hidden");
});

$("#searchUctyForm").on("submit", (e) => {
    e.preventDefault();
    const searchQuery = e.currentTarget.uctySearch.value;
    const mena = e.currentTarget.mena.value;
    const summary = e.currentTarget.summary.checked;
    const spravca = e.currentTarget.spravca.checked;
    const date = e.currentTarget.date.value;
    const link = e.currentTarget.link.value;
    if (
        searchQuery === "" &&
        mena === "" &&
        summary === false &&
        spravca === false &&
        date === ""
    ) {
        $("#resetFilter").addClass("hidden");
    } else {
        $("#resetFilter").removeClass("hidden");
    }
    if (searchQuery !== "") {
        searchEntered = true;
    }
    htmx.ajax('POST', `/src/Controllers/prehlady/ucty/${link}/find.php`, {
        target: '#uctiky',
        values: {
            "query": searchQuery,
            "mena": mena,
            "summary": summary,
            "date": date,
            "spravca": spravca
        }
    });
});
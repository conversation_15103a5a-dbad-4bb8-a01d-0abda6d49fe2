function initializeMultiSelect(id) {
    let optionsArr = [];
    let optionsAll = [];
    let secondParamArr = [];
    window?.addEventListener('click', function (e) {
        if (e.target.id !== "") {
            if (!e.target.id.includes(id + "MultiSelect")) {
                document.getElementById(`${id}multiselectDropdown`).style.display = 'none';
            } else {
                document.getElementById(`${id}multiselectDropdown`).style.display = 'block';
            }
        } else {
            document.getElementById(`${id}multiselectDropdown`).style.display = 'none';
        }
    });
    document.getElementById(id + "MultiSelectWrapper")?.addEventListener('click', function (event) {
        console.log('Element clicked:', event.target);
        let dropdown = document.getElementById(id + "multiselectDropdown");
        $(dropdown).toggle();
        document.getElementById(id + "search").focus();
    });
    document.getElementById(id + "multiselectDropdown")?.addEventListener('click', function (e) {
        e.stopPropagation();
    });
    document.getElementById(id + "clearSearch")?.addEventListener('click', function () {
        document.getElementById(id + "search").value = "";
        document.getElementById(id + "clearSearch").style.display = "none";
        for (let element of document.getElementsByClassName(id + "MultiSelectSpan")) {
            element.style.display = "block";
        }
    });
    document.getElementById(id + "search")?.addEventListener('keyup', function (e) {
        let searchError = document.getElementById(id + "MultiSelectSearchError");

        if (e.target.value !== "") {
            document.getElementById(id + "clearSearch").style.display = "block";
        } else {
            document.getElementById(id + "clearSearch").style.display = "none";
        }

        let spans = $("." + id + "MultiSelectSpan");
        let selectedSpans = document.getElementsByClassName("spanko");
        for (let element of spans) {
            element.style.display = "none";
        }
        let selected = [];

        for (let i = 0; i < selectedSpans.length; i++) {
            selected.push(selectedSpans[i].id);
        }
        console.log(spans)
        let matchingSpan = spans.filter(function () {
            if ($(this).attr('id').includes(e.target.value)) {
                return $(this).attr('id').includes(e.target.value);
            }
            if ($(this).attr('data-fondid').includes(e.target.value)) {
                return $(this).attr('data-fondid').includes(e.target.value);
            }
        });

        matchingSpan.each(function (number, element) {
            selected.forEach((item, index) => {
                if (selected[index].includes(matchingSpan[number]?.id)) {
                    matchingSpan.splice(number, 1);
                }
            });
        });

        if (matchingSpan.length !== 0) {
            searchError.style.display = "none";
            for (let element of matchingSpan) {
                element.style.display = "block";
            }
        } else {
            searchError.innerText = "Žiadne výsledky...";
            searchError.style.display = "block";
        }
    });

    document.getElementById(id + "MultiSelectAll")?.addEventListener('click', function () {
        document.getElementById(id + "MultiSelectPickerList").innerHTML = "";
        document.getElementById(id + "MultiSelectValues").value = "";
        $("#" + id + "MultiSelectValues").trigger("change");
        document.getElementById(id + "MultiSelectResetAllButton").style.display = "block";
        document.getElementById(id + "MultiSelectPickerInitial").style.display = "none";
        let options = document.querySelectorAll("." + id + "MultiSelectSpan");
        options.forEach((item) => {
            console.log(item)
            optionsAll.push(item["attributes"]["data-fondid"].nodeValue);
        });
        console.log(optionsAll);
        document.getElementById(id + "MultiSelectValues").value = optionsAll.toString();
        $("#" + id + "MultiSelectValues").trigger("change");
        let div = document.createElement("div");
        div.innerText = `Boli vybrané všetky možnosti (${options.length})`;
        document.getElementById(id + "MultiSelectPickerList").append(div);
        document.getElementById(id.toString() + "multiselectDropdown").style.display = "none";
    });

    document.querySelectorAll("." + id + "MultiSelectSpan").forEach(function (item) {
        item?.addEventListener('click', function () {
            document.getElementById(id + "MultiSelectResetAllButton").style.display = "block";
            let optionId = item.getAttribute("id");
            let fondid = item.getAttribute("data-fondid");
            let second = item.getAttribute("data-second");
            console.log("MENA: " + second);
            document.getElementById(id + "MultiSelectPickerInitial").style.display = "none";
            if (optionsAll.length !== 0) {
                optionsAll = [];
                optionsArr = [];
                document.getElementById(id + "MultiSelectPickerList").innerHTML = "";
            }
            let span = document.createElement("span");
            span.className = `text-xs font-medium me-2 px-2.5 py-0.5 hover:bg-red-500 hover:text-black transition-all ${id}spanko rounded bg-gray-700 text-gray-300`;
            span.id = `option-${optionId}`;
            span.setAttribute("data-fondid", fondid);
            span.innerHTML = optionId;
            span?.addEventListener('click', function () {
                const index = optionsArr.indexOf(span.id);
                optionsArr.splice(index, 1);
                let currentValue = document.getElementById(id + "MultiSelectValues").value;

                function removeNumber(string, number) {
                    let array = string.split(',');
                    console.log(array)
                    array = array.filter(item => item.trim() !== number);
                    return array.join(', ');
                }

                document.getElementById(id + "MultiSelectValues").value = removeNumber(currentValue, span.attributes["data-fondid"].nodeValue);
                $("#" + id + "MultiSelectValues").trigger("change");
                span.remove();
                if (optionsArr.length === 0) {
                    document.getElementById(id.toString() + "MultiSelectPickerInitial").style.display = "block";
                }
                document.getElementById(id.toString() + "multiselectDropdown").style.display = "none";
                if (optionsArr.length === 0) {
                    document.getElementById(id.toString() + "MultiSelectResetAllButton").style.display = "none";
                }
            });
            document.getElementById(id + "MultiSelectPickerList").append(span);
            optionsArr.push(fondid);
            secondParamArr.push(second);
            document.getElementById(id + "MultiSelectValues").value = optionsArr;
            document.getElementById(id + "SecondParameter").value = secondParamArr;
            $("#" + id + "MultiSelectValues").trigger("change");
            item.style.display = "none";
        });
    });

    document.getElementById(id + "MultiSelectResetAllButton")?.addEventListener('click', function () {
        optionsAll = [];
        optionsArr = [];
        document.getElementById(id + "MultiSelectValues").value = "";
        document.getElementById(id + "SecondParameter").value = "";
        $("#" + id + "MultiSelectValues").trigger("change");
        document.getElementById(id + "MultiSelectPickerInitial").style.display = "block";
        document.getElementById(id + "multiselectDropdown").style.display = "none";
        document.getElementById(id + "MultiSelectPickerList").innerHTML = "";
        document.getElementById(id + "MultiSelectResetAllButton").style.display = "none";
    });
}
document.querySelectorAll(".protistranaSelect").forEach((item) => {
    item.addEventListener("change", (e) => {
        let id = e.target.id[e.target.id.length - 1];
        let checkbox = document.getElementById('checkbox-table' + id);
        if (e.target.value === "") {
            checkbox.checked = false;
            checkbox.disabled = true;
            checkbox.style.border = "solid 1px #D1D5DB";
        } else {
            checkbox.disabled = false;
            checkbox.style.border = "solid 1px #1A56DB";
        }
        checkCheckboxes();
    });
});

function checkCheckboxes() {
    document.querySelectorAll(".checkboxStrana").forEach((item) => {
        console.log(item.disabled);
        if (item.disabled === false) {
            document.getElementById("checkbox-all-search").disabled = false;
            document.getElementById("checkbox-all-search").style.border = "solid 1px #1A56DB";
        }
    });
}

document.getElementById("checkbox-all-search").addEventListener("change", (e) => {
    if (e.target.checked) {
        document.querySelectorAll(".checkboxStrana").forEach((item) => {
            if (item.disabled === false) {
                item.checked = true;
            }
        });
    } else {
        document.querySelectorAll(".checkboxStrana").forEach((item) => {
            item.checked = false;
            item.disabled = true;
            item.style.border = "solid 1px #D1D5DB";
        });
        e.target.disabled = true;
        e.target.style.border = "solid 1px #D1D5DB";
        document.querySelectorAll(".protistranaSelect").forEach((item) => {
            item.getElementsByTagName('option')[0].selected = true;
        });
    }
});
document.querySelector(".mifirForm").addEventListener("submit", (e) => {
    e.preventDefault();
    let url = "/src/Controllers/prehlady/nbs-gfi/XMLExports/mifirXML.php";
    document.querySelector(".excelIcon").style.display = "none";
    document.getElementById("spinnerExcel").style.display = "block";
    const formData = new FormData(e.currentTarget);
    let leiCodes = [];
    document.querySelectorAll(".protistranaSelect").forEach((item) => {
        console.log(item.value)
        if (item.value !== "") {
            leiCodes.push(item.value);
        }
    });
    let dealids = [];
    document.querySelectorAll(".checkboxStrana").forEach((item) => {
        console.log(item.value)
        if (item.checked === true) {
            dealids.push(item.value);
        }
    });
    let date = e.currentTarget.date.value;
    console.log(JSON.stringify({leiCodes, date, dealids}));
    $.ajax({
        url: url,
        method: "POST",
        data: JSON.stringify({leiCodes, date, dealids}),
        processData: false,
        contentType: false,
        success: function (data) {
            console.log(data);
            document.querySelector(".excelIcon").style.display = "block";
            document.getElementById("spinnerExcel").style.display = "none";
            fetch(data.link.replace("/home/<USER>/www", ""))
                .then(response => response.blob())
                .then(blob => {
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = data.link.replace("/home/<USER>/www", "");
                    a.click();
                    URL.revokeObjectURL(url);
                })
                .catch(error => {
                    alert('File download failed:' + error);
                    console.error('File download failed:', error);
                });
        },
        error: function (xhr, status, error) {
            console.error("Error:", error);
        },
    });
});

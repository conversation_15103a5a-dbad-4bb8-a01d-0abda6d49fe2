if (typeof selectAll !== 'undefined') {
    selectAll = document.getElementById("typeCheckboxes");
} else {
    let selectAll = document.getElementById("typeCheckboxes");
}

let statistikaCheckboxesWrapper = document.getElementById("statistikaTypeCheckboxes");
let checkboxes = Array.from(selectAll.children);
let statistikaCheckboxes = Array.from(statistikaCheckboxesWrapper.children);

function removeElementClass(element) {
    element.classList.remove("bg-blue-500");
    element.classList.add("bg-white");
    element.classList.remove("text-white");
    element.classList.remove("shadow-lg");
    element.lastElementChild.checked = false;
}

function addElementClass(element) {
    element.classList.remove("bg-white");
    element.classList.add("bg-blue-500");
    element.classList.add("text-white");
    element.classList.add("shadow-lg");
    element.lastElementChild.checked = true;
}

for (let checkbox of checkboxes) {
    checkbox.addEventListener("click", function () {
        for (let checkbox of checkboxes) {
            if (checkbox.classList.contains("bg-blue-500")) {
                removeElementClass(checkbox);
            }
        }
        addElementClass(checkbox);
    });
}

for (let checkbox of statistikaCheckboxes) {
    checkbox.addEventListener("click", function () {
        for (let checkbox of statistikaCheckboxes) {
            if (checkbox.classList.contains("bg-blue-500")) {
                removeElementClass(checkbox);
            }
        }
        addElementClass(checkbox);
    });
}
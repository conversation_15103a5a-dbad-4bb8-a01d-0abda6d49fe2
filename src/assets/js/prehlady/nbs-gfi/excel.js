document.querySelectorAll(".excelForm").forEach((item) => {
    item.addEventListener("submit", (e) => {
        e.preventDefault();
        let url;
        switch (e.target.id) {
            case "":
                url = "/src/Controllers/prehlady/nbs-gfi/XLSXExports/exportNBSReportXLSX.php";
                break;
            case "exportToExcelGFI":
                url = "/src/Controllers/prehlady/nbs-gfi/XLSXExports/exportGFItoXLSX.php";
                break;
            case "exportZmluvyKlienti":
                url = "/src/Controllers/prehlady/zmluvy-klienti/exportToXLSX.php";
                break;
            default:
                url = "/src/Controllers/prehlady/nbs-gfi/XLSXExports/exportDataToXLSX.php";
        }
        document.getElementById("excelIcon").style.display = "none";
        document.getElementById("spinnerExcel").style.display = "block";
        const formData = new FormData(e.currentTarget);
        $.ajax({
            url: url,
            method: "POST",
            data: formData,
            processData: false,
            contentType: false,
            success: function (data) {
                console.log(data);
                document.getElementById("excelIcon").style.display = "block";
                document.getElementById("spinnerExcel").style.display = "none";
                fetch(data.link.replace("/home/<USER>/www", ""))
                    .then(response => response.blob())
                    .then(blob => {
                        const url = URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = data.link.replace("/home/<USER>/www", "");
                        a.click();
                        URL.revokeObjectURL(url);
                    })
                    .catch(error => {
                        alert('File download failed:' + error);
                        console.error('File download failed:', error);
                    });
            },
            error: function (xhr, status, error) {
                console.error("Error:", error);
            },
        });
    });
});
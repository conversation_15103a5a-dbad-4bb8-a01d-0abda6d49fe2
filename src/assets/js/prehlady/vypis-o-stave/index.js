$("#submitOne").on("click", () => {
    $("#submitOne").innerHTML("Generujem výpis...");
});

document.getElementById("pdfAfterGenerate").addEventListener("submit", (e) => {
    e.preventDefault();
    document.getElementById("afterPDFIcon").style.display = "none";
    document.getElementById("spinnerPDF").style.display = "block";
    const formData = new FormData(e.currentTarget);
    $.ajax({
        url: `/src/Views/prehlady/vypis_o_stave_majetku/createAndSavePDF.php`,
        method: "POST",
        data: formData,
        processData: false,
        contentType: false,
        success: function (data) {
            console.log(data);
            document.getElementById("afterPDFIcon").style.display = "block";
            document.getElementById("spinnerPDF").style.display = "none";
            document.getElementById("generatePDFAfter").style.background = "#0E9F6E";
            document.getElementById("generatePDFAfter").style.color = "white";
            for (const property in data) {
                if (data[property] !== null) {
                    fetch(data[property].replace("/home/<USER>/www", ""))
                        .then(response => response.blob())
                        .then(blob => {
                            const url = URL.createObjectURL(blob);
                            const a = document.createElement('a');
                            a.href = url;
                            a.download = data[property].replace("/home/<USER>/www/src/assets/pdf/vypisy/", "");
                            a.click();
                            URL.revokeObjectURL(url);
                        })
                        .catch(error => {
                            console.error('File download failed:', error);
                        });
                }
            }
        },
        error: function (xhr, status, error) {
            console.error("Error:", error);
        },
    });
});
document.getElementById("exportToExcelTrigger").addEventListener("click", () => {
    document.getElementById("excelIcon").style.display = "none";
    document.getElementById("spinnerExcel").style.display = "block";
});
let getFields = document.getElementById("getFields");
let gettasks = document.getElementById("getTasks");
let uploadForm = document.getElementById("uploadToDB");
const table = document.getElementById("resultBody");
const tableTasks = document.getElementById("tasksBody");
let globalCities = [];

const citiesList = '900502402944';

let fields = {};
let tasksIDs = [];
let allTasks = [];
let allFields = [];
const apiKey = "pk_6761314_CYI9PD1DUQ6MRM6LF8661M3A5JQ2H602";
//pk_56635946_WBVSQGMHN9L9DGZUUWGVVD8QRADQLRST
const klientCustomFieldsToUpdate = {
    0: {
        dbFieldName: "kontaktemail",
        fieldID: '0bbde94f-ac06-4386-91a9-b4a5910284b4'
    },
    1: {
        dbFieldName: "kontaktphonenumber",
        fieldID: "ddad83e5-d87d-4754-9e9e-4490d1f37187"
    },
    2: {
        dbFieldName: "address",
        fieldID: "3908d1be-6049-4939-b7d2-d61b84ade814"
    },
    3: {
        dbFieldName: "paf",
        fieldID: "955908ea-d50b-4efc-a86e-c3177041602c"
    },
    4: {
        dbFieldName: "aum_sf",
        fieldID: "35ac5423-8786-4064-b308-68a7a903baaa"
    },
    5: {
        dbFieldName: "aum_pf",
        fieldID: "2b8bc5ad-d8ac-4e61-a81b-8a49d09299d5"
    },
    6: {
        dbFieldName: "aum_total",
        fieldID: "b3628a73-388a-43a2-bcd2-c0c8d8eaa660"
    },
    7: {
        dbFieldName: "datum",
        fieldID: "1269077d-4e8f-47a4-adc9-c71cffd26acf"
    },
    8: {
        dbFieldName: "city",
        fieldID: "5140ba2a-aa78-4079-925d-ed84c41f2a33"
    }
}
const pozicieCustomFieldsToUpdate = [
    {
        dbFieldName: "datum",
        fieldID: '57aba433-147f-43d7-bba4-cad32d45f4ce'
    },
    {
        dbFieldName: "pocet",
        fieldID: "a65279f8-869d-477a-be4e-228220e5578e"
    },
    {
        dbFieldName: "trhovyobjem",
        fieldID: "42f02748-2ca9-459e-85bb-dbdd0ed412ff"
    },
    {
        dbFieldName: "objemref",
        fieldID: "4db09b80-9514-406b-b918-3fd14f61a902"
    },
    {
        dbFieldName: "datum",
        fieldID: 'dbf6bcf3-a9a3-471a-b587-d0ece25bd089'
    },
    {
        dbFieldName: "trhovacena",
        fieldID: '22dbddcb-e9ba-4dc4-b7c8-49a9eb22480d'
    },
    {
        dbFieldName: "meno",
        fieldID: 'caf4a778-79b2-48d9-94fd-fdf66f92e57e'
    }
]

async function getCities() {
    if (document.querySelector("#status-current-item") !== null) {
        document.querySelector("#status-current-item").innerHTML = "Sťahujem aktuálny zoznam miest z Clickupu...";
    }
    const fields = await fetch(
        `/src/Controllers/clickup/getAllTasksFromList.php?list=${citiesList}`,
        {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            }
        }
    );
    let cities = await fields.json();
    cities = JSON.parse(cities);
    return cities.tasks;
}

async function createCity(city) {
    document.querySelector("#status-current-item").innerHTML = "Vytváram nové mesto v Clickupe...";
    const resp = await fetch(
        `/src/Controllers/clickup/createTask.php`,
        {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                list: citiesList,
                name: city,
                description: 'Mesto',
                status: "TO DO"
            })
        }
    );
    return await resp.json();
}

async function checkCity(clientCity, clientCities) {
    document.querySelector("#status-current-item").innerHTML = "Prebieha kontrola mesta klienta...";
    let citiesToDelete = [];
    if (clientCities[0] !== undefined) {
        for (let z = 0; z < clientCities[0].length; z++) {
            if (clientCity !== clientCities[0][z].name) {
                //@ts-ignore
                citiesToDelete.push(clientCities[0][z].id);
            }
        }
    }
    let cities = globalCities;
    for (let i = 0; i < cities.length; i++) {
        if (cities[i].name === clientCity) {
            return {
                add: [cities[i].id],
                rem: citiesToDelete
            }
        }
    }
    let cityToUpdate = await createCity(clientCity)
    cityToUpdate = JSON.parse(cityToUpdate)
    globalCities = await getCities();
    return {
        add: [cityToUpdate.id],
        rem: citiesToDelete
    };
}

async function getDataFromDB() {
    document.querySelector("#status-current-item").innerHTML = "Sťahujem potrebné dáta na aktualizáciu...";
    const poziciePodielnici = await fetch('/src/Controllers/clickup/getFromClickupDatabase.php', {
        method: 'GET',
    });
    return await poziciePodielnici.json();
}

async function getLastUpdateDateFromDB(table) {
    const lastDate = await fetch(`/src/Controllers/clickup/getLastUpdateDate.php?table=${table}`, {
        method: 'GET',
    });
    return await lastDate.json();
}

async function createClientOption(label, listID, current) {
    let options
    [] = [];
    for (let i = 0; i < current.type_config.options.length; i++) {
        options.push(current.type_config.options[i]);
    }

    options.push({
        "color": null,
        "label": label
    });

    let bodyPayload = {
        "date_created": current.date_created,
        "date_deleted": null,
        "default_value": null,
        "deleted": false,
        "description": "",
        "hide_from_guests": current.hide_from_guests,
        "id": current.id,
        "name": current.name,
        "pinned": false,
        "required": current.required,
        "required_on_subtasks": false,
        "subcategories": [
            {
                "archived": false,
                "entity": "subcategory",
                "id": listID.toString(),
                "name": "Klienti - Portfóliá",
                "personal_list": null,
                "subcategory_access": true
            }
        ],
        "team_id": "763867",
        "type": "labels",
        "type_config": {
            "options": options
        },
        "userid": "6761314"
    }

    let create = await fetch(`/src/Controllers/clickup/createClientOption.php`, {
        method: "POST",
        body: JSON.stringify({
            fieldId: current.id,
            bodyPayload: JSON.stringify(bodyPayload)
        })
    })
    let created = await create.json();
    console.log(created.ok);
    console.log("CUSTOM OPTION CREATED");
}

async function updateTask(client, listID, task) {
    console.log("UPDATE TASKK", client);
    if (client === undefined) {
        return;
    }
    let taskID = task.id;
    let cities = getCustomFieldID(task.custom_fields, "Mesto (všetky)");
    if (cities === undefined) {
        cities = [];
    }
    console.log(listID, "listID");
    if (listID === 156440911) {
        document.querySelector("#status-step").innerHTML = "Aktualizujem pozície..."
        document.querySelector("#status-current-item").innerHTML = "Aktualizujem pozíciu s identifikátorom: " + task.id;
        for (let y = 0; y < pozicieCustomFieldsToUpdate.length; y++) {
            let fieldName = pozicieCustomFieldsToUpdate[y].dbFieldName;
            let fieldIDcko = pozicieCustomFieldsToUpdate[y].fieldID;
            let valueToInsert;
            if (fieldName === "datum") {
                valueToInsert = Date.parse(client.datum);
            } else if (fieldName === "meno") {
            } else {
                valueToInsert = client[fieldName]
            }
            const resp = await fetch(
                `/src/Controllers/clickup/setCustomField.php`,
                {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        taskID: taskID,
                        fieldID: fieldIDcko,
                        payload: valueToInsert
                    })
                }
            );
            const data = await resp.json();
            console.log(data, "CUSTOM FIELD pozicie SET?");
        }
        const resp = await fetch(
            `/src/Controllers/clickup/updateTask.php`,
            {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    taskID: taskID,
                    name: client.nazovaktiva,
                    description: client.nazovaktiva,
                })
            }
        );
        const data = await resp.json();
        console.log(data, "UPDATE TASK POZICIE??");
        console.log("ČAKAM 1,8 SEKUNDY")
        await new Promise(resolve => setTimeout(resolve, 1800));
        console.log("1,8 SEKUNDY PREŠLI");
        return;
    }
    document.querySelector("#status-step").innerHTML = "Aktualizujem klientov..."
    document.querySelector("#status-current-item").innerHTML = "Aktualizujem klienta s identifikátorom: " + task.id;
    for (let y = 0; y < 9; y++) {
        let fieldName = klientCustomFieldsToUpdate[y].dbFieldName;
        let fieldIDcko = klientCustomFieldsToUpdate[y].fieldID;
        let valueToInsert;
        if (client.aum_pf > 0 && fieldName === 'paf') {
            valueToInsert = true
        } else if (fieldName === "datum") {
            valueToInsert = Date.parse(client.datum);
        } else if (fieldName === 'kontaktphonenumber') {
            valueToInsert = ''
        } else if (fieldName === 'city') {
            valueToInsert = await checkCity(client.city, [cities]);
        } else {
            valueToInsert = client[fieldName]
        }
        console.log(valueToInsert);
        const resp = await fetch(
            `/src/Controllers/clickup/setCustomField.php`,
            {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    taskID: taskID,
                    fieldID: fieldIDcko,
                    payload: valueToInsert
                })
            }
        );
        const data = await resp.json();
        console.log(data);
    }
    await new Promise(resolve => setTimeout(resolve, 1800));
}

async function getTasks(listID) {
    allTasks = [];
    document.querySelector("#status-step").innerHTML = "Sťahujem všetky tasky z listu " + listID
    let page = 0;
    let taskData;
    let arrayOfTasks = [];
    while (true) {
        const tasks = await fetch(
            `/src/Controllers/clickup/getAllTasksFromList.php?list=${listID}&page=${page}`,
            {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            }
        );
        taskData = await tasks.json();
        taskData = JSON.parse(taskData);
        //@ts-ignore
        arrayOfTasks.push(...taskData.tasks)
        if (taskData.last_page === true) {
            break;
        }
        page++;
    }
    //@ts-ignore
    allTasks.push({
        listID: listID,
        tasks: arrayOfTasks
    });
    console.log(allTasks);
    return allTasks;
}

function handleCustomFields(client) {
    let aktivum, mena;
    let nazovcp;
    let clientAktivum = client.aktivum.trim();
    if (clientAktivum === "Pohľadávka/Záväzok") {
        clientAktivum = "Pohľ/Záv"
    }
    console.log(client);
    let aktivumCustomField = getCustomFieldID(allFields[1].fields, "Aktívum");
    let menaCustomField = getCustomFieldID(allFields[1].fields, "Mena");
    let nazovcpCustomField = getCustomFieldID(allFields[1].fields, "Cenný papier");

    if (clientAktivum !== null) {
        console.log(clientAktivum);
        console.log(aktivumCustomField);
        aktivum = aktivumCustomField.options.find((element) => element.name === clientAktivum);
        aktivum = aktivum ? aktivum.id : null;
    }
    if (client.menaref !== null) {
        mena = menaCustomField.options.find((element) => element.name === client.menaref.trim());
        mena = mena ? mena.id : null;
    }
    if (client.nazovcp !== null) {
        nazovcp = nazovcpCustomField.options.find((element) => element.label === client.nazovcp.trim());
        nazovcp = nazovcp ? nazovcp.id : null;
    }

    return {
        aktivum: aktivum,
        mena: mena,
        nazovcp: [nazovcp]
    }
}

async function createAndGetOptionToInsert(meno, listID, klientiCustomField) {
    await createClientOption(meno, listID, klientiCustomField);
    let customFi = await getCustomFieldsFromList(listID);
    let customFieldObject = customFi.find((element) => element.name === "Klienti (label)");
    let clientObject = customFieldObject.type_config.options.find((element) => element.label === meno);
    if (clientObject !== undefined) {
        return [clientObject.id];
    } else {
        return undefined
    }
}

/**
 * @description This function creates task based on given parameters.
 * @param client
 * @param listID
 * @param idsf
 * @param clients
 * @param allClients
 */
async function createTask(client, listID, idsf, clients, allClients) {
    if (listID === 156440911) {
        let fieldsToUpdate;
        let c_fields = handleCustomFields(client);
        for (let i = 0; i < clients.length; i++) {
            if (client.klientid === parseInt(clients[i]) && client.klientid === parseInt(getCustomFieldID(allClients[0].tasks[i].custom_fields, "ID SF"))) {
                document.querySelector("#status-step").innerHTML = "Vytváram novú pozíciu v Clickupe..."
                fieldsToUpdate = [
                    {id: pozicieCustomFieldsToUpdate[0].fieldID, value: Date.parse(client.datum)},
                    {id: pozicieCustomFieldsToUpdate[1].fieldID, value: client.pocet},
                    {id: pozicieCustomFieldsToUpdate[2].fieldID, value: client.trhovyobjem},
                    {id: pozicieCustomFieldsToUpdate[3].fieldID, value: client.objemref},
                    {id: pozicieCustomFieldsToUpdate[4].fieldID, value: Date.parse(client.datum)},
                    {id: pozicieCustomFieldsToUpdate[5].fieldID, value: client.trhovacena},
                    {id: '8e5cf86b-65ec-47e9-9b66-af315c78cd6c', value: client.meno},
                    {
                        id: "3de42d4a-8031-487e-99c9-7adae2406db3", value: {
                            add: [allClients[0].tasks[i].id],
                            rem: []
                        }
                    },
                    {id: "5538a700-b5d8-444b-9537-50a33892e434", value: client.isin}
                ]
                if (c_fields.aktivum !== undefined) {
                    fieldsToUpdate.push({id: "e4e797c9-1811-47e2-ade6-09c82734d678", value: c_fields.aktivum});
                }
                if (c_fields.mena !== undefined) {
                    fieldsToUpdate.push({id: "753a7a68-729e-4e69-903e-134c41ae6e88", value: c_fields.mena});
                }
                if (c_fields.nazovcp[0] !== "" && c_fields.nazovcp[0] !== undefined && c_fields.nazovcp[0] !== null) {
                    fieldsToUpdate.push({id: "5ce6989c-9ae0-4186-b048-5aad21063841", value: c_fields.nazovcp});
                }
                const resp = await fetch(
                    `/src/Controllers/clickup/createTask.php`,
                    {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            list: listID,
                            name: client.nazovaktiva,
                            description: client.nazovaktiva,
                            status: 'TO DO',
                            check_required_custom_fields: true,
                            custom_fields: fieldsToUpdate
                        })
                    }
                );
                const data = await resp.json();
                console.log(data, "POZICIA cant stop now TASK");
                return;
            }
        }
        return;
    }
    document.querySelector("#status-step").innerHTML = "Vytváram nového klienta v Clickupe..."
    const resp = await fetch(
        `/src/Controllers/clickup/createTask.php`,
        {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                list: listID,
                name: client.meno ? client.meno + " " + client.prieznaz : client.prieznaz,
                description: client.meno ? client.meno + " " + client.prieznaz : client.prieznaz,
                status: 'KLIENT',
                check_required_custom_fields: true,
                custom_fields: [
                    {id: klientCustomFieldsToUpdate[0].fieldID, value: client.email},
                    {id: klientCustomFieldsToUpdate[1].fieldID, value: client.phonenumber},
                    {id: klientCustomFieldsToUpdate[2].fieldID, value: client.address},
                    {id: klientCustomFieldsToUpdate[3].fieldID, value: client.aum_pf},
                    {id: klientCustomFieldsToUpdate[4].fieldID, value: client.aum_sf},
                    {id: klientCustomFieldsToUpdate[5].fieldID, value: client.aum_pf > 0 ? true : null},
                    {id: klientCustomFieldsToUpdate[6].fieldID, value: client.aum_total},
                    {id: klientCustomFieldsToUpdate[7].fieldID, value: Date.parse(client.datum)},
                    {id: klientCustomFieldsToUpdate[8].fieldID, value: await checkCity(client.city, [])},
                    {id: 'd6f9c1f0-19d8-4dc4-8ee7-374332fe273e', value: idsf}
                ]
            })
        }
    );
    const data = await resp.json();
    console.log(data, "KLIENT TAASK ");
}

async function deleteTask(taskId) {
    const resp = await fetch(`https://api.clickup.com/api/v2/task/${taskId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                Authorization: apiKey
            }
        }
    );
    const data = await resp.text();
    console.log(data);
}

function findMissingElements(arr1, arr2) {
    const set1 = new Set(arr1);
    console.log(set1.size);
    const set2 = new Set(arr2);
    console.log(set2.size);
    return [...set1].filter(element => !set2.has(element));
}


function getCustomFieldID(custom_fields, fieldName) {
    let customField = custom_fields.find((element) => element.name === fieldName);
    return customField === undefined ? "" : customField.value === undefined ? customField.type_config : customField.value;
}

async function getCustomFieldsFromList(listID) {
    const fields = await fetch(`/src/Controllers/clickup/getCustomFields.php?list=${listID}`, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
        }
    });
    let data = await fields.json();
    data = JSON.parse(data)
    console.log(data);
    return data.fields;
}

async function deleteMultipleTasks(tasks, lastUpdateDateDB, customFieldName) {
    for (let d = 0; d < tasks.length; d++) {
        const task = tasks[d];
        console.log(task.status.status);
        const lastUpdateDate = getCustomFieldID(task.custom_fields, customFieldName);
        if (lastUpdateDate !== undefined ? lastUpdateDateDB !== new Date(parseInt(lastUpdateDate)).toISOString().split('T')[0] ? true : false : true) {
            await deleteTask(task.id);
        }
    }
}

async function runAutomaticUpdate() {
    document.querySelector("#status-step").innerHTML = "Spúšťam operáciu aktualizácie clickupu..."
    document.getElementById("loading-contianer").style.display = "block";
    gettasks.style.display = "none";
    let listIDs = [162517514, 156440911];
    let [data, allPositions, globalCities] = await Promise.all([
        getDataFromDB(),
        getTasks(listIDs[0]),
        getTasks(listIDs[1]),
        getCities()
    ])
    let podielnici = data.podielnici;
    let pozicie = data.pozicie;
    let clients = [];

    for (let y = 0; y < listIDs.length; y++) {
        const fields = await fetch(
            `/src/Controllers/clickup/getCustomFields.php?list=${listIDs[y]}`,
            {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            }
        );
        let data = await fields.json();
        data = JSON.parse(data)
        console.log(data);
        let index = listIDs[y];
        allFields[y] = {
            listID: index,
            fields: data.fields
        }
    }
    console.log(allFields);

    for (let y = 0; y < allPositions[0].tasks.length; y++) {
        //@ts-ignore
        clients.push(getCustomFieldID(allPositions[0].tasks[y].custom_fields, "ID SF"));
    }

    console.log(clients, "CLIENTS");
    console.log(pozicie)
    console.log(allPositions)
    console.log(podielnici);
    console.log(pozicie)

    // UPDATE OR CREATE clients in app.clickup.com 
    for (let c = 0; c < podielnici.length; c++) {
        const podielnik = podielnici[c];
        const polarisid = podielnici[c].polarisid ?? 0;
        const goldmannid = podielnici[c].goldmannid ?? 0;
        const client = allPositions[0].tasks.find((element) => parseInt(getCustomFieldID(element.custom_fields, "ID SF")) === polarisid || parseInt(getCustomFieldID(element.custom_fields, "ID SF")) === goldmannid);
        console.log("POLARISID: " + polarisid + "  GOLDMANNID: " + goldmannid);
        console.log(getCustomFieldID(allPositions[0].tasks.find((element) => parseInt(getCustomFieldID(element.custom_fields, "ID SF")) !== polarisid && parseInt(getCustomFieldID(element.custom_fields, "ID SF")) !== goldmannid).custom_fields, "ID SF"));
        console.log(podielnik);
        if (client) {
            await updateTask(podielnik, listIDs[0], client);
        } else {
            await createTask(podielnik, listIDs[0], polarisid === 0 ? goldmannid : polarisid, clients, allPositions[0]);
        }
    }

    let allClients = await getTasks(listIDs[0]);
    let lastUpdateDateClient = await getLastUpdateDateFromDB("podielnik");
    lastUpdateDateClient = lastUpdateDateClient.datum[0].datum;

    for (let p = 0; p < pozicie.length; p++) {
        const dbItem = pozicie[p];
        const task = allPositions[1].tasks.find((element) => getCustomFieldID(element.custom_fields, "ID pozicie Goldmann") === dbItem.idpozicie);
        console.log(allPositions);
        let updatedFields = await getCustomFieldsFromList(listIDs[1]);
        let klientiCustomField = updatedFields.find((element) => element.name === "Klienti (label)");
        if (task) {
            //await updateTask(dbItem, listIDs[1], task, klientiCustomField);
        } else {
            await createTask(dbItem, listIDs[1], dbItem.idpozicie, clients, allClients);
        }
        await new Promise(resolve => setTimeout(resolve, 500));
    }

    allPositions = await getTasks(listIDs[1]);
    let lastUpdateDateDB = await getLastUpdateDateFromDB("pozicie");
    lastUpdateDateDB = lastUpdateDateDB.datum[0].datum;

    //await deleteMultipleTasks(allPositions[0].tasks, lastUpdateDateDB, "Aktualizácia");

    document.getElementById("loading-contianer").style.display = "none";
    document.getElementById("confirm-contianer").style.display = "block";
}

//@ts-ignore
gettasks.addEventListener("submit", (e) => {
    e.preventDefault();
    runAutomaticUpdate();
})
//@ts-ignore
getFields.addEventListener("submit", async (e) => {
    e.preventDefault();
    document.querySelector("#status-step").innerHTML = "Spúšťam operáciu aktualizácie clickupu..."
    document.getElementById("loading-contianer").style.display = "block";
    let data = await getDataFromDB();
    let podielnici = data.podielnici;
    let pozicie = data.pozicie;
    let allClients, allPositions;
    document.getElementById("alert").innerHTML = "";
    const lists = document.getElementsByClassName('list-check');
    let listIDs = [];
    for (let i = 0; i < lists.length; i++) {
        if (lists[i].checked === true) {
            listIDs.push(parseInt(lists[i].value))
        }
    }
    if (listIDs.length === 0) {
        document.getElementById("alert").innerHTML = "Pre pokračovanie musíš zaškrtnúť jednu z možností!";
        return;
    }

    for (let y = 0; y < listIDs.length; y++) {
        const fields = await fetch(
            `/src/Controllers/clickup/getCustomFields.php?list=${listIDs[y]}`,
            {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            }
        );
        let data = await fields.json();
        data = JSON.parse(data)
        console.log(data);
        let index = listIDs[y];
        allFields[y] = {
            listID: index,
            fields: data.fields
        }
        allClients = await getTasks(listIDs[y]);
        allPositions = await getTasks(listIDs[y])
    }
    console.log(allFields);


    allTasks = allClients.concat(allPositions);

    console.log(allTasks)

    let fieldIDs = [];
    allFields.map(field => {
        field.fields.map(f => {
            if (!fieldIDs.includes(f.id)) {
                fieldIDs.push(f.id);
                let row = table.insertRow();
                row.classList.add("border-b");
                let name = row.insertCell(0);
                name.innerHTML = f.name;
                name.style.fontWeight = "bold";
                let poradie = row.insertCell(1);
                poradie.innerHTML = f.type;
                let listID = row.insertCell(2);
                listID.innerHTML = field.listID;
                let fieldID = row.insertCell(3);
                fieldID.innerHTML = f.id;
                let options = row.insertCell(4);
                if (f.type_config.options) {
                    const select = document.createElement('select');
                    select.classList.add("bg-gray-50", "border", "border-gray-300", "text-gray-900", "text-sm", "rounded-lg", "focus:ring-blue-500", "focus:border-blue-500", "block", "w-full", "p-2.5");
                    f.type_config.options.map(option => {
                        let opt = document.createElement("option");
                        opt.value = option.id;
                        opt.innerHTML = `<strong>${option.label ? option.label : option.name}</strong>` + ` | (${option.id})`;
                        select.appendChild(opt);
                    });
                    options.appendChild(select);
                } else {
                    options.innerHTML = "Field has no options"
                }
            }
        })
    })

    allTasks.map(field => {
        field.tasks.map(f => {
            if (!tasksIDs.includes(f.id)) {
                tasksIDs.push(f.id);
                let row = tableTasks.insertRow();
                row.classList.add("border-b");
                let name = row.insertCell(0);
                name.innerHTML = f.name;
                name.style.fontWeight = "bold";
                let desc = row.insertCell(1);
                desc.innerHTML = f.description;
                let listID = row.insertCell(2);
                listID.innerHTML = f.status.status;
                let fieldID = row.insertCell(3);
                fieldID.innerHTML = f.id;
                let options = row.insertCell(4);
                if (f.custom_fields) {
                    const select = document.createElement('select');
                    select.classList.add("bg-gray-50", "border", "border-gray-300", "text-gray-900", "text-sm", "rounded-lg", "focus:ring-blue-500", "focus:border-blue-500", "block", "w-full", "p-2.5");
                    f.custom_fields.map(option => {
                        let opt = document.createElement("option");
                        opt.value = option.id;
                        opt.innerHTML = `<strong>${option.label ? option.label : option.name}</strong>` + ` | (${option.id})`;
                        select.appendChild(opt);
                    });
                    options.appendChild(select);
                } else {
                    options.innerHTML = "Task has no custom fields"
                }
                let updateButton = row.insertCell(5);
                if (field.listID === 162517514) {
                    podielnici.map(podielnik => {
                        console.log(f.custom_fields)
                        if (podielnik.podielnikid === f.custom_fields[30].value) {
                            updateButton.innerHTML = `<button type="button" onclick='updateTask(${JSON.stringify(podielnik)}, ${field.listID}, (${JSON.stringify(f)}))' class="focus:outline-none text-white bg-yellow-400 hover:bg-yellow-500 focus:ring-4 focus:ring-yellow-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2">Aktualizácia</button>`;
                        }
                    })
                } else {
                    pozicie.map(pozicia => {
                        if (f.custom_fields[71].value === pozicia.idpozicie) {
                            setTimeout(() => {
                                document.querySelector("#status-step").innerHTML = "Nastavujem metódy jednotlivej aktualizácie..."
                                updateButton.innerHTML = `<p>${f.custom_fields[71].value}</p><button type="button" onclick='updateTask(${JSON.stringify(pozicia)}, ${field.listID}, (${JSON.stringify(f)}))' class="focus:outline-none text-white bg-yellow-400 hover:bg-yellow-500 focus:ring-4 focus:ring-yellow-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2">Aktualizácia</button>`;
                            }, 500)
                        }
                    })
                }
            }
        })
    })
    document.getElementById("counter").innerHTML = fieldIDs.length.toString();
    document.getElementById("counterTask").innerHTML = tasksIDs.length.toString();
    document.getElementById("result").style.display = "inline-table";

    fields = allFields;
    console.log(allFields);
    console.log(allTasks);
    document.getElementById("loading-contianer").style.display = "none";
})

tablinks = document.getElementsByClassName("tablink");
for (let i = 0; i < tablinks.length; i++) {
    tablinks[i].addEventListener("click", function (e) {
        let y, tabcontent, tablinks;

        let rezim = e.target.attributes.content.value

        tabcontent = document.getElementsByClassName("tabka");
        for (y = 0; y < tabcontent.length; y++) {
            tabcontent[y].style.display = "none";
        }

        tablinks = document.getElementsByClassName("tablink");
        for (y = 0; y < tablinks.length; y++) {
            tablinks[y].className = tablinks[y].className.replace("bg-gray-50", "");
            tablinks[y].className = tablinks[y].className.replace("bg-blue-700", "");
            tablinks[y].className = tablinks[y].className.replace("hover:bg-gray-100", "");
            tablinks[y].className = tablinks[y].className.replace("hover:text-gray-900", "");
            tablinks[y].className = tablinks[y].className.replace("text-white", "");
        }

        console.log(e.currentTarget.classList)

        document.getElementById(rezim).style.display = "block";
        e.target.className += " bg-blue-700";
        e.target.className += " text-white";

    })
}

let turboupload = document.getElementById("turboupload");
turboupload.addEventListener("click", async (e) => {
    let startingTime = parseInt(new Date().getTime() / 1000);
    for (let t = 0; t < 1000; t++) {
        let count = 0;
        let secondsElapsed = parseInt(new Date().getTime() / 1000);
        if (count <= 99 || (secondsElapsed - startingTime) >= 58) {
            console.log("Updating field...", t);
            let current = await getCustomFieldsFromList(156440911);
            let klientiCustomField = current.find((element) => element.name === "TURBOUPLOAD");
            await createClientOption("HATE" + (t * (69 + t)), 156440911, klientiCustomField);
        } else {
            console.log("Waiting 2 seconds...")
            await new Promise(resolve => setTimeout(resolve, 2000));
            count = 0;
        }
        count++;
    }
})

uploadForm.addEventListener("submit", (e) => {
    e.preventDefault();
    document.getElementById("toast-loading").style.display = "block";
    document.querySelector("#toast-loading .textik").innerHTML = "Uploading data. Please wait..."
    console.log(fields.length);
    let parsed = {};

    for (let i = 0; i < fields.length; i++) {
        parsed[i] = {
            list: {
                listID: fields[i].listID,
                fields: fields[i].fields
            }
        }
    }
    fetch('/src/Controllers/clickup/uploadToDatabase.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(parsed)
    })
        .then(response => response.json())
        .then(data => {
            console.log(data.data);
            console.log(data.message);
            document.getElementById("toast-loading").style.display = "none";
            document.querySelector("#toast-loading .textik").innerHTML = ""
            if (data.message === "Data succesfully uploaded") {
                document.getElementById("toast-success").style.display = "flex";
                document.querySelector("#toast-success .textik").innerHTML = data.message;
                setTimeout(function () {
                    document.getElementById("toast-success").style.display = "none";
                }, 3000);
            } else {
                document.getElementById("toast-danger").style.display = "flex";
                document.querySelector("#toast-danger .textik").innerHTML = data.message;
                setTimeout(function () {
                    document.getElementById("toast-danger").style.display = "none";
                }, 3000);
            }
        })
        .catch(error => console.error(error));
})

"use strict";(()=>{var Et="4.0.17";var we=92,Ue=47,Ke=42,cn=34,fn=39,dn=58,_e=59,ae=10,xe=32,De=9,Rt=123,ot=125,st=40,Ot=41,pn=91,mn=93,Pt=45,lt=64,gn=33;function me(t){t[0]==="\uFEFF"&&(t=t.slice(1)),t=t.replaceAll(`\r
`,`
`);let r=[],n=[],e=[],i=null,a=null,l="",p="",c;for(let d=0;d<t.length;d++){let f=t.charCodeAt(d);if(f===we)l+=t.slice(d,d+2),d+=1;else if(f===Ue&&t.charCodeAt(d+1)===Ke){let m=d;for(let y=d+2;y<t.length;y++)if(c=t.charCodeAt(y),c===we)y+=1;else if(c===Ke&&t.charCodeAt(y+1)===Ue){d=y+1;break}let g=t.slice(m,d+1);g.charCodeAt(2)===gn&&n.push(je(g.slice(2,-2)))}else if(f===fn||f===cn){let m=d;for(let g=d+1;g<t.length;g++)if(c=t.charCodeAt(g),c===we)g+=1;else if(c===f){d=g;break}else{if(c===_e&&t.charCodeAt(g+1)===ae)throw new Error(`Unterminated string: ${t.slice(m,g+1)+String.fromCharCode(f)}`);if(c===ae)throw new Error(`Unterminated string: ${t.slice(m,g)+String.fromCharCode(f)}`)}l+=t.slice(m,d+1)}else{if((f===xe||f===ae||f===De)&&(c=t.charCodeAt(d+1))&&(c===xe||c===ae||c===De))continue;if(f===ae){if(l.length===0)continue;c=l.charCodeAt(l.length-1),c!==xe&&c!==ae&&c!==De&&(l+=" ")}else if(f===Pt&&t.charCodeAt(d+1)===Pt&&l.length===0){let m="",g=d,y=-1;for(let k=d+2;k<t.length;k++)if(c=t.charCodeAt(k),c===we)k+=1;else if(c===Ue&&t.charCodeAt(k+1)===Ke){for(let A=k+2;A<t.length;A++)if(c=t.charCodeAt(A),c===we)A+=1;else if(c===Ke&&t.charCodeAt(A+1)===Ue){k=A+1;break}}else if(y===-1&&c===dn)y=l.length+k-g;else if(c===_e&&m.length===0){l+=t.slice(g,k),d=k;break}else if(c===st)m+=")";else if(c===pn)m+="]";else if(c===Rt)m+="}";else if((c===ot||t.length-1===k)&&m.length===0){d=k-1,l+=t.slice(g,k);break}else(c===Ot||c===mn||c===ot)&&m.length>0&&t[k]===m[m.length-1]&&(m=m.slice(0,-1));let b=at(l,y);if(!b)throw new Error("Invalid custom property, expected a value");i?i.nodes.push(b):r.push(b),l=""}else if(f===_e&&l.charCodeAt(0)===lt)a=Ae(l),i?i.nodes.push(a):r.push(a),l="",a=null;else if(f===_e&&p[p.length-1]!==")"){let m=at(l);if(!m)throw l.length===0?new Error("Unexpected semicolon"):new Error(`Invalid declaration: \`${l.trim()}\``);i?i.nodes.push(m):r.push(m),l=""}else if(f===Rt&&p[p.length-1]!==")")p+="}",a=M(l.trim()),i&&i.nodes.push(a),e.push(i),i=a,l="",a=null;else if(f===ot&&p[p.length-1]!==")"){if(p==="")throw new Error("Missing opening {");if(p=p.slice(0,-1),l.length>0)if(l.charCodeAt(0)===lt)a=Ae(l),i?i.nodes.push(a):r.push(a),l="",a=null;else{let g=l.indexOf(":");if(i){let y=at(l,g);if(!y)throw new Error(`Invalid declaration: \`${l.trim()}\``);i.nodes.push(y)}}let m=e.pop()??null;m===null&&i&&r.push(i),i=m,l="",a=null}else if(f===st)p+=")",l+="(";else if(f===Ot){if(p[p.length-1]!==")")throw new Error("Missing opening (");p=p.slice(0,-1),l+=")"}else{if(l.length===0&&(f===xe||f===ae||f===De))continue;l+=String.fromCharCode(f)}}}if(l.charCodeAt(0)===lt&&r.push(Ae(l)),p.length>0&&i){if(i.kind==="rule")throw new Error(`Missing closing } at ${i.selector}`);if(i.kind==="at-rule")throw new Error(`Missing closing } at ${i.name} ${i.params}`)}return n.length>0?n.concat(r):r}function Ae(t,r=[]){for(let n=5;n<t.length;n++){let e=t.charCodeAt(n);if(e===xe||e===st){let i=t.slice(0,n).trim(),a=t.slice(n).trim();return U(i,a,r)}}return U(t.trim(),"",r)}function at(t,r=t.indexOf(":")){if(r===-1)return null;let n=t.indexOf("!important",r+1);return s(t.slice(0,r).trim(),t.slice(r+1,n===-1?t.length:n).trim(),n!==-1)}function ne(t){if(arguments.length===0)throw new TypeError("`CSS.escape` requires an argument.");let r=String(t),n=r.length,e=-1,i,a="",l=r.charCodeAt(0);if(n===1&&l===45)return"\\"+r;for(;++e<n;){if(i=r.charCodeAt(e),i===0){a+="\uFFFD";continue}if(i>=1&&i<=31||i===127||e===0&&i>=48&&i<=57||e===1&&i>=48&&i<=57&&l===45){a+="\\"+i.toString(16)+" ";continue}if(i>=128||i===45||i===95||i>=48&&i<=57||i>=65&&i<=90||i>=97&&i<=122){a+=r.charAt(e);continue}a+="\\"+r.charAt(e)}return a}function se(t){return t.replace(/\\([\dA-Fa-f]{1,6}[\t\n\f\r ]?|[\S\s])/g,r=>r.length>2?String.fromCodePoint(Number.parseInt(r.slice(1).trim(),16)):r[1])}var Kt=new Map([["--font",["--font-weight","--font-size"]],["--inset",["--inset-shadow","--inset-ring"]],["--text",["--text-color","--text-underline-offset","--text-indent","--text-decoration-thickness","--text-decoration-color"]]]);function Ut(t,r){return(Kt.get(r)??[]).some(n=>t===n||t.startsWith(`${n}-`))}var Ie=class{constructor(r=new Map,n=new Set([])){this.values=r;this.keyframes=n}prefix=null;add(r,n,e=0){if(r.endsWith("-*")){if(n!=="initial")throw new Error(`Invalid theme value \`${n}\` for namespace \`${r}\``);r==="--*"?this.values.clear():this.clearNamespace(r.slice(0,-2),0)}if(e&4){let i=this.values.get(r);if(i&&!(i.options&4))return}n==="initial"?this.values.delete(r):this.values.set(r,{value:n,options:e})}keysInNamespaces(r){let n=[];for(let e of r){let i=`${e}-`;for(let a of this.values.keys())a.startsWith(i)&&a.indexOf("--",2)===-1&&(Ut(a,e)||n.push(a.slice(i.length)))}return n}get(r){for(let n of r){let e=this.values.get(n);if(e)return e.value}return null}hasDefault(r){return(this.getOptions(r)&4)===4}getOptions(r){return r=se(this.#r(r)),this.values.get(r)?.options??0}entries(){return this.prefix?Array.from(this.values,r=>(r[0]=this.prefixKey(r[0]),r)):this.values.entries()}prefixKey(r){return this.prefix?`--${this.prefix}-${r.slice(2)}`:r}#r(r){return this.prefix?`--${r.slice(3+this.prefix.length)}`:r}clearNamespace(r,n){let e=Kt.get(r)??[];e:for(let i of this.values.keys())if(i.startsWith(r)){if(n!==0&&(this.getOptions(i)&n)!==n)continue;for(let a of e)if(i.startsWith(a))continue e;this.values.delete(i)}}#e(r,n){for(let e of n){let i=r!==null?`${e}-${r}`:e;if(!this.values.has(i))if(r!==null&&r.includes(".")){if(i=`${e}-${r.replaceAll(".","_")}`,!this.values.has(i))continue}else continue;if(!Ut(i,e))return i}return null}#t(r){let n=this.values.get(r);if(!n)return null;let e=null;return n.options&2&&(e=n.value),`var(${ne(this.prefixKey(r))}${e?`, ${e}`:""})`}markUsedVariable(r){let n=se(this.#r(r)),e=this.values.get(n);if(!e)return!1;let i=e.options&16;return e.options|=16,!i}resolve(r,n,e=0){let i=this.#e(r,n);if(!i)return null;let a=this.values.get(i);return(e|a.options)&1?a.value:this.#t(i)}resolveValue(r,n){let e=this.#e(r,n);return e?this.values.get(e).value:null}resolveWith(r,n,e=[]){let i=this.#e(r,n);if(!i)return null;let a={};for(let p of e){let c=`${i}${p}`,d=this.values.get(c);d&&(d.options&1?a[p]=d.value:a[p]=this.#t(c))}let l=this.values.get(i);return l.options&1?[l.value,a]:[this.#t(i),a]}namespace(r){let n=new Map,e=`${r}-`;for(let[i,a]of this.values)i===r?n.set(null,a.value):i.startsWith(`${e}-`)?n.set(i.slice(r.length),a.value):i.startsWith(e)&&n.set(i.slice(e.length),a.value);return n}addKeyframes(r){this.keyframes.add(r)}getKeyframes(){return Array.from(this.keyframes)}};var z=class extends Map{constructor(n){super();this.factory=n}get(n){let e=super.get(n);return e===void 0&&(e=this.factory(n,this),this.set(n,e)),e}};function ct(t){return{kind:"word",value:t}}function hn(t,r){return{kind:"function",value:t,nodes:r}}function bn(t){return{kind:"separator",value:t}}function Y(t,r,n=null){for(let e=0;e<t.length;e++){let i=t[e],a=!1,l=0,p=r(i,{parent:n,replaceWith(c){a||(a=!0,Array.isArray(c)?c.length===0?(t.splice(e,1),l=0):c.length===1?(t[e]=c[0],l=1):(t.splice(e,1,...c),l=c.length):t[e]=c)}})??0;if(a){p===0?e--:e+=l-1;continue}if(p===2)return 2;if(p!==1&&i.kind==="function"&&Y(i.nodes,r,i)===2)return 2}}function H(t){let r="";for(let n of t)switch(n.kind){case"word":case"separator":{r+=n.value;break}case"function":r+=n.value+"("+H(n.nodes)+")"}return r}var _t=92,yn=41,Dt=58,jt=44,vn=34,It=61,zt=62,Ft=60,Lt=10,kn=40,wn=39,Mt=47,Wt=32,Bt=9;function F(t){t=t.replaceAll(`\r
`,`
`);let r=[],n=[],e=null,i="",a;for(let l=0;l<t.length;l++){let p=t.charCodeAt(l);switch(p){case _t:{i+=t[l]+t[l+1],l++;break}case Dt:case jt:case It:case zt:case Ft:case Lt:case Mt:case Wt:case Bt:{if(i.length>0){let m=ct(i);e?e.nodes.push(m):r.push(m),i=""}let c=l,d=l+1;for(;d<t.length&&(a=t.charCodeAt(d),!(a!==Dt&&a!==jt&&a!==It&&a!==zt&&a!==Ft&&a!==Lt&&a!==Mt&&a!==Wt&&a!==Bt));d++);l=d-1;let f=bn(t.slice(c,d));e?e.nodes.push(f):r.push(f);break}case wn:case vn:{let c=l;for(let d=l+1;d<t.length;d++)if(a=t.charCodeAt(d),a===_t)d+=1;else if(a===p){l=d;break}i+=t.slice(c,l+1);break}case kn:{let c=hn(i,[]);i="",e?e.nodes.push(c):r.push(c),n.push(c),e=c;break}case yn:{let c=n.pop();if(i.length>0){let d=ct(i);c.nodes.push(d),i=""}n.length>0?e=n[n.length-1]:e=null;break}default:i+=String.fromCharCode(p)}}return i.length>0&&r.push(ct(i)),r}function ze(t){let r=[];return Y(F(t),n=>{if(!(n.kind!=="function"||n.value!=="var"))return Y(n.nodes,e=>{e.kind!=="word"||e.value[0]!=="-"||e.value[1]!=="-"||r.push(e.value)}),1}),r}var An=64;function j(t,r=[]){return{kind:"rule",selector:t,nodes:r}}function U(t,r="",n=[]){return{kind:"at-rule",name:t,params:r,nodes:n}}function M(t,r=[]){return t.charCodeAt(0)===An?Ae(t,r):j(t,r)}function s(t,r,n=!1){return{kind:"declaration",property:t,value:r,important:n}}function je(t){return{kind:"comment",value:t}}function X(t,r){return{kind:"context",context:t,nodes:r}}function D(t){return{kind:"at-root",nodes:t}}function K(t,r,n=[],e={}){for(let i=0;i<t.length;i++){let a=t[i],l=n[n.length-1]??null;if(a.kind==="context"){if(K(a.nodes,r,n,{...e,...a.context})===2)return 2;continue}n.push(a);let p=!1,c=0,d=r(a,{parent:l,context:e,path:n,replaceWith(f){p||(p=!0,Array.isArray(f)?f.length===0?(t.splice(i,1),c=0):f.length===1?(t[i]=f[0],c=1):(t.splice(i,1,...f),c=f.length):(t[i]=f,c=1))}})??0;if(n.pop(),p){d===0?i--:i+=c-1;continue}if(d===2)return 2;if(d!==1&&"nodes"in a){n.push(a);let f=K(a.nodes,r,n,e);if(n.pop(),f===2)return 2}}}function Fe(t,r,n=[],e={}){for(let i=0;i<t.length;i++){let a=t[i],l=n[n.length-1]??null;if(a.kind==="rule"||a.kind==="at-rule")n.push(a),Fe(a.nodes,r,n,e),n.pop();else if(a.kind==="context"){Fe(a.nodes,r,n,{...e,...a.context});continue}n.push(a),r(a,{parent:l,context:e,path:n,replaceWith(p){Array.isArray(p)?p.length===0?t.splice(i,1):p.length===1?t[i]=p[0]:t.splice(i,1,...p):t[i]=p,i+=p.length-1}}),n.pop()}}function ue(t,r){let n=[],e=new Set,i=new z(()=>new Set),a=new Set,l=new Set,p=new z(()=>new Set);function c(f,m,g={},y=0){if(f.kind==="declaration"){if(f.property==="--tw-sort"||f.value===void 0||f.value===null)return;if(g.theme&&f.property[0]==="-"&&f.property[1]==="-"){if(f.value==="initial"){f.value=void 0;return}g.keyframes||i.get(m).add(f)}if(f.value.includes("var("))if(g.theme&&f.property[0]==="-"&&f.property[1]==="-")for(let b of ze(f.value))p.get(b).add(f.property);else r.trackUsedVariables(f.value);if(f.property==="animation")for(let b of qt(f.value))l.add(b);m.push(f)}else if(f.kind==="rule")if(f.selector==="&")for(let b of f.nodes){let k=[];c(b,k,g,y+1),k.length>0&&m.push(...k)}else{let b={...f,nodes:[]};for(let k of f.nodes)c(k,b.nodes,g,y+1);b.nodes.length>0&&m.push(b)}else if(f.kind==="at-rule"&&f.name==="@property"&&y===0){if(e.has(f.params))return;e.add(f.params);let b={...f,nodes:[]};for(let k of f.nodes)c(k,b.nodes,g,y+1);m.push(b)}else if(f.kind==="at-rule"){f.name==="@keyframes"&&(g={...g,keyframes:!0});let b={...f,nodes:[]};for(let k of f.nodes)c(k,b.nodes,g,y+1);f.name==="@keyframes"&&g.theme&&a.add(b),(b.nodes.length>0||b.name==="@layer"||b.name==="@charset"||b.name==="@custom-media"||b.name==="@namespace"||b.name==="@import")&&m.push(b)}else if(f.kind==="at-root")for(let b of f.nodes){let k=[];c(b,k,g,0);for(let A of k)n.push(A)}else if(f.kind==="context"){if(f.context.reference)return;for(let b of f.nodes)c(b,m,{...g,...f.context},y)}else f.kind==="comment"&&m.push(f)}let d=[];for(let f of t)c(f,d,{},0);e:for(let[f,m]of i)for(let g of m){if(Ht(g.property,r.theme,p)){if(g.property.startsWith(r.theme.prefixKey("--animate-")))for(let k of qt(g.value))l.add(k);continue}let b=f.indexOf(g);if(f.splice(b,1),f.length===0){let k=Cn(d,A=>A.kind==="rule"&&A.nodes===f);if(!k||k.length===0)continue e;k.unshift({kind:"at-root",nodes:d});do{let A=k.pop();if(!A)break;let x=k[k.length-1];if(!x||x.kind!=="at-root"&&x.kind!=="at-rule")break;let V=x.nodes.indexOf(A);if(V===-1)break;x.nodes.splice(V,1)}while(!0);continue e}}for(let f of a)if(!l.has(f.params)){let m=n.indexOf(f);n.splice(m,1)}return d.concat(n)}function J(t){function r(e,i=0){let a="",l="  ".repeat(i);if(e.kind==="declaration")a+=`${l}${e.property}: ${e.value}${e.important?" !important":""};
`;else if(e.kind==="rule"){a+=`${l}${e.selector} {
`;for(let p of e.nodes)a+=r(p,i+1);a+=`${l}}
`}else if(e.kind==="at-rule"){if(e.nodes.length===0)return`${l}${e.name} ${e.params};
`;a+=`${l}${e.name}${e.params?` ${e.params} `:" "}{
`;for(let p of e.nodes)a+=r(p,i+1);a+=`${l}}
`}else if(e.kind==="comment")a+=`${l}/*${e.value}*/
`;else if(e.kind==="context"||e.kind==="at-root")return"";return a}let n="";for(let e of t){let i=r(e);i!==""&&(n+=i)}return n}function Cn(t,r){let n=[];return K(t,(e,{path:i})=>{if(r(e))return n=[...i],2}),n}function Ht(t,r,n,e=new Set){if(e.has(t)||(e.add(t),r.getOptions(t)&24))return!0;{let a=n.get(t)??[];for(let l of a)if(Ht(l,r,n,e))return!0}return!1}function qt(t){return t.split(/[\s,]+/)}var ft=["calc","min","max","clamp","mod","rem","sin","cos","tan","asin","acos","atan","atan2","pow","sqrt","hypot","log","exp","round"],Me=["anchor-size"],Gt=new RegExp(`(${Me.join("|")})\\(`,"g");function Se(t){return t.indexOf("(")!==-1&&ft.some(r=>t.includes(`${r}(`))}function Yt(t){if(!ft.some(i=>t.includes(i)))return t;let r=!1;Me.some(i=>t.includes(i))&&(Gt.lastIndex=0,t=t.replace(Gt,(i,a)=>(r=!0,`$${Me.indexOf(a)}$(`)));let n="",e=[];for(let i=0;i<t.length;i++){let a=t[i];if(a==="("){n+=a;let l=i;for(let c=i-1;c>=0;c--){let d=t.charCodeAt(c);if(d>=48&&d<=57)l=c;else if(d>=97&&d<=122)l=c;else break}let p=t.slice(l,i);if(ft.includes(p)){e.unshift(!0);continue}else if(e[0]&&p===""){e.unshift(!0);continue}e.unshift(!1);continue}else if(a===")")n+=a,e.shift();else if(a===","&&e[0]){n+=", ";continue}else{if(a===" "&&e[0]&&n[n.length-1]===" ")continue;if((a==="+"||a==="*"||a==="/"||a==="-")&&e[0]){let l=n.trimEnd(),p=l[l.length-1];if(p==="+"||p==="*"||p==="/"||p==="-"){n+=a;continue}else if(p==="("||p===","){n+=a;continue}else t[i-1]===" "?n+=`${a} `:n+=` ${a} `}else if(e[0]&&t.startsWith("to-zero",i)){let l=i;i+=7,n+=t.slice(l,i+1)}else n+=a}}return r?n.replace(/\$(\d+)\$/g,(i,a)=>Me[a]??i):n}function ie(t){if(t.indexOf("(")===-1)return ge(t);let r=F(t);return dt(r),t=H(r),t=Yt(t),t}function ge(t,r=!1){let n="";for(let e=0;e<t.length;e++){let i=t[e];i==="\\"&&t[e+1]==="_"?(n+="_",e+=1):i==="_"&&!r?n+=" ":n+=i}return n}function dt(t){for(let r of t)switch(r.kind){case"function":{if(r.value==="url"||r.value.endsWith("_url")){r.value=ge(r.value);break}if(r.value==="var"||r.value.endsWith("_var")||r.value==="theme"||r.value.endsWith("_theme")){r.value=ge(r.value);for(let n=0;n<r.nodes.length;n++){if(n==0&&r.nodes[n].kind==="word"){r.nodes[n].value=ge(r.nodes[n].value,!0);continue}dt([r.nodes[n]])}break}r.value=ge(r.value),dt(r.nodes);break}case"separator":case"word":{r.value=ge(r.value);break}default:Sn(r)}}function Sn(t){throw new Error(`Unexpected value: ${t}`)}var We=new Uint8Array(256);function O(t,r){let n=0,e=[],i=0,a=t.length,l=r.charCodeAt(0);for(let p=0;p<a;p++){let c=t.charCodeAt(p);if(n===0&&c===l){e.push(t.slice(i,p)),i=p+1;continue}switch(c){case 92:p+=1;break;case 39:case 34:for(;++p<a;){let d=t.charCodeAt(p);if(d===92){p+=1;continue}if(d===c)break}break;case 40:We[n]=41,n++;break;case 91:We[n]=93,n++;break;case 123:We[n]=125,n++;break;case 93:case 125:case 41:n>0&&c===We[n-1]&&n--;break}}return e.push(t.slice(i)),e}var Nn=58,Jt=45,Zt=97,Qt=122;function*Xt(t,r){let n=O(t,":");if(r.theme.prefix){if(n.length===1||n[0]!==r.theme.prefix)return null;n.shift()}let e=n.pop(),i=[];for(let m=n.length-1;m>=0;--m){let g=r.parseVariant(n[m]);if(g===null)return;i.push(g)}let a=!1;e[e.length-1]==="!"?(a=!0,e=e.slice(0,-1)):e[0]==="!"&&(a=!0,e=e.slice(1)),r.utilities.has(e,"static")&&!e.includes("[")&&(yield{kind:"static",root:e,variants:i,important:a,raw:t});let[l,p=null,c]=O(e,"/");if(c)return;let d=p===null?null:pt(p);if(p!==null&&d===null)return;if(l[0]==="["){if(l[l.length-1]!=="]")return;let m=l.charCodeAt(1);if(m!==Jt&&!(m>=Zt&&m<=Qt))return;l=l.slice(1,-1);let g=l.indexOf(":");if(g===-1||g===0||g===l.length-1)return;let y=l.slice(0,g),b=ie(l.slice(g+1));yield{kind:"arbitrary",property:y,value:b,modifier:d,variants:i,important:a,raw:t};return}let f;if(l[l.length-1]==="]"){let m=l.indexOf("-[");if(m===-1)return;let g=l.slice(0,m);if(!r.utilities.has(g,"functional"))return;let y=l.slice(m+1);f=[[g,y]]}else if(l[l.length-1]===")"){let m=l.indexOf("-(");if(m===-1)return;let g=l.slice(0,m);if(!r.utilities.has(g,"functional"))return;let y=l.slice(m+2,-1),b=O(y,":"),k=null;if(b.length===2&&(k=b[0],y=b[1]),y[0]!=="-"&&y[1]!=="-")return;f=[[g,k===null?`[var(${y})]`:`[${k}:var(${y})]`]]}else f=tr(l,m=>r.utilities.has(m,"functional"));for(let[m,g]of f){let y={kind:"functional",root:m,modifier:d,value:null,variants:i,important:a,raw:t};if(g===null){yield y;continue}{let b=g.indexOf("[");if(b!==-1){if(g[g.length-1]!=="]")return;let A=ie(g.slice(b+1,-1)),x="";for(let V=0;V<A.length;V++){let P=A.charCodeAt(V);if(P===Nn){x=A.slice(0,V),A=A.slice(V+1);break}if(!(P===Jt||P>=Zt&&P<=Qt))break}if(A.length===0||A.trim().length===0)continue;y.value={kind:"arbitrary",dataType:x||null,value:A}}else{let A=p===null||y.modifier?.kind==="arbitrary"?null:`${g}/${p}`;y.value={kind:"named",value:g,fraction:A}}}yield y}}function pt(t){if(t[0]==="["&&t[t.length-1]==="]"){let r=ie(t.slice(1,-1));return r.length===0||r.trim().length===0?null:{kind:"arbitrary",value:r}}if(t[0]==="("&&t[t.length-1]===")"){let r=ie(t.slice(1,-1));return r.length===0||r.trim().length===0||r[0]!=="-"&&r[1]!=="-"?null:{kind:"arbitrary",value:`var(${r})`}}return{kind:"named",value:t}}function er(t,r){if(t[0]==="["&&t[t.length-1]==="]"){if(t[1]==="@"&&t.includes("&"))return null;let n=ie(t.slice(1,-1));if(n.length===0||n.trim().length===0)return null;let e=n[0]===">"||n[0]==="+"||n[0]==="~";return!e&&n[0]!=="@"&&!n.includes("&")&&(n=`&:is(${n})`),{kind:"arbitrary",selector:n,relative:e}}{let[n,e=null,i]=O(t,"/");if(i)return null;let a=tr(n,l=>r.variants.has(l));for(let[l,p]of a)switch(r.variants.kind(l)){case"static":return p!==null||e!==null?null:{kind:"static",root:l};case"functional":{let c=e===null?null:pt(e);if(e!==null&&c===null)return null;if(p===null)return{kind:"functional",root:l,modifier:c,value:null};if(p[p.length-1]==="]"){if(p[0]!=="[")continue;let d=ie(p.slice(1,-1));return d.length===0||d.trim().length===0?null:{kind:"functional",root:l,modifier:c,value:{kind:"arbitrary",value:d}}}if(p[p.length-1]===")"){if(p[0]!=="(")continue;let d=ie(p.slice(1,-1));return d.length===0||d.trim().length===0||d[0]!=="-"&&d[1]!=="-"?null:{kind:"functional",root:l,modifier:c,value:{kind:"arbitrary",value:`var(${d})`}}}return{kind:"functional",root:l,modifier:c,value:{kind:"named",value:p}}}case"compound":{if(p===null)return null;let c=r.parseVariant(p);if(c===null||!r.variants.compoundsWith(l,c))return null;let d=e===null?null:pt(e);return e!==null&&d===null?null:{kind:"compound",root:l,modifier:d,variant:c}}}}return null}function*tr(t,r){r(t)&&(yield[t,null]);let n=t.lastIndexOf("-");if(n===-1){t[0]==="@"&&r("@")&&(yield["@",t.slice(1)]);return}do{let e=t.slice(0,n);if(r(e)){let i=[e,t.slice(n+1)];if(i[1]==="")break;yield i}n=t.lastIndexOf("-",n-1)}while(n>0)}function ce(t,r,n){if(t===r)return 0;let e=t.indexOf("("),i=r.indexOf("("),a=e===-1?t.replace(/[\d.]+/g,""):t.slice(0,e),l=i===-1?r.replace(/[\d.]+/g,""):r.slice(0,i),p=(a===l?0:a<l?-1:1)||(n==="asc"?parseInt(t)-parseInt(r):parseInt(r)-parseInt(t));return Number.isNaN(p)?t<r?-1:1:p}var $n=new Set(["black","silver","gray","white","maroon","red","purple","fuchsia","green","lime","olive","yellow","navy","blue","teal","aqua","aliceblue","antiquewhite","aqua","aquamarine","azure","beige","bisque","black","blanchedalmond","blue","blueviolet","brown","burlywood","cadetblue","chartreuse","chocolate","coral","cornflowerblue","cornsilk","crimson","cyan","darkblue","darkcyan","darkgoldenrod","darkgray","darkgreen","darkgrey","darkkhaki","darkmagenta","darkolivegreen","darkorange","darkorchid","darkred","darksalmon","darkseagreen","darkslateblue","darkslategray","darkslategrey","darkturquoise","darkviolet","deeppink","deepskyblue","dimgray","dimgrey","dodgerblue","firebrick","floralwhite","forestgreen","fuchsia","gainsboro","ghostwhite","gold","goldenrod","gray","green","greenyellow","grey","honeydew","hotpink","indianred","indigo","ivory","khaki","lavender","lavenderblush","lawngreen","lemonchiffon","lightblue","lightcoral","lightcyan","lightgoldenrodyellow","lightgray","lightgreen","lightgrey","lightpink","lightsalmon","lightseagreen","lightskyblue","lightslategray","lightslategrey","lightsteelblue","lightyellow","lime","limegreen","linen","magenta","maroon","mediumaquamarine","mediumblue","mediumorchid","mediumpurple","mediumseagreen","mediumslateblue","mediumspringgreen","mediumturquoise","mediumvioletred","midnightblue","mintcream","mistyrose","moccasin","navajowhite","navy","oldlace","olive","olivedrab","orange","orangered","orchid","palegoldenrod","palegreen","paleturquoise","palevioletred","papayawhip","peachpuff","peru","pink","plum","powderblue","purple","rebeccapurple","red","rosybrown","royalblue","saddlebrown","salmon","sandybrown","seagreen","seashell","sienna","silver","skyblue","slateblue","slategray","slategrey","snow","springgreen","steelblue","tan","teal","thistle","tomato","turquoise","violet","wheat","white","whitesmoke","yellow","yellowgreen","transparent","currentcolor","canvas","canvastext","linktext","visitedtext","activetext","buttonface","buttontext","buttonborder","field","fieldtext","highlight","highlighttext","selecteditem","selecteditemtext","mark","marktext","graytext","accentcolor","accentcolortext"]),Tn=/^(rgba?|hsla?|hwb|color|(ok)?(lab|lch)|light-dark|color-mix)\(/i;function rr(t){return t.charCodeAt(0)===35||Tn.test(t)||$n.has(t.toLowerCase())}var Vn={color:rr,length:Be,percentage:mt,ratio:Ln,number:ir,integer:T,url:nr,position:Bn,"bg-size":qn,"line-width":Rn,image:Un,"family-name":_n,"generic-name":Kn,"absolute-size":Dn,"relative-size":jn,angle:Yn,vector:Zn};function L(t,r){if(t.startsWith("var("))return null;for(let n of r)if(Vn[n]?.(t))return n;return null}var En=/^url\(.*\)$/;function nr(t){return En.test(t)}function Rn(t){return O(t," ").every(r=>Be(r)||ir(r)||r==="thin"||r==="medium"||r==="thick")}var On=/^(?:element|image|cross-fade|image-set)\(/,Pn=/^(repeating-)?(conic|linear|radial)-gradient\(/;function Un(t){let r=0;for(let n of O(t,","))if(!n.startsWith("var(")){if(nr(n)){r+=1;continue}if(Pn.test(n)){r+=1;continue}if(On.test(n)){r+=1;continue}return!1}return r>0}function Kn(t){return t==="serif"||t==="sans-serif"||t==="monospace"||t==="cursive"||t==="fantasy"||t==="system-ui"||t==="ui-serif"||t==="ui-sans-serif"||t==="ui-monospace"||t==="ui-rounded"||t==="math"||t==="emoji"||t==="fangsong"}function _n(t){let r=0;for(let n of O(t,",")){let e=n.charCodeAt(0);if(e>=48&&e<=57)return!1;n.startsWith("var(")||(r+=1)}return r>0}function Dn(t){return t==="xx-small"||t==="x-small"||t==="small"||t==="medium"||t==="large"||t==="x-large"||t==="xx-large"||t==="xxx-large"}function jn(t){return t==="larger"||t==="smaller"}var te=/[+-]?\d*\.?\d+(?:[eE][+-]?\d+)?/,In=new RegExp(`^${te.source}$`);function ir(t){return In.test(t)||Se(t)}var zn=new RegExp(`^${te.source}%$`);function mt(t){return zn.test(t)||Se(t)}var Fn=new RegExp(`^${te.source}s*/s*${te.source}$`);function Ln(t){return Fn.test(t)||Se(t)}var Mn=["cm","mm","Q","in","pc","pt","px","em","ex","ch","rem","lh","rlh","vw","vh","vmin","vmax","vb","vi","svw","svh","lvw","lvh","dvw","dvh","cqw","cqh","cqi","cqb","cqmin","cqmax"],Wn=new RegExp(`^${te.source}(${Mn.join("|")})$`);function Be(t){return Wn.test(t)||Se(t)}function Bn(t){let r=0;for(let n of O(t," ")){if(n==="center"||n==="top"||n==="right"||n==="bottom"||n==="left"){r+=1;continue}if(!n.startsWith("var(")){if(Be(n)||mt(n)){r+=1;continue}return!1}}return r>0}function qn(t){let r=0;for(let n of O(t,",")){if(n==="cover"||n==="contain"){r+=1;continue}let e=O(n," ");if(e.length!==1&&e.length!==2)return!1;if(e.every(i=>i==="auto"||Be(i)||mt(i))){r+=1;continue}}return r>0}var Hn=["deg","rad","grad","turn"],Gn=new RegExp(`^${te.source}(${Hn.join("|")})$`);function Yn(t){return Gn.test(t)}var Jn=new RegExp(`^${te.source} +${te.source} +${te.source}$`);function Zn(t){return Jn.test(t)}function T(t){let r=Number(t);return Number.isInteger(r)&&r>=0&&String(r)===String(t)}function gt(t){let r=Number(t);return Number.isInteger(r)&&r>0&&String(r)===String(t)}function he(t){return or(t,.25)}function qe(t){return or(t,.25)}function or(t,r){let n=Number(t);return n>=0&&n%r===0&&String(n)===String(t)}var Qn=new Set(["inset","inherit","initial","revert","unset"]),lr=/^-?(\d+|\.\d+)(.*?)$/g;function fe(t,r){return O(t,",").map(e=>{e=e.trim();let i=O(e," ").filter(d=>d.trim()!==""),a=null,l=null,p=null;for(let d of i)Qn.has(d)||(lr.test(d)?(l===null?l=d:p===null&&(p=d),lr.lastIndex=0):a===null&&(a=d));if(l===null||p===null)return e;let c=r(a??"currentcolor");return a!==null?e.replace(a,c):`${e} ${c}`}).join(", ")}var ti=/^-?[a-z][a-zA-Z0-9/%._-]*$/,ri=/^-?[a-z][a-zA-Z0-9/%._-]*-\*$/,ht=["0","0.5","1","1.5","2","2.5","3","3.5","4","5","6","7","8","9","10","11","12","14","16","20","24","28","32","36","40","44","48","52","56","60","64","72","80","96"],bt=class{utilities=new z(()=>[]);completions=new Map;static(r,n){this.utilities.get(r).push({kind:"static",compileFn:n})}functional(r,n,e){this.utilities.get(r).push({kind:"functional",compileFn:n,options:e})}has(r,n){return this.utilities.has(r)&&this.utilities.get(r).some(e=>e.kind===n)}get(r){return this.utilities.has(r)?this.utilities.get(r):[]}getCompletions(r){return this.completions.get(r)?.()??[]}suggest(r,n){this.completions.set(r,n)}keys(r){let n=[];for(let[e,i]of this.utilities.entries())for(let a of i)if(a.kind===r){n.push(e);break}return n}};function $(t,r,n){return U("@property",t,[s("syntax",n?`"${n}"`:'"*"'),s("inherits","false"),...r?[s("initial-value",r)]:[]])}function Z(t,r){if(r===null)return t;let n=Number(r);return Number.isNaN(n)||(r=`${n*100}%`),`color-mix(in oklab, ${t} ${r}, transparent)`}function W(t,r,n){if(!r)return t;if(r.kind==="arbitrary")return Z(t,r.value);let e=n.resolve(r.value,["--opacity"]);return e?Z(t,e):qe(r.value)?Z(t,`${r.value}%`):null}function G(t,r,n){let e=null;switch(t.value.value){case"inherit":{e="inherit";break}case"transparent":{e="transparent";break}case"current":{e="currentColor";break}default:{e=r.resolve(t.value.value,n);break}}return e?W(e,t.modifier,r):null}function sr(t){let r=new bt;function n(o,u){let h=/(\d+)_(\d+)/g;function*w(N){for(let E of t.keysInNamespaces(N))yield E.replace(h,(S,C,I)=>`${C}.${I}`)}let v=["1/2","1/3","2/3","1/4","2/4","3/4","1/5","2/5","3/5","4/5","1/6","2/6","3/6","4/6","5/6","1/12","2/12","3/12","4/12","5/12","6/12","7/12","8/12","9/12","10/12","11/12"];r.suggest(o,()=>{let N=[];for(let E of u()){if(typeof E=="string"){N.push({values:[E],modifiers:[]});continue}let S=[...E.values??[],...w(E.valueThemeKeys??[])],C=[...E.modifiers??[],...w(E.modifierThemeKeys??[])];E.supportsFractions&&S.push(...v),E.hasDefaultValue&&S.unshift(null),N.push({supportsNegative:E.supportsNegative,values:S,modifiers:C})}return N})}function e(o,u){r.static(o,()=>u.map(h=>typeof h=="function"?h():s(h[0],h[1])))}function i(o,u){function h({negative:w}){return v=>{let N=null;if(v.value)if(v.value.kind==="arbitrary"){if(v.modifier)return;N=v.value.value}else{if(N=t.resolve(v.value.fraction??v.value.value,u.themeKeys??[]),N===null&&u.supportsFractions&&v.value.fraction){let[E,S]=O(v.value.fraction,"/");if(!T(E)||!T(S))return;N=`calc(${v.value.fraction} * 100%)`}if(N===null&&w&&u.handleNegativeBareValue){if(N=u.handleNegativeBareValue(v.value),!N?.includes("/")&&v.modifier)return;if(N!==null)return u.handle(N)}if(N===null&&u.handleBareValue&&(N=u.handleBareValue(v.value),!N?.includes("/")&&v.modifier))return}else{if(v.modifier)return;N=u.defaultValue!==void 0?u.defaultValue:t.resolve(null,u.themeKeys??[])}if(N!==null)return u.handle(w?`calc(${N} * -1)`:N)}}u.supportsNegative&&r.functional(`-${o}`,h({negative:!0})),r.functional(o,h({negative:!1})),n(o,()=>[{supportsNegative:u.supportsNegative,valueThemeKeys:u.themeKeys??[],hasDefaultValue:u.defaultValue!==void 0&&u.defaultValue!==null,supportsFractions:u.supportsFractions}])}function a(o,u){r.functional(o,h=>{if(!h.value)return;let w=null;if(h.value.kind==="arbitrary"?(w=h.value.value,w=W(w,h.modifier,t)):w=G(h,t,u.themeKeys),w!==null)return u.handle(w)}),n(o,()=>[{values:["current","inherit","transparent"],valueThemeKeys:u.themeKeys,modifiers:Array.from({length:21},(h,w)=>`${w*5}`)}])}function l(o,u,h,{supportsNegative:w=!1,supportsFractions:v=!1}={}){w&&r.static(`-${o}-px`,()=>h("-1px")),r.static(`${o}-px`,()=>h("1px")),i(o,{themeKeys:u,supportsFractions:v,supportsNegative:w,defaultValue:null,handleBareValue:({value:N})=>{let E=t.resolve(null,["--spacing"]);return!E||!he(N)?null:`calc(${E} * ${N})`},handleNegativeBareValue:({value:N})=>{let E=t.resolve(null,["--spacing"]);return!E||!he(N)?null:`calc(${E} * -${N})`},handle:h}),n(o,()=>[{values:t.get(["--spacing"])?ht:[],supportsNegative:w,supportsFractions:v,valueThemeKeys:u}])}e("sr-only",[["position","absolute"],["width","1px"],["height","1px"],["padding","0"],["margin","-1px"],["overflow","hidden"],["clip","rect(0, 0, 0, 0)"],["white-space","nowrap"],["border-width","0"]]),e("not-sr-only",[["position","static"],["width","auto"],["height","auto"],["padding","0"],["margin","0"],["overflow","visible"],["clip","auto"],["white-space","normal"]]),e("pointer-events-none",[["pointer-events","none"]]),e("pointer-events-auto",[["pointer-events","auto"]]),e("visible",[["visibility","visible"]]),e("invisible",[["visibility","hidden"]]),e("collapse",[["visibility","collapse"]]),e("static",[["position","static"]]),e("fixed",[["position","fixed"]]),e("absolute",[["position","absolute"]]),e("relative",[["position","relative"]]),e("sticky",[["position","sticky"]]);for(let[o,u]of[["inset","inset"],["inset-x","inset-inline"],["inset-y","inset-block"],["start","inset-inline-start"],["end","inset-inline-end"],["top","top"],["right","right"],["bottom","bottom"],["left","left"]])e(`${o}-auto`,[[u,"auto"]]),e(`${o}-full`,[[u,"100%"]]),e(`-${o}-full`,[[u,"-100%"]]),l(o,["--inset","--spacing"],h=>[s(u,h)],{supportsNegative:!0,supportsFractions:!0});e("isolate",[["isolation","isolate"]]),e("isolation-auto",[["isolation","auto"]]),e("z-auto",[["z-index","auto"]]),i("z",{supportsNegative:!0,handleBareValue:({value:o})=>T(o)?o:null,themeKeys:["--z-index"],handle:o=>[s("z-index",o)]}),n("z",()=>[{supportsNegative:!0,values:["0","10","20","30","40","50"],valueThemeKeys:["--z-index"]}]),e("order-first",[["order","-9999"]]),e("order-last",[["order","9999"]]),e("order-none",[["order","0"]]),i("order",{supportsNegative:!0,handleBareValue:({value:o})=>T(o)?o:null,themeKeys:["--order"],handle:o=>[s("order",o)]}),n("order",()=>[{supportsNegative:!0,values:Array.from({length:12},(o,u)=>`${u+1}`),valueThemeKeys:["--order"]}]),e("col-auto",[["grid-column","auto"]]),i("col",{supportsNegative:!0,handleBareValue:({value:o})=>T(o)?o:null,themeKeys:["--grid-column"],handle:o=>[s("grid-column",o)]}),e("col-span-full",[["grid-column","1 / -1"]]),i("col-span",{handleBareValue:({value:o})=>T(o)?o:null,handle:o=>[s("grid-column",`span ${o} / span ${o}`)]}),e("col-start-auto",[["grid-column-start","auto"]]),i("col-start",{supportsNegative:!0,handleBareValue:({value:o})=>T(o)?o:null,themeKeys:["--grid-column-start"],handle:o=>[s("grid-column-start",o)]}),e("col-end-auto",[["grid-column-end","auto"]]),i("col-end",{supportsNegative:!0,handleBareValue:({value:o})=>T(o)?o:null,themeKeys:["--grid-column-end"],handle:o=>[s("grid-column-end",o)]}),n("col-span",()=>[{values:Array.from({length:12},(o,u)=>`${u+1}`),valueThemeKeys:[]}]),n("col-start",()=>[{supportsNegative:!0,values:Array.from({length:13},(o,u)=>`${u+1}`),valueThemeKeys:["--grid-column-start"]}]),n("col-end",()=>[{supportsNegative:!0,values:Array.from({length:13},(o,u)=>`${u+1}`),valueThemeKeys:["--grid-column-end"]}]),e("row-auto",[["grid-row","auto"]]),i("row",{supportsNegative:!0,handleBareValue:({value:o})=>T(o)?o:null,themeKeys:["--grid-row"],handle:o=>[s("grid-row",o)]}),e("row-span-full",[["grid-row","1 / -1"]]),i("row-span",{themeKeys:[],handleBareValue:({value:o})=>T(o)?o:null,handle:o=>[s("grid-row",`span ${o} / span ${o}`)]}),e("row-start-auto",[["grid-row-start","auto"]]),i("row-start",{supportsNegative:!0,handleBareValue:({value:o})=>T(o)?o:null,themeKeys:["--grid-row-start"],handle:o=>[s("grid-row-start",o)]}),e("row-end-auto",[["grid-row-end","auto"]]),i("row-end",{supportsNegative:!0,handleBareValue:({value:o})=>T(o)?o:null,themeKeys:["--grid-row-end"],handle:o=>[s("grid-row-end",o)]}),n("row-span",()=>[{values:Array.from({length:12},(o,u)=>`${u+1}`),valueThemeKeys:[]}]),n("row-start",()=>[{supportsNegative:!0,values:Array.from({length:13},(o,u)=>`${u+1}`),valueThemeKeys:["--grid-row-start"]}]),n("row-end",()=>[{supportsNegative:!0,values:Array.from({length:13},(o,u)=>`${u+1}`),valueThemeKeys:["--grid-row-end"]}]),e("float-start",[["float","inline-start"]]),e("float-end",[["float","inline-end"]]),e("float-right",[["float","right"]]),e("float-left",[["float","left"]]),e("float-none",[["float","none"]]),e("clear-start",[["clear","inline-start"]]),e("clear-end",[["clear","inline-end"]]),e("clear-right",[["clear","right"]]),e("clear-left",[["clear","left"]]),e("clear-both",[["clear","both"]]),e("clear-none",[["clear","none"]]);for(let[o,u]of[["m","margin"],["mx","margin-inline"],["my","margin-block"],["ms","margin-inline-start"],["me","margin-inline-end"],["mt","margin-top"],["mr","margin-right"],["mb","margin-bottom"],["ml","margin-left"]])e(`${o}-auto`,[[u,"auto"]]),l(o,["--margin","--spacing"],h=>[s(u,h)],{supportsNegative:!0});e("box-border",[["box-sizing","border-box"]]),e("box-content",[["box-sizing","content-box"]]),e("line-clamp-none",[["overflow","visible"],["display","block"],["-webkit-box-orient","horizontal"],["-webkit-line-clamp","unset"]]),i("line-clamp",{themeKeys:["--line-clamp"],handleBareValue:({value:o})=>T(o)?o:null,handle:o=>[s("overflow","hidden"),s("display","-webkit-box"),s("-webkit-box-orient","vertical"),s("-webkit-line-clamp",o)]}),n("line-clamp",()=>[{values:["1","2","3","4","5","6"],valueThemeKeys:["--line-clamp"]}]),e("block",[["display","block"]]),e("inline-block",[["display","inline-block"]]),e("inline",[["display","inline"]]),e("hidden",[["display","none"]]),e("inline-flex",[["display","inline-flex"]]),e("table",[["display","table"]]),e("inline-table",[["display","inline-table"]]),e("table-caption",[["display","table-caption"]]),e("table-cell",[["display","table-cell"]]),e("table-column",[["display","table-column"]]),e("table-column-group",[["display","table-column-group"]]),e("table-footer-group",[["display","table-footer-group"]]),e("table-header-group",[["display","table-header-group"]]),e("table-row-group",[["display","table-row-group"]]),e("table-row",[["display","table-row"]]),e("flow-root",[["display","flow-root"]]),e("flex",[["display","flex"]]),e("grid",[["display","grid"]]),e("inline-grid",[["display","inline-grid"]]),e("contents",[["display","contents"]]),e("list-item",[["display","list-item"]]),e("field-sizing-content",[["field-sizing","content"]]),e("field-sizing-fixed",[["field-sizing","fixed"]]),e("aspect-auto",[["aspect-ratio","auto"]]),e("aspect-square",[["aspect-ratio","1 / 1"]]),i("aspect",{themeKeys:["--aspect"],handleBareValue:({fraction:o})=>{if(o===null)return null;let[u,h]=O(o,"/");return!T(u)||!T(h)?null:o},handle:o=>[s("aspect-ratio",o)]});for(let[o,u]of[["auto","auto"],["full","100%"],["svw","100svw"],["lvw","100lvw"],["dvw","100dvw"],["svh","100svh"],["lvh","100lvh"],["dvh","100dvh"],["min","min-content"],["max","max-content"],["fit","fit-content"]])e(`size-${o}`,[["--tw-sort","size"],["width",u],["height",u]]),e(`w-${o}`,[["width",u]]),e(`h-${o}`,[["height",u]]),e(`min-w-${o}`,[["min-width",u]]),e(`min-h-${o}`,[["min-height",u]]),o!=="auto"&&(e(`max-w-${o}`,[["max-width",u]]),e(`max-h-${o}`,[["max-height",u]]));e("w-screen",[["width","100vw"]]),e("min-w-screen",[["min-width","100vw"]]),e("max-w-screen",[["max-width","100vw"]]),e("h-screen",[["height","100vh"]]),e("min-h-screen",[["min-height","100vh"]]),e("max-h-screen",[["max-height","100vh"]]),e("max-w-none",[["max-width","none"]]),e("max-h-none",[["max-height","none"]]),l("size",["--size","--spacing"],o=>[s("--tw-sort","size"),s("width",o),s("height",o)],{supportsFractions:!0});for(let[o,u,h]of[["w",["--width","--spacing","--container"],"width"],["min-w",["--min-width","--spacing","--container"],"min-width"],["max-w",["--max-width","--spacing","--container"],"max-width"],["h",["--height","--spacing"],"height"],["min-h",["--min-height","--height","--spacing"],"min-height"],["max-h",["--max-height","--height","--spacing"],"max-height"]])l(o,u,w=>[s(h,w)],{supportsFractions:!0});r.static("container",()=>{let o=[...t.namespace("--breakpoint").values()];o.sort((h,w)=>ce(h,w,"asc"));let u=[s("--tw-sort","--tw-container-component"),s("width","100%")];for(let h of o)u.push(U("@media",`(width >= ${h})`,[s("max-width",h)]));return u}),e("flex-auto",[["flex","auto"]]),e("flex-initial",[["flex","0 auto"]]),e("flex-none",[["flex","none"]]),r.functional("flex",o=>{if(o.value){if(o.value.kind==="arbitrary")return o.modifier?void 0:[s("flex",o.value.value)];if(o.value.fraction){let[u,h]=O(o.value.fraction,"/");return!T(u)||!T(h)?void 0:[s("flex",`calc(${o.value.fraction} * 100%)`)]}if(T(o.value.value))return o.modifier?void 0:[s("flex",o.value.value)]}}),n("flex",()=>[{supportsFractions:!0}]),i("shrink",{defaultValue:"1",handleBareValue:({value:o})=>T(o)?o:null,handle:o=>[s("flex-shrink",o)]}),i("grow",{defaultValue:"1",handleBareValue:({value:o})=>T(o)?o:null,handle:o=>[s("flex-grow",o)]}),n("shrink",()=>[{values:["0"],valueThemeKeys:[],hasDefaultValue:!0}]),n("grow",()=>[{values:["0"],valueThemeKeys:[],hasDefaultValue:!0}]),e("basis-auto",[["flex-basis","auto"]]),e("basis-full",[["flex-basis","100%"]]),l("basis",["--flex-basis","--spacing","--container"],o=>[s("flex-basis",o)],{supportsFractions:!0}),e("table-auto",[["table-layout","auto"]]),e("table-fixed",[["table-layout","fixed"]]),e("caption-top",[["caption-side","top"]]),e("caption-bottom",[["caption-side","bottom"]]),e("border-collapse",[["border-collapse","collapse"]]),e("border-separate",[["border-collapse","separate"]]);let p=()=>D([$("--tw-border-spacing-x","0","<length>"),$("--tw-border-spacing-y","0","<length>")]);l("border-spacing",["--border-spacing","--spacing"],o=>[p(),s("--tw-border-spacing-x",o),s("--tw-border-spacing-y",o),s("border-spacing","var(--tw-border-spacing-x) var(--tw-border-spacing-y)")]),l("border-spacing-x",["--border-spacing","--spacing"],o=>[p(),s("--tw-border-spacing-x",o),s("border-spacing","var(--tw-border-spacing-x) var(--tw-border-spacing-y)")]),l("border-spacing-y",["--border-spacing","--spacing"],o=>[p(),s("--tw-border-spacing-y",o),s("border-spacing","var(--tw-border-spacing-x) var(--tw-border-spacing-y)")]),e("origin-center",[["transform-origin","center"]]),e("origin-top",[["transform-origin","top"]]),e("origin-top-right",[["transform-origin","top right"]]),e("origin-right",[["transform-origin","right"]]),e("origin-bottom-right",[["transform-origin","bottom right"]]),e("origin-bottom",[["transform-origin","bottom"]]),e("origin-bottom-left",[["transform-origin","bottom left"]]),e("origin-left",[["transform-origin","left"]]),e("origin-top-left",[["transform-origin","top left"]]),i("origin",{themeKeys:["--transform-origin"],handle:o=>[s("transform-origin",o)]}),e("perspective-origin-center",[["perspective-origin","center"]]),e("perspective-origin-top",[["perspective-origin","top"]]),e("perspective-origin-top-right",[["perspective-origin","top right"]]),e("perspective-origin-right",[["perspective-origin","right"]]),e("perspective-origin-bottom-right",[["perspective-origin","bottom right"]]),e("perspective-origin-bottom",[["perspective-origin","bottom"]]),e("perspective-origin-bottom-left",[["perspective-origin","bottom left"]]),e("perspective-origin-left",[["perspective-origin","left"]]),e("perspective-origin-top-left",[["perspective-origin","top left"]]),i("perspective-origin",{themeKeys:["--perspective-origin"],handle:o=>[s("perspective-origin",o)]}),e("perspective-none",[["perspective","none"]]),i("perspective",{themeKeys:["--perspective"],handle:o=>[s("perspective",o)]});let c=()=>D([$("--tw-translate-x","0"),$("--tw-translate-y","0"),$("--tw-translate-z","0")]);e("translate-none",[["translate","none"]]),e("-translate-full",[c,["--tw-translate-x","-100%"],["--tw-translate-y","-100%"],["translate","var(--tw-translate-x) var(--tw-translate-y)"]]),e("translate-full",[c,["--tw-translate-x","100%"],["--tw-translate-y","100%"],["translate","var(--tw-translate-x) var(--tw-translate-y)"]]),l("translate",["--translate","--spacing"],o=>[c(),s("--tw-translate-x",o),s("--tw-translate-y",o),s("translate","var(--tw-translate-x) var(--tw-translate-y)")],{supportsNegative:!0,supportsFractions:!0});for(let o of["x","y"])e(`-translate-${o}-full`,[c,[`--tw-translate-${o}`,"-100%"],["translate","var(--tw-translate-x) var(--tw-translate-y)"]]),e(`translate-${o}-full`,[c,[`--tw-translate-${o}`,"100%"],["translate","var(--tw-translate-x) var(--tw-translate-y)"]]),l(`translate-${o}`,["--translate","--spacing"],u=>[c(),s(`--tw-translate-${o}`,u),s("translate","var(--tw-translate-x) var(--tw-translate-y)")],{supportsNegative:!0,supportsFractions:!0});l("translate-z",["--translate","--spacing"],o=>[c(),s("--tw-translate-z",o),s("translate","var(--tw-translate-x) var(--tw-translate-y) var(--tw-translate-z)")],{supportsNegative:!0}),e("translate-3d",[c,["translate","var(--tw-translate-x) var(--tw-translate-y) var(--tw-translate-z)"]]);let d=()=>D([$("--tw-scale-x","1"),$("--tw-scale-y","1"),$("--tw-scale-z","1")]);e("scale-none",[["scale","none"]]);function f({negative:o}){return u=>{if(!u.value||u.modifier)return;let h;return u.value.kind==="arbitrary"?(h=u.value.value,[s("scale",h)]):(h=t.resolve(u.value.value,["--scale"]),!h&&T(u.value.value)&&(h=`${u.value.value}%`),h?(h=o?`calc(${h} * -1)`:h,[d(),s("--tw-scale-x",h),s("--tw-scale-y",h),s("--tw-scale-z",h),s("scale","var(--tw-scale-x) var(--tw-scale-y)")]):void 0)}}r.functional("-scale",f({negative:!0})),r.functional("scale",f({negative:!1})),n("scale",()=>[{supportsNegative:!0,values:["0","50","75","90","95","100","105","110","125","150","200"],valueThemeKeys:["--scale"]}]);for(let o of["x","y","z"])i(`scale-${o}`,{supportsNegative:!0,themeKeys:["--scale"],handleBareValue:({value:u})=>T(u)?`${u}%`:null,handle:u=>[d(),s(`--tw-scale-${o}`,u),s("scale",`var(--tw-scale-x) var(--tw-scale-y)${o==="z"?" var(--tw-scale-z)":""}`)]}),n(`scale-${o}`,()=>[{supportsNegative:!0,values:["0","50","75","90","95","100","105","110","125","150","200"],valueThemeKeys:["--scale"]}]);e("scale-3d",[d,["scale","var(--tw-scale-x) var(--tw-scale-y) var(--tw-scale-z)"]]),e("rotate-none",[["rotate","none"]]);function m({negative:o}){return u=>{if(!u.value||u.modifier)return;let h;if(u.value.kind==="arbitrary"){h=u.value.value;let w=u.value.dataType??L(h,["angle","vector"]);if(w==="vector")return[s("rotate",`${h} var(--tw-rotate)`)];if(w!=="angle")return[s("rotate",h)]}else if(h=t.resolve(u.value.value,["--rotate"]),!h&&T(u.value.value)&&(h=`${u.value.value}deg`),!h)return;return[s("rotate",o?`calc(${h} * -1)`:h)]}}r.functional("-rotate",m({negative:!0})),r.functional("rotate",m({negative:!1})),n("rotate",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12","45","90","180"],valueThemeKeys:["--rotate"]}]);{let o=["var(--tw-rotate-x)","var(--tw-rotate-y)","var(--tw-rotate-z)","var(--tw-skew-x)","var(--tw-skew-y)"].join(" "),u=()=>D([$("--tw-rotate-x","rotateX(0)"),$("--tw-rotate-y","rotateY(0)"),$("--tw-rotate-z","rotateZ(0)"),$("--tw-skew-x","skewX(0)"),$("--tw-skew-y","skewY(0)")]);for(let h of["x","y","z"])i(`rotate-${h}`,{supportsNegative:!0,themeKeys:["--rotate"],handleBareValue:({value:w})=>T(w)?`${w}deg`:null,handle:w=>[u(),s(`--tw-rotate-${h}`,`rotate${h.toUpperCase()}(${w})`),s("transform",o)]}),n(`rotate-${h}`,()=>[{supportsNegative:!0,values:["0","1","2","3","6","12","45","90","180"],valueThemeKeys:["--rotate"]}]);i("skew",{supportsNegative:!0,themeKeys:["--skew"],handleBareValue:({value:h})=>T(h)?`${h}deg`:null,handle:h=>[u(),s("--tw-skew-x",`skewX(${h})`),s("--tw-skew-y",`skewY(${h})`),s("transform",o)]}),i("skew-x",{supportsNegative:!0,themeKeys:["--skew"],handleBareValue:({value:h})=>T(h)?`${h}deg`:null,handle:h=>[u(),s("--tw-skew-x",`skewX(${h})`),s("transform",o)]}),i("skew-y",{supportsNegative:!0,themeKeys:["--skew"],handleBareValue:({value:h})=>T(h)?`${h}deg`:null,handle:h=>[u(),s("--tw-skew-y",`skewY(${h})`),s("transform",o)]}),n("skew",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12"],valueThemeKeys:["--skew"]}]),n("skew-x",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12"],valueThemeKeys:["--skew"]}]),n("skew-y",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12"],valueThemeKeys:["--skew"]}]),r.functional("transform",h=>{if(h.modifier)return;let w=null;if(h.value?h.value.kind==="arbitrary"&&(w=h.value.value):w=o,w!==null)return[u(),s("transform",w)]}),n("transform",()=>[{hasDefaultValue:!0}]),e("transform-cpu",[["transform",o]]),e("transform-gpu",[["transform",`translateZ(0) ${o}`]]),e("transform-none",[["transform","none"]])}e("transform-flat",[["transform-style","flat"]]),e("transform-3d",[["transform-style","preserve-3d"]]),e("transform-content",[["transform-box","content-box"]]),e("transform-border",[["transform-box","border-box"]]),e("transform-fill",[["transform-box","fill-box"]]),e("transform-stroke",[["transform-box","stroke-box"]]),e("transform-view",[["transform-box","view-box"]]),e("backface-visible",[["backface-visibility","visible"]]),e("backface-hidden",[["backface-visibility","hidden"]]);for(let o of["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out"])e(`cursor-${o}`,[["cursor",o]]);i("cursor",{themeKeys:["--cursor"],handle:o=>[s("cursor",o)]});for(let o of["auto","none","manipulation"])e(`touch-${o}`,[["touch-action",o]]);let g=()=>D([$("--tw-pan-x"),$("--tw-pan-y"),$("--tw-pinch-zoom")]);for(let o of["x","left","right"])e(`touch-pan-${o}`,[g,["--tw-pan-x",`pan-${o}`],["touch-action","var(--tw-pan-x,) var(--tw-pan-y,) var(--tw-pinch-zoom,)"]]);for(let o of["y","up","down"])e(`touch-pan-${o}`,[g,["--tw-pan-y",`pan-${o}`],["touch-action","var(--tw-pan-x,) var(--tw-pan-y,) var(--tw-pinch-zoom,)"]]);e("touch-pinch-zoom",[g,["--tw-pinch-zoom","pinch-zoom"],["touch-action","var(--tw-pan-x,) var(--tw-pan-y,) var(--tw-pinch-zoom,)"]]);for(let o of["none","text","all","auto"])e(`select-${o}`,[["-webkit-user-select",o],["user-select",o]]);e("resize-none",[["resize","none"]]),e("resize-x",[["resize","horizontal"]]),e("resize-y",[["resize","vertical"]]),e("resize",[["resize","both"]]),e("snap-none",[["scroll-snap-type","none"]]);let y=()=>D([$("--tw-scroll-snap-strictness","proximity","*")]);for(let o of["x","y","both"])e(`snap-${o}`,[y,["scroll-snap-type",`${o} var(--tw-scroll-snap-strictness)`]]);e("snap-mandatory",[y,["--tw-scroll-snap-strictness","mandatory"]]),e("snap-proximity",[y,["--tw-scroll-snap-strictness","proximity"]]),e("snap-align-none",[["scroll-snap-align","none"]]),e("snap-start",[["scroll-snap-align","start"]]),e("snap-end",[["scroll-snap-align","end"]]),e("snap-center",[["scroll-snap-align","center"]]),e("snap-normal",[["scroll-snap-stop","normal"]]),e("snap-always",[["scroll-snap-stop","always"]]);for(let[o,u]of[["scroll-m","scroll-margin"],["scroll-mx","scroll-margin-inline"],["scroll-my","scroll-margin-block"],["scroll-ms","scroll-margin-inline-start"],["scroll-me","scroll-margin-inline-end"],["scroll-mt","scroll-margin-top"],["scroll-mr","scroll-margin-right"],["scroll-mb","scroll-margin-bottom"],["scroll-ml","scroll-margin-left"]])l(o,["--scroll-margin","--spacing"],h=>[s(u,h)],{supportsNegative:!0});for(let[o,u]of[["scroll-p","scroll-padding"],["scroll-px","scroll-padding-inline"],["scroll-py","scroll-padding-block"],["scroll-ps","scroll-padding-inline-start"],["scroll-pe","scroll-padding-inline-end"],["scroll-pt","scroll-padding-top"],["scroll-pr","scroll-padding-right"],["scroll-pb","scroll-padding-bottom"],["scroll-pl","scroll-padding-left"]])l(o,["--scroll-padding","--spacing"],h=>[s(u,h)]);e("list-inside",[["list-style-position","inside"]]),e("list-outside",[["list-style-position","outside"]]),e("list-none",[["list-style-type","none"]]),e("list-disc",[["list-style-type","disc"]]),e("list-decimal",[["list-style-type","decimal"]]),i("list",{themeKeys:["--list-style-type"],handle:o=>[s("list-style-type",o)]}),e("list-image-none",[["list-style-image","none"]]),i("list-image",{themeKeys:["--list-style-image"],handle:o=>[s("list-style-image",o)]}),e("appearance-none",[["appearance","none"]]),e("appearance-auto",[["appearance","auto"]]),e("scheme-normal",[["color-scheme","normal"]]),e("scheme-dark",[["color-scheme","dark"]]),e("scheme-light",[["color-scheme","light"]]),e("scheme-light-dark",[["color-scheme","light dark"]]),e("scheme-only-dark",[["color-scheme","only dark"]]),e("scheme-only-light",[["color-scheme","only light"]]),e("columns-auto",[["columns","auto"]]),i("columns",{themeKeys:["--columns","--container"],handleBareValue:({value:o})=>T(o)?o:null,handle:o=>[s("columns",o)]}),n("columns",()=>[{values:Array.from({length:12},(o,u)=>`${u+1}`),valueThemeKeys:["--columns","--container"]}]);for(let o of["auto","avoid","all","avoid-page","page","left","right","column"])e(`break-before-${o}`,[["break-before",o]]);for(let o of["auto","avoid","avoid-page","avoid-column"])e(`break-inside-${o}`,[["break-inside",o]]);for(let o of["auto","avoid","all","avoid-page","page","left","right","column"])e(`break-after-${o}`,[["break-after",o]]);e("grid-flow-row",[["grid-auto-flow","row"]]),e("grid-flow-col",[["grid-auto-flow","column"]]),e("grid-flow-dense",[["grid-auto-flow","dense"]]),e("grid-flow-row-dense",[["grid-auto-flow","row dense"]]),e("grid-flow-col-dense",[["grid-auto-flow","column dense"]]),e("auto-cols-auto",[["grid-auto-columns","auto"]]),e("auto-cols-min",[["grid-auto-columns","min-content"]]),e("auto-cols-max",[["grid-auto-columns","max-content"]]),e("auto-cols-fr",[["grid-auto-columns","minmax(0, 1fr)"]]),i("auto-cols",{themeKeys:["--grid-auto-columns"],handle:o=>[s("grid-auto-columns",o)]}),e("auto-rows-auto",[["grid-auto-rows","auto"]]),e("auto-rows-min",[["grid-auto-rows","min-content"]]),e("auto-rows-max",[["grid-auto-rows","max-content"]]),e("auto-rows-fr",[["grid-auto-rows","minmax(0, 1fr)"]]),i("auto-rows",{themeKeys:["--grid-auto-rows"],handle:o=>[s("grid-auto-rows",o)]}),e("grid-cols-none",[["grid-template-columns","none"]]),e("grid-cols-subgrid",[["grid-template-columns","subgrid"]]),i("grid-cols",{themeKeys:["--grid-template-columns"],handleBareValue:({value:o})=>gt(o)?`repeat(${o}, minmax(0, 1fr))`:null,handle:o=>[s("grid-template-columns",o)]}),e("grid-rows-none",[["grid-template-rows","none"]]),e("grid-rows-subgrid",[["grid-template-rows","subgrid"]]),i("grid-rows",{themeKeys:["--grid-template-rows"],handleBareValue:({value:o})=>gt(o)?`repeat(${o}, minmax(0, 1fr))`:null,handle:o=>[s("grid-template-rows",o)]}),n("grid-cols",()=>[{values:Array.from({length:12},(o,u)=>`${u+1}`),valueThemeKeys:["--grid-template-columns"]}]),n("grid-rows",()=>[{values:Array.from({length:12},(o,u)=>`${u+1}`),valueThemeKeys:["--grid-template-rows"]}]),e("flex-row",[["flex-direction","row"]]),e("flex-row-reverse",[["flex-direction","row-reverse"]]),e("flex-col",[["flex-direction","column"]]),e("flex-col-reverse",[["flex-direction","column-reverse"]]),e("flex-wrap",[["flex-wrap","wrap"]]),e("flex-nowrap",[["flex-wrap","nowrap"]]),e("flex-wrap-reverse",[["flex-wrap","wrap-reverse"]]),e("place-content-center",[["place-content","center"]]),e("place-content-start",[["place-content","start"]]),e("place-content-end",[["place-content","end"]]),!1,e("place-content-between",[["place-content","space-between"]]),e("place-content-around",[["place-content","space-around"]]),e("place-content-evenly",[["place-content","space-evenly"]]),e("place-content-baseline",[["place-content","baseline"]]),e("place-content-stretch",[["place-content","stretch"]]),e("place-items-center",[["place-items","center"]]),e("place-items-start",[["place-items","start"]]),e("place-items-end",[["place-items","end"]]),!1,e("place-items-baseline",[["place-items","baseline"]]),e("place-items-stretch",[["place-items","stretch"]]),e("content-normal",[["align-content","normal"]]),e("content-center",[["align-content","center"]]),e("content-start",[["align-content","flex-start"]]),e("content-end",[["align-content","flex-end"]]),!1,e("content-between",[["align-content","space-between"]]),e("content-around",[["align-content","space-around"]]),e("content-evenly",[["align-content","space-evenly"]]),e("content-baseline",[["align-content","baseline"]]),e("content-stretch",[["align-content","stretch"]]),e("items-center",[["align-items","center"]]),e("items-start",[["align-items","flex-start"]]),e("items-end",[["align-items","flex-end"]]),!1,e("items-baseline",[["align-items","baseline"]]),!1,e("items-stretch",[["align-items","stretch"]]),e("justify-normal",[["justify-content","normal"]]),e("justify-center",[["justify-content","center"]]),e("justify-start",[["justify-content","flex-start"]]),e("justify-end",[["justify-content","flex-end"]]),!1,e("justify-between",[["justify-content","space-between"]]),e("justify-around",[["justify-content","space-around"]]),e("justify-evenly",[["justify-content","space-evenly"]]),e("justify-baseline",[["justify-content","baseline"]]),e("justify-stretch",[["justify-content","stretch"]]),e("justify-items-normal",[["justify-items","normal"]]),e("justify-items-center",[["justify-items","center"]]),e("justify-items-start",[["justify-items","start"]]),e("justify-items-end",[["justify-items","end"]]),!1,e("justify-items-stretch",[["justify-items","stretch"]]),l("gap",["--gap","--spacing"],o=>[s("gap",o)]),l("gap-x",["--gap","--spacing"],o=>[s("column-gap",o)]),l("gap-y",["--gap","--spacing"],o=>[s("row-gap",o)]),l("space-x",["--space","--spacing"],o=>[D([$("--tw-space-x-reverse","0")]),j(":where(& > :not(:last-child))",[s("--tw-sort","row-gap"),s("--tw-space-x-reverse","0"),s("margin-inline-start",`calc(${o} * var(--tw-space-x-reverse))`),s("margin-inline-end",`calc(${o} * calc(1 - var(--tw-space-x-reverse)))`)])],{supportsNegative:!0}),l("space-y",["--space","--spacing"],o=>[D([$("--tw-space-y-reverse","0")]),j(":where(& > :not(:last-child))",[s("--tw-sort","column-gap"),s("--tw-space-y-reverse","0"),s("margin-block-start",`calc(${o} * var(--tw-space-y-reverse))`),s("margin-block-end",`calc(${o} * calc(1 - var(--tw-space-y-reverse)))`)])],{supportsNegative:!0}),e("space-x-reverse",[()=>D([$("--tw-space-x-reverse","0")]),()=>j(":where(& > :not(:last-child))",[s("--tw-sort","row-gap"),s("--tw-space-x-reverse","1")])]),e("space-y-reverse",[()=>D([$("--tw-space-y-reverse","0")]),()=>j(":where(& > :not(:last-child))",[s("--tw-sort","column-gap"),s("--tw-space-y-reverse","1")])]),e("accent-auto",[["accent-color","auto"]]),a("accent",{themeKeys:["--accent-color","--color"],handle:o=>[s("accent-color",o)]}),a("caret",{themeKeys:["--caret-color","--color"],handle:o=>[s("caret-color",o)]}),a("divide",{themeKeys:["--divide-color","--color"],handle:o=>[j(":where(& > :not(:last-child))",[s("--tw-sort","divide-color"),s("border-color",o)])]}),e("place-self-auto",[["place-self","auto"]]),e("place-self-start",[["place-self","start"]]),e("place-self-end",[["place-self","end"]]),e("place-self-center",[["place-self","center"]]),!1,e("place-self-stretch",[["place-self","stretch"]]),e("self-auto",[["align-self","auto"]]),e("self-start",[["align-self","flex-start"]]),e("self-end",[["align-self","flex-end"]]),e("self-center",[["align-self","center"]]),!1,e("self-stretch",[["align-self","stretch"]]),e("self-baseline",[["align-self","baseline"]]),e("justify-self-auto",[["justify-self","auto"]]),e("justify-self-start",[["justify-self","flex-start"]]),e("justify-self-end",[["justify-self","flex-end"]]),e("justify-self-center",[["justify-self","center"]]),!1,e("justify-self-stretch",[["justify-self","stretch"]]);for(let o of["auto","hidden","clip","visible","scroll"])e(`overflow-${o}`,[["overflow",o]]),e(`overflow-x-${o}`,[["overflow-x",o]]),e(`overflow-y-${o}`,[["overflow-y",o]]);for(let o of["auto","contain","none"])e(`overscroll-${o}`,[["overscroll-behavior",o]]),e(`overscroll-x-${o}`,[["overscroll-behavior-x",o]]),e(`overscroll-y-${o}`,[["overscroll-behavior-y",o]]);e("scroll-auto",[["scroll-behavior","auto"]]),e("scroll-smooth",[["scroll-behavior","smooth"]]),e("truncate",[["overflow","hidden"],["text-overflow","ellipsis"],["white-space","nowrap"]]),e("text-ellipsis",[["text-overflow","ellipsis"]]),e("text-clip",[["text-overflow","clip"]]),e("hyphens-none",[["-webkit-hyphens","none"],["hyphens","none"]]),e("hyphens-manual",[["-webkit-hyphens","manual"],["hyphens","manual"]]),e("hyphens-auto",[["-webkit-hyphens","auto"],["hyphens","auto"]]),e("whitespace-normal",[["white-space","normal"]]),e("whitespace-nowrap",[["white-space","nowrap"]]),e("whitespace-pre",[["white-space","pre"]]),e("whitespace-pre-line",[["white-space","pre-line"]]),e("whitespace-pre-wrap",[["white-space","pre-wrap"]]),e("whitespace-break-spaces",[["white-space","break-spaces"]]),e("text-wrap",[["text-wrap","wrap"]]),e("text-nowrap",[["text-wrap","nowrap"]]),e("text-balance",[["text-wrap","balance"]]),e("text-pretty",[["text-wrap","pretty"]]),e("break-normal",[["overflow-wrap","normal"],["word-break","normal"]]),e("break-words",[["overflow-wrap","break-word"]]),e("break-all",[["word-break","break-all"]]),e("break-keep",[["word-break","keep-all"]]),!1;for(let[o,u]of[["rounded",["border-radius"]],["rounded-s",["border-start-start-radius","border-end-start-radius"]],["rounded-e",["border-start-end-radius","border-end-end-radius"]],["rounded-t",["border-top-left-radius","border-top-right-radius"]],["rounded-r",["border-top-right-radius","border-bottom-right-radius"]],["rounded-b",["border-bottom-right-radius","border-bottom-left-radius"]],["rounded-l",["border-top-left-radius","border-bottom-left-radius"]],["rounded-ss",["border-start-start-radius"]],["rounded-se",["border-start-end-radius"]],["rounded-ee",["border-end-end-radius"]],["rounded-es",["border-end-start-radius"]],["rounded-tl",["border-top-left-radius"]],["rounded-tr",["border-top-right-radius"]],["rounded-br",["border-bottom-right-radius"]],["rounded-bl",["border-bottom-left-radius"]]])e(`${o}-none`,u.map(h=>[h,"0"])),e(`${o}-full`,u.map(h=>[h,"calc(infinity * 1px)"])),i(o,{themeKeys:["--radius"],handle:h=>u.map(w=>s(w,h))});e("border-solid",[["--tw-border-style","solid"],["border-style","solid"]]),e("border-dashed",[["--tw-border-style","dashed"],["border-style","dashed"]]),e("border-dotted",[["--tw-border-style","dotted"],["border-style","dotted"]]),e("border-double",[["--tw-border-style","double"],["border-style","double"]]),e("border-hidden",[["--tw-border-style","hidden"],["border-style","hidden"]]),e("border-none",[["--tw-border-style","none"],["border-style","none"]]);{let u=function(h,w){r.functional(h,v=>{if(!v.value){if(v.modifier)return;let N=t.get(["--default-border-width"])??"1px",E=w.width(N);return E?[o(),...E]:void 0}if(v.value.kind==="arbitrary"){let N=v.value.value;switch(v.value.dataType??L(N,["color","line-width","length"])){case"line-width":case"length":{if(v.modifier)return;let S=w.width(N);return S?[o(),...S]:void 0}default:return N=W(N,v.modifier,t),N===null?void 0:w.color(N)}}{let N=G(v,t,["--border-color","--color"]);if(N)return w.color(N)}{if(v.modifier)return;let N=t.resolve(v.value.value,["--border-width"]);if(N){let E=w.width(N);return E?[o(),...E]:void 0}if(T(v.value.value)){let E=w.width(`${v.value.value}px`);return E?[o(),...E]:void 0}}}),n(h,()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--border-color","--color"],modifiers:Array.from({length:21},(v,N)=>`${N*5}`),hasDefaultValue:!0},{values:["0","2","4","8"],valueThemeKeys:["--border-width"]}])};var A=u;let o=()=>D([$("--tw-border-style","solid")]);u("border",{width:h=>[s("border-style","var(--tw-border-style)"),s("border-width",h)],color:h=>[s("border-color",h)]}),u("border-x",{width:h=>[s("border-inline-style","var(--tw-border-style)"),s("border-inline-width",h)],color:h=>[s("border-inline-color",h)]}),u("border-y",{width:h=>[s("border-block-style","var(--tw-border-style)"),s("border-block-width",h)],color:h=>[s("border-block-color",h)]}),u("border-s",{width:h=>[s("border-inline-start-style","var(--tw-border-style)"),s("border-inline-start-width",h)],color:h=>[s("border-inline-start-color",h)]}),u("border-e",{width:h=>[s("border-inline-end-style","var(--tw-border-style)"),s("border-inline-end-width",h)],color:h=>[s("border-inline-end-color",h)]}),u("border-t",{width:h=>[s("border-top-style","var(--tw-border-style)"),s("border-top-width",h)],color:h=>[s("border-top-color",h)]}),u("border-r",{width:h=>[s("border-right-style","var(--tw-border-style)"),s("border-right-width",h)],color:h=>[s("border-right-color",h)]}),u("border-b",{width:h=>[s("border-bottom-style","var(--tw-border-style)"),s("border-bottom-width",h)],color:h=>[s("border-bottom-color",h)]}),u("border-l",{width:h=>[s("border-left-style","var(--tw-border-style)"),s("border-left-width",h)],color:h=>[s("border-left-color",h)]}),i("divide-x",{defaultValue:t.get(["--default-border-width"])??"1px",themeKeys:["--divide-width","--border-width"],handleBareValue:({value:h})=>T(h)?`${h}px`:null,handle:h=>[D([$("--tw-divide-x-reverse","0")]),j(":where(& > :not(:last-child))",[s("--tw-sort","divide-x-width"),o(),s("--tw-divide-x-reverse","0"),s("border-inline-style","var(--tw-border-style)"),s("border-inline-start-width",`calc(${h} * var(--tw-divide-x-reverse))`),s("border-inline-end-width",`calc(${h} * calc(1 - var(--tw-divide-x-reverse)))`)])]}),i("divide-y",{defaultValue:t.get(["--default-border-width"])??"1px",themeKeys:["--divide-width","--border-width"],handleBareValue:({value:h})=>T(h)?`${h}px`:null,handle:h=>[D([$("--tw-divide-y-reverse","0")]),j(":where(& > :not(:last-child))",[s("--tw-sort","divide-y-width"),o(),s("--tw-divide-y-reverse","0"),s("border-bottom-style","var(--tw-border-style)"),s("border-top-style","var(--tw-border-style)"),s("border-top-width",`calc(${h} * var(--tw-divide-y-reverse))`),s("border-bottom-width",`calc(${h} * calc(1 - var(--tw-divide-y-reverse)))`)])]}),n("divide-x",()=>[{values:["0","2","4","8"],valueThemeKeys:["--divide-width","--border-width"],hasDefaultValue:!0}]),n("divide-y",()=>[{values:["0","2","4","8"],valueThemeKeys:["--divide-width","--border-width"],hasDefaultValue:!0}]),e("divide-x-reverse",[()=>D([$("--tw-divide-x-reverse","0")]),()=>j(":where(& > :not(:last-child))",[s("--tw-divide-x-reverse","1")])]),e("divide-y-reverse",[()=>D([$("--tw-divide-y-reverse","0")]),()=>j(":where(& > :not(:last-child))",[s("--tw-divide-y-reverse","1")])]);for(let h of["solid","dashed","dotted","double","none"])e(`divide-${h}`,[()=>j(":where(& > :not(:last-child))",[s("--tw-sort","divide-style"),s("--tw-border-style",h),s("border-style",h)])])}e("bg-auto",[["background-size","auto"]]),e("bg-cover",[["background-size","cover"]]),e("bg-contain",[["background-size","contain"]]),e("bg-fixed",[["background-attachment","fixed"]]),e("bg-local",[["background-attachment","local"]]),e("bg-scroll",[["background-attachment","scroll"]]),e("bg-center",[["background-position","center"]]),e("bg-top",[["background-position","top"]]),e("bg-right-top",[["background-position","right top"]]),e("bg-right",[["background-position","right"]]),e("bg-right-bottom",[["background-position","right bottom"]]),e("bg-bottom",[["background-position","bottom"]]),e("bg-left-bottom",[["background-position","left bottom"]]),e("bg-left",[["background-position","left"]]),e("bg-left-top",[["background-position","left top"]]),e("bg-repeat",[["background-repeat","repeat"]]),e("bg-no-repeat",[["background-repeat","no-repeat"]]),e("bg-repeat-x",[["background-repeat","repeat-x"]]),e("bg-repeat-y",[["background-repeat","repeat-y"]]),e("bg-repeat-round",[["background-repeat","round"]]),e("bg-repeat-space",[["background-repeat","space"]]),e("bg-none",[["background-image","none"]]);{let h=function(N){let E="in oklab";if(N?.kind==="named")switch(N.value){case"longer":case"shorter":case"increasing":case"decreasing":E=`in oklch ${N.value} hue`;break;default:E=`in ${N.value}`}else N?.kind==="arbitrary"&&(E=N.value);return E},w=function({negative:N}){return E=>{if(!E.value)return;if(E.value.kind==="arbitrary"){if(E.modifier)return;let I=E.value.value;switch(E.value.dataType??L(I,["angle"])){case"angle":return I=N?`calc(${I} * -1)`:`${I}`,[s("--tw-gradient-position",I),s("background-image",`linear-gradient(var(--tw-gradient-stops,${I}))`)];default:return N?void 0:[s("--tw-gradient-position",I),s("background-image",`linear-gradient(var(--tw-gradient-stops,${I}))`)]}}let S=E.value.value;if(!N&&u.has(S))S=u.get(S);else if(T(S))S=N?`calc(${S}deg * -1)`:`${S}deg`;else return;let C=h(E.modifier);return[s("--tw-gradient-position",`${S} ${C}`),s("background-image","linear-gradient(var(--tw-gradient-stops))")]}},v=function({negative:N}){return E=>{if(E.value?.kind==="arbitrary"){if(E.modifier)return;let I=E.value.value;return[s("--tw-gradient-position",I),s("background-image",`conic-gradient(var(--tw-gradient-stops,${I}))`)]}let S=h(E.modifier);if(!E.value)return[s("--tw-gradient-position",S),s("background-image","conic-gradient(var(--tw-gradient-stops))")];let C=E.value.value;if(T(C))return C=N?`calc(${C}deg * -1)`:`${C}deg`,[s("--tw-gradient-position",`from ${C} ${S}`),s("background-image","conic-gradient(var(--tw-gradient-stops))")]}};var x=h,V=w,P=v;let o=["oklab","oklch","srgb","hsl","longer","shorter","increasing","decreasing"],u=new Map([["to-t","to top"],["to-tr","to top right"],["to-r","to right"],["to-br","to bottom right"],["to-b","to bottom"],["to-bl","to bottom left"],["to-l","to left"],["to-tl","to top left"]]);r.functional("-bg-linear",w({negative:!0})),r.functional("bg-linear",w({negative:!1})),n("bg-linear",()=>[{values:[...u.keys()],modifiers:o},{values:["0","30","60","90","120","150","180","210","240","270","300","330"],supportsNegative:!0,modifiers:o}]),r.functional("-bg-conic",v({negative:!0})),r.functional("bg-conic",v({negative:!1})),n("bg-conic",()=>[{hasDefaultValue:!0,modifiers:o},{values:["0","30","60","90","120","150","180","210","240","270","300","330"],supportsNegative:!0,modifiers:o}]),r.functional("bg-radial",N=>{if(!N.value){let E=h(N.modifier);return[s("--tw-gradient-position",E),s("background-image","radial-gradient(var(--tw-gradient-stops))")]}if(N.value.kind==="arbitrary"){if(N.modifier)return;let E=N.value.value;return[s("--tw-gradient-position",E),s("background-image",`radial-gradient(var(--tw-gradient-stops,${E}))`)]}}),n("bg-radial",()=>[{hasDefaultValue:!0,modifiers:o}])}r.functional("bg",o=>{if(o.value){if(o.value.kind==="arbitrary"){let u=o.value.value;switch(o.value.dataType??L(u,["image","color","percentage","position","bg-size","length","url"])){case"percentage":case"position":return o.modifier?void 0:[s("background-position",u)];case"bg-size":case"length":case"size":return o.modifier?void 0:[s("background-size",u)];case"image":case"url":return o.modifier?void 0:[s("background-image",u)];default:return u=W(u,o.modifier,t),u===null?void 0:[s("background-color",u)]}}{let u=G(o,t,["--background-color","--color"]);if(u)return[s("background-color",u)]}{if(o.modifier)return;let u=t.resolve(o.value.value,["--background-image"]);if(u)return[s("background-image",u)]}}}),n("bg",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--background-color","--color"],modifiers:Array.from({length:21},(o,u)=>`${u*5}`)},{values:[],valueThemeKeys:["--background-image"]}]);let b=()=>D([$("--tw-gradient-position"),$("--tw-gradient-from","#0000","<color>"),$("--tw-gradient-via","#0000","<color>"),$("--tw-gradient-to","#0000","<color>"),$("--tw-gradient-stops"),$("--tw-gradient-via-stops"),$("--tw-gradient-from-position","0%","<length-percentage>"),$("--tw-gradient-via-position","50%","<length-percentage>"),$("--tw-gradient-to-position","100%","<length-percentage>")]);function k(o,u){r.functional(o,h=>{if(h.value){if(h.value.kind==="arbitrary"){let w=h.value.value;switch(h.value.dataType??L(w,["color","length","percentage"])){case"length":case"percentage":return h.modifier?void 0:u.position(w);default:return w=W(w,h.modifier,t),w===null?void 0:u.color(w)}}{let w=G(h,t,["--background-color","--color"]);if(w)return u.color(w)}{if(h.modifier)return;let w=t.resolve(h.value.value,["--gradient-color-stop-positions"]);if(w)return u.position(w);if(h.value.value[h.value.value.length-1]==="%"&&T(h.value.value.slice(0,-1)))return u.position(h.value.value)}}}),n(o,()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--background-color","--color"],modifiers:Array.from({length:21},(h,w)=>`${w*5}`)},{values:Array.from({length:21},(h,w)=>`${w*5}%`),valueThemeKeys:["--gradient-color-stop-positions"]}])}k("from",{color:o=>[b(),s("--tw-sort","--tw-gradient-from"),s("--tw-gradient-from",o),s("--tw-gradient-stops","var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position))")],position:o=>[b(),s("--tw-gradient-from-position",o)]}),e("via-none",[["--tw-gradient-via-stops","initial"]]),k("via",{color:o=>[b(),s("--tw-sort","--tw-gradient-via"),s("--tw-gradient-via",o),s("--tw-gradient-via-stops","var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position)"),s("--tw-gradient-stops","var(--tw-gradient-via-stops)")],position:o=>[b(),s("--tw-gradient-via-position",o)]}),k("to",{color:o=>[b(),s("--tw-sort","--tw-gradient-to"),s("--tw-gradient-to",o),s("--tw-gradient-stops","var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position))")],position:o=>[b(),s("--tw-gradient-to-position",o)]}),e("box-decoration-slice",[["-webkit-box-decoration-break","slice"],["box-decoration-break","slice"]]),e("box-decoration-clone",[["-webkit-box-decoration-break","clone"],["box-decoration-break","clone"]]),e("bg-clip-text",[["background-clip","text"]]),e("bg-clip-border",[["background-clip","border-box"]]),e("bg-clip-padding",[["background-clip","padding-box"]]),e("bg-clip-content",[["background-clip","content-box"]]),e("bg-origin-border",[["background-origin","border-box"]]),e("bg-origin-padding",[["background-origin","padding-box"]]),e("bg-origin-content",[["background-origin","content-box"]]);for(let o of["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"])e(`bg-blend-${o}`,[["background-blend-mode",o]]),e(`mix-blend-${o}`,[["mix-blend-mode",o]]);e("mix-blend-plus-darker",[["mix-blend-mode","plus-darker"]]),e("mix-blend-plus-lighter",[["mix-blend-mode","plus-lighter"]]),e("fill-none",[["fill","none"]]),r.functional("fill",o=>{if(!o.value)return;if(o.value.kind==="arbitrary"){let h=W(o.value.value,o.modifier,t);return h===null?void 0:[s("fill",h)]}let u=G(o,t,["--fill","--color"]);if(u)return[s("fill",u)]}),n("fill",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--fill","--color"],modifiers:Array.from({length:21},(o,u)=>`${u*5}`)}]),e("stroke-none",[["stroke","none"]]),r.functional("stroke",o=>{if(o.value){if(o.value.kind==="arbitrary"){let u=o.value.value;switch(o.value.dataType??L(u,["color","number","length","percentage"])){case"number":case"length":case"percentage":return o.modifier?void 0:[s("stroke-width",u)];default:return u=W(o.value.value,o.modifier,t),u===null?void 0:[s("stroke",u)]}}{let u=G(o,t,["--stroke","--color"]);if(u)return[s("stroke",u)]}{let u=t.resolve(o.value.value,["--stroke-width"]);if(u)return[s("stroke-width",u)];if(T(o.value.value))return[s("stroke-width",o.value.value)]}}}),n("stroke",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--stroke","--color"],modifiers:Array.from({length:21},(o,u)=>`${u*5}`)},{values:["0","1","2","3"],valueThemeKeys:["--stroke-width"]}]),e("object-contain",[["object-fit","contain"]]),e("object-cover",[["object-fit","cover"]]),e("object-fill",[["object-fit","fill"]]),e("object-none",[["object-fit","none"]]),e("object-scale-down",[["object-fit","scale-down"]]),e("object-bottom",[["object-position","bottom"]]),e("object-center",[["object-position","center"]]),e("object-left",[["object-position","left"]]),e("object-left-bottom",[["object-position","left bottom"]]),e("object-left-top",[["object-position","left top"]]),e("object-right",[["object-position","right"]]),e("object-right-bottom",[["object-position","right bottom"]]),e("object-right-top",[["object-position","right top"]]),e("object-top",[["object-position","top"]]),i("object",{themeKeys:["--object-position"],handle:o=>[s("object-position",o)]});for(let[o,u]of[["p","padding"],["px","padding-inline"],["py","padding-block"],["ps","padding-inline-start"],["pe","padding-inline-end"],["pt","padding-top"],["pr","padding-right"],["pb","padding-bottom"],["pl","padding-left"]])l(o,["--padding","--spacing"],h=>[s(u,h)]);e("text-left",[["text-align","left"]]),e("text-center",[["text-align","center"]]),e("text-right",[["text-align","right"]]),e("text-justify",[["text-align","justify"]]),e("text-start",[["text-align","start"]]),e("text-end",[["text-align","end"]]),l("indent",["--text-indent","--spacing"],o=>[s("text-indent",o)],{supportsNegative:!0}),e("align-baseline",[["vertical-align","baseline"]]),e("align-top",[["vertical-align","top"]]),e("align-middle",[["vertical-align","middle"]]),e("align-bottom",[["vertical-align","bottom"]]),e("align-text-top",[["vertical-align","text-top"]]),e("align-text-bottom",[["vertical-align","text-bottom"]]),e("align-sub",[["vertical-align","sub"]]),e("align-super",[["vertical-align","super"]]),i("align",{themeKeys:[],handle:o=>[s("vertical-align",o)]}),r.functional("font",o=>{if(!(!o.value||o.modifier)){if(o.value.kind==="arbitrary"){let u=o.value.value;switch(o.value.dataType??L(u,["number","generic-name","family-name"])){case"generic-name":case"family-name":return[s("font-family",u)];default:return[D([$("--tw-font-weight")]),s("--tw-font-weight",u),s("font-weight",u)]}}{let u=t.resolveWith(o.value.value,["--font"],["--font-feature-settings","--font-variation-settings"]);if(u){let[h,w={}]=u;return[s("font-family",h),s("font-feature-settings",w["--font-feature-settings"]),s("font-variation-settings",w["--font-variation-settings"])]}}{let u=t.resolve(o.value.value,["--font-weight"]);if(u)return[D([$("--tw-font-weight")]),s("--tw-font-weight",u),s("font-weight",u)]}}}),n("font",()=>[{values:[],valueThemeKeys:["--font"]},{values:[],valueThemeKeys:["--font-weight"]}]),e("uppercase",[["text-transform","uppercase"]]),e("lowercase",[["text-transform","lowercase"]]),e("capitalize",[["text-transform","capitalize"]]),e("normal-case",[["text-transform","none"]]),e("italic",[["font-style","italic"]]),e("not-italic",[["font-style","normal"]]),e("underline",[["text-decoration-line","underline"]]),e("overline",[["text-decoration-line","overline"]]),e("line-through",[["text-decoration-line","line-through"]]),e("no-underline",[["text-decoration-line","none"]]),e("font-stretch-normal",[["font-stretch","normal"]]),e("font-stretch-ultra-condensed",[["font-stretch","ultra-condensed"]]),e("font-stretch-extra-condensed",[["font-stretch","extra-condensed"]]),e("font-stretch-condensed",[["font-stretch","condensed"]]),e("font-stretch-semi-condensed",[["font-stretch","semi-condensed"]]),e("font-stretch-semi-expanded",[["font-stretch","semi-expanded"]]),e("font-stretch-expanded",[["font-stretch","expanded"]]),e("font-stretch-extra-expanded",[["font-stretch","extra-expanded"]]),e("font-stretch-ultra-expanded",[["font-stretch","ultra-expanded"]]),i("font-stretch",{handleBareValue:({value:o})=>{if(!o.endsWith("%"))return null;let u=Number(o.slice(0,-1));return!T(u)||Number.isNaN(u)||u<50||u>200?null:o},handle:o=>[s("font-stretch",o)]}),n("font-stretch",()=>[{values:["50%","75%","90%","95%","100%","105%","110%","125%","150%","200%"]}]),a("placeholder",{themeKeys:["--background-color","--color"],handle:o=>[j("&::placeholder",[s("--tw-sort","placeholder-color"),s("color",o)])]}),e("decoration-solid",[["text-decoration-style","solid"]]),e("decoration-double",[["text-decoration-style","double"]]),e("decoration-dotted",[["text-decoration-style","dotted"]]),e("decoration-dashed",[["text-decoration-style","dashed"]]),e("decoration-wavy",[["text-decoration-style","wavy"]]),e("decoration-auto",[["text-decoration-thickness","auto"]]),e("decoration-from-font",[["text-decoration-thickness","from-font"]]),r.functional("decoration",o=>{if(o.value){if(o.value.kind==="arbitrary"){let u=o.value.value;switch(o.value.dataType??L(u,["color","length","percentage"])){case"length":case"percentage":return o.modifier?void 0:[s("text-decoration-thickness",u)];default:return u=W(u,o.modifier,t),u===null?void 0:[s("text-decoration-color",u)]}}{let u=t.resolve(o.value.value,["--text-decoration-thickness"]);if(u)return o.modifier?void 0:[s("text-decoration-thickness",u)];if(T(o.value.value))return o.modifier?void 0:[s("text-decoration-thickness",`${o.value.value}px`)]}{let u=G(o,t,["--text-decoration-color","--color"]);if(u)return[s("text-decoration-color",u)]}}}),n("decoration",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--text-decoration-color","--color"],modifiers:Array.from({length:21},(o,u)=>`${u*5}`)},{values:["0","1","2"],valueThemeKeys:["--text-decoration-thickness"]}]),e("animate-none",[["animation","none"]]),i("animate",{themeKeys:["--animate"],handle:o=>[s("animation",o)]});{let o=["var(--tw-blur,)","var(--tw-brightness,)","var(--tw-contrast,)","var(--tw-grayscale,)","var(--tw-hue-rotate,)","var(--tw-invert,)","var(--tw-saturate,)","var(--tw-sepia,)","var(--tw-drop-shadow,)"].join(" "),u=["var(--tw-backdrop-blur,)","var(--tw-backdrop-brightness,)","var(--tw-backdrop-contrast,)","var(--tw-backdrop-grayscale,)","var(--tw-backdrop-hue-rotate,)","var(--tw-backdrop-invert,)","var(--tw-backdrop-opacity,)","var(--tw-backdrop-saturate,)","var(--tw-backdrop-sepia,)"].join(" "),h=()=>D([$("--tw-blur"),$("--tw-brightness"),$("--tw-contrast"),$("--tw-grayscale"),$("--tw-hue-rotate"),$("--tw-invert"),$("--tw-opacity"),$("--tw-saturate"),$("--tw-sepia"),$("--tw-drop-shadow")]),w=()=>D([$("--tw-backdrop-blur"),$("--tw-backdrop-brightness"),$("--tw-backdrop-contrast"),$("--tw-backdrop-grayscale"),$("--tw-backdrop-hue-rotate"),$("--tw-backdrop-invert"),$("--tw-backdrop-opacity"),$("--tw-backdrop-saturate"),$("--tw-backdrop-sepia")]);r.functional("filter",v=>{if(!v.modifier){if(v.value===null)return[h(),s("filter",o)];if(v.value.kind==="arbitrary")return[s("filter",v.value.value)];switch(v.value.value){case"none":return[s("filter","none")]}}}),r.functional("backdrop-filter",v=>{if(!v.modifier){if(v.value===null)return[w(),s("-webkit-backdrop-filter",u),s("backdrop-filter",u)];if(v.value.kind==="arbitrary")return[s("-webkit-backdrop-filter",v.value.value),s("backdrop-filter",v.value.value)];switch(v.value.value){case"none":return[s("-webkit-backdrop-filter","none"),s("backdrop-filter","none")]}}}),i("blur",{themeKeys:["--blur"],handle:v=>[h(),s("--tw-blur",`blur(${v})`),s("filter",o)]}),e("blur-none",[h,["--tw-blur"," "],["filter",o]]),i("backdrop-blur",{themeKeys:["--backdrop-blur","--blur"],handle:v=>[w(),s("--tw-backdrop-blur",`blur(${v})`),s("-webkit-backdrop-filter",u),s("backdrop-filter",u)]}),e("backdrop-blur-none",[w,["--tw-backdrop-blur"," "],["-webkit-backdrop-filter",u],["backdrop-filter",u]]),i("brightness",{themeKeys:["--brightness"],handleBareValue:({value:v})=>T(v)?`${v}%`:null,handle:v=>[h(),s("--tw-brightness",`brightness(${v})`),s("filter",o)]}),i("backdrop-brightness",{themeKeys:["--backdrop-brightness","--brightness"],handleBareValue:({value:v})=>T(v)?`${v}%`:null,handle:v=>[w(),s("--tw-backdrop-brightness",`brightness(${v})`),s("-webkit-backdrop-filter",u),s("backdrop-filter",u)]}),n("brightness",()=>[{values:["0","50","75","90","95","100","105","110","125","150","200"],valueThemeKeys:["--brightness"]}]),n("backdrop-brightness",()=>[{values:["0","50","75","90","95","100","105","110","125","150","200"],valueThemeKeys:["--backdrop-brightness","--brightness"]}]),i("contrast",{themeKeys:["--contrast"],handleBareValue:({value:v})=>T(v)?`${v}%`:null,handle:v=>[h(),s("--tw-contrast",`contrast(${v})`),s("filter",o)]}),i("backdrop-contrast",{themeKeys:["--backdrop-contrast","--contrast"],handleBareValue:({value:v})=>T(v)?`${v}%`:null,handle:v=>[w(),s("--tw-backdrop-contrast",`contrast(${v})`),s("-webkit-backdrop-filter",u),s("backdrop-filter",u)]}),n("contrast",()=>[{values:["0","50","75","100","125","150","200"],valueThemeKeys:["--contrast"]}]),n("backdrop-contrast",()=>[{values:["0","50","75","100","125","150","200"],valueThemeKeys:["--backdrop-contrast","--contrast"]}]),i("grayscale",{themeKeys:["--grayscale"],handleBareValue:({value:v})=>T(v)?`${v}%`:null,defaultValue:"100%",handle:v=>[h(),s("--tw-grayscale",`grayscale(${v})`),s("filter",o)]}),i("backdrop-grayscale",{themeKeys:["--backdrop-grayscale","--grayscale"],handleBareValue:({value:v})=>T(v)?`${v}%`:null,defaultValue:"100%",handle:v=>[w(),s("--tw-backdrop-grayscale",`grayscale(${v})`),s("-webkit-backdrop-filter",u),s("backdrop-filter",u)]}),n("grayscale",()=>[{values:["0","25","50","75","100"],valueThemeKeys:["--grayscale"],hasDefaultValue:!0}]),n("backdrop-grayscale",()=>[{values:["0","25","50","75","100"],valueThemeKeys:["--backdrop-grayscale","--grayscale"],hasDefaultValue:!0}]),i("hue-rotate",{supportsNegative:!0,themeKeys:["--hue-rotate"],handleBareValue:({value:v})=>T(v)?`${v}deg`:null,handle:v=>[h(),s("--tw-hue-rotate",`hue-rotate(${v})`),s("filter",o)]}),i("backdrop-hue-rotate",{supportsNegative:!0,themeKeys:["--backdrop-hue-rotate","--hue-rotate"],handleBareValue:({value:v})=>T(v)?`${v}deg`:null,handle:v=>[w(),s("--tw-backdrop-hue-rotate",`hue-rotate(${v})`),s("-webkit-backdrop-filter",u),s("backdrop-filter",u)]}),n("hue-rotate",()=>[{values:["0","15","30","60","90","180"],valueThemeKeys:["--hue-rotate"]}]),n("backdrop-hue-rotate",()=>[{values:["0","15","30","60","90","180"],valueThemeKeys:["--backdrop-hue-rotate","--hue-rotate"]}]),i("invert",{themeKeys:["--invert"],handleBareValue:({value:v})=>T(v)?`${v}%`:null,defaultValue:"100%",handle:v=>[h(),s("--tw-invert",`invert(${v})`),s("filter",o)]}),i("backdrop-invert",{themeKeys:["--backdrop-invert","--invert"],handleBareValue:({value:v})=>T(v)?`${v}%`:null,defaultValue:"100%",handle:v=>[w(),s("--tw-backdrop-invert",`invert(${v})`),s("-webkit-backdrop-filter",u),s("backdrop-filter",u)]}),n("invert",()=>[{values:["0","25","50","75","100"],valueThemeKeys:["--invert"],hasDefaultValue:!0}]),n("backdrop-invert",()=>[{values:["0","25","50","75","100"],valueThemeKeys:["--backdrop-invert","--invert"],hasDefaultValue:!0}]),i("saturate",{themeKeys:["--saturate"],handleBareValue:({value:v})=>T(v)?`${v}%`:null,handle:v=>[h(),s("--tw-saturate",`saturate(${v})`),s("filter",o)]}),i("backdrop-saturate",{themeKeys:["--backdrop-saturate","--saturate"],handleBareValue:({value:v})=>T(v)?`${v}%`:null,handle:v=>[w(),s("--tw-backdrop-saturate",`saturate(${v})`),s("-webkit-backdrop-filter",u),s("backdrop-filter",u)]}),n("saturate",()=>[{values:["0","50","100","150","200"],valueThemeKeys:["--saturate"]}]),n("backdrop-saturate",()=>[{values:["0","50","100","150","200"],valueThemeKeys:["--backdrop-saturate","--saturate"]}]),i("sepia",{themeKeys:["--sepia"],handleBareValue:({value:v})=>T(v)?`${v}%`:null,defaultValue:"100%",handle:v=>[h(),s("--tw-sepia",`sepia(${v})`),s("filter",o)]}),i("backdrop-sepia",{themeKeys:["--backdrop-sepia","--sepia"],handleBareValue:({value:v})=>T(v)?`${v}%`:null,defaultValue:"100%",handle:v=>[w(),s("--tw-backdrop-sepia",`sepia(${v})`),s("-webkit-backdrop-filter",u),s("backdrop-filter",u)]}),n("sepia",()=>[{values:["0","50","100"],valueThemeKeys:["--sepia"],hasDefaultValue:!0}]),n("backdrop-sepia",()=>[{values:["0","50","100"],valueThemeKeys:["--backdrop-sepia","--sepia"],hasDefaultValue:!0}]),e("drop-shadow-none",[h,["--tw-drop-shadow"," "],["filter",o]]),i("drop-shadow",{themeKeys:["--drop-shadow"],handle:v=>[h(),s("--tw-drop-shadow",O(v,",").map(N=>`drop-shadow(${N})`).join(" ")),s("filter",o)]}),i("backdrop-opacity",{themeKeys:["--backdrop-opacity","--opacity"],handleBareValue:({value:v})=>qe(v)?`${v}%`:null,handle:v=>[w(),s("--tw-backdrop-opacity",`opacity(${v})`),s("-webkit-backdrop-filter",u),s("backdrop-filter",u)]}),n("backdrop-opacity",()=>[{values:Array.from({length:21},(v,N)=>`${N*5}`),valueThemeKeys:["--backdrop-opacity","--opacity"]}])}{let o=`var(--tw-ease, ${t.resolve(null,["--default-transition-timing-function"])??"ease"})`,u=`var(--tw-duration, ${t.resolve(null,["--default-transition-duration"])??"0s"})`;e("transition-none",[["transition-property","none"]]),e("transition-all",[["transition-property","all"],["transition-timing-function",o],["transition-duration",u]]),e("transition-colors",[["transition-property","color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to"],["transition-timing-function",o],["transition-duration",u]]),e("transition-opacity",[["transition-property","opacity"],["transition-timing-function",o],["transition-duration",u]]),e("transition-shadow",[["transition-property","box-shadow"],["transition-timing-function",o],["transition-duration",u]]),e("transition-transform",[["transition-property","transform, translate, scale, rotate"],["transition-timing-function",o],["transition-duration",u]]),i("transition",{defaultValue:"color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter",themeKeys:["--transition-property"],handle:h=>[s("transition-property",h),s("transition-timing-function",o),s("transition-duration",u)]}),e("transition-discrete",[["transition-behavior","allow-discrete"]]),e("transition-normal",[["transition-behavior","normal"]]),i("delay",{handleBareValue:({value:h})=>T(h)?`${h}ms`:null,themeKeys:["--transition-delay"],handle:h=>[s("transition-delay",h)]});{let h=()=>D([$("--tw-duration")]);e("duration-initial",[h,["--tw-duration","initial"]]),r.functional("duration",w=>{if(w.modifier||!w.value)return;let v=null;if(w.value.kind==="arbitrary"?v=w.value.value:(v=t.resolve(w.value.fraction??w.value.value,["--transition-duration"]),v===null&&T(w.value.value)&&(v=`${w.value.value}ms`)),v!==null)return[h(),s("--tw-duration",v),s("transition-duration",v)]})}n("delay",()=>[{values:["75","100","150","200","300","500","700","1000"],valueThemeKeys:["--transition-delay"]}]),n("duration",()=>[{values:["75","100","150","200","300","500","700","1000"],valueThemeKeys:["--transition-duration"]}])}{let o=()=>D([$("--tw-ease")]);e("ease-initial",[o,["--tw-ease","initial"]]),e("ease-linear",[o,["--tw-ease","linear"],["transition-timing-function","linear"]]),i("ease",{themeKeys:["--ease"],handle:u=>[o(),s("--tw-ease",u),s("transition-timing-function",u)]})}e("will-change-auto",[["will-change","auto"]]),e("will-change-scroll",[["will-change","scroll-position"]]),e("will-change-contents",[["will-change","contents"]]),e("will-change-transform",[["will-change","transform"]]),i("will-change",{themeKeys:[],handle:o=>[s("will-change",o)]}),e("content-none",[["--tw-content","none"],["content","none"]]),i("content",{themeKeys:[],handle:o=>[D([$("--tw-content",'""')]),s("--tw-content",o),s("content","var(--tw-content)")]});{let o="var(--tw-contain-size,) var(--tw-contain-layout,) var(--tw-contain-paint,) var(--tw-contain-style,)",u=()=>D([$("--tw-contain-size"),$("--tw-contain-layout"),$("--tw-contain-paint"),$("--tw-contain-style")]);e("contain-none",[["contain","none"]]),e("contain-content",[["contain","content"]]),e("contain-strict",[["contain","strict"]]),e("contain-size",[u,["--tw-contain-size","size"],["contain",o]]),e("contain-inline-size",[u,["--tw-contain-size","inline-size"],["contain",o]]),e("contain-layout",[u,["--tw-contain-layout","layout"],["contain",o]]),e("contain-paint",[u,["--tw-contain-paint","paint"],["contain",o]]),e("contain-style",[u,["--tw-contain-style","style"],["contain",o]]),i("contain",{themeKeys:[],handle:h=>[s("contain",h)]})}e("forced-color-adjust-none",[["forced-color-adjust","none"]]),e("forced-color-adjust-auto",[["forced-color-adjust","auto"]]),e("leading-none",[()=>D([$("--tw-leading")]),["--tw-leading","1"],["line-height","1"]]),l("leading",["--leading","--spacing"],o=>[D([$("--tw-leading")]),s("--tw-leading",o),s("line-height",o)]),i("tracking",{supportsNegative:!0,themeKeys:["--tracking"],handle:o=>[D([$("--tw-tracking")]),s("--tw-tracking",o),s("letter-spacing",o)]}),e("antialiased",[["-webkit-font-smoothing","antialiased"],["-moz-osx-font-smoothing","grayscale"]]),e("subpixel-antialiased",[["-webkit-font-smoothing","auto"],["-moz-osx-font-smoothing","auto"]]);{let o="var(--tw-ordinal,) var(--tw-slashed-zero,) var(--tw-numeric-figure,) var(--tw-numeric-spacing,) var(--tw-numeric-fraction,)",u=()=>D([$("--tw-ordinal"),$("--tw-slashed-zero"),$("--tw-numeric-figure"),$("--tw-numeric-spacing"),$("--tw-numeric-fraction")]);e("normal-nums",[["font-variant-numeric","normal"]]),e("ordinal",[u,["--tw-ordinal","ordinal"],["font-variant-numeric",o]]),e("slashed-zero",[u,["--tw-slashed-zero","slashed-zero"],["font-variant-numeric",o]]),e("lining-nums",[u,["--tw-numeric-figure","lining-nums"],["font-variant-numeric",o]]),e("oldstyle-nums",[u,["--tw-numeric-figure","oldstyle-nums"],["font-variant-numeric",o]]),e("proportional-nums",[u,["--tw-numeric-spacing","proportional-nums"],["font-variant-numeric",o]]),e("tabular-nums",[u,["--tw-numeric-spacing","tabular-nums"],["font-variant-numeric",o]]),e("diagonal-fractions",[u,["--tw-numeric-fraction","diagonal-fractions"],["font-variant-numeric",o]]),e("stacked-fractions",[u,["--tw-numeric-fraction","stacked-fractions"],["font-variant-numeric",o]])}{let o=()=>D([$("--tw-outline-style","solid")]);r.static("outline-hidden",()=>[s("--tw-outline-style","none"),s("outline-style","none"),U("@media","(forced-colors: active)",[s("outline","2px solid transparent"),s("outline-offset","2px")])]),e("outline-none",[["--tw-outline-style","none"],["outline-style","none"]]),e("outline-solid",[["--tw-outline-style","solid"],["outline-style","solid"]]),e("outline-dashed",[["--tw-outline-style","dashed"],["outline-style","dashed"]]),e("outline-dotted",[["--tw-outline-style","dotted"],["outline-style","dotted"]]),e("outline-double",[["--tw-outline-style","double"],["outline-style","double"]]),r.functional("outline",u=>{if(u.value===null){if(u.modifier)return;let h=t.get(["--default-outline-width"])??"1px";return[o(),s("outline-style","var(--tw-outline-style)"),s("outline-width",h)]}if(u.value.kind==="arbitrary"){let h=u.value.value;switch(u.value.dataType??L(h,["color","length","number","percentage"])){case"length":case"number":case"percentage":return u.modifier?void 0:[o(),s("outline-style","var(--tw-outline-style)"),s("outline-width",h)];default:return h=W(h,u.modifier,t),h===null?void 0:[s("outline-color",h)]}}{let h=G(u,t,["--outline-color","--color"]);if(h)return[s("outline-color",h)]}{if(u.modifier)return;let h=t.resolve(u.value.value,["--outline-width"]);if(h)return[o(),s("outline-style","var(--tw-outline-style)"),s("outline-width",h)];if(T(u.value.value))return[o(),s("outline-style","var(--tw-outline-style)"),s("outline-width",`${u.value.value}px`)]}}),n("outline",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--outline-color","--color"],modifiers:Array.from({length:21},(u,h)=>`${h*5}`),hasDefaultValue:!0},{values:["0","1","2","4","8"],valueThemeKeys:["--outline-width"]}]),i("outline-offset",{supportsNegative:!0,themeKeys:["--outline-offset"],handleBareValue:({value:u})=>T(u)?`${u}px`:null,handle:u=>[s("outline-offset",u)]}),n("outline-offset",()=>[{supportsNegative:!0,values:["0","1","2","4","8"],valueThemeKeys:["--outline-offset"]}])}i("opacity",{themeKeys:["--opacity"],handleBareValue:({value:o})=>qe(o)?`${o}%`:null,handle:o=>[s("opacity",o)]}),n("opacity",()=>[{values:Array.from({length:21},(o,u)=>`${u*5}`),valueThemeKeys:["--opacity"]}]),e("underline-offset-auto",[["text-underline-offset","auto"]]),i("underline-offset",{supportsNegative:!0,themeKeys:["--text-underline-offset"],handleBareValue:({value:o})=>T(o)?`${o}px`:null,handle:o=>[s("text-underline-offset",o)]}),n("underline-offset",()=>[{supportsNegative:!0,values:["0","1","2","4","8"],valueThemeKeys:["--text-underline-offset"]}]),r.functional("text",o=>{if(o.value){if(o.value.kind==="arbitrary"){let u=o.value.value;switch(o.value.dataType??L(u,["color","length","percentage","absolute-size","relative-size"])){case"size":case"length":case"percentage":case"absolute-size":case"relative-size":{if(o.modifier){let w=o.modifier.kind==="arbitrary"?o.modifier.value:t.resolve(o.modifier.value,["--leading"]);if(!w&&he(o.modifier.value)){let v=t.resolve(null,["--spacing"]);if(!v)return null;w=`calc(${v} * ${o.modifier.value})`}return!w&&o.modifier.value==="none"&&(w="1"),w?[s("font-size",u),s("line-height",w)]:null}return[s("font-size",u)]}default:return u=W(u,o.modifier,t),u===null?void 0:[s("color",u)]}}{let u=G(o,t,["--text-color","--color"]);if(u)return[s("color",u)]}{let u=t.resolveWith(o.value.value,["--text"],["--line-height","--letter-spacing","--font-weight"]);if(u){let[h,w={}]=Array.isArray(u)?u:[u];if(o.modifier){let v=o.modifier.kind==="arbitrary"?o.modifier.value:t.resolve(o.modifier.value,["--leading"]);if(!v&&he(o.modifier.value)){let E=t.resolve(null,["--spacing"]);if(!E)return null;v=`calc(${E} * ${o.modifier.value})`}if(!v&&o.modifier.value==="none"&&(v="1"),!v)return null;let N=[s("font-size",h)];return v&&N.push(s("line-height",v)),N}return typeof w=="string"?[s("font-size",h),s("line-height",w)]:[s("font-size",h),s("line-height",w["--line-height"]?`var(--tw-leading, ${w["--line-height"]})`:void 0),s("letter-spacing",w["--letter-spacing"]?`var(--tw-tracking, ${w["--letter-spacing"]})`:void 0),s("font-weight",w["--font-weight"]?`var(--tw-font-weight, ${w["--font-weight"]})`:void 0)]}}}}),n("text",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--text-color","--color"],modifiers:Array.from({length:21},(o,u)=>`${u*5}`)},{values:[],valueThemeKeys:["--text"],modifiers:[],modifierThemeKeys:["--leading"]}]);{let v=function(S){return`var(--tw-ring-inset,) 0 0 0 calc(${S} + var(--tw-ring-offset-width)) var(--tw-ring-color, ${w})`},N=function(S){return`inset 0 0 0 ${S} var(--tw-inset-ring-color, currentColor)`};var R=v,_=N;let o=["var(--tw-inset-shadow)","var(--tw-inset-ring-shadow)","var(--tw-ring-offset-shadow)","var(--tw-ring-shadow)","var(--tw-shadow)"].join(", "),u="0 0 #0000",h=()=>D([$("--tw-shadow",u),$("--tw-shadow-color"),$("--tw-inset-shadow",u),$("--tw-inset-shadow-color"),$("--tw-ring-color"),$("--tw-ring-shadow",u),$("--tw-inset-ring-color"),$("--tw-inset-ring-shadow",u),$("--tw-ring-inset"),$("--tw-ring-offset-width","0px","<length>"),$("--tw-ring-offset-color","#fff"),$("--tw-ring-offset-shadow",u)]);e("shadow-initial",[h,["--tw-shadow-color","initial"]]),r.functional("shadow",S=>{if(!S.value){let C=t.get(["--shadow"]);return C===null?void 0:[h(),s("--tw-shadow",fe(C,I=>`var(--tw-shadow-color, ${I})`)),s("box-shadow",o)]}if(S.value.kind==="arbitrary"){let C=S.value.value;switch(S.value.dataType??L(C,["color"])){case"color":return C=W(C,S.modifier,t),C===null?void 0:[h(),s("--tw-shadow-color",C)];default:return[h(),s("--tw-shadow",fe(C,Pe=>`var(--tw-shadow-color, ${Pe})`)),s("box-shadow",o)]}}switch(S.value.value){case"none":return S.modifier?void 0:[h(),s("--tw-shadow",u),s("box-shadow",o)]}{let C=t.get([`--shadow-${S.value.value}`]);if(C)return S.modifier?void 0:[h(),s("--tw-shadow",fe(C,I=>`var(--tw-shadow-color, ${I})`)),s("box-shadow",o)]}{let C=G(S,t,["--box-shadow-color","--color"]);if(C)return[h(),s("--tw-shadow-color",C)]}}),n("shadow",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--box-shadow-color","--color"],modifiers:Array.from({length:21},(S,C)=>`${C*5}`)},{values:["none"],valueThemeKeys:["--shadow"],hasDefaultValue:!0}]),e("inset-shadow-initial",[h,["--tw-inset-shadow-color","initial"]]),r.functional("inset-shadow",S=>{if(!S.value){let C=t.get(["--inset-shadow"]);return C===null?void 0:[h(),s("--tw-inset-shadow",fe(C,I=>`var(--tw-inset-shadow-color, ${I})`)),s("box-shadow",o)]}if(S.value.kind==="arbitrary"){let C=S.value.value;switch(S.value.dataType??L(C,["color"])){case"color":return C=W(C,S.modifier,t),C===null?void 0:[h(),s("--tw-inset-shadow-color",C)];default:return[h(),s("--tw-inset-shadow",`inset ${fe(C,Pe=>`var(--tw-inset-shadow-color, ${Pe})`)}`),s("box-shadow",o)]}}switch(S.value.value){case"none":return S.modifier?void 0:[h(),s("--tw-inset-shadow",u),s("box-shadow",o)]}{let C=t.get([`--inset-shadow-${S.value.value}`]);if(C)return S.modifier?void 0:[h(),s("--tw-inset-shadow",fe(C,I=>`var(--tw-inset-shadow-color, ${I})`)),s("box-shadow",o)]}{let C=G(S,t,["--box-shadow-color","--color"]);if(C)return[h(),s("--tw-inset-shadow-color",C)]}}),n("inset-shadow",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--box-shadow-color","--color"],modifiers:Array.from({length:21},(S,C)=>`${C*5}`)},{values:[],valueThemeKeys:["--inset-shadow"],hasDefaultValue:!0}]),e("ring-inset",[h,["--tw-ring-inset","inset"]]);let w=t.get(["--default-ring-color"])??"currentColor";r.functional("ring",S=>{if(!S.value){if(S.modifier)return;let C=t.get(["--default-ring-width"])??"1px";return[h(),s("--tw-ring-shadow",v(C)),s("box-shadow",o)]}if(S.value.kind==="arbitrary"){let C=S.value.value;switch(S.value.dataType??L(C,["color","length"])){case"length":return S.modifier?void 0:[h(),s("--tw-ring-shadow",v(C)),s("box-shadow",o)];default:return C=W(C,S.modifier,t),C===null?void 0:[s("--tw-ring-color",C)]}}{let C=G(S,t,["--ring-color","--color"]);if(C)return[s("--tw-ring-color",C)]}{if(S.modifier)return;let C=t.resolve(S.value.value,["--ring-width"]);if(C===null&&T(S.value.value)&&(C=`${S.value.value}px`),C)return[h(),s("--tw-ring-shadow",v(C)),s("box-shadow",o)]}}),n("ring",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--ring-color","--color"],modifiers:Array.from({length:21},(S,C)=>`${C*5}`)},{values:["0","1","2","4","8"],valueThemeKeys:["--ring-width"],hasDefaultValue:!0}]),r.functional("inset-ring",S=>{if(!S.value)return S.modifier?void 0:[h(),s("--tw-inset-ring-shadow",N("1px")),s("box-shadow",o)];if(S.value.kind==="arbitrary"){let C=S.value.value;switch(S.value.dataType??L(C,["color","length"])){case"length":return S.modifier?void 0:[h(),s("--tw-inset-ring-shadow",N(C)),s("box-shadow",o)];default:return C=W(C,S.modifier,t),C===null?void 0:[s("--tw-inset-ring-color",C)]}}{let C=G(S,t,["--ring-color","--color"]);if(C)return[s("--tw-inset-ring-color",C)]}{if(S.modifier)return;let C=t.resolve(S.value.value,["--ring-width"]);if(C===null&&T(S.value.value)&&(C=`${S.value.value}px`),C)return[h(),s("--tw-inset-ring-shadow",N(C)),s("box-shadow",o)]}}),n("inset-ring",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--ring-color","--color"],modifiers:Array.from({length:21},(S,C)=>`${C*5}`)},{values:["0","1","2","4","8"],valueThemeKeys:["--ring-width"],hasDefaultValue:!0}]);let E="var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color)";r.functional("ring-offset",S=>{if(S.value){if(S.value.kind==="arbitrary"){let C=S.value.value;switch(S.value.dataType??L(C,["color","length"])){case"length":return S.modifier?void 0:[s("--tw-ring-offset-width",C),s("--tw-ring-offset-shadow",E)];default:return C=W(C,S.modifier,t),C===null?void 0:[s("--tw-ring-offset-color",C)]}}{let C=t.resolve(S.value.value,["--ring-offset-width"]);if(C)return S.modifier?void 0:[s("--tw-ring-offset-width",C),s("--tw-ring-offset-shadow",E)];if(T(S.value.value))return S.modifier?void 0:[s("--tw-ring-offset-width",`${S.value.value}px`),s("--tw-ring-offset-shadow",E)]}{let C=G(S,t,["--ring-offset-color","--color"]);if(C)return[s("--tw-ring-offset-color",C)]}}})}return n("ring-offset",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--ring-offset-color","--color"],modifiers:Array.from({length:21},(o,u)=>`${u*5}`)},{values:["0","1","2","4","8"],valueThemeKeys:["--ring-offset-width"]}]),r.functional("@container",o=>{let u=null;if(o.value===null?u="inline-size":o.value.kind==="arbitrary"?u=o.value.value:o.value.kind==="named"&&o.value.value==="normal"&&(u="normal"),u!==null)return o.modifier?[s("container-type",u),s("container-name",o.modifier.value)]:[s("container-type",u)]}),n("@container",()=>[{values:["normal"],valueThemeKeys:[],hasDefaultValue:!0}]),r}function ur(t){let r=t.params;return ri.test(r)?n=>{let e={"--value":{usedSpacingInteger:!1,usedSpacingNumber:!1,themeKeys:new Set,literals:new Set},"--modifier":{usedSpacingInteger:!1,usedSpacingNumber:!1,themeKeys:new Set,literals:new Set}};K(t.nodes,i=>{if(i.kind!=="declaration"||!i.value||!i.value.includes("--value(")&&!i.value.includes("--modifier("))return;let a=F(i.value);Y(a,l=>{if(l.kind!=="function")return;if(l.value==="--spacing"&&!(e["--modifier"].usedSpacingNumber&&e["--value"].usedSpacingNumber))return Y(l.nodes,c=>{if(c.kind!=="function"||c.value!=="--value"&&c.value!=="--modifier")return;let d=c.value;for(let f of c.nodes)if(f.kind==="word"){if(f.value==="integer")e[d].usedSpacingInteger||=!0;else if(f.value==="number"&&(e[d].usedSpacingNumber||=!0,e["--modifier"].usedSpacingNumber&&e["--value"].usedSpacingNumber))return 2}}),0;if(l.value!=="--value"&&l.value!=="--modifier")return;let p=O(H(l.nodes),",");for(let[c,d]of p.entries())d=d.replace(/\\\*/g,"*"),d=d.replace(/--(.*?)\s--(.*?)/g,"--$1-*--$2"),d=d.replace(/\s+/g,""),d=d.replace(/(-\*){2,}/g,"-*"),d[0]==="-"&&d[1]==="-"&&!d.includes("-*")&&(d+="-*"),p[c]=d;l.nodes=F(p.join(","));for(let c of l.nodes)if(c.kind==="word"&&(c.value[0]==='"'||c.value[0]==="'")&&c.value[0]===c.value[c.value.length-1]){let d=c.value.slice(1,-1);e[l.value].literals.add(d)}else if(c.kind==="word"&&c.value[0]==="-"&&c.value[1]==="-"){let d=c.value.replace(/-\*.*$/g,"");e[l.value].themeKeys.add(d)}}),i.value=H(a)}),n.utilities.functional(r.slice(0,-2),i=>{let a=structuredClone(t),l=i.value,p=i.modifier;if(l===null)return;let c=!1,d=!1,f=!1,m=!1,g=new Map,y=!1;if(K([a],(b,{parent:k,replaceWith:A})=>{if(k?.kind!=="rule"&&k?.kind!=="at-rule"||b.kind!=="declaration"||!b.value)return;let x=F(b.value);(Y(x,(P,{replaceWith:R})=>{if(P.kind==="function"){if(P.value==="--value"){c=!0;let _=ar(l,P,n);return _?(d=!0,_.ratio?y=!0:g.set(b,k),R(_.nodes),1):(c||=!1,A([]),2)}else if(P.value==="--modifier"){if(p===null)return A([]),2;f=!0;let _=ar(p,P,n);return _?(m=!0,R(_.nodes),1):(f||=!1,A([]),2)}}})??0)===0&&(b.value=H(x))}),c&&!d||f&&!m||y&&m||p&&!y&&!m)return null;if(y)for(let[b,k]of g){let A=k.nodes.indexOf(b);A!==-1&&k.nodes.splice(A,1)}return a.nodes}),n.utilities.suggest(r.slice(0,-2),()=>{let i=[],a=[];for(let[l,{literals:p,usedSpacingNumber:c,usedSpacingInteger:d,themeKeys:f}]of[[i,e["--value"]],[a,e["--modifier"]]]){for(let m of p)l.push(m);if(c)l.push(...ht);else if(d)for(let m of ht)T(m)&&l.push(m);for(let m of n.theme.keysInNamespaces(f))l.push(m)}return[{values:i,modifiers:a}]})}:ti.test(r)?n=>{n.utilities.static(r,()=>structuredClone(t.nodes))}:null}function ar(t,r,n){for(let e of r.nodes){if(t.kind==="named"&&e.kind==="word"&&(e.value[0]==="'"||e.value[0]==='"')&&e.value[e.value.length-1]===e.value[0]&&e.value.slice(1,-1)===t.value)return{nodes:F(t.value)};if(t.kind==="named"&&e.kind==="word"&&e.value[0]==="-"&&e.value[1]==="-"){let i=e.value;if(i.endsWith("-*")){i=i.slice(0,-2);let a=n.theme.resolve(t.value,[i]);if(a)return{nodes:F(a)}}else{let a=i.split("-*");if(a.length<=1)continue;let l=[a.shift()],p=n.theme.resolveWith(t.value,l,a);if(p){let[,c={}]=p;{let d=c[a.pop()];if(d)return{nodes:F(d)}}}}}else if(t.kind==="named"&&e.kind==="word"){if(e.value!=="number"&&e.value!=="integer"&&e.value!=="ratio"&&e.value!=="percentage")continue;let i=e.value==="ratio"&&"fraction"in t?t.fraction:t.value;if(!i)continue;let a=L(i,[e.value]);if(a===null)continue;if(a==="ratio"){let[l,p]=O(i,"/");if(!T(l)||!T(p))continue}else{if(a==="number"&&!he(i))continue;if(a==="percentage"&&!T(i.slice(0,-1)))continue}return{nodes:F(i),ratio:a==="ratio"}}else if(t.kind==="arbitrary"&&e.kind==="word"&&e.value[0]==="["&&e.value[e.value.length-1]==="]"){let i=e.value.slice(1,-1);if(i==="*")return{nodes:F(t.value)};if("dataType"in t&&t.dataType&&t.dataType!==i)continue;if("dataType"in t&&t.dataType)return{nodes:F(t.value)};if(L(t.value,[i])!==null)return{nodes:F(t.value)}}}}var yt={"--alpha":ni,"--spacing":ii,"--theme":oi,theme:li};function ni(t,r,n,...e){let[i,a]=O(n,"/").map(l=>l.trim());if(!i||!a)throw new Error(`The --alpha(\u2026) function requires a color and an alpha value, e.g.: \`--alpha(${i||"var(--my-color)"} / ${a||"50%"})\``);if(e.length>0)throw new Error(`The --alpha(\u2026) function only accepts one argument, e.g.: \`--alpha(${i||"var(--my-color)"} / ${a||"50%"})\``);return Z(i,a)}function ii(t,r,n,...e){if(!n)throw new Error("The --spacing(\u2026) function requires an argument, but received none.");if(e.length>0)throw new Error(`The --spacing(\u2026) function only accepts a single argument, but received ${e.length+1}.`);let i=t.theme.resolve(null,["--spacing"]);if(!i)throw new Error("The --spacing(\u2026) function requires that the `--spacing` theme variable exists, but it was not found.");return`calc(${i} * ${n})`}function oi(t,r,n,...e){if(!n.startsWith("--"))throw new Error("The --theme(\u2026) function can only be used with CSS variables from your theme.");let i=!1;n.endsWith(" inline")&&(i=!0,n=n.slice(0,-7)),r.kind==="at-rule"&&(i=!0);let a=t.resolveThemeValue(n,i);if(!a){if(e.length>0)return e.join(", ");throw new Error(`Could not resolve value for theme function: \`theme(${n})\`. Consider checking if the variable name is correct or provide a fallback value to silence this error.`)}if(e.length===0)return a;let l=e.join(", ");if(l==="initial")return a;if(a==="initial")return l;if(a.startsWith("var(")||a.startsWith("theme(")||a.startsWith("--theme(")){let p=F(a);return si(p,l),H(p)}return a}function li(t,r,n,...e){n=ai(n);let i=t.resolveThemeValue(n);if(!i&&e.length>0)return e.join(", ");if(!i)throw new Error(`Could not resolve value for theme function: \`theme(${n})\`. Consider checking if the path is correct or provide a fallback value to silence this error.`);return i}var cr=new RegExp(Object.keys(yt).map(t=>`${t}\\(`).join("|"));function be(t,r){let n=0;return K(t,e=>{if(e.kind==="declaration"&&e.value&&cr.test(e.value)){n|=8,e.value=fr(e.value,e,r);return}e.kind==="at-rule"&&(e.name==="@media"||e.name==="@custom-media"||e.name==="@container"||e.name==="@supports")&&cr.test(e.params)&&(n|=8,e.params=fr(e.params,e,r))}),n}function fr(t,r,n){let e=F(t);return Y(e,(i,{replaceWith:a})=>{if(i.kind==="function"&&i.value in yt){let l=O(H(i.nodes).trim(),",").map(c=>c.trim()),p=yt[i.value](n,r,...l);return a(F(p))}}),H(e)}function ai(t){if(t[0]!=="'"&&t[0]!=='"')return t;let r="",n=t[0];for(let e=1;e<t.length-1;e++){let i=t[e],a=t[e+1];i==="\\"&&(a===n||a==="\\")?(r+=a,e++):r+=i}return r}function si(t,r){Y(t,n=>{if(n.kind==="function"&&!(n.value!=="var"&&n.value!=="theme"&&n.value!=="--theme"))if(n.nodes.length===1)n.nodes.push({kind:"word",value:`, ${r}`});else{let e=n.nodes[n.nodes.length-1];e.kind==="word"&&e.value==="initial"&&(e.value=r)}})}function He(t,r){let n=t.length,e=r.length,i=n<e?n:e;for(let a=0;a<i;a++){let l=t.charCodeAt(a),p=r.charCodeAt(a);if(l>=48&&l<=57&&p>=48&&p<=57){let c=a,d=a+1,f=a,m=a+1;for(l=t.charCodeAt(d);l>=48&&l<=57;)l=t.charCodeAt(++d);for(p=r.charCodeAt(m);p>=48&&p<=57;)p=r.charCodeAt(++m);let g=t.slice(c,d),y=r.slice(f,m),b=Number(g)-Number(y);if(b)return b;if(g<y)return-1;if(g>y)return 1;continue}if(l!==p)return l-p}return t.length-r.length}var ui=/^\d+\/\d+$/;function dr(t){let r=[];for(let e of t.utilities.keys("static"))r.push({name:e,utility:e,fraction:!1,modifiers:[]});for(let e of t.utilities.keys("functional")){let i=t.utilities.getCompletions(e);for(let a of i)for(let l of a.values){let p=l!==null&&ui.test(l),c=l===null?e:`${e}-${l}`;r.push({name:c,utility:e,fraction:p,modifiers:a.modifiers}),a.supportsNegative&&r.push({name:`-${c}`,utility:`-${e}`,fraction:p,modifiers:a.modifiers})}}return r.length===0?[]:(r.sort((e,i)=>He(e.name,i.name)),ci(r))}function ci(t){let r=[],n=null,e=new Map,i=new z(()=>[]);for(let l of t){let{utility:p,fraction:c}=l;n||(n={utility:p,items:[]},e.set(p,n)),p!==n.utility&&(r.push(n),n={utility:p,items:[]},e.set(p,n)),c?i.get(p).push(l):n.items.push(l)}n&&r[r.length-1]!==n&&r.push(n);for(let[l,p]of i){let c=e.get(l);c&&c.items.push(...p)}let a=[];for(let l of r)for(let p of l.items)a.push([p.name,{modifiers:p.modifiers}]);return a}function pr(t){let r=[];for(let[e,i]of t.variants.entries()){let p=function({value:c,modifier:d}={}){let f=e;c&&(f+=a?`-${c}`:c),d&&(f+=`/${d}`);let m=t.parseVariant(f);if(!m)return[];let g=j(".__placeholder__",[]);if(ye(g,m,t.variants)===null)return[];let y=[];return Fe(g.nodes,(b,{path:k})=>{if(b.kind!=="rule"&&b.kind!=="at-rule"||b.nodes.length>0)return;k.sort((V,P)=>{let R=V.kind==="at-rule",_=P.kind==="at-rule";return R&&!_?-1:!R&&_?1:0});let A=k.flatMap(V=>V.kind==="rule"?V.selector==="&"?[]:[V.selector]:V.kind==="at-rule"?[`${V.name} ${V.params}`]:[]),x="";for(let V=A.length-1;V>=0;V--)x=x===""?A[V]:`${A[V]} { ${x} }`;y.push(x)}),y};var n=p;if(i.kind==="arbitrary")continue;let a=e!=="@",l=t.variants.getCompletions(e);switch(i.kind){case"static":{r.push({name:e,values:l,isArbitrary:!1,hasDash:a,selectors:p});break}case"functional":{r.push({name:e,values:l,isArbitrary:!0,hasDash:a,selectors:p});break}case"compound":{r.push({name:e,values:l,isArbitrary:!0,hasDash:a,selectors:p});break}}}return r}function mr(t,r){let{astNodes:n,nodeSorting:e}=oe(Array.from(r),t),i=new Map(r.map(l=>[l,null])),a=0n;for(let l of n){let p=e.get(l)?.candidate;p&&i.set(p,i.get(p)??a++)}return r.map(l=>[l,i.get(l)??null])}var Ge=/^@?[a-zA-Z0-9_-]*$/;var kt=class{compareFns=new Map;variants=new Map;completions=new Map;groupOrder=null;lastOrder=0;static(r,n,{compounds:e,order:i}={}){this.set(r,{kind:"static",applyFn:n,compoundsWith:0,compounds:e??2,order:i})}fromAst(r,n){let e=[];K(n,i=>{i.kind==="rule"?e.push(i.selector):i.kind==="at-rule"&&i.name!=="@slot"&&e.push(`${i.name} ${i.params}`)}),this.static(r,i=>{let a=structuredClone(n);wt(a,i.nodes),i.nodes=a},{compounds:de(e)})}functional(r,n,{compounds:e,order:i}={}){this.set(r,{kind:"functional",applyFn:n,compoundsWith:0,compounds:e??2,order:i})}compound(r,n,e,{compounds:i,order:a}={}){this.set(r,{kind:"compound",applyFn:e,compoundsWith:n,compounds:i??2,order:a})}group(r,n){this.groupOrder=this.nextOrder(),n&&this.compareFns.set(this.groupOrder,n),r(),this.groupOrder=null}has(r){return this.variants.has(r)}get(r){return this.variants.get(r)}kind(r){return this.variants.get(r)?.kind}compoundsWith(r,n){let e=this.variants.get(r),i=typeof n=="string"?this.variants.get(n):n.kind==="arbitrary"?{compounds:de([n.selector])}:this.variants.get(n.root);return!(!e||!i||e.kind!=="compound"||i.compounds===0||e.compoundsWith===0||(e.compoundsWith&i.compounds)===0)}suggest(r,n){this.completions.set(r,n)}getCompletions(r){return this.completions.get(r)?.()??[]}compare(r,n){if(r===n)return 0;if(r===null)return-1;if(n===null)return 1;if(r.kind==="arbitrary"&&n.kind==="arbitrary")return r.selector<n.selector?-1:1;if(r.kind==="arbitrary")return 1;if(n.kind==="arbitrary")return-1;let e=this.variants.get(r.root).order,i=this.variants.get(n.root).order,a=e-i;if(a!==0)return a;if(r.kind==="compound"&&n.kind==="compound"){let d=this.compare(r.variant,n.variant);return d!==0?d:r.modifier&&n.modifier?r.modifier.value<n.modifier.value?-1:1:r.modifier?1:n.modifier?-1:0}let l=this.compareFns.get(e);if(l!==void 0)return l(r,n);if(r.root!==n.root)return r.root<n.root?-1:1;let p=r.value,c=n.value;return p===null?-1:c===null||p.kind==="arbitrary"&&c.kind!=="arbitrary"?1:p.kind!=="arbitrary"&&c.kind==="arbitrary"||p.value<c.value?-1:1}keys(){return this.variants.keys()}entries(){return this.variants.entries()}set(r,{kind:n,applyFn:e,compounds:i,compoundsWith:a,order:l}){let p=this.variants.get(r);p?Object.assign(p,{kind:n,applyFn:e,compounds:i}):(l===void 0&&(this.lastOrder=this.nextOrder(),l=this.lastOrder),this.variants.set(r,{kind:n,applyFn:e,order:l,compoundsWith:a,compounds:i}))}nextOrder(){return this.groupOrder??this.lastOrder+1}};function de(t){let r=0;for(let n of t){if(n[0]==="@"){if(!n.startsWith("@media")&&!n.startsWith("@supports")&&!n.startsWith("@container"))return 0;r|=1;continue}if(n.includes("::"))return 0;r|=2}return r}function hr(t){let r=new kt;function n(d,f,{compounds:m}={}){m=m??de(f),r.static(d,g=>{g.nodes=f.map(y=>M(y,g.nodes))},{compounds:m})}n("*",[":is(& > *)"],{compounds:0}),n("**",[":is(& *)"],{compounds:0});function e(d,f){return f.map(m=>{m=m.trim();let g=O(m," ");return g[0]==="not"?g.slice(1).join(" "):d==="@container"?g[0][0]==="("?`not ${m}`:g[1]==="not"?`${g[0]} ${g.slice(2).join(" ")}`:`${g[0]} not ${g.slice(1).join(" ")}`:`not ${m}`})}let i=["@media","@supports","@container"];function a(d){for(let f of i){if(f!==d.name)continue;let m=O(d.params,",");return m.length>1?null:(m=e(d.name,m),U(d.name,m.join(", ")))}return null}function l(d){return d.includes("::")?null:`&:not(${O(d,",").map(m=>(m=m.replaceAll("&","*"),m)).join(", ")})`}r.compound("not",3,(d,f)=>{if(f.variant.kind==="arbitrary"&&f.variant.relative||f.modifier)return null;let m=!1;if(K([d],(g,{path:y})=>{if(g.kind!=="rule"&&g.kind!=="at-rule")return 0;if(g.nodes.length>0)return 0;let b=[],k=[];for(let x of y)x.kind==="at-rule"?b.push(x):x.kind==="rule"&&k.push(x);if(b.length>1)return 2;if(k.length>1)return 2;let A=[];for(let x of k){let V=l(x.selector);if(!V)return m=!1,2;A.push(j(V,[]))}for(let x of b){let V=a(x);if(!V)return m=!1,2;A.push(V)}return Object.assign(d,j("&",A)),m=!0,1}),d.kind==="rule"&&d.selector==="&"&&d.nodes.length===1&&Object.assign(d,d.nodes[0]),!m)return null}),r.suggest("not",()=>Array.from(r.keys()).filter(d=>r.compoundsWith("not",d))),r.compound("group",2,(d,f)=>{if(f.variant.kind==="arbitrary"&&f.variant.relative)return null;let m=f.modifier?`:where(.${t.prefix?`${t.prefix}\\:`:""}group\\/${f.modifier.value})`:`:where(.${t.prefix?`${t.prefix}\\:`:""}group)`,g=!1;if(K([d],(y,{path:b})=>{if(y.kind!=="rule")return 0;for(let A of b.slice(0,-1))if(A.kind==="rule")return g=!1,2;let k=y.selector.replaceAll("&",m);O(k,",").length>1&&(k=`:is(${k})`),y.selector=`&:is(${k} *)`,g=!0}),!g)return null}),r.suggest("group",()=>Array.from(r.keys()).filter(d=>r.compoundsWith("group",d))),r.compound("peer",2,(d,f)=>{if(f.variant.kind==="arbitrary"&&f.variant.relative)return null;let m=f.modifier?`:where(.${t.prefix?`${t.prefix}\\:`:""}peer\\/${f.modifier.value})`:`:where(.${t.prefix?`${t.prefix}\\:`:""}peer)`,g=!1;if(K([d],(y,{path:b})=>{if(y.kind!=="rule")return 0;for(let A of b.slice(0,-1))if(A.kind==="rule")return g=!1,2;let k=y.selector.replaceAll("&",m);O(k,",").length>1&&(k=`:is(${k})`),y.selector=`&:is(${k} ~ *)`,g=!0}),!g)return null}),r.suggest("peer",()=>Array.from(r.keys()).filter(d=>r.compoundsWith("peer",d))),n("first-letter",["&::first-letter"]),n("first-line",["&::first-line"]),n("marker",["& *::marker","&::marker","& *::-webkit-details-marker","&::-webkit-details-marker"]),n("selection",["& *::selection","&::selection"]),n("file",["&::file-selector-button"]),n("placeholder",["&::placeholder"]),n("backdrop",["&::backdrop"]),!1;{let d=function(){return D([U("@property","--tw-content",[s("syntax",'"*"'),s("initial-value",'""'),s("inherits","false")])])};var p=d;r.static("before",f=>{f.nodes=[j("&::before",[d(),s("content","var(--tw-content)"),...f.nodes])]},{compounds:0}),r.static("after",f=>{f.nodes=[j("&::after",[d(),s("content","var(--tw-content)"),...f.nodes])]},{compounds:0})}n("first",["&:first-child"]),n("last",["&:last-child"]),n("only",["&:only-child"]),n("odd",["&:nth-child(odd)"]),n("even",["&:nth-child(even)"]),n("first-of-type",["&:first-of-type"]),n("last-of-type",["&:last-of-type"]),n("only-of-type",["&:only-of-type"]),n("visited",["&:visited"]),n("target",["&:target"]),n("open",["&:is([open], :popover-open, :open)"]),n("default",["&:default"]),n("checked",["&:checked"]),n("indeterminate",["&:indeterminate"]),n("placeholder-shown",["&:placeholder-shown"]),n("autofill",["&:autofill"]),n("optional",["&:optional"]),n("required",["&:required"]),n("valid",["&:valid"]),n("invalid",["&:invalid"]),!1,n("in-range",["&:in-range"]),n("out-of-range",["&:out-of-range"]),n("read-only",["&:read-only"]),n("empty",["&:empty"]),n("focus-within",["&:focus-within"]),r.static("hover",d=>{d.nodes=[j("&:hover",[U("@media","(hover: hover)",d.nodes)])]}),n("focus",["&:focus"]),n("focus-visible",["&:focus-visible"]),n("active",["&:active"]),n("enabled",["&:enabled"]),n("disabled",["&:disabled"]),n("inert",["&:is([inert], [inert] *)"]),r.compound("in",2,(d,f)=>{if(f.modifier)return null;let m=!1;if(K([d],(g,{path:y})=>{if(g.kind!=="rule")return 0;for(let b of y.slice(0,-1))if(b.kind==="rule")return m=!1,2;g.selector=`:where(${g.selector.replaceAll("&","*")}) &`,m=!0}),!m)return null}),r.suggest("in",()=>Array.from(r.keys()).filter(d=>r.compoundsWith("in",d))),r.compound("has",2,(d,f)=>{if(f.modifier)return null;let m=!1;if(K([d],(g,{path:y})=>{if(g.kind!=="rule")return 0;for(let b of y.slice(0,-1))if(b.kind==="rule")return m=!1,2;g.selector=`&:has(${g.selector.replaceAll("&","*")})`,m=!0}),!m)return null}),r.suggest("has",()=>Array.from(r.keys()).filter(d=>r.compoundsWith("has",d))),r.functional("aria",(d,f)=>{if(!f.value||f.modifier)return null;f.value.kind==="arbitrary"?d.nodes=[j(`&[aria-${gr(f.value.value)}]`,d.nodes)]:d.nodes=[j(`&[aria-${f.value.value}="true"]`,d.nodes)]}),r.suggest("aria",()=>["busy","checked","disabled","expanded","hidden","pressed","readonly","required","selected"]),r.functional("data",(d,f)=>{if(!f.value||f.modifier)return null;d.nodes=[j(`&[data-${gr(f.value.value)}]`,d.nodes)]}),r.functional("nth",(d,f)=>{if(!f.value||f.modifier||f.value.kind==="named"&&!T(f.value.value))return null;d.nodes=[j(`&:nth-child(${f.value.value})`,d.nodes)]}),r.functional("nth-last",(d,f)=>{if(!f.value||f.modifier||f.value.kind==="named"&&!T(f.value.value))return null;d.nodes=[j(`&:nth-last-child(${f.value.value})`,d.nodes)]}),r.functional("nth-of-type",(d,f)=>{if(!f.value||f.modifier||f.value.kind==="named"&&!T(f.value.value))return null;d.nodes=[j(`&:nth-of-type(${f.value.value})`,d.nodes)]}),r.functional("nth-last-of-type",(d,f)=>{if(!f.value||f.modifier||f.value.kind==="named"&&!T(f.value.value))return null;d.nodes=[j(`&:nth-last-of-type(${f.value.value})`,d.nodes)]}),r.functional("supports",(d,f)=>{if(!f.value||f.modifier)return null;let m=f.value.value;if(m===null)return null;if(/^[\w-]*\s*\(/.test(m)){let g=m.replace(/\b(and|or|not)\b/g," $1 ");d.nodes=[U("@supports",g,d.nodes)];return}m.includes(":")||(m=`${m}: var(--tw)`),(m[0]!=="("||m[m.length-1]!==")")&&(m=`(${m})`),d.nodes=[U("@supports",m,d.nodes)]},{compounds:1}),n("motion-safe",["@media (prefers-reduced-motion: no-preference)"]),n("motion-reduce",["@media (prefers-reduced-motion: reduce)"]),n("contrast-more",["@media (prefers-contrast: more)"]),n("contrast-less",["@media (prefers-contrast: less)"]);{let d=function(f,m,g,y){if(f===m)return 0;let b=y.get(f);if(b===null)return g==="asc"?-1:1;let k=y.get(m);return k===null?g==="asc"?1:-1:ce(b,k,g)};var c=d;{let f=t.namespace("--breakpoint"),m=new z(g=>{switch(g.kind){case"static":return t.resolveValue(g.root,["--breakpoint"])??null;case"functional":{if(!g.value||g.modifier)return null;let y=null;return g.value.kind==="arbitrary"?y=g.value.value:g.value.kind==="named"&&(y=t.resolveValue(g.value.value,["--breakpoint"])),!y||y.includes("var(")?null:y}case"arbitrary":case"compound":return null}});r.group(()=>{r.functional("max",(g,y)=>{if(y.modifier)return null;let b=m.get(y);if(b===null)return null;g.nodes=[U("@media",`(width < ${b})`,g.nodes)]},{compounds:1})},(g,y)=>d(g,y,"desc",m)),r.suggest("max",()=>Array.from(f.keys()).filter(g=>g!==null)),r.group(()=>{for(let[g,y]of t.namespace("--breakpoint"))g!==null&&r.static(g,b=>{b.nodes=[U("@media",`(width >= ${y})`,b.nodes)]},{compounds:1});r.functional("min",(g,y)=>{if(y.modifier)return null;let b=m.get(y);if(b===null)return null;g.nodes=[U("@media",`(width >= ${b})`,g.nodes)]},{compounds:1})},(g,y)=>d(g,y,"asc",m)),r.suggest("min",()=>Array.from(f.keys()).filter(g=>g!==null))}{let f=t.namespace("--container"),m=new z(g=>{switch(g.kind){case"functional":{if(g.value===null)return null;let y=null;return g.value.kind==="arbitrary"?y=g.value.value:g.value.kind==="named"&&(y=t.resolveValue(g.value.value,["--container"])),!y||y.includes("var(")?null:y}case"static":case"arbitrary":case"compound":return null}});r.group(()=>{r.functional("@max",(g,y)=>{let b=m.get(y);if(b===null)return null;g.nodes=[U("@container",y.modifier?`${y.modifier.value} (width < ${b})`:`(width < ${b})`,g.nodes)]},{compounds:1})},(g,y)=>d(g,y,"desc",m)),r.suggest("@max",()=>Array.from(f.keys()).filter(g=>g!==null)),r.group(()=>{r.functional("@",(g,y)=>{let b=m.get(y);if(b===null)return null;g.nodes=[U("@container",y.modifier?`${y.modifier.value} (width >= ${b})`:`(width >= ${b})`,g.nodes)]},{compounds:1}),r.functional("@min",(g,y)=>{let b=m.get(y);if(b===null)return null;g.nodes=[U("@container",y.modifier?`${y.modifier.value} (width >= ${b})`:`(width >= ${b})`,g.nodes)]},{compounds:1})},(g,y)=>d(g,y,"asc",m)),r.suggest("@min",()=>Array.from(f.keys()).filter(g=>g!==null)),r.suggest("@",()=>Array.from(f.keys()).filter(g=>g!==null))}}return n("portrait",["@media (orientation: portrait)"]),n("landscape",["@media (orientation: landscape)"]),n("ltr",['&:where(:dir(ltr), [dir="ltr"], [dir="ltr"] *)']),n("rtl",['&:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *)']),n("dark",["@media (prefers-color-scheme: dark)"]),n("starting",["@starting-style"]),n("print",["@media print"]),n("forced-colors",["@media (forced-colors: active)"]),!1,!1,!1,r}function gr(t){if(t.includes("=")){let[r,...n]=O(t,"="),e=n.join("=").trim();if(e[0]==="'"||e[0]==='"')return t;if(e.length>1){let i=e[e.length-1];if(e[e.length-2]===" "&&(i==="i"||i==="I"||i==="s"||i==="S"))return`${r}="${e.slice(0,-2)}" ${i}`}return`${r}="${e}"`}return t}function wt(t,r){K(t,(n,{replaceWith:e})=>{if(n.kind==="at-rule"&&n.name==="@slot")e(r);else if(n.kind==="at-rule"&&(n.name==="@keyframes"||n.name==="@property"))return Object.assign(n,D([U(n.name,n.params,n.nodes)])),1})}function br(t){let r=sr(t),n=hr(t),e=new z(c=>er(c,p)),i=new z(c=>Array.from(Xt(c,p))),a=new z(c=>{let d=yr(c,p);try{be(d.map(({node:f})=>f),p)}catch{return[]}return d}),l=new z(c=>{for(let d of ze(c))t.markUsedVariable(d)}),p={theme:t,utilities:r,variants:n,invalidCandidates:new Set,important:!1,candidatesToCss(c){let d=[];for(let f of c){let m=!1,{astNodes:g}=oe([f],this,{onInvalidCandidate(){m=!0}});g=ue(g,p),g.length===0||m?d.push(null):d.push(J(g))}return d},getClassOrder(c){return mr(this,c)},getClassList(){return dr(this)},getVariants(){return pr(this)},parseCandidate(c){return i.get(c)},parseVariant(c){return e.get(c)},compileAstNodes(c){return a.get(c)},getVariantOrder(){let c=Array.from(e.values());c.sort((g,y)=>this.variants.compare(g,y));let d=new Map,f,m=0;for(let g of c)g!==null&&(f!==void 0&&this.variants.compare(f,g)!==0&&m++,d.set(g,m),f=g);return d},resolveThemeValue(c,d=!0){let f=c.lastIndexOf("/"),m=null;f!==-1&&(m=c.slice(f+1).trim(),c=c.slice(0,f).trim());let g=t.resolve(null,[c],d?1:0)??void 0;return m&&g?Z(g,m):g},trackUsedVariables(c){l.get(c)}};return p}var xt=["container-type","pointer-events","visibility","position","inset","inset-inline","inset-block","inset-inline-start","inset-inline-end","top","right","bottom","left","isolation","z-index","order","grid-column","grid-column-start","grid-column-end","grid-row","grid-row-start","grid-row-end","float","clear","--tw-container-component","margin","margin-inline","margin-block","margin-inline-start","margin-inline-end","margin-top","margin-right","margin-bottom","margin-left","box-sizing","display","field-sizing","aspect-ratio","height","max-height","min-height","width","max-width","min-width","flex","flex-shrink","flex-grow","flex-basis","table-layout","caption-side","border-collapse","border-spacing","transform-origin","translate","--tw-translate-x","--tw-translate-y","--tw-translate-z","scale","--tw-scale-x","--tw-scale-y","--tw-scale-z","rotate","--tw-rotate-x","--tw-rotate-y","--tw-rotate-z","--tw-skew-x","--tw-skew-y","transform","animation","cursor","touch-action","--tw-pan-x","--tw-pan-y","--tw-pinch-zoom","resize","scroll-snap-type","--tw-scroll-snap-strictness","scroll-snap-align","scroll-snap-stop","scroll-margin","scroll-margin-inline","scroll-margin-block","scroll-margin-inline-start","scroll-margin-inline-end","scroll-margin-top","scroll-margin-right","scroll-margin-bottom","scroll-margin-left","scroll-padding","scroll-padding-inline","scroll-padding-block","scroll-padding-inline-start","scroll-padding-inline-end","scroll-padding-top","scroll-padding-right","scroll-padding-bottom","scroll-padding-left","list-style-position","list-style-type","list-style-image","appearance","columns","break-before","break-inside","break-after","grid-auto-columns","grid-auto-flow","grid-auto-rows","grid-template-columns","grid-template-rows","flex-direction","flex-wrap","place-content","place-items","align-content","align-items","justify-content","justify-items","gap","column-gap","row-gap","--tw-space-x-reverse","--tw-space-y-reverse","divide-x-width","divide-y-width","--tw-divide-y-reverse","divide-style","divide-color","place-self","align-self","justify-self","overflow","overflow-x","overflow-y","overscroll-behavior","overscroll-behavior-x","overscroll-behavior-y","scroll-behavior","border-radius","border-start-radius","border-end-radius","border-top-radius","border-right-radius","border-bottom-radius","border-left-radius","border-start-start-radius","border-start-end-radius","border-end-end-radius","border-end-start-radius","border-top-left-radius","border-top-right-radius","border-bottom-right-radius","border-bottom-left-radius","border-width","border-inline-width","border-block-width","border-inline-start-width","border-inline-end-width","border-top-width","border-right-width","border-bottom-width","border-left-width","border-style","border-inline-style","border-block-style","border-inline-start-style","border-inline-end-style","border-top-style","border-right-style","border-bottom-style","border-left-style","border-color","border-inline-color","border-block-color","border-inline-start-color","border-inline-end-color","border-top-color","border-right-color","border-bottom-color","border-left-color","background-color","background-image","--tw-gradient-position","--tw-gradient-stops","--tw-gradient-via-stops","--tw-gradient-from","--tw-gradient-from-position","--tw-gradient-via","--tw-gradient-via-position","--tw-gradient-to","--tw-gradient-to-position","box-decoration-break","background-size","background-attachment","background-clip","background-position","background-repeat","background-origin","fill","stroke","stroke-width","object-fit","object-position","padding","padding-inline","padding-block","padding-inline-start","padding-inline-end","padding-top","padding-right","padding-bottom","padding-left","text-align","text-indent","vertical-align","font-family","font-size","line-height","font-weight","letter-spacing","text-wrap","overflow-wrap","word-break","text-overflow","hyphens","white-space","color","text-transform","font-style","font-stretch","font-variant-numeric","text-decoration-line","text-decoration-color","text-decoration-style","text-decoration-thickness","text-underline-offset","-webkit-font-smoothing","placeholder-color","caret-color","accent-color","color-scheme","opacity","background-blend-mode","mix-blend-mode","box-shadow","--tw-shadow","--tw-shadow-color","--tw-ring-shadow","--tw-ring-color","--tw-inset-shadow","--tw-inset-shadow-color","--tw-inset-ring-shadow","--tw-inset-ring-color","--tw-ring-offset-width","--tw-ring-offset-color","outline","outline-width","outline-offset","outline-color","--tw-blur","--tw-brightness","--tw-contrast","--tw-drop-shadow","--tw-grayscale","--tw-hue-rotate","--tw-invert","--tw-saturate","--tw-sepia","filter","--tw-backdrop-blur","--tw-backdrop-brightness","--tw-backdrop-contrast","--tw-backdrop-grayscale","--tw-backdrop-hue-rotate","--tw-backdrop-invert","--tw-backdrop-opacity","--tw-backdrop-saturate","--tw-backdrop-sepia","backdrop-filter","transition-property","transition-behavior","transition-delay","transition-duration","transition-timing-function","will-change","contain","content","forced-color-adjust"];function oe(t,r,{onInvalidCandidate:n}={}){let e=new Map,i=[],a=new Map;for(let p of t){if(r.invalidCandidates.has(p)){n?.(p);continue}let c=r.parseCandidate(p);if(c.length===0){n?.(p);continue}a.set(p,c)}let l=r.getVariantOrder();for(let[p,c]of a){let d=!1;for(let f of c){let m=r.compileAstNodes(f);if(m.length!==0){d=!0;for(let{node:g,propertySort:y}of m){let b=0n;for(let k of f.variants)b|=1n<<BigInt(l.get(k));e.set(g,{properties:y,variants:b,candidate:p}),i.push(g)}}}d||n?.(p)}return i.sort((p,c)=>{let d=e.get(p),f=e.get(c);if(d.variants-f.variants!==0n)return Number(d.variants-f.variants);let m=0;for(;m<d.properties.order.length&&m<f.properties.order.length&&d.properties.order[m]===f.properties.order[m];)m+=1;return(d.properties.order[m]??1/0)-(f.properties.order[m]??1/0)||f.properties.count-d.properties.count||He(d.candidate,f.candidate)}),{astNodes:i,nodeSorting:e}}function yr(t,r){let n=hi(t,r);if(n.length===0)return[];let e=[],i=`.${ne(t.raw)}`;for(let a of n){let l=bi(a);(t.important||r.important)&&kr(a);let p={kind:"rule",selector:i,nodes:a};for(let c of t.variants)if(ye(p,c,r.variants)===null)return[];e.push({node:p,propertySort:l})}return e}function ye(t,r,n,e=0){if(r.kind==="arbitrary"){if(r.relative&&e===0)return null;t.nodes=[M(r.selector,t.nodes)];return}let{applyFn:i}=n.get(r.root);if(r.kind==="compound"){let l=U("@slot");if(ye(l,r.variant,n,e+1)===null||r.root==="not"&&l.nodes.length>1)return null;for(let c of l.nodes)if(c.kind!=="rule"&&c.kind!=="at-rule"||i(c,r)===null)return null;K(l.nodes,c=>{if((c.kind==="rule"||c.kind==="at-rule")&&c.nodes.length<=0)return c.nodes=t.nodes,1}),t.nodes=l.nodes;return}if(i(t,r)===null)return null}function vr(t){let r=t.options?.types??[];return r.length>1&&r.includes("any")}function hi(t,r){if(t.kind==="arbitrary"){let l=t.value;return t.modifier&&(l=W(l,t.modifier,r.theme)),l===null?[]:[[s(t.property,l)]]}let n=r.utilities.get(t.root)??[],e=[],i=n.filter(l=>!vr(l));for(let l of i){if(l.kind!==t.kind)continue;let p=l.compileFn(t);if(p!==void 0){if(p===null)return e;e.push(p)}}if(e.length>0)return e;let a=n.filter(l=>vr(l));for(let l of a){if(l.kind!==t.kind)continue;let p=l.compileFn(t);if(p!==void 0){if(p===null)return e;e.push(p)}}return e}function kr(t){for(let r of t)r.kind!=="at-root"&&(r.kind==="declaration"?r.important=!0:(r.kind==="rule"||r.kind==="at-rule")&&kr(r.nodes))}function bi(t){let r=new Set,n=0,e=t.slice(),i=!1;for(;e.length>0;){let a=e.shift();if(a.kind==="declaration"){if(a.value===void 0||(n++,i))continue;if(a.property==="--tw-sort"){let p=xt.indexOf(a.value??"");if(p!==-1){r.add(p),i=!0;continue}}let l=xt.indexOf(a.property);l!==-1&&r.add(l)}else if(a.kind==="rule"||a.kind==="at-rule")for(let l of a.nodes)e.push(l)}return{order:Array.from(r).sort((a,l)=>a-l),count:n}}function $e(t,r){let n=0,e=M("&",t),i=new Set,a=new z(()=>new Set),l=new z(()=>new Set);K([e],(m,{parent:g})=>{if(m.kind==="at-rule"){if(m.name==="@keyframes")return K(m.nodes,y=>{if(y.kind==="at-rule"&&y.name==="@apply")throw new Error("You cannot use `@apply` inside `@keyframes`.")}),1;if(m.name==="@utility"){let y=m.params.replace(/-\*$/,"");l.get(y).add(m),K(m.nodes,b=>{if(!(b.kind!=="at-rule"||b.name!=="@apply")){i.add(m);for(let k of wr(b,r))a.get(m).add(k)}});return}if(m.name==="@apply"){if(g===null)return;n|=1,i.add(g);for(let y of wr(m,r))a.get(g).add(y)}}});let p=new Set,c=[],d=new Set;function f(m,g=[]){if(!p.has(m)){if(d.has(m)){let y=g[(g.indexOf(m)+1)%g.length];throw m.kind==="at-rule"&&m.name==="@utility"&&y.kind==="at-rule"&&y.name==="@utility"&&K(m.nodes,b=>{if(b.kind!=="at-rule"||b.name!=="@apply")return;let k=b.params.split(/\s+/g);for(let A of k)for(let x of r.parseCandidate(A))switch(x.kind){case"arbitrary":break;case"static":case"functional":if(y.params.replace(/-\*$/,"")===x.root)throw new Error(`You cannot \`@apply\` the \`${A}\` utility here because it creates a circular dependency.`);break;default:}}),new Error(`Circular dependency detected:

${J([m])}
Relies on:

${J([y])}`)}d.add(m);for(let y of a.get(m))for(let b of l.get(y))g.push(m),f(b,g),g.pop();p.add(m),d.delete(m),c.push(m)}}for(let m of i)f(m);for(let m of c)if("nodes"in m)for(let g=0;g<m.nodes.length;g++){let y=m.nodes[g];if(y.kind!=="at-rule"||y.name!=="@apply")continue;let b=y.params.split(/\s+/g);{let k=oe(b,r,{onInvalidCandidate:x=>{throw new Error(`Cannot apply unknown utility class: ${x}`)}}).astNodes,A=[];for(let x of k)if(x.kind==="rule")for(let V of x.nodes)A.push(V);else A.push(x);m.nodes.splice(g,1,...A)}}return n}function*wr(t,r){for(let n of t.params.split(/\s+/g))for(let e of r.parseCandidate(n))switch(e.kind){case"arbitrary":break;case"static":case"functional":yield e.root;break;default:}}async function At(t,r,n,e=0){let i=0,a=[];return K(t,(l,{replaceWith:p})=>{if(l.kind==="at-rule"&&(l.name==="@import"||l.name==="@reference")){let c=yi(F(l.params));if(c===null)return;l.name==="@reference"&&(c.media="reference"),i|=2;let{uri:d,layer:f,media:m,supports:g}=c;if(d.startsWith("data:")||d.startsWith("http://")||d.startsWith("https://"))return;let y=X({},[]);return a.push((async()=>{if(e>100)throw new Error(`Exceeded maximum recursion depth while resolving \`${d}\` in \`${r}\`)`);let b=await n(d,r),k=me(b.content);await At(k,b.base,n,e+1),y.nodes=vi([X({base:b.base},k)],f,m,g)})()),p(y),1}}),a.length>0&&await Promise.all(a),i}function yi(t){let r,n=null,e=null,i=null;for(let a=0;a<t.length;a++){let l=t[a];if(l.kind!=="separator"){if(l.kind==="word"&&!r){if(!l.value||l.value[0]!=='"'&&l.value[0]!=="'")return null;r=l.value.slice(1,-1);continue}if(l.kind==="function"&&l.value.toLowerCase()==="url"||!r)return null;if((l.kind==="word"||l.kind==="function")&&l.value.toLowerCase()==="layer"){if(n)return null;if(i)throw new Error("`layer(\u2026)` in an `@import` should come before any other functions or conditions");"nodes"in l?n=H(l.nodes):n="";continue}if(l.kind==="function"&&l.value.toLowerCase()==="supports"){if(i)return null;i=H(l.nodes);continue}e=H(t.slice(a));break}}return r?{uri:r,layer:n,media:e,supports:i}:null}function vi(t,r,n,e){let i=t;return r!==null&&(i=[U("@layer",r,i)]),n!==null&&(i=[U("@media",n,i)]),e!==null&&(i=[U("@supports",e[0]==="("?e:`(${e})`,i)]),i}function ve(t,r=null){return Array.isArray(t)&&t.length===2&&typeof t[1]=="object"&&typeof t[1]!==null?r?t[1][r]??null:t[0]:Array.isArray(t)&&r===null?t.join(", "):typeof t=="string"&&r===null?t:null}function xr(t,{theme:r},n){for(let e of n){let i=Ye([e]);i&&t.theme.clearNamespace(`--${i}`,4)}for(let[e,i]of ki(r)){if(typeof i!="string"&&typeof i!="number")continue;if(typeof i=="string"&&(i=i.replace(/<alpha-value>/g,"1")),e[0]==="opacity"&&(typeof i=="number"||typeof i=="string")){let l=typeof i=="string"?parseFloat(i):i;l>=0&&l<=1&&(i=l*100+"%")}let a=Ye(e);a&&t.theme.add(`--${a}`,""+i,7)}if(Object.hasOwn(r,"fontFamily")){let e=5;{let i=ve(r.fontFamily.sans);i&&t.theme.hasDefault("--font-sans")&&(t.theme.add("--default-font-family",i,e),t.theme.add("--default-font-feature-settings",ve(r.fontFamily.sans,"fontFeatureSettings")??"normal",e),t.theme.add("--default-font-variation-settings",ve(r.fontFamily.sans,"fontVariationSettings")??"normal",e))}{let i=ve(r.fontFamily.mono);i&&t.theme.hasDefault("--font-mono")&&(t.theme.add("--default-mono-font-family",i,e),t.theme.add("--default-mono-font-feature-settings",ve(r.fontFamily.mono,"fontFeatureSettings")??"normal",e),t.theme.add("--default-mono-font-variation-settings",ve(r.fontFamily.mono,"fontVariationSettings")??"normal",e))}}return r}function ki(t){let r=[];return Ar(t,[],(n,e)=>{if(xi(n))return r.push([e,n]),1;if(Ai(n)){r.push([e,n[0]]);for(let i of Reflect.ownKeys(n[1]))r.push([[...e,`-${i}`],n[1][i]]);return 1}if(Array.isArray(n)&&n.every(i=>typeof i=="string"))return r.push([e,n.join(", ")]),1}),r}var wi=/^[a-zA-Z0-9-_%/\.]+$/;function Ye(t){if(t[0]==="container")return null;t=structuredClone(t),t[0]==="animation"&&(t[0]="animate"),t[0]==="aspectRatio"&&(t[0]="aspect"),t[0]==="borderRadius"&&(t[0]="radius"),t[0]==="boxShadow"&&(t[0]="shadow"),t[0]==="colors"&&(t[0]="color"),t[0]==="containers"&&(t[0]="container"),t[0]==="fontFamily"&&(t[0]="font"),t[0]==="fontSize"&&(t[0]="text"),t[0]==="letterSpacing"&&(t[0]="tracking"),t[0]==="lineHeight"&&(t[0]="leading"),t[0]==="maxWidth"&&(t[0]="container"),t[0]==="screens"&&(t[0]="breakpoint"),t[0]==="transitionTimingFunction"&&(t[0]="ease");for(let r of t)if(!wi.test(r))return null;return t.map((r,n,e)=>r==="1"&&n!==e.length-1?"":r).map(r=>r.replaceAll(".","_").replace(/([a-z])([A-Z])/g,(n,e,i)=>`${e}-${i.toLowerCase()}`)).filter((r,n)=>r!=="DEFAULT"||n!==t.length-1).join("-")}function xi(t){return typeof t=="number"||typeof t=="string"}function Ai(t){if(!Array.isArray(t)||t.length!==2||typeof t[0]!="string"&&typeof t[0]!="number"||t[1]===void 0||t[1]===null||typeof t[1]!="object")return!1;for(let r of Reflect.ownKeys(t[1]))if(typeof r!="string"||typeof t[1][r]!="string"&&typeof t[1][r]!="number")return!1;return!0}function Ar(t,r=[],n){for(let e of Reflect.ownKeys(t)){let i=t[e];if(i==null)continue;let a=[...r,e],l=n(i,a)??0;if(l!==1){if(l===2)return 2;if(!(!Array.isArray(i)&&typeof i!="object")&&Ar(i,a,n)===2)return 2}}}function Je(t){let r=[];for(let n of O(t,".")){if(!n.includes("[")){r.push(n);continue}let e=0;for(;;){let i=n.indexOf("[",e),a=n.indexOf("]",i);if(i===-1||a===-1)break;i>e&&r.push(n.slice(e,i)),r.push(n.slice(i+1,a)),e=a+1}e<=n.length-1&&r.push(n.slice(e))}return r}function ke(t){if(Object.prototype.toString.call(t)!=="[object Object]")return!1;let r=Object.getPrototypeOf(t);return r===null||Object.getPrototypeOf(r)===null}function Te(t,r,n,e=[]){for(let i of r)if(i!=null)for(let a of Reflect.ownKeys(i)){e.push(a);let l=n(t[a],i[a],e);l!==void 0?t[a]=l:!ke(t[a])||!ke(i[a])?t[a]=i[a]:t[a]=Te({},[t[a],i[a]],n,e),e.pop()}return t}function Ze(t,r,n){return function(i,a){let l=i.lastIndexOf("/"),p=null;l!==-1&&(p=i.slice(l+1).trim(),i=i.slice(0,l).trim());let c=(()=>{let d=Je(i),[f,m]=Ci(t.theme,d),g=n(Cr(r()??{},d)??null);if(typeof g=="string"&&(g=g.replace("<alpha-value>","1")),typeof f!="object")return typeof m!="object"&&m&4?g??f:f;if(g!==null&&typeof g=="object"&&!Array.isArray(g)){let y=Te({},[g],(b,k)=>k);if(f===null&&Object.hasOwn(g,"__CSS_VALUES__")){let b={};for(let k in g.__CSS_VALUES__)b[k]=g[k],delete y[k];f=b}for(let b in f)b!=="__CSS_VALUES__"&&(g?.__CSS_VALUES__?.[b]&4&&Cr(y,b.split("-"))!==void 0||(y[se(b)]=f[b]));return y}if(Array.isArray(f)&&Array.isArray(m)&&Array.isArray(g)){let y=f[0],b=f[1];m[0]&4&&(y=g[0]??y);for(let k of Object.keys(b))m[1][k]&4&&(b[k]=g[1][k]??b[k]);return[y,b]}return f??g})();return p&&typeof c=="string"&&(c=Z(c,p)),c??a}}function Ci(t,r){if(r.length===1&&r[0].startsWith("--"))return[t.get([r[0]]),t.getOptions(r[0])];let n=Ye(r),e=new Map,i=new z(()=>new Map),a=t.namespace(`--${n}`);if(a.size===0)return[null,0];let l=new Map;for(let[f,m]of a){if(!f||!f.includes("--")){e.set(f,m),l.set(f,t.getOptions(f?`--${n}-${f}`:`--${n}`));continue}let g=f.indexOf("--"),y=f.slice(0,g),b=f.slice(g+2);b=b.replace(/-([a-z])/g,(k,A)=>A.toUpperCase()),i.get(y===""?null:y).set(b,[m,t.getOptions(`--${n}${f}`)])}let p=t.getOptions(`--${n}`);for(let[f,m]of i){let g=e.get(f);if(typeof g!="string")continue;let y={},b={};for(let[k,[A,x]]of m)y[k]=A,b[k]=x;e.set(f,[g,y]),l.set(f,[p,b])}let c={},d={};for(let[f,m]of e)Sr(c,[f??"DEFAULT"],m);for(let[f,m]of l)Sr(d,[f??"DEFAULT"],m);return r[r.length-1]==="DEFAULT"?[c?.DEFAULT??null,d.DEFAULT??0]:"DEFAULT"in c&&Object.keys(c).length===1?[c.DEFAULT,d.DEFAULT??0]:(c.__CSS_VALUES__=d,[c,d])}function Cr(t,r){for(let n=0;n<r.length;++n){let e=r[n];if(t?.[e]===void 0){if(r[n+1]===void 0)return;r[n+1]=`${e}-${r[n+1]}`;continue}t=t[e]}return t}function Sr(t,r,n){for(let e of r.slice(0,-1))t[e]===void 0&&(t[e]={}),t=t[e];t[r[r.length-1]]=n}function Si(t){return{kind:"combinator",value:t}}function Ni(t,r){return{kind:"function",value:t,nodes:r}}function Ve(t){return{kind:"selector",value:t}}function $i(t){return{kind:"separator",value:t}}function Ti(t){return{kind:"value",value:t}}function Ee(t,r,n=null){for(let e=0;e<t.length;e++){let i=t[e],a=!1,l=0,p=r(i,{parent:n,replaceWith(c){a||(a=!0,Array.isArray(c)?c.length===0?(t.splice(e,1),l=0):c.length===1?(t[e]=c[0],l=1):(t.splice(e,1,...c),l=c.length):(t[e]=c,l=1))}})??0;if(a){p===0?e--:e+=l-1;continue}if(p===2)return 2;if(p!==1&&i.kind==="function"&&Ee(i.nodes,r,i)===2)return 2}}function Re(t){let r="";for(let n of t)switch(n.kind){case"combinator":case"selector":case"separator":case"value":{r+=n.value;break}case"function":r+=n.value+"("+Re(n.nodes)+")"}return r}var Nr=92,Vi=93,$r=41,Ei=58,Tr=44,Ri=34,Oi=46,Vr=62,Er=10,Pi=35,Rr=91,Or=40,Pr=43,Ui=39,Ur=32,Kr=9,_r=126;function Qe(t){t=t.replaceAll(`\r
`,`
`);let r=[],n=[],e=null,i="",a;for(let l=0;l<t.length;l++){let p=t.charCodeAt(l);switch(p){case Tr:case Vr:case Er:case Ur:case Pr:case Kr:case _r:{if(i.length>0){let g=Ve(i);e?e.nodes.push(g):r.push(g),i=""}let c=l,d=l+1;for(;d<t.length&&(a=t.charCodeAt(d),!(a!==Tr&&a!==Vr&&a!==Er&&a!==Ur&&a!==Pr&&a!==Kr&&a!==_r));d++);l=d-1;let f=t.slice(c,d),m=f.trim()===","?$i(f):Si(f);e?e.nodes.push(m):r.push(m);break}case Or:{let c=Ni(i,[]);if(i="",c.value!==":not"&&c.value!==":where"&&c.value!==":has"&&c.value!==":is"){let d=l+1,f=0;for(let g=l+1;g<t.length;g++){if(a=t.charCodeAt(g),a===Or){f++;continue}if(a===$r){if(f===0){l=g;break}f--}}let m=l;c.nodes.push(Ti(t.slice(d,m))),i="",l=m,e?e.nodes.push(c):r.push(c);break}e?e.nodes.push(c):r.push(c),n.push(c),e=c;break}case $r:{let c=n.pop();if(i.length>0){let d=Ve(i);c.nodes.push(d),i=""}n.length>0?e=n[n.length-1]:e=null;break}case Oi:case Ei:case Pi:{if(i.length>0){let c=Ve(i);e?e.nodes.push(c):r.push(c)}i=String.fromCharCode(p);break}case Rr:{if(i.length>0){let f=Ve(i);e?e.nodes.push(f):r.push(f)}i="";let c=l,d=0;for(let f=l+1;f<t.length;f++){if(a=t.charCodeAt(f),a===Rr){d++;continue}if(a===Vi){if(d===0){l=f;break}d--}}i+=t.slice(c,l+1);break}case Ui:case Ri:{let c=l;for(let d=l+1;d<t.length;d++)if(a=t.charCodeAt(d),a===Nr)d+=1;else if(a===p){l=d;break}i+=t.slice(c,l+1);break}case Nr:{let c=t.charCodeAt(l+1);i+=String.fromCharCode(p)+String.fromCharCode(c),l+=1;break}default:i+=String.fromCharCode(p)}}return i.length>0&&r.push(Ve(i)),r}var Dr=/^[a-z@][a-zA-Z0-9/%._-]*$/;function Ct({designSystem:t,ast:r,resolvedConfig:n,featuresRef:e,referenceMode:i}){let a={addBase(l){if(i)return;let p=ee(l);e.current|=be(p,t),r.push(U("@layer","base",p))},addVariant(l,p){if(!Ge.test(l))throw new Error(`\`addVariant('${l}')\` defines an invalid variant name. Variants should only contain alphanumeric, dashes or underscore characters.`);typeof p=="string"||Array.isArray(p)?t.variants.static(l,c=>{c.nodes=jr(p,c.nodes)},{compounds:de(typeof p=="string"?[p]:p)}):typeof p=="object"&&t.variants.fromAst(l,ee(p))},matchVariant(l,p,c){function d(m,g,y){let b=p(m,{modifier:g?.value??null});return jr(b,y)}let f=Object.keys(c?.values??{});t.variants.group(()=>{t.variants.functional(l,(m,g)=>{if(!g.value){if(c?.values&&"DEFAULT"in c.values){m.nodes=d(c.values.DEFAULT,g.modifier,m.nodes);return}return null}if(g.value.kind==="arbitrary")m.nodes=d(g.value.value,g.modifier,m.nodes);else if(g.value.kind==="named"&&c?.values){let y=c.values[g.value.value];if(typeof y!="string")return;m.nodes=d(y,g.modifier,m.nodes)}})},(m,g)=>{if(m.kind!=="functional"||g.kind!=="functional")return 0;let y=m.value?m.value.value:"DEFAULT",b=g.value?g.value.value:"DEFAULT",k=c?.values?.[y]??y,A=c?.values?.[b]??b;if(c&&typeof c.sort=="function")return c.sort({value:k,modifier:m.modifier?.value??null},{value:A,modifier:g.modifier?.value??null});let x=f.indexOf(y),V=f.indexOf(b);return x=x===-1?f.length:x,V=V===-1?f.length:V,x!==V?x-V:k<A?-1:1})},addUtilities(l){l=Array.isArray(l)?l:[l];let p=l.flatMap(d=>Object.entries(d));p=p.flatMap(([d,f])=>O(d,",").map(m=>[m.trim(),f]));let c=new z(()=>[]);for(let[d,f]of p){if(d.startsWith("@keyframes ")){i||r.push(M(d,ee(f)));continue}let m=Qe(d),g=!1;if(Ee(m,y=>{if(y.kind==="selector"&&y.value[0]==="."&&Dr.test(y.value.slice(1))){let b=y.value;y.value="&";let k=Re(m),A=b.slice(1),x=k==="&"?ee(f):[M(k,ee(f))];c.get(A).push(...x),g=!0,y.value=b;return}if(y.kind==="function"&&y.value===":not")return 1}),!g)throw new Error(`\`addUtilities({ '${d}' : \u2026 })\` defines an invalid utility selector. Utilities must be a single class name and start with a lowercase letter, eg. \`.scrollbar-none\`.`)}for(let[d,f]of c)t.theme.prefix&&K(f,m=>{if(m.kind==="rule"){let g=Qe(m.selector);Ee(g,y=>{y.kind==="selector"&&y.value[0]==="."&&(y.value=`.${t.theme.prefix}\\:${y.value.slice(1)}`)}),m.selector=Re(g)}}),t.utilities.static(d,m=>{let g=structuredClone(f);return Ir(g,d,m.raw),e.current|=$e(g,t),g})},matchUtilities(l,p){let c=p?.type?Array.isArray(p?.type)?p.type:[p.type]:["any"];for(let[f,m]of Object.entries(l)){let g=function({negative:y}){return b=>{if(b.value?.kind==="arbitrary"&&c.length>0&&!c.includes("any")&&(b.value.dataType&&!c.includes(b.value.dataType)||!b.value.dataType&&!L(b.value.value,c)))return;let k=c.includes("color"),A=null,x=!1;{let R=p?.values??{};k&&(R=Object.assign({inherit:"inherit",transparent:"transparent",current:"currentColor"},R)),b.value?b.value.kind==="arbitrary"?A=b.value.value:b.value.fraction&&R[b.value.fraction]?(A=R[b.value.fraction],x=!0):R[b.value.value]?A=R[b.value.value]:R.__BARE_VALUE__&&(A=R.__BARE_VALUE__(b.value)??null,x=(b.value.fraction!==null&&A?.includes("/"))??!1):A=R.DEFAULT??null}if(A===null)return;let V;{let R=p?.modifiers??null;b.modifier?R==="any"||b.modifier.kind==="arbitrary"?V=b.modifier.value:R?.[b.modifier.value]?V=R[b.modifier.value]:k&&!Number.isNaN(Number(b.modifier.value))?V=`${b.modifier.value}%`:V=null:V=null}if(b.modifier&&V===null&&!x)return b.value?.kind==="arbitrary"?null:void 0;k&&V!==null&&(A=Z(A,V)),y&&(A=`calc(${A} * -1)`);let P=ee(m(A,{modifier:V}));return Ir(P,f,b.raw),e.current|=$e(P,t),P}};var d=g;if(!Dr.test(f))throw new Error(`\`matchUtilities({ '${f}' : \u2026 })\` defines an invalid utility name. Utilities should be alphanumeric and start with a lowercase letter, eg. \`scrollbar\`.`);p?.supportsNegativeValues&&t.utilities.functional(`-${f}`,g({negative:!0}),{types:c}),t.utilities.functional(f,g({negative:!1}),{types:c}),t.utilities.suggest(f,()=>{let y=p?.values??{},b=new Set(Object.keys(y));b.delete("__BARE_VALUE__"),b.has("DEFAULT")&&(b.delete("DEFAULT"),b.add(null));let k=p?.modifiers??{},A=k==="any"?[]:Object.keys(k);return[{supportsNegative:p?.supportsNegativeValues??!1,values:Array.from(b),modifiers:A}]})}},addComponents(l,p){this.addUtilities(l,p)},matchComponents(l,p){this.matchUtilities(l,p)},theme:Ze(t,()=>n.theme??{},l=>l),prefix(l){return l},config(l,p){let c=n;if(!l)return c;let d=Je(l);for(let f=0;f<d.length;++f){let m=d[f];if(c[m]===void 0)return p;c=c[m]}return c??p}};return a.addComponents=a.addComponents.bind(a),a.matchComponents=a.matchComponents.bind(a),a}function ee(t){let r=[];t=Array.isArray(t)?t:[t];let n=t.flatMap(e=>Object.entries(e));for(let[e,i]of n)if(typeof i!="object"){if(!e.startsWith("--")){if(i==="@slot"){r.push(M(e,[U("@slot")]));continue}e=e.replace(/([A-Z])/g,"-$1").toLowerCase()}r.push(s(e,String(i)))}else if(Array.isArray(i))for(let a of i)typeof a=="string"?r.push(s(e,a)):r.push(M(e,ee(a)));else i!==null&&r.push(M(e,ee(i)));return r}function jr(t,r){return(typeof t=="string"?[t]:t).flatMap(e=>{if(e.trim().endsWith("}")){let i=e.replace("}","{@slot}}"),a=me(i);return wt(a,r),a}else return M(e,r)})}function Ir(t,r,n){K(t,e=>{if(e.kind==="rule"){let i=Qe(e.selector);Ee(i,a=>{a.kind==="selector"&&a.value===`.${r}`&&(a.value=`.${ne(n)}`)}),e.selector=Re(i)}})}function zr(t,r,n){for(let e of _i(r))t.theme.addKeyframes(e)}function _i(t){let r=[];if("keyframes"in t.theme)for(let[n,e]of Object.entries(t.theme.keyframes))r.push(U("@keyframes",n,ee(e)));return r}var Xe={inherit:"inherit",current:"currentColor",transparent:"transparent",black:"#000",white:"#fff",slate:{50:"oklch(0.984 0.003 247.858)",100:"oklch(0.968 0.007 247.896)",200:"oklch(0.929 0.013 255.508)",300:"oklch(0.869 0.022 252.894)",400:"oklch(0.704 0.04 256.788)",500:"oklch(0.554 0.046 257.417)",600:"oklch(0.446 0.043 257.281)",700:"oklch(0.372 0.044 257.287)",800:"oklch(0.279 0.041 260.031)",900:"oklch(0.208 0.042 265.755)",950:"oklch(0.129 0.042 264.695)"},gray:{50:"oklch(0.985 0.002 247.839)",100:"oklch(0.967 0.003 264.542)",200:"oklch(0.928 0.006 264.531)",300:"oklch(0.872 0.01 258.338)",400:"oklch(0.707 0.022 261.325)",500:"oklch(0.551 0.027 264.364)",600:"oklch(0.446 0.03 256.802)",700:"oklch(0.373 0.034 259.733)",800:"oklch(0.278 0.033 256.848)",900:"oklch(0.21 0.034 264.665)",950:"oklch(0.13 0.028 261.692)"},zinc:{50:"oklch(0.985 0 0)",100:"oklch(0.967 0.001 286.375)",200:"oklch(0.92 0.004 286.32)",300:"oklch(0.871 0.006 286.286)",400:"oklch(0.705 0.015 286.067)",500:"oklch(0.552 0.016 285.938)",600:"oklch(0.442 0.017 285.786)",700:"oklch(0.37 0.013 285.805)",800:"oklch(0.274 0.006 286.033)",900:"oklch(0.21 0.006 285.885)",950:"oklch(0.141 0.005 285.823)"},neutral:{50:"oklch(0.985 0 0)",100:"oklch(0.97 0 0)",200:"oklch(0.922 0 0)",300:"oklch(0.87 0 0)",400:"oklch(0.708 0 0)",500:"oklch(0.556 0 0)",600:"oklch(0.439 0 0)",700:"oklch(0.371 0 0)",800:"oklch(0.269 0 0)",900:"oklch(0.205 0 0)",950:"oklch(0.145 0 0)"},stone:{50:"oklch(0.985 0.001 106.423)",100:"oklch(0.97 0.001 106.424)",200:"oklch(0.923 0.003 48.717)",300:"oklch(0.869 0.005 56.366)",400:"oklch(0.709 0.01 56.259)",500:"oklch(0.553 0.013 58.071)",600:"oklch(0.444 0.011 73.639)",700:"oklch(0.374 0.01 67.558)",800:"oklch(0.268 0.007 34.298)",900:"oklch(0.216 0.006 56.043)",950:"oklch(0.147 0.004 49.25)"},red:{50:"oklch(0.971 0.013 17.38)",100:"oklch(0.936 0.032 17.717)",200:"oklch(0.885 0.062 18.334)",300:"oklch(0.808 0.114 19.571)",400:"oklch(0.704 0.191 22.216)",500:"oklch(0.637 0.237 25.331)",600:"oklch(0.577 0.245 27.325)",700:"oklch(0.505 0.213 27.518)",800:"oklch(0.444 0.177 26.899)",900:"oklch(0.396 0.141 25.723)",950:"oklch(0.258 0.092 26.042)"},orange:{50:"oklch(0.98 0.016 73.684)",100:"oklch(0.954 0.038 75.164)",200:"oklch(0.901 0.076 70.697)",300:"oklch(0.837 0.128 66.29)",400:"oklch(0.75 0.183 55.934)",500:"oklch(0.705 0.213 47.604)",600:"oklch(0.646 0.222 41.116)",700:"oklch(0.553 0.195 38.402)",800:"oklch(0.47 0.157 37.304)",900:"oklch(0.408 0.123 38.172)",950:"oklch(0.266 0.079 36.259)"},amber:{50:"oklch(0.987 0.022 95.277)",100:"oklch(0.962 0.059 95.617)",200:"oklch(0.924 0.12 95.746)",300:"oklch(0.879 0.169 91.605)",400:"oklch(0.828 0.189 84.429)",500:"oklch(0.769 0.188 70.08)",600:"oklch(0.666 0.179 58.318)",700:"oklch(0.555 0.163 48.998)",800:"oklch(0.473 0.137 46.201)",900:"oklch(0.414 0.112 45.904)",950:"oklch(0.279 0.077 45.635)"},yellow:{50:"oklch(0.987 0.026 102.212)",100:"oklch(0.973 0.071 103.193)",200:"oklch(0.945 0.129 101.54)",300:"oklch(0.905 0.182 98.111)",400:"oklch(0.852 0.199 91.936)",500:"oklch(0.795 0.184 86.047)",600:"oklch(0.681 0.162 75.834)",700:"oklch(0.554 0.135 66.442)",800:"oklch(0.476 0.114 61.907)",900:"oklch(0.421 0.095 57.708)",950:"oklch(0.286 0.066 53.813)"},lime:{50:"oklch(0.986 0.031 120.757)",100:"oklch(0.967 0.067 122.328)",200:"oklch(0.938 0.127 124.321)",300:"oklch(0.897 0.196 126.665)",400:"oklch(0.841 0.238 128.85)",500:"oklch(0.768 0.233 130.85)",600:"oklch(0.648 0.2 131.684)",700:"oklch(0.532 0.157 131.589)",800:"oklch(0.453 0.124 130.933)",900:"oklch(0.405 0.101 131.063)",950:"oklch(0.274 0.072 132.109)"},green:{50:"oklch(0.982 0.018 155.826)",100:"oklch(0.962 0.044 156.743)",200:"oklch(0.925 0.084 155.995)",300:"oklch(0.871 0.15 154.449)",400:"oklch(0.792 0.209 151.711)",500:"oklch(0.723 0.219 149.579)",600:"oklch(0.627 0.194 149.214)",700:"oklch(0.527 0.154 150.069)",800:"oklch(0.448 0.119 151.328)",900:"oklch(0.393 0.095 152.535)",950:"oklch(0.266 0.065 152.934)"},emerald:{50:"oklch(0.979 0.021 166.113)",100:"oklch(0.95 0.052 163.051)",200:"oklch(0.905 0.093 164.15)",300:"oklch(0.845 0.143 164.978)",400:"oklch(0.765 0.177 163.223)",500:"oklch(0.696 0.17 162.48)",600:"oklch(0.596 0.145 163.225)",700:"oklch(0.508 0.118 165.612)",800:"oklch(0.432 0.095 166.913)",900:"oklch(0.378 0.077 168.94)",950:"oklch(0.262 0.051 172.552)"},teal:{50:"oklch(0.984 0.014 180.72)",100:"oklch(0.953 0.051 180.801)",200:"oklch(0.91 0.096 180.426)",300:"oklch(0.855 0.138 181.071)",400:"oklch(0.777 0.152 181.912)",500:"oklch(0.704 0.14 182.503)",600:"oklch(0.6 0.118 184.704)",700:"oklch(0.511 0.096 186.391)",800:"oklch(0.437 0.078 188.216)",900:"oklch(0.386 0.063 188.416)",950:"oklch(0.277 0.046 192.524)"},cyan:{50:"oklch(0.984 0.019 200.873)",100:"oklch(0.956 0.045 203.388)",200:"oklch(0.917 0.08 205.041)",300:"oklch(0.865 0.127 207.078)",400:"oklch(0.789 0.154 211.53)",500:"oklch(0.715 0.143 215.221)",600:"oklch(0.609 0.126 221.723)",700:"oklch(0.52 0.105 223.128)",800:"oklch(0.45 0.085 224.283)",900:"oklch(0.398 0.07 227.392)",950:"oklch(0.302 0.056 229.695)"},sky:{50:"oklch(0.977 0.013 236.62)",100:"oklch(0.951 0.026 236.824)",200:"oklch(0.901 0.058 230.902)",300:"oklch(0.828 0.111 230.318)",400:"oklch(0.746 0.16 232.661)",500:"oklch(0.685 0.169 237.323)",600:"oklch(0.588 0.158 241.966)",700:"oklch(0.5 0.134 242.749)",800:"oklch(0.443 0.11 240.79)",900:"oklch(0.391 0.09 240.876)",950:"oklch(0.293 0.066 243.157)"},blue:{50:"oklch(0.97 0.014 254.604)",100:"oklch(0.932 0.032 255.585)",200:"oklch(0.882 0.059 254.128)",300:"oklch(0.809 0.105 251.813)",400:"oklch(0.707 0.165 254.624)",500:"oklch(0.623 0.214 259.815)",600:"oklch(0.546 0.245 262.881)",700:"oklch(0.488 0.243 264.376)",800:"oklch(0.424 0.199 265.638)",900:"oklch(0.379 0.146 265.522)",950:"oklch(0.282 0.091 267.935)"},indigo:{50:"oklch(0.962 0.018 272.314)",100:"oklch(0.93 0.034 272.788)",200:"oklch(0.87 0.065 274.039)",300:"oklch(0.785 0.115 274.713)",400:"oklch(0.673 0.182 276.935)",500:"oklch(0.585 0.233 277.117)",600:"oklch(0.511 0.262 276.966)",700:"oklch(0.457 0.24 277.023)",800:"oklch(0.398 0.195 277.366)",900:"oklch(0.359 0.144 278.697)",950:"oklch(0.257 0.09 281.288)"},violet:{50:"oklch(0.969 0.016 293.756)",100:"oklch(0.943 0.029 294.588)",200:"oklch(0.894 0.057 293.283)",300:"oklch(0.811 0.111 293.571)",400:"oklch(0.702 0.183 293.541)",500:"oklch(0.606 0.25 292.717)",600:"oklch(0.541 0.281 293.009)",700:"oklch(0.491 0.27 292.581)",800:"oklch(0.432 0.232 292.759)",900:"oklch(0.38 0.189 293.745)",950:"oklch(0.283 0.141 291.089)"},purple:{50:"oklch(0.977 0.014 308.299)",100:"oklch(0.946 0.033 307.174)",200:"oklch(0.902 0.063 306.703)",300:"oklch(0.827 0.119 306.383)",400:"oklch(0.714 0.203 305.504)",500:"oklch(0.627 0.265 303.9)",600:"oklch(0.558 0.288 302.321)",700:"oklch(0.496 0.265 301.924)",800:"oklch(0.438 0.218 303.724)",900:"oklch(0.381 0.176 304.987)",950:"oklch(0.291 0.149 302.717)"},fuchsia:{50:"oklch(0.977 0.017 320.058)",100:"oklch(0.952 0.037 318.852)",200:"oklch(0.903 0.076 319.62)",300:"oklch(0.833 0.145 321.434)",400:"oklch(0.74 0.238 322.16)",500:"oklch(0.667 0.295 322.15)",600:"oklch(0.591 0.293 322.896)",700:"oklch(0.518 0.253 323.949)",800:"oklch(0.452 0.211 324.591)",900:"oklch(0.401 0.17 325.612)",950:"oklch(0.293 0.136 325.661)"},pink:{50:"oklch(0.971 0.014 343.198)",100:"oklch(0.948 0.028 342.258)",200:"oklch(0.899 0.061 343.231)",300:"oklch(0.823 0.12 346.018)",400:"oklch(0.718 0.202 349.761)",500:"oklch(0.656 0.241 354.308)",600:"oklch(0.592 0.249 0.584)",700:"oklch(0.525 0.223 3.958)",800:"oklch(0.459 0.187 3.815)",900:"oklch(0.408 0.153 2.432)",950:"oklch(0.284 0.109 3.907)"},rose:{50:"oklch(0.969 0.015 12.422)",100:"oklch(0.941 0.03 12.58)",200:"oklch(0.892 0.058 10.001)",300:"oklch(0.81 0.117 11.638)",400:"oklch(0.712 0.194 13.428)",500:"oklch(0.645 0.246 16.439)",600:"oklch(0.586 0.253 17.585)",700:"oklch(0.514 0.222 16.935)",800:"oklch(0.455 0.188 13.697)",900:"oklch(0.41 0.159 10.272)",950:"oklch(0.271 0.105 12.094)"}};function pe(t){return{__BARE_VALUE__:t}}var Q=pe(t=>{if(T(t.value))return t.value}),q=pe(t=>{if(T(t.value))return`${t.value}%`}),le=pe(t=>{if(T(t.value))return`${t.value}px`}),Fr=pe(t=>{if(T(t.value))return`${t.value}ms`}),et=pe(t=>{if(T(t.value))return`${t.value}deg`}),Di=pe(t=>{if(t.fraction===null)return;let[r,n]=O(t.fraction,"/");if(!(!T(r)||!T(n)))return t.fraction}),Lr=pe(t=>{if(T(Number(t.value)))return`repeat(${t.value}, minmax(0, 1fr))`}),Mr={accentColor:({theme:t})=>t("colors"),animation:{none:"none",spin:"spin 1s linear infinite",ping:"ping 1s cubic-bezier(0, 0, 0.2, 1) infinite",pulse:"pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite",bounce:"bounce 1s infinite"},aria:{busy:'busy="true"',checked:'checked="true"',disabled:'disabled="true"',expanded:'expanded="true"',hidden:'hidden="true"',pressed:'pressed="true"',readonly:'readonly="true"',required:'required="true"',selected:'selected="true"'},aspectRatio:{auto:"auto",square:"1 / 1",video:"16 / 9",...Di},backdropBlur:({theme:t})=>t("blur"),backdropBrightness:({theme:t})=>({...t("brightness"),...q}),backdropContrast:({theme:t})=>({...t("contrast"),...q}),backdropGrayscale:({theme:t})=>({...t("grayscale"),...q}),backdropHueRotate:({theme:t})=>({...t("hueRotate"),...et}),backdropInvert:({theme:t})=>({...t("invert"),...q}),backdropOpacity:({theme:t})=>({...t("opacity"),...q}),backdropSaturate:({theme:t})=>({...t("saturate"),...q}),backdropSepia:({theme:t})=>({...t("sepia"),...q}),backgroundColor:({theme:t})=>t("colors"),backgroundImage:{none:"none","gradient-to-t":"linear-gradient(to top, var(--tw-gradient-stops))","gradient-to-tr":"linear-gradient(to top right, var(--tw-gradient-stops))","gradient-to-r":"linear-gradient(to right, var(--tw-gradient-stops))","gradient-to-br":"linear-gradient(to bottom right, var(--tw-gradient-stops))","gradient-to-b":"linear-gradient(to bottom, var(--tw-gradient-stops))","gradient-to-bl":"linear-gradient(to bottom left, var(--tw-gradient-stops))","gradient-to-l":"linear-gradient(to left, var(--tw-gradient-stops))","gradient-to-tl":"linear-gradient(to top left, var(--tw-gradient-stops))"},backgroundOpacity:({theme:t})=>t("opacity"),backgroundPosition:{bottom:"bottom",center:"center",left:"left","left-bottom":"left bottom","left-top":"left top",right:"right","right-bottom":"right bottom","right-top":"right top",top:"top"},backgroundSize:{auto:"auto",cover:"cover",contain:"contain"},blur:{0:"0",none:"",sm:"4px",DEFAULT:"8px",md:"12px",lg:"16px",xl:"24px","2xl":"40px","3xl":"64px"},borderColor:({theme:t})=>({DEFAULT:"currentColor",...t("colors")}),borderOpacity:({theme:t})=>t("opacity"),borderRadius:{none:"0px",sm:"0.125rem",DEFAULT:"0.25rem",md:"0.375rem",lg:"0.5rem",xl:"0.75rem","2xl":"1rem","3xl":"1.5rem",full:"9999px"},borderSpacing:({theme:t})=>t("spacing"),borderWidth:{DEFAULT:"1px",0:"0px",2:"2px",4:"4px",8:"8px",...le},boxShadow:{sm:"0 1px 2px 0 rgb(0 0 0 / 0.05)",DEFAULT:"0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)",md:"0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)",lg:"0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)",xl:"0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)","2xl":"0 25px 50px -12px rgb(0 0 0 / 0.25)",inner:"inset 0 2px 4px 0 rgb(0 0 0 / 0.05)",none:"none"},boxShadowColor:({theme:t})=>t("colors"),brightness:{0:"0",50:".5",75:".75",90:".9",95:".95",100:"1",105:"1.05",110:"1.1",125:"1.25",150:"1.5",200:"2",...q},caretColor:({theme:t})=>t("colors"),colors:()=>({...Xe}),columns:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12","3xs":"16rem","2xs":"18rem",xs:"20rem",sm:"24rem",md:"28rem",lg:"32rem",xl:"36rem","2xl":"42rem","3xl":"48rem","4xl":"56rem","5xl":"64rem","6xl":"72rem","7xl":"80rem",...Q},container:{},content:{none:"none"},contrast:{0:"0",50:".5",75:".75",100:"1",125:"1.25",150:"1.5",200:"2",...q},cursor:{auto:"auto",default:"default",pointer:"pointer",wait:"wait",text:"text",move:"move",help:"help","not-allowed":"not-allowed",none:"none","context-menu":"context-menu",progress:"progress",cell:"cell",crosshair:"crosshair","vertical-text":"vertical-text",alias:"alias",copy:"copy","no-drop":"no-drop",grab:"grab",grabbing:"grabbing","all-scroll":"all-scroll","col-resize":"col-resize","row-resize":"row-resize","n-resize":"n-resize","e-resize":"e-resize","s-resize":"s-resize","w-resize":"w-resize","ne-resize":"ne-resize","nw-resize":"nw-resize","se-resize":"se-resize","sw-resize":"sw-resize","ew-resize":"ew-resize","ns-resize":"ns-resize","nesw-resize":"nesw-resize","nwse-resize":"nwse-resize","zoom-in":"zoom-in","zoom-out":"zoom-out"},divideColor:({theme:t})=>t("borderColor"),divideOpacity:({theme:t})=>t("borderOpacity"),divideWidth:({theme:t})=>({...t("borderWidth"),...le}),dropShadow:{sm:"0 1px 1px rgb(0 0 0 / 0.05)",DEFAULT:["0 1px 2px rgb(0 0 0 / 0.1)","0 1px 1px rgb(0 0 0 / 0.06)"],md:["0 4px 3px rgb(0 0 0 / 0.07)","0 2px 2px rgb(0 0 0 / 0.06)"],lg:["0 10px 8px rgb(0 0 0 / 0.04)","0 4px 3px rgb(0 0 0 / 0.1)"],xl:["0 20px 13px rgb(0 0 0 / 0.03)","0 8px 5px rgb(0 0 0 / 0.08)"],"2xl":"0 25px 25px rgb(0 0 0 / 0.15)",none:"0 0 #0000"},fill:({theme:t})=>t("colors"),flex:{1:"1 1 0%",auto:"1 1 auto",initial:"0 1 auto",none:"none"},flexBasis:({theme:t})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%","1/12":"8.333333%","2/12":"16.666667%","3/12":"25%","4/12":"33.333333%","5/12":"41.666667%","6/12":"50%","7/12":"58.333333%","8/12":"66.666667%","9/12":"75%","10/12":"83.333333%","11/12":"91.666667%",full:"100%",...t("spacing")}),flexGrow:{0:"0",DEFAULT:"1",...Q},flexShrink:{0:"0",DEFAULT:"1",...Q},fontFamily:{sans:["ui-sans-serif","system-ui","sans-serif",'"Apple Color Emoji"','"Segoe UI Emoji"','"Segoe UI Symbol"','"Noto Color Emoji"'],serif:["ui-serif","Georgia","Cambria",'"Times New Roman"',"Times","serif"],mono:["ui-monospace","SFMono-Regular","Menlo","Monaco","Consolas",'"Liberation Mono"','"Courier New"',"monospace"]},fontSize:{xs:["0.75rem",{lineHeight:"1rem"}],sm:["0.875rem",{lineHeight:"1.25rem"}],base:["1rem",{lineHeight:"1.5rem"}],lg:["1.125rem",{lineHeight:"1.75rem"}],xl:["1.25rem",{lineHeight:"1.75rem"}],"2xl":["1.5rem",{lineHeight:"2rem"}],"3xl":["1.875rem",{lineHeight:"2.25rem"}],"4xl":["2.25rem",{lineHeight:"2.5rem"}],"5xl":["3rem",{lineHeight:"1"}],"6xl":["3.75rem",{lineHeight:"1"}],"7xl":["4.5rem",{lineHeight:"1"}],"8xl":["6rem",{lineHeight:"1"}],"9xl":["8rem",{lineHeight:"1"}]},fontWeight:{thin:"100",extralight:"200",light:"300",normal:"400",medium:"500",semibold:"600",bold:"700",extrabold:"800",black:"900"},gap:({theme:t})=>t("spacing"),gradientColorStops:({theme:t})=>t("colors"),gradientColorStopPositions:{"0%":"0%","5%":"5%","10%":"10%","15%":"15%","20%":"20%","25%":"25%","30%":"30%","35%":"35%","40%":"40%","45%":"45%","50%":"50%","55%":"55%","60%":"60%","65%":"65%","70%":"70%","75%":"75%","80%":"80%","85%":"85%","90%":"90%","95%":"95%","100%":"100%",...q},grayscale:{0:"0",DEFAULT:"100%",...q},gridAutoColumns:{auto:"auto",min:"min-content",max:"max-content",fr:"minmax(0, 1fr)"},gridAutoRows:{auto:"auto",min:"min-content",max:"max-content",fr:"minmax(0, 1fr)"},gridColumn:{auto:"auto","span-1":"span 1 / span 1","span-2":"span 2 / span 2","span-3":"span 3 / span 3","span-4":"span 4 / span 4","span-5":"span 5 / span 5","span-6":"span 6 / span 6","span-7":"span 7 / span 7","span-8":"span 8 / span 8","span-9":"span 9 / span 9","span-10":"span 10 / span 10","span-11":"span 11 / span 11","span-12":"span 12 / span 12","span-full":"1 / -1"},gridColumnEnd:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...Q},gridColumnStart:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...Q},gridRow:{auto:"auto","span-1":"span 1 / span 1","span-2":"span 2 / span 2","span-3":"span 3 / span 3","span-4":"span 4 / span 4","span-5":"span 5 / span 5","span-6":"span 6 / span 6","span-7":"span 7 / span 7","span-8":"span 8 / span 8","span-9":"span 9 / span 9","span-10":"span 10 / span 10","span-11":"span 11 / span 11","span-12":"span 12 / span 12","span-full":"1 / -1"},gridRowEnd:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...Q},gridRowStart:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...Q},gridTemplateColumns:{none:"none",subgrid:"subgrid",1:"repeat(1, minmax(0, 1fr))",2:"repeat(2, minmax(0, 1fr))",3:"repeat(3, minmax(0, 1fr))",4:"repeat(4, minmax(0, 1fr))",5:"repeat(5, minmax(0, 1fr))",6:"repeat(6, minmax(0, 1fr))",7:"repeat(7, minmax(0, 1fr))",8:"repeat(8, minmax(0, 1fr))",9:"repeat(9, minmax(0, 1fr))",10:"repeat(10, minmax(0, 1fr))",11:"repeat(11, minmax(0, 1fr))",12:"repeat(12, minmax(0, 1fr))",...Lr},gridTemplateRows:{none:"none",subgrid:"subgrid",1:"repeat(1, minmax(0, 1fr))",2:"repeat(2, minmax(0, 1fr))",3:"repeat(3, minmax(0, 1fr))",4:"repeat(4, minmax(0, 1fr))",5:"repeat(5, minmax(0, 1fr))",6:"repeat(6, minmax(0, 1fr))",7:"repeat(7, minmax(0, 1fr))",8:"repeat(8, minmax(0, 1fr))",9:"repeat(9, minmax(0, 1fr))",10:"repeat(10, minmax(0, 1fr))",11:"repeat(11, minmax(0, 1fr))",12:"repeat(12, minmax(0, 1fr))",...Lr},height:({theme:t})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%",full:"100%",screen:"100vh",svh:"100svh",lvh:"100lvh",dvh:"100dvh",min:"min-content",max:"max-content",fit:"fit-content",...t("spacing")}),hueRotate:{0:"0deg",15:"15deg",30:"30deg",60:"60deg",90:"90deg",180:"180deg",...et},inset:({theme:t})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%",full:"100%",...t("spacing")}),invert:{0:"0",DEFAULT:"100%",...q},keyframes:{spin:{to:{transform:"rotate(360deg)"}},ping:{"75%, 100%":{transform:"scale(2)",opacity:"0"}},pulse:{"50%":{opacity:".5"}},bounce:{"0%, 100%":{transform:"translateY(-25%)",animationTimingFunction:"cubic-bezier(0.8,0,1,1)"},"50%":{transform:"none",animationTimingFunction:"cubic-bezier(0,0,0.2,1)"}}},letterSpacing:{tighter:"-0.05em",tight:"-0.025em",normal:"0em",wide:"0.025em",wider:"0.05em",widest:"0.1em"},lineHeight:{none:"1",tight:"1.25",snug:"1.375",normal:"1.5",relaxed:"1.625",loose:"2",3:".75rem",4:"1rem",5:"1.25rem",6:"1.5rem",7:"1.75rem",8:"2rem",9:"2.25rem",10:"2.5rem"},listStyleType:{none:"none",disc:"disc",decimal:"decimal"},listStyleImage:{none:"none"},margin:({theme:t})=>({auto:"auto",...t("spacing")}),lineClamp:{1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",...Q},maxHeight:({theme:t})=>({none:"none",full:"100%",screen:"100vh",svh:"100svh",lvh:"100lvh",dvh:"100dvh",min:"min-content",max:"max-content",fit:"fit-content",...t("spacing")}),maxWidth:({theme:t})=>({none:"none",xs:"20rem",sm:"24rem",md:"28rem",lg:"32rem",xl:"36rem","2xl":"42rem","3xl":"48rem","4xl":"56rem","5xl":"64rem","6xl":"72rem","7xl":"80rem",full:"100%",min:"min-content",max:"max-content",fit:"fit-content",prose:"65ch",...t("spacing")}),minHeight:({theme:t})=>({full:"100%",screen:"100vh",svh:"100svh",lvh:"100lvh",dvh:"100dvh",min:"min-content",max:"max-content",fit:"fit-content",...t("spacing")}),minWidth:({theme:t})=>({full:"100%",min:"min-content",max:"max-content",fit:"fit-content",...t("spacing")}),objectPosition:{bottom:"bottom",center:"center",left:"left","left-bottom":"left bottom","left-top":"left top",right:"right","right-bottom":"right bottom","right-top":"right top",top:"top"},opacity:{0:"0",5:"0.05",10:"0.1",15:"0.15",20:"0.2",25:"0.25",30:"0.3",35:"0.35",40:"0.4",45:"0.45",50:"0.5",55:"0.55",60:"0.6",65:"0.65",70:"0.7",75:"0.75",80:"0.8",85:"0.85",90:"0.9",95:"0.95",100:"1",...q},order:{first:"-9999",last:"9999",none:"0",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",...Q},outlineColor:({theme:t})=>t("colors"),outlineOffset:{0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...le},outlineWidth:{0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...le},padding:({theme:t})=>t("spacing"),placeholderColor:({theme:t})=>t("colors"),placeholderOpacity:({theme:t})=>t("opacity"),ringColor:({theme:t})=>({DEFAULT:"currentColor",...t("colors")}),ringOffsetColor:({theme:t})=>t("colors"),ringOffsetWidth:{0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...le},ringOpacity:({theme:t})=>({DEFAULT:"0.5",...t("opacity")}),ringWidth:{DEFAULT:"3px",0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...le},rotate:{0:"0deg",1:"1deg",2:"2deg",3:"3deg",6:"6deg",12:"12deg",45:"45deg",90:"90deg",180:"180deg",...et},saturate:{0:"0",50:".5",100:"1",150:"1.5",200:"2",...q},scale:{0:"0",50:".5",75:".75",90:".9",95:".95",100:"1",105:"1.05",110:"1.1",125:"1.25",150:"1.5",...q},screens:{sm:"40rem",md:"48rem",lg:"64rem",xl:"80rem","2xl":"96rem"},scrollMargin:({theme:t})=>t("spacing"),scrollPadding:({theme:t})=>t("spacing"),sepia:{0:"0",DEFAULT:"100%",...q},skew:{0:"0deg",1:"1deg",2:"2deg",3:"3deg",6:"6deg",12:"12deg",...et},space:({theme:t})=>t("spacing"),spacing:{px:"1px",0:"0px",.5:"0.125rem",1:"0.25rem",1.5:"0.375rem",2:"0.5rem",2.5:"0.625rem",3:"0.75rem",3.5:"0.875rem",4:"1rem",5:"1.25rem",6:"1.5rem",7:"1.75rem",8:"2rem",9:"2.25rem",10:"2.5rem",11:"2.75rem",12:"3rem",14:"3.5rem",16:"4rem",20:"5rem",24:"6rem",28:"7rem",32:"8rem",36:"9rem",40:"10rem",44:"11rem",48:"12rem",52:"13rem",56:"14rem",60:"15rem",64:"16rem",72:"18rem",80:"20rem",96:"24rem"},stroke:({theme:t})=>({none:"none",...t("colors")}),strokeWidth:{0:"0",1:"1",2:"2",...Q},supports:{},data:{},textColor:({theme:t})=>t("colors"),textDecorationColor:({theme:t})=>t("colors"),textDecorationThickness:{auto:"auto","from-font":"from-font",0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...le},textIndent:({theme:t})=>t("spacing"),textOpacity:({theme:t})=>t("opacity"),textUnderlineOffset:{auto:"auto",0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...le},transformOrigin:{center:"center",top:"top","top-right":"top right",right:"right","bottom-right":"bottom right",bottom:"bottom","bottom-left":"bottom left",left:"left","top-left":"top left"},transitionDelay:{0:"0s",75:"75ms",100:"100ms",150:"150ms",200:"200ms",300:"300ms",500:"500ms",700:"700ms",1e3:"1000ms",...Fr},transitionDuration:{DEFAULT:"150ms",0:"0s",75:"75ms",100:"100ms",150:"150ms",200:"200ms",300:"300ms",500:"500ms",700:"700ms",1e3:"1000ms",...Fr},transitionProperty:{none:"none",all:"all",DEFAULT:"color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter",colors:"color, background-color, border-color, outline-color, text-decoration-color, fill, stroke",opacity:"opacity",shadow:"box-shadow",transform:"transform"},transitionTimingFunction:{DEFAULT:"cubic-bezier(0.4, 0, 0.2, 1)",linear:"linear",in:"cubic-bezier(0.4, 0, 1, 1)",out:"cubic-bezier(0, 0, 0.2, 1)","in-out":"cubic-bezier(0.4, 0, 0.2, 1)"},translate:({theme:t})=>({"1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%",full:"100%",...t("spacing")}),size:({theme:t})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%","1/12":"8.333333%","2/12":"16.666667%","3/12":"25%","4/12":"33.333333%","5/12":"41.666667%","6/12":"50%","7/12":"58.333333%","8/12":"66.666667%","9/12":"75%","10/12":"83.333333%","11/12":"91.666667%",full:"100%",min:"min-content",max:"max-content",fit:"fit-content",...t("spacing")}),width:({theme:t})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%","1/12":"8.333333%","2/12":"16.666667%","3/12":"25%","4/12":"33.333333%","5/12":"41.666667%","6/12":"50%","7/12":"58.333333%","8/12":"66.666667%","9/12":"75%","10/12":"83.333333%","11/12":"91.666667%",full:"100%",screen:"100vw",svw:"100svw",lvw:"100lvw",dvw:"100dvw",min:"min-content",max:"max-content",fit:"fit-content",...t("spacing")}),willChange:{auto:"auto",scroll:"scroll-position",contents:"contents",transform:"transform"},zIndex:{auto:"auto",0:"0",10:"10",20:"20",30:"30",40:"40",50:"50",...Q}};function Wr(t){return{theme:{...Mr,colors:({theme:r})=>r("color",{}),extend:{fontSize:({theme:r})=>({...r("text",{})}),boxShadow:({theme:r})=>({...r("shadow",{})}),animation:({theme:r})=>({...r("animate",{})}),aspectRatio:({theme:r})=>({...r("aspect",{})}),borderRadius:({theme:r})=>({...r("radius",{})}),screens:({theme:r})=>({...r("breakpoint",{})}),letterSpacing:({theme:r})=>({...r("tracking",{})}),lineHeight:({theme:r})=>({...r("leading",{})}),transitionDuration:{DEFAULT:t.get(["--default-transition-duration"])??null},transitionTimingFunction:{DEFAULT:t.get(["--default-transition-timing-function"])??null},maxWidth:({theme:r})=>({...r("container",{})})}}}}var ji={blocklist:[],future:{},prefix:"",important:!1,darkMode:null,theme:{},plugins:[],content:{files:[]}};function Nt(t,r){let n={design:t,configs:[],plugins:[],content:{files:[]},theme:{},extend:{},result:structuredClone(ji)};for(let i of r)St(n,i);for(let i of n.configs)"darkMode"in i&&i.darkMode!==void 0&&(n.result.darkMode=i.darkMode??null),"prefix"in i&&i.prefix!==void 0&&(n.result.prefix=i.prefix??""),"blocklist"in i&&i.blocklist!==void 0&&(n.result.blocklist=i.blocklist??[]),"important"in i&&i.important!==void 0&&(n.result.important=i.important??!1);let e=zi(n);return{resolvedConfig:{...n.result,content:n.content,theme:n.theme,plugins:n.plugins},replacedThemeKeys:e}}function Ii(t,r){if(Array.isArray(t)&&ke(t[0]))return t.concat(r);if(Array.isArray(r)&&ke(r[0])&&ke(t))return[t,...r];if(Array.isArray(r))return r}function St(t,{config:r,base:n,path:e,reference:i}){let a=[];for(let c of r.plugins??[])"__isOptionsFunction"in c?a.push({...c(),reference:i}):"handler"in c?a.push({...c,reference:i}):a.push({handler:c,reference:i});if(Array.isArray(r.presets)&&r.presets.length===0)throw new Error("Error in the config file/plugin/preset. An empty preset (`preset: []`) is not currently supported.");for(let c of r.presets??[])St(t,{path:e,base:n,config:c,reference:i});for(let c of a)t.plugins.push(c),c.config&&St(t,{path:e,base:n,config:c.config,reference:!!c.reference});let l=r.content??[],p=Array.isArray(l)?l:l.files;for(let c of p)t.content.files.push(typeof c=="object"?c:{base:n,pattern:c});t.configs.push(r)}function zi(t){let r=new Set,n=Ze(t.design,()=>t.theme,i),e=Object.assign(n,{theme:n,colors:Xe});function i(a){return typeof a=="function"?a(e)??null:a??null}for(let a of t.configs){let l=a.theme??{},p=l.extend??{};for(let c in l)c!=="extend"&&r.add(c);Object.assign(t.theme,l);for(let c in p)t.extend[c]??=[],t.extend[c].push(p[c])}delete t.theme.extend;for(let a in t.extend){let l=[t.theme[a],...t.extend[a]];t.theme[a]=()=>{let p=l.map(i);return Te({},p,Ii)}}for(let a in t.theme)t.theme[a]=i(t.theme[a]);if(t.theme.screens&&typeof t.theme.screens=="object")for(let a of Object.keys(t.theme.screens)){let l=t.theme.screens[a];l&&typeof l=="object"&&("raw"in l||"max"in l||"min"in l&&(t.theme.screens[a]=l.min))}return r}function Br(t,r){let n=t.theme.container||{};if(typeof n!="object"||n===null)return;let e=Fi(n,r);e.length!==0&&r.utilities.static("container",()=>structuredClone(e))}function Fi({center:t,padding:r,screens:n},e){let i=[],a=null;if(t&&i.push(s("margin-inline","auto")),(typeof r=="string"||typeof r=="object"&&r!==null&&"DEFAULT"in r)&&i.push(s("padding-inline",typeof r=="string"?r:r.DEFAULT)),typeof n=="object"&&n!==null){a=new Map;let l=Array.from(e.theme.namespace("--breakpoint").entries());if(l.sort((p,c)=>ce(p[1],c[1],"asc")),l.length>0){let[p]=l[0];i.push(U("@media",`(width >= --theme(--breakpoint-${p}))`,[s("max-width","none")]))}for(let[p,c]of Object.entries(n)){if(typeof c=="object")if("min"in c)c=c.min;else continue;a.set(p,U("@media",`(width >= ${c})`,[s("max-width",c)]))}}if(typeof r=="object"&&r!==null){let l=Object.entries(r).filter(([p])=>p!=="DEFAULT").map(([p,c])=>[p,e.theme.resolveValue(p,["--breakpoint"]),c]).filter(Boolean);l.sort((p,c)=>ce(p[1],c[1],"asc"));for(let[p,,c]of l)if(a&&a.has(p))a.get(p).nodes.push(s("padding-inline",c));else{if(a)continue;i.push(U("@media",`(width >= theme(--breakpoint-${p}))`,[s("padding-inline",c)]))}}if(a)for(let[,l]of a)i.push(l);return i}function qr({addVariant:t,config:r}){let n=r("darkMode",null),[e,i=".dark"]=Array.isArray(n)?n:[n];if(e==="variant"){let a;if(Array.isArray(i)||typeof i=="function"?a=i:typeof i=="string"&&(a=[i]),Array.isArray(a))for(let l of a)l===".dark"?(e=!1,console.warn('When using `variant` for `darkMode`, you must provide a selector.\nExample: `darkMode: ["variant", ".your-selector &"]`')):l.includes("&")||(e=!1,console.warn('When using `variant` for `darkMode`, your selector must contain `&`.\nExample `darkMode: ["variant", ".your-selector &"]`'));i=a}e===null||(e==="selector"?t("dark",`&:where(${i}, ${i} *)`):e==="media"?t("dark","@media (prefers-color-scheme: dark)"):e==="variant"?t("dark",i):e==="class"&&t("dark",`&:is(${i} *)`))}function Hr(t){for(let[r,n]of[["t","top"],["tr","top right"],["r","right"],["br","bottom right"],["b","bottom"],["bl","bottom left"],["l","left"],["tl","top left"]])t.utilities.static(`bg-gradient-to-${r}`,()=>[s("--tw-gradient-position",`to ${n} in oklab`),s("background-image","linear-gradient(var(--tw-gradient-stops))")]);t.utilities.functional("max-w-screen",r=>{if(!r.value||r.value.kind==="arbitrary")return;let n=t.theme.resolve(r.value.value,["--breakpoint"]);if(n)return[s("max-width",n)]}),t.utilities.static("overflow-ellipsis",()=>[s("text-overflow","ellipsis")]),t.utilities.static("decoration-slice",()=>[s("-webkit-box-decoration-break","slice"),s("box-decoration-break","slice")]),t.utilities.static("decoration-clone",()=>[s("-webkit-box-decoration-break","clone"),s("box-decoration-break","clone")]),t.utilities.functional("flex-shrink",r=>{if(!r.modifier){if(!r.value)return[s("flex-shrink","1")];if(r.value.kind==="arbitrary")return[s("flex-shrink",r.value.value)];if(T(r.value.value))return[s("flex-shrink",r.value.value)]}}),t.utilities.functional("flex-grow",r=>{if(!r.modifier){if(!r.value)return[s("flex-grow","1")];if(r.value.kind==="arbitrary")return[s("flex-grow",r.value.value)];if(T(r.value.value))return[s("flex-grow",r.value.value)]}})}function Gr(t,r){let n=t.theme.screens||{},e=r.variants.get("min")?.order??0,i=[];for(let[l,p]of Object.entries(n)){let g=function(y){r.variants.static(l,b=>{b.nodes=[U("@media",m,b.nodes)]},{order:y})};var a=g;let c=r.variants.get(l),d=r.theme.resolveValue(l,["--breakpoint"]);if(c&&d&&!r.theme.hasDefault(`--breakpoint-${l}`))continue;let f=!0;typeof p=="string"&&(f=!1);let m=Li(p);f?i.push(g):g(e)}if(i.length!==0){for(let[,l]of r.variants.variants)l.order>e&&(l.order+=i.length);r.variants.compareFns=new Map(Array.from(r.variants.compareFns).map(([l,p])=>(l>e&&(l+=i.length),[l,p])));for(let[l,p]of i.entries())p(e+l+1)}}function Li(t){return(Array.isArray(t)?t:[t]).map(n=>typeof n=="string"?{min:n}:n&&typeof n=="object"?n:null).map(n=>{if(n===null)return null;if("raw"in n)return n.raw;let e="";return n.max!==void 0&&(e+=`${n.max} >= `),e+="width",n.min!==void 0&&(e+=` >= ${n.min}`),`(${e})`}).filter(Boolean).join(", ")}function Yr(t,r){let n=t.theme.aria||{},e=t.theme.supports||{},i=t.theme.data||{};if(Object.keys(n).length>0){let a=r.variants.get("aria"),l=a?.applyFn,p=a?.compounds;r.variants.functional("aria",(c,d)=>{let f=d.value;return f&&f.kind==="named"&&f.value in n?l?.(c,{...d,value:{kind:"arbitrary",value:n[f.value]}}):l?.(c,d)},{compounds:p})}if(Object.keys(e).length>0){let a=r.variants.get("supports"),l=a?.applyFn,p=a?.compounds;r.variants.functional("supports",(c,d)=>{let f=d.value;return f&&f.kind==="named"&&f.value in e?l?.(c,{...d,value:{kind:"arbitrary",value:e[f.value]}}):l?.(c,d)},{compounds:p})}if(Object.keys(i).length>0){let a=r.variants.get("data"),l=a?.applyFn,p=a?.compounds;r.variants.functional("data",(c,d)=>{let f=d.value;return f&&f.kind==="named"&&f.value in i?l?.(c,{...d,value:{kind:"arbitrary",value:i[f.value]}}):l?.(c,d)},{compounds:p})}}var Mi=/^[a-z]+$/;async function Zr({designSystem:t,base:r,ast:n,loadModule:e,globs:i}){let a=0,l=[],p=[];K(n,(m,{parent:g,replaceWith:y,context:b})=>{if(m.kind==="at-rule"){if(m.name==="@plugin"){if(g!==null)throw new Error("`@plugin` cannot be nested.");let k=m.params.slice(1,-1);if(k.length===0)throw new Error("`@plugin` must have a path.");let A={};for(let x of m.nodes??[]){if(x.kind!=="declaration")throw new Error(`Unexpected \`@plugin\` option:

${J([x])}

\`@plugin\` options must be a flat list of declarations.`);if(x.value===void 0)continue;let V=x.value,P=O(V,",").map(R=>{if(R=R.trim(),R==="null")return null;if(R==="true")return!0;if(R==="false")return!1;if(Number.isNaN(Number(R))){if(R[0]==='"'&&R[R.length-1]==='"'||R[0]==="'"&&R[R.length-1]==="'")return R.slice(1,-1);if(R[0]==="{"&&R[R.length-1]==="}")throw new Error(`Unexpected \`@plugin\` option: Value of declaration \`${J([x]).trim()}\` is not supported.

Using an object as a plugin option is currently only supported in JavaScript configuration files.`)}else return Number(R);return R});A[x.property]=P.length===1?P[0]:P}l.push([{id:k,base:b.base,reference:!!b.reference},Object.keys(A).length>0?A:null]),y([]),a|=4;return}if(m.name==="@config"){if(m.nodes.length>0)throw new Error("`@config` cannot have a body.");if(g!==null)throw new Error("`@config` cannot be nested.");p.push({id:m.params.slice(1,-1),base:b.base,reference:!!b.reference}),y([]),a|=4;return}}}),Hr(t);let c=t.resolveThemeValue;if(t.resolveThemeValue=function(g,y){return g.startsWith("--")?c(g,y):(a|=Jr({designSystem:t,base:r,ast:n,globs:i,configs:[],pluginDetails:[]}),t.resolveThemeValue(g,y))},!l.length&&!p.length)return 0;let[d,f]=await Promise.all([Promise.all(p.map(async({id:m,base:g,reference:y})=>{let b=await e(m,g,"config");return{path:m,base:b.base,config:b.module,reference:y}})),Promise.all(l.map(async([{id:m,base:g,reference:y},b])=>{let k=await e(m,g,"plugin");return{path:m,base:k.base,plugin:k.module,options:b,reference:y}}))]);return a|=Jr({designSystem:t,base:r,ast:n,globs:i,configs:d,pluginDetails:f}),a}function Jr({designSystem:t,base:r,ast:n,globs:e,configs:i,pluginDetails:a}){let l=0,c=[...a.map(k=>{if(!k.options)return{config:{plugins:[k.plugin]},base:k.base,reference:k.reference};if("__isOptionsFunction"in k.plugin)return{config:{plugins:[k.plugin(k.options)]},base:k.base,reference:k.reference};throw new Error(`The plugin "${k.path}" does not accept options`)}),...i],{resolvedConfig:d}=Nt(t,[{config:Wr(t.theme),base:r,reference:!0},...c,{config:{plugins:[qr]},base:r,reference:!0}]),{resolvedConfig:f,replacedThemeKeys:m}=Nt(t,c);t.resolveThemeValue=function(A,x){let V=y.theme(A,x);if(Array.isArray(V)&&V.length===2)return V[0];if(Array.isArray(V))return V.join(", ");if(typeof V=="string")return V};let g={designSystem:t,ast:n,resolvedConfig:d,featuresRef:{set current(k){l|=k}}},y=Ct({...g,referenceMode:!1}),b;for(let{handler:k,reference:A}of d.plugins)A?(b||=Ct({...g,referenceMode:!0}),k(b)):k(y);if(xr(t,f,m),zr(t,f,m),Yr(f,t),Gr(f,t),Br(f,t),!t.theme.prefix&&d.prefix){if(d.prefix.endsWith("-")&&(d.prefix=d.prefix.slice(0,-1),console.warn(`The prefix "${d.prefix}" is invalid. Prefixes must be lowercase ASCII letters (a-z) only and is written as a variant before all utilities. We have fixed up the prefix for you. Remove the trailing \`-\` to silence this warning.`)),!Mi.test(d.prefix))throw new Error(`The prefix "${d.prefix}" is invalid. Prefixes must be lowercase ASCII letters (a-z) only.`);t.theme.prefix=d.prefix}if(!t.important&&d.important===!0&&(t.important=!0),typeof d.important=="string"){let k=d.important;K(n,(A,{replaceWith:x,parent:V})=>{if(A.kind==="at-rule"&&!(A.name!=="@tailwind"||A.params!=="utilities"))return V?.kind==="rule"&&V.selector===k?2:(x(j(k,[A])),2)})}for(let k of d.blocklist)t.invalidCandidates.add(k);for(let k of d.content.files){if("raw"in k)throw new Error(`Error in the config file/plugin/preset. The \`content\` key contains a \`raw\` entry:

${JSON.stringify(k,null,2)}

This feature is not currently supported.`);e.push(k)}return l}var Qr=/^(-?\d+)\.\.(-?\d+)(?:\.\.(-?\d+))?$/;function tt(t){let r=t.indexOf("{");if(r===-1)return[t];let n=[],e=t.slice(0,r),i=t.slice(r),a=0,l=i.lastIndexOf("}");for(let m=0;m<i.length;m++){let g=i[m];if(g==="{")a++;else if(g==="}"&&(a--,a===0)){l=m;break}}if(l===-1)throw new Error(`The pattern \`${t}\` is not balanced.`);let p=i.slice(1,l),c=i.slice(l+1),d;Wi(p)?d=Bi(p):d=O(p,","),d=d.flatMap(m=>tt(m));let f=tt(c);for(let m of f)for(let g of d)n.push(e+g+m);return n}function Wi(t){return Qr.test(t)}function Bi(t){let r=t.match(Qr);if(!r)return[t];let[,n,e,i]=r,a=i?parseInt(i,10):void 0,l=[];if(/^-?\d+$/.test(n)&&/^-?\d+$/.test(e)){let p=parseInt(n,10),c=parseInt(e,10),d=Math.max(n.replace(/^-/,"").length,e.replace(/^-/,"").length);if(a===void 0&&(a=p<=c?1:-1),a===0)throw new Error("Step cannot be zero in sequence expansion.");if(a>0)for(let f=p;f<=c;f+=a){let m=f.toString();m.length<d&&(m=m.padStart(d,"0")),l.push(m)}else for(let f=p;f>=c;f+=a){let m=f.toString();m.length<d&&(m=m.padStart(d,"0")),l.push(m)}}return l}var qi=/^[a-z]+$/;function Hi(){throw new Error("No `loadModule` function provided to `compile`")}function Gi(){throw new Error("No `loadStylesheet` function provided to `compile`")}function Yi(t){let r=0,n=null;for(let e of O(t," "))e==="reference"?r|=2:e==="inline"?r|=1:e==="default"?r|=4:e==="static"?r|=8:e.startsWith("prefix(")&&e.endsWith(")")&&(n=e.slice(7,-1));return[r,n]}async function Ji(t,{base:r="",loadModule:n=Hi,loadStylesheet:e=Gi}={}){let i=0;t=[X({base:r},t)],i|=await At(t,r,e);let a=null,l=new Ie,p=[],c=[],d=null,f=null,m=[],g=[],y=[],b=[],k=null;K(t,(x,{parent:V,replaceWith:P,context:R})=>{if(x.kind==="at-rule"){if(x.name==="@tailwind"&&(x.params==="utilities"||x.params.startsWith("utilities"))){if(f!==null){P([]);return}let _=O(x.params," ");for(let o of _)if(o.startsWith("source(")){let u=o.slice(7,-1);if(u==="none"){k=u;continue}if(u[0]==='"'&&u[u.length-1]!=='"'||u[0]==="'"&&u[u.length-1]!=="'"||u[0]!=="'"&&u[0]!=='"')throw new Error("`source(\u2026)` paths must be quoted.");k={base:R.sourceBase??R.base,pattern:u.slice(1,-1)}}f=x,i|=16}if(x.name==="@utility"){if(V!==null)throw new Error("`@utility` cannot be nested.");if(x.nodes.length===0)throw new Error(`\`@utility ${x.params}\` is empty. Utilities should include at least one property.`);let _=ur(x);if(_===null)throw new Error(`\`@utility ${x.params}\` defines an invalid utility name. Utilities should be alphanumeric and start with a lowercase letter.`);c.push(_)}if(x.name==="@source"){if(x.nodes.length>0)throw new Error("`@source` cannot have a body.");if(V!==null)throw new Error("`@source` cannot be nested.");let _=!1,o=!1,u=x.params;if(!1,u[0]==='"'&&u[u.length-1]!=='"'||u[0]==="'"&&u[u.length-1]!=="'"||u[0]!=="'"&&u[0]!=='"')throw new Error("`@source` paths must be quoted.");let h=u.slice(1,-1);if(!1){let w=_?b:y,v=O(h," ");for(let N of v)for(let E of tt(N))w.push(E)}else g.push({base:R.base,pattern:h});P([]);return}if(x.name==="@variant"&&(V===null?x.nodes.length===0?x.name="@custom-variant":(K(x.nodes,_=>{if(_.kind==="at-rule"&&_.name==="@slot")return x.name="@custom-variant",2}),x.name==="@variant"&&m.push(x)):m.push(x)),x.name==="@custom-variant"){if(V!==null)throw new Error("`@custom-variant` cannot be nested.");P([]);let[_,o]=O(x.params," ");if(!Ge.test(_))throw new Error(`\`@custom-variant ${_}\` defines an invalid variant name. Variants should only contain alphanumeric, dashes or underscore characters.`);if(x.nodes.length>0&&o)throw new Error(`\`@custom-variant ${_}\` cannot have both a selector and a body.`);if(x.nodes.length===0){if(!o)throw new Error(`\`@custom-variant ${_}\` has no selector or body.`);let u=O(o.slice(1,-1),",");if(u.length===0||u.some(v=>v.trim()===""))throw new Error(`\`@custom-variant ${_} (${u.join(",")})\` selector is invalid.`);let h=[],w=[];for(let v of u)v=v.trim(),v[0]==="@"?h.push(v):w.push(v);p.push(v=>{v.variants.static(_,N=>{let E=[];w.length>0&&E.push(j(w.join(", "),N.nodes));for(let S of h)E.push(M(S,N.nodes));N.nodes=E},{compounds:de([...w,...h])})});return}else{p.push(u=>{u.variants.fromAst(_,x.nodes)});return}}if(x.name==="@media"){let _=O(x.params," "),o=[];for(let u of _)if(u.startsWith("source(")){let h=u.slice(7,-1);K(x.nodes,(w,{replaceWith:v})=>{if(w.kind==="at-rule"&&w.name==="@tailwind"&&w.params==="utilities")return w.params+=` source(${h})`,v([X({sourceBase:R.base},[w])]),2})}else if(u.startsWith("theme(")){let h=u.slice(6,-1),w=h.includes("reference");K(x.nodes,v=>{if(v.kind!=="at-rule"){if(w)throw new Error('Files imported with `@import "\u2026" theme(reference)` must only contain `@theme` blocks.\nUse `@reference "\u2026";` instead.');return 0}if(v.name==="@theme")return v.params+=" "+h,1})}else if(u.startsWith("prefix(")){let h=u.slice(7,-1);K(x.nodes,w=>{if(w.kind==="at-rule"&&w.name==="@theme")return w.params+=` prefix(${h})`,1})}else u==="important"?a=!0:u==="reference"?x.nodes=[X({reference:!0},x.nodes)]:o.push(u);o.length>0?x.params=o.join(" "):_.length>0&&P(x.nodes)}if(x.name==="@theme"){let[_,o]=Yi(x.params);if(R.reference&&(_|=2),o){if(!qi.test(o))throw new Error(`The prefix "${o}" is invalid. Prefixes must be lowercase ASCII letters (a-z) only.`);l.prefix=o}return K(x.nodes,u=>{if(u.kind==="at-rule"&&u.name==="@keyframes")return l.addKeyframes(u),1;if(u.kind==="comment")return;if(u.kind==="declaration"&&u.property.startsWith("--")){l.add(se(u.property),u.value??"",_);return}let h=J([U(x.name,x.params,[u])]).split(`
`).map((w,v,N)=>`${v===0||v>=N.length-2?" ":">"} ${w}`).join(`
`);throw new Error(`\`@theme\` blocks must only contain custom properties or \`@keyframes\`.

${h}`)}),d?P([]):(d=j(":root, :host",[]),P([d])),1}}});let A=br(l);if(a&&(A.important=a),b.length>0)for(let x of b)A.invalidCandidates.add(x);i|=await Zr({designSystem:A,base:r,ast:t,loadModule:n,globs:g});for(let x of p)x(A);for(let x of c)x(A);if(d){let x=[];for(let[P,R]of A.theme.entries())R.options&2||x.push(s(ne(P),R.value));let V=A.theme.getKeyframes();for(let P of V)t.push(X({theme:!0},[D([P])]));d.nodes=[X({theme:!0},x)]}if(f){let x=f;x.kind="context",x.context={}}if(m.length>0){for(let x of m){let V=j("&",x.nodes),P=x.params,R=A.parseVariant(P);if(R===null)throw new Error(`Cannot use \`@variant\` with unknown variant: ${P}`);if(ye(V,R,A.variants)===null)throw new Error(`Cannot use \`@variant\` with variant: ${P}`);Object.assign(x,V)}i|=32}return i|=be(t,A),i|=$e(t,A),K(t,(x,{replaceWith:V})=>{if(x.kind==="at-rule")return x.name==="@utility"&&V([]),1}),{designSystem:A,ast:t,globs:g,root:k,utilitiesNode:f,features:i,inlineCandidates:y}}async function Zi(t,r={}){let{designSystem:n,ast:e,globs:i,root:a,utilitiesNode:l,features:p,inlineCandidates:c}=await Ji(t,r);e.unshift(je(`! tailwindcss v${Et} | MIT License | https://tailwindcss.com `));function d(b){n.invalidCandidates.add(b)}let f=new Set,m=null,g=0,y=!1;for(let b of c)n.invalidCandidates.has(b)||(f.add(b),y=!0);return{globs:i,root:a,features:p,build(b){if(p===0)return t;if(!l)return m??=ue(e,n),m;let k=y,A=!1;y=!1;let x=f.size;for(let P of b)if(!n.invalidCandidates.has(P))if(P[0]==="-"&&P[1]==="-"){let R=n.theme.markUsedVariable(P);k||=R,A||=R}else f.add(P),k||=f.size!==x;if(!k)return m??=ue(e,n),m;let V=oe(f,n,{onInvalidCandidate:d}).astNodes;return!A&&g===V.length?(m??=ue(e,n),m):(g=V.length,l.nodes=V,m=ue(e,n),m)}}}async function en(t,r={}){let n=me(t),e=await Zi(n,r),i=n,a=t;return{...e,build(l){let p=e.build(l);return p===i||(a=J(p),i=p),a}}}var tn=`@layer theme, base, components, utilities;

@import './theme.css' layer(theme);
@import './preflight.css' layer(base);
@import './utilities.css' layer(utilities);
`;var rn=`/*
  1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
  2. Remove default margins and padding
  3. Reset all borders.
*/

*,
::after,
::before,
::backdrop,
::file-selector-button {
  box-sizing: border-box; /* 1 */
  margin: 0; /* 2 */
  padding: 0; /* 2 */
  border: 0 solid; /* 3 */
}

/*
  1. Use a consistent sensible line-height in all browsers.
  2. Prevent adjustments of font size after orientation changes in iOS.
  3. Use a more readable tab size.
  4. Use the user's configured \`sans\` font-family by default.
  5. Use the user's configured \`sans\` font-feature-settings by default.
  6. Use the user's configured \`sans\` font-variation-settings by default.
  7. Disable tap highlights on iOS.
*/

html,
:host {
  line-height: 1.5; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */
  tab-size: 4; /* 3 */
  font-family: --theme(
    --default-font-family,
    ui-sans-serif,
    system-ui,
    sans-serif,
    'Apple Color Emoji',
    'Segoe UI Emoji',
    'Segoe UI Symbol',
    'Noto Color Emoji'
  ); /* 4 */
  font-feature-settings: --theme(--default-font-feature-settings, normal); /* 5 */
  font-variation-settings: --theme(--default-font-variation-settings, normal); /* 6 */
  -webkit-tap-highlight-color: transparent; /* 7 */
}

/*
  1. Add the correct height in Firefox.
  2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
  3. Reset the default border style to a 1px solid border.
*/

hr {
  height: 0; /* 1 */
  color: inherit; /* 2 */
  border-top-width: 1px; /* 3 */
}

/*
  Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
  text-decoration: underline dotted;
}

/*
  Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
  Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

/*
  Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
  1. Use the user's configured \`mono\` font-family by default.
  2. Use the user's configured \`mono\` font-feature-settings by default.
  3. Use the user's configured \`mono\` font-variation-settings by default.
  4. Correct the odd \`em\` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: --theme(
    --default-mono-font-family,
    ui-monospace,
    SFMono-Regular,
    Menlo,
    Monaco,
    Consolas,
    'Liberation Mono',
    'Courier New',
    monospace
  ); /* 1 */
  font-feature-settings: --theme(--default-mono-font-feature-settings, normal); /* 2 */
  font-variation-settings: --theme(--default-mono-font-variation-settings, normal); /* 3 */
  font-size: 1em; /* 4 */
}

/*
  Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
  Prevent \`sub\` and \`sup\` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
  1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
  2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
  3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0; /* 1 */
  border-color: inherit; /* 2 */
  border-collapse: collapse; /* 3 */
}

/*
  Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
  Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
  Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
  Make lists unstyled by default.
*/

ol,
ul,
menu {
  list-style: none;
}

/*
  1. Make replaced elements \`display: block\` by default. (https://github.com/mozdevs/cssremedy/issues/14)
  2. Add \`vertical-align: middle\` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
      This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block; /* 1 */
  vertical-align: middle; /* 2 */
}

/*
  Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/*
  1. Inherit font styles in all browsers.
  2. Remove border radius in all browsers.
  3. Remove background color in all browsers.
  4. Ensure consistent opacity for disabled states in all browsers.
*/

button,
input,
select,
optgroup,
textarea,
::file-selector-button {
  font: inherit; /* 1 */
  font-feature-settings: inherit; /* 1 */
  font-variation-settings: inherit; /* 1 */
  letter-spacing: inherit; /* 1 */
  color: inherit; /* 1 */
  border-radius: 0; /* 2 */
  background-color: transparent; /* 3 */
  opacity: 1; /* 4 */
}

/*
  Restore default font weight.
*/

:where(select:is([multiple], [size])) optgroup {
  font-weight: bolder;
}

/*
  Restore indentation.
*/

:where(select:is([multiple], [size])) optgroup option {
  padding-inline-start: 20px;
}

/*
  Restore space after button.
*/

::file-selector-button {
  margin-inline-end: 4px;
}

/*
  Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
*/

::placeholder {
  opacity: 1;
}

/*
  Set the default placeholder color to a semi-transparent version of the current text color in browsers that do not
  crash when using \`color-mix(\u2026)\` with \`currentColor\`. (https://github.com/tailwindlabs/tailwindcss/issues/17194)
*/

@supports (not (-webkit-appearance: -apple-pay-button)) /* Not Safari */ or
  (contain-intrinsic-size: 1px) /* Safari 17+ */ {
  ::placeholder {
    color: color-mix(in oklab, currentColor 50%, transparent);
  }
}

/*
  Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
  Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
  1. Ensure date/time inputs have the same height when empty in iOS Safari.
  2. Ensure text alignment can be changed on date/time inputs in iOS Safari.
*/

::-webkit-date-and-time-value {
  min-height: 1lh; /* 1 */
  text-align: inherit; /* 2 */
}

/*
  Prevent height from changing on date/time inputs in macOS Safari when the input is set to \`display: block\`.
*/

::-webkit-datetime-edit {
  display: inline-flex;
}

/*
  Remove excess padding from pseudo-elements in date/time inputs to ensure consistent height across browsers.
*/

::-webkit-datetime-edit-fields-wrapper {
  padding: 0;
}

::-webkit-datetime-edit,
::-webkit-datetime-edit-year-field,
::-webkit-datetime-edit-month-field,
::-webkit-datetime-edit-day-field,
::-webkit-datetime-edit-hour-field,
::-webkit-datetime-edit-minute-field,
::-webkit-datetime-edit-second-field,
::-webkit-datetime-edit-millisecond-field,
::-webkit-datetime-edit-meridiem-field {
  padding-block: 0;
}

/*
  Remove the additional \`:invalid\` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
  Correct the inability to style the border radius in iOS Safari.
*/

button,
input:where([type='button'], [type='reset'], [type='submit']),
::file-selector-button {
  appearance: button;
}

/*
  Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
  Make elements with the HTML hidden attribute stay hidden by default.
*/

[hidden]:where(:not([hidden='until-found'])) {
  display: none !important;
}
`;var nn=`@theme default {
  --font-sans:
    ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
    'Noto Color Emoji';
  --font-serif: ui-serif, Georgia, Cambria, 'Times New Roman', Times, serif;
  --font-mono:
    ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New',
    monospace;

  --color-red-50: oklch(0.971 0.013 17.38);
  --color-red-100: oklch(0.936 0.032 17.717);
  --color-red-200: oklch(0.885 0.062 18.334);
  --color-red-300: oklch(0.808 0.114 19.571);
  --color-red-400: oklch(0.704 0.191 22.216);
  --color-red-500: oklch(0.637 0.237 25.331);
  --color-red-600: oklch(0.577 0.245 27.325);
  --color-red-700: oklch(0.505 0.213 27.518);
  --color-red-800: oklch(0.444 0.177 26.899);
  --color-red-900: oklch(0.396 0.141 25.723);
  --color-red-950: oklch(0.258 0.092 26.042);

  --color-orange-50: oklch(0.98 0.016 73.684);
  --color-orange-100: oklch(0.954 0.038 75.164);
  --color-orange-200: oklch(0.901 0.076 70.697);
  --color-orange-300: oklch(0.837 0.128 66.29);
  --color-orange-400: oklch(0.75 0.183 55.934);
  --color-orange-500: oklch(0.705 0.213 47.604);
  --color-orange-600: oklch(0.646 0.222 41.116);
  --color-orange-700: oklch(0.553 0.195 38.402);
  --color-orange-800: oklch(0.47 0.157 37.304);
  --color-orange-900: oklch(0.408 0.123 38.172);
  --color-orange-950: oklch(0.266 0.079 36.259);

  --color-amber-50: oklch(0.987 0.022 95.277);
  --color-amber-100: oklch(0.962 0.059 95.617);
  --color-amber-200: oklch(0.924 0.12 95.746);
  --color-amber-300: oklch(0.879 0.169 91.605);
  --color-amber-400: oklch(0.828 0.189 84.429);
  --color-amber-500: oklch(0.769 0.188 70.08);
  --color-amber-600: oklch(0.666 0.179 58.318);
  --color-amber-700: oklch(0.555 0.163 48.998);
  --color-amber-800: oklch(0.473 0.137 46.201);
  --color-amber-900: oklch(0.414 0.112 45.904);
  --color-amber-950: oklch(0.279 0.077 45.635);

  --color-yellow-50: oklch(0.987 0.026 102.212);
  --color-yellow-100: oklch(0.973 0.071 103.193);
  --color-yellow-200: oklch(0.945 0.129 101.54);
  --color-yellow-300: oklch(0.905 0.182 98.111);
  --color-yellow-400: oklch(0.852 0.199 91.936);
  --color-yellow-500: oklch(0.795 0.184 86.047);
  --color-yellow-600: oklch(0.681 0.162 75.834);
  --color-yellow-700: oklch(0.554 0.135 66.442);
  --color-yellow-800: oklch(0.476 0.114 61.907);
  --color-yellow-900: oklch(0.421 0.095 57.708);
  --color-yellow-950: oklch(0.286 0.066 53.813);

  --color-lime-50: oklch(0.986 0.031 120.757);
  --color-lime-100: oklch(0.967 0.067 122.328);
  --color-lime-200: oklch(0.938 0.127 124.321);
  --color-lime-300: oklch(0.897 0.196 126.665);
  --color-lime-400: oklch(0.841 0.238 128.85);
  --color-lime-500: oklch(0.768 0.233 130.85);
  --color-lime-600: oklch(0.648 0.2 131.684);
  --color-lime-700: oklch(0.532 0.157 131.589);
  --color-lime-800: oklch(0.453 0.124 130.933);
  --color-lime-900: oklch(0.405 0.101 131.063);
  --color-lime-950: oklch(0.274 0.072 132.109);

  --color-green-50: oklch(0.982 0.018 155.826);
  --color-green-100: oklch(0.962 0.044 156.743);
  --color-green-200: oklch(0.925 0.084 155.995);
  --color-green-300: oklch(0.871 0.15 154.449);
  --color-green-400: oklch(0.792 0.209 151.711);
  --color-green-500: oklch(0.723 0.219 149.579);
  --color-green-600: oklch(0.627 0.194 149.214);
  --color-green-700: oklch(0.527 0.154 150.069);
  --color-green-800: oklch(0.448 0.119 151.328);
  --color-green-900: oklch(0.393 0.095 152.535);
  --color-green-950: oklch(0.266 0.065 152.934);

  --color-emerald-50: oklch(0.979 0.021 166.113);
  --color-emerald-100: oklch(0.95 0.052 163.051);
  --color-emerald-200: oklch(0.905 0.093 164.15);
  --color-emerald-300: oklch(0.845 0.143 164.978);
  --color-emerald-400: oklch(0.765 0.177 163.223);
  --color-emerald-500: oklch(0.696 0.17 162.48);
  --color-emerald-600: oklch(0.596 0.145 163.225);
  --color-emerald-700: oklch(0.508 0.118 165.612);
  --color-emerald-800: oklch(0.432 0.095 166.913);
  --color-emerald-900: oklch(0.378 0.077 168.94);
  --color-emerald-950: oklch(0.262 0.051 172.552);

  --color-teal-50: oklch(0.984 0.014 180.72);
  --color-teal-100: oklch(0.953 0.051 180.801);
  --color-teal-200: oklch(0.91 0.096 180.426);
  --color-teal-300: oklch(0.855 0.138 181.071);
  --color-teal-400: oklch(0.777 0.152 181.912);
  --color-teal-500: oklch(0.704 0.14 182.503);
  --color-teal-600: oklch(0.6 0.118 184.704);
  --color-teal-700: oklch(0.511 0.096 186.391);
  --color-teal-800: oklch(0.437 0.078 188.216);
  --color-teal-900: oklch(0.386 0.063 188.416);
  --color-teal-950: oklch(0.277 0.046 192.524);

  --color-cyan-50: oklch(0.984 0.019 200.873);
  --color-cyan-100: oklch(0.956 0.045 203.388);
  --color-cyan-200: oklch(0.917 0.08 205.041);
  --color-cyan-300: oklch(0.865 0.127 207.078);
  --color-cyan-400: oklch(0.789 0.154 211.53);
  --color-cyan-500: oklch(0.715 0.143 215.221);
  --color-cyan-600: oklch(0.609 0.126 221.723);
  --color-cyan-700: oklch(0.52 0.105 223.128);
  --color-cyan-800: oklch(0.45 0.085 224.283);
  --color-cyan-900: oklch(0.398 0.07 227.392);
  --color-cyan-950: oklch(0.302 0.056 229.695);

  --color-sky-50: oklch(0.977 0.013 236.62);
  --color-sky-100: oklch(0.951 0.026 236.824);
  --color-sky-200: oklch(0.901 0.058 230.902);
  --color-sky-300: oklch(0.828 0.111 230.318);
  --color-sky-400: oklch(0.746 0.16 232.661);
  --color-sky-500: oklch(0.685 0.169 237.323);
  --color-sky-600: oklch(0.588 0.158 241.966);
  --color-sky-700: oklch(0.5 0.134 242.749);
  --color-sky-800: oklch(0.443 0.11 240.79);
  --color-sky-900: oklch(0.391 0.09 240.876);
  --color-sky-950: oklch(0.293 0.066 243.157);

  --color-blue-50: oklch(0.97 0.014 254.604);
  --color-blue-100: oklch(0.932 0.032 255.585);
  --color-blue-200: oklch(0.882 0.059 254.128);
  --color-blue-300: oklch(0.809 0.105 251.813);
  --color-blue-400: oklch(0.707 0.165 254.624);
  --color-blue-500: oklch(0.623 0.214 259.815);
  --color-blue-600: oklch(0.546 0.245 262.881);
  --color-blue-700: oklch(0.488 0.243 264.376);
  --color-blue-800: oklch(0.424 0.199 265.638);
  --color-blue-900: oklch(0.379 0.146 265.522);
  --color-blue-950: oklch(0.282 0.091 267.935);

  --color-indigo-50: oklch(0.962 0.018 272.314);
  --color-indigo-100: oklch(0.93 0.034 272.788);
  --color-indigo-200: oklch(0.87 0.065 274.039);
  --color-indigo-300: oklch(0.785 0.115 274.713);
  --color-indigo-400: oklch(0.673 0.182 276.935);
  --color-indigo-500: oklch(0.585 0.233 277.117);
  --color-indigo-600: oklch(0.511 0.262 276.966);
  --color-indigo-700: oklch(0.457 0.24 277.023);
  --color-indigo-800: oklch(0.398 0.195 277.366);
  --color-indigo-900: oklch(0.359 0.144 278.697);
  --color-indigo-950: oklch(0.257 0.09 281.288);

  --color-violet-50: oklch(0.969 0.016 293.756);
  --color-violet-100: oklch(0.943 0.029 294.588);
  --color-violet-200: oklch(0.894 0.057 293.283);
  --color-violet-300: oklch(0.811 0.111 293.571);
  --color-violet-400: oklch(0.702 0.183 293.541);
  --color-violet-500: oklch(0.606 0.25 292.717);
  --color-violet-600: oklch(0.541 0.281 293.009);
  --color-violet-700: oklch(0.491 0.27 292.581);
  --color-violet-800: oklch(0.432 0.232 292.759);
  --color-violet-900: oklch(0.38 0.189 293.745);
  --color-violet-950: oklch(0.283 0.141 291.089);

  --color-purple-50: oklch(0.977 0.014 308.299);
  --color-purple-100: oklch(0.946 0.033 307.174);
  --color-purple-200: oklch(0.902 0.063 306.703);
  --color-purple-300: oklch(0.827 0.119 306.383);
  --color-purple-400: oklch(0.714 0.203 305.504);
  --color-purple-500: oklch(0.627 0.265 303.9);
  --color-purple-600: oklch(0.558 0.288 302.321);
  --color-purple-700: oklch(0.496 0.265 301.924);
  --color-purple-800: oklch(0.438 0.218 303.724);
  --color-purple-900: oklch(0.381 0.176 304.987);
  --color-purple-950: oklch(0.291 0.149 302.717);

  --color-fuchsia-50: oklch(0.977 0.017 320.058);
  --color-fuchsia-100: oklch(0.952 0.037 318.852);
  --color-fuchsia-200: oklch(0.903 0.076 319.62);
  --color-fuchsia-300: oklch(0.833 0.145 321.434);
  --color-fuchsia-400: oklch(0.74 0.238 322.16);
  --color-fuchsia-500: oklch(0.667 0.295 322.15);
  --color-fuchsia-600: oklch(0.591 0.293 322.896);
  --color-fuchsia-700: oklch(0.518 0.253 323.949);
  --color-fuchsia-800: oklch(0.452 0.211 324.591);
  --color-fuchsia-900: oklch(0.401 0.17 325.612);
  --color-fuchsia-950: oklch(0.293 0.136 325.661);

  --color-pink-50: oklch(0.971 0.014 343.198);
  --color-pink-100: oklch(0.948 0.028 342.258);
  --color-pink-200: oklch(0.899 0.061 343.231);
  --color-pink-300: oklch(0.823 0.12 346.018);
  --color-pink-400: oklch(0.718 0.202 349.761);
  --color-pink-500: oklch(0.656 0.241 354.308);
  --color-pink-600: oklch(0.592 0.249 0.584);
  --color-pink-700: oklch(0.525 0.223 3.958);
  --color-pink-800: oklch(0.459 0.187 3.815);
  --color-pink-900: oklch(0.408 0.153 2.432);
  --color-pink-950: oklch(0.284 0.109 3.907);

  --color-rose-50: oklch(0.969 0.015 12.422);
  --color-rose-100: oklch(0.941 0.03 12.58);
  --color-rose-200: oklch(0.892 0.058 10.001);
  --color-rose-300: oklch(0.81 0.117 11.638);
  --color-rose-400: oklch(0.712 0.194 13.428);
  --color-rose-500: oklch(0.645 0.246 16.439);
  --color-rose-600: oklch(0.586 0.253 17.585);
  --color-rose-700: oklch(0.514 0.222 16.935);
  --color-rose-800: oklch(0.455 0.188 13.697);
  --color-rose-900: oklch(0.41 0.159 10.272);
  --color-rose-950: oklch(0.271 0.105 12.094);

  --color-slate-50: oklch(0.984 0.003 247.858);
  --color-slate-100: oklch(0.968 0.007 247.896);
  --color-slate-200: oklch(0.929 0.013 255.508);
  --color-slate-300: oklch(0.869 0.022 252.894);
  --color-slate-400: oklch(0.704 0.04 256.788);
  --color-slate-500: oklch(0.554 0.046 257.417);
  --color-slate-600: oklch(0.446 0.043 257.281);
  --color-slate-700: oklch(0.372 0.044 257.287);
  --color-slate-800: oklch(0.279 0.041 260.031);
  --color-slate-900: oklch(0.208 0.042 265.755);
  --color-slate-950: oklch(0.129 0.042 264.695);

  --color-gray-50: oklch(0.985 0.002 247.839);
  --color-gray-100: oklch(0.967 0.003 264.542);
  --color-gray-200: oklch(0.928 0.006 264.531);
  --color-gray-300: oklch(0.872 0.01 258.338);
  --color-gray-400: oklch(0.707 0.022 261.325);
  --color-gray-500: oklch(0.551 0.027 264.364);
  --color-gray-600: oklch(0.446 0.03 256.802);
  --color-gray-700: oklch(0.373 0.034 259.733);
  --color-gray-800: oklch(0.278 0.033 256.848);
  --color-gray-900: oklch(0.21 0.034 264.665);
  --color-gray-950: oklch(0.13 0.028 261.692);

  --color-zinc-50: oklch(0.985 0 0);
  --color-zinc-100: oklch(0.967 0.001 286.375);
  --color-zinc-200: oklch(0.92 0.004 286.32);
  --color-zinc-300: oklch(0.871 0.006 286.286);
  --color-zinc-400: oklch(0.705 0.015 286.067);
  --color-zinc-500: oklch(0.552 0.016 285.938);
  --color-zinc-600: oklch(0.442 0.017 285.786);
  --color-zinc-700: oklch(0.37 0.013 285.805);
  --color-zinc-800: oklch(0.274 0.006 286.033);
  --color-zinc-900: oklch(0.21 0.006 285.885);
  --color-zinc-950: oklch(0.141 0.005 285.823);

  --color-neutral-50: oklch(0.985 0 0);
  --color-neutral-100: oklch(0.97 0 0);
  --color-neutral-200: oklch(0.922 0 0);
  --color-neutral-300: oklch(0.87 0 0);
  --color-neutral-400: oklch(0.708 0 0);
  --color-neutral-500: oklch(0.556 0 0);
  --color-neutral-600: oklch(0.439 0 0);
  --color-neutral-700: oklch(0.371 0 0);
  --color-neutral-800: oklch(0.269 0 0);
  --color-neutral-900: oklch(0.205 0 0);
  --color-neutral-950: oklch(0.145 0 0);

  --color-stone-50: oklch(0.985 0.001 106.423);
  --color-stone-100: oklch(0.97 0.001 106.424);
  --color-stone-200: oklch(0.923 0.003 48.717);
  --color-stone-300: oklch(0.869 0.005 56.366);
  --color-stone-400: oklch(0.709 0.01 56.259);
  --color-stone-500: oklch(0.553 0.013 58.071);
  --color-stone-600: oklch(0.444 0.011 73.639);
  --color-stone-700: oklch(0.374 0.01 67.558);
  --color-stone-800: oklch(0.268 0.007 34.298);
  --color-stone-900: oklch(0.216 0.006 56.043);
  --color-stone-950: oklch(0.147 0.004 49.25);

  --color-black: #000;
  --color-white: #fff;

  --spacing: 0.25rem;

  --breakpoint-sm: 40rem;
  --breakpoint-md: 48rem;
  --breakpoint-lg: 64rem;
  --breakpoint-xl: 80rem;
  --breakpoint-2xl: 96rem;

  --container-3xs: 16rem;
  --container-2xs: 18rem;
  --container-xs: 20rem;
  --container-sm: 24rem;
  --container-md: 28rem;
  --container-lg: 32rem;
  --container-xl: 36rem;
  --container-2xl: 42rem;
  --container-3xl: 48rem;
  --container-4xl: 56rem;
  --container-5xl: 64rem;
  --container-6xl: 72rem;
  --container-7xl: 80rem;

  --text-xs: 0.75rem;
  --text-xs--line-height: calc(1 / 0.75);
  --text-sm: 0.875rem;
  --text-sm--line-height: calc(1.25 / 0.875);
  --text-base: 1rem;
  --text-base--line-height: calc(1.5 / 1);
  --text-lg: 1.125rem;
  --text-lg--line-height: calc(1.75 / 1.125);
  --text-xl: 1.25rem;
  --text-xl--line-height: calc(1.75 / 1.25);
  --text-2xl: 1.5rem;
  --text-2xl--line-height: calc(2 / 1.5);
  --text-3xl: 1.875rem;
  --text-3xl--line-height: calc(2.25 / 1.875);
  --text-4xl: 2.25rem;
  --text-4xl--line-height: calc(2.5 / 2.25);
  --text-5xl: 3rem;
  --text-5xl--line-height: 1;
  --text-6xl: 3.75rem;
  --text-6xl--line-height: 1;
  --text-7xl: 4.5rem;
  --text-7xl--line-height: 1;
  --text-8xl: 6rem;
  --text-8xl--line-height: 1;
  --text-9xl: 8rem;
  --text-9xl--line-height: 1;

  --font-weight-thin: 100;
  --font-weight-extralight: 200;
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  --font-weight-black: 900;

  --tracking-tighter: -0.05em;
  --tracking-tight: -0.025em;
  --tracking-normal: 0em;
  --tracking-wide: 0.025em;
  --tracking-wider: 0.05em;
  --tracking-widest: 0.1em;

  --leading-tight: 1.25;
  --leading-snug: 1.375;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;
  --leading-loose: 2;

  --radius-xs: 0.125rem;
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-3xl: 1.5rem;
  --radius-4xl: 2rem;

  --shadow-2xs: 0 1px rgb(0 0 0 / 0.05);
  --shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);

  --inset-shadow-2xs: inset 0 1px rgb(0 0 0 / 0.05);
  --inset-shadow-xs: inset 0 1px 1px rgb(0 0 0 / 0.05);
  --inset-shadow-sm: inset 0 2px 4px rgb(0 0 0 / 0.05);

  --drop-shadow-xs: 0 1px 1px rgb(0 0 0 / 0.05);
  --drop-shadow-sm: 0 1px 2px rgb(0 0 0 / 0.15);
  --drop-shadow-md: 0 3px 3px rgb(0 0 0 / 0.12);
  --drop-shadow-lg: 0 4px 4px rgb(0 0 0 / 0.15);
  --drop-shadow-xl: 0 9px 7px rgb(0 0 0 / 0.1);
  --drop-shadow-2xl: 0 25px 25px rgb(0 0 0 / 0.15);

  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);

  --animate-spin: spin 1s linear infinite;
  --animate-ping: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
  --animate-pulse: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  --animate-bounce: bounce 1s infinite;

  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }

  @keyframes ping {
    75%,
    100% {
      transform: scale(2);
      opacity: 0;
    }
  }

  @keyframes pulse {
    50% {
      opacity: 0.5;
    }
  }

  @keyframes bounce {
    0%,
    100% {
      transform: translateY(-25%);
      animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
    }

    50% {
      transform: none;
      animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
    }
  }

  --blur-xs: 4px;
  --blur-sm: 8px;
  --blur-md: 12px;
  --blur-lg: 16px;
  --blur-xl: 24px;
  --blur-2xl: 40px;
  --blur-3xl: 64px;

  --perspective-dramatic: 100px;
  --perspective-near: 300px;
  --perspective-normal: 500px;
  --perspective-midrange: 800px;
  --perspective-distant: 1200px;

  --aspect-video: 16 / 9;

  --default-transition-duration: 150ms;
  --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  --default-font-family: --theme(--font-sans, initial);
  --default-font-feature-settings: --theme(--font-sans--font-feature-settings, initial);
  --default-font-variation-settings: --theme(--font-sans--font-variation-settings, initial);
  --default-mono-font-family: --theme(--font-mono, initial);
  --default-mono-font-feature-settings: --theme(--font-mono--font-feature-settings, initial);
  --default-mono-font-variation-settings: --theme(--font-mono--font-variation-settings, initial);
}

/* Deprecated */
@theme default inline reference {
  --blur: 8px;
  --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-inner: inset 0 2px 4px 0 rgb(0 0 0 / 0.05);
  --drop-shadow: 0 1px 2px rgb(0 0 0 / 0.1), 0 1px 1px rgb(0 0 0 / 0.06);
  --radius: 0.25rem;
  --max-width-prose: 65ch;
}
`;var on=`@tailwind utilities;
`;var Oe={index:tn,preflight:rn,theme:nn,utilities:on};var rt=class{start(r){performance.mark(`${r} (start)`)}end(r,n){performance.mark(`${r} (end)`),performance.measure(r,{start:`${r} (start)`,end:`${r} (end)`,detail:n})}hit(r,n){performance.mark(r,{detail:n})}error(r){throw performance.mark("(error)",{detail:{error:`${r}`}}),r}};console.warn("The browser build of Tailwind CSS should not be used in production. To use Tailwind CSS in production, use the Tailwind CLI, Vite plugin, or PostCSS plugin: https://tailwindcss.com/docs/installation");var an="text/tailwindcss",nt,Tt=new Set,$t="",Vt=document.createElement("style"),ln=Promise.resolve(),io=1,B=new rt;async function oo(){B.start("Create compiler"),B.start("Reading Stylesheets");let t=document.querySelectorAll(`style[type="${an}"]`),r="";for(let n of t)sn(n),r+=n.textContent+`
`;if(r.includes("@import")||(r=`@import "tailwindcss";${r}`),B.end("Reading Stylesheets",{size:r.length,changed:$t!==r}),$t!==r){$t=r,B.start("Compile CSS");try{nt=await en(r,{base:"/",loadStylesheet:lo,loadModule:ao})}finally{B.end("Compile CSS"),B.end("Create compiler")}Tt.clear()}}async function lo(t,r){function n(){if(t==="tailwindcss")return{base:r,content:Oe.index};if(t==="tailwindcss/preflight"||t==="tailwindcss/preflight.css"||t==="./preflight.css")return{base:r,content:Oe.preflight};if(t==="tailwindcss/theme"||t==="tailwindcss/theme.css"||t==="./theme.css")return{base:r,content:Oe.theme};if(t==="tailwindcss/utilities"||t==="tailwindcss/utilities.css"||t==="./utilities.css")return{base:r,content:Oe.utilities};throw new Error(`The browser build does not support @import for "${t}"`)}try{let e=n();return B.hit("Loaded stylesheet",{id:t,base:r,size:e.content.length}),e}catch(e){throw B.hit("Failed to load stylesheet",{id:t,base:r,error:e.message??e}),e}}async function ao(){throw new Error("The browser build does not support plugins or config files.")}async function so(t){if(!nt)return;let r=new Set;B.start("Collect classes");for(let n of document.querySelectorAll("[class]"))for(let e of n.classList)Tt.has(e)||(Tt.add(e),r.add(e));B.end("Collect classes",{count:r.size}),!(r.size===0&&t==="incremental")&&(B.start("Build utilities"),Vt.textContent=nt.build(Array.from(r)),B.end("Build utilities"))}function it(t){async function r(){if(!nt&&t!=="full")return;let n=io++;B.start(`Build #${n} (${t})`),t==="full"&&await oo(),B.start("Build"),await so(t),B.end("Build"),B.end(`Build #${n} (${t})`)}ln=ln.then(r).catch(n=>B.error(n))}var uo=new MutationObserver(()=>it("full"));function sn(t){uo.observe(t,{attributes:!0,attributeFilter:["type"],characterData:!0,subtree:!0,childList:!0})}new MutationObserver(t=>{let r=0,n=0;for(let e of t){for(let i of e.addedNodes)i.nodeType===Node.ELEMENT_NODE&&i.tagName==="STYLE"&&i.getAttribute("type")===an&&(sn(i),r++);for(let i of e.addedNodes)i.nodeType===1&&i!==Vt&&n++;e.type==="attributes"&&n++}if(r>0)return it("full");if(n>0)return it("incremental")}).observe(document.documentElement,{attributes:!0,attributeFilter:["class"],childList:!0,subtree:!0});it("full");document.head.append(Vt);})();

(function () {
    let typingTimer;
    const doneTyping = 600;

    $("#resetSearchCPList").on("click", () => {
        $("#searchBarCPList").val("");
        document.getElementById("resetSearchCPList").style.display = "none";
        document.getElementById("loadingSearchCPList").style.display = "none";
        $("#submitFilterFormCP").click();
    });

    $("#searchBarCPList").on("keyup", () => {
        document.getElementById("resetSearchCPList").style.display = "block";
        document.getElementById("loadingSearchCPList").style.display = "block";
        clearTimeout(typingTimer);
        typingTimer = setTimeout(() => {
            $("#submitFilterFormCP").click();
        }, doneTyping);

    });

    $("#searchBarCPList").on("keydown", () => {
        clearTimeout(typingTimer);
    });

    $(".filter-checkbox").on("change", () => {
        $("#submitFilterFormCP").click();
    });

    $(".sanctionBtn").on("click", (e) => {
        e.stopPropagation();
        const id = e.currentTarget.id;
        console.log($(`#row${id}`));
        $(`#${id}tooltip`).css("display", "flex");
        $(`#row${id}`).css("background-color", "#ff000030");
    });

    $(".sanctionForm").on("submit", (e) => {
        e.preventDefault();
        console.log(e.currentTarget.createSanction);
        const formData = new FormData(e.currentTarget);
        const isin = formData.getAll("isin");
        const datefrom = formData.get("datefrom");
        const dateto = formData.get("dateto");
        const reason = formData.get("reason");

        e.currentTarget.createSanction.children[0].innerHTML = "Pridávam na zoznam...";
        e.currentTarget.createSanction.children[1].style.display = "inline-flex";

        console.log(formData);

        htmx.ajax('POST', `/api/cp/createSanction`,
            {
                target: "#toast",
                values: {
                    "isin": isin,
                    "datefrom": datefrom,
                    "dateto": dateto,
                    "reason": reason
                }
            }).then((response) => {

            });
    });

    document.body.addEventListener('htmx:afterSettle', function (evt) {
        document.getElementById("loadingSearchCPList").style.display = "none";
    });

    $(document).click(function (event) {
        var obj = $(".sanctionTooltip");
        if (!obj.is(event.target) && !obj.has(event.target).length) {
            $(".sanctionTooltip").hide();
            $(".sanctionTooltip").parent().parent().css("background-color", "unset");
        }
    });
})();


$("#sektorWrapper").on("change", "select", function (e) {
  console.log(e.target.value);
  $.ajax({
    url: `/src/Controllers/aktiva/getCPDruh.php?sektorid=${e.target.value}`,
    type: "GET",
    dataType: "json",
    success: function (data) {
      console.log(data.value.length);
      if (data.value.length !== 0) {
        let druhSelect = $("#odvetvieid");
        for (let i = 0; i < data.value.length; i++) {
          let option = $(
            `<option value='${data.value[i].odvetvieid}'>${data.value[i].odvetviepopis}</option>`,
          );
          druhSelect.append(option);
        }
      }
    },
    error: function (xhr, status, error) {
      console.error("Error:", error);
      alert("Nepodarilo sa aktualizovať!");
    },
  });
});
$("#addUcet").on("submit", function (e) {
  e.preventDefault();
  let formData = new URLSearchParams(new FormData(e.currentTarget));
  console.log(formData);
  let currencyUcet = formData.get("currencyUcet");
  let koducet = formData.get("kodUcet");
  let bloomberg = e.target.bloomberg.checked ? "Áno" : "Nie";
  let trh = formData.get("trh");
  let ucet = formData.get("ucet");
  let data = [currencyUcet, koducet, trh, bloomberg, ucet];
  let countik = $("#ucetni").children().length;

  let ucetTR = $(`<tr id='ucet${countik}'></tr>`);

  for (let i = 0; i < data.length; i++) {
    ucetTR.append(`<td class='py-2 border-black pl-5'>${data[i]}</td>`);
  }
  ucetTR.append(
    `<td class="text-red-500 underline cursor-pointer hover:text-red-800 hover:no-underline"><button type="button" id="${countik}" class="deleteUcet bg-red-500 px-2 text-white rounded-lg hover:bg-red-800 cursor-pointer transition-all">Zmazať</button></td>`,
  );

  $("#ucetni").append(ucetTR);
});

$(document).on("click", ".deleteUcet", function () {
  let id = $(".deleteUcet").attr("id");
  $("#ucet" + id).remove();
});

$(document).on("click", ".deleteUcetEdit", function () {
  let id = $(".deleteUcetEdit").attr("id");
  $.ajax({
    url: `/src/Controllers/aktiva/deleteUcet.php`,
    type: "POST",
    dataType: "json",
    processData: false,
    contentType: false,
    data: JSON.stringify({ id }),
    success: function (data) {
      console.log(data);
      if (data.error) {
        alert(data.errorMsg);
      } else {
        alert(data.errorMsg);
        $("#ucet" + id).remove();
      }
    },
    error: function (xhr, status, error) {
      console.error("Error:", error);
      alert("Nepodarilo sa aktualizovať!");
    },
  });
});

$("#addEmitentForm").on("submit", (e) => {
  e.preventDefault();
  let formData = new URLSearchParams(new FormData(e.currentTarget));
  console.log(formData);
  let nazovEmitenta = formData.get("nazovEmitenta");
  let skratka = formData.get("skratka");
  let stat = formData.get("stat");
  let ico = formData.get("ico");
  let sektor = formData.get("sektor");
  $.ajax({
    url: `/src/Controllers/aktiva/createNewEmitent.php`,
    type: "POST",
    dataType: "json",
    processData: false,
    contentType: false,
    data: JSON.stringify({ nazovEmitenta, skratka, stat, ico, sektor }),
    success: function (data) {
      $("#addEmitentForm").trigger("reset");
      $("#emitentMsg").removeClass("hidden");
      $("#emitentMsg").html(data.errorMsg);
      $("#emitentMsg").addClass(
        data.error ? "bg-red-400 font-bold" : "bg-green-400 font-bold",
      );
      $("#emitentMsg").delay(3000).fadeOut("slow");
      if (data.error === false) {
        $("#emitent").append(
          `<option selected='true' value='${data.id}'>${nazovEmitenta}</option>`,
        );
      }
      console.log(data);
    },
    error: function (xhr, status, error) {
      console.error("Error:", error);
      alert("Nepodarilo sa aktualizovať!");
    },
  });
});
$("#createCP").on("submit", (e) => {
  e.preventDefault();
  let formData = new URLSearchParams(new FormData(e.currentTarget));
  console.log(formData);
  console.log($("#ucetni").children().length);
  let uctyArray = [];
  for (const ucet of $("#ucetni").children()) {
    console.log(ucet.childNodes[1].innerText);
    const ucetObj = {
      mena: ucet.childNodes[0].innerText,
      kod: ucet.childNodes[1].innerText,
      trh: ucet.childNodes[2].innerText,
      kurz: ucet.childNodes[3].innerText,
      ucet: ucet.childNodes[4].innerText,
    };
    uctyArray.push(ucetObj);
  }
  console.log(new FormData(e.currentTarget));
  let formular = JSON.stringify(
    Object.fromEntries(new FormData(e.currentTarget)),
  );
  console.log(JSON.stringify({ formular, uctyArray }));
  $.ajax({
    url: `/src/Controllers/aktiva/createNewCp.php`,
    type: "POST",
    dataType: "json",
    processData: false,
    contentType: false,
    data: JSON.stringify({ formular, uctyArray }),
    success: function (data) {
      console.log(data);
    },
    error: function (xhr, status, error) {
      console.error("Error:", error);
      alert("Nepodarilo sa aktualizovať!");
    },
  });
});

$("#editCPForm").on("submit", (e) => {
  e.preventDefault();
  let formData = new URLSearchParams(new FormData(e.currentTarget));
  console.log(formData);
  console.log($("#ucetni").children().length);
  let uctyArray = [];
  for (const ucet of $("#ucetni").children()) {
    console.log(ucet.childNodes[1].innerText);
    const ucetObj = {
      mena: ucet.childNodes[0].innerText,
      kod: ucet.childNodes[1].innerText,
      trh: ucet.childNodes[2].innerText,
      kurz: ucet.childNodes[3].innerText,
      ucet: ucet.childNodes[4].innerText,
    };
    uctyArray.push(ucetObj);
  }
  console.log(new FormData(e.currentTarget));
  let formular = JSON.stringify(
    Object.fromEntries(new FormData(e.currentTarget)),
  );
  console.log(JSON.stringify({ formular, uctyArray }));
  $.ajax({
    url: `/src/Controllers/aktiva/editCp.php`,
    type: "POST",
    dataType: "json",
    processData: false,
    contentType: false,
    data: JSON.stringify({ formular, uctyArray }),
    success: function (data) {
      console.log(data);
    },
    error: function (xhr, status, error) {
      console.error("Error:", error);
      alert("Nepodarilo sa aktualizovať!");
    },
  });
});

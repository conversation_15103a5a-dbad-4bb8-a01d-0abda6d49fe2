$(document).ready(function () {
  $("#archived").on("change", (e) => {
    $("#submitFilterForm").click();
  });

  function delay(callback, ms) {
    var timer = 0;
    return function () {
      var context = this, args = arguments;
      clearTimeout(timer);
      timer = setTimeout(function () {
        callback.apply(context, args);
      }, ms || 0);
    };
  }

  $('#searchBar').keyup(delay(function (e) {
    if (e.currentTarget.value !== "") {
      $("#resetSearch").removeClass("hidden");
    } else {
      $("#resetSearch").addClass("hidden");
    }
    $("#submitFilterForm").click();
  }, 1000));

  $(":checkbox").on("change", () => {
    $("#submitFilterForm").click();
  });

  $("#archiveForm").on("submit", (e) => {
    e.preventDefault();
  });

  $("#archiveBtn").on("click", (e) => {
    document.getElementById("archiveButton").style.display = "none";
    document.getElementById("archiveSpinner").style.display = "block";
  });

  document.addEventListener('htmx:afterSettle', function (evt) {
    document.getElementById("archiveButton").style.display = "block";
    document.getElementById("archiveSpinner").style.display = "none";
  });

  $("#resetSearch").on("click", () => {
    $("#searchBar").val("");
    $("#resetSearch").addClass("hidden");
    $("#submitFilterForm").click();
  });
});
document.addEventListener('htmx:afterSettle', function (evt) {
    document.getElementById("paginationForm").addEventListener("submit", (e) => {
        e.preventDefault();
        console.log(document.getElementById("tabContentResult"));
        const link = e.submitter.value;
        const formData = new FormData(e.currentTarget);
        const page = formData.get("page");
        const filterData = $("#filteredValuesInput").val();
        const paginationRequest = htmx.ajax('POST', link, {
            target: '#tabContentResult',
            source: '#paginationForm',
            values: {
                "data": JSON.stringify(filterData)
            }
        });
        Promise.all([paginationRequest]).then(() => {
            console.log('Both requests completed successfully!');
        })
            .catch((error) => {
                console.error('One or both requests failed:', error);
          });
    });

    $("#feeCreateOpenClient").on("click", (e) => {
        e.stopPropagation();
        $("#dropdownklientCreate").css("display", "block");
    });

    document.getElementById("wholeForm").addEventListener("submit", (e) => {
        e.preventDefault();
        submitFormAndHandleData(e);
    });

    $("#dataToExcelBtn").on("click", () => {
        $("#wholeBodySubmitter").click();
    });

    $(".actionShow").on("click", (e) => {
        e.stopPropagation();
    });

    $(".filterToggler").on("click", (e) => {
        e.stopPropagation();
        setTimeout(() => {
            $(e.currentTarget).attr("hx-disable", "true");
        }, 300);
    });

    $("#killClient").on("submit", (e) => {
        e.preventDefault();
        const formData = new FormData(e.currentTarget);
        const data = formData.get("data");
        htmx.ajax('POST', `/api/transakcie-klienta/change/global/termination`, {
            target: "#terminationBody",
            values: { "data": data }
        }).then((response) => {

        });
    });
});

$(".actionShow").on("click", (e) => {
    e.stopPropagation();
});

function showActionMenu(e) {
    e.parentNode.parentNode.querySelector(".tableCheckboxSelect").click();
    console.log(e.parentNode.childNodes[0].nextSibling)
    e.parentNode.childNodes[0].nextSibling.style.display = "block";
}

$(".filterToggler").on("click", (e) => {
    e.stopPropagation();
    setTimeout(() => {
        $(e.currentTarget).attr("hx-disable", "true");
    }, 300);
});

$(document).click(function (event) {
    var $target = $(event.target);
    if (!$target.closest('.actionConfirm').length) {
        $(".actionConfirm").css("display", "none");
    }
    if (!$target.closest('#dropdownklientCreate').length) {
        $("#dropdownklientCreate").css("display", "none");
    }
});

$(".sortToggler").on("click", (e) => {
    e.stopPropagation();
    $(e.currentTarget).addClass("hidden");
    $(e.currentTarget).parent().siblings(".hiddenSort").val("DESC");
    $(e.currentTarget).siblings(".sortTogglerDESC").removeClass("hidden");
});

$(".sortTogglerDESC").on("click", (e) => {
    e.stopPropagation();
    $(e.currentTarget).addClass("hidden");
    $(e.currentTarget).parent().siblings(".hiddenSort").val("ASC");
    $(e.currentTarget).siblings(".sortTogglerASC").removeClass("hidden");
});

$(".sortTogglerASC").on("click", (e) => {
    e.stopPropagation();
    $(e.currentTarget).addClass("hidden");
    $(e.currentTarget).parent().siblings(".hiddenSort").val("");
    $(e.currentTarget).siblings(".sortToggler").removeClass("hidden");
});

$("#limit").on("change", () => {
    $("#wholeBodySubmitter").click();
});

$("#resetFilter").on("click", (e) => {
    $("#filteredValues").html("");
    $("#wholeForm").trigger("reset");
    $("#filteredValuesInput2").val("");
    document.querySelectorAll(".klientCheckbox").forEach((item) => {
        item.checked = false;
    });
    $("#pageNumber").val("1");
    $("#wholeBodySubmitter").click();
    window.history.pushState("", "", "/poplatky");
    e.currentTarget.classList.add("hidden");
    $("#filteredValues").html("");
});

function submitWholeForm(e) {
    $("#wholeBodySubmitter").click();
}

let checked = [];

function handleCheckboxSelect(e) {
    const checkboxes = document.querySelectorAll(".tableCheckboxSelect");
    let sum;
    if ($("#sumSumaValue").html() === "") {
        sum = 0;
    } else {
        sum = parseFloat($("#sumSumaValue").html());
    }

    checkboxes.forEach((item) => {
        if (item.checked) {
            checked.push(item.closest("tr").querySelector(".reconfirmedColumnData input").value)
        }
    });
    if (e.checked) {
        const menaItem = $(e).closest("tr")[0].querySelector(".menaColumnData input").value;
        const globalMena = $("#sumSumaMena").val();
        console.log(checked);
        const allAreReconfirmed = checked.every((val, i, arr) => val === arr[0]);

        console.log(allAreReconfirmed);
        if (globalMena === "") {
            $("#sumSumaMena").val(menaItem);
        } else {
            if (menaItem !== globalMena) {
                alert("Nemôžete vybrať poplatky s rôznymi menami");
                e.checked = false;
                return;
            }
            if (!allAreReconfirmed) {
                alert("Nemôžete vybrať nerekonfirmované a rekonfirmované poplatky spoločne!");
                checked = [];
                e.checked = false;
                return;
            }
        }
        $(e).closest("tr").removeClass("bg-white dark:bg-gray-800");
        $(e).closest("tr").addClass("bg-gray-300 dark:bg-gray-600");
        const row = $(e).closest("tr");
        let suma = parseFloat(row[0].querySelector(".sumaColumnData input").value);
        sum = sum + suma;
        $("#sumSumaValue").html(sum.toFixed(2));
    } else {
        $("#sumSumaMena").val("");
        const row = $(e).closest("tr");
        let suma = parseFloat(row[0].querySelector(".sumaColumnData input").value);
        sum = sum - suma;
        $("#sumSumaValue").html(sum.toFixed(2));
        $(e).closest("tr").addClass("bg-white dark:bg-gray-800");
        $(e).closest("tr").removeClass("bg-gray-300 dark:bg-gray-600");
    }

    if (checked.length > 0) {
        if (checked.every((val, i, arr) => val === "0")) {
            $("#payment").css("display", "none");
            $("#changeMena").css("display", "none");
            $("#changeSum").css("display", "none");
        } else {
            $("#changeMena").css("display", "inline-flex");
            $("#changeSum").css("display", "inline-flex");
            $("#payment").css("display", "inline-flex");
        }
        checked = [];
        $("#sumSuma").removeClass("hidden");
    } else {
        $("#changeMena").css("display", "inline-flex");
        $("#changeSum").css("display", "inline-flex");
        $("#payment").css("display", "inline-flex");
        $("#sumSuma").addClass("hidden");
    }
}

function exportToExcel(e, action) {
    const formData = new FormData(e.currentTarget);
    const ids = formData.getAll("cislozmluvy");
    const columns = formData.getAll("column");
    document.getElementById("excelIcon").style.display = "none";
    document.getElementById("spinnerExcel").style.display = "block";
    const formDataObj = [];
    for (let i = 0; ids.length > i; i++) {
        formDataObj.push({
            cislozmluvy: formData.getAll("cislozmluvy")[i],
            typ: formData.getAll("typ")[i],
            reconfirmed: formData.getAll("reconfirmed")[i] === 0 ? "Nerekonfirmovaný" : "Rekonfirmovaný",
            datum: formData.getAll("datum")[i],
            isin: formData.getAll("isin")[i],
            suma: formData.getAll("suma")[i],
            mena: formData.getAll("mena")[i],
            cub: formData.getAll("cub")[i] === "" ? "neexistuje" : formData.getAll("cub")[i],
            hotovost: formData.getAll("hotovost")[i] > 0 ? "Má" : "Nemá",
            fondid: formData.getAll("fondid")[i],
            dan: formData.getAll("dan")[i] === "" ? 0 : formData.getAll("dan")[i]
        });

    }

    if (formDataObj.length > 0) {
        $.ajax({
            url: "/src/Controllers/global/exportToExcel.php",
            method: "POST",
            data: JSON.stringify({ "data": formDataObj, "columns": columns }),
            dataType: "json",
            contentType: "application/json",
            success: function (data) {
                console.log(data);
                fetch(data.link.replace("/home/<USER>/www", ""))
                    .then(response => response.blob())
                    .then(blob => {
                        const url = URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = data.link.replace("/home/<USER>/www", "");
                        a.click();
                        URL.revokeObjectURL(url);
                        document.getElementById("excelIcon").style.display = "block";
                        document.getElementById("spinnerExcel").style.display = "none";
                    })
                    .catch(error => {
                        alert('File download failed:' + error);
                        console.error('File download failed:', error);
                    });
            },
            error: function (xhr, status, error) {
                console.error("Error:", error);
            },
        });
    }
}

function getFilteredFilter(whatToGet, target) {
    const filterData = document.getElementById("filteredValuesInput2").value;
    const clientID = document.getElementById("clientID").value;
    htmx.ajax('POST', `/api/transakcie-klienta/get/${whatToGet}`, {
        target: `#${target}`,
        values: {
            "data": filterData,
            "id": clientID
        }
    });
}

function filterTable(e) {
    const formData = new FormData(e.currentTarget);
    const end = formData.get("end");
    const start = formData.get("start");
    const types = formData.getAll("popisFilter");
    const fondids = formData.getAll("fondidFilter");
    const page = formData.get("page");
    const limit = formData.get("limit");
    //SORTING
    const datumSort = formData.get("datumSort");
    let sorting;

    if (datumSort == "") {
        sortingEnabled = false;
    } else {
        sortingEnabled = true
    }

    const filterData = {
        "start": start,
        "end": end,
        "types": types,
        "fondids": fondids
    }

    $("#filteredValuesInput").val(JSON.stringify(filterData));
    $("#filteredValuesInput2").val(JSON.stringify(filterData));

    if (end === "" && start === "" && types.length === 0) {
        $("#resetFilter").addClass("hidden");
    } else {
        $("#resetFilter").removeClass("hidden");

    }

    const filterRequest = htmx.ajax('POST', `/api/transakcie-klienta/get/filteredData`, {
        target: '#poplatkyTBODY',
        values: {
            "data": JSON.stringify(filterData),
            "page": page,
            "datumSort": datumSort,
            "sortingEnabled": sortingEnabled,
            "limit": limit
        }
    });

    const paginationRequest = htmx.ajax('POST', `/api/transakcie-klienta/get/paginationData`, {
        target: '#pagination',
        values: {
            "data": JSON.stringify(filterData),
            "page": page,
            "limit": limit
        }
    });

    Promise.all([filterRequest, paginationRequest]).then(() => {
        setTimeout(() => {
            htmx.ajax('POST', `/api/transakcie-klienta/get/displayFilteredData`, {
                target: '#filteredValues',
                values: {
                    "data": JSON.stringify(filterData),
                    "page": page
                }
            }).then(() => {
            });
            htmx.ajax('POST', `src/Controllers/global/transakcie-klienta/resultCount.php`, {
                target: '#limitSelectWrapper',
                values: {
                    "data": JSON.stringify(filterData),
                    "limit": limit
                }
            });
        }, 500);
        const resultCount = document.querySelector(".totalCountInput");
        if (resultCount !== null) {
            if (resultCount.value <= 30) {
                window.history.pushState('', '', '/poplatky');
                htmx.ajax('POST', `/api/transakcie-klienta/get/paginationData`, {
                    target: '#pagination',
                    values: {
                        "data": JSON.stringify(filterData),
                        "page": 1,
                        "limit": resultCount.value
                    }
                });
            }
            document.getElementById("totalCountStrong").innerHTML = `(${resultCount.value})`;
            document.getElementById("totalCountBottom").innerHTML = resultCount.value;
        } else {
            document.getElementById("totalCountStrong").innerHTML = "(0)";
            document.getElementById("totalCountBottom").innerHTML = "0";
        }
    })
        .catch((error) => {
            console.error('One or both requests failed:', error);
        });
}

function submitFormAndHandleData(e) {
    console.log(e.submitter);
    const action = e.submitter.id;
    switch (action) {
        case "changeMena":
        case "reconfirm":
        case "paymentTrigger":
        case "cancelContract":
        case "changeSum":
            executeAction(e, action === "paymentTrigger" ? "payment" : action);
            break;
        case "dataToExcelBtn":
            exportToExcel(e, "all");
            break;
        default:
            filterTable(e);
            break;
    }
}

document.getElementById("wholeForm").addEventListener("submit", (e) => {
    e.preventDefault();
    submitFormAndHandleData(e);
});

document.getElementById("paginationForm").addEventListener("submit", (e) => {
    e.preventDefault();
    console.log(document.getElementById("tabContentResult"));
    // const link = e.submitter.value;
    // const formData = new FormData(e.currentTarget);
    // const page = formData.get("page");
    // const limit = formData.get("limit");
    // const filterData = $("#filteredValuesInput").val();
    // console.log(filterData, link);
    // const paginationRequest = htmx.ajax('POST', link, {
    //     target: '#tabContentResult',
    //     values: {
    //         "data": JSON.stringify(filterData),
    //         "limit": limit,
    //         "page": page
    //     }
    // });
    // Promise.all([paginationRequest]).then(() => {
    //     console.log('Both requests completed successfully!');
    // })
    //     .catch((error) => {
    //         console.error('One or both requests failed:', error);
    //     });
});
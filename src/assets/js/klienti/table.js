$(document).ready(function () {
    let isArchived = false;
    let filterEnabled = false;
    let searchEntered = false;

    $('#isArchived').on('click', function () {
        $('.archivedCol').removeClass('hidden')
        $('#isArchived').removeClass('hover:bg-gray-100')
        $('#noArchived').removeClass('bg-green-200 font-bold')
        $('#isArchived').addClass('bg-green-200 font-bold')
        $('#noArchived').addClass('hover:-bg-gray-100')
        isArchived = true;
        if (filterEnabled) {
            $('#filterForm').submit();
        } else if (searchEntered) {
            $('#searchFrom').submit();
        } else {
            $.ajax({
                url: '/src/Controllers/klienti/getAllClients.php',
                type: 'GET',
                dataType: 'json',
                success: function (data) {
                    isArchived = true;
                    fillTable(data, true)
                },
                error: function (xhr, status, error) {
                    // Handle errors
                    console.error('Error:', error);
                }
            });
        }
    })
    $('#noArchived').on('click', function () {
        $('.archivedCol').addClass('hidden')
        $('#isArchived').addClass('hover:bg-gray-100')
        $('#noArchived').addClass('bg-green-200 font-bold')
        $('#isArchived').removeClass('bg-green-200 font-bold')
        $('#noArchived').removeClass('hover:-bg-gray-100')
        isArchived = false;
        if (filterEnabled) {
            $('#filterForm').submit();
        } else if (searchEntered) {
            $('#searchFrom').submit();
        } else {
            $.ajax({
                url: '/src/Controllers/klienti/getNonArchivedClients.php',
                type: 'GET',
                dataType: 'json',
                success: function (data) {
                    filterEnabled = true;
                    fillTable(data, false)
                },
                error: function (xhr, status, error) {
                    console.error('Error:', error);
                }
            });
        }
    })

    $('#resetFilter').on('click', () => {
        filterEnabled = false;
        $('#resetFilter').addClass('hidden');
        $('#filterIcon').removeClass('text-blue-500');
        $('#filterIcon').addClass('text-gray-400');
        $('#filterText').text('Filtrovať');
        $('.filter-checkbox').prop('checked', false);
        $('#filterForm').submit();
    })

    $('.filter-checkbox').change(() => {
        $('#filterForm').submit();
        if ($('.filter-checkbox').is(':checked')) {
            filterEnabled = true;
            $('#filterIcon').addClass('text-blue-500');
            $('#filterIcon').removeClass('text-gray-400');
            $('#filterText').text('Filtrované');
            $('#resetFilter').removeClass('hidden');
        } else {
            filterEnabled = false;
            $('#filterIcon').removeClass('text-blue-500');
            $('#filterIcon').addClass('text-gray-400');
            $('#filterText').text('Filtrovať');
            $('#resetFilter').addClass('hidden');
        }
    })

    $('#resetSearch').on('click', () => {
        $('#searchBar').val('');
        $('#resetSearch').addClass('hidden')
        $('#searchFrom').submit();
    })

    let typingTimer;
    const doneTyping = 600;

    $('#searchBar').on('keyup', (e) => {
        if (e.currentTarget.value !== "") {
            $('#resetSearch').removeClass('hidden')
        } else {
            $('#resetSearch').addClass('hidden')
        }
        clearTimeout(typingTimer);
        typingTimer = setTimeout(() => {
            $('#searchFrom').submit();
        }, doneTyping);
    })

    $('#searchBar').on('keydown', () => {
        clearTimeout(typingTimer);
    });

    $('#searchFrom').on('submit', (e) => {
        e.preventDefault();
        const formData = new FormData(e.currentTarget)
        const searchQuery = formData.get("search")
        if (searchQuery !== "") {
            searchEntered = true;
        }
        htmx.ajax('POST', `/src/Controllers/klienti/searchFilter.php`, {
            target: '#klientiTableBody',
            values: {
                "search": searchQuery,
                "isArchived": isArchived
            }
        });
    });

    $('#filterForm').on('submit', (e) => {
        e.preventDefault();
        filterEnabled = true;
        const formData = new FormData(e.currentTarget);
        let groups = [];
        let cities = [];
        for (let entry of formData.entries()) {
            if (entry[0] === 'group') {
                groups.push(entry[1]);
            }
            if (entry[0] === 'city') {
                cities.push(entry[1]);
            }
        }
        $.ajax({
            url: `/src/Controllers/klienti/filterClients.php?group=${JSON.stringify(groups)}&cities=${JSON.stringify(cities)}&isArchived=${isArchived}`,
            type: 'GET',
            dataType: 'json',
            success: function (data) {
                fillTable(data, isArchived)
            },
            error: function (xhr, status, error) {
                console.error('Error:', error);
            }
        });
    })

    function fillTable(data, archived) {
        $('tbody').html('')
        for (let i = 0; i < data.length; i++) {
            const tableTR = $('<tr>', { class: "border-b dark:border-gray-600 hover:bg-gray-100" })
            $(tableTR).append(`<td class="w-4 px-4 py-4">
                                <div class="flex items-center">
                                    <input id="checkbox-table-search-1" type="checkbox"
                                           onclick="event.stopPropagation()"
                                           class="w-4 h-4 bg-gray-100 border-gray-300 rounded text-primary-600 focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                                    <label for="checkbox-table-search-1" class="sr-only">checkbox</label>
                                </div>
                            </td>`)
            $(tableTR).append(`<td class="px-4 py-2">
                                <span class="text-sm text-gray-900 font-bold px-2 py-0.5 rounded">${data[i].titulpred === null ? "" : data[i].titulpred} ${data[i].meno === null ? "" : data[i].meno} ${data[i].prieznaz === null ? "" : data[i].prieznaz} ${data[i].titulza === null ? "" : data[i].titulza}</span>
                            </td>`)
            $(tableTR).append(`<td class="px-4 py-2">
                                <span class="text-sm text-gray-900 font-bold px-2 py-0.5 rounded">${data[i].cislozmluvy}</span>
                            </td>`)
            if (data[i].kontaktcity === "" || data[i].kontaktcity === null) {
                $(tableTR).append(`<td class="px-4 py-2 font-medium text-gray-900 whitespace-nowrap dark:text-white">${data[i].city}</td>`)
            } else {
                $(tableTR).append(`<td class="px-4 py-2 font-medium text-gray-900 whitespace-nowrap dark:text-white">${data[i].kontaktcity}</td>`)
            }

            if (data[i].kontaktaddress === "" || data[i].kontaktaddress === null) {
                $(tableTR).append(`<td class="px-4 py-2 font-medium text-gray-900 whitespace-nowrap dark:text-white">${data[i].address}</td>`)
            } else {
                $(tableTR).append(`<td class="px-4 py-2 font-medium text-gray-900 whitespace-nowrap dark:text-white">${data[i].kontaktaddress}</td>`)
            }

            if (data[i].kontaktnazpriez === "" || data[i].kontaktnazpriez === null) {
                $(tableTR).append(`<td class="px-4 py-2 font-medium text-gray-900 whitespace-nowrap dark:text-white">-</td>`)
            } else {
                $(tableTR).append(`<td class="px-4 py-2 font-medium text-gray-900 whitespace-nowrap dark:text-white">${data[i].kontaktnazpriez}</td>`)
            }

            if (archived) {
                if (data[i].archiv === "t") {
                    $(tableTR).append(`<td class="px-4 py-2 font-medium text-success-900 gap-2 flex items-center justify-center whitespace-nowrap dark:text-white">
                                <svg className="w-5 h-5 text-green-600" aria-hidden="true"  xmlns="http://www.w3.org/2000/svg" width="24"
                                 height="24" fill="currentColor" viewBox="0 0 24 24">
                    <path fill-rule="evenodd"
                          d="M20 10H4v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8ZM9 13v-1h6v1a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1Z"
                          clip-rule="evenodd"/>
                    <path d="M2 6a2 2 0 0 1 2-2h16a2 2 0 1 1 0 4H4a2 2 0 0 1-2-2Z"/>
                </svg><span>Archivovaný</span>
                            </td>`)
                } else {
                    $(tableTR).append('<td></td>')
                }
            }
            $(tableTR).append(`<td class="px-4 py-2 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                <div class="flex items-center gap-4">
                                    <a href="/klienti/detail/${data[i].podielnikid}">
                                        <button data-tooltip-target="view-tooltip"
                                                class="p-2 cursor-pointer hover:bg-gray-300 transition-all rounded-lg">
                                            <svg class="w-5 h-5 text-gray-800 dark:text-white" aria-hidden="true"
                                                 xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none"
                                                 viewBox="0 0 24 24">
                                                <path stroke="currentColor" stroke-width="2"
                                                      d="M21 12c0 1.2-4.03 6-9 6s-9-4.8-9-6c0-1.2 4.03-6 9-6s9 4.8 9 6Z"/>
                                                <path stroke="currentColor" stroke-width="2"
                                                      d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"/>
                                            </svg>
                                        </button>
                                    </a>
                                    <div id="view-tooltip" role="tooltip"
                                         class="absolute z-10 invisible inline-block px-3 py-2 text-sm font-medium text-white transition-opacity duration-300 bg-gray-900 rounded-lg shadow-sm opacity-0 tooltip dark:bg-gray-700">
                                        Zobraziť
                                        <div class="tooltip-arrow" data-popper-arrow></div>
                                    </div>
                                    <a href="/klienti/detail/${data[i].podielnikid}">
                                        <button data-tooltip-target="edit-tooltip"
                                                class="p-2 cursor-pointer hover:bg-gray-300 transition-all rounded-lg">
                                            <svg class="w-5 h-5 text-gray-800 dark:text-white" aria-hidden="true"
                                                 xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                 fill="currentColor"
                                                 viewBox="0 0 24 24">
                                                <path fill-rule="evenodd"
                                                      d="M5 8a4 4 0 1 1 7.796 1.263l-2.533 2.534A4 4 0 0 1 5 8Zm4.06 5H7a4 4 0 0 0-4 4v1a2 2 0 0 0 2 2h2.172a2.999 2.999 0 0 1-.114-1.588l.674-3.372a3 3 0 0 1 .82-1.533L9.06 13Zm9.032-5a2.907 2.907 0 0 0-2.056.852L9.967 14.92a1 1 0 0 0-.273.51l-.675 3.373a1 1 0 0 0 1.177 1.177l3.372-.675a1 1 0 0 0 .511-.273l6.07-6.07a2.91 2.91 0 0 0-.944-4.742A2.907 2.907 0 0 0 18.092 8Z"
                                                      clip-rule="evenodd"/>
                                            </svg>
                                        </button>
                                    </a>
                                    <div id="edit-tooltip" role="tooltip"
                                         class="absolute z-10 invisible inline-block px-3 py-2 text-sm font-medium text-white transition-opacity duration-300 bg-gray-900 rounded-lg shadow-sm opacity-0 tooltip dark:bg-gray-700">
                                        Upraviť
                                        <div class="tooltip-arrow" data-popper-arrow></div>
                                    </div>
                                    <button data-tooltip-target="archive-tooltip"
                                            class="p-2 cursor-pointer hover:bg-gray-300 transition-all rounded-lg">
                                        <svg class="w-5 h-5 text-gray-800 dark:text-white" aria-hidden="true"
                                             xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none"
                                             viewBox="0 0 24 24">
                                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                                  stroke-width="2"
                                                  d="M12 11v5m0 0 2-2m-2 2-2-2M3 6v1a1 1 0 0 0 1 1h16a1 1 0 0 0 1-1V6a1 1 0 0 0-1-1H4a1 1 0 0 0-1 1Zm2 2v10a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V8H5Z"/>
                                        </svg>
                                    </button>
                                    <div id="archive-tooltip" role="tooltip"
                                         class="absolute z-40 invisible inline-block px-3 py-2 text-sm font-medium text-white transition-opacity duration-300 bg-gray-900 rounded-lg shadow-sm opacity-0 tooltip dark:bg-gray-700">
                                        Archivovať
                                        <div class="tooltip-arrow" data-popper-arrow></div>
                                    </div>
                                </div>
                            </td>`)
            $(tableTR).append(``)
            $('tbody').append(tableTR)
        }
    }
});
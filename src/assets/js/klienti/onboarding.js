$(document).ready(function() {
  $('#getOnboardingData').on("change", (e) => {
    let select = $('#getOnboardingData');
    let valueObject = JSON.parse(e.target.value);
    console.log(valueObject.email);
    $.ajax({
      url: `/src/Controllers/klienti/getOnboardingData.php?onboardingID=${valueObject.onboarding_id}`,
      type: 'GET',
      dataType: 'json',
      success: function(data) {
        select.removeClass("bg-gray-50");
        select.addClass("bg-blue-500 text-white font-bold");
        fillForm(data[0], valueObject);
      },
      error: function(xhr, status, error) {
        console.error('Error:', error);
        alert('Nepodarilo sa aktualizovať!')
      }
    });
  })

  $('#newMoneyAccount').on("submit", (e) => {
    e.preventDefault();
    $("#accountsErrorMsg").addClass("hidden");
    $("#addAnother").removeClass("hidden");
    let accountsData = {
      iban: e.currentTarget.iban.value,
      mena: e.currentTarget.mena.value,
      swift: e.currentTarget.swift.value,
    }
    $("#accountsData").val(JSON.stringify(accountsData));;
    let deactivateBtn = `<div id="view-tooltip" role="tooltip"
                                         class="absolute z-10 invisible inline-block px-3 py-2 text-sm font-medium text-white transition-opacity duration-300 bg-gray-900 rounded-lg shadow-sm opacity-0 tooltip dark:bg-gray-700">
                                        Deaktivovať
                                        <div class="tooltip-arrow" data-popper-arrow></div>
                                    </div>
                                        <button data-tooltip-target="view-tooltip" id="accountDeactivateBtn" type="button"
                                                class="p-2 cursor-pointer hover:bg-gray-300 transition-all rounded-lg">
                                           <svg class="w-6 h-6 text-red-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
  <path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="m6 6 12 12m3-6a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"/>
</svg> 
                                         </button>`;
    const valuesForTable = [e.currentTarget.iban.value, e.currentTarget.mena.value, e.currentTarget.swift.value, new Date().toString().substring(0, new Date().toString().indexOf("GMT")), new Date("2100-05-16").toString().substring(0, new Date("2100-05-16").toString().indexOf("GMT")), deactivateBtn];
    const tr = $("<tr>");  
    $("#accountsTable").removeClass("hidden");
    $("#accountButton").addClass("hidden");
    $("#accountsBody").append(tr);
    for(let i = 0; i < valuesForTable.length; i++){
      let td = $(`<td></td>`);
      td[0].innerText = valuesForTable[i];
      td[0].setAttribute("class", "text-center px-8 py-2");
      $(tr).append(`<td class="text-center px-8 py-2">${valuesForTable[i]}</td>`);
    }
    $("#newMoneyAccount").trigger("reset");
  })

  $("#fpo").on("change", (e) => {
    let value = e.target.value
    console.log(typeof value);
    console.log(value);
    if (value === "0"){
      $("#sektor_esa95").val(13).addClass("bg-green-200 border border-gray-500").prop('readonly', true);
      $("#pohlavieWrapper").show();
      $("#rcicoWrapper").show();
      $("#datum_narodeniaWrapper").show();
      $("#cisloidWrapper").show();
      $("#prieznazSection").show();
      $("#menoSection").show();
      $("#titulSection").show();      
      $("#kontaktnaOsoba").hide();
      $("#foInputs").hide();
      $("#ico").prop("required", false);      
      $("#typzdanenia").removeClass("bg-green-200 border border-gray-500").prop("readonly", false).prop("selectedIndex", 0);
    }
    if (value !== "0"){
      $("#sektor_esa95").removeClass("bg-green-200 border border-gray-500").prop('readonly', false).prop('selectedIndex', 0);
      $("#pohlavieWrapper").hide();
      $("#rcicoWrapper").hide();
      $("#datum_narodeniaWrapper").hide();
      $("#cisloidWrapper").hide();
      $("#prieznazSection").hide();
      $("#menoSection").hide();
      $("#titulSection").hide();
      $("#kontaktnaOsoba").show();
      $("#foInputs").show();
      $("#ico").prop("required", true);
      $("#typzdanenia").val("PO").addClass("bg-green-200 border border-gray-500").prop("readonly", true);
    }
  })

  function fillForm(data, selectData) {
    console.log(data);
    $('#prieznaz').val(data["last_name"]).addClass("bg-green-200 border border-gray-500").prop('readonly', true);
    $('#meno').val(data["first_name"]).addClass("bg-green-200 border border-gray-500").prop('readonly', true);
    $("#sektor_esa95").addClass("bg-green-200 border border-gray-500");
    $("#sektor_esa95 option").each(function() {
      if($(this).val() === "13"){
        $(this).prop("selected", true);
      } else {
        $(this).prop("disabled", true)
      }
      console.log($(this).val())
    })    
    $('#fpo').val(0).addClass("bg-green-200 border border-gray-500").prop('readonly', true);
    $('#druhid').val(0).addClass("bg-green-200 border border-gray-500").prop('readonly', true);
    $('#typklienta').val(2).addClass("bg-green-200 border border-gray-500").prop('readonly', true);
    $('#elektronickyOnboarding').show();
    if (data["name_prefix"] !== null) 
      $('#titulpred').val(data["name_prefix"]).addClass("bg-green-200 border border-gray-500").prop('readonly', true);
    if (data["identification_number"] !== null)
      $('#rcico').val(data["identification_number"]).addClass("bg-green-200 border border-gray-500").prop('readonly', true);
    if (data["date_of_birth"] !== null)
      $('#datum_narodenia').val(data["date_of_birth"]).addClass("bg-green-200 border border-gray-500").prop('readonly', true);
    if (data["id_card_number"] !== null)
      $('#cisloid').val(data["id_card_number"]).addClass("bg-green-200 border border-gray-500").prop('readonly', true);
    if (data["residence_address"] !== null)
      $('#address').val(data["residence_address"]).addClass("bg-green-200 border border-gray-500").prop('readonly', true);
    if (data["residence_city"] !== null)
      $('#city').val(data["residence_city"]).addClass("bg-green-200 border border-gray-500").prop('readonly', true);
    if (selectData.email !== null)
      $('#kontaktemail').val(selectData.email).addClass("bg-green-200 border border-gray-500").prop('readonly', true);   
    if (data["residence_postalcode"] !== null)
      $('#postalcode').val(data["residence_postcode"]).addClass("bg-green-200 border border-gray-500").prop('readonly', true);
    if (data["phone"] !== null)
      $('#kontaktphonenumber').val(data["phone"]).addClass("bg-green-200 border border-gray-500").prop('readonly', true);
    if(data["id_onboarding"] !== null)
      $('#onboarding_id').val(data["id_onboarding"]).addClass("bg-green-200 border border-gray-500").prop('readonly', true);
    if(selectData.email_verified_at !== null)
      $('#email_verified_at').val(selectData.email_verified_at).addClass("bg-green-200 border border-gray-500").prop('readonly', true);
    if(selectData.created_at !== null)
      $('#created_at').val(selectData.created_at).addClass("bg-green-200 border border-gray-500").prop('readonly', true);
    if(selectData.id != null)
      $('#vs_id').val(selectData.id).addClass("bg-green-200 border border-gray-500").prop('readonly', true);
    if(data["pre_contractual_conditions"] !== null)
      $('#pre_contractual_conditions').val(data["pre_contractual_conditions"]).addClass("bg-green-200 border border-gray-500").prop("readonly", true);
    if(data["general_conditions"] !== null){
      $('#general_conditions').val(data["general_conditions"]).addClass("bg-green-200 border border-gray-500").prop("readonly", true);
      let timeArr = data["general_conditions"].split(" ");
      $('#datetimeagreement').val(timeArr[0]).addClass("bg-green-100 border border-gray-500");
      $('#timeagreement').val(timeArr[1].replace(/:\d{2}$/, '')).addClass("bg-green-100 border border-gray-500");
    }
    $("#addAnother").removeClass("hidden");
    let deactivateBtn = `<div id="view-tooltip" role="tooltip"
                                         class="absolute z-10 invisible inline-block px-3 py-2 text-sm font-medium text-white transition-opacity duration-300 bg-gray-900 rounded-lg shadow-sm opacity-0 tooltip dark:bg-gray-700">
                                        Deaktivovať
                                        <div class="tooltip-arrow" data-popper-arrow></div>
                                    </div>
                                        <button data-tooltip-target="view-tooltip" id="accountDeactivateBtn" type="button"
                                                class="p-2 cursor-pointer hover:bg-gray-300 transition-all rounded-lg">
                                           <svg class="w-6 h-6 text-red-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
  <path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="m6 6 12 12m3-6a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"/>
</svg> 
                                        </button>`;
    const valuesForTable = [data["bank_account_iban"], data["bank_account_currency"], data["bank_account_swift"], data["general_conditions"], new Date("2100-05-16").toString().substring(0, new Date("2100-05-16").toString().indexOf("GMT")), deactivateBtn];
    const tr = $("<tr>");  
    $("#accountsTable").removeClass("hidden");
    $("#accountButton").addClass("hidden");
    $("#accountsBody").append(tr);
    for(let i = 0; i < valuesForTable.length; i++){
      console.log(valuesForTable[i]);
      let td = $(`<td></td>`);
      td[0].innerText = valuesForTable[i];
      td[0].setAttribute("class", "text-center px-8 py-2");
      $(tr).append(`<td class="text-center px-8 py-2">${valuesForTable[i]}</td>`);
    }
  }
});

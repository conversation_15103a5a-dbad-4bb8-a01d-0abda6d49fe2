$("#clientUpdate").submit(function (e) {
  e.preventDefault();
  const formData = new FormData(e.currentTarget);
  console.log(formData);
  const values = $(this).serialize();

  $.ajax({
    url: `/src/Controllers/klienti/updateClient.php`,
    type: "POST",
    dataType: "json",
    data: values,
    success: function (data) {
      console.log(data);
      alert("Úspešne aktualizované");
    },
    error: function (xhr, status, error) {
      console.error("Error:", error);
      alert("Nepodarilo sa aktualizovať!");
    },
  });
});
$("#vyberPortfolio").submit(function (e) {
  e.preventDefault();
  let fondid = e.currentTarget.template.value;
  let datetik = e.currentTarget.portfolioDate.value;
  console.log(datetik);
  const currentDate = new Date();
  const timezoneOffsetInMinutes = currentDate.getTimezoneOffset();
  const adjustedDateTime = new Date(
    currentDate.getTime() - timezoneOffsetInMinutes * 60 * 1000,
  );
  const actualDate = adjustedDateTime
    .toISOString()
    .slice(0, 19)
    .replace("T", " ");
  console.log(actualDate);
  let values = { fondid: fondid };
  $.ajax({
    url: `/src/Controllers/klienti/loadFondTemplate.php`,
    type: "POST",
    dataType: "json",
    data: values,
    success: function (data) {
      console.log(data);
      $(".inputFond").addClass("bg-yellow-100");
      $("#status").val(data.value.archiv === "f" ? "Aktívny" : "Neaktívny");
      $("#nazov").val(data.value.fondnameall ? data.value.fondnameall : "");
      $("#skratka").val(
        data.value.fondnameshort ? data.value.fondnameshort : "",
      );
      $("#dateOfStart").val(actualDate.split(" ")[0]);
      $("#mena").val(data.value.refmena);
      $("#typPredlohy").val(data.value.typpredlohy);
      $("#uhradaPoplatkov").val(data.value.uhrada_poplatkov);
      console.log(data.ucty.length);
      if (data.ucty.length !== 0) {
        $("#penazneUctyTable").removeClass("hidden");
        $("#penazneUcty").html("");
        $("#penazneUctyNotice").addClass("hidden");
        for (let i = 0; i < data.ucty.length; i++) {
          const tableTR = $("<tr>", {
            class: "border-b dark:border-gray-600 hover:bg-gray-100",
          });
          $(tableTR).append(`<td class="px-6 py-4">${data.ucty[i].cub}</td>`);
          $(tableTR).append(`<td class="px-6 py-4">${data.ucty[i].mena}</td>`);
          $(tableTR).append(`<td class="px-6 py-4">${data.ucty[i].banka}</td>`);
          $(tableTR).append(
            `<td class="text-center"><button class="underline text-red-500">Odstrániť</button></td>`,
          );
          $("#penazneUcty").append(tableTR);
        }
      }
      if (data.mucty.length !== 0) {
        $("#majetkoveUcty").html("");
        $("#majetkoveUctyTable").removeClass("hidden");
        $("#majetkoveUctyNotice").addClass("hidden");
        for (let i = 0; i < data.ucty.length; i++) {
          const tableTR = $("<tr>", {
            class: "border-b dark:border-gray-600 hover:bg-gray-100",
          });
          $(tableTR).append(`<td class="px-6 py-4">${data.mucty[i].cum}</td>`);
          $(tableTR).append(`<td class="px-6 py-4">${data.mucty[i].dest}</td>`);
          $(tableTR).append(
            `<td class="px-6 py-4">${data.mucty[i].typ_maj_uctu}</td>`,
          );
          $(tableTR).append(
            `<td class="text-center"><button data-modal-target="createPenaznyUcetModal" data-modal-toggle="createPenaznyUcetModal" class="underline text-red-500">Odstrániť</button></td>`,
          );
          $("#majetkoveUcty").append(tableTR);
        }
      }
    },
    error: function (xhr, status, error) {
      console.error("Error:", error);
      alert("Nepodarilo sa aktualizovať!");
    },
  });
});
$(".inputFond").on("change", (e) => {
  $(e.target).removeClass("bg-yellow-100");
});

function fillTable(data, table) {}

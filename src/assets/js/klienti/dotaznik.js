$(document).ready(function () {
  $("#fileToUpload").on("change", (e) => {
    const clientID = e.currentTarget.files[0];
    console.log(clientID);
    $("#futureFile").addClass("font-bold");
    $("#pdfIcon").removeClass("hidden");
    $("#uploadIcon").addClass("hidden");
    $("#futureFile").html("Zvolený súbor: " + clientID.name);
    $("#deleteText").html("");
  });

  $("#dotaznickovyForm").on("submit", (e) => {
    e.preventDefault();
    let file = e.target.fileToUpload.files[0];
    let date = e.target.date.value;
    let user_id = e.target.user_id.value;
    let shouldRedirect = e.target.redirect.value;

    // Create a FormData object to store the file and other data
    let formData = new FormData();
    formData.append("fileToUpload", file);
    formData.append("date", date);
    formData.append("user_id", user_id);

    $.ajax({
      url: `/src/Controllers/klienti/uploadDotaznikToFileSystem.php`,
      type: "POST",
      contentType: false,
      processData: false,
      dataType: "json",
      data: formData,
      success: function (data) {
        if (data.error === false) {
          console.log(shouldRedirect);
          if (shouldRedirect === "true") {
            window.location = "/klienti/create-new/final-step/" + user_id;
          } else {
            $("#add-modal").hide();
          }
        } else {
          $("#pdfUploadError").html(data.errorMsg);
          $("#labelFileToUpload").addClass("border-red-500");
        }
        console.log(data);
      },
      error: function (xhr, status, error) {
        console.error("Error:", error);
        alert("Nepodarilo sa aktualizovať!");
      },
    });
  });
  $("#getpdf").on("click", () => {
    let clientID = $("#clientIDText").html();
    console.log(
      `/src/Controllers/klienti/getAndShowPDF.php?clientID=${clientID}`,
    );
    $.ajax({
      url: `/src/Controllers/klienti/getAndShowPDF.php?clientID=${clientID}`,
      type: "GET",
      success: function (data) {
        console.log(data);
        if (data.error) {
          $("#pdfErrorWrapper").removeClass("hidden");
          $("#pdfError").html(data.msg);
        } else {
          window.location = `/src/Controllers/klienti/getAndShowPDF.php?clientID=${clientID}`;
        }
      },
      error: function (xhr, status, error) {
        console.error("Error:", error);
        alert("Nepodarilo sa aktualizovať!");
      },
    });
  });
});

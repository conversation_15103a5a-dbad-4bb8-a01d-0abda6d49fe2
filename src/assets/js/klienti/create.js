$(document).ready(function () {
  $("#clientCreate").on("submit", (e) => {
    e.preventDefault();
    let formData = new URLSearchParams(new FormData(e.currentTarget));
    console.log(formData);
    let accountsData = formData.get("accountsData");
    console.log(accountsData);
    $.ajax({
      url: `/src/Controllers/klienti/createNewClient.php`,
      type: "POST",
      dataType: "json",
      processData: false,
      contentType: false,
      data: formData,
      success: function (data) {
        if (data.error === false) {
          window.location = "/klienti/create-new/step-2/" + data.userid;
        }
        console.log(data);
      },
      error: function (xhr, status, error) {
        console.error("Error:", error);
        alert("Nepodarilo sa aktualizovať!");
      },
    });
  });
});

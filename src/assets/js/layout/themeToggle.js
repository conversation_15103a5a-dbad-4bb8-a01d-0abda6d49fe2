// Theme toggle functionality that works with HTMX
window.themeToggle = {
    // Initialize theme on page load
    init: function() {
        this.updateThemeIcons();
        this.setupGlobalClickHandler();
    },

    // Update icons based on current theme
    updateThemeIcons: function() {
        var themeToggleDarkIcon = document.getElementById("theme-toggle-dark-icon");
        var themeToggleLightIcon = document.getElementById("theme-toggle-light-icon");

        if (!themeToggleDarkIcon || !themeToggleLightIcon) return;

        // Change the icons inside the button based on previous settings
        if (
            localStorage.getItem("color-theme") === "dark" ||
            (!("color-theme" in localStorage) &&
                window.matchMedia("(prefers-color-scheme: dark)").matches)
        ) {
            themeToggleLightIcon.classList.remove("hidden");
            themeToggleDarkIcon.classList.add("hidden");
        } else {
            themeToggleDarkIcon.classList.remove("hidden");
            themeToggleLightIcon.classList.add("hidden");
        }
    },

    // Setup global click handler using event delegation
    setupGlobalClickHandler: function() {
        // Remove any existing onclick handlers to avoid duplicates
        var existingHandler = document.onclick;

        document.onclick = function(event) {
            // Call existing handler if it exists
            if (existingHandler && typeof existingHandler === 'function') {
                existingHandler.call(this, event);
            }

            // Check if clicked element is the theme toggle button
            if (event.target.id === 'theme-toggle' || event.target.closest('#theme-toggle')) {
                window.themeToggle.handleToggle();
            }
        };
    },

    // Handle theme toggle logic
    handleToggle: function() {
        var themeToggleDarkIcon = document.getElementById("theme-toggle-dark-icon");
        var themeToggleLightIcon = document.getElementById("theme-toggle-light-icon");

        if (!themeToggleDarkIcon || !themeToggleLightIcon) return;

        // Toggle icons inside button
        themeToggleDarkIcon.classList.toggle("hidden");
        themeToggleLightIcon.classList.toggle("hidden");

        // If set via local storage previously
        if (localStorage.getItem("color-theme")) {
            if (localStorage.getItem("color-theme") === "light") {
                document.documentElement.classList.add("dark");
                localStorage.setItem("color-theme", "dark");
            } else {
                document.documentElement.classList.remove("dark");
                localStorage.setItem("color-theme", "light");
            }
        } else {
            // If NOT set via local storage previously
            if (document.documentElement.classList.contains("dark")) {
                document.documentElement.classList.remove("dark");
                localStorage.setItem("color-theme", "light");
            } else {
                document.documentElement.classList.add("dark");
                localStorage.setItem("color-theme", "dark");
            }
        }
    }
};

// Initialize on page load
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function() {
        window.themeToggle.init();
    });
} else {
    window.themeToggle.init();
}

// Re-initialize after HTMX requests
document.body.addEventListener('htmx:afterSettle', function() {
    window.themeToggle.updateThemeIcons();
});


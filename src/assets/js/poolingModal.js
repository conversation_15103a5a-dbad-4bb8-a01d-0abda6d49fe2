$("#minimizeModal").on("click", (e) => {
    $("#modalWrapperInside").css("position", "absolute");
    $("#modalWrapperInside").animate({
        width: "20vw",
        bottom: "-1.5rem",
        right: "3rem"
    });
    $("#pooling-modal").css("height", "0");
    $("#poolingBtn").prop("disabled", true);
    $("#pooling-modal").removeClass("fixed");
    $("#minimizeModal").addClass("hidden");
    $("#maximizeModal").removeClass("hidden");
    $("#poolDetailForm").hide();
});

$("#maximizeModal").on("click", (e) => {
    $("#modalWrapperInside").css("position", "unset");
    $("#modalWrapperInside").animate({
        width: "100%",
        bottom: "unset",
        right: "unset"
    });
    $("#pooling-modal").css("height", "100vh");
    $("#pooling-modal").addClass("fixed");
    $("#poolingBtn").prop("disabled", false);
    $("#minimizeModal").removeClass("hidden");
    $("#maximizeModal").addClass("hidden");
    $("#poolDetailForm").show();
});

$("#confirmPoolButton").on("click", () => {
    $(".primaryBtnSpinner").css("display", "inline-flex");
    $("#confirmPoolButton span").text("Potvrdzujem...");
    setTimeout(() => {
        $(".primaryBtnSpinner").css("display", "none");
        $("#confirmPoolButton span").text("Potvrdiť pool");
    }, 1000);
})
let form = document.getElementById("loginForm");
form.addEventListener("submit", (e) => {
    e.preventDefault();
    document.getElementById("loading").style.display = "flex";
    document.getElementById("loginFormWrapper").style.display = "none";
    let username = form.querySelector("input[type = text]").value;
    let pass = form.querySelector("input[type = password]").value;
    let postData = {
        username: username,
        pass: pass
    };
    let formData = new FormData();
    for (let key in postData) {
        formData.append(key, postData[key]);
    }
    if(pass !== "" && username !== ""){
        fetch('src/Controllers/login/loginValidation.php', {
            method: 'POST',
            body: formData
        })
            .then(response => response.text())
            .then(data => {
                console.log(data);
                if(data === "true"){
                    window.location.href = "/dashboard";
                } else {
                    document.getElementById("loading").style.display = "none";
                    document.getElementById("loginFormWrapper").style.display = "block";
                    document.getElementById("loginAlert").innerHTML = data;
                    document.getElementById("loginAlert").style.display = "block";
                }
            })
            .catch(error => {
                console.error(error);
            });
    }
    form.reset();
})
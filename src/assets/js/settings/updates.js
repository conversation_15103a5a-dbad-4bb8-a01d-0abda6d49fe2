document.querySelectorAll(".updateForm").forEach((item) => {
    item.addEventListener("submit", (e) => {
        e.preventDefault();
        item.querySelector("#spinnerUpdate").style.display = "block";
        let hidden = item.querySelector(".hiddenOne").name;
        item.querySelector("#updateButton > span").innerHTML = "Aktualizujem...";
        const formData = JSON.stringify($(item).serializeArray());
        $.ajax({
            url: `/src/Controllers/settings/updates/update.php`,
            method: "POST",
            data: JSON.stringify({"columns": formData, "table": e.target.id, "hidden": hidden}),
            processData: false,
            contentType: false,
            success: function (data) {
                console.log(data);
                item.querySelector("#responseMsg").innerHTML = data.msg;
                item.querySelector("#responseMsg").style.display = "block";
                if (data.error) {
                    item.querySelector("#responseMsg").classList.add("bg-red-500");
                } else {
                    item.querySelector("#responseMsg").classList.add("bg-green-500");
                }
                item.querySelector("#spinnerUpdate").style.display = "none";
                item.querySelector("#updateButton > span").innerHTML = "Aktualizovať";
                if (!data.error) {
                    window.location.reload();
                }
            },
            error: function (xhr, status, error) {
                console.error("Error:", error);
            },
        });
    });
});

document.querySelector(".createForm").addEventListener("submit", (e) => {
    e.preventDefault();
    e.target.querySelector("#spinnerUpdate").style.display = "block";
    e.target.querySelector("#updateButton > span").innerHTML = "Vytváram...";
    let hidden = e.target.querySelector(".hiddenOne").name;
    const formData = JSON.stringify($(e.target).serializeArray());
    console.log(formData);
    $.ajax({
        url: `/src/Controllers/settings/creation/create.php`,
        method: "POST",
        data: JSON.stringify({"columns": formData, "table": e.target.id, "hidden": hidden}),
        processData: false,
        contentType: false,
        success: function (data) {
            console.log(data);
            e.target.querySelector("#responseMsg > span").innerHTML = data.msg;
            e.target.querySelector("#responseMsg").style.display = "block";
            if (data.error) {
                e.target.querySelector("#responseMsg").classList.add("bg-red-50");
                e.target.querySelector("#responseMsg").classList.add("text-red-800");
            } else {
                e.target.querySelector("#responseMsg").classList.add("bg-green-50");
                e.target.querySelector("#responseMsg").classList.add("text-green-800");
            }
            e.target.querySelector("#spinnerUpdate").style.display = "none";
            e.target.querySelector("#updateButton > span").innerHTML = "Vytvoriť";
            /*if (!data.error) {
                window.location.reload();
            }*/
        },
        error: function (xhr, status, error) {
            console.error("Error:", error);
        },
    });
});

document.querySelectorAll(".deleteForm").forEach((item) => {
    item.addEventListener("submit", (e) => {
        e.preventDefault();
        item.querySelector("#spinnerUpdate").style.display = "block";
        item.querySelector("#deleteButton > span").innerHTML = "Odstraňujem...";
        let hidden = item.querySelector(".hiddenOne").name;
        const formData = JSON.stringify($(item).serializeArray());
        $.ajax({
            url: `/src/Controllers/settings/deletes/delete.php`,
            method: "POST",
            data: JSON.stringify({"columns": formData, "table": e.target.id, "hidden": hidden}),
            processData: false,
            contentType: false,
            success: function (data) {
                console.log(data);
                item.querySelector("#responseMsg").innerHTML = data.msg;
                item.querySelector("#responseMsg").style.display = "block";
                if (data.error) {
                    item.querySelector("#responseMsg").classList.add("bg-red-500");
                } else {
                    item.querySelector("#responseMsg").classList.add("bg-green-500");
                }
                item.querySelector("#spinnerUpdate").style.display = "none";
                item.querySelector("#deleteButton > span").innerHTML = "Odstrániť";
                if (!data.error) {
                    window.location.reload();
                }
            },
            error: function (xhr, status, error) {
                console.error("Error:", error);
            },
        });
    });
});

document.getElementById("logoutAll").addEventListener("click", (e) => {
    document.querySelector("#logoutAllSpinner").style.display = "block";
    document.getElementById("logoutAllIcon").style.display = "none";
    $.ajax({
        url: `/src/Controllers/settings/actions/logoutAll.php`,
        data: JSON.stringify({"userid": userid}),
        method: "POST",
        processData: false,
        contentType: false,
        success: function (data) {
            console.log(data);
            document.getElementById("logoutAllSpinner").style.display = "none";
            document.getElementById("logoutAllIcon").style.display = "block";
            /*if (!data.error) {
                window.location.reload();
            }*/
        },
        error: function (xhr, status, error) {
            console.error("Error:", error);
        },
    });
});

document.querySelector(".killAllForm").addEventListener("submit", (e) => {
    e.preventDefault();
    let userid = e.target.userid.value;
    let isForcedLogout = e.target.isForcedLogout.value;
    console.log(isForcedLogout);
    document.querySelector("#killAllSpinner").style.display = "block";
    document.getElementById("killAllIcon").style.display = "none";
    document.getElementById("unlockAllIcon").style.display = "none";
    $.ajax({
        url: `/src/Controllers/settings/actions/killAll.php`,
        data: JSON.stringify({"userid": userid, "logoutBool": isForcedLogout}),
        method: "POST",
        processData: false,
        contentType: false,
        success: function (data) {
            console.log(data);
            document.getElementById("killAllSpinner").style.display = "none";
            if(isForcedLogout === "true"){
                document.getElementById("killAllIcon").style.display = "block";
                document.getElementById("isForcedLogout").value = "false";
            } else {
                document.getElementById("unlockAllIcon").style.display = "block";
                document.getElementById("isForcedLogout").value = "true";
            }
        },
        error: function (xhr, status, error) {
            console.error("Error:", error);
        },
    });
});
let form = document.getElementById("resetForm");
form.addEventListener("submit", (e) => {
    e.preventDefault();
    let formData = new FormData(e.currentTarget);
    console.log(formData);
    if(username !== ""){
        fetch('src/Controllers/login/passwordForgor.php', {
            method: 'POST',
            body: formData
        })
            .then(response => response.text())
            .then(data => {
                console.log(data);
                if(data === "true"){
                    document.getElementById("successAlertspan").innerHTML = "Email bol úspšene odoslaný, skontrolujte si svoju emailovú schránku";
                    document.getElementById("successAlert").style.display = "block";
                } else {
                    document.getElementById("loginAlert").innerHTML = "Email sa nepodarilo odoslať, skúste to znova.";
                    document.getElementById("loginAlert").style.display = "block";
                }
            })
            .catch(error => {
                console.error(error);
            });
    }
    //form.reset();
})
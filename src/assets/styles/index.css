@tailwind base;
@tailwind components;
@tailwind utilities;
@import url('https://fonts.googleapis.com/css2?family=Geist+Mono:wght@100..900&display=swap');

body {
    font-family: "Geist Mono", monospace;
}

html {
    scroll-behavior: smooth;
}

/*body h1,h2,h3,h4,h5,h6,p,span,small,a,img,svg,i,input,td {
    animation: spin 1s linear infinite;
[
    @keyframes spin {
        from {[F
            transform: rotate(0deg);
        }
        to {
            transform: rotate(360deg);
        }
    }
}*/

.htmx-indicator {
    opacity: 0;
    transition: opacity 500ms ease-in;
}

.htmx-request .htmx-indicator {
    opacity: 1;
}

.htmx-request.htmx-indicator {
    opacity: 1;
}

.file-upload-wrapper {
    position: relative;
    display: inline-block;
}

.file-upload-button {
    background-color: #007bff;
    color: white;
    padding: 10px 20px;
    font-size: 16px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
}

.file-upload-input {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

#appWrapper {
    background: linear-gradient(120deg, #fdfbfb 0%, #ebedee 100%);
}

#loading-contianer {
    display: none;
}

#confirm-contianer {
    display: none;
}

.sub {
    margin-left: 1rem;
    padding: 4px 0.4rem 4px 1rem;
}

.sub2 {
    margin-left: 2rem;
    padding: 4px 1rem;
}

[data-tooltip-target]:hover+#view-tooltip {
    opacity: 1;
    visibility: visible;
    margin-top: -5rem;
    margin-left: -1.2rem;
}

[data-tooltip-target]:hover+#edit-tooltip {
    opacity: 1;
    visibility: visible;
    margin-top: -5rem;
    margin-left: 2.2rem;
}

[data-tooltip-target]:hover+#archive-tooltip {
    opacity: 1;
    visibility: visible;
    margin-top: -5rem;
    margin-left: 4.8rem;
}

.sub-category {
    margin-left: 2rem;
    color: #434650;
    font-weight: bold;
    font-size: 14px;
}

#notification-dot {
    position: absolute;
    background: red;
    font-size: 13px;
    font-weight: bold;
    color: white;
    border-radius: 50%;
    min-height: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    top: 0;
    right: 0;
    padding: 0px 5px;
    line-height: 16px;
}

.main-container {
    width: 100%;
    height: 100vh;
    display: flex;
    flex-flow: column;
    justify-content: center;
    align-items: center;
}

.check-container {
    width: 6.25rem;
    height: 7.5rem;
    display: flex;
    flex-flow: column;
    align-items: center;
    justify-content: space-between;
}

.check-container .check-background {
    width: 100%;
    height: calc(100% - 1.25rem);
    background: linear-gradient(to bottom right, #5de593, #41d67c);
    box-shadow: 0px 0px 0px 65px rgba(255, 255, 255, 0.25) inset,
        0px 0px 0px 65px rgba(255, 255, 255, 0.25) inset;
    transform: scale(0.84);
    border-radius: 50%;
    animation: animateContainer 0.75s ease-out forwards 0.75s;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
}

.check-container .check-background svg {
    width: 65%;
    transform: translateY(0.25rem);
    stroke-dasharray: 80;
    stroke-dashoffset: 80;
    animation: animateCheck 0.35s forwards 1.25s ease-out;
}

.check-container .check-shadow {
    bottom: calc(-15% - 5px);
    left: 0;
    border-radius: 50%;
    background: radial-gradient(closest-side, #49da83, transparent);
    animation: animateShadow 0.75s ease-out forwards 0.75s;
}

@keyframes animateContainer {
    0% {
        opacity: 0;
        transform: scale(0);
        box-shadow: 0px 0px 0px 65px rgba(255, 255, 255, 0.25) inset,
            0px 0px 0px 65px rgba(255, 255, 255, 0.25) inset;
    }

    25% {
        opacity: 1;
        transform: scale(0.9);
        box-shadow: 0px 0px 0px 65px rgba(255, 255, 255, 0.25) inset,
            0px 0px 0px 65px rgba(255, 255, 255, 0.25) inset;
    }

    43.75% {
        transform: scale(1.15);
        box-shadow: 0px 0px 0px 43.334px rgba(255, 255, 255, 0.25) inset,
            0px 0px 0px 65px rgba(255, 255, 255, 0.25) inset;
    }

    62.5% {
        transform: scale(1);
        box-shadow: 0px 0px 0px 0px rgba(255, 255, 255, 0.25) inset,
            0px 0px 0px 21.667px rgba(255, 255, 255, 0.25) inset;
    }

    81.25% {
        box-shadow: 0px 0px 0px 0px rgba(255, 255, 255, 0.25) inset,
            0px 0px 0px 0px rgba(255, 255, 255, 0.25) inset;
    }

    100% {
        opacity: 1;
        box-shadow: 0px 0px 0px 0px rgba(255, 255, 255, 0.25) inset,
            0px 0px 0px 0px rgba(255, 255, 255, 0.25) inset;
    }
}

@keyframes animateCheck {
    from {
        stroke-dashoffset: 80;
    }

    to {
        stroke-dashoffset: 0;
    }
}

@keyframes animateShadow {
    0% {
        opacity: 0;
        width: 100%;
        height: 15%;
    }

    25% {
        opacity: 0.25;
    }

    43.75% {
        width: 40%;
        height: 7%;
        opacity: 0.35;
    }

    100% {
        width: 85%;
        height: 15%;
        opacity: 0.25;
    }
}

.spanko:hover {
    background: #b40000;
}

.no-scrollbar::-webkit-scrollbar {
    display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.no-scrollbar {
    -ms-overflow-style: none;
    /* IE and Edge */
    scrollbar-width: none;
    /* Firefox */
}

.multiselectresetall {
    transition: 0.3s;
}

.multiselectresetall:hover {
    background: #cc0101;
}

#intervalButtonCheck:hover {
    background: #0E9F6E;
}

#intervalButtonClose:hover {
    color: white;
    background: #6B7280;
}

.moveOut {
    animation-name: moveOut;
    animation-duration: 2s;
    animation-iteration-count: 1;
    animation-fill-mode: forwards;
}

@keyframes moveOut {
    0% {
        opacity: 1;
    }

    100% {
        opacity: 0;
    }
}

.moveIn {
    animation-name: moveIn;
    animation-duration: 2s;
    animation-iteration-count: 1;
    animation-fill-mode: forwards;
}

.form-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 600px;
    overflow: hidden;
}

.form-header {
    background-color: #2c3e50;
    color: white;
    padding: 20px;
}

.form-header h1 {
    font-size: 1.25rem;
    font-weight: 500;
}

.form-content {
    padding: 24px;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    margin-bottom: 24px;
}

.form-field {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

label {
    font-size: 0.875rem;
    font-weight: 500;
}

button {
    cursor: pointer;
}

input {
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    font-size: 0.95rem;
    transition: border-color 0.2s;
}

input:focus {
    outline: none;
    border-color: #3182ce;
    box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 20px;
    background-color: #f8fafc;
    border-top: 1px solid #e2e8f0;
}

.dropdownUsersNotif {
    inset: unset !important;
    transform: unset !important;
    top: 2.3rem !important;
    right: 1rem !important;
}

.btn-primary-dark {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    background-color: #1a2533;
    color: white;
}

.btn-primary-dark:hover {
    background-color: #464d57;
}

.btn-primary {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    color: white;
}

.btn-primary:hover {
    background-color: #1a2533;
}

.btn-secondary {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    background-color: #e2e8f0;
    color: #4a5568;
}

.btn-secondary:hover {
    background-color: #cbd5e0;
}

@media (max-width: 640px) {
    .form-grid {
        grid-template-columns: 1fr;
    }
}

.inputGroupForm {
    margin-bottom: 0 !important;
}

#poolDetailForm {
    margin-bottom: 0 !important;
}

@keyframes moveIn {
    0% {
        opacity: 0;
        display: none;
    }

    100% {
        opacity: 1;
        display: block;
    }
}

#pooling-modal {
    background-color: rgba(0, 0, 0, 0.432) !important;
    height: 100vh;
}

#modalWrapperko {
    max-height: 65vh !important;
}

.poolEditInput {
    all: unset;
    border: 1px solid #f1f1f1;
    border-radius: 7px;
    padding: 0.3rem .6rem;
    transition: .3s all;
}

.poolEditInput:focus {
    border: 1px solid #7c7c7c !important;
}

.inputGroup button {
    transition: .2s all;
    pointer-events: none;
    opacity: 0;
}

.inputGroup:focus-within button {
    opacity: 100;
    pointer-events: all;
}

.buttonPayment {
    /* default color */

    /* "to left" / "to right" - affects initial color */
    background: linear-gradient(90deg, rgba(243, 244, 246, 1) 45%, rgba(132, 225, 188, 1) 75%) left;
    background-size: 200%;
    transition: background-position 3s ease-out;
}
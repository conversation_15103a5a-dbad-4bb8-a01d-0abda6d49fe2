<?php
// Theme Demo Page
?>

<div class="p-6 space-y-6">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            Theme Toggle Demo
        </h1>
        <p class="text-gray-600 dark:text-gray-300 mb-6">
            T<PERSON><PERSON> stránka demonštruje funkčnosť prepínania tém medzi svetlým a tmavým režimom.
        </p>

        <!-- Theme Toggle Instructions -->
        <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-6">
            <h3 class="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-2">
                Ako pou<PERSON>í<PERSON>ť prepínanie tém:
            </h3>
            <ul class="list-disc list-inside text-blue-800 dark:text-blue-200 space-y-1">
                <li>Kliknite na ikonu mesiaca/slnka v hornej lište</li>
                <li>Použite klávesovú skratku: <kbd class="px-2 py-1 bg-gray-200 dark:bg-gray-700 rounded text-sm">Ctrl + Shift + T</kbd></li>
                <li>Téma sa automaticky uloží a obnoví pri ďalšom návšteve</li>
                <li>Ak nie je nastavená téma, použije sa systémové nastavenie</li>
            </ul>

            <!-- Demo Theme Toggle Button -->
            <div class="mt-4 p-3 bg-white dark:bg-gray-800 rounded-lg border border-blue-200 dark:border-blue-700">
                <p class="text-sm text-gray-600 dark:text-gray-300 mb-2">Alebo skúste túto demo verziu:</p>
                <div class="flex items-center gap-2">
                    <?php include "/home/<USER>/www/src/Components/layout/header/themeToggle.php"; ?>
                    <span class="text-sm text-gray-500 dark:text-gray-400">Demo prepínač tém</span>
                </div>
            </div>
        </div>

        <!-- Sample Components -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Card Example -->
            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    Ukážková karta
                </h4>
                <p class="text-gray-600 dark:text-gray-300 mb-4">
                    Toto je ukážka karty s tmavým režimom.
                </p>
                <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                    Tlačidlo
                </button>
            </div>

            <!-- Form Example -->
            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    Ukážkový formulár
                </h4>
                <div class="space-y-3">
                    <input 
                        type="text" 
                        placeholder="Zadajte text..." 
                        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
                    >
                    <select class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white">
                        <option>Možnosť 1</option>
                        <option>Možnosť 2</option>
                        <option>Možnosť 3</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Table Example -->
        <div class="mt-6">
            <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Ukážková tabuľka
            </h4>
            <div class="overflow-x-auto">
                <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                        <tr>
                            <th scope="col" class="px-6 py-3">Názov</th>
                            <th scope="col" class="px-6 py-3">Kategória</th>
                            <th scope="col" class="px-6 py-3">Stav</th>
                            <th scope="col" class="px-6 py-3">Akcie</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                            <td class="px-6 py-4 font-medium text-gray-900 dark:text-white">
                                Položka 1
                            </td>
                            <td class="px-6 py-4">Kategória A</td>
                            <td class="px-6 py-4">
                                <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-green-900 dark:text-green-300">
                                    Aktívny
                                </span>
                            </td>
                            <td class="px-6 py-4">
                                <button class="text-blue-600 dark:text-blue-400 hover:underline">
                                    Upraviť
                                </button>
                            </td>
                        </tr>
                        <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                            <td class="px-6 py-4 font-medium text-gray-900 dark:text-white">
                                Položka 2
                            </td>
                            <td class="px-6 py-4">Kategória B</td>
                            <td class="px-6 py-4">
                                <span class="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-red-900 dark:text-red-300">
                                    Neaktívny
                                </span>
                            </td>
                            <td class="px-6 py-4">
                                <button class="text-blue-600 dark:text-blue-400 hover:underline">
                                    Upraviť
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Current Theme Display -->
        <div class="mt-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
            <p class="text-yellow-800 dark:text-yellow-200">
                <strong>Aktuálna téma:</strong> 
                <span id="current-theme-display" class="font-mono">
                    <!-- Will be populated by JavaScript -->
                </span>
            </p>
        </div>
    </div>
</div>

<script>
// Update current theme display
function updateThemeDisplay() {
    const themeDisplay = document.getElementById('current-theme-display');
    if (themeDisplay && window.themeManager) {
        themeDisplay.textContent = window.themeManager.getCurrentTheme() || 'loading...';
    }
}

// Update on theme change
window.addEventListener('themeChanged', updateThemeDisplay);

// Initial update
document.addEventListener('DOMContentLoaded', () => {
    setTimeout(updateThemeDisplay, 100);
});
</script>

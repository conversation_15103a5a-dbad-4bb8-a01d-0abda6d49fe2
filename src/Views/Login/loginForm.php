<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

if (isset($_GET["logoutKilled"])) {
    $error = "Va<PERSON>e prihlasovanie bolo zablokované administrátorom!";
}

//CHECK IF LOGIN IS PERMITTED
$loginDisabled = Connection::getDataFromDatabase("SELECT * FROM nologin", defaultDB)[1];
if (sizeof($loginDisabled) > 0) {
    $loginDisabledError = "Prihlásenie je zablokované administrátorom!";
}
?>
<div id="forLoading">
    <div id="loading" class="hidden animate-pulse flex-col gap-6 items-center py-24 justify-center">
        <div role="status">
            <svg aria-hidden="true" class="inline w-16 h-16 text-gray-200 animate-spin dark:text-gray-600 fill-blue-600"
                viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                    d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                    fill="currentColor" />
                <path
                    d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                    fill="currentFill" />
            </svg>
            <span class="sr-only">Loading...</span>
        </div>
        <h1 class="text-xl font-bold leading-tight mb-6 tracking-tight text-gray-900 md:text-2xl dark:text-white">
            Prihlasujem...
        </h1>
    </div>
    <?php if (isset($loginDisabledError)) { ?>
        <h1 class="text-xl font-bold leading-tight mb-6 bg-red-400 text-red-900 py-8 rounded-lg text-center tracking-tight text-gray-900 md:text-2xl dark:text-white">
                <?php echo $loginDisabledError; ?>
            </h1>
    <?php } else { ?>
        <div id="loginFormWrapper" class="">
            <?php if (isset($_GET["pswdReset"])) { ?>
                <div id="successAlert"
                    class="p-4 mb-4 text-sm text-green-800 rounded-lg bg-green-200 dark:bg-gray-800 dark:text-green-400"
                    role="alert">
                    <span id="successAlertspan" class="font-medium">Teraz sa môžete znova prihlásiť!</span>
                </div>
            <?php } ?>
            <h1 class="text-xl font-bold leading-tight mb-6 tracking-tight text-gray-900 md:text-2xl dark:text-white">
                Prihlás sa do svojho účtu
            </h1>
            <?php if ($error !== "") { ?>
                <div class="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 font-bold dark:text-red-400"
                    role="alert">
                    <?php echo $error; ?>
                </div>
            <?php } ?>
            <div id="loginAlert"
                class="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-100 dark:bg-gray-800 dark:text-red-400" role="alert">
                <span class="font-medium"></span>
            </div>
            <form class="space-y-4 md:space-y-6" id="loginForm">
                <div>
                    <label for="username" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Prihlasovacie
                        meno</label>
                    <input type="text" name="username" id="username"
                        class="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-blue-600 focus:border-blue-600 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                </div>
                <div class="password">
                    <label for="password" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Heslo</label>
                    <input type="password" name="password" id="password"
                        class="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-blue-600 focus:border-blue-600 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                        required="">
                </div>
                <div class="flex items-center jusitfy-between py-2 border-t">
                    <a href="/zabudnute-heslo"
                        class="font-medium text-xs text-blue-600 underline dark:text-blue-500 hover:no-underline">Zabudnuté
                        heslo</a>
                </div>
                <button type="submit"
                    class="w-full text-white bg-blue-700 hover:bg-blue-900 transition-all focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                    Prihlásiť sa
                </button>
            </form>
        </div>
    <?php } ?>
</div>

<script src="src/assets/js/loginValidation.js"></script>
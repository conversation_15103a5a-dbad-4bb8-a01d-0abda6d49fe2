<h1 class="text-xl font-bold leading-tight tracking-tight mb-2 text-gray-900 md:text-2xl dark:text-white"
    style="margin: 0; margin-bottom: 0.5rem;">
    <PERSON><PERSON><PERSON><PERSON><PERSON>
</h1>
<p class="text-xs mt-0 p-0 text-gray-500" style="margin: 0;"><PERSON> email Vám príde link s resetovaním.</p>
<div id="successAlert" class="p-4 mb-4 hidden text-sm text-green-800 rounded-lg bg-green-200 dark:bg-gray-800 dark:text-green-400"
    role="alert">
    <span id="successAlertspan" class="font-medium"></span>
</div>
<form class="space-y-4 md:space-y-6" id="resetForm">
    <div>
        <label for="username" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">E-mail</label>
        <input type="text" name="email" id="username"
            class="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-1.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
    </div>
    <button type="submit"
        class="w-full text-white bg-blue-700 hover:bg-primary-700 Ffocus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800">
        Odoslať
    </button>
</form>
<script src="src/assets/js/passwordForgor.js"></script>
<?php
require_once('/home/<USER>/www/src/lib/connection.php');
require_once("/home/<USER>/www/conf/settings.php");

if ($_SERVER['REQUEST_METHOD'] === "POST") {
    print_r($_POST);
    if ($_POST["password"] !== "") {
        $pass = $_POST["password"];
        $token = $_GET["token"];
        $tokenInfo = Connection::getDataFromDatabase("SELECT * FROM passwordtokens WHERE token = '$token'", defaultDB);
        date_default_timezone_set("Europe/Bratislava");
        $currentDateAndTime = date('Y-m-d H:i:s');
        if (date('Y-m-d H:i:s') > $tokenInfo[1][0]["expiration"]) {
            $error = "Tento link je expirovaný.";
        } else {
            $userID = $tokenInfo[1][0]["userid"];
            $newHash = password_hash($pass, PASSWORD_DEFAULT);
            $smt = Connection::InsertUpdateCreateDelete("UPDATE users SET password = ? WHERE userid = ?", [$newHash, $userID], defaultDB);
            if ($smt) {
                header("Location: /?pswdReset=1");
            } else {
                echo "Nové heslo sa nepodarilo vytvoriť. Skúste to znova";
            }
        }
    } else {
        $error = "Nezadali ste heslo. Heslo nesmie byť prázdne";
    }
}


if (isset($_GET["token"])) {
    $token = $_GET["token"];
    $tokenInfo = Connection::getDataFromDatabase("SELECT * FROM passwordtokens WHERE token = '$token'", defaultDB);
    date_default_timezone_set("Europe/Bratislava");
    $currentDateAndTime = date('Y-m-d H:i:s');
    if (date('Y-m-d H:i:s') > $tokenInfo[1][0]["expiration"]) {
        $error = "Tento link je expirovaný.";
    } else {
        $error = "";
    }

} else {
    header("Location: /");
}

?>
<h1 class="text-xl font-bold leading-tight tracking-tight mb-2 text-gray-900 md:text-2xl dark:text-white"
    style="margin: 0; margin-bottom: 0.5rem;">
    Reset hesla
</h1>
<?php
if ($error !== "") { ?>
    <div class="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 font-bold dark:text-red-400"
        role="alert">
        <span class="font-extrabold">Chyba!</span> <?php echo $error; ?>
    </div>
<?php } else { ?>
    <p class="text-xs mt-0 p-0 text-gray-500" style="margin: 0;">Teraz si môžete vytvoriť nové heslo.</p>
<?php }
?>

<div id="loginAlert" class="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-100 dark:bg-gray-800 dark:text-red-400"
    role="alert">
    <span class="font-medium"></span>
</div>
<form class="space-y-4 md:space-y-6" method="POST" action="">
    <input type="hidden" name="userid" id="userid" value="<?php echo $tokenInfo[1][0]["userid"]; ?>" />
    <div>
        <label for="password" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Heslo</label>
        <input type="password" name="password" id="password"
            class="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-1.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
    </div>
    <button type="submit"
        class="w-full text-white bg-blue-700 hover:bg-primary-700 Ffocus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800">
        Vytvoriť nové heslo
    </button>
</form>
<?php
print_r($_POST);
$query = "WITH subjektid_lookup AS (SELECT p1.dealid,
                                 CASE
                                     WHEN COUNT(po1.cislozmluvy) = 1 THEN MAX(po1.cislozmluvy)
                                     ELSE ''
                                     END AS subjektid
                          FROM pool p1
                                   JOIN pooldetail pd1 ON p1.poolid = pd1.poolid
                                   JOIN portfolio po1 ON pd1.subjektid = po1.fondid
                          GROUP BY p1.dealid)

SELECT ktv.dealid,
       pa.skratka,
       ktv.z_td                                          AS zaciatok,
       ktv.k_td                                          AS koniec,
       ktv.dk_td,
       ktv.sum_td,
       ktv.pd_td,
       ktv.ir_td,
       ktv.iv_b,
       ktv.iv_n,
       ktv.mena,
       ktv.cond1,
       ktv.dealer,
       ktv.datum_cas_obchodu,
       tp.typ_pokynu,
       ktv.suma_dane,
       CASE
           WHEN ktv.logactivityid = 25 THEN 'Splatené'
           WHEN ktv.logactivityid IN (4, 8) THEN 'Zámer'
           WHEN ktv.logactivityid IN (12, 13, 14, 15, 16, 17) THEN 'Potvrdené'
           ELSE 'Založené'
           END                                           AS stav,
       2                                                 AS detail,
       COALESCE(sl.subjektid, 'Sympatia', p.cislozmluvy) AS subjektid
FROM konfirmaciaktv ktv
         JOIN partner pa ON pa.partnerid = ktv.partnerid
         LEFT JOIN typy_pokynov tp ON tp.typid = ktv.typ_pokynu
         LEFT JOIN portfolio p ON ktv.subjektid = p.fondid
         LEFT JOIN subjektid_lookup sl ON ktv.dealid = sl.dealid
WHERE TO_CHAR(ktv.z_td, 'yyyy') = '$rok'
  AND ktv.logactivityid <> 3 AND mena = '$mena'
ORDER BY ktv.dealid;";
echo $query;
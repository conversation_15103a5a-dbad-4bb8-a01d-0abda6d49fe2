<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

//COMPONENTS
require_once "/home/<USER>/www/src/Components/buttons/declineButton.php";

$menoveParicky = Connection::getDataFromDatabase("SELECT * FROM menovypar", defaultDB)[1];
$meny = Connection::getDataFromDatabase("SELECT mena FROM menadb ORDER BY poradie", defaultDB)[1];

?>
<section class="py-3 px-5" style="margin-bottom: 8rem">
    <h3 class="text-4xl mb-4 font-bold text-gray-900 dark:text-white">Menov<PERSON> páry</h3>
    <div class="mx-auto">
        <form id="searchFrom" hx-target="tbody" class="flex mb-2 w-full items-center">
            <div class="relative w-full bg-white shadow-md dark:bg-gray-800 sm:rounded-lg">
                <div
                    class="flex flex-col w-full items-start justify-between p-4 space-y-3 md:flex-row md:space-y-0 md:space-x-4">
                    <div class="w-full md:w-1/2">
                        <label for="simple-search" class="sr-only">Vyhľadávanie</label>
                        <div class="relative flex items-center w-full">
                            <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                <svg aria-hidden="true" class="w-5 h-5 text-gray-500 dark:text-gray-400"
                                    fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd"
                                        d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                                        clip-rule="evenodd" />
                                </svg>
                            </div>
                            <input type="text" id="searchBar" name="search"
                                class="block w-full p-2 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                placeholder="Vyhľadávanie...">
                            <div id="resetSearch"
                                class="absolute hidden text-gray-500 hover:text-gray-900 transition-all cursor-pointer"
                                style="right: .7rem">
                                <svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24"
                                    height="24" fill="currentColor" viewBox="0 0 24 24">
                                    <path fill-rule="evenodd"
                                        d="M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10-4.477 10-10 10S2 17.523 2 12Zm7.707-3.707a1 1 0 0 0-1.414 1.414L10.586 12l-2.293 2.293a1 1 0 1 0 1.414 1.414L12 13.414l2.293 2.293a1 1 0 0 0 1.414-1.414L13.414 12l2.293-2.293a1 1 0 0 0-1.414-1.414L12 10.586 9.707 8.293Z"
                                        clip-rule="evenodd" />
                                </svg>

                            </div>
                        </div>
                    </div>
                    <div
                        class="flex flex-col items-stretch justify-end flex-shrink-0 w-full space-y-2 md:w-auto md:flex-row md:space-y-0 md:items-center md:space-x-3">
                        <button type="button" data-modal-target="create-modal" data-modal-toggle="create-modal"
                            class="flex items-center justify-center px-4 py-2 text-sm font-medium text-white rounded-lg bg-blue-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 focus:outline-none dark:focus:ring-primary-800">
                            <svg class="h-3.5 w-3.5 mr-2" fill="currentColor" viewbox="0 0 20 20"
                                xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                                <path clip-rule="evenodd" fill-rule="evenodd"
                                    d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" />
                            </svg>
                            Pridať menový pár
                        </button>
                    </div>
                </div>
            </div>
            <button id="submitFilterForm" type="submit" class="hidden h-0 w-0"></button>
        </form>
        <div class="overflow-x-visible">
            <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                    <tr>
                        <th scope="col" class="px-4 py-3">Menový pár</th>
                        <th scope="col" class="px-4 py-3">Kurz</th>
                        <th scope="col" class="px-4 py-3">Lot</th>
                        <th scope="col" class="px-4 py-3">Možnosti</th>
                    </tr>
                </thead>
                <tbody id="menove_paricky">
                    <?php require_once "/home/<USER>/www/src/Controllers/menove-pary/tableData.php"; ?>
                </tbody>
            </table>
        </div>
    </div>
</section>
<div class="hidden" id="result"></div>
<div id="create-modal" tabindex="-1" aria-hidden="true"
    class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
    <div class="relative p-4 w-full max-w-2xl max-h-full">
        <form hx-post="/api/menove-pary/create" hx-target="#result"
            class="relative bg-white rounded-lg shadow-sm dark:bg-gray-700">
            <div
                class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600 border-gray-200">
                <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                    Pridať nový menový pár
                </h3>
                <button type="button"
                    class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
                    data-modal-hide="create-modal">
                    <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                        viewBox="0 0 14 14">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                    </svg>
                    <span class="sr-only">Close modal</span>
                </button>
            </div>
            <div class="p-4 md:p-5 space-y-4">
                <div class="grid gap-4 sm:grid-cols-2 sm:gap-6">
                    <div class="w-full">
                        <label for="nakupuje"
                            class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Klient
                            nakupuje</label>
                        <select id="nakupuje" name="nakupuje" onchange="selectPar()"
                            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                            <?php
                            foreach ($meny as $key => $value) { ?>
                                <option value="<?php echo $value["mena"]; ?>"><?php echo $value["mena"]; ?></option>
                            <?php } ?>
                        </select>
                    </div>
                    <div class="w-full">
                        <label for="predava" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Klient
                            predáva</label>
                        <select id="predava" name="predava" onchange="selectPar()"
                            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                            <?php
                            foreach ($meny as $key => $value) { ?>
                                <option value="<?php echo $value["mena"]; ?>"><?php echo $value["mena"]; ?></option>
                            <?php } ?>
                        </select>
                    </div>
                    <div>
                        <label for="menovypar"
                            class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Menový pár</label>
                        <select id="menovypar" name="menovypar" disabled="true"
                            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">

                        </select>
                    </div>
                    <div>
                        <label for="nacitatkurz"
                            class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Načítať kurz</label>
                        <select id="nacitatkurz" name="nacitatkurz"
                            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                            <option value="1">Áno</option>
                            <option value="0" selected>Nie</option>
                        </select>
                    </div>
                    <div class="sm:col-span-2">
                        <label for="lot"
                            class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Lot</label>
                        <input type="text" id="lot" name="lot" decimal="5" size="12"
                            class="block w-full p-2 text-gray-900 border border-gray-300 rounded-lg bg-gray-50 text-xs focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                    </div>
                    <div class="sm:col-span-2">
                        <label for="rezim"
                            class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Režim</label>
                        <select id="rezim" name="rezim"
                            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                            <option value="DIV">delenie kurzu</option>
                            <option value="MUL">násobenie kurzu</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="flex items-center p-4 md:p-5 border-t border-gray-200 rounded-b dark:border-gray-600">
                <?php
                $acceptButton = new Button("Vytvoriť menový pár", "create-menove-pary", "submit", "", "primary");
                $acceptButton->render();
                $closeButton = new Button("Zrušiť", "create-modal", "button", "create-modal", "decline");
                $closeButton->render(); ?>
            </div>
        </form>
    </div>
</div>
<div id="edit-modal" tabindex="-1" aria-hidden="true"
    class="hidden overflow-y-auto overflow-x-hidden fixed bg-black/50 top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%*1)] max-h-full">
    <div class="relative p-4 w-full max-w-2xl max-h-full">
        <form hx-post="/api/menove-pary/update" hx-target="#result"
            class="relative bg-white rounded-lg shadow-sm dark:bg-gray-700">
            <div
                class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600 border-gray-200">
                <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                    Pridať nový menový pár
                </h3>
                <button type="button"
                    class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
                    data-modal-hide="edit-modal">
                    <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                        viewBox="0 0 14 14">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                    </svg>
                    <span class="sr-only">Close modal</span>
                </button>
            </div>
            <div id="edit-form" class="p-4 md:p-5 space-y-4">
                Loading...
            </div>
            <div class="flex items-center p-4 md:p-5 border-t border-gray-200 rounded-b dark:border-gray-600">
                <?php
                $acceptButton = new Button("Aktualizovať menový pár", "edit-menove-pary", "submit", "edit-modal", "primary");
                $acceptButton->render();
                $closeButton = new Button("Zrušiť", "edit-modal", "button", "edit-modal", "decline");
                $closeButton->render(); ?>
            </div>
        </form>
    </div>
</div>
<script>
    function selectPar(e) {
        let nakupuje = document.getElementById("nakupuje").value;
        let predava = document.getElementById("predava").value;
        let menovypar = document.getElementById("menovypar");
        if (nakupuje != "" && predava != "") {
            menovypar.innerHTML = "";
            let option = document.createElement("option");
            option.value = nakupuje + predava;
            option.text = nakupuje + predava;
            menovypar.add(option);
            option = document.createElement("option");
            option.value = predava + nakupuje;
            option.text = predava + nakupuje;
            menovypar.add(option);
            menovypar.disabled = false;
        } else {
            menovypar.disabled = true;
        }
    }

    (function () {
        let typingTimer;
        const doneTyping = 500;

        $('#searchBar').on('keyup', (e) => {
            if (e.currentTarget.value !== "") {
                $('#resetSearch').removeClass('hidden')
            } else {
                $('#resetSearch').addClass('hidden')
            }
            clearTimeout(typingTimer);
            typingTimer = setTimeout(() => {
                $('#searchFrom').submit();
            }, doneTyping);
        });

        $('#searchBar').on('keydown', () => {
            clearTimeout(typingTimer);
        });

        $('#searchFrom').on('submit', (e) => {
            e.preventDefault();
            const formData = new FormData(e.currentTarget)
            const searchQuery = formData.get("search");
            console.log(searchQuery);
            if (searchQuery !== "") {
                searchEntered = true;
            }
            htmx.ajax('POST', `/src/Controllers/menove-pary/tableData.php`, {
                target: '#menove_paricky',
                values: {
                    "search": searchQuery
                }
            });
        });
    })();
</script>
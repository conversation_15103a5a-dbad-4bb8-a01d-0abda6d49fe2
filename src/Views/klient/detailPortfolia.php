<?php
$detailyFonduRes = Connection::getDataFromDatabase("SELECT 
        CASE 
            WHEN dlhopisy = 0 THEN 'Nie'
            WHEN dlhopisy IS NULL THEN 'Nie'
            WHEN dlhopisy = 1 THEN 'Áno' 
        END as dlhopisy, 
        CASE 
            WHEN akcie = 0 THEN 'Nie' 
            WHEN akcie IS NULL THEN 'Nie'
            WHEN akcie = 1 THEN 'Áno'
        END as akcie, 
        refmena
	from 
		fonds 
	where 
		fondid=$id_fond", defaultDB);
$detailyFondu = $detailyFonduRes[1];

$portfolioInfoRes = Connection::getDataFromDatabase("SELECT 
    nav * COALESCE(k.kurz, 1) as nav,
    to_char(p.datum, 'DD.MM.YYYY') as datum
FROM 
    pricestore p
LEFT JOIN kurzyaktivarchiv k ON p.datum = k.datum AND k.ric = 'EUR'||refmena 
WHERE 
    fondid = $id_fond
    AND p.datum = (SELECT max(datum) FROM pricestore WHERE fondid = $id_fond)", defaultDB);
$portfolioInfo = $portfolioInfoRes[1];


$query = "SELECT
		cislouctu,
		CASE 
            WHEN k.eqid = 'BU' THEN 1
            WHEN k.eqid = 'TD' THEN 2
            WHEN k.eqid = 'Bonds' THEN 3
            WHEN k.eqid = 'Shares' THEN 4
            WHEN k.eqid = 'Fonds' THEN 5
            ELSE 6
        END AS orderList,
		k.eqid as druh,
		COALESCE(de.isinreal, k.kodaktiva) as aktivum,
		COALESCE(de.cpnazskratka, k.kodaktiva) as skratka,
		max(jednotka) as jednotka,
		sum(stav) as stav,
		sum(stav - rezervacia - odchadzajuce - zavazokemisia - ktvviazane - poplatky - ostatne_zavazky + prevody) as volne,
		sum(rezervacia + poplatky) as rezervacia,
		sum(odchadzajuce) as odchadzajuce,
		sum(prichadzajuce) as prichadzajuce
	from
	(
			select
			cutd as cislouctu,
			'' as destinacia,
			subjektid,
			mena as kodaktiva,
			'TD' as eqid,
			mena as jednotka,
			0 as stav,
			0 as rezervacia,
			0 as odchadzajuce,
			0 as prichadzajuce,
			0 as pocet,
			0 as zavazokemisia,
			0 as ktvviazane,
			0 as poplatky,
			0 as ostatne_zavazky,
			0 as prevody
			from konfirmaciaktv
			where
			subjektid = $id_fond and
			logactivityid in (17,18,19,20,21,22)
		UNION ALL
			select
			ucetaktiva as cislouctu,
			'' as destinacia,
			mt.subjektid,
			mena||' ('||kodaktiva||')' as kodaktiva,
			'NDF' as eqid,
			mena as jednotka,
			pocet*(sign(md_d-0.5)*(-1)) as stav,
			0 as rezervacia,
			0 as odchadzajuce,
			0 as prichadzajuce,
			pocet*(sign(md_d-0.5)*(-1)) as pocet,
			0 as zavazokemisia,
			0 as ktvviazane,
			0 as poplatky,
			0 as ostatne_zavazky,
			0 as prevody
			from majetoktoday mt, konverzia k
			where
			mt.subjektid = $id_fond and
			mt.subjektid = k.subjektid and
			'NDF-'||mt.kodaktiva = 'NDF-'||k.dealid and
			mena='SKK' and
			uctovnykod in (315181,325181) and
			logactivityid=12 and
			typ_konverzie = 1
		UNION ALL
			select
			ucetaktiva as cislouctu,
			'majetoktoday' as destinacia,
			subjektid,
			kodaktiva,
			eqid,
			jednotka,
			0 as stav,
			0 as rezervacia,
			0 as odchadzajuce,
			0 as prichadzajuce,
			0 as pocet,
			0 as zavazokemisia,
			0 as ktvviazane,
			(sign(md_d - 0.5) * pocet) as poplatky,
			0 as ostatne_zavazky,
			0 as prevody
			from majetoktoday
			where
			subjektid = $id_fond and
			uctovnykod in (325413,325411,325412,325422,325421,325435,325437,325521,325522,325143,325443)
		UNION ALL
			select
			ucetaktiva as cislouctu,
			'majetoktoday' as destinacia,
			subjektid,
			kodaktiva,
			eqid,
			jednotka,
			0 as stav,
			0 as rezervacia,
			0 as odchadzajuce,
			0 as prichadzajuce,
			0 as pocet,
			(sign(md_d - 0.5) * pocet) as zavazokemisia,
			0 as ktvviazane,
			0 as poplatky,
			0 as ostatne_zavazky,
			0 as prevody
			from majetoktoday
			where
			subjektid = $id_fond and
			uctovnykod in (325210)
		UNION ALL
			select
			ucetaktiva as cislouctu,
			'majetoktoday' as destinacia,
			subjektid,
			kodaktiva,
			eqid,
			jednotka,
			0 as stav,
			0 as rezervacia,
			0 as odchadzajuce,
			0 as prichadzajuce,
			0 as pocet,
			0 as zavazokemisia,
			0 as ktvviazane,
			0 as poplatky,
			(sign(md_d - 0.5) * pocet) as ostatne_zavazky,
			0 as prevody
			from majetoktoday
			where
			subjektid = $id_fond and
			uctovnykod in (325300,325350)
		UNION ALL
			select
			ucetaktiva as cislouctu,
			'majetoktoday' as destinacia,
			subjektid,
			kodaktiva,
			eqid,
			jednotka,
			0 as stav,
			0 as rezervacia,
			(sign(md_d - 0.5) * pocet) as odchadzajuce,
			0 as prichadzajuce,
			0 as pocet,
			0 as zavazokemisia,
			0 as ktvviazane,
			0 as poplatky,
			0 as ostatne_zavazky,
			0 as prevody
			from majetoktoday
			where
			subjektid = $id_fond and
			uctovnykod in (325700)
		UNION ALL
			select
			ucetaktiva as cislouctu,
			'majetoktoday' as destinacia,
			subjektid,
			kodaktiva,
			eqid,
			jednotka,
			(sign(md_d - 0.5) * (-1) * pocet) as stav,
			0 as rezervacia,
			0 as odchadzajuce,
			0 as prichadzajuce,
			(sign(md_d - 0.5) * (-1) * pocet) as pocet,
			0 as zavazokemisia,
			0 as ktvviazane,
			0 as poplatky,
			0 as ostatne_zavazky,
			0 as prevody
			from majetoktoday
			where
			subjektid = $id_fond and
			uctovnykod in ( 221110,221210,251110,251200,251300,251400)
		UNION ALL
			select
			ucetaktiva as cislouctu,
			'majetokcesta' as destinacia,
			subjektid,
			kodaktiva,
			eqid,
			jednotka,
			0 as stav,
			0 as rezervacia,
			((1-in_out) * pocet) as odchadzajuce,
			(in_out * pocet) as prichadzajuce,
			(sign(in_out - 0.5) * pocet) as pocet,
			0 as zavazokemisia,
			0 as ktvviazane,
			0 as poplatky,
			0 as ostatne_zavazky,
			0 as prevody
			from majetokcesta ma
			where subjektid = $id_fond and
			 in_out=1
		UNION ALL
			select
			ma.ucetaktiva as cislouctu,
			'majetokcesta' as destinacia,
			pdr.subjektid,
			ma.kodaktiva,
			ma.eqid,
			ma.jednotka,
			0 as stav,
			0 as rezervacia,
			((1-ma.in_out) * pdr.ksreal) as odchadzajuce,
			(ma.in_out * pdr.ksreal) as prichadzajuce,
			(sign(ma.in_out - 0.5) * pdr.ksreal) as pocet,
			0 as zavazokemisia,
			0 as ktvviazane,
			0 as poplatky,
			0 as ostatne_zavazky,
			0 as prevody
			from majetokcesta ma, today t, pool p, pooldetailreal pdr
			where ma.subjektid = 0 and 
      			pdr.subjektid = $id_fond and
			t.fondid = ma.subjektid and
			ma.cestatill>=t.datum and
			ma.in_out=1 and 
			(ma.destinacia = p.destinacia or (ma.destinacia = 'rekonfirmaciacp' and p.destinacia = 'konfirmaciacp'))  
			and ma.dealid = p.dealid 
			and p.poolid = pdr.poolid
			and ma.eqid in ('Bonds', 'Shares', 'Fonds')
		UNION ALL
			select
			ma.ucetaktiva as cislouctu,
			'majetokcesta' as destinacia,
			pdr.subjektid,
			ma.kodaktiva,
			ma.eqid,
			ma.jednotka,
			0 as stav,
			0 as rezervacia,
			((1-ma.in_out) * pdr.transsumareal) as odchadzajuce,
			(ma.in_out * pdr.transsumareal) as prichadzajuce,
			(sign(ma.in_out - 0.5) * pdr.transsumareal) as pocet,
			0 as zavazokemisia,
			0 as ktvviazane,
			0 as poplatky,
			0 as ostatne_zavazky,
			0 as prevody
			from majetokcesta ma, today t, pool p, pooldetailreal pdr
			where ma.subjektid = 0 and 
      			pdr.subjektid = $id_fond and
			t.fondid = ma.subjektid and
			ma.cestatill>=t.datum and
			ma.in_out=1 and 
			(ma.destinacia = p.destinacia or (ma.destinacia = 'rekonfirmaciacp' and p.destinacia = 'konfirmaciacp'))  
			and ma.dealid = p.dealid 
			and p.poolid = pdr.poolid
			and ma.eqid in ('BU')
			and ma.tranza = 1
		UNION ALL
			select
			ma.ucetaktiva as cislouctu,
			'majetokcesta' as destinacia,
			pdr.subjektid,
			ma.kodaktiva,
			ma.eqid,
			ma.jednotka,
			0 as stav,
			0 as rezervacia,
			(1 - ma.in_out) * (COALESCE(pdr.auvreal, 0) - COALESCE(pdr.dan, 0)) AS odchadzajuce,
            ma.in_out * (COALESCE(pdr.auvreal, 0) - COALESCE(pdr.dan, 0)) AS prichadzajuce,
            SIGN(ma.in_out - 0.5) * (COALESCE(pdr.auvreal, 0) - COALESCE(pdr.dan, 0)) AS pocet,
			0 as zavazokemisia,
			0 as ktvviazane,
			0 as poplatky,
			0 as ostatne_zavazky,
			0 as prevody
			from majetokcesta ma, today t, pool p, pooldetailreal pdr
			where ma.subjektid = 0 and 
      			pdr.subjektid = $id_fond and
			t.fondid = ma.subjektid and
			ma.cestatill>=t.datum and
			ma.in_out=1 and 
			(ma.destinacia = p.destinacia or (ma.destinacia = 'rekonfirmaciacp' and p.destinacia = 'konfirmaciacp'))  
			and ma.dealid = p.dealid 
			and p.poolid = pdr.poolid
			and ma.eqid in ('BU')
			and ma.tranza = 2
      		UNION ALL
			select
			ucetaktiva as cislouctu,
			'majetokcesta' as destinacia,
			subjektid,
			kodaktiva,
			eqid,
			jednotka,
			0 as stav,
			0 as rezervacia,
			((1-in_out) * pocet) as odchadzajuce,
			(in_out * pocet) as prichadzajuce,
			(sign(in_out - 0.5) * pocet) as pocet,
			0 as zavazokemisia,
			0 as ktvviazane,
			0 as poplatky,
			0 as ostatne_zavazky,
			0 as prevody
			from majetokcesta ma
			where subjektid = $id_fond and
			 in_out=0
		UNION ALL
			select 
       			ma.ucetaktiva as cislouctu,
			'majetokcesta' as destinacia,
			pdr.subjektid,
			ma.kodaktiva,
			ma.eqid,
			ma.jednotka,
			0 as stav,
			0 as rezervacia,
			((1-ma.in_out) * pdr.ksreal) as odchadzajuce,
			(ma.in_out * pdr.ksreal) as prichadzajuce,
			(sign(ma.in_out - 0.5) * pdr.ksreal) as pocet,
			0 as zavazokemisia,
			0 as ktvviazane,
			0 as poplatky,
			0 as ostatne_zavazky,
			0 as prevody
			from majetokcesta ma, pool p, pooldetailreal pdr
			where 
			ma.subjektid = 0 and
			ma.in_out=0 and 
			(ma.destinacia = p.destinacia or (ma.destinacia = 'rekonfirmaciacp' and p.destinacia = 'konfirmaciacp'))  
			and ma.dealid = p.dealid and 
			p.poolid = pdr.poolid and
			pdr.subjektid = $id_fond
			and ma.eqid in ('Bonds', 'Shares', 'Fonds')
		UNION ALL
			select 
       			ma.ucetaktiva as cislouctu,
			'majetokcesta' as destinacia,
			pdr.subjektid,
			ma.kodaktiva,
			ma.eqid,
			ma.jednotka,
			0 as stav,
			0 as rezervacia,
			((1-ma.in_out) * pdr.transsumareal) as odchadzajuce,
			(ma.in_out * pdr.transsumareal) as prichadzajuce,
			(sign(ma.in_out - 0.5) * pdr.transsumareal) as pocet,
			0 as zavazokemisia,
			0 as ktvviazane,
			0 as poplatky,
			0 as ostatne_zavazky,
			0 as prevody
			from majetokcesta ma, pool p, pooldetailreal pdr
			where 
			ma.subjektid = 0 and
			ma.in_out=0 and 
			(ma.destinacia = p.destinacia or (ma.destinacia = 'rekonfirmaciacp' and p.destinacia = 'konfirmaciacp'))  
			and ma.dealid = p.dealid and 
			p.poolid = pdr.poolid and
			pdr.subjektid = $id_fond
			and ma.eqid in ('BU')
		UNION ALL
			select
			ucetaktiva as cislouctu,
			'rezervacia' as destinacia,
			subjektid,
			kodaktiva,
			eqid,
			jednotka,
			0 as stav,
			pocet as rezervacia,
			0 as odchadzajuce,
			0 as prichadzajuce,
			(pocet * -1) as pocet,
			0 as zavazokemisia,
			0 as ktvviazane,
			0 as poplatky,
			0 as ostatne_zavazky,
			0 as prevody
			from rezervacia
			where subjektid = $id_fond
				and destinacia <> 'konfirmaciacp'
		UNION ALL
			select
			r.ucetaktiva as cislouctu,
			'rezervacia' as destinacia,
			r.subjektid,
			r.kodaktiva,
			r.eqid,
			r.jednotka,
			0 as stav,
			pd.transsuma as rezervacia,
			0 as odchadzajuce,
			0 as prichadzajuce,
			(pd.transsuma * -1) as pocet,
			0 as zavazokemisia,
			0 as ktvviazane,
			0 as poplatky,
			0 as ostatne_zavazky,
			0 as prevody
			from rezervacia r, pool p, pooldetail pd
			where r.subjektid = 0 and p.dealid = r.dealid and pd.poolid = p.poolid and r.destinacia = 'konfirmaciaktv' and pd.subjektid = $id_fond
		UNION ALL
			select
			ucet as cislouctu,
			'prevodPP' as destinacia,
			subjektid,
			mena as kodaktiva,
			'BU' as eqid,
			mena as jednotka,
			0 as stav,
			0 as rezervacia,
			suma as odchadzajuce,
			0 as prichadzajuce,
			(-1 * suma) as pocet,
			0 as zavazokemisia,
			0 as ktvviazane,
			0 as poplatky,
			0 as ostatne_zavazky,
			0 as prevody
			from konfirmaciapp
			where subjektid = $id_fond and
			druhobchodu = 'prevod' and
			logactivityid < 3 and
      			dealid_related is not NULL
		UNION ALL
			select
			ucet as cislouctu,
			'prevodPP' as destinacia,
			subjektid,
			mena as kodaktiva,
			'BU' as eqid,
			mena as jednotka,
			0 as stav,
			0 as rezervacia,
			0 as odchadzajuce,
			suma as prichadzajuce,
			suma as pocet,
			0 as zavazokemisia,
			0 as ktvviazane,
			0 as poplatky,
			0 as ostatne_zavazky,
			suma as prevody
			from konfirmaciapp
			where subjektid = $id_fond and
			druhobchodu = 'prevod' and
			logactivityid < 3 and
      			dealid_related is NULL  
	) as k
	LEFT JOIN dbequitycurrric d ON k.kodaktiva = d.isincurrric
	LEFT JOIN dbequitycurr dc ON d.isincurr = dc.isincurr
	LEFT JOIN dbequity de ON dc.isin = de.isin
	group by
		cislouctu, k.eqid, kodaktiva, de.isinreal, de.cpnazskratka
	order by
		orderList, aktivum
";
$portfolioRes = Connection::getDataFromDatabase($query, defaultDB);
$portfolio = $portfolioRes[1];
$pocetSum = 0;
?>
<div
	class="bg-white dark:bg-gray-700 rounded-xl shadow-sm overflow-hidden border dark:border-slate-600 border-slate-200 hover:shadow-md transition-shadow">
	<div class="p-5 border-b border-slate-100">
		<div id="prepinacka"></div>
		<div id="clientResult"></div>
		<div class="flex justify-between items-center">
			<div id="client"
				onclick="runGoToPortfolio(event, '<?php echo $cislozmluvy; ?>', '<?php echo $id_fond; ?>', '<?php echo $clientID; ?>', '<?php echo $clientName; ?>', '<?php echo $clientPrieznaz; ?>', true)"
				class="text-xl font-bold hover:underline group flex items-center gap-3 cursor-pointer text-primary-600">
				<span><?php echo $cislozmluvy; ?></span>
				<span class="hidden group-hover:block transition-all">
					<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
						stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
						class="lucide lucide-square-arrow-out-up-right-icon lucide-square-arrow-out-up-right">
						<path d="M21 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h6" />
						<path d="m21 3-9 9" />
						<path d="M15 3h6v6" />
					</svg>
				</span>
			</div>
			<span class="bg-green-100 text-green-800 text-sm font-medium px-3 py-1 rounded-full">Aktívne</span>
		</div>
	</div>
	<div class="p-5 space-y-4">
		<!-- Market Value -->
		<div class="flex justify-between items-center">
			<span class="text-slate-300 text-xs">Hodnota k <?php echo $portfolioInfo[0]["datum"]; ?></span>
			<div class="flex items-center">
				<span
					class="bg-green-500 dark:bg-green-800 text-white font-medium px-3 py-1 rounded-md"><?php echo number_format($portfolioInfo[0]["nav"], 2, '.', ' ') ?></span>
			</div>
		</div>

		<!-- Asset Type -->
		<div class="grid grid-cols-2 gap-4">
			<div class="bg-slate-50 dark:bg-gray-900 p-1.5 px-2 rounded-lg">
				<div class="text-xs dark:text-slate-300 text-slate-500">Dlhopisy</div>
				<div class="font-medium dark:text-slate-200 text-slate-800"><?php echo $detailyFondu[0]["dlhopisy"] ?>
				</div>
			</div>
			<div class="bg-slate-50 dark:bg-gray-900 p-1.5 px-2 rounded-lg">
				<div class="text-xs dark:text-slate-300 text-slate-500">Akcie</div>
				<div class="font-medium dark:text-slate-200 text-slate-800"><?php echo $detailyFondu[0]["akcie"] ?>
				</div>
			</div>
		</div>
		<hr class="border-slate-100">
		<div class="flex justify-between items-center">
			<span class="text-slate-500 text-sm dark:text-slate-300">Účty</span>
		</div>
		<div class="grid <?php echo count($portfolio) <= 2 ? "grid-cols-1" : "grid-cols-2" ?> gap-4">
			<?php foreach ($portfolio as $key => $value) { ?>
				<div class="bg-slate-50 dark:bg-gray-900 p-4 rounded-lg space-y-1">
					<section class="flex items-center justify-between">
						<span
							class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-sm dark:bg-green-900 dark:text-green-300"><?php echo $value["cislouctu"]; ?></span>
						<span class="font-bold dark:text-slate-200 text-slate-800"><?php echo $value["druh"]; ?></span>
						<span
							class="font-bold dark:text-slate-300 text-slate-800"><?php echo $detailyFondu[0]["refmena"] ?></span>
					</section>
					<div class="flex justify-between items-center">
						<span class="text-sm dark:text-slate-400 -text-slate-500">Status</span>
						<span
							class="font-medium dark:text-slate-200 text-slate-800"><?php echo number_format($value["stav"], 2, '.', ' '); ?></span>
					</div>
					<div class="flex justify-between items-center">
						<span class="text-sm dark:text-slate-400 -text-slate-500">Dostupné</span>
						<span
							class="font-medium dark:text-slate-200 text-slate-800"><?php echo number_format($value["volne"], 2, '.', ' '); ?></span>
					</div>
					<div class="flex justify-between items-center">
						<span class="text-sm dark:text-slate-400 -text-slate-500">Rezervované</span>
						<span
							class="font-medium dark:text-slate-200 text-slate-800"><?php echo number_format($value["rezervacia"], 2, '.', ' '); ?></span>
					</div>
					<div class="flex justify-between items-center">
						<span class="text-sm dark:text-slate-400 -text-slate-500">Odchádzajúce</span>
						<span
							class="font-medium dark:text-slate-200 text-slate-800"><?php echo number_format($value["odchadzajuce"], 2, '.', ' '); ?></span>
					</div>
					<div class="flex justify-between items-center">
						<span class="text-sm dark:text-slate-400 -text-slate-500">Prichádzajúce</span>
						<span
							class="font-medium dark:text-slate-200 text-slate-800"><?php echo number_format($value["prichadzajuce"], 2, '.', ' '); ?></span>
					</div>
				</div>
			<?php } ?>
		</div>
	</div>
	<div class="bg-slate-50 dark:bg-gray-900 p-4 flex justify-end">
		<button id="client"
			onclick="runGoToPortfolio(event, '<?php echo $cislozmluvy; ?>', '<?php echo $id_fond; ?>', '<?php echo $clientID; ?>', '<?php echo $clientName; ?>', '<?php echo $clientPrieznaz; ?>', true)"
			class="text-primary-600 hover:text-primary-700 hover:underline font-medium text-sm">Zobraziť
			detaily</button>
	</div>
</div>
<div id="<?php echo $cislozmluvy; ?>" class="w-full hidden rounded-lg p-8 pb-5">
	<div class="flex w-full gap-4 rounded-t-lg border-b items-center sticky bg-white z-20 p-4" style="top: 3.7rem;">
		<div class="flex gap-4 items-center">
			<a href="#<?php echo $cislozmluvy; ?>">
				<h3 class="text-3xl font-bold dark:text-white">Portfólio <strong
						class="text-blue-500"><?php echo $cislozmluvy ?></strong></h3>
			</a>
		</div>
	</div>
	<div class="bg-white p-5 flex items-center justify-evenly">
		<section class="flex w-full justify-between items-center gap-8 pr-5" style="border-right: #1d9099 1px solid">
			<p class="text-sm w-full">Trhová hodnota k <b><?php echo $portfolioInfo[0]["datum"]; ?></b></p>
			<p class="bg-green-500 text-sm w-full text-white rounded-lg px-2 py-1">
				<?php echo number_format($portfolioInfo[0]["nav"], 2, '.', ' ') ?>
			</p>
		</section>
		<section class="flex w-full justify-center items-center gap-4 px-5" style="border-right: #1d9099 1px solid">
			<p>Dlhopisy</p>
			<strong><?php echo $detailyFondu[0]["dlhopisy"] ?></strong>
		</section>
		<section class="flex w-full justify-center items-center gap-4 px-5" style="border-right: #1d9099 1px solid">
			<p>Akcie</p>
			<strong><?php echo $detailyFondu[0]["akcie"] ?></strong>
		</section>
		<section class="flex w-full justify-center items-center gap-4 px-5" style="border-right: #1d9099 1px solid">
			<p>Referenčná mena: </p>
			<strong><?php echo $detailyFondu[0]["refmena"] ?></strong>
		</section>
	</div>

	<div class="relative overflow-x-auto">
		<table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
			<thead class="text-xs text-gray-700 bg-gray-400 uppercase dark:bg-gray-700 dark:text-gray-400">
				<tr>
					<th scope="col" class="px-6 py-3">
						Číslo účtu
					</th>
					<th scope="col" class="px-6 py-3">
						Druh
					</th>
					<th scope="col" class="px-6 py-3">
						Aktívum
					</th>
					<th scope="col" class="px-6 py-3">
						Skratka
					</th>
					<th scope="col" class="px-6 py-3">
						Stav
					</th>
					<th scope="col" class="px-6 py-3">
						Voľné
					</th>
					<th scope="col" class="px-6 py-3">
						Rezervované
					</th>
					<th scope="col" class="px-6 py-3">
						Odchadzajúce
					</th>
					<th scope="col" class="px-6 py-3">
						Prichadzajúce
					</th>
				</tr>
			</thead>
			<tbody>
				<?php foreach ($portfolio as $key => $value) { ?>
					<tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
						<th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
							<?php echo $value["cislouctu"]; ?>
						</th>
						<td class="px-6 py-4">
							<?php echo $value["druh"]; ?>
						</td>
						<td class="px-6 py-4">
							<?php echo $value["aktivum"]; ?>
						</td>
						<td class="px-6 py-4">
							<?php echo $value["skratka"]; ?>
						</td>
						<td class="px-6 py-4">
							<?php echo $value["stav"]; ?>
						</td>
						<td class="px-6 py-4">
							<?php echo $value["volne"]; ?>
						</td>
						<td class="px-6 py-4">
							<?php echo $value["rezervacia"]; ?>
						</td>
						<td class="px-6 py-4">
							<?php echo $value["odchadzajuce"]; ?>
						</td>
						<td class="px-6 py-4">
							<?php echo $value["prichadzajuce"]; ?>
						</td>
					</tr>
				<?php } ?>
			</tbody>
		</table>
	</div>
</div>
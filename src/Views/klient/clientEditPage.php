<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

$clientID = isset($matches[1]) ? intval($matches[1]) : null;

$clientInfo = Connection::getDataFromDatabase("SELECT * FROM podielnik WHERE podielnikid = $clientID", defaultDB);
$client = $clientInfo[1][0];
$pracovnikID = $client["userid"];

$statesRes = Connection::getDataFromDatabase("SELECT * FROM state", defaultDB);
$states = $statesRes[1];

$pobockaRes = Connection::getDataFromDatabase("SELECT * FROM pobocka", defaultDB);
$pobocka = $pobockaRes[1];

$clientTypesRes = Connection::getDataFromDatabase("SELECT * FROM typklienta", defaultDB);
$clientTypes = $clientTypesRes[1];

$usersRes = Connection::getDataFromDatabase("SELECT * FROM users", defaultDB);
$users = $usersRes[1];

$sektorRes = Connection::getDataFromDatabase("SELECT * FROM sektor_esa95 order by esa95_sektoroznacenie", defaultDB);
$sectors = $sektorRes[1];

$pracovnikRES = Connection::getDataFromDatabase("SELECT userid, username FROM users WHERE userid = $pracovnikID", defaultDB);
$pracovnik = $pracovnikRES[1][0];
?>

<form id="clientUpdate" class="relative w-full">
    <input type="hidden" class="hidden" name="podielnikid" value="<?php echo $client["podielnikid"] ?>">
    <div class="flex w-full justify-between  border-b border-black items-center">
        <div class="flex gap-4 mb-4 items-center">
            <a href="/klienti" class="hover:bg-gray-500 hover:text-white transition-all rounded-lg mt-1">
                <svg class="w-6 h-6" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                     width="24" height="24" fill="none" viewBox="0 0 24 24">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M5 12h14M5 12l4-4m-4 4 4 4"/>
                </svg>
            </a>
            <h3 class="text-3xl font-bold dark:text-white">Úprava klienta
                <strong class="text-blue-800"><?php echo $client["meno"] . " " . $client["prieznaz"] ?></strong></h3>
        </div>
        <div class="flex gap-4 items-center">
            <h3 class="text-xl mb-4 font-bold dark:text-white">Registračné číslo:
                <strong class="text-blue-800"><?php echo $clientID ?></strong></h3>
        </div>
    </div>
    <h3 class="text-lg mt-1 font-semibold dark:text-white">Osobné údaje</h3>
    <div class="flex mt-3 gap-8">
        <div class="w-full bg-white rounded-xl p-2 px-4 flex flex-col gap-1 shadow-md">
            <div class="flex items-center mb-1">
                <label for="prieznaz" class="block mb-2 w-full text-sm font-semibold text-gray-900 dark:text-white">Priezvisko/Názov: </label>
                <input type="text" id="prieznaz" value="<?php echo $client['prieznaz']; ?>" name="prieznaz"
                       class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 px-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                       placeholder="Priezvisko" required/>
            </div>
            <div class="flex items-center mb-1">
                <label for="meno"
                       class="block mb-2 w-full text-sm font-semibold text-gray-900 dark:text-white">Meno: </label>
                <input type="text" id="meno" value="<?php echo $client['meno']; ?>" name="meno"
                       class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 px-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                       placeholder="Meno"/>
            </div>
            <div class="flex items-center">
                <p class="flex text-sm w-full justify-between">
                    <span class="font-semibold">Tituly: </span></p>
                <section class="flex mr-1 gap-2 w-full justify-between">
                    <div class="flex items-center mb-1">
                        <input type="text" id="titulpred" value="<?php echo $client['titulpred']; ?>" name="titulpred"
                               class="bg-gray-50 w-full border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-0.5 px-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                               placeholder="Titul pred menom"/>
                    </div>
                    <div class="flex items-center mb-1">
                        <input type="text" id="titulza" value="<?php echo $client['titulza']; ?>" name="titulza"
                               class="bg-gray-50 w-full border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-0.5 px-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                               placeholder="Titul za menom"/>
                    </div>
                </section>
            </div>
            <div class="flex gap-3 items-center mb-1">
                <label for="sex"
                       class="block text-sm w-full font-semibold text-gray-900 dark:text-white">Typ klienta: </label>
                <select id="typklienta" name="typklienta"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                    <?php
                    foreach ($clientTypes as $clientType) { ?>
                        <option value="<?php echo $clientType['typklienta']; ?>" <?php if ($client['typklienta'] === $clientType['typklienta']) { ?> selected="selected" <?php } ?>><?php echo $clientType["popis"] ?></option>
                    <?php } ?>
                </select>
            </div>
            <div class="flex items-center mb-1">
                <label for="fpo" class="block mb-2 w-full text-sm font-semibold text-gray-900 dark:text-white">F/P
                    osoba: </label>
                <input type="text" id="fpo" value="<?php echo $client['fpo']; ?>" name="fpo"
                       class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 px-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                       placeholder="FPO" required/>
            </div>
            <div class="flex items-center mb-1">
                <label for="druhid" class="block mb-2 w-full text-sm font-semibold text-gray-900 dark:text-white">Druh
                    dokladu: </label>
                <input type="text" id="druhid" value="<?php echo $client['druhid']; ?>" name="druhid"
                       class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 px-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                       placeholder="Druh dokladu"/>
            </div>
            <div class="flex items-center mb-1">
                <label for="bu" class="block mb-2 w-full text-sm font-semibold text-gray-900 dark:text-white">Bežný
                    účet: </label>
                <input type="text" id="bu" value="<?php echo $client['bu']; ?>" name="bu"
                       class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 px-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                       placeholder="Číslo bežného účtu"/>
            </div>
        </div>
        <div class="w-full bg-white rounded-xl p-2 px-4 flex flex-col gap-1 shadow-md">
            <div class="flex gap-3 items-center mb-1">
                <label for="pohlavie"
                       class="block mb-2 text-sm w-full font-semibold text-gray-900 dark:text-white">Pohlavie</label>
                <select id="pohlavie" value="<?php echo($client["pohlavie"] === "z" ? "z" : "m") ?>" name="pohlavie"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                    <option value="m" <?php if ($client['pohlavie'] === "m") { ?> selected="selected" <?php } ?>>Muž
                    </option>
                    <option value="z" <?php if ($client['pohlavie'] === "z") { ?> selected="selected" <?php } ?>>Žena
                    </option>
                </select>
            </div>
            <div class="flex items-center mb-1">
                <label for="rcico" class="block mb-2 w-full text-sm font-semibold text-gray-900 dark:text-white">Rodné
                    číslo: </label>
                <input type="text" id="rcico" value="<?php echo $client['rcico']; ?>" name="rcico"
                       class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 px-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                       placeholder="Rodné číslo" required/>
            </div>
            <div class="flex items-center mb-1">
                <label for="datum_narodenia"
                       class="block mb-2 w-full text-sm font-semibold text-gray-900 dark:text-white">Dátum
                    narodenia: </label>
                <input type="date" id="datum_narodenia" value="<?php echo $client["datum_narodenia"] ?>"
                       name="datum_narodenia"
                       class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 px-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                       placeholder="Dátum narodenia"/>
            </div>
            <div class="flex items-center mb-1">
                <label for="cisloid" class="block mb-2 w-full text-sm font-semibold text-gray-900 dark:text-white">Číslo
                    dokladu: </label>
                <input type="text" id="cisloid" value="<?php echo $client["cisloid"]; ?>" name="cisloid"
                       class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 px-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                       placeholder="Číslo dokladu"/>
            </div>
            <div class="flex items-center mb-1">
                <label for="stateid"
                       class="block mb-2 w-full text-sm font-semibold text-gray-900 dark:text-white">Štát: </label>
                <select id="stateid" name="stateid"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                    <?php
                    foreach ($states as $state) { ?>
                        <option value="<?php echo $state['stateid']; ?>" <?php if ($client['stateid'] === $state['stateid']) { ?> selected="selected" <?php } ?>><?php echo $state["stateall"] ?></option>
                    <?php } ?>
                </select>
            </div>
            <div class="flex items-center mb-1">
                <label for="sektor_esa95" class="block mb-2 w-full text-sm font-semibold text-gray-900 dark:text-white">Sektor: </label>
                <select id="sektor_esa95" value="<?php echo $client["sektor_esa95"] ?>" name="sektor_esa95"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                    <?php
                    foreach ($sectors as $sector) { ?>
                        <option value="<?php echo $sector['esa95_sektorid']; ?>" <?php if ($client['sektor_esa95'] === $state['esa95_sektorid']) { ?> selected="selected" <?php } ?>><?php echo $sector["esa95_sektorpopis"] ?></option>
                    <?php } ?>
                </select>
            </div>
        </div>
    </div>

    <div class="">
        <div class="flex gap-10 w-full">
            <h3 class="text-lg mt-3 w-full font-bold dark:text-white">Kontaktné údaje</h3>
            <h3 class="text-lg mt-3 w-full px-8 font-bold dark:text-white">AML</h3>
        </div>
        <div class="flex mt-3 gap-8">
            <div class="flex w-full mt-3 gap-8">
                <div class="w-full bg-white rounded-xl p-2 px-4 flex flex-col gap-1 shadow-md">
                    <div class="mb-1">
                        <label for="address"
                               class="block mb-2 text-sm w-1/2 font-semibold text-gray-900 dark:text-white">Ulica: </label>
                        <input type="text" id="address" value="<?php echo $client["address"]; ?>" name="address"
                               class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 px-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                               placeholder="Ulica"/>
                    </div>
                    <div class="mb-1">
                        <label for="city" class="block mb-2 text-sm w-1/2 font-semibold text-gray-900 dark:text-white">Mesto: </label>
                        <input type="text" id="city" value="<?php echo $client["city"]; ?>" name="city"
                               class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 px-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                               placeholder="Mesto"/>
                    </div>
                    <div class="mb-1">
                        <label for="postalcode"
                               class="block mb-2 text-sm w-1/2 font-semibold text-gray-900 dark:text-white">PSČ: </label>
                        <input type="text" id="postalcode" value="<?php echo $client["postalcode"]; ?>"
                               name="postalcode"
                               class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 px-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                               placeholder="PSČ"/>
                    </div>
                </div>
                <div class="w-full bg-white rounded-xl p-2 px-4 flex flex-col gap-1 shadow-md">
                    <div class="mb-1">
                        <label for="kontaktphonenumber"
                               class="block mb-2 text-sm w-1/2 font-semibold text-gray-900 dark:text-white">Telefón: </label>
                        <input type="text" id="kontaktphonenumber" value="<?php echo $client["kontaktphonenumber"]; ?>"
                               name="kontaktphonenumber"
                               class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 px-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                               placeholder="Telefón"/>
                    </div>
                    <div class="mb-1">
                        <label for="kontaktmobilnumber"
                               class="block mb-2 text-sm w-1/2 font-semibold text-gray-900 dark:text-white">Mobil: </label>
                        <input type="text" id="kontaktmobilnumber" value="<?php echo $client["kontaktphonenumber"]; ?>"
                               name="kontaktmobilnumber"
                               class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 px-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                               placeholder="Mobil"/>
                    </div>
                    <div class="mb-1">
                        <label for="kontaktemail"
                               class="block mb-2 text-sm w-1/2 font-semibold text-gray-900 dark:text-white">Email: </label>
                        <input type="text" id="kontaktemail" value="<?php echo $client["kontaktemail"]; ?>"
                               name="kontaktemail"
                               class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 px-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                               placeholder="Email"/>
                    </div>
                </div>
            </div>
            <div class="w-full flex-col flex justify-between">
                <div class="flex mt-3 gap-8">
                    <div class="w-full bg-white rounded-xl p-2 px-4 flex flex-col gap-1 shadow-md">
                        <div class="flex justify-between">
                            <strong>Politicky exponovaná
                                osoba</strong>
                            <div class="flex items-center">
                                <input <?php if ($client["is_pep"]) { ?> checked="checked" <?php } ?> id="is_pep"
                                                                                                      name="is_pep"
                                                                                                      type="checkbox"
                                    <?php if ($client["is_pep"]) { ?> value="1" <?php } else { ?> value="0" <?php } ?>
                                                                                                      class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                                <label for="is_pep"
                                       class="ms-2 text-sm font-medium text-gray-900 dark:text-gray-300">Áno</label>
                            </div>
                            </span>
                        </div>
                        <div class="flex justify-between items-center mb-1">
                            <label for="uroven_aml_rizika"
                                   class="block text-sm w-full font-semibold text-gray-900 dark:text-white">Úroveň
                                AML rizika: </label>
                            <select id="uroven_aml_rizika" value="<?php echo $client["uroven_aml_rizika"] ?>"
                                    name="uroven_aml_rizika" required
                                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                                <option value="1" <?php if ($client['uroven_aml_rizika'] === 1) { ?>
                                    selected="selected" <?php } ?>>
                                    Nízke
                                </option>
                                <option value="2" <?php if ($client['uroven_aml_rizika'] === 2) { ?> selected="selected" <?php } ?>>
                                    Stredné
                                </option>
                                <option value="3" <?php if ($client['uroven_aml_rizika'] === 3) { ?> selected="selected" <?php } ?>>
                                    Vysoké
                                </option>
                            </select>
                        </div>
                    </div>
                </div>
                <h3 class="text-lg mt-3 font-semibold dark:text-white">Služby tretích strán</h3>
                <div class="flex mb-32 mt-3 gap-8">
                    <div class="w-full bg-white rounded-xl p-2 px-4 flex flex-col gap-1 shadow-md">
                        <div class="flex justify-between">
                            <strong>Refundácia dane</strong>
                            <div class="flex">
                                <div class="flex items-center me-4">
                                    <input id="refundaciadane0" type="radio" name="refundaciadane"
                                        <?php if ($client["refundaciadane"] !== null || $client["refundaciadane"] === true) { ?> checked="checked" <?php } ?>
                                           value="1"
                                           class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                                    <label for="refundaciadane0"
                                           class="ms-2 text-sm font-medium text-gray-900 dark:text-gray-300">Áno</label>
                                </div>
                                <div class="flex items-center me-4">
                                    <input id="refundaciadane1" type="radio" name="refundaciadane"
                                        <?php if ($client["refundaciadane"] === null || $client["refundaciadane"] === false) { ?> checked="checked" <?php } ?>
                                           value="0"
                                           class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                                    <label for="refundaciadane1"
                                           class="ms-2 text-sm font-medium text-gray-900 dark:text-gray-300">Nie</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <h3 class="text-lg mt-3 font-semibold dark:text-white">Výpisy</h3>
    <div class="flex mb-32 mt-3 gap-8">
        <div class="w-full bg-white rounded-xl p-2 px-4 flex flex-col gap-1 shadow-md">
            <div class="flex gap-3 items-center mb-1">
                <label for="vypisfrekvencia"
                       class="block text-sm w-full font-semibold text-gray-900 dark:text-white">Frekvencia</label>
                <select id="vypisfrekvencia" value="<?php echo $client["vypisfrekvencia"] ?>" name="vypisfrekvencia"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                    <option value="1" <?php if ($client['vypisfrekvencia'] === 1) { ?> selected="selected" <?php } ?>>
                        Mesačne
                    </option>
                    <option value="4" <?php if ($client['vypisfrekvencia'] === 4) { ?> selected="selected" <?php } ?>>
                        Štvrťročne
                    </option>
                    <option value="6" <?php if ($client['vypisfrekvencia'] === 6) { ?> selected="selected" <?php } ?>>
                        Polročne
                    </option>
                    <option value="12" <?php if ($client['vypisfrekvencia'] === 12) { ?> selected="selected" <?php } ?>>
                        Ročne
                    </option>
                </select>
            </div>
            <div class="flex gap-3 items-center mb-1">
                <label for="gfi"
                       class="block text-sm w-full font-semibold text-gray-900 dark:text-white">Je súčasťou
                    GFI</label>
                <select id="gfi" value="<?php echo $client["gfi"] ?>" name="gfi"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                    <option value="1" <?php if ($client['gfi'] === 1) { ?> selected="selected" <?php } ?>>
                        Áno
                    </option>
                    <option value="0" <?php if ($client['gfi'] === 0) { ?> selected="selected" <?php } ?>>
                        Nie
                    </option>
                    <option value="2" <?php if ($client['gfi'] === 2) { ?> selected="selected" <?php } ?>>
                        Spriaznená osoba
                    </option>
                </select>
            </div>
        </div>
        <div class="w-full bg-white rounded-xl p-2 px-4 flex flex-col gap-1 shadow-md">
            <div class="flex gap-3 items-center mb-1">
                <label for="dan_domicil"
                       class="block text-sm w-full font-semibold text-gray-900 dark:text-white">Daňový
                    domicil</label>
                <select id="dan_domicil" name="dan_domicil"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                    <?php
                    foreach ($states as $state) { ?>
                        <option value="<?php echo $state['stateid']; ?>" <?php if ($client['dan_domicil'] === $state['stateid']) { ?> selected="selected" <?php } ?>><?php echo $state["stateall"] ?></option>
                    <?php } ?>
                </select>
            </div>
            <div class="flex gap-3 items-center mb-1">
                <label for="typzdanenia"
                       class="block text-sm w-full font-semibold text-gray-900 dark:text-white">Typ zdanenia</label>
                <select id="typzdanenia" value="<?php echo $client["typ_zdanenia"] ?>" name="typzdanenia"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                        required>
                    <option value="FO" <?php if ($client['typ_zdanenia'] === "FO") { ?> selected="selected" <?php } ?>>
                        FO
                    </option>
                    <option value="PO" <?php if ($client['typ_zdanenia'] === "PO") { ?> selected="selected" <?php } ?>>
                        PO
                    </option>
                    <option value="NO" <?php if ($client['typ_zdanenia'] === "NO") { ?> selected="selected" <?php } ?>>
                        NO
                    </option>
                </select>
            </div>
        </div>
    </div>
    <h3 class="text-lg mt-3 font-semibold dark:text-white">Predajné miesto</h3>
    <div class="flex mb-32 mt-3 gap-8">
        <div class="w-full bg-white rounded-xl p-2 px-4 flex flex-col gap-1 shadow-md">
            <div class="flex gap-3 items-center mb-1">
                <label for="pobocka"
                       class="block text-sm w-full font-semibold text-gray-900 dark:text-white">Priradený
                    pracovník</label>
                <select id="pobocka" value="" name="pobocka"
                        class="bg-gray-300 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1"
                        disabled="disabled">
                    <?php
                    foreach ($pobocka as $pobo) { ?>
                        <option value="<?php echo $pobo['pobockaid']; ?>"><?php echo $pobo["pobockaname"] . " " . $pobo["address"] . ", " . $pobo["postalcode"] ?></option>
                    <?php } ?>
                </select>
            </div>
            <div class="flex gap-3 items-center mb-1">
                <label for="userid"
                       class="block text-sm w-full font-semibold text-gray-900 dark:text-white">Priradený
                    pracovník</label>
                <select id="userid" name="userid"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                        required>
                    <?php
                    foreach ($users as $user) { ?>
                        <option value="<?php echo $user['userid']; ?>" <?php if ($client["userid"] === $user['userid']) { ?> selected="selected" <?php
                        } ?>><?php echo $user["username"]; ?></option>
                    <?php } ?>
                </select>
            </div>
        </div>
        <div class="w-full bg-white rounded-xl p-2 px-4 flex flex-col gap-1 shadow-md">
            <div class="flex mb-1">
                <label for="datetimeagreement"
                       class="block mb-2 text-sm w-1/2 font-semibold text-gray-900 dark:text-white">Dátum
                    podania: </label>
                <input type="date" id="datetimeagreement" value="<?php echo $client["datetimeagreement"]; ?>"
                       name="datetimeagreement"
                       class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 px-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                       placeholder="Mobil"/>
            </div>
            <div class="flex mb-1">
                <label for="timeagreement"
                       class="block mb-2 text-sm w-1/2 font-semibold text-gray-900 dark:text-white">Čas
                    podania: </label>
                <input type="time" id="timeagreement" value="<?php echo $client["timeagreement"]; ?>"
                       name="timeagreement"
                       class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 px-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                       placeholder="Čas podania"/>
            </div>
        </div>
    </div>
    <div class="flex sticky z-40 mt-6 shadow-md border border-gray-200 rounded-lg px-5 w-full bg-white p-3 items-center" style="bottom: 2rem;">
        <button type="submit"
                class="focus:outline-none text-white bg-green-700 hover:bg-green-800 focus:ring-4 focus:ring-green-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-green-600 dark:hover:bg-green-700 dark:focus:ring-green-800">
            Aktualizovať
        </button>
    </div>
</form>
<script src="/src/assets/js/klienti/edit.js"></script>
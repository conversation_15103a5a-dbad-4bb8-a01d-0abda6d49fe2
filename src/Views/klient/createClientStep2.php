<?php
require "/home/<USER>/www/src/lib/connection.php";
require "/home/<USER>/www/conf/settings.php";
$clientID = isset($matches[1]) ? intval($matches[1]) : null;

if($clientID === null){
  header("Location: /");
}


?>
<div class="flex bg-white px-4 border justify-between border-gray-200 rounded-lg shadow-sm items-center">
  <div class="nevime flex items-center gap-2">
    <div class="flex mb-1">
    <a href="/klienti" class="hover:bg-gray-500 hover:text-white transition-all rounded-lg mt-1">
                <svg class="w-6 h-6" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                     width="24" height="24" fill="none" viewBox="0 0 24 24">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M5 12h14M5 12l4-4m-4 4 4 4"/>
                </svg>
      </a>
    </div>
    <ol class="flex items-center w-full p-3 space-x-2 text-sm font-medium text-center text-gray-500 dark:text-gray-400 sm:text-base dark:bg-gray-800 dark:border-gray-700 sm:p-4 sm:space-x-4 rtl:space-x-revers">
      <li class="flex items-center">
        <span class="flex items-center justify-center w-5 h-5 me-2 text-xs border-2 flex items-center justify-center font-bold border-green-500 text-green-500 rounded-full shrink-0">
            1
          </span><span class="text-green-500 font-bold">Osobné údaje</span>
        <svg class="w-3 h-3 ms-2 sm:ms-4 rtl:rotate-180" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 12 10">
            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m7 9 4-4-4-4M1 9l4-4-4-4"/>
        </svg>
      </li>
      <li class="flex items-center text-blue-600">
        <span class="flex items-center justify-center w-5 h-5 me-2 text-xs border border-blue-600 rounded-full shrink-0 dark:border-gray-400">
            2
          </span>Investičný dotazník</span>
        <svg class="w-3 h-3 ms-2 sm:ms-4 rtl:rotate-180" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 12 10">
            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m7 9 4-4-4-4M1 9l4-4-4-4"/>
        </svg>
      </li>
      <li class="flex items-center">
        <span class="flex items-center justify-center w-5 h-5 me-2 text-xs border border-gray-500 rounded-full shrink-0 dark:border-gray-400">
            3
          </span>
        Zriadenie portfólia
      </li>
    </ol>
  </div>
  <div class="flex items-center gap-4">
    <strong>ID zákazníka: </strong>
    <h2 "><?php echo $clientID ?></h2>
  </div>
</div>
<form id="dotaznickovyForm">
  <input type="hidden" name="user_id" id="user_id" value="<?php echo $clientID; ?>"/>
  <input type="hidden" name="redirect" id="redirect" value="true"/>
        <div class="flex mt-8 items-center justify-center w-full">
    <label id="labelFileToUpload" for="fileToUpload" class="flex flex-col items-center justify-center w-full h-64 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 dark:hover:bg-bray-800 dark:bg-gray-700 hover:bg-gray-100 dark:border-gray-600 dark:hover:border-gray-500 dark:hover:bg-gray-600">
      <div class="flex flex-col items-center justify-center pt-5 pb-6">
            <svg id="pdfIcon" class="w-8 hidden h-8 mb-4 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
              <path fill-rule="evenodd" d="M9 2.221V7H4.221a2 2 0 0 1 .365-.5L8.5 2.586A2 2 0 0 1 9 2.22ZM11 2v5a2 2 0 0 1-2 2H4a2 2 0 0 0-2 2v7a2 2 0 0 0 2 2 2 2 0 0 0 2 2h12a2 2 0 0 0 2-2 2 2 0 0 0 2-2v-7a2 2 0 0 0-2-2V4a2 2 0 0 0-2-2h-7Zm-6 9a1 1 0 0 0-1 1v5a1 1 0 1 0 2 0v-1h.5a2.5 2.5 0 0 0 0-5H5Zm1.5 3H6v-1h.5a.5.5 0 0 1 0 1Zm4.5-3a1 1 0 0 0-1 1v5a1 1 0 0 0 1 1h1.376A2.626 2.626 0 0 0 15 15.375v-1.75A2.626 2.626 0 0 0 12.375 11H11Zm1 5v-3h.375a.626.626 0 0 1 .625.626v1.748a.625.625 0 0 1-.626.626H12Zm5-5a1 1 0 0 0-1 1v5a1 1 0 1 0 2 0v-1h1a1 1 0 1 0 0-2h-1v-1h1a1 1 0 1 0 0-2h-2Z" clip-rule="evenodd"/>
            </svg>
            <svg id="uploadIcon" class="w-8 h-8 mb-4 text-gray-500 dark:text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 16">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2"/>
            </svg>
            <p id="futureFile" class="mb-2 text-sm text-gray-500 dark:text-gray-400"><span class="font-semibold">Klikni pre nahratie súboru</span></p>
            <p id="deleteText" class="text-xs text-gray-500 dark:text-gray-400">PNG, JPG alebo PDF (MAX. 1MB)</p>
            <p id="pdfUploadError" class="text-xs text-red-500"></p>
        </div>
        <input id="fileToUpload" type="file" name="fileToUpload" class="hidden" />
    </label>
</div>
<label for="first_name" class="block mb-2 text-sm mt-6 font-medium text-gray-900 dark:text-white">Dátum vyhotovenia:</label>
  <input type="date" id="date"i name="date" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5" required />
  <button type="submit" class="text-white bg-gradient-to-r from-green-400 via-green-500 to-green-600 hover:bg-gradient-to-br focus:ring-4 focus:outline-none focus:ring-green-300 font-bold mt-6 rounded-lg text-sm px-5 py-2.5 text-center me-2 mb-2">Uložiť a prejsť na další krok</button>
</form>
<script src="/src/assets/js/klienti/dotaznik.js"></script>


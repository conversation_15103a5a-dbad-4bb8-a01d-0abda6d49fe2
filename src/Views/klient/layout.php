<?php require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
$udaje = Connection::getDataFromDatabase("SELECT p.*, po.cislozmluvy as cislozmluvy FROM podielnik p LEFT JOIN portfolio po ON po.podielnikid = p.podielnikid WHERE archiv = 'f' ORDER BY prieznaz ASC", defaultDB);
$skupiny = Connection::getDataFromDatabase("SELECT DISTINCT skupina FROM podielnik WHERE skupina IS NOT NULL ORDER BY skupina ASC", defaultDB);
$mesta = Connection::getDataFromDatabase("SELECT DISTINCT city FROM podielnik WHERE city IS NOT NULL ORDER BY city ASC", defaultDB);

$podielnici = [];
foreach ($udaje[1] as $key => $value) {
    if ($podielnici[$value["podielnikid"]]["podielnikid"] == $value["podielnikid"]) {
        $podielnici[$value["podielnikid"]]["cislozmluvy"][] = $value["cislozmluvy"];
    } else {
        $podielnici[$value["podielnikid"]] = [
            "podielnikid" => $value["podielnikid"],
            "meno" => $value["meno"],
            "prieznaz" => $value["prieznaz"],
            "cislozmluvy" => [$value["cislozmluvy"]],
            "titulpred" => $value["titulpred"],
            "titulza" => $value["titulza"],
            "kontaktcity" => $value["kontaktcity"],
            "city" => $value["city"],
            "address" => $value["address"]
        ];
    }
}

?>
<?php include "/home/<USER>/www/src/Components/klient/clientsTable.php" ?>
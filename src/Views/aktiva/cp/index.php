<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

?>
<section class="py-3 px-5" style="margin-bottom: 8rem">
      <h3 class="text-4xl mb-4 font-bold text-gray-900 dark:text-white">Zoznam cenn<PERSON><PERSON> papierov</h3>
      <div class="mx-auto">
            <form hx-post="/api/cp/getFilteredData" id="searchFrom" hx-target="tbody"
                  class="flex mb-2 w-full items-center">
                  <div class="relative w-full bg-white shadow-md dark:bg-gray-800 sm:rounded-lg">
                        <div
                              class="flex flex-col w-full items-start justify-between p-4 space-y-3 md:flex-row md:space-y-0 md:space-x-4">
                              <div class="w-full md:w-1/2">
                                    <label for="simple-search" class="sr-only">Vyh<PERSON>ad<PERSON><PERSON><PERSON></label>
                                    <div class="relative flex items-center w-full">
                                          <div
                                                class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                                <svg aria-hidden="true" class="w-5 h-5 text-gray-500 dark:text-gray-400"
                                                      fill="currentColor" viewbox="0 0 20 20"
                                                      xmlns="http://www.w3.org/2000/svg">
                                                      <path fill-rule="evenodd"
                                                            d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                                                            clip-rule="evenodd" />
                                                </svg>
                                          </div>
                                          <input type="text" id="searchBar" name="search"
                                                class="block w-full p-2 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                                placeholder="Vyhľadávanie...">
                                          <div id="resetSearch"
                                                class="absolute hidden text-gray-500 hover:text-gray-900 transition-all cursor-pointer"
                                                style="right: .7rem">
                                                <svg class="w-4 h-4" aria-hidden="true"
                                                      xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                      fill="currentColor" viewBox="0 0 24 24">
                                                      <path fill-rule="evenodd"
                                                            d="M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10-4.477 10-10 10S2 17.523 2 12Zm7.707-3.707a1 1 0 0 0-1.414 1.414L10.586 12l-2.293 2.293a1 1 0 1 0 1.414 1.414L12 13.414l2.293 2.293a1 1 0 0 0 1.414-1.414L13.414 12l2.293-2.293a1 1 0 0 0-1.414-1.414L12 10.586 9.707 8.293Z"
                                                            clip-rule="evenodd" />
                                                </svg>

                                          </div>
                                    </div>
                              </div>
                              <div
                                    class="flex flex-col items-stretch justify-end flex-shrink-0 w-full space-y-2 md:w-auto md:flex-row md:space-y-0 md:items-center md:space-x-3">
                                    <a href="/aktiva/create-new" type="button"
                                          class="flex items-center justify-center px-4 py-2 text-sm font-medium text-white rounded-lg bg-blue-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 focus:outline-none dark:focus:ring-primary-800">
                                          <svg class="h-3.5 w-3.5 mr-2" fill="currentColor" viewbox="0 0 20 20"
                                                xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                                                <path clip-rule="evenodd" fill-rule="evenodd"
                                                      d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" />
                                          </svg>
                                          Pridať cenný papier
                                    </a>
                                    <div class="flex items-center w-full space-x-3 md:w-auto">
                                          <select id="archived" name="archived"
                                                class="border border-gray-300 text-gray-900 bg-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                                                <option value="1">Zobraziť aj archivované</option>
                                                <option value="0" selected>Nezobrazovať archivované</option>
                                          </select>
                                          <button id="filterDropdownButton" data-dropdown-toggle="filterDropdown"
                                                class="flex items-center justify-center w-full px-4 py-2 text-sm font-medium text-gray-900 bg-white border border-gray-200 rounded-lg md:w-auto focus:outline-none hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                                                type="button">
                                                <svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true"
                                                      id="filterIcon" class="w-4 h-4 mr-2 text-gray-400"
                                                      viewbox="0 0 20 20" fill="currentColor">
                                                      <path fill-rule="evenodd"
                                                            d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z"
                                                            clip-rule="evenodd" />
                                                </svg>
                                                <span id="filterText">Filtrovať</span>
                                                <svg class="-mr-1 ml-1.5 w-5 h-5" fill="currentColor"
                                                      viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"
                                                      aria-hidden="true">
                                                      <path clip-rule="evenodd" fill-rule="evenodd"
                                                            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" />
                                                </svg>
                                          </button>
                                          <div id="filterDropdown" style="min-width: 20vw;"
                                                class="z-10 hidden p-3 bg-white dark:bg-gray-800 rounded-lg shadow">
                                                <div class="w-full">
                                                      <ul class="space-y-2 text-sm" aria-labelledby="dropdownDefault">
                                                            <li class="flex items-center">
                                                                  <label for="bonds"
                                                                        class="ml-2 text-sm font-medium text-gray-900 flex items-center gap-2 dark:text-gray-100">
                                                                        <input id="bonds" name="bonds" type="checkbox"
                                                                              value="Bonds"
                                                                              class="filter-checkbox w-4 h-4 bg-gray-100 border-gray-300 rounded text-primary-600 focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-700 focus:ring-2 dark:bg-gray-600 dark:border-gray-500" />
                                                                        <span>Dlhopisy</span>
                                                                  </label>
                                                            </li>
                                                            <li class="flex items-center">
                                                                  <label for="shares"
                                                                        class="ml-2 text-sm font-medium text-gray-900 flex items-center gap-2 dark:text-gray-100">
                                                                        <input id="shares" name="shares" type="checkbox"
                                                                              value="Shares"
                                                                              class="filter-checkbox w-4 h-4 bg-gray-100 border-gray-300 rounded text-primary-600 focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-700 focus:ring-2 dark:bg-gray-600 dark:border-gray-500" />
                                                                        <span>Akcie</span>
                                                                  </label>
                                                            </li>
                                                            <li class="flex items-center">
                                                                  <label for="shares"
                                                                        class="ml-2 text-sm font-medium text-gray-900 flex items-center gap-2 dark:text-gray-100">
                                                                        <input id="depo" name="depo" type="checkbox"
                                                                              value="Depo"
                                                                              class="filter-checkbox w-4 h-4 bg-gray-100 border-gray-300 rounded text-primary-600 focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-700 focus:ring-2 dark:bg-gray-600 dark:border-gray-500" />
                                                                        <span>Depozitné certifikáty</span>
                                                                  </label>
                                                            </li>
                                                            <li class="flex items-center">
                                                                  <label for="fonds"
                                                                        class="ml-2 text-sm font-medium text-gray-900 flex items-center gap-2 dark:text-gray-100">
                                                                        <input id="fonds" name="fonds" type="checkbox"
                                                                              value="Fonds"
                                                                              class="filter-checkbox w-4 h-4 bg-gray-100 border-gray-300 rounded text-primary-600 focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-700 focus:ring-2 dark:bg-gray-600 dark:border-gray-500" />
                                                                        <span>Podielové fondy</span>
                                                                  </label>
                                                            </li>
                                                      </ul>
                                                </div>
                                                <div class="w-full flex items-center justify-end">
                                                      <button id="resetFilter" type="button"
                                                            class="px-3 hidden py-2 text-xs font-medium text-center text-white bg-blue-700 rounded-lg hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                                                            Zrušiť filter <b>X</b></button>
                                                </div>
                                          </div>
                                    </div>
                              </div>
                        </div>
                  </div>
                  <button id="submitFilterForm" type="submit" class="hidden h-0 w-0">S</button>
            </form>
            <div class="overflow-x-visible">
                  <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                        <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                              <tr>
                                    <th scope="col" class="px-4 py-3">ISIN</th>
                                    <th scope="col" class="px-4 py-3">Názov</th>
                                    <th scope="col" class="px-4 py-3">Skratka</th>
                                    <th scope="col" class="px-4 py-3">Typ</th>
                                    <th scope="col" class="px-4 py-3 archivedCol hidden">Archivovaný</th>
                                    <th scope="col" class="px-4 py-3">Možnosti</th>
                              </tr>
                        </thead>
                        <tbody id="CPCKA">
                              <?php include "src/Controllers/aktiva/filteredData.php"; ?>
                        </tbody>
                  </table>
            </div>
      </div>
</section>
<section id="toast"></section>
<script src="/src/assets/js/aktiva/cp/table.js"></script>
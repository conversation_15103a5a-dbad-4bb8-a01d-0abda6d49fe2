<?php
require "/home/<USER>/www/src/lib/connection.php";
require "/home/<USER>/www/conf/settings.php";

$menaRes = Connection::getDataFromDatabase("SELECT * FROM menadb ORDER BY poradie ASC", defaultDB);
$meny = $menaRes[1];

$emitentRes = Connection::getDataFromDatabase("SELECT * FROM equityemitent", defaultDB);
$emitent = $emitentRes[1];

$krajinyRes = Connection::getDataFromDatabase("SELECT * FROM state", defaultDB);
$krajiny = $krajinyRes[1];

$sektorRes = Connection::getDataFromDatabase("SELECT * FROM sektor_esa95", defaultDB);
$sektor = $sektorRes[1];

$druhRes = Connection::getDataFromDatabase("SELECT * FROM equitydruh", defaultDB);
$druheqid = $druhRes[1];


?>
<h3 class="text-3xl mb-4 font-bold dark:text-white">Tvorba nového cenného papiera</h3>
<form id="createCP">
	<div class="mb-4">
		<label for="countries" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Typ cenného papiera</label>
    <select id="cp_type" name="cp_type" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
      <option value="Bonds">Dlhopis</option>
      <option value="Shares">Akcia</option>
      <option value="Depo">Depozitný certifikát</option>
      <option value="Fonds">Podielové fondy</option>
    </select>
	</div>
    <div class="grid gap-6 mb-6 md:grid-cols-2">
        <div>
            <label for="cpnaz" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Názov <small class="text-red-500">*</small></label>
            <input type="text" id="cpnaz" name="cpnaz" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full" required />
      </div>
      <div>
            <label for="poplatokcp" class="block mb-2 text-sm font-medium text-gray-900">Kategória</label>
            <select id="poplatokcp" name="poplatokcp" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full">
                <option value="1">1 - Tuzemské</option>
                <option value="2">2 - EB, US, DE, UK, ČR, BE, LU</option>
                <option value="3">3 - OECD</option>
                <option value="4">4 - Ostatné</option>
              </select>
      </div>
      <section class="flex gap-6 items-center">
          <div class="w-full">
            <label for="cpnazskratka" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Skratka <small class="text-red-500">*</small></label>
            <input type="text" id="cpnazskratka" name="cpnazskratka" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full" required />
        </div>  
        <div class="w-full">
            <label for="isin" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">ISIN <small class="text-red-500">*</small></label>
            <input type="text" id="isin" name="isin" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full" required />
        </div>
      </section>
      <div>
                    <label for="currencynom" class="block mb-2 text-sm font-medium text-gray-900">Mena</label>
                    <select id="currencynom" name="currencynom" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full">
        <?php foreach ($meny as $key => $value){?>
                          <option value="<?php echo $value['mena'];?>"><?php echo $value["mena"] ?></option>
                              <?php } ?>
      </select>
              </div>
    <div>
                          <label for="emitent" class="block mb-2 text-sm font-medium text-gray-900">Emitent</label>
                    <div class="flex gap-2 items-center">
                          <select id="emitent" name="emitent" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full">
                <?php foreach ($emitent as $key => $value){?>
                                  <option value="<?php echo $value['emitentid'];?>"><?php echo $value["emitentnazov"] ?></option>
                                      <?php } ?>
        </select>
      <button data-modal-target="addEmitent" data-modal-toggle="addEmitent" type="button" class="text-white bg-green-700 hover:bg-blue-800 transition-all focus:ring-4 focus:outline-none p-1 focus:ring-blue-300 font-medium rounded-lg text-sm text-center inline-flex items-center">
          <svg class="w-6 h-6" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
      <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 7.757v8.486M7.757 12h8.486M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"/>
    </svg>
          <span class="sr-only">Pridať účet</span>
          </button>

        </div>
    </div>
    <div id="sektorWrapper">
                                <label for="sektorid" class="block mb-2 text-sm font-medium text-gray-900">Sektor</label>
                                <select id="sektorid" name="sektorid" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full">
                        <?php foreach ($sektor as $key => $value){?>
                                          <option value="<?php echo $value['esa95_sektorid'];?>"><?php echo $value["esa95_sektorpopis"] ?></option>
                                              <?php } ?>
                      </select>
                          </div>
        <div>
                                      <label for="druheqid" class="block mb-2 text-sm font-medium text-gray-900">Druh</label>
                                      <select id="druheqid" name="druheqid" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full">
                                <?php foreach ($druheqid as $key => $value){?>
                                                  <option value="<?php echo $value['druheqid'];?>"><?php echo $value["poddruheq"] ?></option>
                                                      <?php } ?>
                              </select>
                                </div>
        <div>
                                            <label for="odvetvieid" class="block mb-2 text-sm font-medium text-gray-900">Odvetvie</label>
                                            <select id="odvetvieid" name="odvetvieid" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full">
                                      </select>
                                      </div>
    </div>
<hr class="w-full"/>
    <div class="grid mt-4 gap-6 mb-6 md:grid-cols-2">
        <div>
            <label for="dateemisie" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Dátum emisie <small class="text-red-500">*</small></label>
            <input type="date" id="dateemisie" name="dateemisie" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full" required />
        </div>
    <div>
        <label for="objememisie" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Objem emisie (%)</label>
<div class="relative">
  <input type="number" id="objememisie" name="objememisie" style="padding: .5rem .5rem .5rem 3rem;" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full px-20" >
</div>
    </div> 
            <div>
                  <label for="datesplatnosti" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Dátum splatnosti <small class="text-red-500">*</small></label>
                  <input type="date" id="datesplatnosti" name="datesplatnosti" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full" required />
              </div>
        <div>
              <label for="nominalemisie" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Nominál emisie</label>
      <div class="relative">
          <input type="number" id="nominalemisie" name="nominalemisie" style="padding: .5rem .5rem .5rem 3rem;" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full px-20" >
        </div>
          </div> 
            <div>
                  <label for="prvy_kupon" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Dátum prvého kupónu</label>
                  <input type="date" id="prvy_kupon" name="prvy_kupon" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full"/>
              </div>
                <div>
                        <label for="posledny_kupon" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Dátum posledného kupónu</label>
                        <input type="date" id="posledny_kupon" name="posledny_kupon" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full"/>
                    </div>
        <div>
              <label for="kupon" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Kupón (%)</label>
      <div class="relative">
          <div class="absolute inset-y-0 start-0 flex bg-gray-300 items-center px-3 font-bold rounded-lg text-gray-600 pointer-events-none">
             %  </div>
          <input type="number" id="kupon" name="kupon" style="padding: .5rem .5rem .5rem 3rem;" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full px-20">
        </div>
          </div> 
            <div>
                    <label for="dan" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Daň (%)</label>
            <div class="relative">
                  <div class="absolute inset-y-0 start-0 flex bg-gray-300 items-center px-3 font-bold rounded-lg text-gray-600 pointer-events-none">
                       %  </div>
                  <input type="number" id="dan" name="dan" style="padding: .5rem .5rem .5rem 3rem;" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full px-20" >
                </div>
                </div> 
          <div>
                  <label for="zaklad" class="block mb-2 text-sm font-medium text-gray-900">Konvencia</label>
                  <select id="zaklad" name="zaklad" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full">
                        <option value="1">30/360</option>
                        <option value="2">30E/360</option>
                        <option value="3">Act/Act</option>
                        <option value="4">Act/360</option>
                        <option value="5">Act/365</option>
                        <option value="6">30/365</option>
                        <option value="7">30/Act</option>
                        <option value="10">Act/Act(Mex)</option>
                      </select>
            </div>
              <div>
                        <label for="kupfrek" class="block mb-2 text-sm font-medium text-gray-900">Frekvencia kupónu</label>
                        <select id="kupfrek" name="kupfrek" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full">
                                <option value="1">Mesačne</option>
                                <option value="3">Štvrťročne</option>
                                <option value="6">Polročne</option>
                                <option value="12">Ročne</option>
                                <option value="77">Pri splatnosti</option>
                              </select>
                  </div>
              <div class="w-full">
                  <label for="exfrekkup" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Exkupón</label>
                  <input type="text" id="exfrekkup" name="exfrekkup" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full"/>
              </div>  
            <div class="w-full">
                  <label for="exfrekist" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Existina</label>
                  <input type="text" id="exfrekist" name="exfrekist" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full"/>
    </div>
    </div>
                  <div class="mb-6">
                              <label for="istfrek" class="block mb-2 text-sm font-medium text-gray-900">Frekvencia istiny</label>
                              <select id="istfrek" name="istfrek" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full">
                                        <option value="1">Mesačne</option>
                                        <option value="3">Štvrťročne</option>
                                        <option value="6">Polročne</option>
                                        <option value="12">Ročne</option>
                                        <option value="77">Pri splatnosti</option>
                                      </select>
                        </div>
    <section class="flex gap-6">
              <div class="w-full">
                        <label for="sadzba_fo" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Sadzba FO:</label>
                        <input type="text" id="sadzba_fo" name="sadzba_fo" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full"/>
                    </div>  
                  <div class="w-full">
                        <label for="sadzba_po" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Sadzba PO:</label>
                        <input type="text" id="sadzba_po" name="sadzba_po" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full"/>
                    </div>  
                  <div class="w-full">
                        <label for="sadzba_no" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Sadzba NO:</label>
                        <input type="text" id="sadzba_no" name="sadzba_no" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full"/>
                    </div>  

  </section>
  <div class="overflow-x-visible bg-gray-200 mt-5 p-5 rounded-lg">
    <div class="flex justify-between items-center">
      <h2 class="text-xl font-bold mb-5">Účty</h2>
      <button data-modal-target="default-modal" data-modal-toggle="default-modal" type="button" class="text-white bg-green-700 hover:bg-blue-800 transition-all focus:ring-4 focus:outline-none p-1 focus:ring-blue-300 font-medium rounded-lg text-sm text-center inline-flex items-center me-2">
        <svg class="w-6 h-6" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
  <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 7.757v8.486M7.757 12h8.486M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"/>
</svg>
        <span class="sr-only">Pridať účet</span>
        </button>
      </div>
                              <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                                      <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                                          <tr>
                                                  <th scope="col" class="px-4 py-3">Mena</th>
                                                  <th scope="col" class="px-4 py-3">Kód</th>
                                                  <th scope="col" class="px-4 py-3">Trh</th>
                                                  <th scope="col" class="px-4 py-3">Bloomberg</th>
                                                  <th scope="col" class="px-4 py-3">Účet</th>
                                                  <th scope="col" class="px-4 py-3"></th>
                                              </tr>
                                          </thead>
                                      <tbody id="ucetni">
                                </tbody>
                                  </table>
                          </div>
    <button type="submit" class="text-white bg-blue-700 mt-10 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm w-full sm:w-auto px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">Vytvoriť</button>
</form>
<div id="default-modal" tabindex="-1" aria-hidden="true" class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
    <div class="relative p-4 w-full max-w-2xl max-h-full">
        <!-- Modal content -->
        <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
            <!-- Modal header -->
            <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
                <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                   Vytvoriť nový účet 
                </h3>
                <button type="button" class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white" data-modal-hide="default-modal">
                    <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                    </svg>
                    <span class="sr-only">Zavierť modálne okno vytvorenia nového účtu</span>
                </button>
            </div>
            <div class="p-4 md:p-5 space-y-4">
              <form id="addUcet">
              <div class="mb-4">
                              <label for="currencyUcet" class="block mb-2 text-sm font-medium text-gray-900">Mena</label>
                              <select id="currencyUcet" name="currencyUcet" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full">
                    <?php foreach ($meny as $key => $value){?>
                                    <option value="<?php echo $value['mena'];?>"><?php echo $value["mena"] ?></option>
                                          <?php } ?>
                </select>
                        </div>
                          <div class="w-full mb-4">
                                  <label for="kodUcet" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Kód</label>
                                  <input type="text" id="kodUcet" name="kodUcet" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full" required />
                              </div>  
                                  <div class="w-full mb-4">
                                            <label for="trh" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Trh</label>
                                            <input type="text" id="trh" name="trh" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full" required />
                                        </div>  
        <div class="flex items-center mb-4">
    <input id="bloomberg" name="bloomberg" type="checkbox" value="" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
    <label for="bloomberg" class="ms-2 text-sm font-medium text-gray-900 dark:text-gray-300">Bloomberg</label>
</div
                        <div>
                                        <label for="ucet" class="block mb-2 text-sm font-medium text-gray-900">Účet</label>
                                        <select id="ucet" name="ucet" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full">
                                              <option value="SK">SK</option>
                                                          <option value="CZ">CZ</option>
              <option value="PL">PL</option>
                                                          <option value="CEDEL">ČSOB</option>
              <option value="CBL">SLSP</option>
              <option value="EXA">EXANTE</option>
                          </select>
          </div>
                      <div class="flex items-center p-4 md:p-5 border-t border-gray-200 rounded-b dark:border-gray-600">
                <button type="submit" type="button" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">Pridať nový účet</button>
                <button data-modal-hide="default-modal" type="button" class="py-2.5 px-5 ms-3 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700">Zavrieť</button>
          </form>  </div>
        </div>
    </div>
</div>
<div id="addEmitent" tabindex="-1" aria-hidden="true" class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
      <div class="relative p-4 w-full max-w-2xl max-h-full">
        <!-- Modal content -->
        <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
            <!-- Modal header -->
            <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
                <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                   Pridať nového emitenta 
                </h3>
                <button type="button" class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white" data-modal-hide="addEmitent">
                    <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                    </svg>
                    <span class="sr-only">Zavierť modálne okno vytvorenia nového účtu</span>
                </button>
            </div>
            <div class="p-4 md:p-5 space-y-4">
              <form id="addEmitentForm">
                                    <div class="w-full mb-4">
                        <label for="" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Názov emitenta</label>
                                              <input type="text" id="nazovEmitenta" name="nazovEmitenta" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full" required />
                                          </div>  
                          <div class="w-full mb-4">
            <label for="skratka" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Skratka</label>
                                  <input type="text" id="skratka" name="skratka" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full" required />
                              </div>  
              <div class="mb-4">
                                            <label for="stat" class="block mb-2 text-sm font-medium text-gray-900">Štát</label>
                                            <select id="atat" name="stat" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full">
                                        <?php foreach ($krajiny as $key => $value){?>
                                                          <option value="<?php echo $value['stateid'];?>"><?php echo $value["stateall"] ?></option>
                                                              <?php } ?>
                                      </select>
                                      </div>
                                  <div class="w-full mb-4">
                                            <label for="ico" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">IČO</label>
                                            <input type="text" id="ico" name="ico" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full" required />
                                        </div>  
    <div id="sektorWrar" class="mb-4">
                                <label for="sektor" class="block mb-2 text-sm font-medium text-gray-900">Sektor</label>
                                <select id="sektor" name="sektor" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full">
                          <?php foreach ($sektor as $key => $value){?>
                                            <option value="<?php echo $value['esa95_sektorid'];?>"><?php echo $value["esa95_sektorpopis"] ?></option>
                                                <?php } ?>
                        </select>
                          </div>
          <div id="emitentMsg" class="hidden p-4 rounded-lg mb-4 text-white shadow-lg">
            
            </div>
                      <div class="flex items-center border-t border-gray-200 rounded-b dark:border-gray-600">
                <button type="submit" type="button" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">Pridať nového emitenta</button>
                <button data-modal-hide="addEmitent" type="button" class="py-2.5 px-5 ms-3 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700">Zavrieť</button>
          </form>  </div>
        </div>
    </div>
  </div>

<script src="/src/assets/js/aktiva/cp/create.js"></script>


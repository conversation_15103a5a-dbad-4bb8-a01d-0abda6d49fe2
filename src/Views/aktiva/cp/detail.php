<?php

require "/home/<USER>/www/src/lib/connection.php";
require "/home/<USER>/www/conf/settings.php";

$clientID = isset($matches[1]) ? $matches[1] : null;
$menaRes = Connection::getDataFromDatabase("SELECT * FROM dbequity WHERE isin = '$clientID'", defaultDB);
$dlhopis = $menaRes[1][0];
$emitentid = $dlhopis["emitentid"];
$emitentRes = Connection::getDataFromDatabase("SELECT * FROM equityemitent WHERE emitentid = $emitentid", defaultDB);
$emitent = $emitentRes[1][0];
$sektorid = $dlhopis["sektorid"];
$sektorRes = Connection::getDataFromDatabase("SELECT * FROM sektor_esa95  WHERE esa95_sektorid = $sektorid", defaultDB);
$sektor = $sektorRes[1][0];
$druhid = $dlhopis["druheqid"];
$eqid = $dlhopis["eqid"];

switch ($eqid) {
  case "Shares":
    $urlPart = "akcie";
    break;
  case "Bonds":
    $urlPart = "dlhopisy";
    break;
  case "Fonds":
    $urlPart = "podielove-fondy";
    break;
}

$druhRes = Connection::getDataFromDatabase("SELECT * FROM equitydruh WHERE druheqid = $druhid", defaultDB);
$druheqid = $druhRes[1][0];

$odvetvieid = $dlhopis["odvetvieid"];
$odvetvieRes = Connection::getDataFromDatabase("SELECT * FROM equityodvetvie WHERE odvetvieid = $odvetvieid", defaultDB);
$odvetvie = $odvetvieRes[1][0];

$isincurr = $dlhopis["isin"] . $dlhopis["currencynom"];
$uctyRes = Connection::getDataFromDatabase("SELECT * FROM dbequitycurrric WHERE isincurr = '$isincurr'", defaultDB);
$ucty = $uctyRes[1];
$detailHeading = "";
$kategoria = "";
$kupfrek = "";
$istfrek = "";

switch ($dlhopis["eqid"]) {
  case 'Shares':
    $detailHeading = "Detaili akcie";
  case 'Bonds':
    $detailHeading = "Detail dlhopisu";
  case "Depo":
    $detailHeading = "Detail depozitného certifikátu";
  default:
    $detailHeading = "Detail podielového fondu";
}

switch ($dlhopis["poplatokcp"]) {
  case 1:
    $kategoria = "Tuzemské";
    break;
  case 2:
    $kategoria = "EB, US, DE, UK, ČR, BE, LU";
    break;
  case 3:
    $kategoria = "OECD";
    break;
  case 4:
    $kategoria = "Ostatné";
    break;
}

switch ($dlhopis["kupfrek"]) {
  case 1:
    $kupfrek = "Mesačne";
    break;
  case 3:
    $kupfrek = "Štvrťročne";
    break;
  case 6:
    $kupfrek = "Polročne";
    break;
  case 12:
    $kupfrek = "Ročne";
    break;
  case 77:
    $kupfrek = "Pri splatnosti";
    break;
}
switch ($dlhopis["istfrek"]) {
  case 1:
    $istfrek = "Mesačne";
    break;
  case 3:
    $istfrek = "Štvrťročne";
    break;
  case 6:
    $istfrek = "Polročne";
    break;
  case 12:
    $istfrek = "Ročne";
    break;
  case 77:
    $istfrek = "Pri splatnosti";
    break;
}

$isin = $dlhopis["isin"];
$mena = $dlhopis["currencynom"];

$datum = Connection::getDataFromDatabase("SELECT max(datum) as datum FROM today", defaultDB)[1][0]["datum"];
$aktualnaQuery = "WITH filtered_majetok AS (SELECT mt1.uctovnykod,
                                 mt1.kodaktiva,
                                 mt1.subjektid,
                                 mt1.md_d,
                                 mt1.pocet,
                                 mt1.ucetaktiva,
                                 mt1.obratdatatimezauctovane as datum
                          FROM majetoktoday mt1
                                   JOIN today t ON mt1.subjektid = t.fondid
                          WHERE t.datum = DATE '$datum'
                          UNION
                          SELECT mt2.uctovnykod,
                                 mt2.kodaktiva,
                                 mt2.subjektid,
                                 mt2.md_d,
                                 mt2.pocet,
                                 mt2.ucetaktiva,
                                 mt2.datum as datum
                          FROM majetoktotal mt2
                          WHERE mt2.datum = DATE '$datum'),
     valid_uctovnykody AS (SELECT uctovnykod
                           FROM kodobratumd_d
                           WHERE kodobratu IN (251, 254, 255, 256)
                             AND md_d = 0)
SELECT p.cislozmluvy,
       SUM(mt.pocet * SIGN(0.5 - mt.md_d)) AS pocet,
       po.titulpred                        as titulpred,
       po.meno                             as meno,
       po.prieznaz                         as prieznaz,
       po.titulza                          as titulza,
       po.podielnikid                      as podielnikid,
       min(mta.datum)                      as datum_nakupu
FROM filtered_majetok mt
         JOIN valid_uctovnykody vu ON mt.uctovnykod = vu.uctovnykod
         JOIN portfolio p ON p.fondid = mt.subjektid
         JOIN podielnik po ON p.podielnikid = po.podielnikid
         JOIN dbequity e ON e.isin = '$isin'
         JOIN dbequitycurr dc ON e.isin = dc.isin AND dc.CURRENCYTRADE = '$mena'
         JOIN dbequitycurrric dcr ON dcr.isincurr = dc.isincurr AND mt.kodaktiva = dcr.isincurrric
         JOIN majetoktotal mta ON mta.subjektid = p.fondid AND mta.eqid = 'Shares' AND dcr.isincurrric = mta.kodaktiva
GROUP BY p.cislozmluvy, po.titulpred, po.meno, po.prieznaz, po.titulza, po.podielnikid, mt.datum, dcr.isincurrric
ORDER BY p.cislozmluvy;";
$aktualnyVlastnici = Connection::getDataFromDatabase($aktualnaQuery, defaultDB)[1];

$historiaVlastnictvaQuery = "SELECT mt.obratdatatimezauctovane,
       po.titulpred   as titulpred,
       po.meno        as meno,
       po.prieznaz    as prieznaz,
       po.titulza     as titulza,
       po.podielnikid as podielnikid,
       mt.md_d,
       mt.pocet
FROM majetokarchiv mt
         JOIN portfolio p ON p.fondid = mt.subjektid
         JOIN podielnik po ON p.podielnikid = po.podielnikid
         JOIN dbequity e ON e.isin = '$isin'
         JOIN dbequitycurr dc ON e.isin = dc.isin AND dc.CURRENCYTRADE = '$mena'
         JOIN dbequitycurrric dcr ON dcr.isincurr = dc.isincurr AND mt.kodaktiva = dcr.isincurrric
WHERE mt.eqid = 'Shares' and uctovnykod in (251200)
GROUP BY pocet, mt.obratdatatimezauctovane, po.titulpred, po.meno, po.prieznaz, po.titulza, po.podielnikid, mt.md_d
ORDER BY mt.obratdatatimezauctovane DESC LIMIT 10;";
$historiaVlastnictva = Connection::getDataFromDatabase($historiaVlastnictvaQuery, defaultDB)[1];

?>
<div class="p-6 space-y-6">
  <div class="bg-slate-800 border-slate-700 rounded-lg border">
    <div class="p-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <div class="w-16 h-16 bg-slate-700 dark:text-gray-200 rounded-full flex items-center p-3 justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
              stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-box-icon lucide-box">
              <path
                d="M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z" />
              <path d="m3.3 7 8.7 5 8.7-5" />
              <path d="M12 22V12" />
            </svg>
          </div>
          <div>
            <div class="flex items-center space-x-3">
              <h1 class="text-2xl dark:text-gray-100 font-bold"><?php echo $dlhopis["cpnaz"] ?></h1>
              <span
                class="bg-green-600 hover:bg-green-700 text-xs font-semibold px-2.5 py-0.5 rounded-full">Aktívne</span>
            </div>
            <div class="text-slate-400 mt-1">Symbol: <?php echo $dlhopis["cpnazskratka"] ?> • ISIN:
              <?php echo $dlhopis["isin"] ?>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
    <!-- Asset Information -->
    <div class="lg:col-span-2 space-y-6">
      <div class="bg-slate-800 border-slate-700 rounded-lg border">
        <div class="p-6">
          <section class="flex items-center justify-between">
            <div class="pb-4 border-b border-slate-700">
              <h3 class="text-lg font-semibold text-slate-200">Základné informácie</h3>
              <p class="text-slate-400 text-sm">Všetky informácie o aktíve</p>
            </div>
            <button hx-get="/api/aktiva/editInfo/<?php echo $dlhopis["isin"]; ?>" hx-target="#assetInfo"
              class="hover:bg-slate-700 bg-slate-900 transition-all dark:text-white rounded-lg p-1">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="lucide lucide-settings2-icon lucide-settings-2">
                <path d="M20 7h-9" />
                <path d="M14 17H5" />
                <circle cx="17" cy="17" r="3" />
                <circle cx="7" cy="7" r="3" />
              </svg>
            </button>
          </section>
          <div id="assetInfo" class="pt-4 space-y-4">
            <div class="grid grid-cols-2 gap-6">
              <div class="space-y-3">
                <div class="flex justify-between">
                  <span class="text-slate-400">Názov aktíva</span>
                  <span class="text-white"><?php echo $dlhopis["cpnaz"]; ?></span>
                </div>
                <div class="flex justify-between">
                  <span class="text-slate-400">Symbol</span>
                  <span class="text-white"><?php echo $dlhopis["cpnazskratka"]; ?></span>
                </div>
                <div class="flex justify-between">
                  <span class="text-slate-400">ISIN</span>
                  <span class="text-white"><?php echo $dlhopis["isin"]; ?></span>
                </div>
                <div class="flex justify-between">
                  <span class="text-slate-400">Typ aktíva</span>
                  <span class="text-white"><?php echo $druheqid["poddruheq"]; ?></span>
                </div>
                <div class="flex justify-between">
                  <span class="text-slate-400">Sektor</span>
                  <span class="text-white text-xs"><?php echo $sektor["esa95_sektorpopis"]; ?></span>
                </div>
                <div class="flex justify-between">
                  <span class="text-slate-400">Odvetvie</span>
                  <span class="text-white"><?php echo $odvetvie["odvetviepopis"]; ?></span>
                </div>
              </div>
              <div class="space-y-3">
                <div class="flex justify-between">
                  <span class="text-slate-400">Mena</span>
                  <span class="text-white font-semibold"><?php echo $dlhopis["currencynom"]; ?></span>
                </div>
                <div class="flex justify-between">
                  <span class="text-slate-400">Dátum splatnosti</span>
                  <span
                    class="text-white font-semibold"><?php echo $dlhopis["maturitydate"] ? $dlhopis["maturitydate"] : "N/A"; ?></span>
                </div>
                <div class="flex justify-between">
                  <span class="text-slate-400">Dátum prvého kupónu</span>
                  <span class="text-white"><?php echo $dlhopis["prvy_kupon"] ? $dlhopis["prvy_kupon"] : "N/A"; ?></span>
                </div>
                <div class="flex justify-between">
                  <span class="text-slate-400">Dátum posledného kupónu</span>
                  <span
                    class="text-white"><?php echo $dlhopis["posledny_kupon"] ? $dlhopis["posledny_kupon"] : "N/A"; ?></span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="bg-slate-800 border-slate-700 rounded-lg border">
        <div class="p-6">
          <div class="pb-4 border-b border-slate-700">
            <h3 class="text-lg font-semibold text-slate-200">Aktuálni vlastníci</h3>
            <p class="text-slate-400 text-sm">Klienti, ktorí momentálne vlastnia toto aktívum</p>
          </div>
          <div class="pt-4">
            <div class="relative w-full overflow-auto">
              <table class="w-full caption-bottom text-sm">
                <thead>
                  <tr class="border-slate-700 border-b">
                    <th class="text-slate-300 h-12 px-4 text-left align-middle font-medium">Klient</th>
                    <th class="text-slate-300 h-12 px-4 text-left align-middle font-medium">Množstvo</th>
                    <th class="text-slate-300 h-12 px-4 text-left align-middle font-medium">Dátum nákupu</th>
                  </tr>
                </thead>
                <tbody>
                  <?php
                  if (count($aktualnyVlastnici) > 0) {
                    foreach ($aktualnyVlastnici as $key => $value) { ?>
                      <tr class="border-slate-700 border-b">
                        <td class="p-4 align-middle">
                          <div hx-get="/klienti/detail/<?php echo $value["podielnikid"]; ?>" hx-target="#pageContentMain"
                            hx-replace-url="true" hx-push-url="true" preload="always mouseover"
                            class="flex items-center p-1 rounded-lg dark:hover:bg-gray-700 transition-all hover:bg-gray-100 cursor-pointer space-x-3">
                            <div class="w-8 h-8 rounded-full bg-slate-700 flex items-center justify-center text-slate-300">
                              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                class="lucide lucide-user-round-icon lucide-user-round">
                                <circle cx="12" cy="8" r="5" />
                                <path d="M20 21a8 8 0 0 0-16 0" />
                              </svg>
                            </div>
                            <div>
                              <div class="text-white font-medium">
                                <?php echo $value["titulpred"] . " " . $value["meno"] . " " . $value["prieznaz"] . " " . $value["titulza"] ?>
                              </div>
                              <div class="text-slate-400 text-sm">ID: <?php echo $value["podielnikid"] ?></div>
                            </div>
                          </div>
                        </td>
                        <td class="p-4 align-middle text-white"><?php echo $value["pocet"]; ?>ks</td>
                        <td class="p-4 align-middle text-slate-300"><?php echo $value["datum_nakupu"]; ?></td>
                      </tr>
                    <?php }
                  } else { ?>
                    <tr class="border-slate-700 border-b">
                      <td class="p-4 align-middle text-white" colspan="3">Žiadni aktuálni vlastníci</td>
                    </tr>
                  <?php }
                  ?>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      <!-- Ownership History -->
      <div class="bg-slate-800 border-slate-700 rounded-lg border">
        <div class="p-6">
          <div class="pb-4 border-b border-slate-700">
            <h3 class="text-lg font-semibold text-slate-200">História vlastníctva</h3>
            <p class="text-slate-400 text-sm">Kompletná história transakcií s týmto aktívom</p>
          </div>
          <div class="pt-4">
            <div class="relative w-full overflow-auto">
              <table class="w-full caption-bottom text-sm">
                <thead>
                  <tr class="border-slate-700 border-b">
                    <th class="text-slate-300 h-12 px-4 text-left align-middle font-medium">Dátum vysporiadania</th>
                    <th class="text-slate-300 h-12 px-4 text-left align-middle font-medium">Klient</th>
                    <th class="text-slate-300 h-12 px-4 text-left align-middle font-medium">Typ</th>
                    <th class="text-slate-300 h-12 px-4 text-left align-middle font-medium">Množstvo</th>
                  </tr>
                </thead>
                <tbody>
                  <?php foreach ($historiaVlastnictva as $key => $value) { ?>
                    <tr class="border-slate-700 border-b">
                      <td class="p-4 align-middle text-slate-300"><?php echo $value["obratdatatimezauctovane"]; ?></td>
                      <td class="p-4 align-middle">
                        <div hx-get="/klienti/detail/<?php echo $value["podielnikid"]; ?>" hx-target="#pageContentMain"
                          hx-replace-url="true" hx-push-url="true" preload="always mouseover"
                          class="inline-flex p-1 transition-all px-3 cursor-pointer dark:hover:bg-gray-900 rounded-lg hover:bg-gray-200 items-center space-x-2">
                          <div
                            class="w-6 h-6 rounded-full bg-slate-700 flex p-1 items-center justify-center text-slate-300 text-xs">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                              stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                              class="lucide lucide-user-round-icon lucide-user-round">
                              <circle cx="12" cy="8" r="5" />
                              <path d="M20 21a8 8 0 0 0-16 0" />
                            </svg>
                          </div>
                          <span
                            class="text-white"><?php echo $value["titulpred"] . " " . $value["meno"] . " " . $value["prieznaz"] . " " . $value["titulza"] ?></span>
                        </div>
                      </td>
                      <td class="p-4 align-middle">
                        <span
                          class="<?php echo $value["md_d"] == 0 ? "text-white bg-green-600" : "text-white bg-red-600"; ?> 
                           text-xs font-semibold px-2.5 py-0.5 rounded-full"><?php echo $value["md_d"] == 0 ? "Nákup" : "Predaj"; ?></span>
                      </td>
                      <td class="p-4 align-middle text-white"><?php echo $value["pocet"]; ?> ks</td>
                    </tr>
                  <?php } ?>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="">
      <!-- Asset Performance -->
      <div class="bg-slate-800 border-slate-700 mb-5 rounded-lg border">
        <div class="p-2">
          <div class="pt-2">
            <!-- TradingView Widget BEGIN -->
            <div class="tradingview-widget-container" id="performanceAsset">
              <div class="tradingview-widget-container__widget"></div>
              <script type="text/javascript"
                src="https://s3.tradingview.com/external-embedding/embed-widget-symbol-overview.js" async>
                  {
                    "symbols": [
                      [
                        "<?php echo $dlhopis["cpnazskratka"] ?>|1D"
                      ]
                    ],
                      "chartOnly": false,
                        "width": "100%",
                          "height": "40%",
                            "locale": "en",
                              "colorTheme": "dark",
                                "autosize": true,
                                  "showVolume": false,
                                    "showMA": false,
                                      "hideDateRanges": false,
                                        "hideMarketStatus": false,
                                          "hideSymbolLogo": false,
                                            "scalePosition": "right",
                                              "scaleMode": "Normal",
                                                "fontFamily": "-apple-system, BlinkMacSystemFont, Trebuchet MS, Roboto, Ubuntu, sans-serif",
                                                  "fontSize": "10",
                                                    "noTimeScale": false,
                                                      "valuesTracking": "1",
                                                        "changeMode": "price-and-percent",
                                                          "chartType": "area",
                                                            "maLineColor": "#2962FF",
                                                              "maLineWidth": 1,
                                                                "maLength": 9,
                                                                  "headerFontSize": "medium",
                                                                    "lineWidth": 2,
                                                                      "lineType": 0,
                                                                        "dateRanges": [
                                                                          "1d|1",
                                                                          "1m|30",
                                                                          "3m|60",
                                                                          "12m|1D",
                                                                          "60m|1W",
                                                                          "all|1M"
                                                                        ]
                  }
                </script>
            </div>
            <!-- TradingView Widget END -->
          </div>
        </div>
      </div>
      <!-- Quick Actions -->
      <div class="bg-slate-800 border-slate-700 rounded-lg border">
        <div class="p-6">
          <div class="pb-4 border-b border-slate-700">
            <h3 class="text-lg font-semibold text-slate-200">Rýchle akcie</h3>
          </div>
          <div class="pt-4 space-y-3">
            <button id="global"
              onclick="appModeSwitcher(event, true); refreshMenu(event); refreshTopBar(event); refreshBreadcrumb(event);"
              hx-get="/investicne-zamery/pridat/<?php echo $urlPart; ?>/new/<?php echo $isin; ?>?action=sell&mena=<?php echo $mena; ?>"
              hx-target="#pageContentMain" hx-replace-url="true" hx-push-url="true"
              class="w-full inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none
               focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-red-600 hover:bg-red-700 h-10 px-4 py-2">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="w-4 h-4 mr-2">
                <circle cx="12" cy="12" r="10"></circle>
                <line x1="12" x2="12" y1="8" y2="16"></line>
                <line x1="8" x2="16" y1="12" y2="12"></line>
              </svg>
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="lucide lucide-loader-circle-icon modeSpinner animate-spin lucide-loader-circle"
                style="display: none;">
                <path d="M21 12a9 9 0 1 1-6.219-8.56" />
              </svg>
              Predať
            </button>
            <button id="global"
              onclick="appModeSwitcher(event, true); refreshMenu(event); refreshTopBar(event); refreshBreadcrumb(event);"
              hx-get="/investicne-zamery/pridat/<?php echo $urlPart; ?>/new/<?php echo $isin; ?>?action=buy&mena=<?php echo $mena; ?>"
              hx-target="#pageContentMain" hx-replace-url="true" hx-push-url="true"
              class="w-full inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors text-gray-900 focus-visible:outline-none focus-visible:ring-2
               focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-green-600 hover:bg-green-700 h-10 px-4 py-2">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="w-4 h-4 mr-2">
                <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"></path>
                <polyline points="14 2 14 8 20 8"></polyline>
                <line x1="16" x2="8" y1="13" y2="13"></line>
                <line x1="16" x2="8" y1="17" y2="17"></line>
                <line x1="10" x2="8" y1="9" y2="9"></line>
              </svg>
              Nakúpiť
            </button>
            <button
              class="w-full inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-slate-600 text-slate-300 hover:bg-slate-700 h-10 px-4 py-2">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="w-4 h-4 mr-2">
                <polyline points="23 6 13.5 15.5 8.5 10.5 1 18"></polyline>
                <polyline points="17 6 23 6 23 12"></polyline>
              </svg>
              Výkonnosť report
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
$dealID = isset($matches[1]) ? intval($matches[1]) : null;

$detail = Connection::getDataFromDatabase("select k.*,
       d.cislo as cislo
from konfirmaciacp k,
     dennikpm d
where k.logactivityid in (4, 6, 7, 8, 9)
  and k.dealid = $dealID
  and k.dealid = d.dealid
  and k.SUBJEKTID = 0", defaultDB)[1][0];


if (str_contains($_SERVER['REQUEST_URI'], "akcia")) {
    $object = "Share";
    $color = "#FF9F40";
}
if (str_contains($_SERVER['REQUEST_URI'], "dlhopis")) {
    $object = "Bond";
    $color = "#FF6384";
}
if (str_contains($_SERVER['REQUEST_URI'], "fond")) {
    $object = "Fond";
    $color = "";
}

$nowMV = strtotime($detail["datummv"]);
$nowFV = strtotime($detail["datumfv"]);
$fromDate = strtotime($detail["expfrom"]);
$datediffMV = $nowMV - $fromDate;
$datediffFV = $nowFV - $fromDate;

$maxToday = Connection::getDataFromDatabase("SELECT max(datum) as datum FROM today", defaultDB)[1][0]["datum"];

$userID = $detail["loguserid"];
$user = Connection::getDataFromDatabase("SELECT userid, username from users WHERE userid = $userID", defaultDB)[1][0];

$partnerid = $detail["partnerid"];
$partnerName = Connection::getDataFromDatabase("SELECT nazovpartnera FROM partner WHERE partnerid = $partnerid", defaultDB)[1][0]["nazovpartnera"];

?>

<nav class="flex p-2 px-5 text-gray-700 border border-gray-200 rounded-lg bg-gray-50 dark:bg-gray-800 dark:border-gray-700"
    aria-label="Breadcrumb">
    <ol class="inline-flex items-center space-x-1 md:space-x-2 rtl:space-x-reverse">
        <li class="inline-flex items-center">
            <a href="/"
                class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                <svg class="w-3 h-3 me-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                    viewBox="0 0 20 20">
                    <path
                        d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z" />
                </svg>
                Domov
            </a>
        </li>
        <li>
            <div class="flex items-center">
                <svg class="rtl:rotate-180 block w-3 h-3 mx-1 text-gray-400 " aria-hidden="true"
                    xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="m1 9 4-4-4-4" />
                </svg>
                <span hx-get="/investicne-zamery" hx-target="#pageContentMain" hx-replace-url="true"
                    class="ms-1 cursor-pointer hover:underline text-sm font-medium text-gray-700 hover:text-blue-600 md:ms-2 dark:text-gray-400 dark:hover:text-white">Investičné
                    zámery</span>
            </div>
        </li>
        <li aria-current="page">
            <div class="flex items-center">
                <svg class="rtl:rotate-180 w-3 h-3 mx-1 text-gray-400" aria-hidden="true"
                    xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="m1 9 4-4-4-4" />
                </svg>
                <span class="ms-1 text-sm font-semibold text-blue-500 md:ms-2 dark:text-gray-400">Investičný zámer č.
                    <?php echo $dealID; ?></span>
            </div>
        </li>
    </ol>
</nav>
<section class="relative mt-3">
    <form id="createZamer" hx-post="/api/investicne-zamery/cp/update" hx-target="#toast">
        <input type="hidden" name="dealid" id="dealid" value="<?php echo $dealID; ?>" />
        <input type="hidden" name="dealidseq" id="dealidseq" value="<?php echo $detail["dealidseq"]; ?>" />
        <input type="hidden" name="eqid" id="eqid" value="<?php echo $detail["eqid"]; ?>" />
        <input type="hidden" name="maxToday" id="maxToday" value="<?php echo $maxToday; ?>" />
        <div class="flex justify-between pb-2 mb-4 items-center border-b">
            <h2 class="scroll-m-20 text-3xl font-semibold tracking-tight first:mt-0">
                Investičný zámer <span style="background: <?php echo $color; ?>;"
                    class="text-gray-900 font-bold px-2 py-0.5 rounded-lg"><?php echo $object ?></span>
                <span style="background: #78716c;"
                    class="text-white font-bold px-2 py-0.5 rounded-lg"><?php echo $dealID ?></span>
            </h2>
            <div class="flex items-center gap-1">
                Autor:
                <a href="/pouzivatelia/detail/<?php echo $user["userid"]; ?>"
                    class="flex items-center px-3 gap-2 p-0.5 rounded-lg hover:bg-gray-200 hover:underline cursor-pointer transition-all">

                    <div
                        class="relative w-5 h-5 overflow-hidden bg-gray-200 flex items-center justify-center rounded-full dark:bg-gray-600">
                        <svg class="w-5 h-5 text-gray-400 -mb-2" fill="currentColor" viewBox="0 0 20 20"
                            xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                                clip-rule="evenodd">
                            </path>
                        </svg>
                    </div>
                    <small><?php echo $user["username"] ?></small>
                </a>
            </div>
            <button type="submit"
                class="focus:outline-none flex items-center transition-all gap-2 text-white bg-green-700 hover:bg-green-800 focus:ring-4 focus:ring-green-300 rounded-lg text-md font-extrabold px-5 py-2.5 dark:bg-green-600 dark:hover:bg-green-700 dark:focus:ring-green-800">Aktualizovať
                <svg id="updateChecks" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide lucide-check-check">
                    <path d="M18 6 7 17l-5-5" />
                    <path d="m22 10-7.5 7.5L13 16" />
                </svg>
                <svg id="updateSpinner" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide hidden animate-spin lucide-loader-circle">
                    <path d="M21 12a9 9 0 1 1-6.219-8.56" />
                </svg>
            </button>
        </div>
        <div class="bg-gray-100 rounded-b-lg p-3 flex justify-between w-full">
            <input type="hidden" name="ric" id="ric" value="<?php echo $dlhopis["isincurrricid"]; ?>" />
            <small>RIC: <strong class="text-lg"><?php echo $detail["ric"] ?></strong></small>
            <input type="hidden" name="nazovpartnera" id="nazovpartnera" />
            <input type="hidden" name="partnerid" id="parnterid" value="<?php echo $dlhopis["partnerid"]; ?>" />
            <small>Partner: <strong id="nazovpartneraValue" class="text-lg"><?php echo $partnerName; ?></strong></small>
            <small>Typ: <strong class="text-lg"><?php echo $dlhopis["typ"] ?></strong></small>
            <small>Druh: <strong class="text-lg"><?php echo $dlhopis["druh"] ?></strong></small>
        </div>
        <div class="grid grid-cols-1 my-4 md:grid-cols-2 gap-6">
            <section class="shadow-md p-8 flex flex-col gap-4 rounded-md">
                <div class="flex justify-between items-center">
                    <small>Peňažný podúčet:</small>
                    <span class="text-xl font-bold"><?php echo $detail["cubu"]; ?></span>
                </div>
            </section>
            <section class="shadow-md p-8 flex flex-col gap-4 rounded-md">
                <div class="flex gap-2 justify-between items-center">
                    <small>Majetkový podúčet:</small>
                    <span class="text-xl font-bold"><?php echo $detail["cum"]; ?></span>
                </div>
            </section>
        </div>
        <div class="grid grid-cols-1 my-4 md:grid-cols-2 gap-6">
            <section class="shadow-md p-8 flex flex-col gap-4 rounded-md">
                <div>
                    <label for="datumpokynu" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Dátum
                        pokynu</label>
                    <input type="date" id="datumpokynu" name="datumpokynu" value="<?php echo $detail["datpok"] ?>"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                        required />
                </div>
                <div>
                    <label for="platnostod" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Dátum
                        od</label>
                    <input type="date" id="platnostod" name="platnostod" value="<?php echo $detail["expfrom"] ?>"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                        required />
                </div>
                <div>
                    <label for="platnostdo" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Dátum
                        do</label>
                    <input type="date" id="platnostdo" name="platnostdo" value="<?php echo $detail["exptill"] ?>"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                        required />
                </div>
                <section class="flex items-center gap-4">
                    <div class="w-full">
                        <label for="pocetkusov"
                            class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Počet kusov</label>
                        <input type="number" id="pocetkusov" name="pocetkusov" readonly="true"
                            value="<?php echo $detail["kusov"]; ?>" placeholder="Počet kusov (nemenné)"
                            class="border border-gray-300 text-gray-900 bg-gray-200 text-sm rounded-lg block w-full p-2.5"
                            required />
                    </div>
                    <?php if ($action === "sell") { ?>
                        <span class="w-1/2">Max. počet: <strong class="p-1 px-2 text-white text-xs rounded-lg"
                                style="background-color: #71717a;"><?php echo $dlhopis["pocet"]; ?></strong></span>
                        <!-- <button type="button" id="selectHalf" value="<?php echo $dlhopis["pocet"] / 2; ?>"
                            class="text-white bg-gray-800 hover:bg-gray-900 focus:outline-none focus:ring-4 focus:ring-gray-300 font-extrabold rounded-lg text-sm px-5 py-2.5 me-2 mb-2">1/2</button>
                        <button type="button" id="selectAll" value="<?php echo $dlhopis["pocet"]; ?>"
                            class="text-white bg-blue-800 hover:bg-gray-900 focus:outline-none focus:ring-4 focus:ring-gray-300 font-extrabold rounded-lg text-sm px-5 py-2.5 me-2 mb-2">Všetky</button> -->
                    <?php } ?>
                </section>
                <section class="flex items-center gap-4">
                    <div class="w-full">
                        <label for="dobavysporm"
                            class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Dátum majetkového
                            vysporiadania</label>
                        <input type="text" id="dobavysporm" name="dobavysporm" placeholder="Počet dní"
                            value="<?php echo ($datediffMV / (60 * 60 * 24)); ?>"
                            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                            required />
                    </div>
                    <input type="date" id="datumvysporm" name="datumvysporm" value="<?php echo $detail["datummv"]; ?>"
                        class="w-full mt-4 text-white flex bg-blue-800 justify-center border-none items-center font-bold h-full rounded-lg" />
                </section>
                <section class="flex items-center gap-4">
                    <div class="w-full">
                        <label for="dobavysporf"
                            class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Dátum finančného
                            vysporiadania</label>
                        <input type="text" id="dobavysporf" name="dobavysporf" placeholder="Počet dní"
                            value="<?php echo ($datediffFV / (60 * 60 * 24)); ?>"
                            class="bg-gray-50 border border-gray-300  text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                            required />
                    </div>
                    <input type="date" id="datumvysporf" name="datumvysporf" value="<?php echo $detail["datumfv"]; ?>"
                        class="w-full mt-4 text-white flex justify-center bg-blue-800 border-none items-center font-bold h-full rounded-lg" />
                </section>
            </section>
            <section class="shadow-md p-8 flex flex-col gap-4 rounded-md">
                <div>
                    <label for="limitnykurz"
                        class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Limitný kurz</label>
                    <input type="text" id="limitnykurz" name="limitnykurz" value="<?php echo $detail["limitkurz"]; ?>"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                        required />
                </div>
                <div>
                    <label for="limitprice" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Limitná
                        cena</label>
                    <input type="text" id="limitprice" name="limitprice" readonly="true"
                        value="<?php echo number_format($detail["limitprice"], 2, ".", " "); ?>"
                        class="bg-gray-200 border border-gray-300 text-gray-900 text-sm rounded-lg w-full" required />
                </div>
                <div>
                    <label for="percentopoplatku"
                        class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Percento poplatku</label>
                    <input type="text" id="percentopoplatku" name="percentopoplatku" value="0"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                        required />
                </div>
            </section>
        </div>
    </form>
</section>
<div id="toast" class="opacity-0 hidden absolute right-5 bottom-1">
</div>
<?php include "src/Components/pooling/modal.php"; ?>
<script src="/src/assets/js/global/investicny-zamer/createCPZamer.js"></script>
<script src="/src/assets/js/global/investicny-zamer/form.js"></script>
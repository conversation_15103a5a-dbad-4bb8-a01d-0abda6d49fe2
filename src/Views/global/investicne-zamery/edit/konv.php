<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

$dealID = isset($matches[1]) ? intval($matches[1]) : null;

$detail = Connection::getDataFromDatabase("SELECT k.*, p.nazovpartnera, u.username
FROM konverzia k
         JOIN users u ON u.userid = k.loguserid
         JOIN partner p ON p.partnerid = k.partnerid
WHERE dealid = $dealID", defaultDB)[1][0];

$partners = Connection::getDataFromDatabase("SELECT partnerid, nazovpartnera, skratka FROM public.partner t WHERE archive_date IS NULL AND typpartnera = 1 ORDER BY nazovpartnera", defaultDB)[1];
$skratka = $partners[0]['skratka'];
$menaKredit = $detail["menakredit"];
$menaDebet = $detail["menadebet"];
$cubs = Connection::getDataFromDatabase("select distinct cub, mena from fondsbu where mena like '$menaKredit' AND banka LIKE '$skratka' order by cub", defaultDB)[1];
$debets = Connection::getDataFromDatabase("select distinct cub, mena from fondsbu where mena like '$menaDebet' AND banka LIKE '$skratka' order by cub ", defaultDB)[1];

?>
<nav class="flex p-2 px-5 text-gray-700 border border-gray-200 rounded-lg bg-gray-50 dark:bg-gray-800 dark:border-gray-700"
    aria-label="Breadcrumb">
    <ol class="inline-flex items-center space-x-1 md:space-x-2 rtl:space-x-reverse">
        <li class="inline-flex items-center">
            <a href="/"
                class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                <svg class="w-3 h-3 me-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                    viewBox="0 0 20 20">
                    <path
                        d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z" />
                </svg>
                Domov
            </a>
        </li>
        <li>
            <div class="flex items-center">
                <svg class="rtl:rotate-180 block w-3 h-3 mx-1 text-gray-400 " aria-hidden="true"
                    xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="m1 9 4-4-4-4" />
                </svg>
                <span hx-get="/investicne-zamery" hx-target="#pageContentMain" hx-replace-url="true"
                    class="ms-1 cursor-pointer hover:underline text-sm font-medium text-gray-700 hover:text-blue-600 md:ms-2 dark:text-gray-400 dark:hover:text-white">Investičné
                    zámery</span>
            </div>
        </li>
        <li>
            <div class="flex items-center">
                <svg class="rtl:rotate-180 block w-3 h-3 mx-1 text-gray-400 " aria-hidden="true"
                    xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="m1 9 4-4-4-4" />
                </svg>
                <span hx-get="/investicne-zamery/pridat/konverzie" hx-target="#pageContentMain" hx-replace-url="true"
                    class="ms-1 cursor-pointer hover:underline text-sm font-medium text-gray-700 hover:text-blue-600 md:ms-2 dark:text-gray-400 dark:hover:text-white">Konverzie</span>
            </div>
        </li>
        <li aria-current="page">
            <div class="flex items-center">
                <svg class="rtl:rotate-180 w-3 h-3 mx-1 text-gray-400" aria-hidden="true"
                    xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="m1 9 4-4-4-4" />
                </svg>
                <span class="ms-1 text-sm font-semibold text-blue-500 md:ms-2 dark:text-gray-400">Konfirmácia menovej
                    konverzie</span>
            </div>
        </li>
    </ol>
</nav>
<div class="bg-white rounded-lg w-full max-w-3xl mx-auto">
    <div id="toast" class="absolute bottom-0 right-5"></div>
    <div class="py-4 px-2 space-y-6">
        <div class="flex justify-between pb-2 mb-4 items-center border-b">
            <h2 class="scroll-m-20 text-3xl font-semibold tracking-tight first:mt-0">
                Investičný zámer <span style="background: #7DD3FC;"
                    class="text-gray-900 font-bold px-2 py-0.5 rounded-lg">Konverzia</span>
                <span style="background: #78716c;"
                    class="text-white font-bold px-2 py-0.5 rounded-lg"><?php echo $dealID ?></span>
            </h2>
            <div class="flex items-center gap-1">
                Autor:
                <a href="/pouzivatelia/detail/<?php echo $detail["loguserid"]; ?>"
                    class="flex items-center px-3 gap-2 p-0.5 rounded-lg hover:bg-gray-200 hover:underline cursor-pointer transition-all">

                    <div
                        class="relative w-5 h-5 overflow-hidden bg-gray-200 flex items-center justify-center rounded-full dark:bg-gray-600">
                        <svg class="w-5 h-5 text-gray-400 -mb-2" fill="currentColor" viewBox="0 0 20 20"
                            xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                                clip-rule="evenodd">
                            </path>
                        </svg>
                    </div>
                    <small><?php echo $detail["username"] ?></small>
                </a>
            </div>
        </div>
        <form class="space-y-6" hx-post="/api/investicne-zamery/konverzie/update" hx-target="#toast">
            <input type="hidden" id="dealid" name="dealid" value="<?php echo $dealID; ?>" />
            <div class="">
                <div class=" space-y-2">
                    <label for="partner" class="block text-sm font-medium text-gray-700">Partner</label>
                    <select id="partner" name="partnerid"
                        class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                        <?php foreach ($partners as $key => $item) { ?>
                            <option value="<?php echo $item["partnerid"]; ?>" <?php echo $detail["partnerid"] === $item["partnerid"] ? "selected" : "" ?>>
                                <?php echo $item["nazovpartnera"]; ?>
                            </option>
                        <?php } ?>
                    </select>
                </div>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6" id="uctyWrapper">
                <div class="space-y-2">
                    <input type="hidden" name="creditAccountCurrency" id="creditAccountCurrency"
                        value="<?php echo $cubs[0]["mena"] ?>" />
                    <input type="hidden" name="creditAccountValue" id="creditAccountValue"
                        value="<?php echo $cubs[0]["cub"]; ?>" />
                    <label for="creditAccount" class="block text-sm font-medium text-gray-700">Účet kredit</label>
                    <select id="creditAccount"
                        class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                        <?php foreach ($cubs as $key => $item) { ?>
                            <option value="{cub: <?php echo $item["cub"]; ?>, mena: <?php echo $item["mena"]; ?>}">
                                <?php echo $item["cub"]; ?>
                            </option>
                        <?php } ?>
                    </select>
                </div>
                <div class="space-y-2">
                    <input type="hidden" name="debetAccountCurrency" id="debetAccountCurrency"
                        value="<?php echo $debets[0]["mena"] ?>" />
                    <input type="hidden" name="debitAccountValue" id="debitAccountValue"
                        value="<?php echo $cubs[0]["cub"]; ?>" />
                    <label for="debitAccount" class="block text-sm font-medium text-gray-700">Účet debet</label>
                    <select id="debitAccount"
                        class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                        <?php foreach ($debets as $key => $item) { ?>
                            <option value="{cub: <?php echo $item["cub"]; ?>, mena: <?php echo $item["mena"]; ?>}">
                                <?php echo $item["cub"]; ?>
                            </option>
                        <?php } ?>
                    </select>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="space-y-2">
                    <label for="confirmationDate" class="block text-sm font-medium text-gray-700">Dátum
                        konfirmácie</label>
                    <input type="date" id="confirmationDate" name="confirmationDate"
                        value="<?php echo $detail["dat_konfirmacia"]; ?>"
                        class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                </div>
                <div class="space-y-2">
                    <label for="currencyDate" class="block text-sm font-medium text-gray-700">Dátum valuty</label>
                    <input type="date" id="currencyDate" name="currencyDate"
                        value="<?php echo $detail["dat_realizacia"]; ?>"
                        class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="space-y-2">
                    <label for="currencyPair" class="block text-sm font-medium text-gray-700">Menový pár</label>
                    <input type="text" id="currencyPair" name="currencyPair" value="<?php echo $detail["menovypar"]; ?>"
                        readonly
                        class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 sm:text-sm rounded-md">
                </div>
                <div class="space-y-2">
                    <label for="conversionType" class="block text-sm font-medium text-gray-700">Typ konverzie</label>
                    <select id="conversionType" name="conversionType"
                        class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                        <option value="0">Spot</option>
                        <option value="2">Forward Delivery</option>
                    </select>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="space-y-2">
                    <label for="exchangeRate" class="block text-sm font-medium text-gray-700">Kurz</label>
                    <input type="number" id="exchangeRate" name="exchangeRate"
                        value="<?php echo number_format($detail["kurz"], 2, ".", " "); ?>"
                        class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="space-y-2">
                    <label for="clientBuys" class="block text-sm font-medium text-gray-700">Klient nakupuje</label>
                    <div class="mt-1 flex rounded-md gap-4">
                        <input type="number" id="clientBuys" name="clientBuys" readonly="true"
                            value="<?php echo number_format($detail["sumakredit"], 2, ".", " "); ?>"
                            class="flex-1 min-w-0 block w-full px-3 py-2 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm border-gray-300 rounded-md">
                        <span
                            class="inline-flex items-center px-3 rounded-r-md text-sm bg-gray-200 rounded-lg font-semibold">
                            <?php echo $menaKredit ?>
                        </span>
                    </div>
                </div>
                <div class="space-y-2">
                    <label for="clientSells" class="block text-sm font-medium text-gray-700">Klient predáva</label>
                    <div class="mt-1 flex rounded-md gap-4">
                        <input type="number" id="clientSells" name="clientSells" readonly="true"
                            value="<?php echo number_format($detail["sumadebet"], 2, ".", " "); ?>"
                            class="flex-1 min-w-0 block w-full px-3 py-2 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm border-gray-300 rounded-md">
                        <span
                            class="inline-flex items-center px-3 rounded-r-md text-sm bg-gray-200 rounded-lg font-semibold">
                            <?php echo $menaDebet ?>
                        </span>
                    </div>
                </div>
            </div>
            <input type="hidden" name="poolid" id="poolid" />
            <input type="hidden" name="poolData" id="poolData" />
            <div class="flex justify-end space-x-4 pt-4">
                <button type="submit"
                    class="text-white w-full flex items-center justify-center gap-2 transition-all mt-6 text-xl bg-blue-700 hover:bg-blue-900 transition-all focus:outline-none focus:ring-4 focus:ring-gray-300 font-extrabold rounded-lg text-sm px-5 py-2.5 me-2 mb-2">Aktualizovať
                    <svg id="updateChecks" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                        stroke-linejoin="round" class="lucide lucide-check-check">
                        <path d="M18 6 7 17l-5-5" />
                        <path d="m22 10-7.5 7.5L13 16" />
                    </svg>
                    <svg id="updateSpinner" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                        stroke-linejoin="round" class="lucide hidden animate-spin lucide-loader-circle">
                        <path d="M21 12a9 9 0 1 1-6.219-8.56" />
                    </svg></button>
            </div>
        </form>
    </div>
</div>
<?php include "src/Components/pooling/modal.php"; ?>
<script>
    function enablePooling() {
        if (document.getElementById("poolingBtn")) {
            $("#poolingBtn").css("display", "inline-flex");
            generatePool();
        } else {
            $("#poolingBtn").hide();
        }
    }

    //"/api/investicne-zamery/generatePool?mena=EUR&mena2=USD&typ=Konv&cub={cub: *********/7500, mena: EUR}&cum={cub: 2072401/7500, mena: USD}&kurz=25&currPair=EURUSD"

    function generatePool() {
        const exchangeRate = document.getElementById("exchangeRate").value;
        const creditAccount = document.getElementById("creditAccount").value;
        const debitAccount = document.getElementById("debitAccount").value;
        const currencyBuy = document.getElementById("currencyBuy").value;
        const currencySell = document.getElementById("currencySell").value;
        const currPair = document.getElementById("currencyPair").value;
        const kurz = document.getElementById("exchangeRate").value;
        htmx.ajax('POST', `/api/investicne-zamery/generatePool`,
            {
                target: '#modalWrapperko',
                values: {
                    "mena": currencyBuy,
                    "mena2": currencySell,
                    "typ": "Konv",
                    "cub": {
                        cub: creditAccount,
                        mena: currencyBuy
                    },
                    "cum": {
                        cub: debitAccount,
                        mena: currencySell
                    },
                    "kurz": kurz,
                    "currPair": currPair
                }
            }).then(() => {

            });
        $("#generated").val(1);
    }

    $("#exchangeRate").on("change", (e) => {
        console.log("CHANGED");
        if (e.target.value !== "0") {
            enablePooling(e);
        }
    });

    $("#debitAccount").on("change", (e) => {
        const value = JSON.parse(e.target.value);
        const cub = value.cub;
        const mena = value.mena;
        document.getElementById("debetAccountCurrency").value = mena;
        document.getElementById("debitAccountValue").value = cub;
    });

    $("#creditAccount").on("change", (e) => {
        const value = JSON.parse(e.target.value);
        const cub = value.cub;
        const mena = value.mena;
        document.getElementById("creditAccountCurrency").value = mena;
        document.getElementById("creditAccountValue").value = cub;
    });

    $("#partner").on("change", (e) => {
        const value = e.target.value;
        const currencyBuy = $("#currencyBuy").val();
        const currencySell = $("#currencySell").val();
        htmx.ajax('POST', '/api/penazne-fondy/get/account', {
            target: '#uctyWrapper', swap: 'innerHTML', values: {
                partnerid: value,
                currencyBuy: currencyBuy,
                currencySell: currencySell
            }
        }).then(() => {
            // this code will be executed after the 'htmx:afterOnLoad' event,
            // and before the 'htmx:xhr:loadend' event
            console.log('Content inserted successfully!');
        });
    });

    function recalculateTVValues(e) {
        const formData = new FormData(e.currentTarget);
        const clientBuys = formData.get("poolBuyingAllInput");
        const clientSells = formData.get("poolSellingAllInput");
        const poolid = formData.get("poolidModal");
        const poolData = formData.get("poolDetailData");

        document.getElementById("clientBuys").value = clientBuys;
        document.getElementById("clientSells").value = clientSells;
        document.getElementById("poolid").value = poolid;
        document.getElementById("poolData").value = poolData;
    }

    document.getElementById("poolDetailForm").addEventListener("submit", (e) => {
        e.preventDefault();
        recalculateTVValues(e);
    });
</script>
<script src="/src/assets/js/global/investicny-zamer/form.js"></script>
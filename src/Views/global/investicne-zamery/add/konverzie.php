<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

$meny = Connection::getDataFromDatabase("select mena from MENADB where archiv = 'f'", defaultDB)[1];
$buyCurr = isset($_GET["buyCurr"]) ? $_GET["buyCurr"] : "";
$sellCurr = isset($_GET["sellCurr"]) ? $_GET["sellCurr"] : "";
?>
<section id="konverzieWrapper">
    <form id="sellOrBuy" hx-post="/api/investicne-zamery/konverzie/get/initialData" hx-target="#konverzieWrapper"
        class="grid grid-cols-2 items-center p-4 gap-12">
        <div id="buy" style="padding: 2rem 2rem;" name="buy" value="true"
            class="block text-left w-full h-full p-6 <?php echo $_GET["action"] === "buy" ? "bg-green-800 text-white" : "bg-green-300 text-green-900 hover:text-white" ?> rounded-lg hover:bg-green-800 transition-all duration-500">
            <h5 class="mb-2 text-3xl font-extrabold tracking-tight">Klient nakupuje</h5>
            <p class="font-semibold mb-2">Vyberte menu</p>
            <select id="buyCurr" name="buyCurr"
                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                <?php foreach ($meny as $key => $item) { ?>
                    <option value="<?php echo $item["mena"]; ?>" <?php echo $buyCurr === $item["mena"] ? "selected" : "" ?>>
                        <?php echo $item["mena"]; ?></option>
                <?php } ?>
            </select>
        </div>
        <div id="sell" style="padding: 2rem 2rem;" name="sell" value="true"
            class="block text-left w-full group h-full p-6 <?php echo $_GET["action"] === "sell" ? "bg-red-800 text-white" : "bg-red-400 text-red-900 hover:text-white" ?> rounded-lg hover:bg-red-800 transition-all duration-500 ">
            <h5 class="mb-2 text-3xl font-extrabold tracking-tight">Klient predáva</h5>
            <p class="font-semibold mb-2">Vyberte menu</p>
            <select id="sellCurr" name="sellCurr"
                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                <?php foreach ($meny as $key => $item) { ?>
                    <option value="<?php echo $item["mena"]; ?>" <?php echo $sellCurr === $item["mena"] ? "selected" : "" ?>>
                        <?php echo $item["mena"]; ?></option>
                <?php } ?>
            </select>
        </div>
        <section id="cpList" class="col-span-2">
            <button type="submit"
                class="w-full bg-gray-100 hover:bg-gray-300 transition-all text-3xl font-extrabold rounded-xl flex justify-center items-center font-semibold"
                style="padding: 4rem 0">
                <h5 class="mb-2 text-3xl font-extrabold tracking-tight">Potvrdiť</h5>
            </button>
        </section>
    </form>
</section>
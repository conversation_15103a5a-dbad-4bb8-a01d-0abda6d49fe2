<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

$action = $_GET["action"];
$mena = $_GET["mena"];
$akcia = $action === "buy" ? "Nákup" : "Predaj";

if (isset($_SESSION["client"])) {
    $fondid = $_SESSION["client"]["fondid"];
} elseif ($_SESSION["mode"]["mode"] === "admin") {
    $fondid = 1;
} else {
    $fondid = 0;
}

if ($action === "buy") {
    $ricSelect = "SELECT dcr.trh         as ric,
    p.nazovpart<PERSON>a as nazovpartnera,
    p.partnerid     as partnerid,
    dcr.isincurrricid                      as isincurrricid
from dbequitycurr c
      INNER JOIN dbequity e ON c.isincurr = '$matches[1]' || '$mena' AND e.eqid = 'Bonds' AND c.isin = e.isin
      INNER JOIN equitydruh ed ON ed.druheqid = e.druheqid
      INNER JOIN dbequitycurrric dcr ON dcr.isincurr = c.isincurr
      LEFT JOIN partner p ON dcr.trh = p.skratka
      WHERE c.currencytrade = '$mena'
      ";
    $ricValues = Connection::getDataFromDatabase($ricSelect, defaultDB)[1];
    $dlhopisQuery = "SELECT e.cpnaz             AS nazov,
       e.nominalemisie     AS nominal,
       ed.poddruheq        AS druh,
       dcr.trh             AS ric,
       dcr.isincurrricid   AS isincurrricid,
       dcr.isincurrric     AS isincurrric,
       c.currencytrade     AS currency,
       dcr.typ_maj_uctu_cp AS typ,
       p.nazovpartnera     AS nazovpartnera,
       p.partnerid         AS partnerid,
       CASE
           WHEN fm.typ_maj_uctu = 'CEDEL' THEN 'CSOB'
           WHEN fm.typ_maj_uctu = 'CBL' THEN 'SLSP'
           WHEN fm.typ_maj_uctu = 'SK' THEN 'BCPB'
           WHEN fm.typ_maj_uctu = 'CZ' THEN 'BCPP'
           ELSE 'Neidentifikovateľný'
           END             AS custodian
        FROM dbequitycurr c
                INNER JOIN dbequity e
                            ON c.isin = e.isin
                INNER JOIN equitydruh ed
                            ON ed.druheqid = e.druheqid
                INNER JOIN dbequitycurrric dcr
                            ON dcr.isincurr = c.isincurr
                INNER JOIN fondsmu fm
                            ON fm.typ_maj_uctu = dcr.typ_maj_uctu_cp
                LEFT JOIN partner p
                        ON dcr.trh = p.skratka
        WHERE c.isincurr = '$matches[1]' || '$mena'
        AND e.eqid = 'Bonds'
        GROUP BY e.cpnaz,
                e.nominalemisie,
                ed.poddruheq,
                dcr.trh,
                dcr.isincurrricid,
                c.currencytrade,
                dcr.typ_maj_uctu_cp,
                fm.typ_maj_uctu,
                p.nazovpartnera,
                p.partnerid,
                dcr.isincurrric;
        ";
} else {
    $dlhopisQuery = "SELECT SUM(mt.pocet)                      as pocet,
       e.cpnaz                            as nazov,
       e.nominalemisie                 as nominal,
       ed.poddruheq                       as druh,
       dcr.trh                            as ric,
       dcr.isincurrricid                      as isincurrricid,
       c.currencytrade                    as currency,
       dcr.typ_maj_uctu_cp                as typ,
       p.nazovpartnera                    as nazovpartnera,
       p.partnerid                        as partnerid
from majetoktoday mt
         INNER JOIN dbequitycurrric dcr ON dcr.isincurrric = mt.kodaktiva
         INNER JOIN dbequitycurr c ON dcr.isincurr = c.isincurr AND c.currencytrade = mt.mena
         INNER JOIN dbequity e ON c.isin = e.isin
         INNER JOIN equitydruh ed ON ed.druheqid = e.druheqid
         LEFT JOIN partner p ON dcr.trh = p.skratka
WHERE c.isincurr = '$matches[1]' || '$mena'
AND mt.subjektid = $fondid
  AND e.eqid = 'Bonds'
  AND mt.uctovnykod =
      (select uctovnykod from kodobratumd_d where kodobratu = 251 and md_d = 0)
group by nazov, nominal, druh, ric, currency, typ, trh, p.nazovpartnera, p.partnerid, isincurrricid
";
}

$dlhopis = Connection::getDataFromDatabase($dlhopisQuery, defaultDB)[1][0];
$faktor = Connection::getDataFromDatabase("SELECT faktor
FROM floatkupon
    join public.dbequitycurrric d on floatkupon.isincurrric = d.isincurrric
WHERE isincurr = '$matches[1]' || '$mena' AND datefrom < (SELECT max(datum) FROM today) AND datetill >= (SELECT max(datum) FROM today)", defaultDB)[1][0]["faktor"];
$typ = $dlhopis["typ"];

$datumek = Connection::getDataFromDatabase("SELECT max(datum) as datum FROM today", defaultDB)[1][0]["datum"];

$majetkovyPoducetQuery = "SELECT cum
        from fondsmu
        where fondid = $fondid and typ_maj_uctu='$typ'
        order by cum desc";

if ($action === "buy") {
    if ($_SESSION["mode"]["mode"] === "admin") {
        $penaznyPoducetQuery = "SELECT cub
        from spravcabu
        where mena='$mena'
        order by cub desc";
    } else {
        $penaznyPoducetQuery = "SELECT cub
        from fondsbu
        where fondid = $fondid and mena='$mena'
        order by cub desc";
    }
} else {
    $penaznyPoducetQuery = "WITH kodobratu_cte AS (SELECT uctovnykod FROM kodobratumd_d WHERE kodobratu = 251 AND md_d = 0)
SELECT DISTINCT CASE WHEN fb.cub IS NULL AND fm.dest = 'CDCP' AND (e.isin LIKE 'SK%' OR e.isin LIKE 'CS%') THEN '*********/7500' ELSE fb.cub END AS cub
FROM majetoktoday mt
         LEFT JOIN dbequitycurr c ON substr(mt.kodaktiva, 1, 12) = c.isin
         LEFT JOIN dbequity e ON c.isin = e.isin
         LEFT JOIN dbequitycurrric dcr ON dcr.isincurr = c.isincurr
         LEFT JOIN equitydruh ed ON ed.druheqid = e.druheqid
         INNER JOIN fondsmu fm ON fm.typ_maj_uctu = dcr.typ_maj_uctu_cp
         LEFT JOIN fondsbu fb ON fb.banka = fm.dest AND fb.mena = c.currencytrade
         CROSS JOIN kodobratu_cte k
WHERE c.isincurr = '$matches[1]' || '$mena'
  AND mt.mena = c.currencytrade
  AND mt.uctovnykod = k.uctovnykod
  AND e.eqid = 'Bonds'
    ";
}
$maxToday = Connection::getDataFromDatabase("SELECT max(datum) as datum FROM today", defaultDB)[1][0]["datum"];

$majetkovyPoducet = Connection::getDataFromDatabase($majetkovyPoducetQuery, defaultDB)[1];
$penaznyPoducet = Connection::getDataFromDatabase($penaznyPoducetQuery, defaultDB)[1];

?>
<section class="relative px-4">
    <div id="toast"></div>
    <form id="createZamer" hx-post="/api/investicne-zamery/cp/create" hx-target="#toast" hx-indicator="#updateSpinner">
        <input type="hidden" name="dealid" id="dealid" value="<?php echo $dealID; ?>" />
        <input type="hidden" name="faktor" id="faktor" value="<?php echo $faktor; ?>" />
        <input type="hidden" name="isin" id="isin" value="<?php echo $matches[1]; ?>" />
        <input type="hidden" name="dealidseq" id="dealidseq" value="<?php echo $detail["dealidseq"]; ?>" />
        <input type="hidden" name="partnerid" id="parnterid" value="<?php echo $dlhopis["partnerid"]; ?>" />
        <input type="hidden" name="poolid" id="poolid" />
        <input type="hidden" name="eqid" id="eqid" value="Bonds" />
        <input type="hidden" name="poolData" id="poolData" />
        <input type="hidden" name="maxToday" id="maxToday" value="<?php echo $maxToday; ?>" />
        <div class="flex justify-between py-4 items-center border-b">
            <h2 class="scroll-m-20 text-3xl font-semibold tracking-tight first:mt-0">
                <input type="hidden" name="action" id="action" value="<?php echo $action; ?>" />
                <span
                    class="text-white font-bold <?php echo $action === "sell" ? "bg-red-500" : "bg-green-500" ?> px-3 py-0.5 pb-1 rounded-xl"><?php echo $akcia ?></span>
                <span
                    class="dark:text-gray-100 text-gray-900 font-bold  px-2 py-0.5 rounded-lg"><?php echo $dlhopis["nazov"]; ?></span>
            </h2>
        </div>
        <div class="dark:bg-gray-700 dark:text-gray-100 bg-gray-100 rounded-b-lg p-3 flex justify-between w-full">
            <?php if ($action === "buy") { ?>
                <div class="flex gap-2 items-center">
                    <small>RIC:</small>
                    <select id="ric" name="ric"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg p-2 py-1 focus:ring-blue-500 focus:border-blue-500 block p-2">
                        <?php foreach ($ricValues as $key => $item) { ?>
                            <option value="<?php echo $item["isincurrricid"]; ?>">
                                <?php echo $item["ric"]; ?>
                            </option>
                        <?php } ?>
                    </select>
                </div>
            <?php } else { ?>
                <input type="hidden" name="ric" id="ric" value="<?php echo $dlhopis["isincurrricid"]; ?>" />
                <small>RIC: <strong class="text-lg"><?php echo $dlhopis["ric"] ?></strong></small>
            <?php } ?>
            <input type="hidden" name="nazovpartnera" id="nazovpartnera" />
            <small>Partner: <strong id="nazovpartneraValue"
                    class="text-lg"><?php echo $dlhopis["nazovpartnera"] ?></strong></small>
            <small>Typ: <strong class="text-lg"><?php echo $dlhopis["typ"] ?></strong></small>
            <small>Druh: <strong class="text-lg"><?php echo $dlhopis["druh"] ?></strong></small>
        </div>
        <div class="grid grid-cols-1 my-4 dark:text-gray-100 md:grid-cols-2 gap-6">
            <section class="shadow-md p-4 flex flex-col gap-4 rounded-md">
                <div class="flex gap-2 items-center">
                    <small>Peňažný podúčet:</small>
                    <?php if ($action === "buy") { ?>
                        <select id="spravbu" name="spravbu"
                            class="bg-gray-50 border w-full border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block p-2">
                            <?php foreach ($penaznyPoducet as $key => $item) { ?>
                                <option value="<?php echo $item["cub"]; ?>">
                                    <?php echo $item["cub"]; ?>
                                </option>
                            <?php } ?>
                        </select>
                    <?php } else {
                        if (sizeof($penaznyPoducet) > 1) { ?>
                            <select id="spravbu" name="spravbu"
                                class="bg-gray-50 border w-full border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block p-2">
                                <?php foreach ($penaznyPoducet as $key => $item) { ?>
                                    <option value="<?php echo $item["cub"]; ?>">
                                        <?php echo $item["cub"]; ?>
                                    </option>
                                <?php } ?>
                            </select>
                        <?php } else { ?>
                            <input type="hidden" name="spravbu" id="spravbu" value="<?php echo $penaznyPoducet[0]["cub"] ?>" />
                            <strong class="text-lg"><?php echo $penaznyPoducet[0]["cub"] ?></strong>
                        <?php }
                    } ?>
                </div>
            </section>
            <section class="shadow-md p-4 flex flex-col gap-4 rounded-md">
                <div class="flex gap-2 dark:text-gray-100 items-center">
                    <small>Majetkový podúčet:</small>
                    <select id="cum" name="cum"
                        class="dark:bg-gray-700 dark:text-gray-100 bg-gray-50 border w-full border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block p-2">
                        <?php foreach ($majetkovyPoducet as $key => $item) { ?>
                            <option value="<?php echo $item["cum"]; ?>">
                                <?php echo $item["cum"]; ?>
                            </option>
                        <?php } ?>
                    </select>
                </div>
            </section>
        </div>
        <div class="grid grid-cols-1 my-4 md:grid-cols-2 gap-6">
            <section class="shadow-md p-8 flex flex-col gap-4 rounded-md">
                <div>
                    <label for="datumpokynu" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Dátum
                        pokynu</label>
                    <input type="date" id="datumpokynu" name="datumpokynu" value="<?php echo $datumek ?>"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                        required />
                </div>
                <div>
                    <label for="platnostod" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Dátum
                        od</label>
                    <input type="date" id="platnostod" name="platnostod" value="<?php echo $datumek ?>"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                        required />
                </div>
                <div>
                    <label for="platnostdo" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Dátum
                        do</label>
                    <input type="date" id="platnostdo" name="platnostdo" value="<?php echo $datumek ?>"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                        required />
                </div>
                <section class="flex items-end gap-4">
                    <div class="w-full">
                        <label for="pocetkusov"
                            class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Počet kusov</label>
                        <?php if (isset($_SESSION["client"]) || $_SESSION["mode"]["mode"] === "admin") { ?>
                            <input type="number" id="pocetkusov" name="pocetkusov" min="1"
                                class="border border-gray-300 text-gray-900 dark:text-gray-100 p-2 dark:bg-gray-700 bg-gray-50 text-sm rounded-lg block w-full"
                                required />
                        <?php } else { ?>
                            <input type="number" id="pocetkusov" name="pocetkusov"
                                placeholder="Počet kusov (dynamicky sa vyplní z poolingu)" readonly="true"
                                max="<?php echo $dlhopis["pocet"]; ?>" min="1"
                                class="border border-gray-300 text-gray-900 dark:bg-gray-900 bg-gray-200 text-sm rounded-lg block w-full "
                                required />
                        <?php } ?>
                    </div>
                    <?php if ($action === "sell") { ?>
                        <span class="w-1/2 -mt-2 dark:text-gray-100">Max. počet: <strong
                                class="p-1 px-2 text-white text-xs rounded-lg"
                                style="background-color: #71717a;"><?php echo $dlhopis["pocet"]; ?></strong></span>
                        <?php if (isset($_SESSION["client"])) { ?>
                            <button type="button" id="selectHalf" value="<?php echo $dlhopis["pocet"] / 2; ?>"
                                class="text-white dark:bg-gray-900 bg-gray-800 dark:text-gray-100 hover:bg-gray-900 focus:outline-none focus:ring-4 focus:ring-gray-300 font-extrabold rounded-lg text-sm px-5 py-2.5">
                                1/2
                            </button>
                            <button type="button" id="selectAll" value="<?php echo $dlhopis["pocet"]; ?>"
                                class="focus:outline-none text-white bg-blue-700 hover:bg-purple-800 focus:ring-4 focus:ring-purple-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-purple-900">
                                Všetky
                            </button>
                        <?php }
                    } ?>
                </section>
                <section class="flex items-center gap-4">
                    <div class="w-full">
                        <label for="dobavysporm"
                            class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Dátum majetkového
                            vysporiadania</label>
                        <input type="text" id="dobavysporm" name="dobavysporm" placeholder="Počet dní"
                            class="bg-gray-50 border border-gray-300 text-gray-900 p-2 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full  dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                            required />
                    </div>
                    <input type="date" id="datumvysporm" name="datumvysporm" value="" style="display: none;"
                        class="w-full mt-4 text-white flex justify-center border-none items-center font-bold h-full rounded-lg" />
                </section>
                <section class="flex items-center gap-4">
                    <div class="w-full">
                        <label for="dobavysporf"
                            class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Dátum finančného
                            vysporiadania</label>
                        <input type="text" id="dobavysporf" name="dobavysporf" placeholder="Počet dní"
                            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm p-2 rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full  dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                            required />
                    </div>
                    <input type="date" id="datumvysporf" name="datumvysporf" value="" style="display: none;"
                        class="w-full mt-4 text-white flex justify-center border-none items-center font-bold h-full rounded-lg" />
                </section>
            </section>
            <section class="shadow-md p-8 flex flex-col gap-4 rounded-md">
                <div>
                    <label for="nominal" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Nominálna
                        hodnota</label>
                    <input type="text" id="nominal" name="nominal"
                        value="<?php echo number_format($dlhopis["nominal"], 2, ".", " "); ?>"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 p-2 focus:border-blue-500 block w-full  dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                        required />
                </div>
                <div>
                    <label for="limitnykurz"
                        class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Limitný kurz</label>
                    <input type="text" id="limitnykurz" name="limitnykurz"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 p-2 focus:border-blue-500 block w-full  dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                        required />
                </div>
                <div>
                    <label for="limitprice" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Limitná
                        cena</label>
                    <?php if (isset($_SESSION["client"]) || $_SESSION["mode"]["mode"] === "admin") { ?>
                        <input type="text" id="limitprice" name="limitprice"
                            class="bg-gray-200 border border-gray-300 p-2 dark:bg-gray-700 dark:text-gray-100 text-gray-900 text-sm rounded-lg w-full"
                            required />
                    <?php } else { ?>
                        <input type="text" id="limitprice" name="limitprice" readonly="true"
                            placeholder="(dynamicky sa vypočíta z poolingu)"
                            class="bg-gray-200 border border-gray-300 p-2 dark:bg-gray-900 text-gray-900 text-sm rounded-lg w-full"
                            required />
                    <?php } ?>
                </div>
                <div>
                    <label for="percentopoplatku"
                        class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Percento
                        poplatku</label>
                    <input type="text" id="percentopoplatku" name="percentopoplatku" value="0"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg p-2 focus:ring-blue-500 focus:border-blue-500 block w-full  dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                        required />
                </div>
                <div class="flex items-center dark:text-gray-100 justify-between">
                    <div><small>Custodian: </small><strong class="text-lg"><?php echo $dlhopis["custodian"]; ?></strong>
                    </div>
                    <input type="hidden" name="mena" id="mena" value="<?php echo $mena; ?>" />
                    <div><small>Mena: </small><strong class="text-lg"><?php echo $mena; ?></strong></div>
                </div>
                <div class="flex justify-between gap-4 flex-col">
                    <?php
                    $typ = Connection::getDataFromDatabase("SELECT pokyn FROM fonds WHERE fondid = $fondid", defaultDB)[1][0]["pokyn"];
                    if ($typ == "1") { ?>
                        <label for="assigneduser" class="block text-sm font-medium text-gray-900 dark:text-white">Zadávateľ
                            pokynu:</label>
                        <input type="hidden" name="assigneduser" id="assigneduser" value="<?php echo $fondid; ?>" />
                        <span class="dark:text-gray-200">Klient</span>
                        <?php
                    } else {
                        ?>
                        <div>
                            <label for="assigneduser"
                                class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Zadávateľ
                                pokynu:</label>
                            <select id="assigneduser" name="assigneduser"
                                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700
                                 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                                <?php
                                $users = Connection::getDataFromDatabase("SELECT u.userid, u.username
                                FROM users u
                                        INNER JOIN usergroupusers ug ON u.userid = ug.userid
                                WHERE u.lasttime > '2024-09-01'
                                GROUP BY u.userid", defaultDB)[1];
                                foreach ($users as $user) { ?>
                                    <option value="<?php echo $user["userid"]; ?>"><?php echo $user["username"]; ?></option>
                                <?php } ?>
                            </select>
                        </div>
                    <?php } ?>
                    <section class="flex items-center gap-10">
                        <div class="w-full">
                            <label for="datumpokynu"
                                class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Dátum
                                pokynu</label>
                            <input type="datetime-local" id="datumpokynu" name="datumpokynu"
                                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700
                             dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" required />
                        </div>
                        <div class="w-full">
                            <label for="pokyn_ako"
                                class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Spôsob
                                prijatia pokynu</label>
                            <select id="pokyn_ako" name="pokyn_ako"
                                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700
                             dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                                <option value="0">Onboarding</option>
                                <option value="1">Osobne</option>
                                <option value="2">Email</option>
                                <option value="3">Telefón</option>
                            </select>
                        </div>
                    </section>
                </div>
                <div id="buttonToPool">
                    <?php if (!isset($_SESSION["client"])) { ?>
                        <button type="button" id="poolingBtn" data-modal-target="pooling-modal"
                            data-modal-toggle="pooling-modal" hx-target="#modalWrapperko"
                            class="text-white w-full flex items-center justify-center gap-2 hidden transition-all mt-6 text-xl bg-gray-800 focus:outline-none focus:ring-4 focus:ring-gray-300 font-extrabold rounded-lg text-sm px-5 py-2.5 me-2 mb-2"><svg
                                xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
                                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                class="lucide lucide-database">
                                <ellipse cx="12" cy="5" rx="9" ry="3" />
                                <path d="M3 5V19A9 3 0 0 0 21 19V5" />
                                <path d="M3 12A9 3 0 0 0 21 12" />
                            </svg> Pool</button>
                        <input type="hidden" id="generated" name="generated" value="0" />
                    <?php } ?>
                    <button type="submit"
                        class="focus:outline-none flex w-full text-center justify-center items-center transition-all  text-xl gap-2 text-white bg-green-700 hover:bg-green-800 focus:ring-4 focus:ring-green-300 rounded-lg text-md font-extrabold px-5 py-2.5 dark:bg-green-600 dark:hover:bg-green-700 dark:focus:ring-green-800">
                        <span>Potvrdiť</span>
                        <svg id="updateSpinner" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                            stroke-linecap="round" stroke-linejoin="round"
                            class="lucide hidden animate-spin lucide-loader-circle">
                            <path d="M21 12a9 9 0 1 1-6.219-8.56" />
                        </svg>
                    </button>
                </div>
            </section>
        </div>
    </form>
</section>
<?php
if (isset($_SESSION["client"]) || $_SESSION["mode"]["mode"] === "admin") { ?>
    <script>
        document.addEventListener('htmx:afterSettle', function (evt) {
            document.getElementById("updateChecks").style.display = "block";
            document.getElementById("updateSpinner").style.display = "none";
        });

        $("#dobavysporm").on("keyup", (e) => {
            const days = parseInt(e.target.value);
            const maxToday = document.getElementById("maxToday").value;
            if (Number.isNaN(days) || days < 0) {
                $("#datumvysporm").removeClass("bg-blue-800");
                document.getElementById("datumvysporm").value = "";
                document.getElementById("datumvysporm").style.display = "none";
                e.target.value = "";
            } else {
                const today = new Date(maxToday);
                const futureDate = new Date(today);
                futureDate.setDate(today.getDate() + days);
                document.getElementById("datumvysporm").style.display = "inline-flex";
                $("#datumvysporm").addClass("bg-blue-800");
                document.getElementById("datumvysporm").value = futureDate.toISOString().split('T')[0];
            }
        });

        $("#datumvysporm").on("change", (e) => {
            const date = e.target.value;
            const maxToday = document.getElementById("maxToday").value;
            const oneDay = 24 * 60 * 60 * 1000; // hours*minutes*seconds*milliseconds
            const firstDate = new Date(maxToday);
            const secondDate = new Date(date);

            const diffDays = Math.round(Math.abs((firstDate - secondDate) / oneDay));
            document.getElementById("dobavysporm").value = diffDays;
        });

        $("#dobavysporf").on("keyup", (e) => {
            const days = parseInt(e.target.value);
            const maxToday = document.getElementById("maxToday").value;
            if (Number.isNaN(days) || days < 0) {
                $("#datumvysporf").removeClass("bg-blue-800");
                document.getElementById("datumvysporf").value = "";
                document.getElementById("datumvysporf").style.display = "none";
                e.target.value = "";
            } else {
                const today = new Date(maxToday);
                const futureDate = new Date(today);
                futureDate.setDate(today.getDate() + days);
                document.getElementById("datumvysporf").style.display = "inline-flex";
                $("#datumvysporf").addClass("bg-blue-800");
                document.getElementById("datumvysporf").value = futureDate.toISOString().split('T')[0];
            }
        });

        $("#datumvysporf").on("change", (e) => {
            const date = e.target.value;
            const maxToday = document.getElementById("maxToday").value;
            const oneDay = 24 * 60 * 60 * 1000; // hours*minutes*seconds*milliseconds
            const firstDate = new Date(maxToday);
            const secondDate = new Date(date);

            const diffDays = Math.round(Math.abs((firstDate - secondDate) / oneDay));
            document.getElementById("dobavysporf").value = diffDays;
        });

        $("#selectHalf").on("click", (e) => {
            console.log(e.target.value);
            document.getElementById("pocetkusov").value = e.target.value;
        });

        $("#selectAll").on("click", (e) => {
            console.log(e.target.value);
            document.getElementById("pocetkusov").value = e.target.value;
        });

        $("#ric").on("change", (e) => {
            const values = JSON.parse(e.target.value.replace(/'/g, '"'));
            document.getElementById("nazovpartneraValue").innerHTML = values[1];
            document.getElementById("nazovpartnera").value = values[1];
            document.getElementById("partnerid").value = values[0];
        });

        document.getElementById("createZamer").addEventListener("submit", (e) => {
            document.getElementById("updateChecks").style.display = "none";
            document.getElementById("updateSpinner").style.display = "block";
        });

        document.getElementById("limitnykurz").addEventListener("change", (e) => {
            const kurz = e.target.value;
            const pocet = document.getElementById("pocetkusov").value;
            let nominalko = parseInt(document.getElementById("nominal").value);
            let faktor = parseInt(document.getElementById("faktor").value);
            console.log(kurz / 100 * nominalko * faktor * pocet);
            document.getElementById("limitprice").value = (kurz / 100 * nominalko * faktor * pocet).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }).replace(',', ' ');
        });
    </script>
<?php } else { ?>
    <?php include "src/Components/pooling/modal.php"; ?>
    <script src="/src/assets/js/global/investicny-zamer/createCPZamer.js"></script>
<?php } ?>
<script src="/src/assets/js/global/investicny-zamer/form.js"></script>
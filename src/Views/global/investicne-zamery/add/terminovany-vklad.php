<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
$menyRes = Connection::getDataFromDatabase("SELECT * FROM menadb ORDER BY poradie", defaultDB);
$meny = $menyRes[1];
?>
<nav class="flex py-3 px-5 text-gray-700 dark:text-gray-100 border-b w-full dark:bg-gray-500 bg-white z-20" style="position: sticky; top: 4.5rem;"
    aria-label="Breadcrumb">
    <ol class="inline-flex items-center space-x-1 md:space-x-2 rtl:space-x-reverse">
        <li class="inline-flex items-center">
            <a href="/"
                class="inline-flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-100 hover:text-blue-600 dark:hover:text-white">
                <svg xmlns="http://www.w3.org/2000/svg" width="13" height="13" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide lucide-house">
                    <path d="M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8" />
                    <path
                        d="M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z" />
                </svg>
                Domov
            </a>
        </li>
        <li>
            <div class="flex items-center">
                <svg class="rtl:rotate-180 block w-2 h-2 mx-1 text-gray-400 " aria-hidden="true"
                    xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                        d="m1 9 4-4-4-4" />
                </svg>
                <a href="/investicne-zamery"
                    class="ms-1 cursor-pointer hover:underline text-sm font-medium text-gray-700 dark:text-gray-100 hover:text-blue-600 md:ms-2 dark:hover:text-white">Investičné
                    zámery</a>
            </div>
        </li>
        <li>
            <div class="flex items-center">
                <svg class="rtl:rotate-180 block w-2 h-2 mx-1 text-gray-400 " aria-hidden="true"
                    xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                        d="m1 9 4-4-4-4" />
                </svg>
                <span hx-get="/investicne-zamery/pridat/terminovany-vklad" hx-target="#pageContentMain"
                    hx-replace-url="true"
                    class="ms-1 cursor-pointer hover:underline text-sm font-medium dark:text-blue-200 text-blue-700 hover:text-blue-600 md:ms-2 dark:hover:text-white">Terminované
                    vklady</span>
            </div>
        </li>
        <li>
            <div class="flex items-center">
                <svg class="rtl:rotate-180 block w-2 h-2 mx-1 text-gray-400 " aria-hidden="true"
                    xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                        d="m1 9 4-4-4-4" />
                </svg>
                <span hx-get="/investicne-zamery/pridat/terminovany-vklad" hx-target="#pageContentMain"
                    hx-replace-url="true"
                    class="ms-1 cursor-pointer hover:underline text-sm font-bold dark:text-blue-100 text-blue-800 hover:text-blue-600 md:ms-2 dark:hover:text-white">1.
                    Výber meny</span>
            </div>
        </li>
    </ol>
</nav>
<section id="mainElementForm" class="overflow-hidden relative flex">
    <div class="grid md:grid-cols-4 p-8 gap-6 w-full">
        <?php foreach ($meny as $mena) { ?>
            <form id="terminovanyFormular" class="w-full mb-0"
                hx-post="/investicne-zamery/pridat/terminovany-vklad/vyber-uctu" hx-target="#pageContentMain"
                hx-replace-url="true">
                <input type="hidden" name="mena" id="mena" value="<?php echo $mena["mena"]; ?>" />
                <button type="submit" id="<?php echo $mena["mena"]; ?>"
                    class="dark:bg-gray-600 bg-white w-full border currencyButton cursor-pointer transition-all <?php echo ($mena["mena"] === "EUR" || $mena["mena"] === "USD" ? "border-blue-400 dark:hover:bg-blue-400 hover:bg-blue-200 border-2" : "border-gray-200 dark:hover:bg-gray-400 hover:bg-gray-200") ?> p-2 shadow rounded-lg flex-col items-center gap-4 flex">
                    <?php if ($mena["mena"] === "EUR") { ?>
                        <svg class="w-10 h-10 text-gray-800 dark:text-white" aria-hidden="true"
                            xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="3"
                                d="M6 10h9.231M6 14h9.231M18 5.086A5.95 5.95 0 0 0 14.615 4c-3.738 0-6.769 3.582-6.769 8s3.031 8 6.769 8A5.94 5.94 0 0 0 18 18.916" />
                        </svg>
                    <?php } else if ($mena["mena"] === "USD") { ?>
                            <svg class="w-10 h-10 text-gray-800 dark:text-white" aria-hidden="true"
                                xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M8 17.345a4.76 4.76 0 0 0 2.558 1.618c2.274.589 4.512-.446 4.999-2.31.487-1.866-1.273-3.9-3.546-4.49-2.273-.59-4.034-2.623-3.547-4.488.486-1.865 2.724-2.899 4.998-2.31.982.236 1.87.793 2.538 1.592m-3.879 12.171V21m0-18v2.2" />
                            </svg>
                    <?php } else { ?>
                            <h5 class="mb-2 text-3xl font-bold tracking-tight text-gray-900 dark:text-white">
                            <?php echo $mena["mena"]; ?>
                            </h5>
                    <?php } ?>
                    <a>
                        <h5 class="mb-2 text-md font-medium tracking-tight text-gray-900 dark:text-white">
                            <?php echo $mena["menanaz"]; ?>
                        </h5>
                    </a>
                </button>
            </form>
        <?php } ?>
    </div>
</section>
<script src="/src/assets/js/global/investicny-zamer/form.js"></script>
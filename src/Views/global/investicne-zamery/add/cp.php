<?php
$bread = "";
if (str_contains($_SERVER['REQUEST_URI'], "akcie")) {
    $bread = "Akcie";
    $textik = "akcií";
    $eqid = "Shares";
} else if (str_contains($_SERVER['REQUEST_URI'], "dlhopisy")) {
    $bread = "Dlhopisy";
    $textik = "dlhopisov";
    $eqid = "Bonds";
} else if (str_contains($_SERVER['REQUEST_URI'], "podielove-fondy")) {
    $bread = "Podielové fondy";
    $textik = "podielových fondov";
    $eqid = "Fonds";
}

?>
<form id="sellOrBuy" hx-post="/api/investicne-zamery/cp/get/initialData" hx-target="#cpList"
    class="grid grid-cols-2 items-center p-4 gap-12">
    <input type="hidden" name="eqid" id="eqid" value="<?php echo $eqid; ?>" />
    <button type="submit" id="buy" style="padding: 2rem 2rem;" name="buy" value="true"
        class="block w-full cursor-pointer h-full p-6 <?php echo $_GET["action"] === "buy" ? "bg-green-800 text-white" : "bg-green-300 text-green-900 hover:text-white" ?> rounded-lg hover:bg-green-800 transition-all duration-500">
        <h5 class="mb-2 text-5xl font-extrabold tracking-tight">Nákup</h5>
        <p class="font-semibold">Vytvoriť investičný zámer nákupu <?php echo $textik; ?></p>
    </button>
    <button type="submit" id="sell" style="padding: 2rem 2rem;" name="sell" value="true"
        class="block w-full cursor-pointer group h-full p-6 <?php echo $_GET["action"] === "sell" ? "bg-red-800 text-white" : "bg-red-400 text-red-900 hover:text-white" ?> rounded-lg hover:bg-red-800 transition-all duration-500 ">
        <h5 class="mb-2 text-5xl font-extrabold tracking-tight">Predaj</h5>
        <p class="font-semibold">Vytvoriť investičný zámer predaja <?php echo $textik; ?></p>
    </button>
</form>
<section id="cpList" class="p-4">
    <?php if (isset($_GET["action"])) {
        include "src/Controllers/global/investicne-zamery/cp/get/initialData.php";
    } else { ?>
        <div class="w-full dark:bg-gray-700 dark:text-gray-100 bg-gray-100 rounded-xl flex justify-center items-center font-semibold" style="padding: 15rem 0">
            <span>Pre zobrazenie dát zadajte čo chcete urobiť</span>
        </div>
    <?php } ?>
</section>
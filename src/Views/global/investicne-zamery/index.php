<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
require_once "/home/<USER>/www/src/Components/inputs/selectToLink.php";

if (isset($_SESSION["client"])) {
    $fondid = $_SESSION["client"]["fondid"];
} elseif ($_SESSION["mode"]["mode"] === "admin") {
    $fondid = 1;
} else {
    $fondid = 0;
}
$query = "SELECT kcp.loguserid                     as userid,
       to_char(kcp.datpok, 'DD.MM.YYYY') as d_datpok,
       e.cpnaz                           as cpnaz,
       e.cpnaz<PERSON>ratka                    as cpnazskratka,
       c.currencytrade                   as currencytrade,
       d.cislo                           as cislo,
       kcp.limitkurz                     as limitkurz,
       kcp.limitprice                    as limitprice,
       kcp.kusov                         as kusov,
       kcp.dru<PERSON><PERSON><PERSON><PERSON>                   as druhob<PERSON><PERSON>,
       kcp.dealid                        as dealid,
       kcp.partnerid                     as partnerid,
       kcp.eqid                          as eqid,
       CASE
           WHEN kcp.eqid = 'Bonds' THEN 'Dlhopis'
           WHEN kcp.eqid = 'Shares' THEN 'Akcia'
           WHEN kcp.eqid = 'Fonds' THEN 'Fond'
           ELSE 'N/A'
           END                           AS eqid_trans,
        kcp.assigneduserid                                                        as assigneduserid
from konfirmaciacp kcp,
     dbequity e,
     dbequitycurr c,
     dbequitycurrric r,
     equitydruh ed,
     dennikpm d
where ed.druheqid = e.druheqid
  and e.eqid in ('Bonds', 'Shares', 'Fonds')
  and c.isin = e.isin
  and r.isincurr = c.isincurr
  and kcp.dealid = d.dealid
  and kcp.isin = e.isin
  and kcp.currencyidtrade = c.currencytrade
  and kcp.ric = r.ric
  and kcp.subjektid = $fondid
  and kcp.logactivityid in (4, 6, 7, 8, 9)
union all
select k.loguserid                                                             as loguserid,
       to_char(k.dk_td, 'DD.MM.YYYY')                                          as d_datpok,
       k.CUB                                                                   as cpnaz,
       k.CUB                                                                   as cpnazskratka,
       k.MENA                                                                  as currencytrade,
       d.cislo                                                                 as cislo,
       null                                                                    as limitkurz,
       k.sum_td                                                                as limitprice,
       null                                                                    as kusov,
       to_char(k.z_td, 'dd.mm.yyyy') || ' - ' || to_char(k.k_td, 'dd.mm.yyyy') as druhobchodu,
       k.dealid                                                                as dealid,
       k.partnerid                                                             as partnerid,
       'KTV'                                                                   as eqid,
       'KTV'                                                                   as eqid_trans,
       k.assigneduserid                                                        as assigneduserid
from konfirmaciaktv k,
     dennikpm d
where k.logactivityid in (4, 6, 7, 8, 9)
  and k.dealid in (select dealid from rezervacia)
  and k.dealid = d.dealid
  and k.subjektid = $fondid
union all
select kk.loguserid                              as loguserid,
       to_char(kk.dat_konfirmacia, 'dd.mm.yyyy') as d_datpok,
       kk.menovypar                              as cpnaz,
       kk.menovypar                              as cpnazskratka,
       kk.menakredit                             as currencytrade,
       d.cislo                                   as cislo,
       null                                      as limitkurz,
       kk.sumakredit                             as limitprice,
       null                                      as kusov,
       'SPOT'                                    as druhobchodu,
       kk.dealid                                 as dealid,
       kk.partnerid                              as partnerid,
       'Konv'                                    as eqid,
       'Konverzia'                               as eqid_trans,
       kk.assigneduserid                         as assigneduserid
from konverzia kk,
     dennikpm d
where kk.subjektid = $fondid
  and kk.dealid = d.dealid
  and kk.logactivityid in (4, 6, 7, 8, 9)
order by dealid
";
$investicneDataRes = Connection::getDataFromDatabase($query, defaultDB);
$currentDate = Connection::getDataFromDatabase("SELECT max(datum) as datum FROM today", defaultDB)[1][0]["datum"];
?>

<section class="py-3 sm:py-5 mb-1 px-4">
    <div class="mx-auto">
        <section class="inline-flex justify-between w-full border-b pb-3">
            <h2 class="scroll-m-20 dark:text-gray-50 text-2xl font-semibold">
                Zoznam vytvorených investičných zámerov
            </h2>
            <?php echo SelectToLink::render("Pridať nový investičný zámer", ["Termínovaný vklad", "Dlhopisy", "Akcie", "Podielové fondy", "Konverzie"], "investicnyZamer", ["terminovany-vklad", "dlhopisy", "akcie", "podielove-fondy", "konverzie"], ''); ?>
        </section>
        <div class="overflow-x-visible px-4">
            <?php if (sizeof($investicneDataRes[1]) === 0) { ?>
                <div class="p-4 mb-4 text-sm text-blue-800 rounded-lg bg-blue-50 dark:bg-gray-700 dark:text-blue-400"
                    role="alert">
                    <span class="font-bold">Žiadne záznamy!</span> Momentálne nie sú vytvorené žiadne investičné zámery.
                </div>
            <?php } else {
                ?>
                <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                        <tr>
                            <th scope="col" class="px-4 py-3">Typ</th>
                            <th scope="col" class="px-4 py-3">Mena</th>
                            <th scope="col" class="px-4 py-3">Suma</th>
                            <th scope="col" class="px-4 py-3">Dátum</th>
                            <th scope="col" class="px-4 py-3">Investičný nástroj</th>
                            <th scope="col" class="px-4 py-3">Druh obchodu</th>
                            <th scope="col" class="px-4 py-3">Vytvoril</th>
                            <th scope="col" class="px-4 py-3">Riešiteľ</th>
                            <th scope="col" class="px-4 py-3"></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        foreach ($investicneDataRes[1] as $key => $podienik) {
                            $dealid = $podienik["dealid"];
                            $userID = $podienik["userid"];
                            $assignedUserID = $podienik["assigneduserid"];
                            $user = Connection::getDataFromDatabase("SELECT userid, username from users WHERE userid = $userID", defaultDB)[1][0];
                            if ($assignedUserID !== NULL) {
                                $assignedUser = Connection::getDataFromDatabase("SELECT DISTINCT userid, username, m.mentionedid, m.initiatorid, m.notificationid, m.objektid, m.id
                                    from users
                                    LEFT JOIN mentions m ON userid = m.mentionedid
                                    WHERE userid = $assignedUserID AND m.objektid = $dealid
                                    GROUP BY m.notificationid, users.userid, m.id", defaultDB)[1][0];
                            } else {
                                $assignedUser = "";
                            }

                            $color = "#d4d4d8";
                            $url = "";

                            $partnerid = $podienik["partnerid"];

                            switch ($podienik["eqid_trans"]) {
                                case "KTV":
                                    $url = "terminovany-vklad";
                                    $color = "#38bdf8";
                                    break;
                                case "Konverzia":
                                    $url = "konverzia";
                                    $color = "#7dd3fc";
                                    break;
                                case "Akcia":
                                    $url = "akcia";
                                    $color = "#FF9F40";
                                    $eqid = "Shares";
                                    break;
                                case "Dlhopis":
                                    $url = "dlhopis";
                                    $color = "#FF6384";
                                    $eqid = "Bonds";
                                    break;
                                case "Fond":
                                    $url = "fond";
                                    $eqid = "Fonds";
                                    break;
                            }

                            $druhobchodu = "";

                            switch ($podienik['druhobchodu']) {
                                case "predaj":
                                    $druhobchodu = '<span class="bg-red-200 text-red-800 text-xs font-medium me-2 px-2.5 py-0.5 rounded dark:bg-gray-700 dark:text-red-400 border border-red-400">Predaj</span>';
                                    break;
                                case "nakup":
                                    $druhobchodu = '<span class="bg-green-200 text-green-800 text-xs font-medium me-2 px-2.5 py-0.5 rounded dark:bg-gray-700 dark:text-green-400 border border-green-400">Nákup</span>';
                                    break;
                                default:
                                    $druhobchodu = $podienik["druhobchodu"];
                                    break;
                            }

                            ?>
                            <tr id="<?php echo $dealid; ?>"
                                class="odd:bg-white odd:dark:bg-gray-900 transition-all cursor-pointer dark:hover:bg-gray-600 even:bg-gray-50 even:dark:bg-gray-800 border-b dark:border-gray-700 border-gray-200">
                                <td class="px-4 py-2">
                                    <p style="background: <?php echo $color; ?>;"
                                        class="text-sm dark:text-gray-800 w-full text-center font-bold px-2 py-0.5 rounded">
                                        <?php echo $podienik['eqid_trans'] ?>
                                    </p>
                                </td>
                                <td class="px-4 py-2">
                                    <span
                                        class="text-sm font-normal px-2 py-0.5 rounded"><?php print_r($podienik['currencytrade']); ?></span>
                                </td>
                                <td class="px-4 py-2">
                                    <span class="text-sm font-bold px-2 py-0.5 rounded"><?php
                                    if ($podienik["eqid_trans"] !== "Konverzia") {
                                        echo number_format(round($podienik['limitprice'], 2), 2, ".", " ");
                                    } else {
                                        echo number_format(round($podienik['limitprice'], 2), 2, ".", " ");
                                    }
                                    ?></span>
                                </td>
                                <td class="px-4 py-2">
                                    <span
                                        class="text-sm font-normal px-2 py-0.5 rounded"><?php print_r($podienik['d_datpok']); ?></span>
                                </td>
                                <td class="px-4 py-2">
                                    <span class="text-sm font-normal px-2 py-0.5 rounded"><span
                                            class="bg-gray-100 text-gray-800 text-xs font-bold me-2 px-2.5 py-0.5 rounded-full dark:bg-gray-700 dark:text-gray-300"><?php print_r($podienik['cpnazskratka']); ?></span></span>
                                </td>
                                <td class="px-4 py-2">
                                    <span class="text-sm font-normal px-2 py-0.5 rounded"><?php echo $druhobchodu; ?></span>
                                </td>
                                <td class="px-4 py-2">
                                    <a href="/pouzivatelia/detail/<?php echo $user["userid"]; ?>"
                                        class="flex items-center px-2 justify-between p-0.5 rounded-lg group dark:hover:bg-gray-700 hover:bg-gray-200 hover:underline cursor-pointer transition-all">
                                        <div
                                            class="relative w-5 h-5 overflow-hidden bg-gray-200 flex items-center justify-center rounded-full dark:bg-gray-600">
                                            <svg class="w-5 h-5 text-gray-400 -mb-2" fill="currentColor" viewBox="0 0 20 20"
                                                xmlns="http://www.w3.org/2000/svg">
                                                <path fill-rule="evenodd"
                                                    d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd">
                                                </path>
                                            </svg>
                                        </div>
                                        <small><?php echo $user["username"] ?></small>
                                    </a>
                                </td>
                                <td class="px-4 py-2">
                                    <?php if ($assignedUser !== "") { ?>
                                        <div
                                            class="flex items-center pl-3 justify-between p-0.5 rounded-lg group dark:hover:bg-gray-700 hover:bg-gray-200 hover:underline cursor-pointer transition-all">
                                            <a href="/pouzivatelia/detail/<?php echo $assignedUser["userid"]; ?>"
                                                class="flex gap-2">
                                                <div
                                                    class="relative w-5 h-5 overflow-hidden flex items-center justify-center rounded-full dark:bg-gray-600">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20"
                                                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5"
                                                        stroke-linecap="round" stroke-linejoin="round"
                                                        class="lucide text-blue-300 lucide-circle-user">
                                                        <circle cx="12" cy="12" r="10" />
                                                        <circle cx="12" cy="10" r="3" />
                                                        <path d="M7 20.662V19a2 2 0 0 1 2-2h6a2 2 0 0 1 2 2v1.662" />
                                                    </svg>
                                                </div>
                                                <p class="text-xs"><?php echo $assignedUser["username"]; ?></p>
                                            </a>
                                            <form hx-post="/api/mentionUserToAction" hx-target="#notificationsWrapper" class="mb-0">
                                                <input type="hidden" name="initiatorid" id="initiatorid"
                                                    value="<?php echo $assignedUser["initiatorid"]; ?>" />
                                                <input type="hidden" name="mentionedid" id="mentionedid"
                                                    value="<?php echo $assignedUser["mentionedid"]; ?>" />
                                                <input type="hidden" name="mentioncolumn" id="mentioncolumn" value="objektid" />
                                                <input type="hidden" name="mentiondata" id="mentiondata"
                                                    value="<?php echo $dealid; ?>" />
                                                <input type="hidden" name="action" id="action" value="delete" />
                                                <button type="submit"
                                                    class="p-1 rounded-md removeMention opacity-0 dark:hover:bg-red-700 dark:hover:text-white group-hover:opacity-100 transition-all cursor-pointer">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12"
                                                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                        stroke-linecap="round" stroke-linejoin="round"
                                                        class="lucide removeMentionIcon lucide-x">
                                                        <path d="M18 6 6 18" />
                                                        <path d="m6 6 12 12" />
                                                    </svg>
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="13" height="13"
                                                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                        stroke-linecap="round" stroke-linejoin="round"
                                                        class="lucide animate-spin dark:text-white text-gray-800 removeMentionSpinner lucide-loader-circle"
                                                        style="display: none;">
                                                        <path d="M21 12a9 9 0 1 1-6.219-8.56" />
                                                    </svg>
                                                </button>
                                            </form>
                                        </div>
                                    <?php } else { ?>
                                        <div data-dropdown-toggle="assignedUsers<?php echo $key; ?>"
                                            data-dropdown-placement="bottom"
                                            hx-get="/api/get/notificationUsers?dealid=<?php echo $dealid; ?>"
                                            hx-target="#assignedUsers<?php echo $key; ?>" id="<?php echo $key; ?>"
                                            class="p-1 rounded-md items-center hover:bg-gray-200 assignedUsersOpen dark:text-gray-100 dark:hover:text-gray-600 transition-all cursor-pointer text-gray-600 text-xs inline-flex gap-1 px-2">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                                stroke-linejoin="round" class="lucide lucide-user-round-plus">
                                                <path d="M2 21a8 8 0 0 1 13.292-6"></path>
                                                <circle cx="10" cy="8" r="5"></circle>
                                                <path d="M19 16v6"></path>
                                                <path d="M22 19h-6"></path>
                                            </svg>
                                            <small>Pridať<?php echo $assignedUserID; ?></small>
                                        </div>
                                        <div id="assignedUsers<?php echo $key; ?>"
                                            class="z-10 hidden bg-white absolute right-2 assignedUsersDropdown rounded-lg shadow-xl border dark:bg-gray-700"
                                            style="top: 2rem"></div>
                                    <?php } ?>
                                </td>
                                <td class="px-4 py-2 font-medium whitespace-nowrap dark:text-white">
                                    <div class="flex items-center gap-1">
                                        <button data-tooltip-target="view-tooltip" id="<?php echo $dealid; ?>"
                                            class="p-2 cursor-pointer detailEnabler hover:bg-green-200 transition-all rounded-lg">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24"
                                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                                stroke-linejoin="round" class="lucide text-green-700 lucide-circle-check">
                                                <circle cx="12" cy="12" r="10" />
                                                <path d="m9 12 2 2 4-4" />
                                            </svg>
                                        </button>
                                        <button data-tooltip-target="view-tooltip"
                                            hx-get="/investicne-zamery/<?php echo $url; ?>/edit/<?php echo $podienik["dealid"]; ?>"
                                            hx-target="#pageContentMain" hx-replace-url="true"
                                            class="p-2 cursor-pointer editIZButton hover:bg-gray-300 transition-all rounded-lg">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24"
                                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                                stroke-linejoin="round" class="lucide lucide-pencil">
                                                <path
                                                    d="M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z" />
                                                <path d="m15 5 4 4" />
                                            </svg>
                                        </button>
                                        <button data-modal-target="modal-<?php echo $podienik["dealid"]; ?>"
                                            data-modal-toggle="modal-<?php echo $podienik["dealid"]; ?>"
                                            class="p-2 cursor-pointer hover:bg-red-200 transition-all rounded-lg">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24"
                                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                                stroke-linejoin="round" class="lucide text-red-500 lucide-circle-x">
                                                <circle cx="12" cy="12" r="10" />
                                                <path d="m15 9-6 6" />
                                                <path d="m9 9 6 6" />
                                            </svg>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php
                            if ($podienik["eqid_trans"] === "Dlhopis" || $podienik["eqid_trans"] === "Akcia" || $podienik["eqid_trans"] === "Fond") {
                                $query = "
                                        select
                                            kcp.*,
                                            (kcp.kusov - kcp.kusovreal) as zostatok,
                                            e.cpnazskratka,
                                            e.cpnaz as cpnaz,
                                            to_char(kcp.expfrom,'DD.MM.YYYY') as datzac,
                                            to_char(kcp.exptill,'DD.MM.YYYY') as datend,
                                            d.cislo
                                        from
                                            konfirmaciacp kcp,
                                            dbequity e,
                                            today,
                                            dennikpm d
                                        where
                                            kcp.isin = e.isin and
                                            kcp.dealid = d.dealid and
                                            kcp.subjektid=$fondid and
                                            kcp.exptill >= today.datum and
                                            kcp.logactivityid in (4,6,7,8,9) and
                                            kcp.eqid = '$eqid' and
                                            today.fondid = $fondid and
                                            kcp.dealid = $dealid
                                    ";
                            } else if ($podienik["eqid_trans"] === "KTV") {
                                $query = "select ktv.dealid,
                                            ktv.sum_td,
                                            ktv.mena,
                                            ktv.ir_td,
                                            ktv.logactivityid,
                                            ktv.dk_td as datkonf,
                                            ktv.z_td  as datzac,
                                            ktv.k_td  as datend,
                                            d.cislo,
                                            ktv.partnerid,
                                            ktv.ir_td,
                                            ktv.cond1 as urocenie,
                                            ktv.pd_td as dobaviazanosti,
                                            ktv.dan as dan
                                        from konfirmaciaktv ktv,
                                            rezervacia re,
                                            dennikpm d
                                        where re.eqid like 'BU'
                                        and ktv.dealid = re.dealid
                                        and ktv.dealid = d.dealid
                                        and re.subjektid = $fondid
                                        and ktv.logactivityid in (4, 6, 7, 8)
                                        and ktv.dealid = $dealid";
                            } else {
                                $query = "
                                        select
                                            to_char(dat_konfirmacia,'DD.MM.YYYY') as d_konfirmacia,
                                            to_char(dat_realizacia,'DD.MM.YYYY') as d_realizacia,
                                            kk.*,
                                            d.cislo
                                        from
                                            konverzia kk,
                                            dennikpm d
                                        where
                                            kk.subjektid = $fondid and
                                            kk.dealid=d.dealid and
                                            kk.logactivityid in (4,6,7,8,9)
                                            and kk.dealid = $dealid
                                        order by kk.dealid
                                    ";
                            }
                            $detailOfDetail = Connection::getDataFromDatabase($query, defaultDB)[1][0];
                            ?>
                            <tr>
                                <td colspan="9">
                                    <?php if ($podienik["eqid_trans"] === "KTV") { ?>
                                        <form class="confirmKTVForm mb-0" id="c<?php echo $dealid; ?>">
                                            <input type="hidden" name="dealid" id="dealid" value="<?php echo $dealid; ?>" />
                                            <table class="w-full">
                                                <tbody>
                                                    <tr class="border-b dark:text-gray-100 dark:border-gray-300 dark:bg-gray-900 dark:bg-gray-600 bg-gray-50"
                                                        style="display: none" id="editConfirm<?php echo $podienik["dealid"]; ?>">
                                                        <td class="px-4 py-1">
                                                            <div class="flex flex-col gap-2">
                                                                <small>Číslo
                                                                    konfirmácie</small>
                                                                <span
                                                                    class=""><?php echo str_pad($detailOfDetail["cislo"], 11, "0", STR_PAD_LEFT); ?></span>
                                                            </div>
                                                        </td>
                                                        <td class="px-4 py-1">
                                                            <div class="flex flex-col gap-2">
                                                                <input type="hidden" name="istina"
                                                                    value="<?php echo $detailOfDetail["sum_td"]; ?>" />
                                                                <small>Istina</small>
                                                                <span
                                                                    class=""><?php echo number_format(round($detailOfDetail["sum_td"], 2), 2, ".", " ") ?></span>
                                                            </div>
                                                        </td>
                                                        <td class="px-4 py-1">
                                                            <div class="flex flex-col gap-2">
                                                                <input type="hidden" id="urocenie" name="urocenie"
                                                                    value="<?php echo $detailOfDetail["urocenie"]; ?>" />
                                                                <input type="hidden" id="dobaviazanosti" name="dobaviazanosti"
                                                                    value="<?php echo $detailOfDetail["dobaviazanosti"]; ?>" />
                                                                <input type="hidden" id="dan" name="dan"
                                                                    value="<?php echo $detailOfDetail["dan"]; ?>" />
                                                                <small>Sadzba</small>
                                                                <input type="text" id="ir_td" required name="ir_td"
                                                                    value="<?php echo $detailOfDetail["ir_td"]; ?>"
                                                                    class="block w-full p-1  border border-gray-300 rounded-lg bg-gray-50 text-xs focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                                                            </div>
                                                        </td>
                                                        <input type="hidden" value="<?php echo $detailOfDetail["datkonf"]; ?>"
                                                            name="datekonf">
                                                        <td class="px-4 py-1">
                                                            <div class="flex flex-col gap-2">
                                                                <small>Platnosť pokynu</small>
                                                                <span
                                                                    class=" text-xs"><?php echo $detailOfDetail["datkonf"] . " - " . $detailOfDetail["datzac"] ?></span>
                                                            </div>
                                                        </td>
                                                        <td class="px-4 py-1">
                                                            <div class="flex flex-col gap-2">
                                                                <small>Verifikácia</small>
                                                                <span class="text-xs"><?php
                                                                switch ($detailOfDetail["logactivityid"]) {
                                                                    case 6:
                                                                        echo "Pozitívna";
                                                                        break;
                                                                    case 7:
                                                                        echo "Negatívna";
                                                                        break;
                                                                    case 8:
                                                                        echo "Automatická";
                                                                        break;
                                                                    default:
                                                                        if ($fondid == 1) {
                                                                            echo "Žiadna";
                                                                        }
                                                                        break;
                                                                }
                                                                ?></span>
                                                            </div>
                                                        </td>
                                                        <td class="px-4 py-1">
                                                            <div class="flex flex-col gap-2">
                                                                <small>Dátum/čas obchodu</small>
                                                                <input type="date" name="datumobchodu" id="small-input"
                                                                    value="<?php echo $currentDate; ?>"
                                                                    class="block w-full p-1 border border-gray-300 rounded-lg bg-gray-50 text-xs focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                                                                <input type="time" name="casobchodu" id="small-input" value="<?php echo date("H:i:s");
                                                                ; ?>"
                                                                    class="block w-full p-1 border border-gray-300 rounded-lg bg-gray-50 text-xs focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                                                            </div>
                                                        </td>
                                                        <td class="px-4 py-1">
                                                            <div class="flex flex-col gap-2">
                                                                <small>Typ pokynu</small>
                                                                <select id="typ_pokynu" name="typ_pokynu" required
                                                                    class="block w-full p-1 px-2 mb-6 text-sm border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                                                                    <option selected value="">-</option>
                                                                    <option value="1">Limitný</option>
                                                                    <option value="2">Stop/Loss</option>
                                                                    <option value="3">Trhový</option>
                                                                </select>
                                                            </div>
                                                        </td>
                                                        <td class="px-4 py-1">
                                                            <div class="flex flex-col gap-2">
                                                                <button data-tooltip-target="view-tooltip"
                                                                    class="p-1 px-2 cursor-pointer flex justify-between items-center gap-1 text-xs hover:scale-105 hover:bg-gray-200 shadow-md bg-green-300 transition-all rounded-lg">
                                                                    <spa class="text-gray-800" n>Potvrdiť</spa>
                                                                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14"
                                                                        viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                                        stroke-width="2" stroke-linecap="round"
                                                                        stroke-linejoin="round"
                                                                        class="lucide chevronConfirm lucide-send-horizontal">
                                                                        <path
                                                                            d="M3.714 3.048a.498.498 0 0 0-.683.627l2.843 7.627a2 2 0 0 1 0 1.396l-2.842 7.627a.498.498 0 0 0 .682.627l18-8.5a.5.5 0 0 0 0-.904z" />
                                                                        <path d="M6 12h16" />
                                                                    </svg>
                                                                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14"
                                                                        viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                                        stroke-width="2" style="display: none"
                                                                        stroke-linecap="round" stroke-linejoin="round"
                                                                        class="lucide confirmSpinner animate-spin lucide-loader-circle">
                                                                        <path d="M21 12a9 9 0 1 1-6.219-8.56" />
                                                                    </svg>
                                                                </button>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </form>
                                    <?php } else if ($podienik["eqid_trans"] === "Akcia" || $podienik["eqid_trans"] === "Dlhopis" || $podienik["eqid_trans"] === "Fond") { ?>
                                            <form class="confirmCPForm mb-0" id="c<?php echo $dealid; ?>">
                                                <input type="hidden" name="dealid" id="dealid" value="<?php echo $dealid; ?>" />
                                                <input type="hidden" name="eqid" id="eqid" value="<?php echo $eqid; ?>" />
                                                <table class="w-full">
                                                    <tbody>
                                                        <tr class="border-b dark:border-gray-300 dark:bg-gray-700 bg-gray-50 dark:text-gray-100"
                                                            style="display: none" id="editConfirm<?php echo $podienik["dealid"]; ?>">
                                                            <td class="px-4 py-1">
                                                                <div class="flex flex-col gap-2">
                                                                    <small>Pokyn</small>
                                                                    <input type="hidden" name="pokyn" id="pokyn"
                                                                        value="<?php echo $detailOfDetail["cislo"]; ?>" />
                                                                    <span><?php echo str_pad($detailOfDetail["cislo"], 11, "0", STR_PAD_LEFT); ?></span>
                                                                </div>
                                                            </td>
                                                            <td class="px-4 py-1">
                                                                <div class="flex flex-col gap-2">
                                                                    <small>Cenný papier</small>
                                                                    <input type="hidden" name="partnerid" id="partnerid"
                                                                        value="<?php echo $detailOfDetail["partnerid"]; ?>" />
                                                                    <input type="hidden" name="cpnaz" id="cpnaz"
                                                                        value="<?php echo $detailOfDetail["cpnaz"]; ?>" />
                                                                    <span><?php echo $detailOfDetail["cpnaz"] ?></span>
                                                                </div>
                                                            </td>
                                                            <td class="px-4 py-1">
                                                                <div class="flex flex-col gap-2">
                                                                    <input type="hidden" name="limitprice"
                                                                        value="<?php echo $detailOfDetail["limitprice"]; ?>" />
                                                                    <small>Počet kusov</small>
                                                                    <input type="hidden" name="zostatok" id="zostatok"
                                                                        value="<?php echo $detailOfDetail["zostatok"]; ?>" />
                                                                    <span><?php echo $detailOfDetail["zostatok"] ?></span>
                                                                </div>
                                                            </td>
                                                            <td class="px-4 py-1">
                                                                <div class="flex flex-col gap-2">
                                                                    <small>Limitný kurz</small>
                                                                    <input type="text" id="limitkurz" required name="limitkurz"
                                                                        value="<?php echo number_format($detailOfDetail["limitkurz"], 5); ?>"
                                                                        class="block w-full p-1 border border-gray-300 rounded-lg bg-gray-50 text-xs focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                                                                </div>
                                                            </td>
                                                            <input type="hidden" value="<?php echo $detailOfDetail["datkonf"]; ?>"
                                                                name="datekonf">
                                                            <td class="px-4 py-1">
                                                                <div class="flex flex-col gap-2">
                                                                    <small>Platnosť pokynu</small>
                                                                    <span
                                                                        class="text-xs"><?php echo $detailOfDetail["datzac"] . " - " . $detailOfDetail["datend"] ?></span>
                                                                </div>
                                                            </td>
                                                            <td class="px-4 py-1">
                                                                <div class="flex flex-col gap-2">
                                                                    <small>Verifikácia</small>
                                                                    <span class="text-xs"><?php
                                                                    switch ($detailOfDetail["logactivityid"]) {
                                                                        case 6:
                                                                            echo "Pozitívna";
                                                                            break;
                                                                        case 7:
                                                                            echo "Negatívna";
                                                                            break;
                                                                        case 8:
                                                                            echo "Automatická";
                                                                            break;
                                                                        default:
                                                                            if ($fondid == 1) {
                                                                                echo "Žiadna";
                                                                            }
                                                                            break;
                                                                    }
                                                                    ?></span>
                                                                </div>
                                                            </td>
                                                            <td class="px-4 py-1">
                                                                <div class="flex flex-col gap-2">
                                                                    <small>Dátum/čas obchodu</small>
                                                                    <input type="date" name="datumobchodu" id="small-input"
                                                                        value="<?php echo $currentDate; ?>"
                                                                        class="block w-full p-1 border border-gray-300 rounded-lg bg-gray-50 text-xs focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                                                                    <input type="time" name="casobchodu" id="small-input" value="<?php echo date("H:i:s");
                                                                    ; ?>"
                                                                        class="block w-full p-1 border border-gray-300 rounded-lg bg-gray-50 text-xs focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                                                                </div>
                                                            </td>
                                                            <td class="px-4 py-1">
                                                                <div class="flex flex-col gap-2">
                                                                    <small>Typ pokynu</small>
                                                                    <select id="typ_pokynu" required name="typ_pokynu"
                                                                        class="block w-full p-2 mb-6 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                                                                        <option selected value="">-</option>
                                                                        <option value="1">Limitný</option>
                                                                        <option value="2">Stop/Loss</option>
                                                                        <option value="3">Trhový</option>
                                                                    </select>
                                                                </div>
                                                            </td>
                                                            <td class="px-4 py-1">
                                                                <div class="flex flex-col gap-2">
                                                                    <button data-tooltip-target="view-tooltip"
                                                                        class="p-1 px-2 cursor-pointer flex justify-between dark:text-gray-700 items-center gap-1 text-xs hover:scale-105 hover:bg-gray-200 shadow-md bg-green-300 transition-all rounded-lg">
                                                                        <span>Potvrdiť</span>
                                                                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14"
                                                                            viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                                            stroke-width="2" stroke-linecap="round"
                                                                            stroke-linejoin="round"
                                                                            class="lucide chevronConfirm lucide-send-horizontal">
                                                                            <path
                                                                                d="M3.714 3.048a.498.498 0 0 0-.683.627l2.843 7.627a2 2 0 0 1 0 1.396l-2.842 7.627a.498.498 0 0 0 .682.627l18-8.5a.5.5 0 0 0 0-.904z" />
                                                                            <path d="M6 12h16" />
                                                                        </svg>
                                                                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14"
                                                                            viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                                            stroke-width="2" style="display: none"
                                                                            stroke-linecap="round" stroke-linejoin="round"
                                                                            class="lucide confirmSpinner animate-spin lucide-loader-circle">
                                                                            <path d="M21 12a9 9 0 1 1-6.219-8.56" />
                                                                        </svg>
                                                                    </button>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </form>
                                    <?php } else if ($podienik["eqid_trans"] === "Konverzia") { ?>
                                                <form class="confirmKonverzia mb-0" id="c<?php echo $dealid; ?>">
                                                    <input type="hidden" name="dealid" id="dealid" value="<?php echo $dealid; ?>" />
                                                    <input type="hidden" name="eqid" id="eqid" value="<?php echo $eqid; ?>" />
                                                    <input type="hidden" name="partnerid" id="partnerid"
                                                        value="<?php echo $partnerid; ?>" />
                                                    <table class="w-full">
                                                        <tbody>
                                                            <tr class="border-b dark:border-gray-300 dark:bg-gray-700 dark:text-gray-100 bg-gray-50"
                                                                style="display: none" id="editConfirm<?php echo $podienik["dealid"]; ?>">
                                                                <td class="px-4 py-1">
                                                                    <div class="flex flex-col gap-2">
                                                                        <small>Pokyn</small>
                                                                        <input type="hidden" name="pokyn" id="pokyn"
                                                                            value="<?php echo $detailOfDetail["cislo"]; ?>" />
                                                                        <span
                                                                            class="dark:text-gray-50 text-gray-900"><?php echo str_pad($detailOfDetail["cislo"], 11, "0", STR_PAD_LEFT); ?></span>
                                                                    </div>
                                                                </td>
                                                                <td class="px-4 py-1">
                                                                    <div class="flex flex-col gap-2">
                                                                        <small>Dátum konfirmácie</small>
                                                                        <input type="hidden" name="d_konfirmacia" id="d_konfirmacia"
                                                                            value="<?php echo $detailOfDetail["d_konfirmacia"]; ?>" />
                                                                        <span
                                                                            class="dark:text-gray-50 text-gray-900"><?php echo $detailOfDetail["d_konfirmacia"] ?></span>
                                                                    </div>
                                                                </td>
                                                                <td class="px-4 py-1">
                                                                    <div class="flex flex-col gap-2">
                                                                        <small>Dátum realizácie</small>
                                                                        <input type="hidden" name="d_realizacia" id="d_realizacia"
                                                                            value="<?php echo $detailOfDetail["d_realizacia"]; ?>" />
                                                                        <span
                                                                            class="dark:text-gray-50 text-gray-900"><?php echo $detailOfDetail["d_realizacia"] ?></span>
                                                                    </div>
                                                                </td>
                                                                <td class="px-4 py-1">
                                                                    <div class="flex flex-col gap-2">
                                                                        <small>Sumakredit</small>
                                                                        <input type="hidden" name="sumakredit" id="sumakredit"
                                                                            value="<?php echo $detailOfDetail["sumakredit"]; ?>" />
                                                                        <span
                                                                            class="dark:text-gray-50 text-gray-900"><?php echo $detailOfDetail["sumakredit"] . " " . $detailOfDetail["menakredit"] ?></span>
                                                                    </div>
                                                                </td>
                                                                <td class="px-4 py-1">
                                                                    <div class="flex flex-col gap-2">
                                                                        <small>Sumadebet</small>
                                                                        <input type="hidden" name="sumadebet" id="sumadebet"
                                                                            value="<?php echo $detailOfDetail["sumadebet"]; ?>" />
                                                                        <span
                                                                            class="dark:text-gray-50 text-gray-900"><?php echo $detailOfDetail["sumadebet"] . " " . $detailOfDetail["menadebet"] ?></span>
                                                                    </div>
                                                                </td>
                                                                <td class="px-4 py-1">
                                                                    <div class="flex flex-col gap-2">
                                                                        <small>Kurz</small>
                                                                        <input type="text" id="kurz" required name="kurz"
                                                                            value="<?php echo number_format($detailOfDetail["kurz"], 5); ?>"
                                                                            class="block w-full p-1 border border-gray-300 rounded-lg bg-gray-50 text-xs focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                                                                    </div>
                                                                </td>
                                                                <td class="px-4 py-1">
                                                                    <div class="flex flex-col gap-2">
                                                                        <small>Dátum/čas obchodu</small>
                                                                        <input type="date" name="datumobchodu" id="small-input"
                                                                            value="<?php echo $currentDate; ?>"
                                                                            class="block w-full p-1 border border-gray-300 rounded-lg bg-gray-50 text-xs focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                                                                        <input type="time" name="casobchodu" id="small-input" value="<?php echo date("H:i:s");
                                                                        ; ?>"
                                                                            class="block w-full p-1 border border-gray-300 rounded-lg bg-gray-50 text-xs focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                                                                    </div>
                                                                </td>
                                                                <td class="px-4 py-1">
                                                                    <div class="flex flex-col gap-2">
                                                                        <small>Typ konverzie</small>
                                                                        <span class="dark:text-gray-50 text-gray-900 text-xs"><?php
                                                                        switch ($detailOfDetail["typ_konverzie"]) {
                                                                            case 0:
                                                                                echo "Spot";
                                                                                break;
                                                                            case 1:
                                                                                echo "NDF";
                                                                                break;
                                                                        }
                                                                        ?></span>
                                                                    </div>
                                                                </td>
                                                                <td class="px-4 py-1">
                                                                    <div class="flex flex-col gap-2">
                                                                        <small>Typ pokynu</small>
                                                                        <select id="typ_pokynu" name="typ_pokynu" required
                                                                            class="block w-full p-1 px-2 mb-6 text-sm border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                                                                            <option selected value="">-</option>
                                                                            <option value="1">Limitný</option>
                                                                            <option value="2">Stop/Loss</option>
                                                                            <option value="3">Trhový</option>
                                                                        </select>
                                                                    </div>
                                                                </td>
                                                                <td class="px-4 py-1">
                                                                    <div class="flex flex-col gap-2">
                                                                        <button data-tooltip-target="view-tooltip"
                                                                            class="p-1 px-2 cursor-pointer flex justify-between items-center dark:text-gray-700 gap-1 text-xs hover:scale-105 hover:bg-gray-200 shadow-md bg-green-300 transition-all rounded-lg">
                                                                            <span>Potvrdiť</span>
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14"
                                                                                viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                                                stroke-width="2" stroke-linecap="round"
                                                                                stroke-linejoin="round"
                                                                                class="lucide chevronConfirm lucide-send-horizontal">
                                                                                <path
                                                                                    d="M3.714 3.048a.498.498 0 0 0-.683.627l2.843 7.627a2 2 0 0 1 0 1.396l-2.842 7.627a.498.498 0 0 0 .682.627l18-8.5a.5.5 0 0 0 0-.904z" />
                                                                                <path d="M6 12h16" />
                                                                            </svg>
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14"
                                                                                viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                                                stroke-width="2" style="display: none"
                                                                                stroke-linecap="round" stroke-linejoin="round"
                                                                                class="lucide confirmSpinner animate-spin lucide-loader-circle">
                                                                                <path d="M21 12a9 9 0 1 1-6.219-8.56" />
                                                                            </svg>
                                                                        </button>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </form>
                                    <?php } ?>
                                </td>
                            </tr>
                            <?php
                        }
            }
            ?>
                </tbody>
            </table>
        </div>
    </div>
    </div>
</section>
<?php
foreach ($investicneDataRes[1] as $item) { ?>
    <div id="modal-<?php echo $item["dealid"]; ?>" tabindex="-1"
        class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
        <div class="relative p-4 w-full max-w-md max-h-full">
            <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
                <button type="button"
                    class="absolute top-3 end-2.5 text-gray-400 bg-transparent hover:bg-gray-200 hover:dark:text-gray-50 text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
                    data-modal-hide="modal-<?php echo $item["dealid"]; ?>">
                    <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                        viewBox="0 0 14 14">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                    </svg>
                    <span class="sr-only">Close modal</span>
                </button>
                <div class="p-4 md:p-5 text-center">
                    <section id="resultiko"></section>
                    <form class="deleteInvestmentIntention" hx-post="/api/investicne-zamery/terminovany-vklad/delete/delete"
                        hx-target="#resultiko">
                        <input type="hidden" name="type" id="type" value="<?php echo $item['eqid_trans']; ?>" />
                        <input type="hidden" name="dealid" id="dealid" value="<?php echo $item["dealid"]; ?>" />
                        <svg class="mx-auto mb-4 text-gray-400 w-12 h-12 dark:text-gray-200" aria-hidden="true"
                            xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M10 11V6m0 8h.01M19 10a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                        </svg>
                        <h3 class="mb-5 text-lg font-normal text-gray-500 dark:text-gray-400">Chcete naozaj zrušiť tento
                            <strong>investičný zámer?</strong>
                        </h3>
                        <button data-modal-hide="popup-modal" type="submit"
                            class="text-white cursor-pointer bg-red-600 hover:bg-red-800 focus:ring-4 focus:outline-none focus:ring-red-300 dark:focus:ring-red-800 font-medium rounded-lg text-sm inline-flex gap-4 items-center px-5 py-2.5 text-center">
                            <span id="deleteText">Áno, odstrániť</span>
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                class="lucide animate-spin deleteSpin hidden lucide-loader-circle">
                                <path d="M21 12a9 9 0 1 1-6.219-8.56" />
                            </svg>
                        </button>
                        <button data-modal-hide="modal-<?php echo $item["dealid"]; ?>" type="button"
                            class="py-2.5 px-5 ms-3 text-sm font-medium focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700">Nie,
                            zavrieť</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
<?php } ?>
<script src="/src/assets/js/global/investicny-zamer/table.js"></script>
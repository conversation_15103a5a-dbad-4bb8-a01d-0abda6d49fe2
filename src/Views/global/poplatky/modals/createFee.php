<?php require_once('/home/<USER>/www/src/lib/connection.php');
require_once("/home/<USER>/www/conf/settings.php");
if (isset($_SESSION["client"])) {
    $fondid = $_SESSION["client"]["fondid"];
    $fondidQuery = " WHERE fondid = $fondid";
} else {
    $fondid = 0;
    $fondidQuery = "";
}
$zmluvy = Connection::getDataFromDatabase("select distinct * from portfolio $fondidQuery", defaultDB)[1];
$menyQuery = "SELECT DISTINCT
       pr.mena
FROM portfolio po
         JOIN poplatok_register pr ON po.fondid = pr.fondid
         JOIN fonds f ON f.fondid = po.fondid
         LEFT JOIN konfirmaciacp kcp ON pr.dealid = kcp.dealid
WHERE pr.stav IN (0,1,5)
  AND pr.datum IS NOT NULL AND pr.suma IS NOT NULL";
$meny = Connection::getDataFromDatabase(
    $menyQuery,
    defaultDB
)[1]; ?>
<div id="create-fee-modal" tabindex="-1" aria-hidden="true" class="hidden overflow-y-auto bg-clip-padding backdrop-filter backdrop-blur-sm bg-opacity-10 dark:bg-gray-400/30 bg-white/60 overflow-x-hidden 
fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 max-h-full flex">
    <div id="toastCreate" class="absolute w-full top-5"></div>
    <div class="relative p-4 w-full max-w-2xl max-h-full">
        <div class="relative bg-white rounded-lg shadow-sm dark:bg-gray-700">
            <div
                class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600 border-gray-200">
                <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                    Tvorba nového poplatku
                </h3>
                <button type="button"
                    class="text-gray-400 bg-transparent hover:bg-gray-50 dark:hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
                    data-modal-hide="create-fee-modal">
                    <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                        viewBox="0 0 14 14">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                    </svg>
                    <span class="sr-only">Close modal</span>
                </button>
            </div>
            <form id="createFeeForm" onsubmit="event.preventDefault(); createFee(event)">
                <div class="p-4 md:p-5 grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="feeCreateOpenClient"
                            class="block text-sm font-medium dark:text-gray-100 text-gray-700 mb-1">Portfólio</label>
                        <button id="feeCreateOpenClient" type="button"
                            class="bg-gray-50 border border-gray-300 hover:bg-gray-100 dark:hover:bg-gray-500 hover:scale-95 transition-all text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block text-left
                            w-full p-1.5 dark:bg-gray-600 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                            Vybrať portfolio
                        </button>
                        <div id="dropdownklientCreate"
                            class="absolute z-10 h-72 overflow-y-auto bg-white divide-y divide-gray-100 rounded-lg shadow-sm w-72 dark:bg-gray-700"
                            style="display: none;">
                            <section id="dropdownKlientTargetCreate">
                                <div class="p-3">
                                    <label for="input-group-search" class="sr-only">Search</label>
                                    <div class="relative">
                                        <div
                                            class="absolute inset-y-0 rtl:inset-r-0 start-0 flex items-center ps-3 pointer-events-none">
                                            <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" aria-hidden="true"
                                                xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                                                <path stroke="currentColor" stroke-linecap="round"
                                                    stroke-linejoin="round" stroke-width="2"
                                                    d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z" />
                                            </svg>
                                        </div>
                                        <input type="text" id="feeCreationFindClient"
                                            class="block w-full p-2 ps-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                            placeholder="Search user">
                                    </div>
                                </div>
                                <div id="avaiableClientSection">
                                    <ul class="p-3 text-sm text-gray-700 dark:text-gray-200"
                                        aria-labelledby="dropdownRadioButton">
                                        <?php foreach ($zmluvy as $key => $zmluva) { ?>
                                            <li class="mb-0.5">
                                                <label class="w-full text-sm font-medium text-gray-900 rounded-sm dark:text-gray-300 flex cursor-pointer transition-all items-center gap-2 p-2 rounded-sm 
                hover:bg-gray-100 dark:hover:bg-gray-600">
                                                    <input type="radio" onchange="addAccToCreation(this)"
                                                        value="<?php echo $zmluva["fondid"]; ?>" name="fondid" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 
                    ark:focus:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600" />
                                                    <?php echo $zmluva["cislozmluvy"]; ?>
                                                </label>
                                            </li>
                                        <?php } ?>
                                    </ul>
                                </div>
                                <script>
                                    (function () {
                                        let typingTimer;
                                        const doneTyping = 500;
                                        $("#feeCreationFindClient").on("keyup", (e) => {
                                            clearTimeout(typingTimer);
                                            typingTimer = setTimeout(() => {
                                                htmx.ajax('POST', 'src/Controllers/global/poplatky/zmluvyFilteredRadio.php', {
                                                    target: '#avaiableClientSection',
                                                    values: {
                                                        query: e.target.value,
                                                        data: JSON.stringify(<?php echo json_encode($data); ?>)
                                                    }
                                                });
                                            }, doneTyping);
                                        });

                                        $("#feeCreationFindClient").on("keydown", () => {
                                            clearTimeout(typingTimer);
                                        });
                                    })();
                                </script>
                            </section>
                        </div>
                    </div>
                    <div>
                        <label for="menaa"
                            class="block text-sm font-medium dark:text-gray-100 text-gray-700 mb-1">Mena</label>
                        <select id="menaa" name="mena"
                            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full 
                            p-1.5 dark:bg-gray-600 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                            <?php foreach ($meny as $key => $value) { ?>
                                <option value="<?php echo $value["mena"]; ?>"><?php echo $value["mena"]; ?></option>
                            <?php } ?>
                        </select>
                    </div>
                    <div>
                        <label for="vatRate"
                            class="block text-sm font-medium dark:text-gray-100 text-gray-700 mb-1">Sadzba
                            DPH</label>
                        <div class="flex w-full items-center">
                            <select id="vatRate" name="vatRate" required
                                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full 
                            p-1.5 dark:bg-gray-600 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                                <option value="0" selected>0</option>
                                <option value="20">20</option>
                                <option value="23">23</option>
                            </select>
                            <span class="ml-2 p-1 px-2 bg-gray-500 dark:text-gray-100 rounded-md font-bold">%</span>
                        </div>
                    </div>
                    <div>
                        <label for="amountWithoutVat"
                            class="block text-sm font-medium dark:text-gray-100 text-gray-700 mb-1">Amount
                            without
                            VAT</label>
                        <input type="number" id="amountWithoutVat" name="amountWithoutVat" min="0" step="0.01" required
                            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full 
                            p-1.5 dark:bg-gray-600 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                            placeholder="0.00">
                    </div>
                    <div>
                        <label for="typPoplatku" class="block text-sm font-medium text-gray-700 mb-1">Typ
                            poplatku</label>
                        <select id="typPoplatku" name="typpoplatku" required
                            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full 
                        p-1.5 dark:bg-gray-600 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                            <option value="MANAZ">Riadenie</option>
                            <option value="SPRAVA">Správa</option>
                            <option value="TRAN">Transakcia</option>
                            <option value="VYROV">Vyrovnanie</option>
                        </select>
                    </div>
                    <div>
                        <label for="reason" class="block text-sm font-medium text-gray-700 mb-1">Reason</label>
                        <input type="text" id="reason" name="reason" required minlength="3"
                            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full 
                            p-1.5 dark:bg-gray-600 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                            placeholder="Enter reason">
                    </div>
                </div>
                <div
                    class="flex items-center gap-2 justify-end p-4 md:p-5 border-t border-gray-200 rounded-b dark:border-gray-600 bg-gray-900 sticky bottom-1">
                    <button data-modal-hide="create-fee-modal" type="button"
                        class="py-2.5 px-5 ms-3 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700">Zrušiť</button>
                    <button type="submit"
                        class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5
                         py-2.5 text-center flex gap-2 items-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                        <span id="createFeeSubmit">Potvrdiť</span>
                        <svg id="createFeeSubmitSpinner" xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                            stroke-linecap="round" stroke-linejoin="round"
                            class="lucide hidden animate-spin lucide-loader-circle">
                            <path d="M21 12a9 9 0 1 1-6.219-8.56" />
                        </svg>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
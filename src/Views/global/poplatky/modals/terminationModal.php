<div id="termination-modal" tabindex="-1" class="hidden overflow-y-auto bg-clip-padding backdrop-filter backdrop-blur-sm bg-opacity-10 dark:bg-gray-400/30 bg-white/60 overflow-x-hidden 
fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 max-h-full flex">
    <div class="relative p-4 w-full max-w-md max-h-full">
        <div class="relative bg-white rounded-lg shadow-sm dark:bg-gray-700">
            <button type="button" class="absolute top-3 end-2.5 text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center 
                items-center dark:hover:bg-gray-600 dark:hover:text-white" data-modal-hide="termination-modal">
                <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                    viewBox="0 0 14 14">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                </svg>
                <span class="sr-only">Close modal</span>
            </button>
            <div id="terminationBody" class="p-4 md:p-5 text-center">
                <div class="flex items-center p-4  rounded-lg bg-gray-50 dark:text-gray-100 dark:bg-gray-800 h-full justify-center flex-col gap-4"
                    role="alert">
                    <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"
                        class="lucide animate-spin lucide-loader-circle">
                        <path d="M21 12a9 9 0 1 1-6.219-8.56" />
                    </svg>
                    <span class="sr-only">Info</span>
                    <div class="ms-3 text-sm font-medium text-gray-800 dark:text-gray-300">
                        Getting details...
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
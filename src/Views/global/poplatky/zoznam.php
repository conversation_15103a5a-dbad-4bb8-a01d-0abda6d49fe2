<?php
require_once('/home/<USER>/www/src/lib/connection.php');
require_once("/home/<USER>/www/conf/settings.php");
require_once("/home/<USER>/www/src/Components/exports/excel/excelController.php");

$page = isset($_GET['page']) && is_numeric($_GET['page']) ? $_GET['page'] : 1;
$limit = isset($_GET['limit']) && is_numeric($_GET['limit']) ? $_GET['limit'] : 30;

if (isset($_SESSION["client"])) {
    $fondid = $_SESSION["client"]["fondid"];
    $fondidQuery = " WHERE fondid = $fondid";
} else {
    $fondid = 0;
    $fondidQuery = "";
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST["filteredValuesInput"]) && $_POST["filteredValuesInput"] !== "") {
        $data = json_decode($_POST["filteredValuesInput"]);

        if (sizeof($data->types) > 0) {
            $typQuery = " AND a.typ IN (";
            foreach ($data->types as $type) {
                $typQuery .= "'$type',";
            }
            $typQuery = substr($typQuery, 0, -1);
            $typQuery .= ")";
        }

        if (sizeof($data->clients) > 0) {
            $clientQuery = " AND a.cislozmluvy IN (";
            foreach ($data->clients as $client) {
                $clientQuery .= "'$client',";
            }
            $clientQuery = substr($clientQuery, 0, -1);
            $clientQuery .= ")";
        }

        if (sizeof($data->isins) > 0) {
            $isinQuery = " AND a.isin IN (";
            foreach ($data->isins as $isin) {
                $isinQuery .= "'$isin',";
            }
            $isinQuery = substr($isinQuery, 0, -1);
            $isinQuery .= ")";
        }

        if (sizeof($data->meny) > 0) {
            $menaQuery = " AND a.mena IN (";
            foreach ($data->meny as $mena) {
                $menaQuery .= "'$mena',";
            }
            $menaQuery = substr($menaQuery, 0, -1);
            $menaQuery .= ")";
        }

        if ($data->hotovost === 1) {
            $hotovostQuery = " AND a.suma <= mt.cash AND a.suma >= 0 ";
        } elseif ($data->hotovost === 2) {
            $hotovostQuery = " AND a.suma > mt.cash and a.suma >= 0 ";
        } else {
            $hotovostQuery = "";
        }

        if (sizeof($data->dates) > 0) {
            $dateQuery = " AND a.datum IN (";
            foreach ($data->dates as $date) {
                $dateQuery .= "'$date',";
            }
            $dateQuery = substr($dateQuery, 0, -1);
            $dateQuery .= ")";
        } else {
            $dateQuery = "";
        }
    } else {
        $typQuery = "";
        $clientQuery = "";
        $isinQuery = "";
        $menaQuery = "";
        $hotovostQuery = "";
        $dateQuery = "";
    }
    $count = Connection::getDataFromDatabase("WITH cash as (select round(sum(((0.5 - md_d) / 0.5) * pocet), 2) as cash, mena, ucetaktiva, subjektid
              from majetoktoday
              where subjektid > 1
                and uctovnykod in (221110, 325111, 325121, 325122, 325130, 325140, 325150, 325160)
                and eqid = 'BU'
              group by mena, ucetaktiva, subjektid),
     main as (SELECT pr.id,
                     pr.suma      AS suma,
                     pr.mena,
                     po.cislozmluvy,
                     pr.datum,
                     CASE
                         WHEN pr.typ = 'TRAN' THEN 'Transakcia s CP'
                         WHEN pr.typ = 'VYROV' THEN 'Vyrovnanie transakcie'
                         WHEN pr.typ = 'SPRAVA' THEN 'Za správu'
                         WHEN pr.typ = 'MANAZ' THEN 'Za riadenie'
                         WHEN pr.typ = 'OSTATNE' THEN 'Ostatné'
                         ELSE 'Neznáma operácia'
                         END      AS typ,
                     pr.typ       AS typ_db,
                     pr.fondid,
                     CASE
                         WHEN f.uhrada_poplatkov = 1 THEN 'inkaso'
                         ELSE 'faktura'
                         END      AS uhrada_poplatkov,
                     pr.cub       AS cub,
                     pr.dansadzba AS dansadzba1,
                     kcp.isin
              FROM portfolio po
                       JOIN poplatok_register pr ON po.fondid = pr.fondid
                       JOIN fonds f ON f.fondid = po.fondid
                       LEFT JOIN konfirmaciacp kcp ON pr.dealid = kcp.dealid
              WHERE pr.stav IN (0, 1, 5)
                AND pr.datum IS NOT NULL AND pr.suma IS NOT NULL)
select COUNT(*) as count
FROM main a
         LEFT JOIN cash mt ON a.cub = mt.ucetaktiva and a.fondid = mt.subjektid and a.mena = mt.mena
WHERE 1=1 
$typQuery
$clientQuery
$isinQuery
$menaQuery
$hotovostQuery
$dateQuery", defaultDB)[1][0]["count"];

    $poplatkyQuery = "WITH cash as (select round(sum(((0.5 - md_d) / 0.5) * pocet), 2) as cash, mena, ucetaktiva, subjektid
              from majetoktoday
              where subjektid > 1
                and uctovnykod in (221110, 325111, 325121, 325122, 325130, 325140, 325150, 325160)
                and eqid = 'BU'
              group by mena, ucetaktiva, subjektid),
main as (SELECT pr.id,
                     pr.suma      AS suma,
                     pr.mena,
                     po.cislozmluvy,
                     pr.datum,
                     pr.stav,
                     CASE
                         WHEN pr.typ = 'TRAN' THEN 'Transakcia s CP'
                         WHEN pr.typ = 'VYROV' THEN 'Vyrovnanie transakcie'
                         WHEN pr.typ = 'SPRAVA' THEN 'Za správu'
                         WHEN pr.typ = 'MANAZ' THEN 'Za riadenie'
                         WHEN pr.typ = 'OSTATNE' THEN 'Ostatné'
                         ELSE 'Neznáma operácia'
                         END      AS typ,
                     pr.typ       AS typ_db,
                     pr.fondid,
                     CASE
                         WHEN f.uhrada_poplatkov = 1 THEN 'inkaso'
                         ELSE 'faktura'
                         END      AS uhrada_poplatkov,
                     pr.cub       AS cub,
                     pr.dansadzba AS dansadzba1,
                     pr.dan AS dan,
                     kcp.isin
              FROM portfolio po
                       JOIN poplatok_register pr ON po.fondid = pr.fondid
                       JOIN fonds f ON f.fondid = po.fondid
                       LEFT JOIN konfirmaciacp kcp ON pr.dealid = kcp.dealid
              WHERE pr.stav IN (0, 1, 5)
                AND pr.datum IS NOT NULL AND pr.suma IS NOT NULL)
select a.id,
       a.suma,
       a.mena,
       a.cislozmluvy,
       a.datum,
       a.typ as typ,
       a.typ_db,
       a.fondid,
       a.uhrada_poplatkov,
       a.cub,
       a.dansadzba1,
       a.isin,
       a.stav,
       a.dan,
       COALESCE(mt.cash, 0)                                                         as cash
FROM main a
         LEFT JOIN cash mt ON a.cub = mt.ucetaktiva and a.fondid = mt.subjektid and a.mena = mt.mena
WHERE 1=1 
$typQuery
$clientQuery
$isinQuery
$menaQuery
$hotovostQuery
$dateQuery
order by a.cislozmluvy, a.mena, a.typ";
    if ($page !== 1) {
        $calc_page = ($page - 1) * $limit;
        $poplatkyQuery .= " OFFSET $calc_page LIMIT $limit";
    } else {
        $poplatkyQuery .= " LIMIT $limit";
    }
    echo $poplatkyQuery;
    $poplatky = Connection::getDataFromDatabase($poplatkyQuery, defaultDB)[1];
} else {
    $count = Connection::getDataFromDatabase("WITH cash as (select round(sum(((0.5 - md_d) / 0.5) * pocet), 2) as cash, mena, ucetaktiva, subjektid
              from majetoktoday
              where subjektid > 1
                and uctovnykod in (221110, 325111, 325121, 325122, 325130, 325140, 325150, 325160)
                and eqid = 'BU'
              group by mena, ucetaktiva, subjektid),
     main as (SELECT pr.id,
                     pr.suma      AS suma,
                     pr.mena,
                     po.cislozmluvy,
                     pr.datum,
                     CASE
                         WHEN pr.typ = 'TRAN' THEN 'Transakcia s CP'
                         WHEN pr.typ = 'VYROV' THEN 'Vyrovnanie transakcie'
                         WHEN pr.typ = 'SPRAVA' THEN 'Za správu'
                         WHEN pr.typ = 'MANAZ' THEN 'Za riadenie'
                         WHEN pr.typ = 'OSTATNE' THEN 'Ostatné'
                         ELSE 'Neznáma operácia'
                         END      AS typ,
                     pr.typ       AS typ_db,
                     pr.fondid,
                     CASE
                         WHEN f.uhrada_poplatkov = 1 THEN 'inkaso'
                         ELSE 'faktura'
                         END      AS uhrada_poplatkov,
                     pr.cub       AS cub,
                     pr.dansadzba AS dansadzba1,
                     kcp.isin
              FROM portfolio po
                       JOIN poplatok_register pr ON po.fondid = pr.fondid
                       JOIN fonds f ON f.fondid = po.fondid
                       LEFT JOIN konfirmaciacp kcp ON pr.dealid = kcp.dealid
              WHERE pr.stav IN (0, 1, 5)
                AND pr.datum IS NOT NULL AND pr.suma IS NOT NULL)
select COUNT(*) as count
FROM main a
         LEFT JOIN cash mt ON a.cub = mt.ucetaktiva and a.fondid = mt.subjektid and a.mena = mt.mena
         $fondidQuery
         ", defaultDB)[1][0]["count"];

    $poplatkyQuery = "WITH cash as (select round(sum(((0.5 - md_d) / 0.5) * pocet), 2) as cash, mena, ucetaktiva, subjektid
              from majetoktoday
              where subjektid > 1
                and uctovnykod in (221110, 325111, 325121, 325122, 325130, 325140, 325150, 325160)
                and eqid = 'BU'
              group by mena, ucetaktiva, subjektid),
main as (SELECT pr.id,
                     pr.suma      AS suma,
                     pr.mena,
                     po.cislozmluvy,
                     pr.datum,
                     pr.stav,
                     CASE
                         WHEN pr.typ = 'TRAN' THEN 'Transakcia s CP'
                         WHEN pr.typ = 'VYROV' THEN 'Vyrovnanie transakcie'
                         WHEN pr.typ = 'SPRAVA' THEN 'Za správu'
                         WHEN pr.typ = 'MANAZ' THEN 'Za riadenie'
                         WHEN pr.typ = 'OSTATNE' THEN 'Ostatné'
                         ELSE 'Neznáma operácia'
                         END      AS typ,
                     pr.typ       AS typ_db,
                     pr.fondid,
                     CASE
                         WHEN f.uhrada_poplatkov = 1 THEN 'inkaso'
                         ELSE 'faktura'
                         END      AS uhrada_poplatkov,
                     pr.cub       AS cub,
                     pr.dansadzba AS dansadzba1,
                     pr.dan AS dan,
                     kcp.isin
              FROM portfolio po
                       JOIN poplatok_register pr ON po.fondid = pr.fondid
                       JOIN fonds f ON f.fondid = po.fondid
                       LEFT JOIN konfirmaciacp kcp ON pr.dealid = kcp.dealid
              WHERE pr.stav IN (0, 1, 5)
                AND pr.datum IS NOT NULL AND pr.suma IS NOT NULL)
select a.id,
       a.suma,
       a.mena,
       a.cislozmluvy,
       a.datum,
       a.typ as typ,
       a.typ_db,
       a.fondid,
       a.uhrada_poplatkov,
       a.cub,
       a.dansadzba1,
       a.dan,
       a.isin,
       a.stav,
       COALESCE(mt.cash, 0)                                                         as cash
FROM main a
         LEFT JOIN cash mt ON a.cub = mt.ucetaktiva and a.fondid = mt.subjektid and a.mena = mt.mena
$fondidQuery
order by a.cislozmluvy, a.mena, a.typ
";

    if ($page !== 1) {
        $calc_page = ($page - 1) * $limit;
        $poplatkyQuery .= " OFFSET $calc_page LIMIT $limit";
    } else {
        $poplatkyQuery .= " LIMIT $limit";
    }
    echo $poplatkyQuery;
    $poplatky = Connection::getDataFromDatabase($poplatkyQuery, defaultDB)[1];
}

$total_count = $count;
$columns = ["klient", "typ poplatku", "rekonfirmovaný", "dátum", "isin", "suma", "mena", "účet", "hotovosť", "fondid", "dan"];

?>
<section class="p-4 px-8">
    <header class="mb-4 lg:mb-6 not-format">
        <h1 class="mb-4 text-3xl font-extrabold leading-tight text-gray-900 lg:mb-6 lg:text-3xl dark:text-white">
            Zoznam
            poplatkov <strong id="totalCountStrong" class="dark:text-green-600">(<?php echo $count; ?>)</strong></h1>
    </header>
    <form id="wholeForm" class="mb-0 w-full flex justify-between">
        <input type="hidden" id="pageNumber" name="page" value="<?php echo $page ?>" />
        <div class="relative w-full shadow-md sm:rounded-lg">
            <div id="tableTopHeader" class="p-4 bg-white z-10 flex justify-between dark:bg-gray-900">
                <button type="submit" id="wholeBodySubmitter" class="hidden h-0 w-0"></button>
                <div class="relative flex items-center gap-4 mt-1">
                    <div>
                        <div id="dropdownDefaultCheckbox"
                            class="z-10 hidden w-48 bg-white divide-y divide-gray-100 rounded-lg shadow-sm dark:bg-gray-700 dark:divide-gray-600">
                            <ul class="p-3 space-y-3 text-sm text-gray-700 dark:text-gray-200"
                                aria-labelledby="dropdownCheckboxButton">
                                <?php foreach ($columns as $key => $column) { ?>
                                    <li>
                                        <div class="flex items-center">
                                            <input type="checkbox" value="<?php echo $column; ?>" name="column"
                                                checked="true"
                                                class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded-sm focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-700 dark:focus:ring-offset-gray-700 focus:ring-2 dark:bg-gray-600 dark:border-gray-500">
                                            <label
                                                class="ms-2 text-sm font-medium uppercase text-gray-900 dark:text-gray-300"><?php echo $column; ?></label>
                                        </div>
                                    </li>
                                <?php } ?>
                            </ul>
                        </div>
                    </div>
                    <div>
                        <label for="countries"
                            class="block mb-2 text-xs font-medium text-gray-900 dark:text-white">Počet výsledkov na
                            stránku</label>
                        <section id="limitSelectWrapper">
                            <select id="limit" name="limit"
                                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                                <?php
                                $counter = 30;
                                if ($counter < $count) {
                                    while ($counter < $count) {
                                        if ($counter > $count) { ?>
                                            <option value="<?php echo $counter; ?>" <?php echo $limit <= $counter && $limit >= $counter ? "selected" : "" ?>>
                                                Všetky
                                            </option>
                                        <?php } else { ?>
                                            <option value="<?php echo $counter; ?>" <?php echo $limit <= $counter && $limit >= $counter ? "selected" : "" ?>>
                                                <?php echo $counter; ?>
                                            </option>
                                        <?php }
                                        $counter += 30;
                                        if ($counter > $count) { ?>
                                            <option value="<?php echo $count; ?>" <?php echo $count <= $counter && $limit >= $count ? "selected" : "" ?>>Všetky
                                            </option>
                                        <?php }
                                    }
                                } else { ?>
                                    <option value="<?php echo $count; ?>" <?php echo $count <= $counter && $limit >= $count ? "selected" : "" ?>>Všetky
                                    </option>
                                <?php } ?>
                            </select>
                        </section>

                    </div>
                    <section class="flex flex-col justify-between h-full gap-1">
                        <small class="dark:text-gray-100 mb-1">Vybrané filtre:</small>
                        <section class="flex items-center gap-2 h-full">
                            <div id="filteredValues" class="dark:bg-gray-700 border rounded-lg px-2 py-1.5 h-full">
                                <?php
                                if (isset($data)) {
                                    foreach ($data->dates as $date) { ?>
                                        <span
                                            class="bg-green-100 text-green-800 text-xs border font-medium me-2 px-2.5 py-0.5 rounded-sm dark:bg-green-900 dark:text-green-300">
                                            <?php echo $date; ?>
                                        </span>
                                    <?php }
                                    foreach ($data->types as $type) { ?>
                                        <span
                                            class="bg-green-100 text-green-800 text-xs border font-medium me-2 px-2.5 py-0.5 rounded-sm dark:bg-green-900 dark:text-green-300">
                                            <?php echo $type; ?>
                                        </span>
                                    <?php }
                                    foreach ($data->clients as $client) { ?>
                                        <span
                                            class="bg-green-100 text-green-800 text-xs border font-medium me-2 px-2.5 py-0.5 rounded-sm dark:bg-green-900 dark:text-green-300">
                                            <?php echo $client; ?>
                                        </span>
                                    <?php }
                                    foreach ($data->isins as $isin) { ?>
                                        <span
                                            class="bg-green-100 text-green-800 text-xs border font-medium me-2 px-2.5 py-0.5 rounded-sm dark:bg-green-900 dark:text-green-300">
                                            <?php echo $isin; ?>
                                        </span>
                                    <?php }
                                    foreach ($data->meny as $mena) { ?>
                                        <span
                                            class="bg-green-100 text-green-800 text-xs border font-medium me-2 px-2.5 py-0.5 rounded-sm dark:bg-green-900 dark:text-green-300">
                                            <?php echo $mena; ?>
                                        </span>
                                    <?php }
                                    if ($data->hotovost === "1") { ?>
                                        <span
                                            class="bg-green-100 text-green-800 text-xs border font-medium me-2 px-2.5 py-0.5 rounded-sm dark:bg-green-900 dark:text-green-300">
                                            Hotovost: Ano
                                        </span>
                                    <?php } elseif ($data->hotovost === "2") { ?>
                                        <span
                                            class="bg-green-100 text-green-800 text-xs border font-medium me-2 px-2.5 py-0.5 rounded-sm dark:bg-green-900 dark:text-green-300">
                                            Hotovost: Nie
                                        </span>
                                    <?php }
                                }
                                ?>
                            </div>
                            <button type="button" id="resetFilter"
                                class="p-1 dark:bg-gray-800 hover:dark:bg-gray-600 <?php isset($data) && sizeof($data) > 0 ? "" : "hidden" ?> dark:text-gray-200 rounded-lg transition-all cursor-pointer">
                                <svg xmlns="http://www.w3.org/2000/svg" width="" height="18" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="lucide lucide-filter-x">
                                    <path d="M13.013 3H2l8 9.46V19l4 2v-8.54l.9-1.055" />
                                    <path d="m22 3-5 5" />
                                    <path d="m17 3 5 5" />
                                </svg>
                            </button>
                        </section>
                    </section>
                </div>
                <section class="flex items-center gap-4">
                    <section id="sumSuma"
                        class="dark:text-gray-100 hidden flex gap-2 items-center text-lg bg-white dark:bg-gray-900 p-1 px-2 rounded-lg border z-10">
                        <p>Suma spolu: </p>
                        <strong id="sumSumaValue"></strong>
                        <input id="sumSumaMena" type="hidden" name="sumSumaMena" />
                    </section>
                    <div>
                        <button id="dropdownActionButton" data-dropdown-toggle="dropdownAction" class="inline-flex items-center text-gray-500 bg-white border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 
                        focus:ring-gray-100  font-medium rounded-lg text-sm px-3 py-2.5 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 
                        dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700" type="button">
                            <span class="sr-only">hromadne akcie</span>
                            Hromadné akcie
                            <svg class="w-2.5 h-2.5 ms-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                                fill="none" viewBox="0 0 10 6">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                    stroke-width="2" d="m1 1 4 4 4-4" />
                            </svg>
                        </button>
                        <!-- Dropdown menu -->
                        <div id="dropdownAction"
                            class="z-10 hidden bg-white divide-y divide-gray-100 rounded-lg shadow-sm w-44 dark:bg-gray-700 dark:divide-gray-600">
                            <ul class="p-2 space-y-1 text-sm text-gray-700 dark:text-gray-200"
                                aria-labelledby="dropdownHelperButton">
                                <li>
                                    <button type="submit" id="changeSum" data-modal-target="changeAmountModal"
                                        data-modal-toggle="changeAmountModal"
                                        class="flex p-2 rounded-sm w-full flex items-center gap-1 transition-all hover:bg-gray-100 cursor-pointer dark:hover:bg-gray-800">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round"
                                            class="lucide lucide-circle-dollar-sign">
                                            <circle cx="12" cy="12" r="10" />
                                            <path d="M16 8h-6a2 2 0 1 0 0 4h4a2 2 0 1 1 0 4H8" />
                                            <path d="M12 18V6" />
                                        </svg>
                                        <span class="font-bold">Zmena sumy</span>
                                    </button>
                                </li>
                                <li>
                                    <button type="submit" id="changeMena" data-modal-target="changeAmountModal"
                                        data-modal-toggle="changeAmountModal"
                                        class="flex p-2 rounded-sm w-full flex items-center gap-1 transition-all hover:bg-gray-100 cursor-pointer dark:hover:bg-gray-800">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round"
                                            class="lucide lucide-arrow-left-right">
                                            <path d="M8 3 4 7l4 4" />
                                            <path d="M4 7h16" />
                                            <path d="m16 21 4-4-4-4" />
                                            <path d="M20 17H4" />
                                        </svg>
                                        <span class="font-bold">Zmena meny</span>
                                    </button>
                                </li>
                                <li>
                                    <button type="submit" id="reconfirm" data-modal-target="changeAmountModal"
                                        data-modal-toggle="changeAmountModal"
                                        class="flex p-2 rounded-sm flex w-full items-center gap-1 transition-all hover:bg-gray-100 cursor-pointer dark:hover:bg-gray-800">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round"
                                            class="lucide lucide-check-check">
                                            <path d="M18 6 7 17l-5-5" />
                                            <path d="m22 10-7.5 7.5L13 16" />
                                        </svg>
                                        <span class="font-bold">Rekonfirmácia</span>
                                    </button>
                                </li>
                                <li>
                                    <button type="submit" class="hidden" id="paymentTrigger"
                                        data-modal-target="splatenie-modal" data-modal-toggle="splatenie-modal" />
                                    <button id="payment" type="button" onmousedown="start(this)" onmouseup="end(this)"
                                        class="flex p-2 rounded-sm w-full flex items-center gap-1 buttonPayment transition-all hover:bg-gray-100 cursor-pointer dark:hover:bg-gray-800">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round"
                                            class="lucide lucide-receipt-euro">
                                            <path
                                                d="M4 2v20l2-1 2 1 2-1 2 1 2-1 2 1 2-1 2 1V2l-2 1-2-1-2 1-2-1-2 1-2-1-2 1Z" />
                                            <path d="M8 12h5" />
                                            <path d="M16 9.5a4 4 0 1 0 0 5.2" />
                                        </svg>
                                        <span class="font-bold">Splatenie</span>
                                    </button>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <button type="button" data-modal-target="create-fee-modal" data-modal-toggle="create-fee-modal"
                        class="text-white bg-gradient-to-r cursor-pointer transition-all from-green-400 via-green-500 to-green-600 hover:bg-gradient-to-br 
                    focus:ring-4 focus:outline-none focus:ring-green-300 dark:focus:ring-green-800 shadow-lg shadow-green-500/50 dark:shadow-lg 
                    dark:shadow-green-800/80 font-bold flex items-center gap-2 rounded-lg text-sm px-5 py-2.5 text-center">Pridať
                        poplatok
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-list-plus">
                            <path d="M11 12H3" />
                            <path d="M16 6H3" />
                            <path d="M16 18H3" />
                            <path d="M18 9v6" />
                            <path d="M21 12h-6" />
                        </svg></button>
                    <section>
                        <?php echo ExcelController::renderButton($poplatky, $columns); ?>
                    </section>
                </section>
            </div>
            <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
                <thead class="text-xs text-gray-700 bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                    <tr>
                        <th scope="col" class="p-4">
                            <div class="flex items-center">
                                <input id="checkbox-all-search" type="checkbox"
                                    class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded-sm focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                                <label for="checkbox-all-search" class="sr-only">checkbox</label>
                            </div>
                        </th>
                        <th scope="col" class="px-1 py-3">
                            <section id="klientTogglerWrapper">
                                <div class="flex items-center gap-1 p-1 px-3">
                                    <span class="text-xs uppercase mr-2">klient</span>
                                    <button id="klientTogglerDependant"
                                        onclick="getFilteredFilter('zmluvy', 'dropdownKlientTarget')"
                                        data-dropdown-toggle="dropdownklient"
                                        class="p-1 filterToggler rounded-md hover:bg-gray-200 transition-all hover:dark:bg-gray-900">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-filter">
                                            <polygon points="22 3 2 3 10 12.46 10 19 16 21 16 12.46 22 3" />
                                        </svg>
                                    </button>
                                    <input type="hidden" class="hiddenSort" name="klientSort" value="" />
                                    <?php include "src/Components/tables/dataTable/header/sorting/sortersButtons.php"; ?>
                                </div>
                                <div id="dropdownklient"
                                    class="absolute z-10 hidden h-72 overflow-y-auto bg-white divide-y divide-gray-100 rounded-lg shadow-sm w-72 dark:bg-gray-700">
                                    <section id="dropdownKlientTarget">
                                        <div
                                            class="w-full h-full animate-pulse py-12 flex flex-col gap-2 justify-center items-center">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="lucide animate-spin lucide-loader-circle">
                                                <path d="M21 12a9 9 0 1 1-6.219-8.56" />
                                            </svg>
                                            <small>Načítavam filtre...</small>
                                        </div>
                                    </section>
                                </div>
                            </section>
                        </th>
                        <th scope="col" class="px-6 py-3 relative">
                            <div class="flex items-center gap-1 p-1 px-3">
                                <span class="text-xs uppercase mr-2">Typ poplatku</span>
                                <button id="typyPoplatkovToggler" hx-get="/api/poplatky/get/typy"
                                    hx-target="#dropdownTypyPoplatkovTarget"
                                    data-dropdown-toggle="dropdownTypyPoplatkov"
                                    class="p-1 filterToggler rounded-md hover:bg-gray-200 transition-all hover:dark:bg-gray-900">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-filter">
                                        <polygon points="22 3 2 3 10 12.46 10 19 16 21 16 12.46 22 3" />
                                    </svg>
                                </button>
                                <input type="hidden" class="hiddenSort" name="typSort" value="" />
                                <?php include "src/Components/tables/dataTable/header/sorting/sortersButtons.php"; ?>
                            </div>
                            <div id="dropdownTypyPoplatkov"
                                class="absolute z-10 hidden w-48 bg-white divide-y divide-gray-100 rounded-lg shadow-sm dark:bg-gray-700 dark:divide-gray-600">
                                <section id="dropdownTypyPoplatkovTarget">
                                    <div
                                        class="w-full h-full animate-pulse py-12 flex flex-col gap-2 justify-center items-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round"
                                            class="lucide animate-spin lucide-loader-circle">
                                            <path d="M21 12a9 9 0 1 1-6.219-8.56" />
                                        </svg>
                                        <small>Načítavam filtre...</small>
                                    </div>
                                </section>
                            </div>
                        </th>
                        <th scope="col" class="px-6 py-3 relative">
                            <div class="flex items-center gap-1 p-1 px-3">
                                <span class="text-xs uppercase mr-2">Rekonfirmovaný</span>
                                <button id="rekonfToggler" hx-get="/api/poplatky/get/rekonfirmed"
                                    hx-target="#dropdownReconfirmedTarget" data-dropdown-toggle="dropdownReconfirmed"
                                    class="p-1 filterToggler rounded-md hover:bg-gray-200 transition-all hover:dark:bg-gray-900">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-filter">
                                        <polygon points="22 3 2 3 10 12.46 10 19 16 21 16 12.46 22 3" />
                                    </svg>
                                </button>
                                <input type="hidden" class="hiddenSort" name="typSort" value="" />
                                <?php include "src/Components/tables/dataTable/header/sorting/sortersButtons.php"; ?>
                            </div>
                            <div id="dropdownReconfirmed"
                                class="absolute z-10 hidden w-48 bg-white divide-y divide-gray-100 rounded-lg shadow-sm dark:bg-gray-700 dark:divide-gray-600">
                                <section id="dropdownReconfirmedTarget">
                                    <div
                                        class="w-full h-full animate-pulse py-12 flex flex-col gap-2 justify-center items-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round"
                                            class="lucide animate-spin lucide-loader-circle">
                                            <path d="M21 12a9 9 0 1 1-6.219-8.56" />
                                        </svg>
                                        <small>Načítavam filtre...</small>
                                    </div>
                                </section>
                            </div>
                        </th>
                        <th scope="col" class="px-6 py-3">
                            <div class="flex items-center gap-1 p-1 px-3">
                                <span class="text-xs uppercase mr-2">dátum</span>
                                <button id="dateRangeToggler"
                                    onclick="getFilteredFilter('dateRange', 'dropdownDateRangeTarget')"
                                    data-dropdown-toggle="dropdownDateRange"
                                    class="p-1 filterToggler rounded-md hover:bg-gray-200 transition-all hover:dark:bg-gray-900">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-filter">
                                        <polygon points="22 3 2 3 10 12.46 10 19 16 21 16 12.46 22 3" />
                                    </svg>
                                </button>
                                <input type="hidden" class="hiddenSort" name="dateSort" value="" />
                                <?php include "src/Components/tables/dataTable/header/sorting/sortersButtons.php"; ?>
                            </div>
                            <div id="dropdownDateRange"
                                class="absolute z-10 hidden p-2 bg-white divide-y max-h-72 overflow-y-auto divide-gray-100 rounded-lg shadow-sm dark:bg-gray-700 dark:divide-gray-600">
                                <section id="dropdownDateRangeTarget">
                                    <div
                                        class="w-full h-full animate-pulse py-12 flex flex-col gap-2 justify-center items-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round"
                                            class="lucide animate-spin lucide-loader-circle">
                                            <path d="M21 12a9 9 0 1 1-6.219-8.56" />
                                        </svg>
                                        <small>Načítavam rozdiel dátumov...</small>
                                    </div>
                                </section>
                            </div>
                        </th>
                        <th scope="col" class="px-1 py-3">
                            <div class="flex items-center gap-1 p-1 px-3">
                                <span class="text-xs uppercase mr-2">isin</span>
                                <button id="isinToggler" hx-get="/api/poplatky/get/isin" hx-target="#dropdownIsinTarget"
                                    data-dropdown-toggle="dropdownIsin"
                                    class="p-1 filterToggler rounded-md hover:bg-gray-200 transition-all hover:dark:bg-gray-900">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-filter">
                                        <polygon points="22 3 2 3 10 12.46 10 19 16 21 16 12.46 22 3" />
                                    </svg>
                                </button>
                                <input type="hidden" class="hiddenSort" name="isinSort" value="" />
                                <?php include "src/Components/tables/dataTable/header/sorting/sortersButtons.php"; ?>
                            </div>
                            <div id="dropdownIsin"
                                class="absolute z-10 hidden p-4 bg-white divide-y divide-gray-100 rounded-lg shadow-sm dark:bg-gray-700 dark:divide-gray-600">
                                <section id="dropdownIsinTarget">
                                    <div
                                        class="w-full h-full animate-pulse py-12 flex flex-col gap-2 justify-center items-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round"
                                            class="lucide animate-spin lucide-loader-circle">
                                            <path d="M21 12a9 9 0 1 1-6.219-8.56" />
                                        </svg>
                                        <small>Načítavam čísla ISIN...</small>
                                    </div>
                                </section>
                            </div>
                        </th>
                        <th scope="col" class="px-1 uppercase py-3">
                            Suma
                        </th>
                        <th scope="col" class="px-6 py-3">
                            <div class="flex items-center gap-1 p-1 px-3">
                                <span class="text-xs uppercase mr-2">mena</span>
                                <button id="menaToggler" hx-get="/api/poplatky/get/meny" hx-target="#dropdownMenaTarget"
                                    data-dropdown-toggle="dropdownMena"
                                    class="p-1 filterToggler rounded-md hover:bg-gray-200 transition-all hover:dark:bg-gray-900">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-filter">
                                        <polygon points="22 3 2 3 10 12.46 10 19 16 21 16 12.46 22 3" />
                                    </svg>
                                </button>
                                <input type="hidden" class="hiddenSort" name="menaSort" value="" />
                                <?php include "src/Components/tables/dataTable/header/sorting/sortersButtons.php"; ?>
                            </div>
                            <div id="dropdownMena"
                                class="absolute z-10 hidden p-2 bg-white divide-y divide-gray-100 rounded-lg shadow-sm dark:bg-gray-700 dark:divide-gray-600">
                                <section id="dropdownMenaTarget">
                                    <div
                                        class="w-full h-full animate-pulse py-12 flex flex-col gap-2 justify-center items-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round"
                                            class="lucide animate-spin lucide-loader-circle">
                                            <path d="M21 12a9 9 0 1 1-6.219-8.56" />
                                        </svg>
                                        <small>Načítavam meny...</small>
                                    </div>
                                </section>
                            </div>
                        </th>
                        <th scope="col" class="px-1 uppercase py-3">
                            Účet
                        </th>
                        <th scope="col" class="px-6 py-3">
                            <div class="flex items-center gap-1 p-1 px-3">
                                <span class="text-xs uppercase mr-2">hotovosť</span>
                                <button id="hotovostToggler" data-dropdown-toggle="dropdownHotovost"
                                    class="p-1 filterToggler rounded-md hover:bg-gray-200 transition-all hover:dark:bg-gray-900">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-filter">
                                        <polygon points="22 3 2 3 10 12.46 10 19 16 21 16 12.46 22 3" />
                                    </svg>
                                </button>
                                <input type="hidden" class="hiddenSort" name="hotovostSort" value="" />
                                <?php include "src/Components/tables/dataTable/header/sorting/sortersButtons.php"; ?>
                            </div>
                            <div id="dropdownHotovost"
                                class="absolute z-10 hidden p-2 bg-white divide-y divide-gray-100 rounded-lg shadow-sm dark:bg-gray-700 dark:divide-gray-600">
                                <ul class="p-3 text-sm text-gray-700 dark:text-gray-200"
                                    aria-labelledby="dropdownRadioButton">
                                    <li class="mb-0.5">
                                        <label for="filter-radio-example-0"
                                            class="w-full text-sm font-medium text-gray-900 rounded-sm dark:text-gray-300">
                                            <div
                                                class="flex cursor-pointer transition-all items-center gap-2 p-2 rounded-sm hover:bg-gray-100 dark:hover:bg-gray-600">
                                                <input id="filter-radio-example-0" type="radio" checked="true" value="0"
                                                    name="hotovost"
                                                    class="w-4 h-4 filterInput text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                                                všetky
                                            </div>
                                        </label>
                                    </li>
                                    <li class="mb-0.5">
                                        <label for="filter-radio-example-1"
                                            class="w-full text-sm font-medium text-gray-900 rounded-sm dark:text-gray-300">
                                            <div
                                                class="flex cursor-pointer transition-all items-center gap-2 p-2 rounded-sm hover:bg-gray-100 dark:hover:bg-gray-600">
                                                <input id="filter-radio-example-1" type="radio" value="1"
                                                    name="hotovost"
                                                    class="w-4 h-4 filterInput text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                                                má dosť
                                            </div>
                                        </label>
                                    </li>
                                    <li class="mb-0.5">
                                        <label for="filter-radio-example-2"
                                            class="w-full text-sm font-medium text-gray-900 rounded-sm dark:text-gray-300">
                                            <div
                                                class="flex cursor-pointer transition-all items-center gap-2 p-2 rounded-sm hover:bg-gray-100 dark:hover:bg-gray-600">
                                                <input id="filter-radio-example-2" type="radio" value="2"
                                                    name="hotovost"
                                                    class="w-4 h-4 filterInput text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                                                nemá dosť
                                            </div>
                                        </label>
                                    </li>
                                </ul>
                            </div>
                        </th>
                        <th scope="col" class="px-1 uppercase py-3">
                            Akcie
                        </th>
                    </tr>
                </thead>
                <tbody id="poplatkyTBODY">
                    <?php
                    foreach ($poplatky as $key => $poplatok) { ?>
                        <tr
                            class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 border-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600">
                            <td class="w-4 p-4">
                                <div class="flex items-center">
                                    <input type="hidden" name="dansadzba1" value="<?php echo $poplatok["dansadzba1"]; ?>" />
                                    <input type="hidden" name="dan" value="<?php echo $poplatok["dan"]; ?>" />
                                    <input type="hidden" name="fondid" value="<?php echo $poplatok["fondid"]; ?>" />
                                    <input id="checkbox-<?php echo $poplatok["id"]; ?>" type="checkbox" name="id"
                                        value="<?php echo $poplatok["id"]; ?>"
                                        class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 tableCheckboxSelect rounded-sm focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                                    <label for="checkbox-<?php echo $poplatok["id"]; ?>" class="sr-only">checkbox</label>
                                </div>
                            </td>
                            <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                <input type="hidden" name="cislozmluvy" value="<?php echo $poplatok["cislozmluvy"]; ?>" />
                                <input type="hidden" name="idcheck" value="<?php echo $poplatok["id"]; ?>" />
                                <?php echo $poplatok["cislozmluvy"]; ?>
                            </th>
                            <td class="px-6 py-4">
                                <input type="hidden" name="typ" value="<?php echo $poplatok["typ"]; ?>" />
                                <input type="hidden" name="uhradapoplatkov"
                                    value="<?php echo $poplatok["uhrada_poplatkov"]; ?>" />
                                <span
                                    class="flex w-full justify-between items-center"><?php echo $poplatok["typ"]; ?></span>
                            </td>
                            <td class="px-6 py-4 reconfirmedColumnData">
                                <input type="hidden" name="reconfirmed" value="<?php echo $poplatok["stav"]; ?>" />
                                <span class="flex w-full justify-between items-center"><small
                                        class="font-bold <?php echo $poplatok["stav"] == 0 ? "text-red-500" : "text-green-500"; ?>">
                                        [<?php echo $poplatok["stav"] === 0 ? "Nerekonfirmovaný" : "Rekonfirmovaný"; ?>]</small></span>
                            </td>
                            <td class="px-6 py-4">
                                <input type="hidden" name="datum" value="<?php echo $poplatok["datum"]; ?>" />
                                <?php echo $poplatok["datum"]; ?>
                            </td>
                            <td class="px-6 py-4">
                                <input type="hidden" name="isin" value="<?php echo $poplatok["isin"]; ?>" />
                                <?php echo $poplatok["isin"]; ?>
                            </td>
                            <td class="px-6 sumaColumnData py-4">
                                <input type="hidden" name="suma" value="<?php echo $poplatok["suma"]; ?>" />
                                <input type="hidden" name="sumaMinus"
                                    value="<?php echo $poplatok["suma"] < 0 ? "true" : "false" ?>" />
                                <?php if ($poplatok["suma"] < 0) { ?>
                                    <span
                                        class="bg-red-100 text-red-800 text-sm font-bold me-2 px-2.5 py-0.5 rounded-sm dark:bg-red-900 dark:text-red-300"><?php echo $poplatok["suma"]; ?></span>
                                <?php } else {
                                    echo $poplatok["suma"];
                                } ?>
                            </td>
                            <td class="px-6 menaColumnData py-4">
                                <input type="hidden" name="mena" value="<?php echo $poplatok["mena"]; ?>" />
                                <?php echo $poplatok["mena"]; ?>
                            </td>
                            <td class="px-6 py-4">
                                <input type="hidden" name="cub" value="<?php echo $poplatok["cub"]; ?>" />
                                <?php echo $poplatok["cub"]; ?>
                            </td>
                            <td class="px-6 py-4 font-bold">
                                <input type="hidden" name="cash" value="<?php echo $poplatok["cash"]; ?>" />
                                <?php echo $poplatok["cash"] . " " . $poplatok["mena"] ?>
                            </td>
                            <td class="px-6 py-4 relative">
                                <div class="absolute flex gap-1 bg-white shadow-lg dark:bg-gray-600 items-center actionConfirm flex-col z-20 p-2 rounded-lg"
                                    style="left: -7rem; top: 3rem; display: none;">
                                    <ul class="p-1 space-y-1 text-sm text-gray-700 dark:text-gray-200"
                                        aria-labelledby="dropdownHelperButton">
                                        <?php if ($poplatok["stav"] > 0) { ?>
                                            <li>
                                                <button type="submit" id="changeSum" data-modal-target="changeAmountModal"
                                                    data-modal-toggle="changeAmountModal"
                                                    class="flex p-2 rounded-sm w-full flex items-center gap-1 transition-all hover:bg-gray-100 cursor-pointer dark:hover:bg-gray-800">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                        stroke-linecap="round" stroke-linejoin="round"
                                                        class="lucide lucide-circle-dollar-sign">
                                                        <circle cx="12" cy="12" r="10" />
                                                        <path d="M16 8h-6a2 2 0 1 0 0 4h4a2 2 0 1 1 0 4H8" />
                                                        <path d="M12 18V6" />
                                                    </svg>
                                                    <span class="font-bold">Zmena sumy</span>
                                                </button>
                                            </li>
                                            <li>
                                                <button type="submit" id="changeMena" data-modal-target="changeAmountModal"
                                                    data-modal-toggle="changeAmountModal"
                                                    class="flex p-2 rounded-sm w-full flex items-center gap-1 transition-all hover:bg-gray-100 cursor-pointer dark:hover:bg-gray-800">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                        stroke-linecap="round" stroke-linejoin="round"
                                                        class="lucide lucide-arrow-left-right">
                                                        <path d="M8 3 4 7l4 4" />
                                                        <path d="M4 7h16" />
                                                        <path d="m16 21 4-4-4-4" />
                                                        <path d="M20 17H4" />
                                                    </svg>
                                                    <span class="font-bold">Zmena meny</span>
                                                </button>
                                            </li>
                                        <?php } ?>
                                        <?php if ($poplatok["stav"] === 0) { ?>
                                            <li>
                                                <button type="submit" id="reconfirm" data-modal-target="changeAmountModal"
                                                    data-modal-toggle="changeAmountModal"
                                                    class="flex p-2 rounded-sm flex w-full items-center gap-1 transition-all hover:bg-gray-100 cursor-pointer dark:hover:bg-gray-800">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                        stroke-linecap="round" stroke-linejoin="round"
                                                        class="lucide lucide-check-check">
                                                        <path d="M18 6 7 17l-5-5" />
                                                        <path d="m22 10-7.5 7.5L13 16" />
                                                    </svg>
                                                    <span class="font-bold">Rekonfirmácia</span>
                                                </button>
                                            </li>
                                        <?php } ?>
                                        <?php if ($poplatok["stav"] > 0) { ?>
                                            <li>
                                                <button type="button" onmousedown="start(this)" onmouseup="end(this)"
                                                    class="flex p-2 rounded-sm w-full flex items-center gap-1 buttonPayment transition-all hover:bg-gray-100 cursor-pointer dark:hover:bg-gray-800">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                        stroke-linecap="round" stroke-linejoin="round"
                                                        class="lucide lucide-receipt-euro">
                                                        <path
                                                            d="M4 2v20l2-1 2 1 2-1 2 1 2-1 2 1 2-1 2 1V2l-2 1-2-1-2 1-2-1-2 1-2-1-2 1Z" />
                                                        <path d="M8 12h5" />
                                                        <path d="M16 9.5a4 4 0 1 0 0 5.2" />
                                                    </svg>
                                                    <span class="font-bold">Splatenie</span>
                                                </button>
                                            </li>
                                        <?php } ?>
                                        <li class="border-t">
                                            <button type="submit" id="cancelContract" data-modal-target="termination-modal"
                                                data-modal-toggle="termination-modal"
                                                class="flex p-2 rounded-sm flex w-full items-center gap-1 transition-all hover:bg-gray-100 cursor-pointer dark:hover:bg-gray-800">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                    viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                    stroke-linecap="round" stroke-linejoin="round"
                                                    class="lucide lucide-square-x">
                                                    <rect width="18" height="18" x="3" y="3" rx="2" ry="2" />
                                                    <path d="m15 9-6 6" />
                                                    <path d="m9 9 6 6" />
                                                </svg>
                                                <span class="font-bold text-red-400">Ukončenie
                                                    zmluvy</span>
                                            </button>
                                        </li>
                                    </ul>
                                </div>
                                <button type="button" onclick="showActionMenu(this)"
                                    class="p-1 rounded-lg actionShow hover:bg-blue-300 hover:text-blue-800 transition-all cursor-pointer">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-ellipsis-vertical">
                                        <circle cx="12" cy="12" r="1" />
                                        <circle cx="12" cy="5" r="1" />
                                        <circle cx="12" cy="19" r="1" />
                                    </svg>
                                </button>
                            </td>
                        </tr>
                    <?php } ?>
                </tbody>
            </table>
            <input type="hidden" id="filteredValuesInput2" name="filteredValuesInput" value='<?php if (isset($_POST["filteredValuesInput"]))
                echo $_POST["filteredValuesInput"]; ?>' />
        </div>
    </form>
    <nav id="pagination"
        class="flex items-center mb-10 shadow-lg rounded-b-lg -mt-1 flex-column flex-wrap md:flex-row justify-between p-4"
        aria-label="Table navigation">
        <form id="paginationForm" class="mb-0 w-full flex justify-between" hx-replace-url="true" hx-push-url="true">
            <input type="hidden" id="filteredValuesInput" name="filteredValuesInput" value='<?php if (isset($_POST["filteredValuesInput"]))
                echo $_POST["filteredValuesInput"]; ?>' />
            <span
                class="text-sm font-normal text-gray-500 dark:text-gray-400 mb-4 md:mb-0 block w-full md:inline md:w-auto">Počet
                výsledkov:
                <span class="font-semibold text-gray-900 dark:text-white"></span><span
                    class="font-semibold text-gray-900 dark:text-white"
                    id="totalCountBottom"><?php echo $count; ?></span></span>
            <?php if (ceil($total_count / $limit) > 0): ?>
                <ul class="inline-flex -space-x-px rtl:space-x-reverse text-sm h-8">
                    <?php if ($page > 1): ?>
 
                    <?php endif; ?>

                    <?php if ($page > 3): ?>
                        <input type="hidden" id="numPageInput" name="page" value="<?php echo $page; ?>" />
                        <button type="submit" value="?page=1&limit=<?php echo $limit; ?>">
                            <li
                                class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                                1
                            </li>
                        </button>
                        <li
                            class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                            ...</li>
                    <?php endif; ?>

                    <?php if ($page - 2 > 0): ?>
                        <input type="hidden" id="numPageInput" name="page" value="<?php echo $page - 2; ?>" />
                        <button type="submit" value="?page=<?php echo $page - 2 ?>&limit=<?php echo $limit; ?>">
                            <li
                                class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                                <?php echo $page - 2 ?>
                            </li>
                        </button>
                    <?php endif; ?>
                    <?php if ($page - 1 > 0): ?>
                        <input type="hidden" id="numPageInput" name="page" value="<?php echo $page - 1; ?>" />
                        <button type="submit" value="?page=<?php echo $page - 1 ?>&limit=<?php echo $limit; ?>">
                            <li
                                class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                                <?php echo $page - 1 ?>
                            </li>
                        </button>
                    <?php endif; ?>
                    <input type="hidden" id="numPageInput" name="page" value="<?php echo $page; ?>" />
                    <button type="submit" value="?page=<?php echo $page ?>&limit=<?php echo $limit; ?>">
                        <li
                            class="flex items-center justify-center px-3 h-8 text-blue-600 border border-gray-300 bg-blue-50 hover:bg-blue-100 hover:text-blue-700 dark:border-gray-700 dark:bg-gray-700 dark:text-white">
                            <?php echo $page ?>
                        </li>
                    </button>

                    <?php if ($page + 1 < ceil($total_count / $limit) + 1): ?>
                        <input type="hidden" id="numPageInput" name="page" value="<?php echo $page + 1; ?>" />
                        <button type="submit" value="?page=<?php echo $page + 1 ?>&limit=<?php echo $limit; ?>">
                            <li
                                class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                                <?php echo $page + 1 ?>
                            </li>
                        </button>
                    <?php endif; ?>
                    <?php if ($page + 2 < ceil($total_count / $limit) + 1): ?>
                        <input type="hidden" id="numPageInput" name="page" value="<?php echo $page + 2; ?>" />
                        <button type="submit" value="?page=<?php echo $page + 2 ?>&limit=<?php echo $limit; ?>">
                            <li
                                class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                                <?php echo $page + 2 ?>
                            </li>
                        </button>
                    <?php endif; ?>

                    <?php if ($page < ceil($total_count / $limit) - 2): ?>
                        <li
                            class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                            ...</li>
                        <input type="hidden" id="numPageInput" name="page" value="<?php echo ceil($total_count / $limit); ?>" />
                        <button type="submit"
                            value="?page=<?php echo ceil($total_count / $limit) ?>&limit=<?php echo $limit; ?>">
                            <li
                                class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                                <?php echo ceil($total_count / $limit) ?>
                            </li>
                        </button>
                    <?php endif; ?>

                    <?php if ($page < ceil($total_count / $limit)): ?>
                        <input type="hidden" id="numPageInput" name="page" value="<?php echo $page + 1; ?>" />
                        <button type="submit" value="?page=<?php echo $page + 1 ?>&limit=<?php echo $limit; ?>">
                            <li
                                class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 rounded-e-lg hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                                Next
                            </li>
                        </button>
                    <?php endif; ?>
                </ul>
            <?php endif; ?>
        </form>
    </nav>
    </div>
</section>
<?php include "modals/terminationModal.php"; ?>
<?php include "modals/actionModal.php"; ?>
<?php include "modals/splatenieModal.php"; ?>
<?php include "modals/createFee.php"; ?>
<script>
    $(window).on("scroll", () => {
        if (window.pageYOffset > 200) {
            $("#tableTopHeader").attr("style", "position: fixed;top: 7rem;right: 2rem;left: 17rem;");
            $("thead").attr("style", "position: sticky;left: 17rem;right: 2rem;top: 12.5rem;z-index: 10;");
        } else {
            $("#tableTopHeader").attr("style", "");
            $("thead").attr("style", "");
        }
    });
</script>
<script src="/src/assets/js/global/poplatky/index.js"></script>
<script src="/src/assets/js/prehlady/nbs-gfi/excel.js"></script>
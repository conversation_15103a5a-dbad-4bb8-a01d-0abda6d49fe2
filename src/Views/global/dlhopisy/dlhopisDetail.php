<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

$typSplatenia = 'kupon';
$isincurrric = isset($matches[1]) ? $matches[1] : null;

if (str_contains($_SERVER["REQUEST_URI"], "/kupon/potvrdenie")) {
    $object = "kupon";
    $action = "potvrdenie";
    $pageHeading = "Potvrdenie kupónu pre dlhopis ";
} else if (str_contains($_SERVER["REQUEST_URI"], "/kupon/splatenie")) {
    $object = "kupon";
    $action = "splatenie";
    $pageHeading = "Splatenie kupónu pre dlhopis";
} else if (str_contains($_SERVER["REQUEST_URI"], "/istina/potvrdenie")) {
    $object = "istina";
    $action = "potvrdenie";
    $pageHeading = "Potvrdenie istiny pre dlhopis";
} else if (str_contains($_SERVER["REQUEST_URI"], "/istina/splatenie")) {
    $object = "istina";
    $action = "splatenie";
    $pageHeading = "Splatenie istiny pre dlhopis";
}


if (isset($_SESSION["client"])) {
    $id_fond = $_SESSION["client"]["fondid"];
} else {
    $id_fond = 0;
}

if ($action == "potvrdenie") {
    if ($object === "kupon") {
        $query = "SELECT DISTINCT 361                                         as kodobratu,
            315124                                      as uctovnykod,
            mt1.kodaktiva,
            mt1.ucetaktiva,
            mt1.pocet,
            mt1.mena                                    as mena,
            mt1.subjektid,
            p.cislozmluvy,
            d.cpnazskratka,
            f_koef_auv_isincurrric(dcr.isincurrric, d.zaklad, d.dateemisie::varchar, d.maturitydate,
                                   f.datesplatnost, d.prvy_kupon::varchar, d.posledny_kupon::varchar, d.kupfrek, d.exfrekkup,
                                   2)                   as fkoas,
            mt1.pocet * ROUND(d.nominalemisie * f_kuponfaktor(mt1.kodaktiva::varchar,
                                                              (f_last_coupondate(mt1.kodaktiva, (SELECT MAX(datum) FROM TODAY)) -
                                                               1)) / 100 *
                              f_koef_auv_isincurrric(dcr.isincurrric, d.zaklad, d.dateemisie::varchar, d.maturitydate,
                                                     f.datesplatnost, d.prvy_kupon::varchar, d.posledny_kupon::varchar, d.kupfrek, d.exfrekkup,
                                                     2),
                              COALESCE(d.rounding, 25)) AS suma
            from majetoktoday mt1,
            floatkupon f,
            portfolio p,
            dbequity d,
            dbequitycurr dc,
            dbequitycurrric dcr
            where mt1.eqid = 'Bonds'
            and is_coupon_date(mt1.kodaktiva, (select max(datum) from today)) = 1
            and mt1.uctovnykod = 251110
            and f.datesplatnost = (select max(datum) from today)
            and mt1.obratid = 0
            and not exists(select *
                        from majetoktoday mt2
                        where mt2.subjektid = mt1.subjektid
                            and mt2.uctovnykod = 315124
                            and mt2.eqid = mt1.eqid
                            and mt2.kodaktiva = mt1.kodaktiva
                            and mt2.ucetaktiva = mt1.ucetaktiva
                            and mt2.mena = mt1.mena
                            and mt2.md_d = mt1.md_d)
            and p.fondid = mt1.subjektid
            and dc.isincurr = dcr.isincurr
            and dcr.isincurrric = mt1.kodaktiva
            and d.isin = dc.isin";
    }
    if ($object === "istina") {
        $query = "select 
        360 as kodobratu, 315123 as uctovnykod, mt1.kodaktiva, mt1.ucetaktiva, 
        mt1.pocet, mt1.mena as mena, mt1.subjektid, p.cislozmluvy,
        d.cpnazskratka,
        mt1.pocet * d.nominalemisie * f_istina(mt1.kodaktiva, f_last_istinadate(mt1.kodaktiva, (SELECT MAX(datum) FROM TODAY))) / 100 AS suma				
    from majetoktoday mt1, portfolio p, dbequity d 
    where
        mt1.eqid = 'Bonds'
        and f_istina(mt1.kodaktiva, (select max(datum) from today)) > 0
        and mt1.uctovnykod = 251110
        and mt1.obratid = 0
        and not exists(select * from majetoktoday mt2 where mt2.subjektid=mt1.subjektid and mt2.uctovnykod=315123 and mt2.eqid=mt1.eqid and mt2.kodaktiva=mt1.kodaktiva and mt2.ucetaktiva=mt1.ucetaktiva and mt2.mena=mt1.mena and mt2.md_d=mt1.md_d)
        and p.fondid = mt1.subjektid
        and d.isin = (select max(dc.isin) from dbequitycurr dc, dbequitycurrric dcr where dc.isincurr=dcr.isincurr and dcr.isincurrric=mt1.kodaktiva)";
    }

    if ($isincurrric != "")
        $query .= " and mt1.kodaktiva LIKE '$isincurrric%'";
    if ($id_fond != 0)
        $query .= " and mt1.subjektid=$id_fond";
    $dlhopisy = Connection::getDataFromDatabase($query, defaultDB)[1];
} else {
    if ($isincurrric != "")
        $predmet .= " and mt.kodaktiva LIKE '$isincurrric%'";
    if ($id_fond != 0)
        $predmet .= " and mt.subjektid=$id_fond";
    if ($object === "kupon"){
        $mtUctovnykod = "315124";
    } else {
        $mtUctovnykod = "315123";
    }

    $query = "WITH filtered_mt AS (SELECT *
                     FROM majetoktoday mt
                     WHERE mt.eqid = 'Bonds'
                       AND mt.md_d = 0
                       $predmet
                       AND mt.uctovnykod = $mtUctovnykod
                       AND NOT EXISTS (SELECT 1
                                       FROM majetoktoday mt2
                                       WHERE mt2.subjektid = mt.subjektid
                                         AND mt2.eqid = mt.eqid
                                         AND mt2.ucetaktiva = mt.ucetaktiva
                                         AND mt2.kodaktiva = mt.kodaktiva
                                         AND mt2.mena = mt.mena
                                         AND mt2.uctovnykod = mt.uctovnykod
                                         AND mt2.md_d = 1)),
     istina_dates AS (SELECT mt.kodaktiva, t.datum, f_last_istinadate(mt.kodaktiva, t.datum) AS istdate
                      FROM filtered_mt mt,
                           today t
                      WHERE mt.subjektid = t.fondid),
     coupon_dates AS (SELECT mt.kodaktiva, t.datum, f_last_coupondate(mt.kodaktiva, t.datum) AS kupdate
                      FROM filtered_mt mt,
                           today t
                      WHERE mt.subjektid = t.fondid)
SELECT DISTINCT id.istdate,
                cd.kupdate,
                t.datum                                                           AS todaydate,
                (CASE WHEN mt.uctovnykod = 315123 THEN 'istina' ELSE 'kupon' END) AS typSplatenia,
                mt.subjektid,
                p.cislozmluvy,
                round(de.nominalemisie * f_kuponfaktor(mt.kodaktiva, cd.kupdate - 1) / 100 *
                      f_koef_auv_isincurrric(dr.isincurrric, de.zaklad, de.dateemisie::varchar, de.maturitydate, f.datesplatnost,
                                             de.prvy_kupon::varchar, de.posledny_kupon::varchar, de.kupfrek, de.exfrekkup, 2),
                      COALESCE(de.rounding, 25))                                  AS kuponkus,
                de.nominalemisie * f_istina(mt.kodaktiva, id.istdate) / 100       AS istinakus,
                mt.kodaktiva,
                (CASE WHEN mt.uctovnykod = 315123 THEN 251110 ELSE 251120 END)    AS uctovnykod,
                de.cpnazskratka,
                mt.mena,
                de.nominalemisie,
                de.dan,
                de.istfrek,
                mt.pocet,
                de.druheqid,
                de.isin,
                dc.currencytrade,
                dr.ric,
                mt.ucetaktiva,
                months_diff(de.maturitydate, de.dateemisie)                       AS pocetmesiacov,
                to_char(de.maturitydate, 'dd.mm.yyyy')                            AS maturitydate,
                de.splatnost,
                dr.typ_maj_uctu_cp,
                mt.pocet * ROUND(de.nominalemisie * f_kuponfaktor(mt.kodaktiva::varchar,
                                                              (f_last_coupondate(mt.kodaktiva, (SELECT MAX(datum) FROM TODAY)) -
                                                               1)) / 100 *
                              f_koef_auv_isincurrric(dr.isincurrric, de.zaklad, de.dateemisie::varchar, de.maturitydate,
                                                     f.datesplatnost, de.prvy_kupon::varchar, de.posledny_kupon::varchar, de.kupfrek, de.exfrekkup,
                                                     2),
                              COALESCE(de.rounding, 25)) AS suma
FROM filtered_mt mt
         JOIN today t ON mt.subjektid = t.fondid
         JOIN portfolio p ON p.fondid = mt.subjektid
         JOIN dbequitycurrric dr ON dr.isincurrric = mt.kodaktiva
         JOIN dbequitycurr dc ON dc.isincurr = dr.isincurr
         JOIN dbequity de ON dc.isin = de.isin
         JOIN floatkupon f ON dr.isincurrric = f.isincurrric
         JOIN istina_dates id ON mt.kodaktiva = id.kodaktiva AND t.datum = id.datum
         JOIN coupon_dates cd ON mt.kodaktiva = cd.kodaktiva AND t.datum = cd.datum";
    $dlhopisy = Connection::getDataFromDatabase($query, defaultDB)[1];
}
echo $query;
?>
<section class="p-5 relative">
    <form id="doActionForm">
        <div id="toast" class="right-2 w-full z-10 top-0 shadow-lg h-5 overflow-hidden"></div>
        <h2 class="mb-4 text-3xl dark:text-gray-100 font-bold"><?php echo $pageHeading ?>
            <span class="text-purple-600"><?php echo $isincurrric; ?></span>
        </h2>
        <section
            class="flex items-center w-full <?php echo $action === "splatenie" ? "justify-between" : "justify-end" ?> mb-4">
            <?php if ($action === "splatenie") { ?>
                <div>
                    <label for="sadzba" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Nastavenie
                        sadzby
                        globálne</label>
                    <input type="text" id="sadzba" name="sadzba" value="<?php echo $dlhopisy[0]["dan"]; ?>"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                        placeholder="Sadzba" required />
                </div>
            <?php } ?>
            <button type="submit" id="submitter"
                class="text-white bg-gradient-to-r flex items-center gap-1 from-blue-500 via-blue-600 to-blue-700 hover:bg-gradient-to-br focus:ring-4 focus:outline-none focus:ring-blue-300 dark:focus:ring-blue-800 shadow-lg shadow-blue-500/50 dark:shadow-lg dark:shadow-blue-800/80 font-bold rounded-lg text-lg px-10 py-2.5 text-center me-2 mb-2 ">
                <span>Potvrdiť</span>
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
                    stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide animate-spin lucide-loader-circle" style="display: none;">
                    <path d="M21 12a9 9 0 1 1-6.219-8.56" />
                </svg>
            </button>
        </section>
        <div class="relative overflow-x-auto shadow-md sm:rounded-lg">
            <?php if ($dlhopisy[0] === NULL || $dlhopisy[1] === 0) {
                echo '<div class="p-4 my-4 text-sm text-blue-800 rounded-lg bg-blue-50 w-full dark:bg-gray-700 dark:text-blue-400"
                                    role="alert">
                                    <span class="font-bold">Žiadne dáta!</span> Pre zvolený dlhopis sa nenašli žiadne dáta.
                                </div>';
            } else {
                ?>
                <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                        <?php if ($action == "splatenie") { ?>
                            <tr>
                                <th scope="col" class="p-4">
                                    <div class="flex items-center">
                                        <input id="checkbox-all" type="checkbox"
                                            class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded-sm focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                                        <label for="checkbox-all" class="sr-only">checkbox</label>
                                    </div>
                                </th>
                                <th scope="col" class="px-6 py-3">
                                    Klient
                                </th>
                                <th scope="col" class="px-6 py-3">
                                    Aktívum
                                </th>
                                <th scope="col" class="px-6 py-3">
                                    Mena
                                </th>
                                <th scope="col" class="px-6 py-3">
                                    Výnos
                                </th>
                                <th scope="col" class="px-6 py-3">
                                    Hrubý výnos
                                </th>
                                <th scope="col" class="px-6 py-3">
                                    Sadzba dane
                                </th>
                                <th scope="col" class="px-6 py-3">
                                    Daň
                                </th>
                                <th scope="col" class="px-6 py-3">
                                    Účet vysporiadania
                                </th>
                                <th scope="col" class="px-6 py-3">
                                    Variabilný symbol
                                </th>
                            </tr>
                        <?php } else { ?>
                            <tr>
                                <th scope="col" class="p-4">
                                    <div class="flex items-center">
                                        <input id="checkbox-all" type="checkbox"
                                            class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded-sm focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                                        <label for="checkbox-all" class="sr-only">checkbox</label>
                                    </div>
                                </th>
                                <th scope="col" class="px-6 py-3">
                                    Klient
                                </th>
                                <th scope="col" class="px-6 py-3">
                                    Aktívum
                                </th>
                                <th scope="col" class="px-6 py-3">
                                    Počet
                                </th>
                                <th scope="col" class="px-6 py-3">
                                    Suma
                                </th>
                                <th scope="col" class="px-6 py-3">
                                    Mena
                            </tr>
                        <?php } ?>
                    </thead>
                    <tbody>
                        <?php
                        foreach ($dlhopisy as $key => $dlhopis) {
                            if ($action === "splatenie") {
                                $zaklad_dane = round($dlhopis["kuponkus"] * $dlhopis["pocet"], 2);
                                $zaplatena_dan = (floor($zaklad_dane * $dlhopis["dan"])) / 100;
                                if ($object == "istina") {
                                    $nakus = $dlhopis["istinakus"];
                                } else {
                                    $nakus = $dlhopis["kuponkus"];
                                }
                                $istsum = (float) $dlhopis["istinakus"] * (float) $dlhopis["pocet"];
                                $kupsum = $zaklad_dane - $zaplatena_dan;
                                ?>
                                <tr
                                    class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 border-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600">
                                    <td class="w-4 p-4">
                                        <input type="hidden" name="action" value="<?php echo $action; ?>" />
                                        <input type="hidden" name="kodaktiva" value="<?php echo $dlhopis["kodaktiva"] ?>">
                                        <input type="hidden" name="pocet" value="<?php echo $dlhopis["pocet"] ?>">
                                        <input type="hidden" name="uctovnykod" value="<?php echo $dlhopis["uctovnykod"] ?>">
                                        <input type="hidden" name="kodobratu" value="<?php echo $dlhopis["kodobratu"] ?>">
                                        <input type="hidden" name="ocakdate"
                                            value="<?php echo ($object == "istina") ? $dlhopis["istdate"] : $dlhopis["kupdate"]; ?>">
                                        <input type="hidden" name="suma"
                                            value="<?php echo ($object == "istina") ? $istsum : $kupsum; ?>">
                                        <input type="hidden" name="mena" value="<?php echo $dlhopis["mena"] ?>">
                                        <input type="hidden" name="typ" value="<?php echo $object ?>">
                                        <input type="hidden" name="isin" value="<?php echo $dlhopis["isin"] ?>">
                                        <input type="hidden" name="curr" value="<?php echo $dlhopis["currencytrade"] ?>">
                                        <input type="hidden" name="ric" value="<?php echo $dlhopis["ric"] ?>">
                                        <input type="hidden" name="cpnaz" value="<?php echo $dlhopis["cpnazskratka"] ?>">
                                        <input type="hidden" name="ucetaktiva" value="<?php echo $dlhopis["ucetaktiva"] ?>">
                                        <input type="hidden" name="subjektid" value="<?php echo $dlhopis["subjektid"] ?>">
                                        <input type="hidden" name="realdate" value="<?php echo $dlhopis["todaydate"]; ?>">
                                        <input type="hidden" name="nakus" value="<?php echo number_format($nakus, 7); ?>">
                                        <div class="flex items-center">
                                            <input id="checkbox-table-<?php echo $key ?>" type="checkbox" name="checkboxItem"
                                                class="w-4 h-4 text-blue-600 checkbox-table bg-gray-100 border-gray-300 rounded-sm focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                                            <label for="checkbox-table-1" class="sr-only">checkbox</label>
                                        </div>
                                    </td>
                                    <th scope="row" class="px-6 py-4 font-bold text-gray-900 whitespace-nowrap dark:text-white">
                                        <?php echo $dlhopis["cislozmluvy"]; ?>
                                    </th>
                                    <th scope="row" class="px-6 py-4 font-bold text-gray-900 whitespace-nowrap dark:text-white">
                                        <?php echo $dlhopis["cpnazskratka"]; ?>
                                    </th>
                                    <td class="px-6 py-4">
                                        <?php echo $dlhopis["mena"]; ?>
                                    </td>
                                    <td class="px-6 py-4">
                                        <span
                                            class="suma<?php echo $key; ?>"><?php echo number_format($dlhopis["suma"], 2, ".", " "); ?></span>
                                    </td>
                                    <td class="px-6 py-4">
                                        <input type="hidden" name="zaklad_dane" value="<?php echo $zaklad_dane; ?>">
                                        <span
                                            class="zakladDane<?php echo $key; ?>"><?php echo number_format($zaklad_dane, 2, ".", " "); ?></span>
                                    </td>
                                    <td class="px-6 py-4">
                                        <input type="hidden" name="sadzba_dane" value="<?php echo $dlhopis["dan"]; ?>">
                                        <span
                                            class="sadzbaDaneText"><?php echo number_format($dlhopis["dan"], 2, ".", " "); ?></span>
                                    </td>
                                    <td class="px-6 py-4">
                                        <input type="hidden" name="zaplatena_dan" value="<?php echo $zaplatena_dan; ?>">
                                        <span
                                            class="zaplatenaDan<?php echo $key; ?>"><?php echo number_format($zaplatena_dan, 2, ".", " "); ?></span>
                                    </td>
                                    <td class="px-6 py-4">
                                        <?php
                                        $mena = $dlhopis["mena"];
                                        if ($typ_maj_uctu_cp == 'CBL') {
                                            $cubs = Connection::getDataFromDatabase("SELECT * FROM fondsbu WHERE mena = '$mena' AND cub LIKE '%/0900' AND fondid = $id_fond", defaultDB)[1];
                                        } else {
                                            $cubs = Connection::getDataFromDatabase("SELECT * FROM fondsbu WHERE mena = '$mena' AND fondid = $id_fond", defaultDB)[1];
                                        } ?>
                                        <select id="cub" name="cub"
                                            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                                            <?php foreach ($cubs as $cub) { ?>
                                                <option value="<?php echo $cub["cub"] ?>"><?php echo $cub["cub"] ?></option>
                                            <?php } ?>
                                        </select>
                                    </td>
                                    <td class="px-6 py-4">
                                        <?php
                                        $datum = strtotime($dlhopis["todaydate"]);
                                        $datum = date('dmY', $datum);
                                        echo $datum;
                                        ?>
                                        <input type="hidden" name="vs" value="<?php echo $datum; ?>" />
                                    </td>
                                </tr>
                            <?php } else { ?>
                                <tr
                                    class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 border-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600">
                                    <td class="w-4 p-4">
                                        <input type="hidden" name="action" value="<?php echo $action; ?>" />
                                        <input type="hidden" name="kodaktiva" value="<?php echo $dlhopis["kodaktiva"] ?>">
                                        <input type="hidden" name="pocet" value="<?php echo $dlhopis["pocet"] ?>">
                                        <input type="hidden" name="uctovnykod" value="<?php echo $dlhopis["uctovnykod"] ?>">
                                        <input type="hidden" name="kodobratu" value="<?php echo $dlhopis["kodobratu"] ?>">
                                        <input type="hidden" name="ocakdate"
                                            value="<?php echo ($object == "istina") ? $dlhopis["istdate"] : $dlhopis["kupdate"]; ?>">
                                        <input type="hidden" name="suma"
                                            value="<?php echo ($object == "istina") ? $istsum : $kupsum; ?>">
                                        <input type="hidden" name="mena" value="<?php echo $dlhopis["mena"] ?>">
                                        <input type="hidden" name="typ" value="<?php echo $object ?>">
                                        <input type="hidden" name="isin" value="<?php echo $dlhopis["isin"] ?>">
                                        <input type="hidden" name="curr" value="<?php echo $dlhopis["currencytrade"] ?>">
                                        <input type="hidden" name="ric" value="<?php echo $dlhopis["ric"] ?>">
                                        <input type="hidden" name="cpnaz" value="<?php echo $dlhopis["cpnazskratka"] ?>">
                                        <input type="hidden" name="ucetaktiva" value="<?php echo $dlhopis["ucetaktiva"] ?>">
                                        <input type="hidden" name="subjektid" value="<?php echo $dlhopis["subjektid"] ?>">
                                        <input type="hidden" name="realdate" value="<?php echo $dlhopis["todaydate"]; ?>">
                                        <input type="hidden" name="nakus" value="<?php echo number_format($nakus, 7); ?>">
                                        <div class="flex items-center">
                                            <input id="checkbox-table-<?php echo $key ?>" type="checkbox" name="checkboxItem"
                                                class="w-4 h-4 text-blue-600 checkbox-table bg-gray-100 border-gray-300 rounded-sm focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                                            <label for="checkbox-table-1" class="sr-only">checkbox</label>
                                        </div>
                                    </td>
                                    <th scope="row" class="px-6 py-4 font-bold text-gray-900 whitespace-nowrap dark:text-white">
                                        <?php echo $dlhopis["cislozmluvy"]; ?>
                                    </th>
                                    <th scope="row" class="px-6 py-4 font-bold text-gray-900 whitespace-nowrap dark:text-white">
                                        <?php echo $dlhopis["cpnazskratka"]; ?>
                                    </th>
                                    <td class="px-6 py-4">
                                        <?php echo $dlhopis["pocet"]; ?>
                                    </td>
                                    <td class="px-6 py-4">
                                        <?php echo number_format($dlhopis["suma"], 2, ".", " "); ?>
                                    </td>
                                    <td class="px-6 py-4">
                                        <?php echo $dlhopis["mena"]; ?>
                                    </td>
                                </tr>
                            <?php }
                        } ?>
                    </tbody>
                </table>
            <?php } ?>
        </div>
    </form>
</section>
<script>
    $("#sadzba").on("keyup", (e) => {
        if (e.target.value === NaN || e.target.value < 0 || e.target.value === "") {
            e.target.value = 0;
        }
        $(".sadzbaDaneText").each((i, item) => {
            const sadzbaDane = parseFloat(e.target.value);
            const zakladDane = parseFloat($(".zakladDane" + i).html());
            const dan = parseFloat(Math.floor(parseFloat(sadzbaDane) * parseFloat(zakladDane)) / 100);
            const suma = parseFloat(zakladDane - dan);
            console.log(sadzbaDane, zakladDane);
            item.innerHTML = e.target.value;
            $(".zaplatenaDan" + i).html(dan.toFixed(2));
            $(".suma" + i).html(suma.toFixed(2));
        });
    });
    $("#checkbox-all").on("change", (e) => {
        if (e.target.checked) {
            $(".checkbox-table").each((i, item) => {
                item.checked = true;
            });
        } else {
            $(".checkbox-table").each((i, item) => {
                item.checked = false;
            });
        }
    });

    $("#doActionForm").on("submit", (e) => {
        e.preventDefault();
        $("#submitter span").html("Potvrdzujem...");
        $("#submitter svg").css("display", "inline-flex");
        const formData = new FormData(e.currentTarget);
        dataCount = formData.getAll("checkboxItem").length;
        console.log(formData);
        const formDataObj = [];
        for (let i = 0; dataCount > i; i++) {
            formDataObj.push({
                action: formData.getAll("action")[i],
                subjektid: formData.getAll("subjektid")[i],
                isin: formData.getAll("isin")[i],
                pocet: formData.getAll("pocet")[i],
                mena: formData.getAll("mena")[i],
                uctovnykod: formData.getAll("uctovnykod")[i],
                kodobratu: formData.getAll("kodobratu")[i],
                kodaktiva: formData.getAll("kodaktiva")[i],
                ucetaktiva: formData.getAll("ucetaktiva")[i],
                ocakdate: formData.getAll("ocakdate")[i],
                suma: formData.getAll("suma")[i],
                vs: formData.getAll("vs")[i],
                typ: formData.getAll("typ")[i],
                zaklad_dane: formData.getAll("zaklad_dane")[i],
                sadzba_dane: formData.getAll("sadzba_dane")[i],
                zaplatena_dan: formData.getAll("zaplatena_dan")[i],
                nakus: formData.getAll("nakus")[i],
                cub: formData.getAll("cub")[i]
            });
        }
        console.log(formDataObj);
        htmx.ajax('POST', `/api/dlhopisy/<?php echo $action; ?>`,
            {
                target: "#toast",
                values: { "data": JSON.stringify(formDataObj), "action": '<?php echo $action; ?>' }
            }).then((response) => {

            });
    });
</script>
<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

if (str_contains($_SERVER["REQUEST_URI"], "/kupon/potvrdenie")) {
    $object = "kupon";
    $action = "potvrdenie";
    $pageHeading = "Potvrdenie kupónu pre dlhopis (zoznam dlhopisov)";
} else if (str_contains($_SERVER["REQUEST_URI"], "/kupon/splatenie")) {
    $object = "kupon";
    $action = "splatenie";
    $pageHeading = "Splatenie kupónu pre dlhopis (zoznam dlhopisov)";
} else if (str_contains($_SERVER["REQUEST_URI"], "/istina/potvrdenie")) {
    $object = "istina";
    $action = "potvrdenie";
    $pageHeading = "Potvrdenie istiny pre dlhopis (zoznam dlhopisov)";
} else if (str_contains($_SERVER["REQUEST_URI"], "/istina/splatenie")) {
    $object = "istina";
    $action = "splatenie";
    $pageHeading = "Splatenie istiny pre dlhopis (zoznam dlhopisov)";
}

$query = "SELECT DISTINCT dcr.isincurrric,
       d.isinreal,
       dcr.isincurrric as isincurrric,
       d.cpnaz as cpnaz,
       d.cpnazskratka,
       dc.currencytrade as mena
    from dbequity d,
        dbequitycurr dc,
        dbequitycurrric dcr,
        sanctionlist s
    where dc.isin = d.isin
    and d.eqid = 'Bonds'
    and dcr.isincurr = dc.isincurr
    and s.isin IS DISTINCT FROM d.isin
    and d.maturitydate >= (select max(datum) from today)
    order by d.isinreal, d.cpnaz, d.cpnazskratka, dc.currencytrade";
echo $dlhopisy;
$dlhopisy = Connection::getDataFromDatabase($query, defaultDB)[1];
?>
<section class="p-5">
    <h2 class="mb-4 text-3xl dark:text-gray-100 font-bold"><?php echo $pageHeading; ?></h2>
    <div class="relative overflow-x-auto shadow-md sm:rounded-lg">
        <div class="p-4 bg-white dark:bg-gray-900">
            <label for="table-search" class="sr-only">Search</label>
            <div class="relative mt-1">
                <div class="absolute inset-y-0 rtl:inset-r-0 start-0 flex items-center ps-3 pointer-events-none">
                    <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z" />
                    </svg>
                </div>
                <input type="text" id="table-search"
                    class="block p-3 pl-10 text-sm w-full text-gray-900 border border-gray-300 rounded-lg w-80 bg-gray-50 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                    placeholder="Vyhľadávanie dlhopisu...">
            </div>
        </div>
        <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
            <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                <tr>
                    <th scope="col" class="px-6 py-3">
                        ISIN
                    </th>
                    <th scope="col" class="px-6 py-3">
                        Názov dlhopisu
                    </th>
                    <th scope="col" class="px-6 py-3">
                        Skratka
                    </th>
                    <th scope="col" class="px-6 py-3">
                        Mena
                    </th>
                    <th scope="col" class="px-6 py-3">
                    </th>
                </tr>
            </thead>
            <tbody id="tableTbodyAll">
                <?php foreach ($dlhopisy as $dlhopis) { ?>
                    <tr hx-get="/dlhopisy/<?php echo $object; ?>/<?php echo $action; ?>/<?php echo $dlhopis["isincurrric"]; ?>"
                        hx-target="#pageContentMain" hx-replace-url="true"
                        class="bg-white border-b transition-all cursor-pointer dark:bg-gray-800 dark:border-gray-700 border-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600">
                        <th scope="row" class="px-6 py-4 font-bold text-gray-900 whitespace-nowrap dark:text-white">
                            <?php echo $dlhopis["isinreal"]; ?>
                        </th>
                        <th scope="row" class="px-6 py-4 font-bold text-gray-900 whssitespace-nowrap dark:text-white">
                            <?php echo $dlhopis["cpnaz"]; ?>
                        </th>
                        <td class="px-6 py-4">
                            <?php echo $dlhopis["cpnazskratka"]; ?>
                        </td>
                        <td class="px-6 py-4">
                            <?php echo $dlhopis["mena"]; ?>
                        </td>
                        <td class="px-6 py-4">
                            <button>
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                    fill="none" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="lucide lucide-log-in">
                                    <path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4" />
                                    <polyline points="10 17 15 12 10 7" />
                                    <line x1="15" x2="3" y1="12" y2="12" />
                                </svg>
                            </button>
                        </td>
                    </tr>
                <?php } ?>
            </tbody>
        </table>
    </div>
</section>
<script>
    (function () {
        let typingTimer;
        const doneTypingInterval = 600;

        document.getElementById('table-search').addEventListener('keyup', (e) => {
            clearTimeout(typingTimer);
            typingTimer = setTimeout(() => {
                htmx.ajax('POST', '/api/dlhopisy/filteredData', {
                    target: '#tableTbodyAll',
                    values: {
                        query: e.target.value,
                        action: '<?php echo $action; ?>',
                        object: '<?php echo $object; ?>'
                    }
                });
        }, doneTypingInterval);
    });

    document.getElementById('table-search').addEventListener('keydown', () => {
        clearTimeout(typingTimer);
    });
    }) ();
</script>
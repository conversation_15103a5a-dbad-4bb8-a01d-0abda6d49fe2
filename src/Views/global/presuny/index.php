<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

if ($_SESSION["mode"]["mode"] === "global") {
    $id_fond = 0;
    $tableToJoin = "fondsbu";
    $subjektidStr = "fb.fondid";
} elseif ($_SESSION["mode"]["mode"] === "admin") {
    $id_fond = 1;
    $tableToJoin = "spravcabu";
    $subjektidStr = "fb.spravcaid";
} else {
    $id_fond = $_SESSION["client"]["fondid"];
    $tableToJoin = "fondsbu";
    $subjektidStr = "fb.fondid";
}

$avaiableCurrencies = Connection::getDataFromDatabase("SELECT count(banka) as banka, mena
              from (select distinct mena, banka, cub
                    from $tableToJoin
                   ) a
              group by mena
              having count(banka) > 1", defaultDB)[1];
$meny = [];

foreach ($avaiableCurrencies as $key => $value) {
    $mena = $value['mena'];
    array_push($meny, "'$mena'");
}

$meny = implode(', ', $meny);

$query = "SELECT DISTINCT fb.cub                                                                               AS cisloUctu,
                SUM(COALESCE(CASE WHEN md_d = 0 THEN 1 WHEN md_d = 1 THEN -1 ELSE 0 END * pocet, 0)) AS suma,
                fb.mena
FROM majetoktoday mt
         INNER JOIN $tableToJoin fb ON mt.mena = fb.mena AND mt.ucetaktiva = fb.cub AND mt.subjektid = $subjektidStr
WHERE mt.uctovnykod = 221110 AND fb.mena in ($meny)
GROUP BY fb.mena, fb.cub";

$ucty = Connection::getDataFromDatabase($query, defaultDB)[1];

$cakajucePresuny = Connection::getDataFromDatabase("SELECT kpp_z.dealid                                   AS dealid,
       p.cislozmluvy                                  AS z_portfolia,
       (SELECT cislozmluvy
        FROM portfolio
        WHERE fondid = kpp_na.subjektid)              AS na_portfolio,
       kpp_z.suma                                     AS suma,
       kpp_z.mena                                     AS mena,
       TO_CHAR(kpp_z.datum_zauctovania, 'DD.MM.YYYY') AS datum_zauctovania,
       kpp_z.ucet                                     AS ucet_zdroj,
       kpp_na.ucet                                    AS ucet_ciel,
       kpp_na.subjektid                               AS na_id_fond,
       kpp_na.dealid                                  AS dealid_related
FROM konfirmaciapp kpp_z
         LEFT JOIN portfolio p ON p.fondid = kpp_z.subjektid
         JOIN konfirmaciapp kpp_na ON kpp_na.dealid = kpp_z.dealid_related
WHERE kpp_z.subjektid = 1
  AND kpp_z.logactivityid IN (1, 2)
  AND kpp_z.logactivityid <> 3
  AND kpp_z.druhobchodu = 'presun';
", defaultDB)[1];

?>
<div id="toast" class="absolute top-0 right-5"></div>
<form class="py-3 sm:py-5 px-5 mb-5" hx-post="/api/presuny/vykonat" hx-target="#toast">
    <div class="mx-auto max-w-screen-2xl">
        <h2 class="scroll-m-20 border-b mb-4 pb-2 dark:text-gray-200 text-3xl font-semibold tracking-tight first:mt-0">
            Presun prostriedkov
        </h2>
    </div>
    <div class="flex gap-8 mb-6 px-10 items-center">
        <div class="bg-red-100 dark:bg-gray-900 dark:text-gray-100 rounded-lg p-5 w-full">
            <input type="hidden" id="ucty" value='<?php echo json_encode($ucty); ?>' />
            <p class="font-bold text-xl mb-4">Z účtu</p>
            <input type="hidden" name="poolid" id="poolid" value="" />
            <input type="hidden" name="accountFrom" id="accountFrom" value="<?php echo $ucty[0]["cislouctu"]; ?>" />
            <input type="hidden" name="poolData" id="poolData" />
            <input type="hidden" name="accountFromAmount" id="accountFromAmount"
                value="<?php echo $ucty[0]["suma"]; ?>" />
            <input type="hidden" name="accountFromCurr" id="accountFromCurr" value="<?php echo $ucty[0]["mena"]; ?>" />
            <select id="fromAcc" required onchange="fromAccChange(this)"
                class="bg-gray-50 border border-gray-300 text-gray-900 dark:text-gray-200 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                <?php foreach ($ucty as $key => $item) {
                    $dataArr = json_encode(
                        [
                            'cub' => $item["cislouctu"],
                            'suma' => $item["suma"],
                            'mena' => $item["mena"]
                        ]
                    );
                    ?>
                    <option value='<?php echo $dataArr; ?>'>
                        <?php echo $item["cislouctu"] . " " . $item["mena"]; ?>
                    </option>
                <?php } ?>
            </select>
            <section id="hej" class="items-center mt-4 w-full inline-flex justify-between">
                <p class="font-semibold">Stav účtu:</p>
                <strong id="sumaStav"
                    class="p-1 dark:text-gray-800 rounded-md <?php echo $ucty[0]["suma"] > 0 ? "bg-green-400" : "bg-red-400" ?> px-3 text-white shadow-md">
                    <?php echo $ucty[0]["suma"] . " " . $ucty[0]["mena"]; ?>
                </strong>
            </section>
        </div>
        <section class="w-full inline-flex py-4 flex-col justify-center items-center">
            <div class="mb-6 w-full">
                <label for="amount"
                    class="block mb-2 text-sm font-medium text-gray-900 dark:text-gray-200 dark:text-white">Čiastka</label>
                <input type="text" id="amount" name="amount"
                    class="block w-full text-xl font-bold p-4 text-gray-900 dark:text-gray-200 border border-gray-300 rounded-lg bg-gray-50 text-base focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
            </div>
            <?php if ($_SESSION["mode"]["mode"] != "admin" && !isset($_SESSION["client"])) { ?>
                <button type="button" id="poolingBtn" data-modal-target="pooling-modal" data-modal-toggle="pooling-modal"
                    class="text-white w-full flex items-center justify-center gap-2 transition-all text-xl bg-gray-800 focus:outline-none focus:ring-4 focus:ring-gray-300 font-extrabold rounded-lg text-sm px-5 py-2.5"><svg
                        xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="lucide lucide-database">
                        <ellipse cx="12" cy="5" rx="9" ry="3" />
                        <path d="M3 5V19A9 3 0 0 0 21 19V5" />
                        <path d="M3 12A9 3 0 0 0 21 12" />
                    </svg>Pool</button>
            <?php } ?>
        </section>
        <div class="bg-green-100 dark:bg-gray-900 dark:text-gray-100 rounded-lg p-5 w-full">
            <p class="font-bold text-xl mb-4">Na účet</p>
            <section id="thisisit">
                <input type="hidden" name="accountTo" id="accountTo" value="<?php echo $ucty[1]["cislouctu"]; ?>" />
                <input type="hidden" name="accountToAmount" id="accountToAmount"
                    value="<?php echo $ucty[1]["suma"]; ?>" />
                <input type="hidden" name="accountToCurr" id="accountToCurr" value="<?php echo $ucty[1]["mena"]; ?>" />
                <select id="toAcc" name="toAcc" onchange="toAccChange(this)"
                    class="bg-gray-50 border border-gray-300 text-gray-900 dark:text-gray-200 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                    <?php foreach ($ucty as $key => $item) {
                        if ($key === 0) {
                            continue;
                        }
                        $dataArr = json_encode(
                            [
                                'cub' => $item["cislouctu"],
                                'suma' => $item["suma"],
                                'mena' => $item["mena"]
                            ]
                        );
                        ?>
                        <option value='<?php echo $dataArr; ?>'>
                            <?php echo $item["cislouctu"] . " " . $item["mena"]; ?>
                        </option>
                    <?php } ?>
                </select>
                <section id="hej" class="items-center mt-4 w-full inline-flex justify-between">
                    <p class="font-semibold">Stav účtu:</p>
                    <strong id="sumaTo"
                        class="p-1 rounded-md dark:text-gray-800 <?php echo $ucty[1]["suma"] > 0 ? "bg-green-400" : "bg-red-400" ?> px-3 text-white shadow-md">
                        <?php echo $ucty[1]["suma"] . " " . $ucty[1]["mena"]; ?>
                    </strong>
                </section>
            </section>
        </div>
    </div>
    <button type="submit"
        class="w-full bg-blue-500 hover:bg-blue-800 transition-all p-3 text-white text-2xl font-extrabold rounded-xl flex justify-center items-center"
        <h5 class="mb-2 text-3xl font-extrabold tracking-tight">Potvrdiť</h5>
    </button>
</form>
<?php if (sizeof($cakajucePresuny) > 0) { ?>
    <div class="w-full px-5">
        <h2 class="scroll-m-20 border-b mb-4 pb-2 dark:text-gray-200 text-3xl font-semibold tracking-tight first:mt-0">
            Čakajúce presuny
        </h2>
        <div class="overflow-x-auto px-5">
            <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
                <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                    <tr>
                        <th scope="col" class="px-4 py-3">Dátum</th>
                        <th scope="col" class="px-4 py-3">Portfólio</th>
                        <th scope="col" class="px-4 py-3">Z účtu</th>
                        <th scope="col" class="px-4 py-3">Na účet</th>
                        <th scope="col" class="px-4 py-3">Suma</th>
                        <th scope="col" class="px-4 py-3">Mena</th>
                        <th scope="col" class="px-4 py-3"></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($cakajucePresuny as $key => $item) { ?>
                        <tr
                            class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b dark:border-gray-700">
                            <td class="px-4 py-2">
                                <span
                                    class="text-sm text-gray-900 dark:text-gray-200 font-bold px-2 py-0.5 rounded"><?php echo $item['datum_zauctovania'] ?></span>
                            </td>
                            <td class="px-4 py-2">
                                <span
                                    class="text-sm text-gray-900 dark:text-gray-200 font-bold px-2 py-0.5 rounded"><?php echo $item['z_portfolia'] ?></span>
                            </td>
                            <td class="px-4 py-2">
                                <span
                                    class="text-sm text-gray-900 dark:text-gray-200 font-bold px-2 py-0.5 rounded"><?php echo $item['ucet_zdroj'] ?></span>
                            </td>
                            <td class="px-4 py-2">
                                <span
                                    class="text-sm text-gray-900 dark:text-gray-200 font-bold px-2 py-0.5 rounded"><?php echo $item['ucet_ciel'] ?></span>
                            </td>
                            <td class="px-4 py-2">
                                <span
                                    class="text-sm text-gray-900 dark:text-gray-200 font-bold px-2 py-0.5 rounded"><?php echo $item['suma'] ?></span>
                            </td>
                            <td class="px-4 py-2">
                                <span
                                    class="text-sm text-gray-900 dark:text-gray-200 font-bold px-2 py-0.5 rounded"><?php echo $item['mena'] ?></span>
                            </td>
                            <td class="px-4 py-2 flex items-center gap-2">
                                <form hx-post="/api/presuny/potvrdPrevod" hx-target="#toast" class="potvrdPrevodForm">
                                    <input type="hidden" name="dealid" value="<?php echo $item['dealid'] ?>" />
                                    <input type="hidden" name="ucet_zdroj" value="<?php echo $item['ucet_zdroj'] ?>" />
                                    <input type="hidden" name="ucet_ciel" value="<?php echo $item['ucet_ciel'] ?>" />
                                    <input type="hidden" name="mena" value="<?php echo $item['mena'] ?>" />
                                    <input type="hidden" name="suma" value="<?php echo $item['suma'] ?>" />
                                    <button type="submit" class="font-medium cursor-pointer hover:bg-green-500 hover:text-gray-800 transition-all p-1 rounded-md 
                                        text-blue-600 underline dark:text-blue-500 hover:no-underline">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                            fill="none" stroke="#ffffff" stroke-width="2" stroke-linecap="round"
                                            stroke-linejoin="round" class="lucide potvrdIcon lucide-check-icon lucide-check">
                                            <path d="M20 6 9 17l-5-5" />
                                        </svg>
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                            fill="none" stroke="#ffffff" stroke-width="2" stroke-linecap="round"
                                            style="display: none;" stroke-linejoin="round"
                                            class="lucide potvrdSpinner lucide-loader-circle-icon text-red-500 animate-spin lucide-loader-circle">
                                            <path d="M21 12a9 9 0 1 1-6.219-8.56" />
                                        </svg>
                                    </button>
                                </form>
                                <form class="deletePrevodForm" hx-post="/api/presuny/deletePrevod" hx-target="#toast">
                                    <input type="hidden" name="dealid" value="<?php echo $item['dealid'] ?>" />
                                    <button type="submit" class="font-medium cursor-pointer hover:bg-red-500 hover:text-gray-800 transition-all p-1 rounded-md 
                                        text-blue-600 underline dark:text-blue-500 hover:no-underline">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                            fill="none" stroke="#ffffff" stroke-width="2" stroke-linecap="round"
                                            stroke-linejoin="round"
                                            class="lucide deleteIcon lucide-shredder-icon lucide-shredder">
                                            <path d="M10 22v-5" />
                                            <path d="M14 19v-2" />
                                            <path d="M14 2v4a2 2 0 0 0 2 2h4" />
                                            <path d="M18 20v-3" />
                                            <path d="M2 13h20" />
                                            <path d="M20 13V7l-5-5H6a2 2 0 0 0-2 2v9" />
                                            <path d="M6 20v-3" />
                                        </svg>
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                            fill="none" stroke="#ffffff" stroke-width="2" stroke-linecap="round"
                                            style="display: none;" stroke-linejoin="round"
                                            class="lucide deleteSpinner lucide-loader-circle-icon text-red-500 animate-spin lucide-loader-circle">
                                            <path d="M21 12a9 9 0 1 1-6.219-8.56" />
                                        </svg>
                                    </button>
                                </form>
                            </td>
                        </tr>
                    <?php } ?>
                </tbody>
            </table>
        </div>
    </div>
<?php }
if (!isset($_SESSION["client"]) && $_SESSION["mode"]["mode"] != "admin") {
    include "src/Components/pooling/modal.php";
} ?>
<script>
    let dataArr = JSON.parse(document.getElementById("ucty").value);

    $("#poolingBtn").on("click", (e) => {
        htmx.ajax('POST', "/api/investicne-zamery/generatePool?typ=PP&action=PP",
            {
                target: '#modalWrapperko',
                values: {
                    "mena": $("#accountFromCurr").val(),
                    "mena2": $("#accountToCurr").val(),
                    "accountFrom": $("#accountFrom").val(),
                    "accountTo": $("#accountTo").val(),
                }
            });
    });

    $(".deletePrevodForm").on("submit", (e) => {
        e.preventDefault();
        e.currentTarget.querySelector(".deleteIcon").style.display = "none";
        e.currentTarget.querySelector(".deleteSpinner").style.display = "inline-flex";
    });
    $(".potvrdPrevodForm").on("submit", (e) => {
        e.preventDefault();
        e.currentTarget.querySelector(".potvrdIcon").style.display = "none";
        e.currentTarget.querySelector(".potvrdSpinner").style.display = "inline-flex";
    });

    function fromAccChange(e) {
        const value = JSON.parse(e.value);
        $("#accountFrom").val(value.cub);
        $("#accountFromAmount").val(value.suma);
        $("#accountFromCurr").val(value.mena);
        if (value.suma > 0) {
            $("#sumaStav").removeClass("bg-red-400");
            $("#sumaStav").removeClass("bg-green-400");
            $("#sumaStav").addClass("bg-green-400");
        } else {
            $("#sumaStav").removeClass("bg-red-400");
            $("#sumaStav").removeClass("bg-green-400");
            $("#sumaStav").addClass("bg-red-400");
        }
        console.log({ "data": JSON.stringify(dataArr), "current": value.cub, "mena": value.mena })
        $("#sumaStav").text(value.suma.toLocaleString('de-DE', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) + " " + value.mena);
        htmx.ajax('POST', "/presun/toAcc", { target: '#thisisit', values: { "data": JSON.stringify(dataArr), "current": value.cub, "mena": value.mena } });
    }

    function toAccChange(e) {
        const value = JSON.parse(e.value);
        $("#accountTo").val(value.cub);
        $("#accountToAmount").val(value.suma);
        $("#accountToCurr").val(value.mena);
        if (value.suma > 0) {
            $("#sumaTo").removeClass("bg-red-400");
            $("#sumaTo").removeClass("bg-green-400");
            $("#sumaTo").addClass("bg-green-400");
        } else {
            $("#sumaTo").removeClass("bg-red-400");
            $("#sumaTo").removeClass("bg-green-400");
            $("#sumaTo").addClass("bg-red-400");
        }
        $("#sumaTo").text(value.suma.toLocaleString('de-DE', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) + " " + value.mena);
    }

    function recalculateTVValues(e) {
        const formData = new FormData(e.currentTarget);
        const clientBuys = formData.get("poolSumaFrom");
        const clientSells = formData.get("poolSumaTo");
        const poolid = formData.get("poolidModal");
        const poolData = formData.get("poolDetailData");

        document.getElementById("amount").value = parseFloat(clientBuys) + parseFloat(clientSells);
        document.getElementById("poolid").value = poolid;
        document.getElementById("poolData").value = poolData;
    }

    document.getElementById("poolDetailForm").addEventListener("submit", (e) => {
        e.preventDefault();
        recalculateTVValues(e);
    });
</script>
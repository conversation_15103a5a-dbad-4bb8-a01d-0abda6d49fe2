<?php
$data = json_decode($_POST["data"], true);
$current = $_POST["current"];
$mena = $_POST["mena"];
?>
<select id="toAcc" name="toAcc" onchange="toAccChange(this)"
    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
    <?php foreach ($data as $key => $item) {
        if ($current === $item["cislouctu"]) {
            continue;
        }
        if ($mena === $item["mena"]) {
            $dataArr = json_encode(
                [
                    'cub' => $item["cislouctu"],
                    'suma' => $item["suma"],
                    'mena' => $item["mena"]
                ]
            ); ?>
            <option value='<?php echo $dataArr; ?>'>
                <?php echo $item["cislouctu"] . " " . $item["mena"]; ?>
            </option>
        <?php }
        ?>

        <?php
    } ?>
</select>
<?php foreach ($data as $key => $item) {
    if ($current === $item["cislouctu"]) {
        continue;
    }
    if ($mena === $item["mena"]) { ?>
        <section id="hej" class="items-center mt-4 w-full inline-flex justify-between">
            <p class="font-semibold">Stav účtu:</p>
            <strong id="sumaTo"
                class="p-1 rounded-md dark:text-gray-800 <?php echo $item["suma"] > 0 ? "bg-green-400" : "bg-red-400" ?> px-3 text-white shadow-md">
                <?php echo $item["suma"] . " " . $item["mena"]; ?>
            </strong>
        </section>
        <?php
        break;
    }
} ?>
<?php foreach ($data as $key => $item) {
    if ($current === $item["cislouctu"]) {
        continue;
    }
    if ($mena === $item["mena"]) { ?>
        <input type="hidden" name="accountTo" id="accountTo" value="<?php echo $item["cislouctu"]; ?>" />
        <input type="hidden" name="accountToAmount" id="accountToAmount" value="<?php echo $item["suma"]; ?>" />
        <input type="hidden" name="accountToCurr" id="accountToCurr" value="<?php echo $item["mena"]; ?>" />
        <?php
        break;
    }
} ?>
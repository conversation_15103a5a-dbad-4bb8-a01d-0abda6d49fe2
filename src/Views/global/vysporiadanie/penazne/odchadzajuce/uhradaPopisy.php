<?php
$uhrady = [
    [
        "id" => 301,
        "uhrada" => "Úhrada zriadenia KTV",
        "popis" => "zriadeniektv",
        "cuquery" => "select cutd as ucet from konfirmaciaktv where dealid=" . $dealid,
        "nazov" => "KTV",
        "nazquery" => ""
    ],
    [
        "id" => 302,
        "uhrada" => "Úhrada nákupu dlhopisu",
        "popis" => "nákup dlhopisu",
        "cuquery" => "select cuprotistrana as ucet from rekonfirmaciacp where dealid=" . $dealid . " and tranza=" . $tranza,
        "nazov" => "",
        "nazquery" => "select cpnaz<PERSON>ratka as nazov from dbequity de,konfirmaciacp kcp where kcp.isin=de.isin and kcp.dealid=" . $dealid
    ],
    [
        "id" => 303,
        "uhrada" => "Úhrada výberu",
        "popis" => "redemácia",
        "cuquery" => "select cu as ucet from ziadostreemisia where ziadostreemisiaid=" . $dealid,
        "nazov" => "",
        "nazquery" => "select (meno||' '||prieznaz) as nazov from podielnik where podielnikid in (select podielnikid from ziadostreemisia where ziadostreemisiaid=" . $dealid . ")"
    ],
    [
        "id" => 306,
        "uhrada" => "Presun",
        "popis" => "presun",
        "cuquery" => "select cub as ucet from fondsbu fb, ziadostpresun zp where ziadostpresunid = " . $dealid . " and fb.fondid=zp.fondidnew and fb.mena = zp.mena",
        "nazov" => "",
        "nazquery" => "select fondnameshort as nazov from fonds f, ziadostpresun zp where ziadostpresunid = " . $dealid . " and f.fondid=zp.fondidnew"
    ],
    [
        "id" => 307,
        "uhrada" => "Výplata výnosu",
        "popis" => "dividenda",
        "cuquery" => "select cudividenda as ucet from podielnikemisiaattributes where podielnikid=" . $dealid . "and fondid = $id_fond",
        "nazov" => "",
        "nazquery" => "select ((CASE WHEN fpo=0 THEN meno||' ' ELSE '' END)||prieznaz) as nazov from podielnik where podielnikid=" . $dealid
    ],
    [
        "id" => 310,
        "uhrada" => "Úhrada vstupného poplatku",
        "popis" => "vstupný poplatok",
        "cuquery" => "select ucetaktiva as ucet from majetokcesta where dealid=" . $dealid . " and in_out=1 and subjektid=1 and destinacia like 'fondsfeesstore'",
        "nazov" => "",
        "nazquery" => ""
    ],
    [
        "id" => 311,
        "uhrada" => "Úhrada prestupného poplatku",
        "popis" => "prestupný poplatok",
        "cuquery" => "select ucetaktiva as ucet from majetokcesta where dealid=" . $dealid . " and in_out=1 and subjektid=1 and destinacia like 'fondsfeesstore'",
        "nazov" => "",
        "nazquery" => ""
    ],
    [
        "id" => 312,
        "uhrada" => "Úhrada výstupného poplatku",
        "popis" => "výstupný poplatok",
        "cuquery" => "select ucetaktiva as ucet from majetokcesta where dealid=" . $dealid . " and in_out=1 and subjektid=1 and destinacia like 'fondsfeesstore'",
        "nazov" => "",
        "nazquery" => ""
    ],
    [
        "id" => 313,
        "uhrada" => "Úhrada správcovskej odplaty",
        "popis" => "odplata správcu",
        "cuquery" => "select ucetaktiva as ucet from majetokcesta where dealid=" . $dealid . " and in_out=1 and subjektid=1 and destinacia like 'fondsfeesstore'",
        "nazov" => "",
        "nazquery" => ""
    ],
    [
        "id" => 314,
        "uhrada" => "Úhrada odplaty za riadenie",
        "popis" => "odplata depozitára",
        "cuquery" => "select ucetaktiva as ucet from majetokcesta where dealid=" . $dealid . " and in_out=1 and subjektid=1 and destinacia like 'fondsfeesstore'",
        "nazov" => "",
        "nazquery" => ""
    ],
    [
        "id" => 315,
        "uhrada" => "Úhrada vrátenia vstupného poplatku",
        "popis" => "vrátenie poplatku",
        "cuquery" => "select cuistina as ucet from podielnikemisiaattributes where podielnikid = (select podielnikid from kontopodielnik where obratid = " . $dealid . " ) and fondid = $id_fond",
        "nazov" => "",
        "nazquery" => "select ((CASE WHEN fpo=0 THEN meno||' ' ELSE '' END)||prieznaz) as nazov from podielnik where podielnikid = (select podielnikid from kontopodielnik where obratid = " . $dealid . ")"
    ],
    [
        "id" => 316,
        "uhrada" => "Úhrada vrátenia vkladu",
        "popis" => "vrátenie vkladu",
        "cuquery" => "select cuistina as ucet from podielnikemisiaattributes where podielnikid = (select podielnikid from kontopodielnik where obratid = " . $sparovanie . " ) and fondid = $id_fond",
        "nazov" => "",
        "nazquery" => "select ((CASE WHEN fpo=0 THEN meno||' ' ELSE '' END)||prieznaz) as nazov from podielnik where podielnikid = (select podielnikid from kontopodielnik where obratid = " . $sparovanie . ")"
    ],
    [
        "id" => 320,
        "uhrada" => "Úhrada za spotrebované nákupy",
        "popis" => "nakupy",
        "cuquery" => "",
        "nazov" => "",
        "nazquery" => ""
    ],
    [
        "id" => 321,
        "uhrada" => "Úhrada za služby",
        "popis" => "sluzby",
        "cuquery" => "",
        "nazov" => "",
        "nazquery" => ""
    ],
    [
        "id" => 322,
        "uhrada" => "Úhrada za osobné náklady",
        "popis" => "osobne naklady",
        "cuquery" => "",
        "nazov" => "",
        "nazquery" => ""
    ],
    [
        "id" => 323,
        "uhrada" => "Úhrada daní a poplatkov",
        "popis" => "dane poplatky",
        "cuquery" => "",
        "nazov" => "",
        "nazquery" => ""
    ],
    [
        "id" => 324,
        "uhrada" => "Úhrada prevádzkových nákladov",
        "popis" => "prevadzkove naklady",
        "cuquery" => "",
        "nazov" => "",
        "nazquery" => ""
    ],
    [
        "id" => 325,
        "uhrada" => "Úhrada finančných nákladov",
        "popis" => "financne naklady",
        "cuquery" => "",
        "nazov" => "",
        "nazquery" => ""
    ],
    [
        "id" => 326,
        "uhrada" => "Úhrada ostatných nákladov",
        "popis" => "ostatne naklady",
        "cuquery" => "",
        "nazov" => "",
        "nazquery" => ""
    ],
    [
        "id" => 331,
        "uhrada" => "Úhrada nákupu akcie",
        "popis" => "nákup akcie",
        "cuquery" => "select cuprotistrana as ucet from rekonfirmaciacp where dealid=" . $dealid . " and tranza=" . $tranza,
        "nazov" => "",
        "nazquery" => ""
    ],
    [
        "id" => 332,
        "uhrada" => "Úhrada nákupu fondu",
        "popis" => "nákup fondu",
        "cuquery" => "select cuprotistrana as ucet from rekonfirmaciacp where dealid=" . $dealid . " and tranza=" . $tranza,
        "nazov" => "",
        "nazquery" => ""
    ],
    [
        "id" => 333,
        "uhrada" => "Úhrada nákupu certifikátu",
        "popis" => "nákup certifikátu",
        "cuquery" => "",
        "nazov" => "",
        "nazquery" => ""
    ],
    [
        "id" => 334,
        "uhrada" => "Úhrada konverzie",
        "popis" => "konverzia",
        "cuquery" => "select cukredit as ucet from konverzia where dealid=" . $dealid,
        "nazov" => "",
        "nazquery" => ""
    ],
    [
        "id" => 335,
        "uhrada" => "Konverzia NDF",
        "popis" => "Konverzia NDF",
        "cuquery" => "",
        "nazov" => "Forward",
        "nazquery" => ""
    ],
    [
        "id" => 340,
        "uhrada" => "Zráková daň",
        "popis" => "Zráková daň",
        "cuquery" => "select cub as ucet from spravcabu sb, fonds f where sb.spravcaid=1 and sb.mena = f.refmena and f.fondid = $id_fond and rezervny = 0",
        "nazov" => "",
        "nazquery" => "select nazovshort as nazov from spravca where spravcaid=1"
    ],
    [
        "id" => 343,
        "uhrada" => "Úhrada - Poplatok z výnosu",
        "popis" => "poplatok vynos",
        "cuquery" => "select ucetaktiva as ucet from majetokcesta where dealid=" . $dealid . " and in_out=1 and subjektid=1 and destinacia like 'fondsfeesstore'",
        "nazov" => "",
        "nazquery" => ""
    ],
    [
        "id" => 344,
        "uhrada" => "Úhrada - Výpis o stave portfolia",
        "popis" => "poplatok mimoriadny vypis",
        "cuquery" => "select ucetaktiva as ucet from majetokcesta where dealid=" . $dealid . " and in_out=1 and subjektid=1 and destinacia like 'fondsfeesstore'",
        "nazov" => "",
        "nazquery" => ""
    ],
    [
        "id" => 345,
        "uhrada" => "Úhrada - Poplatok za operáciu na peňažnom trhu",
        "popis" => "poplatok operacia pen.trh",
        "cuquery" => "select ucetaktiva as ucet from majetokcesta where dealid=" . $dealid . " and in_out=1 and subjektid=1 and destinacia like 'fondsfeesstore'",
        "nazov" => "",
        "nazquery" => ""
    ],
    [
        "id" => 346,
        "uhrada" => "Úhrada - Poplatok za menové konverzie",
        "popis" => "poplatok menová konverzia",
        "cuquery" => "select ucetaktiva as ucet from majetokcesta where dealid=" . $dealid . " and in_out=1 and subjektid=1 and destinacia like 'fondsfeesstore'",
        "nazov" => "",
        "nazquery" => ""
    ],
    [
        "id" => 347,
        "uhrada" => "Úhrada - Poplatok za obstaranie transakcie s CP",
        "popis" => "poplatok obstaranie transakcie cp",
        "cuquery" => "select ucetaktiva as ucet from majetokcesta where dealid=" . $dealid . " and in_out=1 and subjektid=1 and destinacia like 'fondsfeesstore'",
        "nazov" => "",
        "nazquery" => ""
    ],
    [
        "id" => 348,
        "uhrada" => "Úhrada - Poplatok za vysporiadanie transakcie s CP",
        "popis" => "poplatok vysporiadanie transakcie cp",
        "cuquery" => "select ucetaktiva as ucet from majetokcesta where dealid=" . $dealid . " and in_out=1 and subjektid=1 and destinacia like 'fondsfeesstore'",
        "nazov" => "",
        "nazquery" => ""
    ],
    [
        "id" => 349,
        "uhrada" => "Nešpecifikovaný záväzok",
        "popis" => "nespecif. zav.",
        "cuquery" => "",
        "nazov" => "",
        "nazquery" => ""
    ],
    [
        "id" => 350,
        "uhrada" => "Poplatok - spoločná uhrada",
        "popis" => "poplatok - spolocna uhrada",
        "cuquery" => "",
        "nazov" => "",
        "nazquery" => ""
    ],
    [
        "id" => 360,
        "uhrada" => "Majetkové účty CP",
        "popis" => "majetkové účty cp",
        "cuquery" => "",
        "nazov" => "",
        "nazquery" => ""
    ]
];
?>
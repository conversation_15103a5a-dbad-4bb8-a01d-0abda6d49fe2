<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
$last = $_POST["last"];
$ks = Connection::getDataFromDatabase("select * from konstantnysymbol", defaultDB)[1];
?>
<th scope="row" class="px-2 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
    <input type="text" id="suma<?php echo $last; ?>" name="suma"
        class="block w-full p-2 text-gray-900 firstInput border border-gray-300 rounded-lg bg-gray-50 text-xs focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
</th>
<td class="px-2 py-4">
    <input type="text" id="vs<?php echo $last; ?>" name="vs"
        class="block w-full p-2 text-gray-900 border border-gray-300 rounded-lg bg-gray-50 text-xs focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
</td>
<td class="px-2 py-4">
    <select id="ks<?php echo $last; ?>" name="ks"
        class="bg-gray-50 dark:bg-gray-700 border border-gray-300 text-gray-900 dark:text-gray-100 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
        <?php foreach ($ks as $key => $value) { ?>
            <option value="<?php echo $value["cislo"]; ?>">
                <?php echo $value["cislo"]; ?>
            </option>
        <?php } ?>
    </select>
</td>
<td class="px-2 py-4">
    <input type="text" id="ss<?php echo $last; ?>" name="ss"
        class="block w-full p-2 text-gray-900 border border-gray-300 rounded-lg bg-gray-50 text-xs focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
</td>
<td class="px-2 py-4">
    <input type="text" id="ucetpartnera<?php echo $last; ?>" name="ucetpartnera"
        class="block w-full p-2 text-gray-900 border border-gray-300 rounded-lg bg-gray-50 text-xs focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
</td>
<td class="px-2 py-4">
    <input type="text" id="nazovpartnera<?php echo $last; ?>" name="nazovpartnera"
        class="block w-full p-2 text-gray-900 border border-gray-300 rounded-lg bg-gray-50 text-xs focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
</td>
<td class="px-2 py-4">
    <input type="date" id="splatnost<?php echo $last; ?>" name="splatnost"
        class="block w-full p-2 text-gray-900 border border-gray-300 rounded-lg bg-gray-50 text-xs focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
</td>
<td class="px-2 py-4">
    <select name="forma" id="forma<?php echo $last; ?>"
        class="bg-gray-50 dark:bg-gray-700 border border-gray-300 text-gray-900 dark:text-gray-100 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
        <option value="1">Hotovosť</option>
        <option value="0" selected>Prevod</option>
    </select>
</td>
<td class="px-2 py-4">
    <button id="<?php echo $last; ?>" class="p-1 rounded-lg focus:ring-4 lastInput focus:ring-green-300"
        type="button">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
            class="lucide lucide-copy-plus">
            <line x1="15" x2="15" y1="12" y2="18" />
            <line x1="12" x2="18" y1="15" y2="15" />
            <rect width="14" height="14" x="8" y="8" rx="2" ry="2" />
            <path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2" />
        </svg>
    </button>
</td>
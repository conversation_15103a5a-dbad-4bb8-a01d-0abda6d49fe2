<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";


$cub = $_GET["ucet"];
$mena = $_GET["mena"];

if (isset($_SESSION["client"])) {
    $id_fond = $_SESSION["client"]["fondid"];
} elseif ($_SESSION["mode"]["mode"] === "admin") {
    $id_fond = 1;
} else {
    $id_fond = 0;
}


$allowcheckneg = "0";
$allowcheckpoz = "0";

$today = Connection::getDataFromDatabase("SELECT max(datum) as today FROM today", defaultDB)[1][0]["today"];

if ($id_fond !== 1) {
    if (in_array("1", $_SESSION["user"]["permissions"]) || in_array("4", $_SESSION["user"]["permissions"])) {
        $allowcheckneg = "1";
    }
    if (in_array("2", $_SESSION["user"]["permissions"])) {
        $allowcheckpoz = "1";
    }
}

if ($id_fond === 1) {
    if (in_array("1", $_SESSION["user"]["permissions"]) || in_array("2", $_SESSION["user"]["permissions"]) || in_array("4", $_SESSION["user"]["permissions"])) {
        $allowcheckneg = "1";
        $allowcheckpoz = "1";
    }
}

$pripraveneUhradyQuery = "SELECT
			uhrada.*,
			datesplatnost as datum
		from
			uhrada
		where
			subjektid = $id_fond and
			cub like '$cub' and
			logactivityid in(4,5,6,7,8)
		order by id desc
";
$ocakavaneUhradyQuery = "SELECT
 			mc.*,
			cestatill as datum,
			cestafrom as datumfrom,
			dealid as curr_dealid
		from
			majetokcesta mc
		where
			subjektid=$id_fond and
			in_out=0 and
			ucetaktiva like '%$cub%' and
			tranza not in (select tranza from uhrada where uhrada.dealid=mc.dealid and uhrada.subjektid = mc.subjektid and logactivityid > 3)
	";
$ocakavaneUhradyQuery .= " order by mc.cestafrom";
$ocakavaneUhrady = Connection::getDataFromDatabase($ocakavaneUhradyQuery, defaultDB)[1];
$pripraveneUhrady = Connection::getDataFromDatabase($pripraveneUhradyQuery, defaultDB)[1];

?>
<section class="py-4 relative px-5 sm:py-5" style="margin-bottom: 8rem">
    <div id="toast" class="z-30 top-26 fixed right-5"></div>
    <div class="mx-auto">
        <h2
            class="scroll-m-20 flex items-center gap-4 border-b mb-4 pb-2 text-3xl font-semibold dark:text-gray-100 tracking-tight first:mt-0">
            Príkaz na úhradu pre účet <span
                class="bg-green-100 text-green-800 text-md font-bold me-2 px-2 py-0.5 rounded-md dark:bg-gray-700 dark:text-green-400 border border-green-400"><?php echo $cub; ?></span>
        </h2>
    </div>
    <div id="ocakavaneUhradyWrapper" class="relative overflow-x-auto shadow-md sm:rounded-lg">
        <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400 overflow-hidden">
            <caption
                class="p-5 text-lg font-semibold text-left rtl:text-right text-gray-900 dark:text-gray-100 bg-white dark:text-white dark:bg-gray-800">
                <section class="flex items-center justify-between">
                    <div>
                        <span>Očakávané úhrady</span>
                        <p class="mt-1 text-sm font-normal text-gray-500 dark:text-gray-400">List všetkých
                            očakávaných
                            úhrad.</p>
                    </div>
                </section>
            </caption>
            <thead
                class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:bg-gray-700 dark:text-gray-400">
                <tr>
                    <th scope="col" class="px-6 py-3">
                        Popis
                    </th>
                    <th scope="col" class="px-6 py-3">
                        Kodobratu
                    </th>
                    <th scope="col" class="px-6 py-3">
                        Aktívum
                    </th>
                    <th scope="col" class="px-6 py-3">
                        Spárovanie
                    </th>
                    <th scope="col" class="px-6 py-3">
                        Suma
                    </th>
                    <th scope="col" class="px-6 py-3">
                        Mena
                    </th>
                </tr>
            </thead>
            <?php if (sizeof($ocakavaneUhrady) > 0) { ?>
                <tbody id="ocakavaneUhradyTBody" class="relative">

                    <?php foreach ($ocakavaneUhrady as $key => $uhrada) {
                        $dealid = $uhrada["dealid"];
                        $tranza = $uhrada["tranza"];
                        include "uhradaPopisy.php";
                        $popis = $uhrada["popis"];
                        $mena = $uhrada["mena"];

                        $cuquery = "";
                        $nazov = "";
                        $nazquery = "";
                        $idUhrady = "";
                        $popisUhrady = "";



                        foreach ($uhrady as $item) {
                            if ("'" . $item["popis"] . "'" == $popis) {
                                $cuquery = $item["cuquery"];
                                $nazov = $item["nazov"];
                                $nazquery = $item["nazquery"];
                                $idUhrady = $item["id"];
                                $popisUhrady = $item["popis"];
                            }
                            if ($item["popis"] == $popis) {
                                $cuquery = $item["cuquery"];
                                $nazov = $item["nazov"];
                                $nazquery = $item["nazquery"];
                                $idUhrady = $item["id"];
                                $popisUhrady = $item["popis"];
                            }
                        }
                        //UCETPARTNERA
                        if ($cuquery != "") {
                            $ucet = Connection::getDataFromDatabase($cuquery, defaultDB)[1][0]["ucet"];
                        }
                        //NAZOVPARTNERA
                        if ($nazquery != "") {
                            $nazov = Connection::getDataFromDatabase($nazquery, defaultDB)[1][0]["nazov"];
                        } else {
                            $nazov = "";
                        }
                        if ($uhrada["id"] === 303 || $uhrada["id"] === 306)
                            $ss = $uhrada["dealid"];
                        else
                            $ss = "";

                        //NASTAVENIE PLATBY
                        $platba = 1;
                        if ($idUhrady === 303) {
                            $platba = Connection::getDataFromDatabase("select (1-forma) as platba from ziadostreemisia where ziadostreemisiaid = " . $uhrada["dealid"], defaultDB)[1][0]["platba"];
                        } else if ($popis == $popisUhrady && $idUhrady === 307 && $ucet == '') {
                            $platba = 0;
                        }

                        //NASTAVENIE VARIABILNEHO SYMBOLU
                        $vs = $uhrada["sparovanie"];
                        if ($idUhrady === 303) {
                            $vsQuery = "select podielnikid as vs from ziadostreemisia where ziadostreemisiaid=" . $dealid;
                        } else if ($idUhrady === 306) {
                            $vsQuery = "select podielnikid as vs from ziadostpresun where ziadostpresunid = " . $dealid;
                        }

                        //NASTAVENIE KONSTANTNEHO SYMBOLU PRE PRIKAZ NA UHRADU
                        $ks = "";
                        if ($idUhrady === 306) {
                            $tmp = $ss;
                            $ss = $vs;
                            $vs = $tmp;
                        }

                        $splatnost = $uhrada["datum"];
                        if ($idUhrady === 306) {
                            $splatnost = $uhrada["datumfrom"];
                        }
                        $kodobratu = $idUhrady;

                        /* pri ziadosti o presun redemaciu a storne poplatku - dodatocny popis*/
                        $vsQuery = "";
                        if ($idUhrady === 303) {
                            //REDEMÁCIA
                            $vsQuery = "SELECT zr.datetimeagreement||'<br>&nbsp;'||trim(prieznaz||' '||meno) as detail 
                                FROM ziadostreemisia zr, podielnik p
                                WHERE zr.podielnikid = p.podielnikid AND zr.ziadostreemisiaid = " . $dealid;
                        } else if ($idUhrady === 306) {
                            //PRESUN
                            $vsQuery = "SELECT z.datetimeagreement||'<br>&nbsp;'||trim(prieznaz||' '||meno) as detail 
                                FROM ziadostpresun z, podielnik p
                                WHERE z.podielnikid = p.podielnikid AND z.ziadostpresunid = " . $dealid;
                        } else if ($idUhrady === 315) {
                            //VRATENIE POPLATKU
                            $vsQuery = "SELECT kp.obratdatatimezauctovanie ||'<br>&nbsp;'||trim(prieznaz||' '||meno) as detail 
                                FROM klientskypodiel kp, podielnik p
                                WHERE kp.podielnikid = p.podielnikid AND kp.obratid = " . $dealid;
                        }
                        if ($vsQuery !== "") {
                            $detail = Connection::getDataFromDatabase($vsQuery, defaultDB)[1][0]["detail"];
                        } else {
                            $detail = "";
                        }

                        if ($uhrada["popis"] === "redemácia") {
                            $popis = "Výber";
                        } else if ($uhrada["popis"] === "odplata správcu") {
                            $popis = "odplata za riadenie";
                        } else if ($uhrada["popis"] === "odplata depozitára") {
                            $popis = "odplata za správu";
                        }
                        //CHECK THIS
                        $splatonost = "";
                        ?>
                        <tr id="uhrada<?php echo $key; ?>"
                            class="bg-white border-b dark:bg-gray-800 transition-all ocakavanaUhrada cursor-pointer hover:dark:bg-gray-600 dark:border-gray-700 border-gray-200">
                            <th scope="row"
                                class="px-6 py-2 font-medium text-gray-900 dark:text-gray-100 whitespace-nowrap dark:text-white">
                                <?php echo $popis ?>
                                <p><?php echo $popis; ?></p>
                            </th>
                            <td class="px-6 py-2">
                                <?php if ($detail === "") {
                                    echo $idUhrady;
                                } else {
                                    echo $detail;
                                } ?>
                            </td>
                            <td class="px-6 py-2">
                                <?php echo $dealid; ?>
                            </td>
                            <td class="px-6 py-2">
                                <?php echo $uhrada["sparovanie"]; ?>
                            </td>
                            <td class="px-6 py-2">
                                <?php echo number_format($uhrada["pocet"], 2, ".", " "); ?>
                            </td>
                            <td class="px-6 py-2">
                                <?php echo $uhrada["mena"]; ?>
                            </td>
                            <td class="hidden">
                                <form class="moveUhradaFormOriginal">
                                    <input type="hidden" name="uhradaid" value="<?php echo $uhrada["id"] ?>">
                                    <input type="hidden" name="suma" value="<?php echo $uhrada["pocet"] ?>">
                                    <input type="hidden" name="mena" value="<?php echo $uhrada["mena"] ?>" />
                                    <input type="hidden" name="vs" value="<?php echo $vs ?>">
                                    <input type="hidden" name="ucetpartnera" value="<?php echo $ucet ?>">
                                    <input type="hidden" name="kodobratu" value="<?php echo $kodobratu ?>">
                                    <input type="hidden" name="ss" value="<?php echo $ss ?>">
                                    <input type="hidden" name="ks" value="<?php echo $ks ?>">
                                    <input type="hidden" name="nazov" value="<?php echo substr($nazov, 1, 40) ?>">
                                    <input type="hidden" name="splatnost" value="<?php echo $splatnost ?>">
                                    <input type="hidden" name="dealid" value="<?php echo $dealid ?>">
                                    <input type="hidden" name="tranza" value="<?php echo $uhrada["tranza"] ?>">
                                    <input type="hidden" name="logactivityid" value="<?php echo $uhrada["logactivityid"] ?>" />
                                    <input type="hidden" name="logdatatimeactivity" value="<?php echo date("Y-m-d"); ?>" />
                                    <input type="hidden" name="platba" value="<?php echo $platba ?>">
                                    <input type="hidden" name="cub" value="<?php echo $cub ?>">
                                    <input type="submit" class="hidden" id="moveUhradaFormOriginalSubmit">
                                </form>
                            </td>
                        </tr>

                    <?php } ?>
                </tbody>
            <?php } else { ?>
                <tbody>
                    <tr id="waitingNoDataDiv"
                        class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 border-gray-200">
                        <td colspan="6"
                            class="px-6 py-4 text-center font-medium text-gray-900 dark:text-gray-100 whitespace-nowrap dark:text-white">
                            <div class="p-4 my-4 text-sm text-blue-800 rounded-lg bg-blue-50 w-full dark:bg-gray-700 dark:text-blue-400"
                                role="alert">
                                <span class="font-bold">Žiadne záznamy!</span> Podľa zadaných kritérii sme nenašli
                                žiadne
                                očakávané úhrady.
                            </div>
                        </td>
                    </tr>
                </tbody>
            <?php } ?>
        </table>
    </div>
    <div class="relative my-4 shadow-md sm:rounded-lg">
        <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400 dark:bg-gray-900">
            <caption
                class="p-5 text-lg font-semibold text-left rtl:text-right text-gray-900 dark:text-gray-100 bg-white dark:text-white dark:bg-gray-900">
                <section class="flex items-center justify-between">
                    <div>
                        <span>Pripravené úhrady</span>
                        <p class="mt-1 text-sm font-normal text-gray-500 dark:text-gray-400">List všetkých novo
                            vytvorených
                            úhrad.</p>
                    </div>
                    <section class="flex items-center gap-2">
                        <?php if ($id_fond != 0) { ?>
                            <button
                                class="p-2 mb-2 me-2 bg-gray-100 cursor-pointer transition-all hover:bg-gray-200 rounded-lg">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="lucide lucide-import">
                                    <path d="M12 3v12" />
                                    <path d="m8 11 4 4 4-4" />
                                    <path d="M8 5H4a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V7a2 2 0 0 0-2-2h-4" />
                                </svg>
                            </button>
                            <button type="button" data-modal-target="paymentModal" data-modal-toggle="paymentModal" class="text-white flex items-center gap-2 bg-gradient-to-r from-green-400 via-green-500 to-green-600 hover:bg-gradient-to-br 
                    focus:ring-4 focus:outline-none transition-all focus:ring-green-300 dark:focus:ring-green-800 shadow-lg shadow-green-500/50 dark:shadow-lg 
                    dark:shadow-green-800/80 font-bold rounded-lg text-sm p-2.5 text-center me-2 mb-2">Pridať
                                novú úhradu
                                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="lucide lucide-plus">
                                    <path d="M5 12h14" />
                                    <path d="M12 5v14" />
                                </svg>
                            </button>
                            <button type="button" data-modal-target="bulkModal" data-modal-toggle="bulkModal"
                                class="text-white flex items-center dark:hover:bg-gray-600 transition-all gap-2 bg-gradient-to-r from-cyan-400 via-cyan-500 to-cyan-600 hover:bg-gradient-to-br focus:ring-4 focus:outline-none focus:ring-cyan-300 
                            dark:focus:ring-cyan-800 shadow-lg shadow-cyan-500/50 dark:shadow-lg 
                            dark:shadow-cyan-800/80 font-bold rounded-lg text-sm px-5 py-2.5 text-center me-2 mb-2">Pridať
                                úhrady (hromadne)
                                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="lucide lucide-list-plus">
                                    <path d="M11 12H3" />
                                    <path d="M16 6H3" />
                                    <path d="M16 18H3" />
                                    <path d="M18 9v6" />
                                    <path d="M21 12h-6" />
                                </svg>
                            </button>
                        <?php } ?>
                    </section>
                </section>
            </caption>
            <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-900 dark:text-gray-400">
                <tr>
                    <th scope="col" class="px-6 py-3">
                        Suma
                    </th>
                    <th scope="col" class="px-6 py-3">
                        VS
                    </th>
                    <th scope="col" class="px-6 py-3">
                        KS
                    </th>
                    <th scope="col" class="px-6 py-3">
                        ŠS
                    </th>
                    <th scope="col" class="px-6 py-3">
                        Účet partnera
                    </th>
                    <th scope="col" class="px-6 py-3">
                        Názvov partnera
                    </th>
                    <th scope="col" class="px-6 py-3">
                        Splatnosť
                    </th>
                    <th scope="col" class="px-6 py-3">
                        Forma
                    </th>
                    <th scope="col" class="px-6 py-3">
                        Status
                    </th>
                    <th scope="col" class="px-6 py-3">
                    </th>
                </tr>
            </thead>
            <?php if (sizeof($pripraveneUhrady) > 0) { ?>
                <tbody id="readyPaymentsTbody" class="dark:bg-gray-900 dark:text-gray-400">
                    <?php foreach ($pripraveneUhrady as $uhrada) {
                        $kodobratu = $uhrada["kodobratu"];
                        switch ($uhrada["logactivityid"]) {
                            case 3:
                            case 4:
                                $verified = "wait";
                                $enabled = ($allowcheckneg == "1") ? 1 : 0;
                                break;
                            case 6:
                                $verified = "yes";
                                $enabled = ($allowcheckpoz == "1") ? 1 : 0;
                                break;
                            case 7:
                                $verified = "no";
                                $enabled = ($allowcheckneg == "1") ? 1 : 0;
                                break;
                            case 8:
                                $verified = "auto";
                                $enabled = ($allowcheckneg == "1") ? 1 : 0;
                                break;
                        }
                        ?>
                        <tr id="uhrada<?php echo $uhrada["id"]; ?>"
                            class="bg-white border-b dark:bg-gray-900 dark:border-gray-700 border-gray-200">
                            <th scope="row"
                                class="px-6 py-4 font-medium text-gray-900 dark:text-gray-100 whitespace-nowrap dark:text-white">
                                <?php echo number_format($uhrada["suma"], 2, ".", " "); ?>
                            </th>
                            <td class="px-6 py-4">
                                <?php echo $uhrada["vs"] ?>
                            </td>
                            <td class="px-6 py-4">
                                <?php echo $uhrada["ks"]; ?>
                            </td>
                            <td class="px-6 py-4">
                                <?php echo $uhrada["ss"]; ?>
                            </td>
                            <td class="px-6 py-4">
                                <?php echo $uhrada["cubpartnera"]; ?>
                            </td>
                            <td class="px-6 py-4">
                                <?php echo $uhrada["nazpartnera"]; ?>
                            </td>
                            <td class="px-6 py-4">
                                <?php echo $uhrada["datum"]; ?>
                            </td>
                            <td class="px-6 py-4">
                                <?php echo $uhrada["forma"] === 1 ? "Hotovosť" : "Prevod"; ?>
                            </td>
                            <td class="px-6 py-5 flex justify-center items-center">
                                <?php if ($verified === "auto") { ?>
                                    <div class="group cursor-help">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                            fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                            stroke-linejoin="round" class="lucide text-yellow-500 lucide-cloud-cog">
                                            <circle cx="12" cy="17" r="3" />
                                            <path d="M4.2 15.1A7 7 0 1 1 15.71 8h1.79a4.5 4.5 0 0 1 2.5 8.2" />
                                            <path d="m15.7 18.4-.9-.3" />
                                            <path d="m9.2 15.9-.9-.3" />
                                            <path d="m10.6 20.7.3-.9" />
                                            <path d="m13.1 14.2.3-.9" />
                                            <path d="m13.6 20.7-.4-1" />
                                            <path d="m10.8 14.3-.4-1" />
                                            <path d="m8.3 18.6 1-.4" />
                                            <path d="m14.7 15.8 1-.4" />
                                        </svg>
                                        <div role="tooltip"
                                            class="absolute z-10 px-3 py-2 text-sm font-medium text-white transition-opacity 
                                                duration-300 bg-gray-900 rounded-lg shadow-xs hidden group-hover:inline-block tooltip dark:bg-gray-700">
                                            Automatická verifikácia príkazu na úhradu
                                            <div class="tooltip-arrow" data-popper-arrow></div>
                                        </div>
                                    </div>

                                <?php } ?>
                                <?php if ($verified === "wait") { ?>
                                    <div class="group cursor-help">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                            fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                            stroke-linejoin="round" class="lucide text-blue-400 lucide-clock-8">
                                            <circle cx="12" cy="12" r="10" />
                                            <polyline points="12 6 12 12 8 14" />
                                        </svg>
                                        <div role="tooltip"
                                            class="absolute z-10 px-3 py-2 text-sm font-medium text-white transition-opacity 
                                                duration-300 bg-gray-900 rounded-lg shadow-xs hidden group-hover:inline-block tooltip dark:bg-gray-700">
                                            <?php if ($uhrada["logactivityid"] == 4) {
                                                echo "Odoslanie príkazu na úhradu na verifikáciu depozitárovi";
                                            } else {
                                                echo "Prevzatie príkazu na úhradu depozitárom";
                                            } ?>
                                            <div class="tooltip-arrow" data-popper-arrow></div>
                                        </div>
                                    </div>
                                <?php } elseif ($verified === "yes") { ?>
                                    <div class="group cursor-help">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                            fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                            stroke-linejoin="round" class="lucide text-green-500 lucide-circle-check-big">
                                            <path d="M21.801 10A10 10 0 1 1 17 3.335" />
                                            <path d="m9 11 3 3L22 4" />
                                        </svg>
                                        <div role="tooltip"
                                            class="absolute z-10 px-3 py-2 text-sm font-medium text-white transition-opacity 
                                                duration-300 bg-gray-900 rounded-lg shadow-xs hidden group-hover:inline-block tooltip dark:bg-gray-700">
                                            Pozitívna verifikácia príkazu na úhradu depozitárom
                                            <div class="tooltip-arrow" data-popper-arrow></div>
                                        </div>
                                    </div>
                                <?php } elseif ($verified === "no") { ?>
                                    <div class="group cursor-help">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                            fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                            stroke-linejoin="round" class="lucide text-red-500 lucide-ban">
                                            <circle cx="12" cy="12" r="10" />
                                            <path d="m4.9 4.9 14.2 14.2" />
                                        </svg>
                                        <div role="tooltip"
                                            class="absolute z-10 px-3 py-2 text-sm font-medium text-white transition-opacity 
                                                duration-300 bg-gray-900 rounded-lg shadow-xs hidden group-hover:inline-block tooltip dark:bg-gray-700">
                                            Negatívna verifikácia príkazu na úhradu depozitárom
                                            <div class="tooltip-arrow" data-popper-arrow></div>
                                        </div>
                                    </div>
                                <?php } else { ?>
                                    <div class="group cursor-help">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                            fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                            stroke-linejoin="round" class="lucide lucide-badge-plus">
                                            <path
                                                d="M3.85 8.62a4 4 0 0 1 4.78-4.77 4 4 0 0 1 6.74 0 4 4 0 0 1 4.78 4.78 4 4 0 0 1 0 6.74 4 4 0 0 1-4.77 4.78 4 4 0 0 1-6.75 0 4 4 0 0 1-4.78-4.77 4 4 0 0 1 0-6.76Z" />
                                            <line x1="12" x2="12" y1="8" y2="16" />
                                            <line x1="8" x2="16" y1="12" y2="12" />
                                        </svg>
                                        <div role="tooltip"
                                            class="absolute z-10 px-3 py-2 text-sm font-medium text-white transition-opacity 
                                                duration-300 bg-gray-900 rounded-lg shadow-xs hidden group-hover:inline-block tooltip dark:bg-gray-700">
                                            Novo vytvorená úhrada
                                            <div class="tooltip-arrow" data-popper-arrow></div>
                                        </div>
                                    </div>
                                <?php } ?>
                            </td>
                            <td class="px-6 py-4 text-right relative overflow-visible">
                                <section class="flex gap-2 items-center">
                                    <form hx-post="/src/Controllers/global/vysporiadanie/majetkove/enableEditMode.php"
                                        hx-target="#uhrada<?php echo $uhrada["id"]; ?>">
                                        <input type="hidden" name="activityid" value="<?php echo $uhrada["logactivityid"] ?>" />
                                        <input type="hidden" name="kodobratu" value="<?php echo $uhrada["kodobratu"] ?>" />
                                        <input type="hidden" name="logdatatimeactivity"
                                            value="<?php echo $uhrada["logdatatimeactivity"] ?>" />
                                        <input type="hidden" name="uhradaid" value="<?php echo $uhrada["id"] ?>" />
                                        <input type="hidden" name="suma" value="<?php echo $uhrada["suma"] ?>" />
                                        <input type="hidden" name="vs" value="<?php echo $uhrada["vs"] ?>" />
                                        <input type="hidden" name="ks" value="<?php echo $uhrada["ks"] ?>" />
                                        <input type="hidden" name="ss" value="<?php echo $uhrada["ss"] ?>" />
                                        <input type="hidden" name="ucetpartnera" value="<?php echo $uhrada["cubpartnera"] ?>" />
                                        <input type="hidden" name="nazovpartnera"
                                            value="<?php echo $uhrada["nazpartnera"] ?>" />
                                        <input type="hidden" name="splatnost" value="<?php echo $uhrada["datum"] ?>" />
                                        <input type="hidden" name="forma" value="<?php echo $uhrada["forma"] ?>" />
                                        <input type="hidden" name="dealid" value="<?php echo $uhrada["dealid"] ?>" />
                                        <input type="hidden" name="cub" value="<?php echo $uhrada["cub"] ?>" />
                                        <button type="submit"
                                            class="p-1 rounded-lg hover:bg-gray-300 hover:text-gray-700 transition-all cursor-pointer">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                                stroke-linejoin="round" class="lucide lucide-pencil">
                                                <path
                                                    d="M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z" />
                                                <path d="m15 5 4 4" />
                                            </svg>
                                        </button>
                                    </form>
                                    <form class="mb-0 deleteUhradaFromList">
                                        <input type="hidden" name="uhradaid" value="<?php echo $uhrada["id"] ?>" />
                                        <button type="submit"
                                            class="p-1 rounded-lg hover:bg-red-300 hover:text-red-800 transition-all cursor-pointer">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                                stroke-linejoin="round" class="lucide deleteIcon text-red-500 lucide-trash">
                                                <path d="M3 6h18" />
                                                <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6" />
                                                <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2" />
                                            </svg>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                                stroke-linejoin="round"
                                                class="lucide deleteSpinner text-red-500 animate-spin lucide-loader-circle"
                                                style="display: none;">
                                                <path d="M21 12a9 9 0 1 1-6.219-8.56" />
                                            </svg>
                                        </button>
                                    </form>
                                    <form hx-post="/src/Controllers/global/vysporiadanie/majetkove/pripraveneUhradySubmit.php"
                                        hx-target="#toast">
                                        <input type="hidden" name="activityid" value="<?php echo $uhrada["logactivityid"] ?>" />
                                        <input type="hidden" name="logdatatimeactivity"
                                            value="<?php echo $uhrada["logdatatimeactivity"] ?>" />
                                        <input type="hidden" name="uhradaid" value="<?php echo $uhrada["id"] ?>" />
                                        <input type="hidden" name="suma" value="<?php echo $uhrada["suma"] ?>" />
                                        <input type="hidden" name="vs" value="<?php echo $uhrada["vs"] ?>" />
                                        <input type="hidden" name="ks" value="<?php echo $uhrada["ks"] ?>" />
                                        <input type="hidden" name="ss" value="<?php echo $uhrada["ss"] ?>" />
                                        <input type="hidden" name="tranza" value="<?php echo $uhrada["tranza"] ?>" />
                                        <input type="hidden" name="ucetpartnera" value="<?php echo $uhrada["cubpartnera"] ?>" />
                                        <input type="hidden" name="nazovpartnera"
                                            value="<?php echo $uhrada["nazpartnera"] ?>" />
                                        <input type="hidden" name="splatnost" value="<?php echo $uhrada["datum"] ?>" />
                                        <input type="hidden" name="forma" value="<?php echo $uhrada["forma"] ?>" />
                                        <input type="hidden" name="dealid" value="<?php echo $uhrada["dealid"] ?>" />
                                        <input type="hidden" name="cub" value="<?php echo $uhrada["cub"] ?>" />
                                        <input type="hidden" name="mena" value="<?php echo $mena ?>" />
                                        <div class="absolute flex gap-1 bg-gray-600 items-center zauctujConfirm hidden flex-col z-20 p-2 rounded-lg"
                                            style="left: -6rem; top: -4rem;">
                                            <?php
                                            $kodyObratu = Connection::getDataFromDatabase("SELECT * from kodobratu where " . ($kodobratu ? "kodobratu = $kodobratu" : "kodobratu IN (320,321,322,324,325,326)"), defaultDB)[1];
                                            ?>
                                            <select name="kodobratu" class="bg-gray-50 border border-gray-300
                                                text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block
                                                w-full p-1 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400
                                                dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"">
                                                <?php foreach ($kodyObratu as $key => $value) { ?>
                                                    <option value=" <?php echo $value["kodobratu"] ?>"><?php echo $value["popisobratu"] ?></option>
                                                <?php } ?>
                                            </select>
                                            <button type="submit" class="text-white bg-gradient-to-r zauctovatBtn from-blue-500 via-blue-600 to-blue-700 hover:bg-gradient-to-br focus:ring-4 focus:outline-none 
                        focus:ring-blue-300 dark:focus:ring-blue-800 shadow-lg shadow-blue-500/50 dark:shadow-lg dark:shadow-blue-800/80 rounded-lg text-xs text-center 
                        w-full p-1 mt-2 font-bold flex justify-center gap-2">
                                                <span>Zaúčtovať</span>
                                                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14"
                                                    viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                    stroke-linecap="round" stroke-linejoin="round" style="display: none"
                                                    class="lucide animate-spin lucide-loader-circle">
                                                    <path d="M21 12a9 9 0 1 1-6.219-8.56" />
                                                </svg>
                                            </button>
                                        </div>
                                        <button type="button"
                                            class="p-1 rounded-lg zauctujShow hover:bg-blue-300 hover:text-blue-800 transition-all cursor-pointer">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                                stroke-linejoin="round" class="lucide lucide-forward">
                                                <polyline points="15 17 20 12 15 7" />
                                                <path d="M4 18v-2a4 4 0 0 1 4-4h12" />
                                            </svg>
                                        </button>
                                    </form>
                                </section>
                            </td>
                        </tr>
                    <?php } ?>
                </tbody>
            <?php } else { ?>
                <tbody id="readyPaymentsTbody" class="dark:bg-gray-900 dark:text-gray-400">
                    <tr id="noDataDiv" class="bg-white border-b dark:bg-gray-900 dark:border-gray-700 border-gray-200">
                        <td colspan="10"
                            class="px-6 py-4 text-center font-medium text-gray-900 dark:text-gray-100 whitespace-nowrap dark:text-white">
                            <div class="p-4 my-4 text-sm text-blue-800 rounded-lg bg-blue-50 w-full dark:bg-gray-700 dark:text-blue-400"
                                role="alert">
                                <span class="font-bold">Žiadne úhrady ešte neboli vytvorené!</span> Pridajte úhrady
                                pomocou
                                tlačidiel vyššie.
                            </div>
                        </td>
                    </tr>
                </tbody>
            <?php } ?>
        </table>
    </div>
</section>
<div id="paymentModal" tabindex="-1" aria-hidden="true"
    class="fixed top-0 left-0 right-0 z-50 hidden w-full p-4 overflow-x-hidden overflow-y-auto md:inset-0 h-[calc(100%-1rem)] max-h-full">
    <div class="relative w-full max-w-2xl max-h-full">
        <div class="relative bg-white dark:bg-gray-800 rounded-lg shadow">
            <div class="flex items-start justify-between p-4 border-b rounded-t">
                <h3 class="text-xl font-semibold text-gray-900 dark:text-gray-100">
                    Pridať novú úhradu
                </h3>
                <button type="button"
                    class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 dark:text-gray-100 rounded-lg text-sm w-8 h-8 ml-auto inline-flex justify-center items-center"
                    data-modal-hide="paymentModal">
                    <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                        viewBox="0 0 14 14">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                    </svg>
                    <span class="sr-only">Close modal</span>
                </button>
            </div>
            <div class="p-6 space-y-6">
                <form id="paymentForm2" class="mb-0">
                    <input type="hidden" name="cub" value="<?php echo $cub; ?>" />
                    <input type="hidden" name="mena" value="<?php echo $mena; ?>" />
                    <div class="grid grid-cols-2 gap-4 mb-4">
                        <div>
                            <label for="amount"
                                class="block mb-2 text-sm font-medium text-gray-900 dark:text-gray-100">Suma</label>
                            <input type="text" id="amount" name="suma" required
                                class="bg-gray-50 dark:bg-gray-700 border border-gray-300 text-gray-900 dark:text-gray-100 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                                required>
                        </div>
                        <div>
                            <label for="vs"
                                class="block mb-2 text-sm font-medium text-gray-900 dark:text-gray-100">Variabilný
                                symbol</label>
                            <input type="text" id="vs" name="vs"
                                class="bg-gray-50 dark:bg-gray-700 border border-gray-300 text-gray-900 dark:text-gray-100 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                        </div>
                    </div>
                    <div class="grid grid-cols-2 gap-4 mb-4">
                        <div>
                            <label for="constantSymbol"
                                class="block mb-2 text-sm font-medium text-gray-900 dark:text-gray-100">Konštantný
                                symbol</label>
                            <select id="constantSymbol" name="ks"
                                class="bg-gray-50 dark:bg-gray-700 border border-gray-300 text-gray-900 dark:text-gray-100 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                                <option value="558" selected>558</option>
                            </select>
                        </div>
                        <div>
                            <label for="ss"
                                class="block mb-2 text-sm font-medium text-gray-900 dark:text-gray-100">Špecifický
                                symbol</label>
                            <input type="text" id="ss" name="ss"
                                class="bg-gray-50 dark:bg-gray-700 border border-gray-300 text-gray-900 dark:text-gray-100 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                        </div>
                    </div>
                    <div class="mb-4">
                        <label for="ucetpartnera"
                            class="block mb-2 text-sm font-medium text-gray-900 dark:text-gray-100">Účet
                            partnera</label>
                        <input type="text" id="ucetpartnera" name="ucetpartnera"
                            class="bg-gray-50 dark:bg-gray-700 border border-gray-300 text-gray-900 dark:text-gray-100 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                            required>
                    </div>
                    <div class="mb-4">
                        <label for="nazovpartnera"
                            class="block mb-2 text-sm font-medium text-gray-900 dark:text-gray-100">Názov
                            partnera</label>
                        <input type="text" id="nazovpartnera" name="nazovpartnera"
                            class="bg-gray-50 dark:bg-gray-700 border border-gray-300 text-gray-900 dark:text-gray-100 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                            required>
                    </div>
                    <div class="grid grid-cols-2 gap-4 mb-4">
                        <div>
                            <label for="splatnost"
                                class="block mb-2 text-sm font-medium text-gray-900 dark:text-gray-100">Dátum
                                splatnosti</label>
                            <input type="date" id="splatnost" name="splatnost" max="<?php echo $today; ?>"
                                class="bg-gray-50 dark:bg-gray-700 border border-gray-300 text-gray-900 dark:text-gray-100 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                        </div>
                        <div>
                            <label for="formType"
                                class="block mb-2 text-sm font-medium text-gray-900 dark:text-gray-100">Form
                                Type</label>
                            <select id="formType" name="forma"
                                class="bg-gray-50 dark:bg-gray-700 border border-gray-300 text-gray-900 dark:text-gray-100 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                                <option value="1">Hotovosť</option>
                                <option value="0" selected>Prevod</option>
                            </select>
                        </div>
                    </div>
                    <div class="flex items-center justify-end p-3 mt-6 rounded-lg dark:bg-gray-900">
                        <button type="submit"
                            class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 transition-all focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center">Pridať
                            úhradu</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<div id="bulkModal" tabindex="-1" aria-hidden="true" style="background: #121B2B7D; height:100vh"
    class="fixed top-0 left-0 right-0 z-50 hidden w-full p-4 overflow-x-hidden overflow-y-auto md:inset-0 h-[calc(100%-1rem)] max-h-full">
    <div class="relative w-full max-w-5xl max-h-full">
        <div class="relative bg-white dark:bg-gray-800 rounded-lg shadow">
            <div class="flex items-start justify-between p-4 border-b rounded-t">
                <h3 class="text-xl font-semibold text-gray-900 dark:text-gray-100">
                    Tvorba viacerých úhrad hromadne
                </h3>
                <button type="button"
                    class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 dark:text-gray-100 rounded-lg text-sm w-8 h-8 ml-auto inline-flex justify-center items-center"
                    data-modal-hide="bulkModal">
                    <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                        viewBox="0 0 14 14">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                    </svg>
                    <span class="sr-only">Close modal</span>
                </button>
            </div>
            <div id="resultik"></div>
            <div class="p-6 space-y-6">
                <form id="paymentForm" class="mb-0">
                    <input type="hidden" name="cub" value="<?php echo $cub; ?>" />
                    <div class="relative overflow-x-auto">
                        <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
                            <thead
                                class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                                <tr>
                                    <th scope="col" class="px-6 py-3">
                                        Suma
                                    </th>
                                    <th scope="col" class="px-6 py-3">
                                        VS
                                    </th>
                                    <th scope="col" class="px-6 py-3">
                                        KS
                                    </th>
                                    <th scope="col" class="px-6 py-3">
                                        ŠS
                                    </th>
                                    <th scope="col" class="px-6 py-3">
                                        Účet partnera
                                    </th>
                                    <th scope="col" class="px-6 py-3">
                                        Názvov partnera
                                    </th>
                                    <th scope="col" class="px-6 py-3">
                                        Splatnosť
                                    </th>
                                    <th scope="col" class="px-6 py-3">
                                        Forma
                                    </th>
                                    <th scope="col" class="px-6 py-3">
                                    </th>
                                </tr>
                            </thead>
                            <tbody id="bulkTbody">
                                <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 border-gray-200">
                                    <th scope="row"
                                        class="px-2 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                        <input type="text" id="suma0" name="suma"
                                            class="block w-full p-2 firstInput text-gray-900 border border-gray-300 rounded-lg bg-gray-50 text-xs focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                                    </th>
                                    <td class="px-2 py-4">
                                        <input type="text" id="vs0" name="vs"
                                            class="block w-full p-2 text-gray-900 border border-gray-300 rounded-lg bg-gray-50 text-xs focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                                    </td>
                                    <td class="px-2 py-4">
                                        <select id="ks0" name="ks"
                                            class="bg-gray-50 dark:bg-gray-700 border border-gray-300 text-gray-900 dark:text-gray-100 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                                            <option value="558" selected>558</option>
                                        </select>
                                    </td>
                                    <td class="px-2 py-4">
                                        <input type="text" id="ss0" name="ss"
                                            class="block w-full p-2 text-gray-900 border border-gray-300 rounded-lg bg-gray-50 text-xs focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                                    </td>
                                    <td class="px-2 py-4">
                                        <input type="text" id="ucetpartnera0" name="ucetpartnera"
                                            class="block w-full p-2 text-gray-900 border border-gray-300 rounded-lg bg-gray-50 text-xs focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                                    </td>
                                    <td class="px-2 py-4">
                                        <input type="text" id="nazovpartnera0" name="nazovpartnera"
                                            class="block w-full p-2 text-gray-900 border border-gray-300 rounded-lg bg-gray-50 text-xs focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                                    </td>
                                    <td class="px-2 py-4">
                                        <input type="date" id="splatnost0" name="splatnost"
                                            class="block w-full p-2 text-gray-900 border border-gray-300 rounded-lg bg-gray-50 text-xs focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                                    </td>
                                    <td class="px-2 py-4">
                                        <select name="forma" id="forma0"
                                            class="bg-gray-50 dark:bg-gray-700 border border-gray-300 text-gray-900 dark:text-gray-100 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                                            <option value="1">Hotovosť</option>
                                            <option value="0" selected>Prevod</option>
                                        </select>
                                    </td>
                                    <td class="px-2 py-4">
                                        <button id="0"
                                            class="p-1 rounded-lg focus:ring-4 lastInput focus:ring-green-300"
                                            type="button">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="lucide lucide-copy-plus">
                                                <line x1="15" x2="15" y1="12" y2="18" />
                                                <line x1="12" x2="18" y1="15" y2="15" />
                                                <rect width="14" height="14" x="8" y="8" rx="2" ry="2" />
                                                <path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2" />
                                            </svg>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="flex items-center justify-end p-3 mt-6 rounded-lg dark:bg-gray-900">
                        <button type="submit" data-modal-hide="bulkModal"
                            class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 transition-all focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center">Pridať
                            úhrady</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<script src="/src/assets/js/global/vysporiadanie/penazne.js"></script>
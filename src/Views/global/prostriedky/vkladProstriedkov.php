<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

if (isset($_SESSION["client"])) {
    $fondid = $_SESSION["client"]["fondid"];
} else if ($_SESSION["mode"]["mode"] === "admin") {
    $fondid = 1;
} else {
    $fondid = 0;
}

?>
<div id="toast"></div>
<form class="py-3 sm:py-5 mb-5" hx-post="/api/vklad-prostriedkov/vykonat" hx-target="#toast">
    <div class="px-5">
        <div class="flex items-end gap-6 dark:bg-gray-900 p-4 px-10">
            <div class="flex w-full flex-col gap-2">
                <label for="amount"
                    class="text-gray-700 dark:text-gray-100 font-medium whitespace-nowrap">Čiastka:</label>
                <input type="number" id="amount" name="amount" min="0.01" step="0.01"
                    class="px-3 py-2 border border-gray-300 rounded-md bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
            </div>
            <div class="flex w-full flex-col gap-2">
                <label for="cub" class="text-gray-700 dark:text-gray-100 font-medium whitespace-nowrap">Na
                    účet:</label>
                <?php
                if ($fondid == 1) {
                    $accounts = Connection::getDataFromDatabase("SELECT * FROM spravcabu WHERE spravcaid = $fondid", defaultDB)[1];
                } else {
                    $accounts = Connection::getDataFromDatabase("SELECT * FROM fondsbu WHERE fondid = $fondid", defaultDB)[1];
                }
                ?>
                <select id="cub" name="cub" class="px-3 py-2 border border-gray-300 rounded-md bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-100
                     focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    <?php foreach ($accounts as $account) { ?>
                        <option value="<?php echo $account["cub"]; ?>">
                            <?php echo $account["cub"] . " " . $account["mena"]; ?>
                        </option>
                    <?php } ?>
                </select>
            </div>
            <div class="flex w-full flex-col gap-2">
                <label for="cubpartnera" class="text-gray-700 dark:text-gray-100 font-medium whitespace-nowrap">Účet
                    partnera:</label>
                <input type="text" id="cubpartnera" name="cubpartnera" class="px-3 py-2 border border-gray-300 rounded-md bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-100 
                    focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
            </div>
            <button type="submit"
                class="px-6 w-full flex justify-center items-center gap-4 py-2 bg-blue-600 text-white font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200">
                Vložiť prostriedky
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide lucide-banknote-arrow-down-icon lucide-banknote-arrow-down">
                    <path d="M12 18H4a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5" />
                    <path d="m16 19 3 3 3-3" />
                    <path d="M18 12h.01" />
                    <path d="M19 16v6" />
                    <path d="M6 12h.01" />
                    <circle cx="12" cy="12" r="2" />
                </svg>
            </button>
        </div>
    </div>
</form>
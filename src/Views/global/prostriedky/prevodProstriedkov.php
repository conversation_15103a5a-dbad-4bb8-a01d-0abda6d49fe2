<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
require_once "/home/<USER>/www/src/Components/inputs/multiSelect/multiSelect.php";

if (isset($_SESSION["client"])) {
    $fondid = $_SESSION["client"]["fondid"];
} else if ($_SESSION["mode"]["mode"] === "admin") {
    header("Location: /");
    $fondid = 1;
} else {
    header("Location: /");
    $fondid = 0;
}
if ($fondid == 1) {
    $accounts = Connection::getDataFromDatabase("SELECT fb.cub AS cislo_uctu,
                            SUM(
                                    COALESCE(
                                            CASE
                                                WHEN md_d = 0 THEN 1
                                                WHEN md_d = 1 THEN -1
                                                ELSE 0
                                                END * pocet,
                                            0
                                    )
                            )      AS suma,
                            fb.mena
                        FROM spravcabu fb
                                LEFT JOIN
                            majetoktoday mt
                            ON mt.ucetaktiva = fb.cub
                                AND mt.mena = fb.mena
                                AND mt.subjektid = fb.spravcaid
                        WHERE mt.uctovnykod = 221110
                        AND fb.spravcaid = $fondid
                        GROUP BY fb.cub, fb.mena;
                        ", defaultDB)[1];
} else {
    $accounts = Connection::getDataFromDatabase("SELECT 
                            fb.cub AS cislo_uctu,
                            SUM(
                                COALESCE(
                                    CASE 
                                        WHEN mt.md_d = 0 THEN 1
                                        WHEN mt.md_d = 1 THEN -1
                                        ELSE 0
                                    END * mt.pocet,
                                    0
                                )
                            ) AS suma,
                            fb.mena
                        FROM 
                            fondsbu fb
                        LEFT JOIN 
                            majetoktoday mt 
                            ON mt.ucetaktiva = fb.cub 
                            AND mt.mena = fb.mena 
                            AND mt.subjektid = fb.fondid
                            AND mt.uctovnykod = 221110
                        WHERE 
                            fb.fondid = $fondid
                        GROUP BY 
                            fb.cub, fb.mena;
                        ", defaultDB)[1];
}

$cislazmluvy = Connection::getDataFromDatabase("SELECT cislozmluvy,fondid from portfolio order by cislozmluvy", defaultDB)[1];
$vybery = Connection::getDataFromDatabase("SELECT 
			kpp_z.dealid as dealid,
			p.cislozmluvy as z_portfolia,
			(select cislozmluvy from portfolio where fondid=kpp_na.subjektid) as na_portfolio,
			kpp_z.suma as suma,
			kpp_z.mena as mena,
			to_char(kpp_z.datum_prevodu,'dd.mm.yyyy') as datum_prevodu,
            kpp_z.datum_prevodu as datum_zauctovania,
			kpp_z.ucet as ucet_zdroj,
			kpp_na.ucet as ucet_ciel,
			kpp_na.subjektid as na_id_fond,
			kpp_na.dealid as dealid_related,
			(
			select 
			SUM(
                COALESCE(
                    CASE 
                    WHEN md_d = 0 THEN 1
                    WHEN md_d = 1 THEN -1
                    END * pocet,
                    0
                )
                ) AS suma
			from 
			majetoktoday mt 
			where 
				  uctovnykod=221110
				  and mt.mena = kpp_z.mena
				  and mt.ucetaktiva = kpp_z.ucet
				  and mt.subjektid = kpp_z.subjektid
			) as stav_uctu
		from
			konfirmaciapp kpp_z,
			konfirmaciapp kpp_na,
			portfolio p
		where
			kpp_z.subjektid = $fondid and
			p.fondid = kpp_z.subjektid and
			kpp_z.logactivityid in (1,2) and
			kpp_z.logactivityid <> 3 and
			kpp_na.dealid = kpp_z.dealid_related and
			kpp_z.druhobchodu = 'prevod'", defaultDB)[1];
?>
<div id="toast"></div>
<form class="py-3 sm:py-5 mb-5" hx-post="/api/prevod-prostriedkov/vykonat" hx-target="#toast">

    <div class="px-5">
        <input type="hidden" name="mena" id="menaValue" value="<?php echo $accounts[0]["mena"]; ?>" />
        <div class="flex items-end gap-6 bg-gray-200 dark:bg-gray-900 p-4 px-10">
            <div class="flex w-full flex-col gap-2">
                <label for="cub" class="text-gray-700 dark:text-gray-100 font-medium whitespace-nowrap">Z účtu:</label>
                <div class="flex items-center gap-2">
                    <select id="cub" name="cub" class="px-3 w-full py-2 border border-gray-300 rounded-md bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-100
                     focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <?php foreach ($accounts as $account) { ?>
                            <option value="<?php echo $account["cislo_uctu"]; ?>"
                                data-suma="<?php echo $account["suma"]; ?>" data-mena="<?php echo $account["mena"]; ?>">
                                <?php echo $account["cislo_uctu"] . " " . $account["mena"]; ?>
                            </option>
                        <?php } ?>
                    </select>
                    <div class="bg-green-400 font-medium rounded-md px-3 py-2">
                        <input type="hidden" id="dostupne" name="dostupne"
                            value="<?php echo $accounts[0]["suma"]; ?>" />
                        <span id="dostupneSpan"><?php echo $accounts[0]["suma"]; ?></span>
                    </div>
                </div>
            </div>
            <div class="flex w-1/2 flex-col gap-2">
                <label for="amount"
                    class="text-gray-700 dark:text-gray-100 font-medium whitespace-nowrap">Čiastka:</label>
                <input type="number" id="amount" name="amount" min="0.01" step="0.01"
                    max="<?php echo $accounts[0]["suma"]; ?>"
                    class="px-3 py-2 border border-gray-300 rounded-md bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
            </div>
            <div class="flex w-full flex-col gap-2">
                <?php echo MultiSelect::render("Vybrať portfolio", $cislazmluvy, "cislozmluvy", ["", "fondid"], "cislozmluvy"); ?>
            </div>
            <div class="flex w-full flex-col gap-2">
                <label for="datum" class="text-gray-700 dark:text-gray-100 font-medium whitespace-nowrap">Dátum
                    zadania</label>
                <input type="date" id="datum" name="datum" class="px-3 py-2 border border-gray-300 rounded-md bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-100 
                    focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
            </div>
        </div>
        <div class="p-4 px-10 bg-gray-200 dark:bg-gray-900">
            <button type="submit"
                class="px-6 w-full flex justify-center items-center gap-4 py-2 bg-blue-600 text-white font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200">
                Previesť prostriedky
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide lucide-arrow-left-right-icon lucide-arrow-left-right">
                    <path d="M8 3 4 7l4 4" />
                    <path d="M4 7h16" />
                    <path d="m16 21 4-4-4-4" />
                    <path d="M20 17H4" />
                </svg>
            </button>
        </div>
    </div>
</form>
<?php
foreach ($vybery as $key => $vyber) { ?>
    <div id="edit-modalek-<?php echo $key; ?>" tabindex="-1" aria-hidden="true"
        class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
        <div class="relative p-4 w-full max-w-2xl max-h-full">
            <form class="py-3 sm:py-5 mb-5 editForm">
                <div class="relative bg-white rounded-lg shadow-sm dark:bg-gray-700">
                    <input type="hidden" name="dealid" value="<?php echo $vyber["dealid"]; ?>" />
                    <div
                        class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600 border-gray-200">
                        <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                            Upraviť prevod [<?php echo $vyber["dealid"]; ?>]
                        </h3>
                        <button type="button"
                            class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
                            data-modal-hide="edit-modalek-<?php echo $key; ?>">
                            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                                viewBox="0 0 14 14">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                            </svg>
                            <span class="sr-only">Close modal</span>
                        </button>
                    </div>
                    <div class="p-4 md:p-5 space-y-4">
                        <div class="px-5">
                            <div class="flex flex-col gap-4">
                                <input type="hidden" name="mena" id="menaValue" />
                                <div class="flex flex-col items-end gap-6 p-4 px-10">
                                    <div class="flex w-full flex-col mb-2 gap-2">
                                        <label for="cub"
                                            class="text-gray-700 dark:text-gray-100 font-medium whitespace-nowrap">Z
                                            účtu</label>
                                        <div class="flex items-center gap-2">
                                            <select id="cub" name="cub" disabled="disabled" class="px-3 w-full py-2 border border-gray-300 rounded-md bg-white dark:bg-gray-600 text-gray-700 dark:text-gray-100
                     focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                                <?php foreach ($accounts as $account) { ?>
                                                    <option value="<?php echo $account["cislo_uctu"]; ?>"
                                                        data-suma="<?php echo $account["suma"]; ?>" <?php echo $account["cislo_uctu"] == $vyber["ucet_zdroj"] ? "selected" : ""; ?>>
                                                        <?php echo $account["cislo_uctu"] . " " . $account["mena"]; ?>
                                                    </option>
                                                <?php } ?>
                                            </select>
                                            <div class="bg-green-400 font-medium rounded-md px-3 py-2">
                                                <input type="hidden" id="dostupne" name="dostupne"
                                                    value="<?php echo $accounts[0]["suma"]; ?>" />
                                                <span id="dostupneSpan"><?php echo $accounts[0]["suma"]; ?></span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="flex w-full flex-col gap-2">
                                        <label for="amount"
                                            class="text-gray-700 dark:text-gray-100 font-medium whitespace-nowrap">Čiastka:</label>
                                        <input type="number" id="amount" name="amount" min="0.01" step="0.01"
                                            value="<?php echo number_format($vyber["suma"], 2); ?>" max="<?php foreach ($accounts as $account) {
                                                    if ($account["cislo_uctu"] == $vyber["ucet_zdroj"]) {
                                                        echo number_format($account["suma"], 2);
                                                    } else {
                                                        echo 0.00;
                                                    }
                                                } ?>"
                                            class="px-3 py-2 border border-gray-300 rounded-md bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    </div>
                                    <div class="flex w-full flex-col gap-2">
                                        <label for="amount"
                                            class="text-gray-700 dark:text-gray-100 font-medium whitespace-nowrap">Na
                                            portfólio:</label>
                                        <span
                                            class="font-bold text-xl dark:text-white"><?php echo $vyber["na_portfolio"]; ?></span>
                                        <input type="hidden" name="na_portfolio"
                                            value="<?php echo $vyber["na_portfolio"]; ?>" />
                                    </div>
                                    <div class="flex w-full flex-col gap-2">
                                        <label for="datum"
                                            class="text-gray-700 dark:text-gray-100 font-medium whitespace-nowrap">Dátum
                                            zadania</label>
                                        <input type="date" id="datum" name="datum"
                                            value="<?php echo $vyber["datum_zauctovania"]; ?>" class="px-3 py-2 border border-gray-300 rounded-md bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-100 
                    focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center p-4 md:p-5 border-t border-gray-200 rounded-b dark:border-gray-600">
                        <button data-modal-hide="edit-modalek-<?php echo $key; ?>" type="submit" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700
                             dark:focus:ring-blue-800">Upraviť</button>
                        <button data-modal-hide="edit-modalek-<?php echo $key; ?>" type="button"
                            class="py-2.5 px-5 ms-3 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4
                             focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700">Zrušiť</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
<?php } ?>
<div class="relative px-5 z-0 overflow-x-auto shadow-md sm:rounded-lg">
    <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
        <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
            <tr>
                <th scope="col" class="px-6 py-3">
                    Z portfólia
                </th>
                <th scope="col" class="px-6 py-3">
                    Na portfólio
                </th>
                <th scope="col" class="px-6 py-3">
                    Suma
                </th>
                <th scope="col" class="px-6 py-3">
                    Stav účtu
                </th>
                <th scope="col" class="px-6 py-3">
                    Mena
                </th>
                <th scope="col" class="px-6 py-3">
                    Dátum zadania
                </th>
                <th scope="col" class="px-6 py-3">
                    <span class="sr-only">Edit</span>
                </th>
            </tr>
        </thead>
        <tbody>
            <?php
            if (count($vybery) > 0) {
                foreach ($vybery as $vyber) { ?>
                    <tr
                        class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 border-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600">
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                            <?php echo $vyber["z_portfolia"]; ?>
                        </th>
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                            <?php echo $vyber["na_portfolio"]; ?>
                        </th>
                        <td class="px-6 py-4">
                            <?php echo number_format($vyber["suma"], 2, ".", " "); ?>
                        </td>
                        <td class="px-6 py-4">
                            <?php echo number_format($vyber["stav_uctu"], 2, ".", " "); ?>
                        </td>
                        <td class="px-6 py-4">
                            <?php echo $vyber["mena"]; ?>
                        </td>
                        <td class="px-6 py-4">
                            <?php echo $vyber["datum_prevodu"]; ?>
                        </td>
                        <td class="px-6 py-4 text-right flex items-center gap-2">
                            <button type="button" data-modal-target="edit-modalek-<?php echo $key; ?>"
                                data-modal-toggle="edit-modalek-<?php echo $key; ?>"
                                class="font-medium text-gray-800 dark:text-gray-100 p-1 rounded-lg hover:bg-gray-200 dark:hover:text-gray-700 transition-all cursor-pointer">
                                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none"
                                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                    class="lucide lucide-pencil-icon lucide-pencil">
                                    <path
                                        d="M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z" />
                                    <path d="m15 5 4 4" />
                                </svg>
                            </button>
                            <form class="actionForm" hx-post="/api/prevod-prostriedkov/delete" hx-target="#toast">
                                <input type="hidden" name="dealid" value="<?php echo $vyber["dealid"]; ?>" />
                                <input type="hidden" name="suma" value="<?php echo $vyber["suma"]; ?>" />
                                <input type="hidden" name="mena" value="<?php echo $vyber["mena"]; ?>" />
                                <input type="hidden" name="ucet_zdroj" value="<?php echo $vyber["ucet_zdroj"]; ?>" />
                                <input type="hidden" name="externy_ucet" id="externy_ucetDelete" value="" />
                                <input type="hidden" name="datum_prevodu" value="<?php echo $vyber["datum_zauctovania"]; ?>" />
                                <button type="submit"
                                    class="font-medium text-red-600 dark:text-red-500 p-1 rounded-lg hover:bg-red-200 dark:hover:bg-red-500 dark:hover:text-white transition-all cursor-pointer">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-circle-x-icon actionIcon lucide-circle-x">
                                        <circle cx="12" cy="12" r="10" />
                                        <path d="m15 9-6 6" />
                                        <path d="m9 9 6 6" />
                                    </svg>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        style="display: none;" stroke-linejoin="round"
                                        class="lucide lucide-loader-circle-icon actionSpinner animate-spin lucide-loader-circle">
                                        <path d="M21 12a9 9 0 1 1-6.219-8.56" />
                                    </svg>
                                </button>
                            </form>
                            <form class="actionForm" hx-post="/api/prevod-prostriedkov/potvrdit" hx-target="#toast">
                                <input type="hidden" name="dealid" value="<?php echo $vyber["dealid"]; ?>" />
                                <input type="hidden" name="dealid_related" value="<?php echo $vyber["dealid_related"]; ?>" />
                                <input type="hidden" name="ucet_ciel" value="<?php echo $vyber["ucet_ciel"]; ?>" />
                                <input type="hidden" name="ucet_zdroj" value="<?php echo $vyber["ucet_zdroj"]; ?>" />
                                <input type="hidden" name="stav_uctu" value="<?php echo $vyber["stav_uctu"]; ?>" />
                                <input type="hidden" name="z_portfolia" value="<?php echo $vyber["z_portfolia"]; ?>" />
                                <input type="hidden" name="na_portfolio" value="<?php echo $vyber["na_portfolio"]; ?>" />
                                <input type="hidden" name="na_id_fond" value="<?php echo $vyber["na_id_fond"]; ?>" />
                                <input type="hidden" name="suma" value="<?php echo $vyber["suma"]; ?>" />
                                <input type="hidden" name="mena" value="<?php echo $vyber["mena"]; ?>" />
                                <input type="hidden" name="ucet_zdroj" value="<?php echo $vyber["ucet_zdroj"]; ?>" />
                                <input type="hidden" name="datum_prevodu" value="<?php echo $vyber["datum_zauctovania"]; ?>" />
                                <button type="submit"
                                    class="font-medium text-graz-600 dark:text-gray-200 p-1 rounded-lg hover:bg-blue-200 dark:hover:bg-blue-500 dark:hover:text-white transition-all cursor-pointer">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round"
                                        class="lucide lucide-send-horizontal-icon actionIcon lucide-send-horizontal">
                                        <path
                                            d="M3.714 3.048a.498.498 0 0 0-.683.627l2.843 7.627a2 2 0 0 1 0 1.396l-2.842 7.627a.498.498 0 0 0 .682.627l18-8.5a.5.5 0 0 0 0-.904z" />
                                        <path d="M6 12h16" />
                                    </svg>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        style="display: none;" stroke-linejoin="round"
                                        class="lucide lucide-loader-circle-icon actionSpinner animate-spin lucide-loader-circle">
                                        <path d="M21 12a9 9 0 1 1-6.219-8.56" />
                                    </svg>
                                </button>
                            </form>
                        </td>
                    </tr>
                <?php }
            } else { ?>
                <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 border-gray-200">
                    <th colspan="6" scope="row"
                        class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                        <div class="p-4 text-sm text-blue-800 rounded-lg bg-blue-100 dark:bg-gray-800 dark:text-blue-200"
                            role="alert">
                            <span class="font-medium">Momentálne nie sú zadané žiadne prevody!</span>
                        </div>
                    </th>
                </tr>
            <?php } ?>
        </tbody>
    </table>
</div>
<script>
    $("#cub").change(() => {
        $("#dostupne").val($("#cub option:selected").data("suma"));
        $("#dostupneSpan").text($("#cub option:selected").data("suma"));
        $("#amount").attr("max", $("#cub option:selected").data("suma"));
        $("#menaValue").val($("#cub option:selected").data("mena"));
    });

    $("#amount").change((e) => {
        const dostupne = parseFloat($("#dostupne").val());
        const amount = parseFloat(e.target.value);
        if (amount > dostupne) {
            $("#amount").val($("#dostupne").val());
        }
        if (amount < 0 || amount == "") {
            $("#amount").val(0);
        }
    });

    $(".actionForm").on("submit", (e) => {
        e.currentTarget.querySelector(".actionIcon").style.display = "none";
        e.currentTarget.querySelector(".actionSpinner").style.display = "inline-flex";
    });

    function updateExternyUcet(input) {
        document.querySelector("#externy_ucet").value = input.value;
    }

    $(".editForm").on("submit", (e) => {
        e.preventDefault();
        const formData = new FormData(e.currentTarget);
        console.log(formData);

        // Convert FormData to object for HTMX
        const formValues = {};
        for (let [key, value] of formData.entries()) {
            formValues[key] = value;
        }

        htmx.ajax('POST', `/api/prevod-prostriedkov/upravit`, {
            target: "#toast",
            values: formValues
        });
    });
</script>
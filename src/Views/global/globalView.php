<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
echo "Hromadný";
/*
$fondNames = [];

$id_fond = 0;

//CHECK IF LOGIN IS PERMITTEDs
$loginDisabled = Connection::getDataFromDatabase("SELECT * FROM nologin", defaultDB)[1];
if (sizeof($loginDisabled) > 0) {
    $loginDisabledError = "Prihlásenie je zablokované administrátorom!";
}

$fonds = Connection::getDataFromDatabase("SELECT fondid as id, fondnameshort as nazov FROM fonds UNION
	SELECT spravcaid as id, nazovshort as nazov FROM spravca
	order by id", defaultDB)[1];

if (sizeof($fonds) > 0) {
    foreach ($fonds as $key => $value) {
        $fondNames[$value['id']] = $value['nazov'];
    }
}

$cislazmluvy = [];

$klienti = Connection::getDataFromDatabase("SELECT p.*, po.podielnikid, po.meno, po.prieznaz FROM portfolio p
JOIN podielnik po ON po.podielnikid = p.podielnikid", defaultDB)[1];

foreach ($klienti as $key => $value) {
    $cislazmluvy[$value['fondid']] = [
        "meno" => $value['meno'],
        "prieznaz" => $value['prieznaz'],
        "podielnikid" => $value['podielnikid'],
        "cislozmluvy" => $value['cislozmluvy'],
        "fondid" => $value['fondid']
    ];
}

require_once "/home/<USER>/www/src/Controllers/uzavierka/check.class.php";

$kontrola = new Check();
include_once "/home/<USER>/www/src/Controllers/uzavierka/detectors.class.php";

if ($id_fond != 0) {
    $kontrola->setSubjektid($id_fond);
}

$problemy = $kontrola->checkAll();
?>
<section class="py-3 sm:py-5 mb-1 px-4 relative">
    <div id="toast" class="absolute right-5 top-0 z-10 w-96"></div>
    <div class="mx-auto">
        <section class="inline-flex justify-between w-full border-b pb-3">
            <h2 class="scroll-m-20 dark:text-gray-50 text-3xl font-semibold">
                Prehľad problémov
            </h2>
        </section>
    </div>
    <div class="relative overflow-x-auto shadow-md sm:rounded-lg">
        <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
            <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                <tr>
                    <th scope="col" class="px-6 py-3">
                        Problém
                    </th>
                    <th scope="col" class="px-6 py-3">
                        Popis
                    </th>
                    <th scope="col" class="px-6 py-3">
                        Klient
                    </th>
                    <th scope="col" class="px-6 py-3">
                        Akcia
                    </th>
                </tr>
            </thead>
            <tbody>
                <?php
                foreach ($problemy as $key => $value) {
                    $level = $value->getSeriousness();
                    ?>
                    <tr class="border-b text-sm border-gray-200">
                        <th scope="row" class="px-3 py-2 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                            <div
                                class="py-1.5 px-4 rounded-lg <?php echo $level === 0 ? "text-blue-50" : ($level === 1 ? "text-yellow-600" : "text-red-600") ?> flex gap-2 items-center">
                                <?php
                                if ($level === 0) {
                                    ?>
                                    <div
                                        class="<?php echo $level === 0 ? "bg-blue-50" : ($level === 1 ? "bg-yellow-700" : "bg-red-100") ?> p-1 rounded-lg">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                            fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                            stroke-linejoin="round" class="lucide lucide-info-icon lucide-info">
                                            <circle cx="12" cy="12" r="10" />
                                            <path d="M12 16v-4" />
                                            <path d="M12 8h.01" />
                                        </svg>
                                    </div>
                                <?php } elseif ($level === 1) { ?>
                                    <div
                                        class="<?php echo $level === 0 ? "bg-blue-50" : ($level === 1 ? "bg-yellow-200" : "bg-red-100") ?> p-2 rounded-lg">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                            fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                            stroke-linejoin="round"
                                            class="lucide lucide-triangle-alert-icon lucide-triangle-alert">
                                            <path
                                                d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3" />
                                            <path d="M12 9v4" />
                                            <path d="M12 17h.01" />
                                        </svg>
                                    </div>
                                <?php } else { ?>
                                    <div
                                        class="<?php echo $level === 0 ? "bg-blue-50" : ($level === 1 ? "bg-yellow-200" : "bg-red-200") ?> p-2 rounded-lg">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                            fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                            stroke-linejoin="round" class="lucide lucide-octagon-x-icon lucide-octagon-x">
                                            <path d="m15 9-6 6" />
                                            <path
                                                d="M2.586 16.726A2 2 0 0 1 2 15.312V8.688a2 2 0 0 1 .586-1.414l4.688-4.688A2 2 0 0 1 8.688 2h6.624a2 2 0 0 1 1.414.586l4.688 4.688A2 2 0 0 1 22 8.688v6.624a2 2 0 0 1-.586 1.414l-4.688 4.688a2 2 0 0 1-1.414.586H8.688a2 2 0 0 1-1.414-.586z" />
                                            <path d="m9 9 6 6" />
                                        </svg>
                                    </div>
                                <?php } ?>
                                <?php echo $value->getProblemName(); ?>
                            </div>
                        </th>
                        <td class="px-3 py-2">
                            <?php echo $value->getDescription(); ?>
                        </td>
                        <td class="px-3 py-2">
                            <div hx-get="/klienti/detail/<?php echo $cislazmluvy[$value->getSubjektId()]["podielnikid"]; ?>/#<?php echo $cislazmluvy[$value->getSubjektId()]["cislozmluvy"]; ?>"
                                hx-target="#pageContentMain" hx-replace-url="true" hx-push-url="true"
                                preload="always mouseover"
                                class="flex gap-2 justify-between items-center hover:bg-green-100 py-1 px-3 rounded-lg transition-all cursor-pointer group">
                                <section class="flex gap-2 items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round"
                                        class="lucide lucide-circle-user-icon p-1 rounded-full ring-2 ring-gray-300 dark:ring-gray-500 lucide-circle-user">
                                        <circle cx="12" cy="12" r="10" />
                                        <circle cx="12" cy="10" r="3" />
                                        <path d="M7 20.662V19a2 2 0 0 1 2-2h6a2 2 0 0 1 2 2v1.662" />
                                    </svg>
                                    <div class="flex flex-col">
                                        <span
                                            class="font-bold"><?php echo $cislazmluvy[$value->getSubjektId()]["meno"] . " " . $cislazmluvy[$value->getSubjektId()]["prieznaz"] ?></span>
                                        <span
                                            class="text-xs text-gray-500"><?php echo $cislazmluvy[$value->getSubjektId()]["cislozmluvy"] ?></span>
                                    </div>
                                </section>
                                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round"
                                    class="lucide lucide-square-arrow-out-up-right-icon group-hover:opacity-100 opacity-0 transition-all lucide-square-arrow-out-up-right">
                                    <path d="M21 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h6" />
                                    <path d="m21 3-9 9" />
                                    <path d="M15 3h6v6" />
                                </svg>
                            </div>
                        </td>
                        <td class="px-3 py-2">
                            <span
                                class="font-medium text-blue-600 cursor-pointer dark:text-blue-500 hover:underline">Vyriešiť</span>
                        </td>
                    </tr>
                <?php } ?>
            </tbody>
        </table>
    </div>
</section>
<div id="uzavierka-modal" tabindex="-1" aria-hidden="true" class="hidden fixed top-0
 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 max-h-full backdrop-blur-xs bg-black/40">
    <div class="relative p-4 w-full max-w-7xl h-full">
        <!-- Modal content -->
        <div class="relative bg-white rounded-lg shadow-sm dark:bg-gray-700 h-full">
            <!-- Modal header -->
            <div
                class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600 border-gray-200">
                <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                    Prebieha uzávierka
                </h3>
                <button type="button" class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center 
                    dark:hover:bg-gray-600 dark:hover:text-white" data-modal-hide="uzavierka-modal">
                    <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                        viewBox="0 0 14 14">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                    </svg>
                    <span class="sr-only">Close modal</span>
                </button>
            </div>
            <!-- Modal body -->
            <div id="uzavierka-modal-body" class="pl-10 pr-5 py-10 h-[90vh] overflow-y-auto">
                <section class="flex gap-2">
                    <ol class="relative text-gray-500 border-s border-gray-200 dark:border-gray-700 dark:text-gray-400">
                        <li class="mb-10 ms-6">
                            <span
                                class="absolute flex items-center justify-center w-8 h-8 bg-gray-100 rounded-full -start-4 ring-4 ring-white dark:ring-gray-900 dark:bg-gray-700">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                                    stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round"
                                    class="lucide lucide-user-minus-icon w-3.5 h-3.5 text-gray-500 dark:text-gray-400 lucide-user-minus">
                                    <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
                                    <circle cx="9" cy="7" r="4" />
                                    <line x1="22" x2="16" y1="11" y2="11" />
                                </svg>
                            </span>
                            <h3 class="font-medium leading-tight">Odhlásenie používateľov</h3>
                            <p class="text-sm">Všetci používatelia boli odhlásení.</p>
                        </li>
                        <li class="mb-10 ms-6">
                            <span
                                class="absolute flex items-center justify-center w-8 h-8 bg-gray-100 rounded-full -start-4 ring-4 ring-white dark:ring-gray-900 dark:bg-gray-700">
                                <svg class="w-3.5 h-3.5 text-gray-500 dark:text-gray-400" aria-hidden="true"
                                    xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 16">
                                    <path
                                        d="M18 0H2a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2ZM6.5 3a2.5 2.5 0 1 1 0 5 2.5 2.5 0 0 1 0-5ZM3.014 13.021l.157-.625A3.427 3.427 0 0 1 6.5 9.571a3.426 3.426 0 0 1 3.322 2.805l.159.622-6.967.023ZM16 12h-3a1 1 0 0 1 0-2h3a1 1 0 0 1 0 2Zm0-3h-3a1 1 0 1 1 0-2h3a1 1 0 1 1 0 2Zm0-3h-3a1 1 0 1 1 0-2h3a1 1 0 1 1 0 2Z" />
                                </svg>
                            </span>
                            <h3 class="font-medium leading-tight">Account Info</h3>
                            <p class="text-sm">Step details here</p>
                        </li>
                        <li class="mb-10 ms-6">
                            <span
                                class="absolute flex items-center justify-center w-8 h-8 bg-gray-100 rounded-full -start-4 ring-4 ring-white dark:ring-gray-900 dark:bg-gray-700">
                                <svg class="w-3.5 h-3.5 text-gray-500 dark:text-gray-400" aria-hidden="true"
                                    xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 18 20">
                                    <path
                                        d="M16 1h-3.278A1.992 1.992 0 0 0 11 0H7a1.993 1.993 0 0 0-1.722 1H2a2 2 0 0 0-2 2v15a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V3a2 2 0 0 0-2-2Zm-3 14H5a1 1 0 0 1 0-2h8a1 1 0 0 1 0 2Zm0-4H5a1 1 0 0 1 0-2h8a1 1 0 1 1 0 2Zm0-5H5a1 1 0 0 1 0-2h2V2h4v2h2a1 1 0 1 1 0 2Z" />
                                </svg>
                            </span>
                            <h3 class="font-medium leading-tight">Review</h3>
                            <p class="text-sm">Step details here</p>
                        </li>
                        <li class="ms-6">
                            <span
                                class="absolute flex items-center justify-center w-8 h-8 bg-gray-100 rounded-full -start-4 ring-4 ring-white dark:ring-gray-900 dark:bg-gray-700">
                                <svg class="w-3.5 h-3.5 text-gray-500 dark:text-gray-400" aria-hidden="true"
                                    xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 18 20">
                                    <path
                                        d="M16 1h-3.278A1.992 1.992 0 0 0 11 0H7a1.993 1.993 0 0 0-1.722 1H2a2 2 0 0 0-2 2v15a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V3a2 2 0 0 0-2-2ZM7 2h4v3H7V2Zm5.7 8.289-3.975 3.857a1 1 0 0 1-1.393 0L5.3 12.182a1.002 1.002 0 1 1 1.4-1.436l1.328 1.289 3.28-3.181a1 1 0 1 1 1.392 1.435Z" />
                                </svg>
                            </span>
                            <h3 class="font-medium leading-tight">Confirmation</h3>
                            <p class="text-sm">Step details here</p>
                        </li>
                    </ol>
                    <div class="flex flex-col items-center justify-center w-full gap-4">
                        <svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-loader-circle-icon animate-spin lucide-loader-circle">
                            <path d="M21 12a9 9 0 1 1-6.219-8.56" />
                        </svg>
                        <span>Odhlasujem používateľov...</span>
                    </div>
                </section>
            </div>
            <!-- Modal footer -->
            <!-- <div class="flex items-center p-4 md:p-5 border-t border-gray-200 rounded-b dark:border-gray-600">
                <button data-modal-hide="uzavierka-modal" type="button" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">I accept</button>
                <button data-modal-hide="uzavierka-modal" type="button" class="py-2.5 px-5 ms-3 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700">Decline</button>
            </div> -->
        </div>
    </div>
</div>
<script>
    $("#stornoUzavierky").on("click", () => {
        $("#stornoUzavierkyIcon").hide();
        $("#stornoUzavierkySpinner").show();
        $("#stornoUzavierkyText").text("Stornujem uzávierku...");
    });
</script>
*/ 
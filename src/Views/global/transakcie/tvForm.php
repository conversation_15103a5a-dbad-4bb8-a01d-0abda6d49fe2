<div class="form-content">
    <input type="hidden" name="k_td" value="<?php echo $detail[0]["datend"]; ?>">
    <input type="hidden" name="z_td" value="<?php echo $detail[0]["datzac"]; ?>">
    <input type="hidden" name="kodaktiva" value="<?php echo $detail[0]["kodaktiva"]; ?>">
    <input type="hidden" name="ucetaktiva" value="<?php echo $detail[0]["ucetaktiva"]; ?>">
    <input type="hidden" name="rate" value="<?php echo $detail[0]["rate"]; ?>">
    <input type="hidden" name="dealid" value="<?php echo $detail[0]["dealid"]; ?>">
    <div class="form-grid">
        <div class="form-field">
            <label for="datekonf">Dátum konfirmácie</label>
            <input type="date" id="datekonf" name="datekonf"
                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                readonly="true" value="<?php echo $detail[0]["datkonf"]; ?>" required>
        </div>
        <div class="form-field">
            <label for="daterekonf">Dátum rekonfirmácie</label>
            <input type="date" id="daterekonf" name="daterekonf"
                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                required value="<?php echo $detail[0]["datkonf"]; ?>">
        </div>
        <div class="form-field">
            <label for="amount">Istina</label>
            <input type="text" id="amount" name="amount"
                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                required readonly="true" value="<?php echo $detail[0]["pocet"]; ?>">
        </div>
        <div class="form-field">
            <label for="mena">Mena</label>
            <input type="text" id="mena" name="mena" required
                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                readonly="true" value="<?php echo $detail[0]["mena"]; ?>">
        </div>
        <div class="form-field">
            <label for="numberktv">Číslo terminového vkladu</label>
            <input type="text" id="numberktv"
                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg font-bold focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                required name="numberktv"
                value="<?php echo "Tv_" . $detail[0]["rate"] . "%_" . date("ymd", strtotime($detail[0]["datzac"])) . "-" . date("ymd", strtotime($detail[0]["datend"])) ?>">
        </div>
        <div class="form-field">
            <label for="dealer">Dealer</label>
            <input type="text" id="dealer"
                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                required name="dealer">
        </div>
        <div class="form-field col-span-2">
            <label for="vs">Variabilný symbol</label>
            <input type="text" id="vs" name="vs" readonly="true" required
                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                value="<?php echo str_pad($detail[0]["dealid"], 10, "0", STR_PAD_LEFT); ?>">
        </div>
    </div>
</div>
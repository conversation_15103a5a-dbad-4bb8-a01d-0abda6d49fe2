<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

if (isset($_SESSION["client"])) {
  $id_fond = $_SESSION["client"]["fondid"];
} elseif ($_SESSION["mode"]["mode"] === "admin") {
  $id_fond = 1;
} else {
  $id_fond = 0;
}
$query = "SELECT DISTINCT
kcp.loguserid                     as userid,
u.username as username,
    to_char(kcp.datpok, 'DD.MM.YYYY') AS d_datpok,
    e.cpnaz AS cpnaz,
    e.cpnazskratka AS cpnazskratka,
    c.currencytrade AS currencytrade,
    d.cislo::text AS cislo,
    kcp.limitkurz AS limitkurz,
    kcp.limitprice AS limitprice,
    kcp.kusov AS kusov,
    kcp.druhobchodu AS druhobchodu,
    kcp.dealid AS dealid,
    kcp.eqid AS eqid,
    CASE
        WHEN kcp.eqid = 'Bonds' THEN 'Dlhopis'
        WHEN kcp.eqid = 'Shares' THEN 'Akcia'
        WHEN kcp.eqid = 'Fonds' THEN 'Fond'
        ELSE 'N/A'
    END AS eqid_trans,
    kcp.assigneduserid                                                        as assigneduserid
FROM konfirmaciacp kcp
JOIN dbequity e ON kcp.isin = e.isin
JOIN dbequitycurr c ON kcp.currencyidtrade = c.currencytrade
JOIN dbequitycurrric r ON kcp.ric = r.ric
JOIN equitydruh ed ON ed.druheqid = e.druheqid
JOIN dennikpm d ON kcp.dealid = d.dealid
JOIN users u ON kcp.loguserid = u.userid
WHERE e.eqid IN ('Bonds', 'Shares', 'Fonds')
  AND kcp.subjektid = $id_fond
  AND kcp.logactivityid = 10

UNION ALL
SELECT
k.loguserid                                                             as loguserid,
u.username as username,
    to_char(k.dk_td, 'DD.MM.YYYY') AS d_datpok,
    k.CUB AS cpnaz,
    k.CUB AS cpnazskratka,
    k.MENA AS currencytrade,
    d.cislo::text AS cislo,
    NULL AS limitkurz,
    k.sum_td AS limitprice,
    NULL AS kusov,
    to_char(k.z_td, 'DD.MM.YYYY') || ' - ' || to_char(k.k_td, 'DD.MM.YYYY') AS druhobchodu,
    k.dealid AS dealid,
    'KTV' AS eqid,
    'KTV' AS eqid_trans,
    k.assigneduserid as assigneduserid
FROM konfirmaciaktv k
JOIN dennikpm d ON k.dealid = d.dealid
JOIN users u ON k.loguserid = u.userid
WHERE k.logactivityid = 10
  AND k.dealid IN (SELECT dealid FROM rezervacia)
  AND k.subjektid = $id_fond

UNION ALL
SELECT
kk.loguserid                              as loguserid,
u.username as username,
    to_char(kk.dat_konfirmacia, 'DD.MM.YYYY') AS d_datpok,
    kk.menovypar AS cpnaz,
    
    kk.menovypar AS cpnazskratka,
    kk.menakredit AS currencytrade,
    d.cislo::text AS cislo,
    NULL AS limitkurz,
    kk.sumakredit AS limitprice,
    NULL AS kusov,
    CASE
        WHEN kk.typ_konverzie = 0 THEN 'SPOT'
        WHEN kk.typ_konverzie = 2 THEN 'Forward Delivery'
        ELSE NULL
    END AS druhobchodu,
    kk.dealid AS dealid,
    'Konv' AS eqid,
    'Konverzia' AS eqid_trans,
    kk.assigneduserid as assigneduserid
FROM konverzia kk
JOIN dennikpm d ON kk.dealid = d.dealid
JOIN users u ON kk.loguserid = u.userid
WHERE kk.subjektid = $id_fond
  AND kk.logactivityid = 10
ORDER BY dealid LIMIT 100";
$investicneDataRes = Connection::getDataFromDatabase($query, defaultDB)[1];

?>
<section class="p-5" style="margin-bottom: 8rem">
  <div class="mx-auto">
    <h2 class="scroll-m-20 border-b mb-4 pb-2 text-3xl font-semibold dark:text-gray-100 tracking-tight first:mt-0">
      Transakcie čakajúce na potvrdenie
    </h2>
    <div class="relative bg-white shadow-md dark:bg-gray-800 sm:rounded-lg">

      <div class="overflow-x-visible">
        <?php if (sizeof($investicneDataRes) === 0) { ?>
          <div class="p-4 mt-4 mb-4 text-sm text-blue-800 rounded-lg bg-blue-50 dark:bg-gray-800 dark:text-blue-400"
            role="alert">
            <span class="font-bold">Žiadne záznamy!</span> Momentálne nie sú vytvorené žiadne investičné zámery.
          </div>
        <?php } else {
          ?>
          <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
            <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
              <tr>
                <th scope="col" class="px-4 py-3">Typ</th>
                <th scope="col" class="px-4 py-3">Mena</th>
                <th scope="col" class="px-4 py-3">Suma</th>
                <th scope="col" class="px-4 py-3">Dátum</th>
                <th scope="col" class="px-4 py-3">Investičný nástroj</th>
                <th scope="col" class="px-4 py-3">Druh obchodu</th>
                <th scope="col" class="px-4 py-3">Vytvoril</th>
                <th scope="col" class="px-4 py-3">Riešiteľ</th>
                <th scope="col" class="px-4 py-3"></th>
              </tr>
            </thead>
            <tbody>
              <?php
              foreach ($investicneDataRes as $key => $podienik) {
                $color = "#d4d4d8";
                $url = "";
                $dealid = $podienik["dealid"];
                $assignedUserID = $podienik["assigneduserid"];
                if ($assignedUserID !== NULL) {
                  $assignedUser = Connection::getDataFromDatabase("SELECT DISTINCT userid, username, m.mentionedid, m.initiatorid, m.notificationid, m.objektid, m.id
                      from users
                      LEFT JOIN mentions m ON userid = m.mentionedid
                      WHERE userid = $assignedUserID AND m.objektid = $dealid
                      GROUP BY m.notificationid, users.userid, m.id", defaultDB)[1][0];
                } else {
                  $assignedUser = "";
                }
                switch ($podienik["eqid_trans"]) {
                  case "KTV":
                    $url = "terminovany-vklad";
                    $color = "#38bdf8";
                    break;
                  case "Konverzia":
                    $url = "konverzia";
                    $color = "#7dd3fc";
                    break;
                  case "Akcia":
                    $url = "akcie";
                    $color = "#FF9F40";
                    $eqid = "Shares";
                    break;
                  case "Dlhopis":
                    $url = "dlhopisy";
                    $color = "#FF6384";
                    $eqid = "Bonds";
                    break;
                  case "Fond":
                    $url = "podielove-fondy";
                    $eqid = "Fonds";
                    break;
                }

                $druhobchodu = "";

                switch ($podienik['druhobchodu']) {
                  case "predaj":
                    $druhobchodu = '<span class="bg-red-200 text-red-800 text-xs font-medium me-2 px-2.5 py-0.5 rounded dark:bg-gray-700 dark:text-red-400 border border-red-400">Predaj</span>';
                    break;
                  case "nakup":
                    $druhobchodu = '<span class="bg-green-200 text-green-800 text-xs font-medium me-2 px-2.5 py-0.5 rounded dark:bg-gray-700 dark:text-green-400 border border-green-400">Nákup</span>';
                    break;
                  default:
                    $druhobchodu = $podienik["druhobchodu"];
                    break;
                }
                ?>
                <tr id="<?php echo $dealid; ?>" data-modal-target="confirm-modal<?php echo $key; ?>"
                  data-modal-toggle="confirm-modal<?php echo $key; ?>"
                  class="odd:bg-white odd:dark:bg-gray-900 detailEnabler transition-all cursor-pointer dark:hover:bg-gray-600 even:bg-gray-50 even:dark:bg-gray-800 border-b dark:border-gray-700 border-gray-200">
                  <td class="px-4 py-2">
                    <p style="background: <?php echo $color; ?>;"
                      class="text-sm w-full text-center dark:text-gray-800 text-gray-900 font-bold px-2 py-0.5 rounded">
                      <?php echo $podienik['eqid_trans'] ?>
                    </p>
                  </td>
                  <td class="px-4 py-2">
                    <span
                      class="text-sm dark:text-gray-100 text-gray-900 font-normal px-2 py-0.5 rounded"><?php print_r($podienik['currencytrade']); ?></span>
                  </td>
                  <td class="px-4 py-2">
                    <span class="text-sm dark:text-gray-100 text-gray-900 font-bold px-2 py-0.5 rounded"><?php
                    if ($podienik["eqid_trans"] !== "Konverzia") {
                      echo number_format(round($podienik['limitprice'], 2), 2, ".", " ");
                    } else {
                      echo number_format(round($podienik['limitprice'], 2), 2, ".", " ");
                    }
                    ?></span>
                  </td>
                  <td class="px-4 py-2">
                    <span
                      class="text-sm dark:text-gray-100 text-gray-900 font-normal px-2 py-0.5 rounded"><?php print_r($podienik['d_datpok']); ?></span>
                  </td>
                  <td class="px-4 py-2">
                    <span class="text-sm dark:text-gray-100 text-gray-900 font-normal px-2 py-0.5 rounded"><span
                        class="bg-gray-100 text-gray-800 text-xs font-bold me-2 px-2.5 py-0.5 rounded-full dark:bg-gray-700 dark:text-gray-300"><?php print_r($podienik['cpnazskratka']); ?></span></span>
                  </td>
                  <td class="px-4 py-2">
                    <span
                      class="text-sm dark:text-gray-100 text-gray-900 font-normal px-2 py-0.5 rounded"><?php echo $druhobchodu; ?></span>
                  </td>
                  <td class="px-4 py-2">
                    <a href="/pouzivatelia/detail/<?php echo $podienik["userid"]; ?>"
                      class="flex items-center pl-3 gap-2 p-0.5 rounded-lg hover:bg-gray-200 hover:underline cursor-pointer transition-all">
                      <div
                        class="relative w-5 h-5 overflow-hidden bg-gray-200 flex items-center justify-center rounded-full dark:bg-gray-600">
                        <svg class="w-5 h-5 text-gray-400 -mb-2" fill="currentColor" viewBox="0 0 20 20"
                          xmlns="http://www.w3.org/2000/svg">
                          <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                            clip-rule="evenodd">
                          </path>
                        </svg>
                      </div>
                      <small><?php echo $podienik["username"] ?></small>
                    </a>
                  </td>
                  <td class="px-4 py-2">
                    <?php if ($assignedUser !== "") { ?>
                      <div
                        class="flex items-center pl-3 justify-between p-0.5 rounded-lg group dark:hover:bg-gray-700 hover:bg-gray-200 hover:underline cursor-pointer transition-all">
                        <a href="/pouzivatelia/detail/<?php echo $assignedUser["userid"]; ?>" class="flex gap-2">
                          <div
                            class="relative w-5 h-5 overflow-hidden flex items-center justify-center rounded-full dark:bg-gray-600">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
                              stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
                              class="lucide text-blue-300 lucide-circle-user">
                              <circle cx="12" cy="12" r="10" />
                              <circle cx="12" cy="10" r="3" />
                              <path d="M7 20.662V19a2 2 0 0 1 2-2h6a2 2 0 0 1 2 2v1.662" />
                            </svg>
                          </div>
                          <p class="text-xs"><?php echo $assignedUser["username"]; ?></p>
                        </a>
                        <form hx-post="/api/mentionUserToAction" hx-target="#notificationsWrapper" class="mb-0">
                          <input type="hidden" name="initiatorid" id="initiatorid"
                            value="<?php echo $assignedUser["initiatorid"]; ?>" />
                          <input type="hidden" name="mentionedid" id="mentionedid"
                            value="<?php echo $assignedUser["mentionedid"]; ?>" />
                          <input type="hidden" name="mentioncolumn" id="mentioncolumn" value="objektid" />
                          <input type="hidden" name="mentiondata" id="mentiondata" value="<?php echo $dealid; ?>" />
                          <input type="hidden" name="action" id="action" value="delete" />
                          <button type="submit"
                            class="p-1 rounded-md removeMention opacity-0 dark:hover:bg-red-700 dark:hover:text-white group-hover:opacity-100 transition-all cursor-pointer">
                            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none"
                              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                              class="lucide removeMentionIcon lucide-x">
                              <path d="M18 6 6 18" />
                              <path d="m6 6 12 12" />
                            </svg>
                            <svg xmlns="http://www.w3.org/2000/svg" width="13" height="13" viewBox="0 0 24 24" fill="none"
                              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                              class="lucide animate-spin dark:text-white text-gray-800 removeMentionSpinner lucide-loader-circle"
                              style="display: none;">
                              <path d="M21 12a9 9 0 1 1-6.219-8.56" />
                            </svg>
                          </button>
                        </form>
                      </div>
                    <?php } else { ?>
                      <div data-dropdown-toggle="assignedUsers<?php echo $key; ?>" data-dropdown-placement="bottom"
                        hx-get="/api/get/notificationUsers?dealid=<?php echo $dealid; ?>&action=rekonf"
                        hx-target="#assignedUsers<?php echo $key; ?>" id="<?php echo $key; ?>"
                        class="p-1 rounded-md items-center hover:bg-gray-200 assignedUsersOpen dark:text-gray-100 dark:hover:text-gray-600 transition-all cursor-pointer text-gray-600 text-xs inline-flex gap-1 px-2">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                          stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                          class="lucide lucide-user-round-plus">
                          <path d="M2 21a8 8 0 0 1 13.292-6"></path>
                          <circle cx="10" cy="8" r="5"></circle>
                          <path d="M19 16v6"></path>
                          <path d="M22 19h-6"></path>
                        </svg>
                        <small>Pridať</small>
                      </div>
                      <div id="assignedUsers<?php echo $key; ?>"
                        class="z-10 hidden bg-white absolute right-2 assignedUsersDropdown rounded-lg shadow-xl border dark:bg-gray-700"
                        style="top: 2rem"></div>
                    <?php } ?>
                  </td>
                  <td class="px-4 py-2 font-medium dark:text-gray-100 text-gray-900 whitespace-nowrap dark:text-white">
                    <div class="flex items-center gap-1">
                      <button data-modal-target="confirm-modal<?php echo $key; ?>"
                        type=data-modal-toggle="confirm-modal<?php echo $key; ?>" id="<?php echo $dealid; ?>"
                        class="p-2 cursor-pointer detailEnabler hover:bg-green-200 transition-all rounded-lg">
                        <input type="hidden" name="eqid" id="eqid" value="<?php echo $podienik["eqid"]; ?>" />
                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none"
                          stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                          class="lucide text-green-700 lucide-circle-check">
                          <circle cx="12" cy="12" r="10" />
                          <path d="m9 12 2 2 4-4" />
                        </svg>
                      </button>
                      <button data-modal-target="modal-<?php echo $podienik["dealid"]; ?>"
                        data-modal-toggle="modal-<?php echo $podienik["dealid"]; ?>"
                        class="p-2 cursor-pointer deleteButton hover:bg-red-200 transition-all rounded-lg">
                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none"
                          stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                          class="lucide text-red-500 lucide-circle-x">
                          <circle cx="12" cy="12" r="10" />
                          <path d="m15 9-6 6" />
                          <path d="m9 9 6 6" />
                        </svg>
                      </button>
                    </div>
                  </td>
                </tr>
                <?php
              }
        }
        ?>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</section>
<?php
foreach ($investicneDataRes as $item) {
  switch ($item["eqid"]) {
    case "Shares":
    case "Bonds":
    case "Fonds":
      $urlToPost = "/api/transakcie/potvrditCP";
      break;
    case "KTV":
      $urlToPost = "/api/transakcie/potvrditTV";
      break;
    case "Konv":
      $urlToPost = "/api/transakcie/potvrditKonv";
      break;
  }

}
foreach ($investicneDataRes as $key => $item) {
  switch ($item["eqid"]) {
    case "Shares":
    case "Bonds":
    case "Fonds":
      $urlToPost = "/api/transakcie/potvrditCP";
      break;
    case "KTV":
      $urlToPost = "/api/transakcie/potvrditTV";
      break;
    case "Konv":
      $urlToPost = "/api/transakcie/potvrditKonv";
      break;
    default:
      $urlToPost = "";
  }
  ?>
  <div id="modal-<?php echo $item["dealid"]; ?>" tabindex="-1"
    class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
    <div class="relative p-4 w-full max-w-md max-h-full">
      <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
        <button type="button"
          class="absolute top-3 end-2.5 text-gray-400 bg-transparent hover:bg-gray-200 hover:dark:text-gray-100 text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
          data-modal-hide="modal-<?php echo $item["dealid"]; ?>">
          <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
          </svg>
          <span class="sr-only">Close modal</span>
        </button>
        <div class="p-4 md:p-5 text-center">
          <div id="toast" class="py-3 px-8"></div>
          <form class="deleteInvestmentIntention" hx-post="/api/investicne-zamery/terminovany-vklad/delete/delete"
            hx-target="#toast">
            <input type="hidden" name="type" id="type" value="<?php echo $item['eqid_trans']; ?>" />
            <input type="hidden" name="dealid" id="dealid" value="<?php echo $item["dealid"]; ?>" />
            <svg class="mx-auto mb-4 text-gray-400 w-12 h-12 dark:text-gray-200" aria-hidden="true"
              xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
              <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M10 11V6m0 8h.01M19 10a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
            </svg>
            <h3 class="mb-5 text-lg font-normal text-gray-500 dark:text-gray-400">Chcete naozaj zrušiť tento
              <strong>investičný zámer?</strong>
            </h3>
            <button type="submit"
              class="text-white bg-red-600 deleteTransakcia hover:bg-red-800 focus:ring-4 focus:outline-none focus:ring-red-300 dark:focus:ring-red-800 font-medium rounded-lg text-sm inline-flex gap-4 items-center px-5 py-2.5 text-center">
              <span id="deleteText">Áno, odstrániť</span>
              <svg id="deleteSpin" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="lucide animate-spin hidden lucide-loader-circle">
                <path d="M21 12a9 9 0 1 1-6.219-8.56" />
              </svg>
            </button>
            <button data-modal-hide="modal-<?php echo $item["dealid"]; ?>" type="button"
              class="py-2.5 px-5 ms-3 text-sm font-medium dark:text-gray-100 text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700">Nie,
              zavrieť</button>
          </form>
        </div>
      </div>
    </div>
  </div>
  <div id="confirm-modal<?php echo $key; ?>" tabindex="-1" aria-hidden="true" style="background: #33333370;"
    class="hidden fixed top-0 right-0 dark:text-gray-100 left-0 z-50 justify-center items-center w-full h-[calc(100%-1rem)] h-full max-h-full">
    <div id="modalWrapperInside" class="relative p-4 w-full max-h-full" style="max-width: 65vw; height: 95vh;">
      <div
        class="relative rounded-lg shadow dark:bg-gray-700 max-h-full flex flex-col bg-white border-2 dark:border-gray-600 border-gray-300 overflow-x-hidden overflow-y-auto">
        <form hx-target="<?php echo $urlToPost; ?>" hx-target="#toast" class="rekonfForm">
          <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
            <h1 class="text-2xl dark:text-gray-100 font-bold mb-0">Potvrdenie obchodu</h1>
            <section class="inline-flex items-center gap-4 relative z-50">
              <button type="button"
                class="text-gray-400 bg-transparent hover:bg-gray-200 hover:dark:text-gray-100 text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
                data-modal-hide="confirm-modal<?php echo $key; ?>">
                <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                  viewBox="0 0 14 14">
                  <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                </svg>
                <span class="sr-only">Close modal</span>
              </button>
            </section>
          </div>
          <div id="toast" class="py-3 px-8"></div>
          <div id="confirmModalBody<?php echo $item["dealid"]; ?>"
            class="p-4 md:p-5 border-b dark:text-gray-100 rounded-t h-full">

          </div>
          <div
            class="flex items-center justify-end p-2 rounded-t border-t sticky dark:bg-gray-800 bg-white dark:border-gray-900 bottom-0 p-5">
            <div class="flex gap-2 items-center">
              <button type="button" data-modal-hide="confirm-modal<?php echo $key; ?>"
                class="btn-secondary">Zavrieť</button>
              <button type="submit"
                class="text-white bg-gradient-to-r from-blue-500 via-blue-600 to-blue-700 hover:bg-gradient-to-br focus:ring-4 focus:outline-none focus:ring-blue-300 dark:focus:ring-blue-800 transition-all font-bold rounded-lg text-sm px-5 py-2.5 text-center flex items-center gap-2 rekonfFormButton">
                <span>Potvrdiť</span>
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                  style="display: none;" class="lucide animate-spin lucide-loader-circle">
                  <path d="M21 12a9 9 0 1 1-6.219-8.56" />
                </svg></button>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
<?php } ?>
<script>
  document.querySelectorAll(".detailEnabler").forEach((item, key) => {
    item.addEventListener("click", (e) => {
      const eqid = item.querySelector("#eqid").value;
      const dealid = item.id;
      console.log('#confirmModalBody' + dealid);
      htmx.ajax('POST', "src/Views/global/transakcie/confirmModalBody.php", { target: '#confirmModalBody' + dealid, values: { "eqid": eqid, "dealid": dealid } });
    });
  });

  $(".rekonfForm").on("submit", (e) => {
    e.preventDefault();
    let object = {}
    const formData = new FormData(e.currentTarget);
    formData.forEach(function (value, key) {
      object[key] = value;
    });
    console.log(object);
    $(".rekonfFormButton").attr('disabled', 'disabled');
    $(".rekonfFormButton").css("background-color", "#9d9d9d");
    $(".rekonfFormButton svg").css("display", "block");
    $(".rekonfFormButton span").html("Potvrdzujem...");
    htmx.ajax('POST', "<?php echo $urlToPost; ?>", { target: '#toast', values: object });
  });

  $(".deleteButton").on("click", (e) => {
    e.stopPropagation();
  });

  $(".assignedUsersOpen").on("click", (e) => {
    e.stopPropagation();
  });

  $(".assignedUsersDropdown").on("click", (e) => {
    e.stopPropagation();
  });

  document.querySelectorAll(".deleteInvestmentIntention").forEach((item) => {
    item.addEventListener("submit", (e) => {
      e.stopPropagation();
      document.getElementById("deleteText").innerHTML = "Odstraňujem...";
      $(".deleteSpin").removeClass("hidden");
      setTimeout(() => {
        htmx.ajax('GET', window.location.pathname + window.location.search, {
          target: "#pageContentMain",
        });
      }, 1000);
    });
  });
</script>
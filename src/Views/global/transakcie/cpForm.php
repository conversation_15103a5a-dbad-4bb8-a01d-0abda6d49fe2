<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

$mena = $detail[0]["currencyidtrade"];

$penaznyPoducetQuery = "select cub
        from fondsbu
        where fondid = 0 and mena='$mena'
        order by cub desc";
$penaznyPoducet = Connection::getDataFromDatabase($penaznyPoducetQuery, defaultDB)[1];

?>
<div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
    <div>
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-100 mb-1" for="cislo-uctu"><PERSON><PERSON><PERSON></label>
        <select id="cislouctu" name="cislouctu"
            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
            <?php foreach ($penaznyPoducet as $key => $item) { ?>
                <option value="<?php echo $item["cub"]; ?>" <?php echo $detail[0]["cubu"] === $item["cub"] ? "selected='selected'" : "" ?>>
                    <?php echo $item["cub"]; ?>
                </option>
            <?php } ?>
        </select>
    </div>
    <div>
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-100 mb-1" for="cuprotistrana">Peňažný účet
            partnera</label>
        <select id="cuprotistrana" name="cuprotistrana"
            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
            <?php foreach ($partners as $key => $value) { ?>
                <option value="<?php echo $value["cu"]; ?>"><?php echo $value["popis"] . " - " . $value["mena"]; ?>
                </option>
            <?php } ?>

        </select>
    </div>
    <div>
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-100 mb-1" for="cislo-majet-uctu">Číslo majetkového
            účtu</label>
        <input type="hidden" id="majetkovyucet" name="majetkovyucet" class="w-full p-2 border rounded"
            value="<?php $detail[0]["cum"]; ?>">
        <h2 class="font-semibold text-lg"><?php echo $detail[0]["cum"]; ?></h2>
    </div>
    <div>
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-100 mb-1" for="partner">Partner</label>
        <input type="hidden" id="partner" name="partner" class="w-full p-2 border rounded"
            value="<?php $detail[0]["partnerid"]; ?>">
        <h2 class="font-semibold text-lg"><?php echo $detail[0]["nazovpartnera"]; ?></h2>
    </div>
</div>

<div class="overflow-x-auto mb-6">
    <table class="w-full border-collapse dark:border-gray-800 border">
        <thead>
            <tr class="dark:bg-gray-900 dark:text-gray-100 bg-gray-100">
                <th class="border dark:border-gray-800 p-2 text-left">Rekonfirmácia</th>
                <th class="border dark:border-gray-800 p-2 text-left">Cenný papier</th>
                <th class="border dark:border-gray-800 p-2 text-left">RIC</th>
                <th class="border dark:border-gray-800 p-2 text-left">ISIN</th>
                <th class="border dark:border-gray-800 p-2 text-left">Druh obchodu</th>
                <th class="border dark:border-gray-800 p-2 text-left">Kusov</th>
                <th class="border dark:border-gray-800 p-2 text-left">Mena</th>
            </tr>
        </thead>
        <tbody>
            <tr class="dark:text-gray-100">
                <td class="border p-2"><?php echo $detail[0]["cislo"]; ?></td>
                <td class="border p-2"><?php echo $detail[0]["cpnazskratka"]; ?></td>
                <td class="border p-2"><?php echo $detail[0]["ric"]; ?></td>
                <td class="border p-2"><?php echo $detail[0]["isinreal"]; ?></td>
                <td id="druhobchodu" class="border p-2"><?php echo $detail[0]["druhobchodu"]; ?></td>
                <td class="border p-2"><?php echo $detail[0]["kusov"]; ?></td>
                <td class="border p-2"><?php echo $detail[0]["currencyidtrade"]; ?></td>
            </tr>
        </tbody>
    </table>
</div>
<input type="hidden" name="logdatatimeactivity" id="logdatatimeactivity"
    value="<?php echo $detail[0]["logdatatimeactivity"]; ?>" />
<input type="hidden" name="dealid" id="dealid" value="<?php echo $detail[0]["dealid"]; ?>" />
<input type="hidden" name="druhobchodu" id="druhobchodu" value="<?php echo $detail[0]["druhobchodu"]; ?>" />
<input type="hidden" name="kusovreal" id="kusovreal" value="<?php echo $detail[0]["kusovreal"]; ?>" />
<input type="hidden" name="isincurrric" id="isincurrric" value="<?php echo $detail[0]["isincurrric"]; ?>" />
<input type="hidden" name="currencyidtrade" id="currencyidtrade" value="<?php echo $detail[0]["currencyidtrade"]; ?>" />
<input type="hidden" name="isinreal" id="isinreal" value="<?php echo $detail[0]["isinreal"]; ?>" />
<input type="hidden" name="cum" id="cum" value="<?php echo $detail[0]["cum"]; ?>" />
<div class="mt-6 border-t-4 pt-3 border-gray-700">
    <h2 class="text-lg font-semibold mb-2">Číslo rekonfirnácie: 00000373024</h2>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-100 mb-1" for="datumobchodu">Dátum/čas
                obchodu</label>
            <input type="datetime-local" id="datumobchodu" name="datumobchodu" class="w-full p-2 border rounded"
                value="<?php echo $detail[0]["datum_cas_obchodu"]; ?>">
        </div>
        <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-100 mb-1" for="dobavysporm">Doba vysporiadania</label>
            <input type="text" id="dobavysporm" name="dobavysporm" class="w-full p-2 border rounded"
                value="<?php echo abs(round($datediff / (60 * 60 * 24))); ?>">
        </div>
        <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-100 mb-1" for="datumvysporm">Dátum
                vysporiadania</label>
            <input type="date" id="datumvysporm" name="datumvysporm" class="w-full p-2 border rounded"
                value="<?php echo $detail[0]["druhobchodu"] === "nakup" ? $detail[0]["datumfv"] : $detail[0]["datummv"]; ?>">
        </div>
        <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-100 mb-1" for="kusov">Kusy zreal.</label>
            <input type="text" id="kusov" name="kusov" class="w-full p-2 border rounded"
                value="<?php echo $detail[0]["kusov"]; ?>">
        </div>
        <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-100 mb-1" for="kurz">Kurz</label>
            <input type="number" id="kurz" name="kurz" class="w-full p-2 border rounded"
                value="<?php echo $detail[0]["limitkurz"]; ?>">
        </div>
        <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-100 mb-1" for="auv">AUV</label>
            <input type="number" id="auv" name="auv" value="<?php echo $detail[0]["auvwork"]; ?>"
                class="w-full p-2 border rounded">
        </div>
        <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-100 mb-1" for="poplatok">Poplatok</label>
            <input type="number" id="poplatok" name="poplatok" class="w-full p-2 border rounded"
                value="<?php echo $detail[0]["poplatokpercent"]; ?>">
        </div>
        <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-100 mb-1" for="cenaobchodu">Cena obchodu</label>
            <input type="text" id="cenaobchodu" name="cenaobchodu" readonly class="w-full p-2 border rounded" value="<?php
            switch ($eqid) {
                case 'Bonds':
                    $cena = (((float) $detail[0]["nominalemisie"] / 100 * (float) $detail[0]["limitkurz"]) * (int) $detail[0]["kusov"]) + (float) $detail[0]["auv"];
                    break;
                case 'Shares':
                    $cena = (float) $detail[0]["limitkurz"] * (int) $detail[0]["kusov"];
                    break;
                case 'Fonds':
                    $cena = (float) $detail[0]["limitkurz"] * (int) $detail[0]["kusov"];
                    break;
                case 'Depo':
                    $cena = (float) $detail[0]["nominalemisie"] * (int) $detail[0]["kusov"];
                    break;
            }
            $cena = number_format($cena, 2, ".", " ");
            echo $cena; ?>">
        </div>
        <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-100 mb-1" for="transsuma">Transakčná
                suma</label>
            <input type="number" id="transsuma" name="transsuma" class="w-full p-2 border rounded"
                value="<?php echo $detail[0]["limitprice"]; ?>">
        </div>
        <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-100 mb-1" for="vs">VS</label>
            <input type="text" id="vs" name="vs" class="w-full p-2 border rounded"
                value="<?php echo str_replace('-', '', $maxDatum); ?>">
        </div>
        <div class="flex items-center">
            <input type="checkbox" id="burzovy" name="burzovy" class="mr-2">
            <label for="burzovy" class="text-sm font-medium text-gray-700 dark:text-gray-100">Burzový</label>
        </div>
        <input type="hidden" name="nominal" id="nominal" value="<?php echo $detail[0]["nominalemisie"]; ?>" />
        <input type="hidden" name="eqid" id="eqid" value="<?php echo $eqid; ?>" />
    </div>
</div>
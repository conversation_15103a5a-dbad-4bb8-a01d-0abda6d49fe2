<div class="form-content">
    <input type="hidden" name="logdatatimeactivity" value="<?php echo $detail[0]["logdatatimeactivity"]; ?>">
    <input type="hidden" name="dealid" value="<?php echo $detail[0]["dealid"]; ?>">
    <input type="hidden" name="typ_konverzie" value="<?php echo $detail[0]["typ_konverzie"]; ?>">
    <input type="hidden" name="menakredit" value="<?php echo $detail[0]["menakredit"]; ?>" />
    <div class="form-grid">
        <section class="col-span-2 inline-flex gap-4 items-center">
            <div class="form-field w-full">
                <label for="datumobchodu">Dátum realizácie</label>
                <input type="date" id="datumobchodu"
                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                    name="datumobchodu" readonly="true" value="<?php echo $detail[0]["d_realizacia"]; ?>" required>
            </div>
            <div class="form-field w-24">
                <label for="dobavyspor">Doba</label>
                <input type="text" id="dobavyspor"
                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                    name="dobavyspor" required readonly="true" value="0">
            </div>
            <div class="form-field w-full">
                <label for="daterekonf">Dátum vysporiadania</label>
                <input type="date" id="daterekonf"
                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                    name="daterekonf" required value="<?php echo $detail[0]["dat_konfirmacia"]; ?>">
            </div>
        </section>
        <div class="form-field col-span-2">
            <label for="vs">Variabilný symbol</label>
            <input type="text" id="vs" name="vs"
                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                readonly="true" required value="<?php echo str_pad($detail[0]["dealid"], 10, "0", STR_PAD_LEFT); ?>">
        </div>
        <div class="flex flex-row gap-2 items-end">
            <section class="form-field w-full">
                <label for="percento">Poplatok</label>
                <input type="text" id="percento"
                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                    name="percento" required
                    value="<?php echo $detail[0]["poplatok"] === null ? "0.00" : $detail[0]["poplatok"]; ?>">
            </section>
            <p class="dark:bg-gray-900 dark:text-gray-100 bg-gray-200 border rounded-lg p-2">%</p>
        </div>
        <div class="flex flex-row gap-2 items-end">
            <section class="form-field w-full">
                <label for="poplatok">Poplatok</label>
                <input type="text" id="poplatok"
                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                    name="poplatok" required readonly="true"
                    value="<?php echo $detail[0]["poplatok"] === null ? "0.00" : $detail[0]["poplatok"]; ?>">
            </section>
            <p class="dark:bg-gray-900 dark:text-gray-100 bg-gray-200 border rounded-lg p-2"><?php echo $detail[0]["menakredit"]; ?></p>
        </div>
        <section class="inline-flex col-span-2 justify-between items-center dark:bg-gray-900 dark:text-gray-100 bg-gray-200 rounded-md p-3">
            <h3 class="font-semibold text-2xl">Suma kredit:</h3>
            <p class="font-bold text-3xl"><?php echo number_format($detail[0]["sumakredit"], 2, ".", " "); ?>€</p>
            <input type="hidden" id="sumakredit" name="sumakredit" value="<?php echo $detail[0]["sumakredit"]; ?>" />
            <input type="hidden" id="sumadebet" name="sumadebet" value="<?php echo $detail[0]["sumadebet"]; ?>" />
        </section>
    </div>
</div>
<script>
    $("#daterekonf").on("change", () => {
        const oneDay = 24 * 60 * 60 * 1000;
        const firstDate = Date.parse($("#datumobchodu").val());
        const secondDate = Date.parse($("#daterekonf").val());

        const diffDays = Math.round(Math.abs((firstDate - secondDate) / oneDay));

        $("#dobavyspor").val(diffDays);
    });

    $("#percento").on("keyup", (e) => {
        const percento = e.target.value;
        const sumakredit = $("#sumakredit").val();
        const poplatok = ((percento / 100) * sumakredit).toFixed(2);
        $("#poplatok").val(poplatok)
    });
</script>
<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

$portfolioID = isset($matches[1]) ? $matches[1] : null;
$portfolioRes = Connection::getDataFromDatabase("SELECT * FROM portfolio WHERE cislozmluvy = '$portfolioID'", defaultDB);
$portfolio = $portfolioRes[1][0];
?>
<div class="bg-white border-b border-slate-200">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center py-6">
        <div class="flex items-center space-x-4">
          <div class="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center">
            <span class="text-primary-600 font-semibold text-lg">JN</span>
          </div>
          <div>
            <h1 class="text-2xl font-bold text-slate-800"><PERSON><PERSON></h1>
            <p class="text-slate-500">Portfolio Overview • ID: 5876901</p>
          </div>
        </div>
        <div class="flex space-x-3">
          <button class="px-4 py-2 bg-white border border-slate-300 rounded-lg text-slate-700 hover:bg-slate-50 transition-colors">
            Export Report
          </button>
          <button class="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors">
            Rebalance Portfolio
          </button>
        </div>
      </div>
    </div>
  </div>

  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Portfolio Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <!-- Total Value -->
      <div class="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-slate-500 text-sm font-medium">Total Portfolio Value</p>
            <p class="text-3xl font-bold text-slate-800 mt-1">€74,557.75</p>
          </div>
          <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="w-6 h-6 text-green-600">
              <path fill-rule="evenodd" d="M10 2a8 8 0 100 16 8 8 0 000-16zM6.75 9.25a.75.75 0 000 1.5h4.59l-2.1 1.95a.75.75 0 001.02 1.1l3.5-3.25a.75.75 0 000-1.1l-3.5-3.25a.75.75 0 10-1.02 1.1l2.1 1.95H6.75z" clip-rule="evenodd" />
            </svg>
          </div>
        </div>
        <div class="flex items-center mt-4">
          <span class="text-green-600 text-sm font-medium">+12.4%</span>
          <span class="text-slate-500 text-sm ml-2">vs last month</span>
        </div>
      </div>

      <!-- Today's Change -->
      <div class="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-slate-500 text-sm font-medium">Today's Change</p>
            <p class="text-2xl font-bold text-green-600 mt-1">+€1,247.32</p>
          </div>
          <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="w-6 h-6 text-green-600">
              <path fill-rule="evenodd" d="M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z" clip-rule="evenodd" />
            </svg>
          </div>
        </div>
        <div class="flex items-center mt-4">
          <span class="text-green-600 text-sm font-medium">+1.7%</span>
          <span class="text-slate-500 text-sm ml-2">daily change</span>
        </div>
      </div>

      <!-- Total Return -->
      <div class="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-slate-500 text-sm font-medium">Total Return</p>
            <p class="text-2xl font-bold text-green-600 mt-1">+€8,557.75</p>
          </div>
          <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="w-6 h-6 text-blue-600">
              <path d="M10.75 10.818v2.614A3.13 3.13 0 0011.888 13c.482-.315.612-.648.612-.875 0-.227-.13-.56-.612-.875a3.13 3.13 0 00-1.138-.432zM8.33 8.62c.053.055.115.11.184.164.208.16.46.284.736.363V6.603a2.45 2.45 0 00-.35.13c-.14.065-.27.143-.386.233-.377.292-.514.627-.514.909 0 .184.058.39.202.592.037.051.08.102.128.152z" />
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-6a.75.75 0 01.75.75v.316a3.78 3.78 0 011.653.713c.426.33.744.74.925 1.2a.75.75 0 01-1.395.55 1.35 1.35 0 00-.447-.563 2.187 2.187 0 00-.736-.363V9.3c.698.093 1.383.32 1.959.696.787.514 1.29 1.27 1.29 2.13 0 .86-.504 1.616-1.29 2.13-.576.377-1.261.603-1.96.696v.299a.75.75 0 11-1.5 0v-.3c-.697-.092-1.382-.318-1.958-.695-.482-.315-.857-.717-1.078-1.188a.75.75 0 111.359-.636c.08.173.245.376.54.569.313.205.706.353 1.138.432v-2.748a3.782 3.782 0 01-1.653-.713C6.9 9.433 6.5 8.681 6.5 7.875c0-.805.4-1.558 1.097-2.096a3.78 3.78 0 011.653-.713V4.75A.75.75 0 0110 4z" clip-rule="evenodd" />
            </svg>
          </div>
        </div>
        <div class="flex items-center mt-4">
          <span class="text-green-600 text-sm font-medium">+12.97%</span>
          <span class="text-slate-500 text-sm ml-2">since inception</span>
        </div>
      </div>

      <!-- Risk Score -->
      <div class="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-slate-500 text-sm font-medium">Risk Score</p>
            <p class="text-2xl font-bold text-amber-600 mt-1">6.2/10</p>
          </div>
          <div class="w-12 h-12 bg-amber-100 rounded-full flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="w-6 h-6 text-amber-600">
              <path fill-rule="evenodd" d="M8.485 2.495c.673-1.167 2.357-1.167 3.03 0l6.28 10.875c.673 1.167-.17 2.625-1.516 2.625H3.72c-1.347 0-2.189-1.458-1.515-2.625L8.485 2.495zM10 5a.75.75 0 01.75.75v3.5a.75.75 0 01-1.5 0v-3.5A.75.75 0 0110 5zm0 9a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd" />
            </svg>
          </div>
        </div>
        <div class="flex items-center mt-4">
          <span class="text-amber-600 text-sm font-medium">Moderate</span>
          <span class="text-slate-500 text-sm ml-2">risk level</span>
        </div>
      </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- Left Column -->
      <div class="lg:col-span-2 space-y-8">
        <!-- Portfolio Performance Chart -->
        <div class="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
          <div class="flex justify-between items-center mb-6">
            <h2 class="text-xl font-bold text-slate-800">Portfolio Performance</h2>
            <div class="flex space-x-2">
              <button class="px-3 py-1 text-sm bg-primary-100 text-primary-700 rounded-md">1M</button>
              <button class="px-3 py-1 text-sm text-slate-500 hover:bg-slate-100 rounded-md">3M</button>
              <button class="px-3 py-1 text-sm text-slate-500 hover:bg-slate-100 rounded-md">6M</button>
              <button class="px-3 py-1 text-sm text-slate-500 hover:bg-slate-100 rounded-md">1Y</button>
              <button class="px-3 py-1 text-sm text-slate-500 hover:bg-slate-100 rounded-md">ALL</button>
            </div>
          </div>
          <div class="h-64">
            <canvas id="portfolioChart"></canvas>
          </div>
        </div>

        <!-- Holdings -->
        <div class="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
          <div class="flex justify-between items-center mb-6">
            <h2 class="text-xl font-bold text-slate-800">Holdings</h2>
            <button class="text-primary-600 hover:text-primary-700 text-sm font-medium">View All</button>
          </div>
          
          <div class="space-y-4">
            <!-- Holding Item -->
            <div class="flex items-center justify-between p-4 bg-slate-50 rounded-lg hover:bg-slate-100 transition-colors">
              <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                  <span class="text-blue-600 font-semibold text-sm">AAPL</span>
                </div>
                <div>
                  <h3 class="font-medium text-slate-800">Apple Inc.</h3>
                  <p class="text-slate-500 text-sm">150 shares • Technology</p>
                </div>
              </div>
              <div class="text-right">
                <p class="font-medium text-slate-800">€26,314.50</p>
                <p class="text-green-600 text-sm">+€2,314.50 (+9.6%)</p>
              </div>
            </div>

            <!-- Holding Item -->
            <div class="flex items-center justify-between p-4 bg-slate-50 rounded-lg hover:bg-slate-100 transition-colors">
              <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                  <span class="text-green-600 font-semibold text-sm">MSFT</span>
                </div>
                <div>
                  <h3 class="font-medium text-slate-800">Microsoft Corporation</h3>
                  <p class="text-slate-500 text-sm">75 shares • Technology</p>
                </div>
              </div>
              <div class="text-right">
                <p class="font-medium text-slate-800">€22,125.00</p>
                <p class="text-green-600 text-sm">+€1,875.00 (+9.3%)</p>
              </div>
            </div>

            <!-- Holding Item -->
            <div class="flex items-center justify-between p-4 bg-slate-50 rounded-lg hover:bg-slate-100 transition-colors">
              <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
                  <span class="text-purple-600 font-semibold text-sm">TSLA</span>
                </div>
                <div>
                  <h3 class="font-medium text-slate-800">Tesla, Inc.</h3>
                  <p class="text-slate-500 text-sm">50 shares • Automotive</p>
                </div>
              </div>
              <div class="text-right">
                <p class="font-medium text-slate-800">€12,750.00</p>
                <p class="text-red-600 text-sm">-€750.00 (-5.6%)</p>
              </div>
            </div>

            <!-- Holding Item -->
            <div class="flex items-center justify-between p-4 bg-slate-50 rounded-lg hover:bg-slate-100 transition-colors">
              <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center">
                  <span class="text-yellow-600 font-semibold text-sm">BOND</span>
                </div>
                <div>
                  <h3 class="font-medium text-slate-800">Government Bonds</h3>
                  <p class="text-slate-500 text-sm">€10,000 • Fixed Income</p>
                </div>
              </div>
              <div class="text-right">
                <p class="font-medium text-slate-800">€10,368.25</p>
                <p class="text-green-600 text-sm">+€368.25 (+3.7%)</p>
              </div>
            </div>

            <!-- Holding Item -->
            <div class="flex items-center justify-between p-4 bg-slate-50 rounded-lg hover:bg-slate-100 transition-colors">
              <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-indigo-100 rounded-full flex items-center justify-center">
                  <span class="text-indigo-600 font-semibold text-sm">ETF</span>
                </div>
                <div>
                  <h3 class="font-medium text-slate-800">S&P 500 ETF</h3>
                  <p class="text-slate-500 text-sm">25 shares • Index Fund</p>
                </div>
              </div>
              <div class="text-right">
                <p class="font-medium text-slate-800">€3,000.00</p>
                <p class="text-green-600 text-sm">+€250.00 (+9.1%)</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Recent Transactions -->
        <div class="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
          <div class="flex justify-between items-center mb-6">
            <h2 class="text-xl font-bold text-slate-800">Recent Transactions</h2>
            <button class="text-primary-600 hover:text-primary-700 text-sm font-medium">View All</button>
          </div>
          
          <div class="space-y-4">
            <!-- Transaction Item -->
            <div class="flex items-center justify-between p-4 border border-slate-200 rounded-lg">
              <div class="flex items-center space-x-4">
                <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="w-5 h-5 text-green-600">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.236 4.53L8.107 10.5a.75.75 0 00-1.214 1.029l1.5 2.25a.75.75 0 001.214 0l3.75-5.25z" clip-rule="evenodd" />
                  </svg>
                </div>
                <div>
                  <h3 class="font-medium text-slate-800">Buy AAPL</h3>
                  <p class="text-slate-500 text-sm">15 shares at €175.43</p>
                </div>
              </div>
              <div class="text-right">
                <p class="font-medium text-slate-800">€2,631.45</p>
                <p class="text-slate-500 text-sm">Today, 10:32 AM</p>
              </div>
            </div>

            <!-- Transaction Item -->
            <div class="flex items-center justify-between p-4 border border-slate-200 rounded-lg">
              <div class="flex items-center space-x-4">
                <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="w-5 h-5 text-blue-600">
                    <path d="M10.75 10.818v2.614A3.13 3.13 0 0011.888 13c.482-.315.612-.648.612-.875 0-.227-.13-.56-.612-.875a3.13 3.13 0 00-1.138-.432zM8.33 8.62c.053.055.115.11.184.164.208.16.46.284.736.363V6.603a2.45 2.45 0 00-.35.13c-.14.065-.27.143-.386.233-.377.292-.514.627-.514.909 0 .184.058.39.202.592.037.051.08.102.128.152z" />
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-6a.75.75 0 01.75.75v.316a3.78 3.78 0 011.653.713c.426.33.744.74.925 1.2a.75.75 0 01-1.395.55 1.35 1.35 0 00-.447-.563 2.187 2.187 0 00-.736-.363V9.3c.698.093 1.383.32 1.959.696.787.514 1.29 1.27 1.29 2.13 0 .86-.504 1.616-1.29 2.13-.576.377-1.261.603-1.96.696v.299a.75.75 0 11-1.5 0v-.3c-.697-.092-1.382-.318-1.958-.695-.482-.315-.857-.717-1.078-1.188a.75.75 0 111.359-.636c.08.173.245.376.54.569.313.205.706.353 1.138.432v-2.748a3.782 3.782 0 01-1.653-.713C6.9 9.433 6.5 8.681 6.5 7.875c0-.805.4-1.558 1.097-2.096a3.78 3.78 0 011.653-.713V4.75A.75.75 0 0110 4z" clip-rule="evenodd" />
                  </svg>
                </div>
                <div>
                  <h3 class="font-medium text-slate-800">Deposit</h3>
                  <p class="text-slate-500 text-sm">Bank transfer</p>
                </div>
              </div>
              <div class="text-right">
                <p class="font-medium text-slate-800">€2,500.00</p>
                <p class="text-slate-500 text-sm">Jun 2, 9:45 AM</p>
              </div>
            </div>

            <!-- Transaction Item -->
            <div class="flex items-center justify-between p-4 border border-slate-200 rounded-lg">
              <div class="flex items-center space-x-4">
                <div class="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="w-5 h-5 text-red-600">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.28 7.22a.75.75 0 00-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 101.06 1.06L10 11.06l1.72 1.72a.75.75 0 101.06-1.06L11.06 10l1.72-1.72a.75.75 0 00-1.06-1.06L10 8.94 8.28 7.22z" clip-rule="evenodd" />
                  </svg>
                </div>
                <div>
                  <h3 class="font-medium text-slate-800">Sell TSLA</h3>
                  <p class="text-slate-500 text-sm">10 shares at €255.00</p>
                </div>
              </div>
              <div class="text-right">
                <p class="font-medium text-slate-800">€2,550.00</p>
                <p class="text-slate-500 text-sm">May 28, 2:15 PM</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Right Column -->
      <div class="space-y-8">
        <!-- Asset Allocation -->
        <div class="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
          <h2 class="text-xl font-bold text-slate-800 mb-6">Asset Allocation</h2>
          <div class="space-y-4">
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <div class="w-4 h-4 bg-blue-500 rounded-full"></div>
                <span class="text-slate-700">Technology</span>
              </div>
              <span class="font-medium text-slate-800">65.2%</span>
            </div>
            <div class="w-full bg-slate-200 rounded-full h-2">
              <div class="bg-blue-500 h-2 rounded-full" style="width: 65.2%"></div>
            </div>

            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <div class="w-4 h-4 bg-green-500 rounded-full"></div>
                <span class="text-slate-700">Fixed Income</span>
              </div>
              <span class="font-medium text-slate-800">13.9%</span>
            </div>
            <div class="w-full bg-slate-200 rounded-full h-2">
              <div class="bg-green-500 h-2 rounded-full" style="width: 13.9%"></div>
            </div>

            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <div class="w-4 h-4 bg-purple-500 rounded-full"></div>
                <span class="text-slate-700">Automotive</span>
              </div>
              <span class="font-medium text-slate-800">17.1%</span>
            </div>
            <div class="w-full bg-slate-200 rounded-full h-2">
              <div class="bg-purple-500 h-2 rounded-full" style="width: 17.1%"></div>
            </div>

            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <div class="w-4 h-4 bg-indigo-500 rounded-full"></div>
                <span class="text-slate-700">Index Funds</span>
              </div>
              <span class="font-medium text-slate-800">4.0%</span>
            </div>
            <div class="w-full bg-slate-200 rounded-full h-2">
              <div class="bg-indigo-500 h-2 rounded-full" style="width: 4.0%"></div>
            </div>
          </div>
        </div>

        <!-- Risk Analysis -->
        <div class="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
          <h2 class="text-xl font-bold text-slate-800 mb-6">Risk Analysis</h2>
          <div class="space-y-4">
            <div class="flex justify-between items-center">
              <span class="text-slate-600">Volatility</span>
              <span class="font-medium text-slate-800">18.5%</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-slate-600">Beta</span>
              <span class="font-medium text-slate-800">1.12</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-slate-600">Sharpe Ratio</span>
              <span class="font-medium text-slate-800">1.34</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-slate-600">Max Drawdown</span>
              <span class="font-medium text-red-600">-12.3%</span>
            </div>
          </div>
          
          <div class="mt-6 p-4 bg-amber-50 border border-amber-200 rounded-lg">
            <div class="flex items-start space-x-3">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="w-5 h-5 text-amber-600 mt-0.5">
                <path fill-rule="evenodd" d="M8.485 2.495c.673-1.167 2.357-1.167 3.03 0l6.28 10.875c.673 1.167-.17 2.625-1.516 2.625H3.72c-1.347 0-2.189-1.458-1.515-2.625L8.485 2.495zM10 5a.75.75 0 01.75.75v3.5a.75.75 0 01-1.5 0v-3.5A.75.75 0 0110 5zm0 9a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd" />
              </svg>
              <div>
                <h4 class="font-medium text-amber-800">Risk Alert</h4>
                <p class="text-amber-700 text-sm mt-1">Your portfolio has high concentration in technology sector. Consider diversification.</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Quick Actions -->
        <div class="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
          <h2 class="text-xl font-bold text-slate-800 mb-6">Quick Actions</h2>
          <div class="space-y-3">
            <button class="w-full px-4 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="w-5 h-5 mr-2">
                <path d="M10.75 10.818v2.614A3.13 3.13 0 0011.888 13c.482-.315.612-.648.612-.875 0-.227-.13-.56-.612-.875a3.13 3.13 0 00-1.138-.432zM8.33 8.62c.053.055.115.11.184.164.208.16.46.284.736.363V6.603a2.45 2.45 0 00-.35.13c-.14.065-.27.143-.386.233-.377.292-.514.627-.514.909 0 .184.058.39.202.592.037.051.08.102.128.152z" />
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-6a.75.75 0 01.75.75v.316a3.78 3.78 0 011.653.713c.426.33.744.74.925 1.2a.75.75 0 01-1.395.55 1.35 1.35 0 00-.447-.563 2.187 2.187 0 00-.736-.363V9.3c.698.093 1.383.32 1.959.696.787.514 1.29 1.27 1.29 2.13 0 .86-.504 1.616-1.29 2.13-.576.377-1.261.603-1.96.696v.299a.75.75 0 11-1.5 0v-.3c-.697-.092-1.382-.318-1.958-.695-.482-.315-.857-.717-1.078-1.188a.75.75 0 111.359-.636c.08.173.245.376.54.569.313.205.706.353 1.138.432v-2.748a3.782 3.782 0 01-1.653-.713C6.9 9.433 6.5 8.681 6.5 7.875c0-.805.4-1.558 1.097-2.096a3.78 3.78 0 011.653-.713V4.75A.75.75 0 0110 4z" clip-rule="evenodd" />
              </svg>
              Add Funds
            </button>
            
            <button class="w-full px-4 py-3 bg-white border border-slate-300 text-slate-700 rounded-lg hover:bg-slate-50 transition-colors flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="w-5 h-5 mr-2">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.236 4.53L8.107 10.5a.75.75 0 00-1.214 1.029l1.5 2.25a.75.75 0 001.214 0l3.75-5.25z" clip-rule="evenodd" />
              </svg>
              Place Order
            </button>
            
            <button class="w-full px-4 py-3 bg-white border border-slate-300 text-slate-700 rounded-lg hover:bg-slate-50 transition-colors flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="w-5 h-5 mr-2">
                <path fill-rule="evenodd" d="M4.93 1.31a41.401 41.401 0 0110.14 0C16.194 1.45 17 2.414 17 3.517V18.25a.75.75 0 01-1.075.676l-2.8-1.344-2.8 1.344a.75.75 0 01-.65 0l-2.8-1.344-2.8 1.344A.75.75 0 013 18.25V3.517c0-1.103.806-2.068 1.93-2.207zm8.85 5.97a.75.75 0 00-1.06-1.06l-6.5 6.5a.75.75 0 101.06 1.06l6.5-6.5zM9 8a1 1 0 11-2 0 1 1 0 012 0zm3 5a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd" />
              </svg>
              Generate Report
            </button>
            
            <button class="w-full px-4 py-3 bg-white border border-slate-300 text-slate-700 rounded-lg hover:bg-slate-50 transition-colors flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="w-5 h-5 mr-2">
                <path fill-rule="evenodd" d="M7.84 1.804A1 1 0 018.82 1h2.36a1 1 0 01.98.804l.331 1.652a6.993 6.993 0 011.929 1.115l1.598-.54a1 1 0 011.186.447l1.18 2.044a1 1 0 01-.205 1.251l-1.267 1.113a7.047 7.047 0 010 2.228l1.267 1.113a1 1 0 01.206 1.25l-1.18 2.045a1 1 0 01-1.187.447l-1.598-.54a6.993 6.993 0 01-1.929 1.115l-.33 1.652a1 1 0 01-.98.804H8.82a1 1 0 01-.98-.804l-.331-1.652a6.993 6.993 0 01-1.929-1.115l-1.598.54a1 1 0 01-1.186-.447l-1.18-2.044a1 1 0 01.205-1.251l1.267-1.114a7.05 7.05 0 010-2.227L1.821 7.773a1 1 0 01-.206-1.25l1.18-2.045a1 1 0 011.187-.447l1.598.54A6.993 6.993 0 017.51 3.456l.33-1.652zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd" />
              </svg>
              Settings
            </button>
          </div>
        </div>

        <!-- Market News -->
        <div class="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
          <h2 class="text-xl font-bold text-slate-800 mb-6">Market News</h2>
          <div class="space-y-4">
            <div class="border-l-4 border-blue-500 pl-4">
              <h4 class="font-medium text-slate-800 text-sm">Apple Reports Strong Q2 Earnings</h4>
              <p class="text-slate-500 text-xs mt-1">2 hours ago</p>
            </div>
            <div class="border-l-4 border-green-500 pl-4">
              <h4 class="font-medium text-slate-800 text-sm">Tech Stocks Rally on AI News</h4>
              <p class="text-slate-500 text-xs mt-1">4 hours ago</p>
            </div>
            <div class="border-l-4 border-amber-500 pl-4">
              <h4 class="font-medium text-slate-800 text-sm">Federal Reserve Holds Rates Steady</h4>
              <p class="text-slate-500 text-xs mt-1">1 day ago</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    // Portfolio Performance Chart
    const ctx = document.getElementById('portfolioChart').getContext('2d');
    new Chart(ctx, {
      type: 'line',
      data: {
        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
        datasets: [{
          label: 'Portfolio Value',
          data: [65000, 67500, 66000, 70000, 72500, 74557],
          borderColor: '#0ea5e9',
          backgroundColor: 'rgba(14, 165, 233, 0.1)',
          borderWidth: 2,
          fill: true,
          tension: 0.4
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          }
        },
        scales: {
          y: {
            beginAtZero: false,
            grid: {
              color: '#f1f5f9'
            },
            ticks: {
              callback: function(value) {
                return '€' + value.toLocaleString();
              }
            }
          },
          x: {
            grid: {
              color: '#f1f5f9'
            }
          }
        }
      }
    });
  </script>
<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
$cp = Connection::getDataFromDatabase("SELECT e.isin, cpnaz, cpnazskratka, eqid, sc.reason FROM dbequity e LEFT JOIN sanctionlist sc ON sc.isin = e.isin WHERE archiv = 'f' LIMIT 50", defaultDB)[1];

?>
<form hx-post="/api/cp/getFilteredCPListData" id="cpListSearchForm" hx-target="#CPListBody"
    class="flex mb-2 w-full items-center">
    <div class="relative w-full bg-white shadow-md dark:bg-gray-800 sm:rounded-lg">
        <div
            class="flex flex-col w-full items-start justify-between p-4 space-y-3 md:flex-row md:space-y-0 md:space-x-4">
            <div class="w-full md:w-1/2 flex items-center gap-4">
                <label for="simple-search" class="sr-only">Vyhľadávanie</label>
                <div class="relative flex gap-4 items-center w-full">
                    <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                        <svg aria-hidden="true" class="w-5 h-5 text-gray-500 dark:text-gray-400" fill="currentColor"
                            viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd"
                                d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                                clip-rule="evenodd" />
                        </svg>
                    </div>
                    <input type="text" id="searchBarCPList" name="search"
                        class="block w-full p-2 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                        placeholder="Vyhľadávanie...">
                    <div id="resetSearchCPList"
                        class="absolute hidden text-gray-500 hover:text-gray-900 transition-all cursor-pointer"
                        style="right: .7rem">
                        <svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24"
                            height="24" fill="currentColor" viewBox="0 0 24 24">
                            <path fill-rule="evenodd"
                                d="M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10-4.477 10-10 10S2 17.523 2 12Zm7.707-3.707a1 1 0 0 0-1.414 1.414L10.586 12l-2.293 2.293a1 1 0 1 0 1.414 1.414L12 13.414l2.293 2.293a1 1 0 0 0 1.414-1.414L13.414 12l2.293-2.293a1 1 0 0 0-1.414-1.414L12 10.586 9.707 8.293Z"
                                clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div id="loadingSearchCPList"
                        class="absolute hidden text-gray-500 hover:text-gray-900 transition-all cursor-pointer"
                        style="right: 2rem">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide animate-spin w-4 h-4 lucide-loader-circle">
                            <path d="M21 12a9 9 0 1 1-6.219-8.56" />
                        </svg>
                    </div>
                </div>
                <div class="flex items-center w-full space-x-3 md:w-auto">
                    <button id="cpListDropdownButton" data-dropdown-toggle="dropdownCPlist"
                        class="flex items-center justify-center w-full px-4 py-2 text-sm font-medium text-gray-900 bg-white border border-gray-200 rounded-lg md:w-auto focus:outline-none hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                        type="button">
                        <svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" id="filterIcon"
                            class="w-4 h-4 mr-2 text-gray-400" viewbox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd"
                                d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z"
                                clip-rule="evenodd" />
                        </svg>
                        <span id="filterText">Filtrovať</span>
                        <svg class="-mr-1 ml-1.5 w-5 h-5" fill="currentColor" viewbox="0 0 20 20"
                            xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                            <path clip-rule="evenodd" fill-rule="evenodd"
                                d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" />
                        </svg>
                    </button>
                    <div id="dropdownCPlist" style="min-width: 20vw;"
                        class="z-10 hidden p-3 bg-white rounded-lg shadow">
                        <div class="w-full">
                            <ul class="space-y-2 text-sm" aria-labelledby="dropdownCPlist">
                                <li class="flex items-center">
                                    <label for="bonds"
                                        class="ml-2 text-sm font-medium text-gray-900 flex items-center gap-2 dark:text-gray-100">
                                        <input id="bonds" name="bonds" type="checkbox" value="Bonds"
                                            class="filter-checkbox w-4 h-4 bg-gray-100 border-gray-300 rounded text-primary-600 focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-700 focus:ring-2 dark:bg-gray-600 dark:border-gray-500" />
                                        <span>Dlhopisy</span>
                                    </label>
                                </li>
                                <li class="flex items-center">
                                    <label for="shares"
                                        class="ml-2 text-sm font-medium text-gray-900 flex items-center gap-2 dark:text-gray-100">
                                        <input id="shares" name="shares" type="checkbox" value="Shares"
                                            class="filter-checkbox w-4 h-4 bg-gray-100 border-gray-300 rounded text-primary-600 focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-700 focus:ring-2 dark:bg-gray-600 dark:border-gray-500" />
                                        <span>Akcie</span>
                                    </label>
                                </li>
                                <li class="flex items-center">
                                    <label for="shares"
                                        class="ml-2 text-sm font-medium text-gray-900 flex items-center gap-2 dark:text-gray-100">
                                        <input id="depo" name="depo" type="checkbox" value="Depo"
                                            class="filter-checkbox w-4 h-4 bg-gray-100 border-gray-300 rounded text-primary-600 focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-700 focus:ring-2 dark:bg-gray-600 dark:border-gray-500" />
                                        <span>Depozitné certifikáty</span>
                                    </label>
                                </li>
                                <li class="flex items-center">
                                    <label for="fonds"
                                        class="ml-2 text-sm font-medium text-gray-900 flex items-center gap-2 dark:text-gray-100">
                                        <input id="fonds" name="fonds" type="checkbox" value="Fonds"
                                            class="filter-checkbox w-4 h-4 bg-gray-100 border-gray-300 rounded text-primary-600 focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-700 focus:ring-2 dark:bg-gray-600 dark:border-gray-500" />
                                        <span>Podielové fondy</span>
                                    </label>
                                </li>
                            </ul>
                        </div>
                        <div class="w-full flex items-center justify-end">
                            <button id="resetFilter" type="button"
                                class="px-3 hidden py-2 text-xs font-medium text-center text-white bg-blue-700 rounded-lg hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                                Zrušiť filter <b>X</b></button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    </div>
    <button id="submitFilterFormCP" type="submit" class="hidden h-0 w-0">s</button>
</form>
<table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
    <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
        <tr>
            <th scope="col" class="px-4 py-3">ISIN</th>
            <th scope="col" class="px-4 py-3">Názov</th>
            <th scope="col" class="px-4 py-3">Typ</th>
            <th scope="col" class="px-4 py-3">Akcia</th>
        </tr>
    </thead>
    <tbody id="CPListBody">
        <?php foreach ($cp as $key => $dlhopis) {
            if ($dlhopis["reason"] === NULL) {
                ?>
                <tr id="rowsanction<?php echo $key; ?>" class="border-b dark:border-gray-600 hover:bg-gray-100">
                    <td class="px-4 py-2">
                        <span class="text-sm text-gray-900 font-bold px-2 py-0.5 rounded"><?php echo $dlhopis["isin"] ?></span>
                    </td>
                    <td class="px-4 py-2 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                        <?php echo $dlhopis["cpnaz"]; ?>
                    </td>
                    <td class="px-4 py-2 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                        <?php echo $dlhopis["eqid"]; ?>
                    </td>
                    <td class="px-4 py-2 font-medium text-gray-900 whitespace-nowrap relative dark:text-white">
                        <div id="sanction<?php echo $key; ?>tooltip"
                            class="flex z-20 bg-white items-center gap-4 sanctionTooltip absolute shadow-lg px-3 py-2 rounded-lg border"
                            style="width: 21rem; right: 1rem; top: 2.5rem; display: none;">
                            <form class="mb-0 flex w-full flex-col gap-2 items-center sanctionForm">
                                <input type="hidden" name="isin" name="isin" value="<?php echo $dlhopis["isin"] ?>" />
                                <section class="flex gap-2 items-center w-full">
                                    <div class="w-full">
                                        <label for="small-input"
                                            class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Dátum
                                            od</label>
                                        <input type="date" name="datefrom" required
                                            class="block w-full p-2 text-gray-900 border border-gray-300 rounded-lg bg-gray-50 text-xs focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                                    </div>
                                    <div class="w-full">
                                        <label for="small-input"
                                            class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Dátum
                                            do</label>
                                        <input type="date" name="dateto" 
                                            class="block w-full p-2 text-gray-900 border border-gray-300 rounded-lg bg-gray-50 text-xs focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                                    </div>
                                </section>
                                <div class="w-full">
                                    <label for="message"
                                        class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Dôvod
                                        sankcie</label>
                                    <textarea id="message" rows="4" name="reason" required
                                        class="block p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                        placeholder="Dôvod sankcie..."></textarea>
                                </div>
                                <button type="submit" id="createSanction"
                                    class="btn-primary w-full flex justify-center items-center gap-3"
                                    style="font-size: .75rem; line-height: 1rem;">
                                    <span>Pridať na sankčný zoznam</span>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24"
                                        fill="none" id="sanctionSpinner" stroke="currentColor" stroke-width="2"
                                        stroke-linecap="round" stroke-linejoin="round" style="display: none;"
                                        class="lucide animate-spin lucide-loader-circle">
                                        <path d="M21 12a9 9 0 1 1-6.219-8.56" />
                                    </svg>
                                </button>
                            </form>
                        </div>
                        <button id="sanction<?php echo $key; ?>" type="submit"
                            class="p-2 cursor-pointer sanctionBtn hover:bg-red-200 transition-all rounded-lg">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                                stroke="red" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                class="lucide lucide-ban">
                                <circle cx="12" cy="12" r="10" />
                                <path d="m4.9 4.9 14.2 14.2" />
                            </svg>
                            <svg id="archiveSpinner" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="display: none;"
                                stroke-linecap="round" stroke-linejoin="round" class="lucide animate-spin lucide-loader-circle">
                                <path d="M21 12a9 9 0 1 1-6.219-8.56" />
                            </svg>
                        </button>
                    </td>
                </tr>
            <?php }
        }
        ?>
    </tbody>
</table>
<script src="/src/assets/js/aktiva/cp/sanctionsCPList.js"></script>
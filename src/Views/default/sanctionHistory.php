<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

$cp = Connection::getDataFromDatabase("SELECT sc.*, e.cpnaz, e.eqid as eqid, u.username FROM sanctionhistory sc JOIN dbequity e ON e.isin = sc.isin JOIN users u ON u.userid = sc.userid", defaultDB)[1];
?>
<script src="/src/assets/js/aktiva/cp/table.js"></script>
<section class="p-5" style="margin-bottom: 8rem">
    <section class="flex justify-between items-center">
        <h3 class="text-2xl mb-4 font-semibold dark:text-white">Sankčná história cenných papierov</h3>
    </section>
    <div class="mx-auto">
        <form hx-post="/api/cp/getFilteredSanctionHistory" id="searchFrom" hx-target="tbody"
            class="flex mb-2 w-full items-center">
            <div class="relative w-full bg-white shadow-md dark:bg-gray-800 sm:rounded-lg">
                <div
                    class="flex flex-col w-full items-start justify-between p-4 space-y-3 md:flex-row md:space-y-0 md:space-x-4">
                    <div class="w-full md:w-1/2">
                        <label for="simple-search" class="sr-only">Vyhľadávanie</label>
                        <div class="relative flex items-center w-full">
                            <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                <svg aria-hidden="true" class="w-5 h-5 text-gray-500 dark:text-gray-400"
                                    fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd"
                                        d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                                        clip-rule="evenodd" />
                                </svg>
                            </div>
                            <input type="text" id="searchBar" name="search"
                                class="block w-full p-2 pl-10 text-sm border border-gray-300 rounded-lg bg-gray-50 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                placeholder="Vyhľadávanie...">
                            <div id="resetSearch"
                                class="absolute hidden text-gray-500 hover:transition-all cursor-pointer"
                                style="right: .7rem">
                                <svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24"
                                    height="24" fill="currentColor" viewBox="0 0 24 24">
                                    <path fill-rule="evenodd"
                                        d="M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10-4.477 10-10 10S2 17.523 2 12Zm7.707-3.707a1 1 0 0 0-1.414 1.414L10.586 12l-2.293 2.293a1 1 0 1 0 1.414 1.414L12 13.414l2.293 2.293a1 1 0 0 0 1.414-1.414L13.414 12l2.293-2.293a1 1 0 0 0-1.414-1.414L12 10.586 9.707 8.293Z"
                                        clip-rule="evenodd" />
                                </svg>

                            </div>
                        </div>
                    </div>
                    <div
                        class="flex flex-col items-stretch justify-end flex-shrink-0 w-full space-y-2 md:w-auto md:flex-row md:space-y-0 md:items-center md:space-x-3">
                        <div class="flex items-center w-full space-x-3 md:w-auto">
                            <button hx-get="/aktiva/sankcny-zoznam" hx-target="#pageContentMain" hx-replace-url="true"
                                class="bg-red-300 hover:bg-red-200 cursor-pointer transition-all flex items-center gap-2 p-2 rounded-md font-semibold text-sm px-4">
                                <span>Aktívne sankcie</span>
                                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="lucide lucide-shield-alert">
                                    <path
                                        d="M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z" />
                                    <path d="M12 8v4" />
                                    <path d="M12 16h.01" />
                                </svg>
                            </button>
                            <button id="filterDropdownButton" data-dropdown-toggle="filterDropdown"
                                class="flex items-center justify-center w-full px-4 py-2 text-sm font-medium bg-white border border-gray-200 rounded-lg md:w-auto focus:outline-none hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                                type="button">
                                <svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" id="filterIcon"
                                    class="w-4 h-4 mr-2 text-gray-400" viewbox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd"
                                        d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z"
                                        clip-rule="evenodd" />
                                </svg>
                                <span id="filterText">Filtrovať</span>
                                <svg class="-mr-1 ml-1.5 w-5 h-5" fill="currentColor" viewbox="0 0 20 20"
                                    xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                                    <path clip-rule="evenodd" fill-rule="evenodd"
                                        d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" />
                                </svg>
                            </button>
                            <div id="filterDropdown" style="min-width: 20vw;"
                                class="z-10 hidden p-3 bg-white dark:bg-gray-800 rounded-lg shadow">
                                <div class="w-full">
                                    <ul class="space-y-2 text-sm" aria-labelledby="dropdownDefault">
                                        <li class="flex items-center">
                                            <label for="bonds"
                                                class="ml-2 text-sm font-medium flex items-center gap-2 dark:text-gray-100">
                                                <input id="bonds" name="bonds" type="checkbox" value="Bonds"
                                                    class="filter-checkbox w-4 h-4 bg-gray-100 border-gray-300 rounded text-primary-600 focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-700 focus:ring-2 dark:bg-gray-600 dark:border-gray-500" />
                                                <span>Dlhopisy</span>
                                            </label>
                                        </li>
                                        <li class="flex items-center">
                                            <label for="shares"
                                                class="ml-2 text-sm font-medium flex items-center gap-2 dark:text-gray-100">
                                                <input id="shares" name="shares" type="checkbox" value="Shares"
                                                    class="filter-checkbox w-4 h-4 bg-gray-100 border-gray-300 rounded text-primary-600 focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-700 focus:ring-2 dark:bg-gray-600 dark:border-gray-500" />
                                                <span>Akcie</span>
                                            </label>
                                        </li>
                                        <li class="flex items-center">
                                            <label for="shares"
                                                class="ml-2 text-sm font-medium flex items-center gap-2 dark:text-gray-100">
                                                <input id="depo" name="depo" type="checkbox" value="Depo"
                                                    class="filter-checkbox w-4 h-4 bg-gray-100 border-gray-300 rounded text-primary-600 focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-700 focus:ring-2 dark:bg-gray-600 dark:border-gray-500" />
                                                <span>Depozitné certifikáty</span>
                                            </label>
                                        </li>
                                        <li class="flex items-center">
                                            <label for="fonds"
                                                class="ml-2 text-sm font-medium flex items-center gap-2 dark:text-gray-100">
                                                <input id="fonds" name="fonds" type="checkbox" value="Fonds"
                                                    class="filter-checkbox w-4 h-4 bg-gray-100 border-gray-300 rounded text-primary-600 focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-700 focus:ring-2 dark:bg-gray-600 dark:border-gray-500" />
                                                <span>Podielové fondy</span>
                                            </label>
                                        </li>
                                    </ul>
                                </div>
                                <div class="w-full flex items-center justify-end">
                                    <button id="resetFilter" type="button"
                                        class="px-3 hidden py-2 text-xs font-medium text-center text-white bg-blue-700 rounded-lg hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                                        Zrušiť filter <b>X</b></button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <button id="submitFilterForm" type="submit" class="hidden h-0 w-0">S</button>
        </form>
        <div class="overflow-x-visible">
            <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                    <tr>
                        <th scope="col" class="px-4 py-3">ISIN</th>
                        <th scope="col" class="px-4 py-3">Názov</th>
                        <th scope="col" class="px-4 py-3">Typ</th>
                        <th scope="col" class="px-4 py-3">Sankciovaný od</th>
                        <th scope="col" class="px-4 py-3">Sankciovaný do</th>
                        <th scope="col" class="px-4 py-3">Dôvod</th>
                        <th scope="col" class="px-4 py-3">Iniciátor</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($cp as $cpItem) { ?>
                        <tr class="border-b dark:border-gray-600 dark:hover:bg-gray-700 hover:bg-gray-100">
                            <td class="px-4 py-2">
                                <span
                                    class="text-sm font-bold px-2 py-0.5 rounded"><?php echo $cpItem["isin"] ?></span>
                            </td>
                            <td class="px-4 py-2 font-medium whitespace-nowrap dark:text-white">
                                <?php echo $cpItem["cpnaz"]; ?>
                            </td>
                            <td class="px-4 py-2 font-medium whitespace-nowrap dark:text-white">
                                <?php echo $cpItem["eqid"]; ?>
                            </td>
                            <td class="px-4 py-2 font-medium whitespace-nowrap dark:text-white">
                                <?php echo $cpItem["datefrom"]; ?>
                            </td>
                            <td class="px-4 py-2 font-medium whitespace-nowrap dark:text-white">
                                <?php echo $cpItem["dateto"]; ?>
                            </td>
                            <td class="px-4 py-2 font-medium whitespace-nowrap dark:text-white">
                                <span
                                    class="bg-gray-100 text-gray-800 text-xs font-medium me-2 px-2.5 py-0.5 rounded-md dark:bg-gray-700 dark:text-gray-400 border border-gray-500"><?php echo $cpItem["reason"]; ?></span>
                            </td>
                            <td class="px-4 py-2 font-medium whitespace-nowrap dark:text-white">
                                <a href="/pouzivatelia/detail/<?php echo $cpItem["userid"]; ?>"
                                    class="flex items-center pl-3 gap-2 p-0.5 rounded-lg hover:bg-gray-200 hover:underline cursor-pointer transition-all">
                                    <div
                                        class="relative w-5 h-5 overflow-hidden bg-gray-200 flex items-center justify-center rounded-full dark:bg-gray-600">
                                        <svg class="w-5 h-5 text-white bg-blue-500" fill="currentColor" viewBox="0 0 20 20"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd"
                                                d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd">
                                            </path>
                                        </svg>
                                    </div>
                                    <small><?php echo $cpItem["username"] ?></small>
                                </a>
                            </td>
                        </tr>
                        <?php
                    } ?>
                </tbody>
            </table>
        </div>
    </div>
</section>
<div id="toast" class="opacity-0 hidden absolute right-5 bottom-1"></div>
<div id="default-modal" tabindex="-1" aria-hidden="true" style="background: #1a253367;"
    class="hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 max-h-full">
    <div class="relative p-4 w-2/3 max-h-full">
        <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
            <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600 shadow-md">
                <h3 class="text-xl font-semibold dark:text-white">
                    Zoznam všetkých cenných papierov (nearchivované)
                </h3>
                <button type="button"
                    class="text-gray-400 bg-transparent hover:bg-gray-200 hover:rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
                    data-modal-hide="default-modal">
                    <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                        viewBox="0 0 14 14">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                    </svg>
                    <span class="sr-only">Close modal</span>
                </button>
            </div>
            <section id="cpList" class="p-4 relative overflow-x-hidden no-scrollbar" style="height:85vh;">
                <div id="loading" class="animate-pulse flex-col text-center gap-6 items-center py-24 justify-center">
                    <div class="flex items-center justify-center" role="status">
                        <svg aria-hidden="true"
                            class="inline w-16 h-16 text-gray-200 animate-spin dark:text-gray-600 fill-blue-600"
                            viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                                fill="currentColor" />
                            <path
                                d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                                fill="currentFill" />
                        </svg>
                    </div>
                </div>
            </section>
        </div>
    </div>
</div>
<?php
foreach ($cp as $key => $item) { ?>
    <div id="editModal<?php echo $item["isin"]; ?>" tabindex="-1" aria-hidden="true" style="background: #1a253367;"
        class="hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 max-h-full">
        <div class="relative p-4 w-5/3 max-h-full">
            <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
                <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600 shadow-md">
                    <h3 class="text-xl font-semibold dark:text-white">
                        Editácia sankcie cenného papiera [<?php echo $item["isin"]; ?>]
                    </h3>
                    <button type="button"
                        class="text-gray-400 bg-transparent hover:bg-gray-200 hover:rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
                        data-modal-hide="editModal<?php echo $item["isin"]; ?>">
                        <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                            viewBox="0 0 14 14">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                        </svg>
                        <span class="sr-only">Close modal</span>
                    </button>
                </div>
                <section id="cpList" class="p-4 relative overflow-x-hidden no-scrollbar">
                    <form class="mb-0 flex w-full flex-col gap-2 items-center sanctionForm">
                        <input type="hidden" name="isin" name="isin" value="<?php echo $item["isin"] ?>" />
                        <input type="hidden" name="id" name="id" value="<?php echo $item["id"]; ?>" />
                        <section class="flex gap-2 items-center w-full">
                            <div class="w-full">
                                <label for="small-input"
                                    class="block mb-2 text-sm font-medium dark:text-white">Dátum
                                    od</label>
                                <input type="date" name="datefrom" required value="<?php echo $item["datefrom"]; ?>"
                                    class="block w-full p-2 border border-gray-300 rounded-lg bg-gray-50 text-xs focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                            </div>
                            <div class="w-full">
                                <label for="small-input"
                                    class="block mb-2 text-sm font-medium dark:text-white">Dátum
                                    do</label>
                                <input type="date" name="dateto" value="<?php echo $item["dateto"]; ?>"
                                    class="block w-full p-2 border border-gray-300 rounded-lg bg-gray-50 text-xs focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                            </div>
                        </section>
                        <div class="w-full">
                            <label for="message" class="block mb-2 text-sm font-medium dark:text-white">Dôvod
                                sankcie</label>
                            <textarea id="message" rows="4" name="reason" required value="<?php echo $item["reason"]; ?>"
                                class="block p-2.5 w-full text-sm bg-gray-50 rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                placeholder="Dôvod sankcie..."><?php echo $item["reason"]; ?></textarea>
                        </div>
                        <button type="submit" id="createSanction"
                            class="btn-primary w-full flex justify-center items-center gap-3"
                            style="font-size: .75rem; line-height: 1rem;">
                            <span>Aktualizovať sankciu</span>
                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none"
                                id="sanctionSpinner" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" style="display: none;"
                                class="lucide animate-spin lucide-loader-circle">
                                <path d="M21 12a9 9 0 1 1-6.219-8.56" />
                            </svg>
                        </button>
                    </form>
                </section>
            </div>
        </div>
    </div>
<?php } ?>
<script>
    $(".sanctionForm").on("submit", (e) => {
        e.preventDefault();
        console.log(e.currentTarget.createSanction);
        const formData = new FormData(e.currentTarget);
        const isin = formData.getAll("isin");
        const datefrom = formData.get("datefrom");
        const dateto = formData.get("dateto");
        const reason = formData.get("reason");
        const id = formData.get("id");

        e.currentTarget.createSanction.children[0].innerHTML = "Aktualizujem...";
        e.currentTarget.createSanction.children[1].style.display = "inline-flex";

        console.log(formData);

        htmx.ajax('POST', `/api/cp/updateSanction`,
            {
                target: "#toast",
                values: {
                    "isin": isin,
                    "datefrom": datefrom,
                    "dateto": dateto,
                    "reason": reason,
                    "id": id
                }
            }).then((response) => {

            });
    });

    document.addEventListener('htmx:afterSettle', function (evt) {
        $(".deleteSanction").on("click", (e) => {
            e.preventDefault();
            const isin = e.currentTarget.id;
            e.currentTarget.children[0].style.display = "none";
            e.currentTarget.children[1].style.display = "flex";

            htmx.ajax('POST', `/api/cp/deleteSanction`,
                {
                    target: "#toast",
                    values: {
                        "isin": isin,
                    }
                }).then((response) => {

                });
        });
        htmx.process(document.getElementById("#CPListBody"));
    });

    $("#deleteSanctionForm").on("submit", (e) => {
        e.preventDefault();
        const formData = new FormData(e.currentTarget);
        const isin = formData.get("isin");
        const dateto = formData.get("dateto");
        const datefrom = formData.get("datefrom");
        const reason = formData.get("reason");
        document.getElementById("deleteSanctionIcon").style.display = "none";
        document.getElementById("deleteSanctionSpinner").style.display = "flex";

        htmx.ajax('POST', `/api/cp/deleteSanction`,
            {
                target: "#toast",
                values: {
                    "isin": isin,
                    "dateto": dateto,
                    "datefrom": datefrom,
                    "reason": reason
                }
            }).then((response) => {

            });
    });
</script>
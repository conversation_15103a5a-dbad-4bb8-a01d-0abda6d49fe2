<?php
session_start();
$mode = $_SESSION["mode"]["mode"];
if ($mode === "admin") {
    include "client/clientView.php";
}
if ($mode === "client") {
    include "client/clientChoose.php";
}
if ($mode === "global") {
    include "global/globalView.php";
}
if ($mode === "defausssslt") { ?>
    <section class="grid grid-cols-6 gap-4 p-5" style="height: 85vh;">
        <div class="col-span-4">
            <!-- TradingView Widget BEGIN -->
            <div class="tradingview-widget-container">
                <div class="tradingview-widget-container__widget"></div>
                <div class="tradingview-widget-copyright"><a href="https://www.tradingview.com/" rel="noopener nofollow"
                        target="_blank"><span class="blue-text">Track all markets on TradingView</span></a></div>
                <script type="text/javascript" src="https://s3.tradingview.com/external-embedding/embed-widget-tickers.js"
                    async>
                        {
                            "symbols": [
                                {
                                    "proName": "FOREXCOM:SPXUSD",
                                    "title": "S&P 500 Index"
                                },
                                {
                                    "proName": "FOREXCOM:NSXUSD",
                                    "title": "US 100 Cash CFD"
                                },
                                {
                                    "proName": "FX_IDC:EURUSD",
                                    "title": "EUR to USD"
                                },
                                {
                                    "proName": "BITSTAMP:BTCUSD",
                                    "title": "Bitcoin"
                                },
                                {
                                    "proName": "BITSTAMP:ETHUSD",
                                    "title": "Ethereum"
                                }
                            ],
                                "isTransparent": false,
                                    "showSymbolLogo": true,
                                        "colorTheme": "dark",
                                            "locale": "en"
                        }
                    </script>
            </div>
            <!-- TradingView Widget END -->
            <!-- TradingView Widget BEGIN -->
            <div class="tradingview-widget-container">
                <div class="tradingview-widget-container__widget"></div>
                <div class="tradingview-widget-copyright"><a href="https://www.tradingview.com/" rel="noopener nofollow"
                        target="_blank"><span class="blue-text">Track all markets on TradingView</span></a></div>
                <script type="text/javascript"
                    src="https://s3.tradingview.com/external-embedding/embed-widget-stock-heatmap.js" async>
                        {
                            "exchanges": [],
                                "dataSource": "SPX500",
                                    "grouping": "sector",
                                        "blockSize": "market_cap_basic",
                                            "blockColor": "change",
                                                "locale": "en",
                                                    "symbolUrl": "",
                                                        "colorTheme": "dark",
                                                            "hasTopBar": true,
                                                                "isDataSetEnabled": true,
                                                                    "isZoomEnabled": true,
                                                                        "hasSymbolTooltip": true,
                                                                            "isMonoSize": false,
                                                                                "width": "100%",
                                                                                    "height": "100%"
                        }
                    </script>
            </div>
            <!-- TradingView Widget END -->
        </div>
        <div class="col-span-2">
            <div class="tradingview-widget-container">
                <div class="tradingview-widget-container__widget"></div>
                <div class="tradingview-widget-copyright"><a href="https://www.tradingview.com/" rel="noopener nofollow"
                        target="_blank"><span class="blue-text">Track all markets on TradingView</span></a></div>
                <script type="text/javascript" src="https://s3.tradingview.com/external-embedding/embed-widget-timeline.js"
                    async>
                        {
                            "feedMode": "all_symbols",
                                "isTransparent": false,
                                    "displayMode": "regular",
                                        "width": "100%",
                                            "height": "100%",
                                                "colorTheme": "dark",
                                                    "locale": "en"
                        }
                    </script>
            </div>
        </div>
    </section>
<?php }
?>
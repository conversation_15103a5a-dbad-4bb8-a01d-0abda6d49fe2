<section>
    <div class="mx-auto">
        <div id="resultofpost" class="sticky top-[88px] right-5 shadow">

        </div>
        <form id="zakladneUdajeForm" class="mb-10 p-4" hx-post="/api/update-spravca" hx-target="#resultofpost">
            <div class="flex gap-8">
                <div class="w-full">
                    <label for="name" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Názov</label>
                    <input type="text" name="name" id="name" disabled value="<?php echo $zakladneUdaje['nazov'] ?>"
                           class="border mb-2 border-gray-300 bg-gray-50 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                           placeholder="Názov firmy je nemenný" required="">
                    <label for="street"
                           class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Ulica</label>
                    <input type="text" name="street" id="street" disabled value="<?php echo $zakladneUdaje['address'] ?>"
                           class="border mb-2 border-gray-300 bg-gray-50 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                           placeholder="<?php echo "BUM" ?>" required="">
                    <label for="city"
                           class="block mb-2 mb-2 text-sm font-medium text-gray-900 dark:text-white">Mesto</label>
                    <input type="text" name="city" id="city" disabled value="<?php echo $zakladneUdaje['city'] ?>"
                           class="border border-gray-300 bg-gray-50 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                           placeholder="$2999" required="">
                    <label for="state"
                           class="block mb-2 mb-2 text-sm font-medium text-gray-900 dark:text-white">Štát</label>
                    <select id="state" disabled name="state"
                            class="border mb-2 border-gray-300 bg-gray-50 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                        <option selected="" value="<?php echo $zakladneUdaje['stateid']; ?>"><?php echo $state; ?></option>
                        <option value="TV">Česká republika</option>
                        <option value="PC">Maďarsko</option>
                        <option value="GA">Poľsko</option>
                        <option value="PH">Ukrajina</option>
                    </select>
                    <label for="psc"
                           class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">PSČ</label>
                    <input type="text" name="psc" id="psc" disabled value="<?php echo $zakladneUdaje['postalcode']; ?>"
                           class="mb-2 border border-gray-300 bg-gray-50 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                           placeholder="821 09" required="">
                </div>
                <div class="w-full">
                    <label for="shortname"
                           class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Skratka</label>
                    <input type="text" name="shortname" id="shortname" disabled value="<?php echo $zakladneUdaje['nazovshort']; ?>"
                           class="border mb-2 border-gray-300 bg-gray-50 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                           placeholder="Skratka" required="">
                    <label for="ico"
                           class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">IČO</label>
                    <input type="text" name="ico" id="ico" disabled value="<?php echo $zakladneUdaje['ico']; ?>"
                           class="border mb-2 border-gray-300 bg-gray-50 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                           placeholder="<?php echo "IČO" ?>" required="">
                    <label for="dic"
                           class="block mb-2 mb-2 text-sm font-medium text-gray-900 dark:text-white">DIČ</label>
                    <input type="text" name="dic" id="dic" disabled value="<?php echo $zakladneUdaje['dic']; ?>"
                           class="border border-gray-300 bg-gray-50 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                           placeholder="DIČ NIČ" required="">
                    <label for="mena"
                           class="block mb-2 mb-2 text-sm font-medium text-gray-900 dark:text-white">Ref. mena</label>
                    <select id="mena" disabled name="mena"
                            class="border mb-2 border-gray-300 bg-gray-50 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                        <option selected="" value="<?php echo $zakladneUdaje['refmena']; ?>"><?php echo $zakladneUdaje['refmena'] ?></option>
                        <option value="TV">CZK</option>
                        <option value="PC">PLN</option>
                        <option value="GA">USD</option>
                        <option value="PH">HU</option>
                    </select>
                    <label for="datumzalozenia"
                           class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Dátum založenia</label>
                    <input type="date" name="datumzalozenia" id="datumzalozenia" disabled value="<?php echo $zakladneUdaje['exdate']; ?>"
                           class="mb-2 border border-gray-300 bg-gray-50 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                           placeholder="821 09" required="">
                </div>
            </div>
            <h3 class="text-xl mb-4 mt-4 font-bold dark:text-white">Výnos</h3>
            <hr class="mb-4"/>
            <div class="flex gap-8">
                <label for="datumvznik" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Dátum vzniku
                    výnosu</label>
                <input type="date" name="datumvznik" id="datumvznik" disabled value="<?php echo $zakladneUdaje['vznikdate']; ?>"
                       class="border mb-2 border-gray-300 bg-gray-50 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                       placeholder="">
            </div>
            <h3 class="text-xl mb-4 mt-4 font-bold dark:text-white">Kontakt</h3>
            <hr class="mb-4"/>
            <div class="flex gap-8">
                <div class="w-full">
                    <label for="kontaktnazov" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Názov</label>
                    <input type="text" name="kontaktnazov" id="kontaktnazov" disabled value="<?php echo $zakladneUdaje['kontaktname']; ?>"
                           class="border mb-2 border-gray-300 bg-gray-50 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                           placeholder="">
                    <label for="kontakttelefon"
                           class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Telefón</label>
                    <input type="text" name="kontakttelefon" id="kontakttelefon" disabled value="<?php echo $zakladneUdaje['kontaktphonenumber']; ?>"
                           class="border mb-2 border-gray-300 bg-gray-50 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                           placeholder="">
                    <label for="kontaktfax" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Fax</label>
                    <input type="text" name="kontaktfax" id="kontaktfax" disabled value="<?php echo $zakladneUdaje['kontaktfaxnumber']; ?>"
                           class="border mb-2 border-gray-300 bg-gray-50 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                           placeholder="">
                </div>
                <div class="w-full">
                    <label for="kontaktmobil" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Mobil</label>
                    <input type="text" name="kontaktmobil" id="kontaktmobil" disabled value="<?php echo $zakladneUdaje['kontaktmobilnumber']; ?>"
                           class="border mb-2 border-gray-300 bg-gray-50 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                           placeholder="">
                    <label for="kontaktemail" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Email</label>
                    <input type="email" name="kontaktemail" id="kontaktemail" disabled value="<?php echo $zakladneUdaje['kontaktemail']; ?>"
                           class="border mb-2 border-gray-300 bg-gray-50 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                           placeholder="<EMAIL>">
                </div>
            </div>
            <h3 class="text-xl mb-4 mt-4 font-bold dark:text-white">Poznámka</h3>
            <hr class="mb-4"/>
            <div class="mb-10">
                <label for="poznamka" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Poznámka</label>
                <textarea id="poznamka" value="<?php echo $zakladneUdaje['note']; ?>" disabled
                          class="border mb-2 border-gray-300 bg-gray-50 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5"
                          name="poznamka" resizable="false" class="w-full" rows="10"></textarea>
            </div>
            <script src="/src/Controllers/spravca/js/zakladneUdaje.js"></script>
            <div id="btnWrapper" class="fixed flex justify-between items-center w-full bg-white py-2 border-t px-4 bottom-0" style="left: 15rem;">
                <button type="button" id="formBtn" hx-on:click="enableEditing();"
                        class="focus:outline-none flex mb-8 items-center text-white bg-green-700 hover:bg-green-800 focus:ring-4 focus:ring-green-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 dark:bg-green-600 dark:hover:bg-green-700 dark:focus:ring-green-800">
                    <svg class="w-4 h-4 mr-2 text-white dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 18">
                        <path d="M12.687 14.408a3.01 3.01 0 0 1-1.533.821l-3.566.713a3 3 0 0 1-3.53-3.53l.713-3.566a3.01 3.01 0 0 1 .821-1.533L10.905 2H2.167A2.169 2.169 0 0 0 0 4.167v11.666A2.169 2.169 0 0 0 2.167 18h11.666A2.169 2.169 0 0 0 16 15.833V11.1l-3.313 3.308Zm5.53-9.065.546-.546a2.518 2.518 0 0 0 0-3.56 2.576 2.576 0 0 0-3.559 0l-.547.547 3.56 3.56Z"/>
                        <path d="M13.243 3.2 7.359 9.081a.5.5 0 0 0-.136.256L6.51 12.9a.5.5 0 0 0 .59.59l3.566-.713a.5.5 0 0 0 .255-.136L16.8 6.757 13.243 3.2Z"/>
                    </svg>
                    Editovať dáta
                </button>
            </div>
        </form>
    </div>
</section>
<?php
require "/home/<USER>/www/src/lib/connection.php";
require "/home/<USER>/www/conf/settings.php";

$isinreal = isset($matches[1]) ? $matches[1] : null;
$query = "SELECT mt.pocet, p.c<PERSON><PERSON>, mt.subjektid, pod.podielnikid, pod.meno, pod.prieznaz, ka.kurz, e.cpnaz as cpnaz, COALESCE(e.nominalemisie, 1) as nominal, COALESCE(fk.faktor, 1) as faktor, fk.datefrom,
CASE WHEN mt.uctovnykod = 251110 THEN 1 ELSE 0 END AS ISBOND, mt.uctovnykod
from majetoktoday mt 
INNER JOIN portfolio p ON mt.subjektid = p.fondid
INNER JOIN podielnik pod ON p.podielnikid = pod.podielnikid
INNER JOIN dbequitycurrric drc ON mt.kodaktiva = drc.isincurrric
INNER JOIN dbequitycurr dc ON drc.isincurr = dc.isincurr
INNER JOIN dbequity e ON dc.isin = e.isin 
INNER JOIN kurzyaktivarchiv ka ON ka.datum = (SELECT MAX(datum) FROM kurzyaktivarchiv) AND drc.ric = ka.ric
LEFT JOIN floatkupon fk ON mt.kodaktiva = fk.isincurrric AND ka.datum >= fk.datefrom AND ka.datum <= fk.datetill 
WHERE e.isinreal = '$isinreal' AND uctovnykod NOT IN (251120)
ORDER BY p.cislozmluvy";


$isinRes = Connection::getDataFromDatabase($query, defaultDB);
$detail = $isinRes[1];
$pocetSum = 0;
?>
<section id="thisisit" class="px-5 pb-10">
    <div class="flex w-full gap-4 border-b items-center sticky z-20 p-4" style="top: 3.7rem;">
        <div hx-get="/prehlady/ucty/majetkovy-ucet" hx-target="#thisisit" hx-replace-url="true"
             class="p-1 rounded-lg cursor-pointer hover:bg-gray-500 transition-all" id="scrollToTop"
             style="rotate: 270deg;">
            <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                 width="24" height="24" fill="none" viewBox="0 0 24 24">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M12 6v13m0-13 4 4m-4-4-4 4"/>
            </svg>
        </div>

        <div class="flex gap-4 items-center">
            <h3 class="text-3xl font-bold dark:text-white">Detail majetkového účtu <strong
                        class="text-blue-500"><?php echo $detail[0]["cpnaz"] ?></strong></h3>
        </div>
    </div>


    <div class="relative overflow-x-auto mt-10">
        <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
            <thead class="text-xs text-gray-700 bg-gray-400 uppercase dark:bg-gray-700 dark:text-gray-400">
            <tr>
                <th scope="col" class="px-6 py-3">
                    Portfólio
                </th>
                <th scope="col" class="px-6 py-3">
                    Počet
                </th>
                <th scope="col" class="px-6 py-3">
                    Suma
                </th>
            </tr>
            </thead>
            <tbody>
            <?php foreach ($detail as $key => $value) {
                $pocetSum = $pocetSum + $value["pocet"];
                $sumaSumarum = $sumaSumarum + (intval($value["pocet"]) * intval($value["kurz"]) * intval($value["nominal"]) * intval($value["faktor"]));
                if ($value["isbond"] === 1) {
                    $sumaSumarum = $sumaSumarum / 100;
                }
                $cislozmluvy = $value["cislozmluvy"];
                $id_fond = $value["subjektid"];
                $clientID = $value["podielnikid"];
                $clientName = $value["meno"];
                $clientPrieznaz = $value["prieznaz"];
                ?>
                <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                    <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                        <span id="client" class="underline hover:no-underline cursor-pointer" onclick="runGoToPortfolio(event, '<?php echo $cislozmluvy; ?>', '<?php echo $id_fond; ?>', '<?php echo $clientID; ?>', '<?php echo $clientName; ?>', '<?php echo $clientPrieznaz; ?>', true)"><?php echo $detail_values["cislozmluvy"]; ?><?php echo $value["cislozmluvy"]; ?></span>
                    </th>
                    <td class="px-6 py-4">
                        <?php echo $value["pocet"]; ?>
                    </td>
                    <td class="px-6 py-4">
                        <?php echo $value["isbond"] === 1 ? intval($value["pocet"]) * intval($value["kurz"]) * intval($value["nominal"]) * intval($value["faktor"]) / 100 : intval($value["pocet"]) * intval($value["kurz"]) * intval($value["nominal"]) * intval($value["faktor"]); ?>
                    </td>
                </tr>
            <?php } ?>
            <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                <td scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                    <strong>Počet spolu: </strong>
                </td>
                <td scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                    <strong><?php echo $pocetSum; ?></strong>
                </td>
                <td scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                    <strong><?php echo $sumaSumarum; ?></strong>
                </td>
            </tr>
            </tbody>
        </table>
    </div>


</section

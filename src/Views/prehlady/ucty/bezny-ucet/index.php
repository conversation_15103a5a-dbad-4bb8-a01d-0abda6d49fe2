<?php
require "/home/<USER>/www/src/lib/connection.php";
require "/home/<USER>/www/conf/settings.php";
$today = Connection::getDataFromDatabase("SELECT TO_CHAR(max(datum), 'dd.mm.yyyy') as datum, max(datum) as datumdb FROM today", defaultDB)[1][0];
$todayDb = $today["datumdb"];
$today = $today["datum"];
$query = "SELECT
              mt.ucetaktiva,
              mt.mena AS mena,
              SUM(SIGN(md_d - 0.5) * (-1) * mt.pocet) AS pocet,
              p.fondid,
              p.cislozmluvy,
            po.meno as meno,
            po.prieznaz as priezvisko,
            po.podielnikid as podielnikid
        FROM
          majetoktoday mt, portfolio p, podielnik po
        WHERE
          mt.subjektid = p.fondid AND p.podielnikid = po.podielnikid AND mt.uctovnykod = 221110
        GROUP BY
          mt.kodaktiva, p.fondid, mt.ucetaktiva, p.cislozmluvy,mt.mena, po.meno, po.prieznaz, po.podielnikid
        ORDER BY
          p.cislozmluvy 
      ";

$getDataRes = Connection::getDataFromDatabase($query, defaultDB);
$initialData = $getDataRes[1];
$menyRes = Connection::getDataFromDatabase("SELECT DISTINCT mena FROM majetoktoday", defaultDB);
$meny = $menyRes[1];

$query = "SELECT ucetaktiva, sum(pocet * ((0.5 - md_d) / 0.5)) as objem, mena, eqid
    from majetoktoday
    where eqid in ('BU', 'TD')
    and subjektid > 1
    and uctovnykod in (221110)
    group by ucetaktiva, mena, eqid;";
$summaryRows = Connection::getDataFromDatabase($query, defaultDB)[1];

$detail = [];
$sums = [];

foreach ($initialData as $record) {
    $ucetaktiva = $record["ucetaktiva"];
    $fondid = $record["fondid"];
    $pocet = $record["pocet"];
    $mena = $record["mena"];
    $cislozmluvy = $record["cislozmluvy"];

    // Process detail array
    if (!isset($detail[$ucetaktiva][$fondid])) {
        $detail[$ucetaktiva][$fondid] = [
            "pocet" => 0,
            "mena" => $mena,
            "cislozmluvy" => $cislozmluvy,
        ];
    }
    $detail[$ucetaktiva][$fondid]["pocet"] += $pocet;
    $detail[$ucetaktiva][$fondid]["mena"] = $mena;
    $detail[$ucetaktiva][$fondid]["cislozmluvy"] = $cislozmluvy;
    $detail[$ucetaktiva][$fondid]["meno"] = $record["meno"] . " " . $record["priezvisko"];
    $detail[$ucetaktiva][$fondid]["id"] = $record["podielnikid"];
    // Process sums array
    if (!isset($sums[$ucetaktiva])) {
        $sums[$ucetaktiva] = [
            "pocet" => 0,
            "mena" => $mena,
        ];
    }

    $sums[$ucetaktiva]["pocet"] += $pocet;
    $sums[$ucetaktiva]["mena"] = $mena;

}
?>
<section class="pb-20 px-5">
    <div class="flex w-full justify-between border-b items-center sticky z-20 p-4">
        <div class="flex gap-4 items-center">
            <h3 class="text-3xl font-bold dark:text-white">Prehľady bežných účtov k <strong id="toDateHeading"
                    class="text-blue-800 dark:text-blue-500"><?php echo $today; ?></strong>
            </h3>
            <input type="hidden" name="today" id="todayInput" value="<?php echo $today; ?>" />
        </div>
    </div>
    <div class="mt-5 shadow-md sm:rounded-lg">
        <div class="p-4 bg-white dark:bg-gray-900">
            <section class="flex mb-3">
                <form id="searchUctyForm" class="mb-0 w-full flex items-center gap-4">
                    <input type="hidden" name="link" value="bezny-ucet" />
                    <section class="flex gap-4 items-center">
                        <label for="table-search" class="sr-only">Vyhľadávať</label>
                        <div class="relative flex items-center">
                            <div
                                class="absolute inset-y-0 rtl:inset-r-0 start-0 flex items-center ps-3 pointer-events-none">
                                <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" aria-hidden="true"
                                    xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                        stroke-width="2" d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z" />
                                </svg>
                            </div>
                            <div id="resetSearch"
                                class="absolute right-2 hidden cursor-pointer hover:bg-gray-500 transition-all rounded-full hover:text-white">
                                <svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24"
                                    height="24" fill="none" viewBox="0 0 24 24">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                        stroke-width="2" d="m15 9-6 6m0-6 6 6m6-3a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                                </svg>
                            </div>
                            <input type="text" id="uctySearch" name="uctySearch"
                                class="block p-2 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg w-80 bg-gray-50 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700"
                                placeholder="Vyhľadávanie">
                        </div>
                        <div class="w-52">
                            <select id="menySelect" name="mena"
                                class="bg-gray-50 dark:bg-gray-600 dark:text-gray-200 border border-gray-300 p-2 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full">
                                <option selected value="">Mena</option>
                                <?php foreach ($meny as $key => $value) {
                                    if ($value["mena"] == "") {
                                        continue;
                                    }
                                    ?>
                                    <option value="<?php echo $value["mena"] ?>"><?php echo $value["mena"]; ?></option>
                                <?php } ?>
                            </select>
                        </div>
                        <div class="flex items-center w-full justify-between gap-10">
                            <div class="flex items-center gap-5">
                                <div class="flex gap-4 items-center">
                                    <label for="date"
                                        class="block text-sm font-medium dark:text-gray-200 text-gray-900">Dátum:</label>
                                    <input type="date" id="date" name="date" class="bg-gray-50 dark:bg-gray-600 border dark:text-gray-200 border-gray-300 p-1.5 text-gray-900 text-sm rounded-lg focus:ring-blue-500
                                            focus:border-blue-500 block w-full" required />
                                </div>
                                <div class="flex items-center">
                                    <input id="summary" name="summary" type="checkbox" value=""
                                        class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500">
                                    <label for="summary"
                                        class="ms-2 text-sm font-medium text-gray-900 dark:text-gray-300">Súhrn</label>
                                </div>
                                <div class="flex items-center">
                                    <input id="spravca" name="spravca" type="checkbox" value=""
                                        class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500">
                                    <label for="spravca"
                                        class="ms-2 text-sm font-medium text-gray-900 dark:text-gray-300">Spravca</label>
                                </div>
                            </div>
                        </div>

                    </section>
                    <section class="w-full flex justify-end">
                        <button id="resetFilter" type="button"
                            class="hidden focus:outline-none text-white bg-red-700 hover:bg-red-800 focus:ring-4 flex items-center gap-2 focus:ring-red-300 font-medium rounded-lg text-xs px-4 py-1">
                            Resetovať filter
                            <svg class="w-4 h-4 text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                                width="24" height="24" fill="none" viewBox="0 0 24 24">
                                <path stroke="currentColor" stroke-linecap="round" stroke-width="2"
                                    d="M18.796 4H5.204a1 1 0 0 0-.753 1.659l5.302 6.058a1 1 0 0 1 .247.659v4.874a.5.5 0 0 0 .2.4l3 2.25a.5.5 0 0 0 .8-.4v-7.124a1 1 0 0 1 .247-.659l5.302-6.059c.566-.646.106-1.658-.753-1.658Z" />
                            </svg>
                        </button>
                    </section>
                </form>
            </section>
            <small id="resultData" class="text-gray-500">Počet výsledkov: <strong
                    id="resultCount"><?php echo $getDataRes[0]; ?></strong></small>
        </div>
        <div class="relative overflow-x-auto shadow-md sm:rounded-lg">
            <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
                <thead class="text-xs text-gray-700 uppercase bg-white dark:bg-gray-700 dark:text-gray-400">
                    <tr>
                        <th scope="col" class="px-6 py-3">
                            Portfólio
                        </th>
                        <th scope="col" class="px-6 py-3">
                            Meno
                        </th>
                        <th scope="col" class="px-6 py-3">
                            Mena
                        </th>
                        <th scope="col" class="px-6 py-3">
                            Suma
                        </th>
                        <th scope="col" class="px-6 py-3">
                            Podiel v %
                        </th>
                    </tr>
                </thead>
                <tbody id="uctiky">
                    <?php
                    foreach ($summaryRows as $item) {
                        $ucetaktiva = $item["ucetaktiva"];
                        $mena = $item["mena"];
                        $objem = $item["objem"];
                        ?>
                        <tr class="font-bold border-b bg-gray-200 text-gray-800 dark:bg-gray-900 dark:text-gray-100"
                            style="border-color: black;">
                            <td class="p-2 text-left"><?php echo $ucetaktiva ?></td>
                            <td>&nbsp;</td>
                            <td class="vlavo"><?php echo $mena ?></td>
                            <td class="vpravo">
                                <?php echo number_format((float) $objem, 2, '.', ' '); ?>
                            </td>
                            <td>&nbsp;</td>
                        </tr>
                        <?php foreach ($detail[$ucetaktiva] as $fondid => $detail_values) {
                            $cislozmluvy = $detail_values["cislozmluvy"];
                            $id_fond = $fondid;
                            $clientID = $detail_values["id"];
                            $clientName = $detail_values["meno"];
                            $clientPrieznaz = $detail_values["prieznaz"];
                            $podiel = round(floatval($objem), 2) != 0 ? round(floatval($detail_values["pocet"]), 2) / round(floatval($objem), 2) * 100 : 0;
                            ?>
                            <tr class="border-b dark:text-gray-200 dark:hover:bg-gray-600 transition-all hover:bg-gray-100">
                                <td class="pl-10 py-3 text-left font-bold">
                                    <span id="client" class="underline hover:no-underline cursor-pointer"
                                        onclick="runGoToPortfolio(event, '<?php echo $cislozmluvy; ?>', '<?php echo $id_fond; ?>', '<?php echo $clientID; ?>', '<?php echo $clientName; ?>', '<?php echo $clientPrieznaz; ?>', true)"><?php echo $detail_values["cislozmluvy"]; ?></span>
                                </td>
                                <td class="vlavo"><a class="underline hover:no-underline"
                                        href="/klienti/detail/<?php echo $detail_values["id"]; ?>"><?php echo $detail_values["meno"]; ?>
                                </td>
                                <td class="vlavo"><?php echo $detail_values["mena"]; ?></td>
                                <td class="vpravo"><?php echo number_format($detail_values["pocet"], 2, '.', ' '); ?></td>
                                <td class="vpravo">
                                    <?php echo number_format($podiel, 2); ?>
                                    %
                                </td>
                            </tr>
                        <?php }
                    } ?>
                </tbody>
            </table>
        </div>
    </div>
    <div class="hidden p-8 bg-red-400 text-white font-bold" id="noticeTable"></div>
</section>
<section id="clientResult"></section>
<script src="/src/assets/js/prehlady/ucty/table.js"></script>
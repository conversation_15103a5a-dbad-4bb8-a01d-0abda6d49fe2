<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
require_once "/home/<USER>/www/src/Components/inputs/multiSelect/multiSelect.php";


$udaje = Connection::getDataFromDatabase("select 
		p.fondid as fondid,
		p.cislozmluvy as cislozmluvy 
		from  
			fonds f, 
			portfolio p 
		where 
			p.fondid = f.fondid and
			f.template = 0 and
			f.archiv = 'f' 
		order by p.cislozmluvy", defaultDB);
$portfolia = $udaje[1];

$menadb = Connection::getDataFromDatabase("SELECT mena, poradie FROM menadb ORDER BY poradie", defaultDB);
$meny = $menadb[1];
?>
<section class="py-3 px-5">
    <div class="w-full mt-4 bg-white border border-gray-200 rounded-lg shadow dark:bg-gray-800 dark:border-gray-700">
        <div id="fullWidthTabContent" class="border-t border-gray-200 dark:border-gray-600">
            <div class="p-4 bg-white rounded-lg md:p-8 dark:bg-gray-800" id="hromadne" role="tabpanel"
                aria-labelledby="hromadne-tab">
                <form id="novyHromadnyVypis" method="POST" hx-target="#resultko" hx-post="/src/Views/prehlady/financne-toky/financne-toky.php"
                    class="flex flex-col items-center">
                    <section class="flex items-end mb-4 justify-center px-32 w-full gap-8" style="padding: 0 5vw;">
                        <div class="w-full">
                            <label for="dateTill"
                                class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Dátum</label>
                            <input type="date" id="dateTill" name="dateTill"
                                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                required />
                        </div>
                        <?php echo MultiSelect::render("Vybrať portfólio", $portfolia, "portfolio", ["cislozmluvy", "fondid"], "fondid"); ?>
                    </section>
                    <section class="flex items-end justify-center px-32 w-full gap-8" style="padding: 0 5vw;">
                        <div class="w-full">
                            <label for="meny"
                                class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Referenčná
                                mena</label>
                            <select id="meny" name="meny"
                                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                                <?php foreach ($meny as $mena) { ?>
                                    <option value="<?php echo $mena["mena"]; ?>"><?php echo $mena["mena"]; ?></option>
                                <?php } ?>
                            </select>
                        </div>
                        <div class="flex items-center w-full mb-3">
                            <input id="includeAwait" type="checkbox" name="includeAwait"
                                class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                            <label for="includeAwait"
                                class="ms-2 text-sm font-medium text-gray-900 dark:text-gray-300">Zahrnúť
                                očakávané finančné toky</label>
                        </div>
                        <button type="submit"
                            class="focus:outline-none text-white bg-green-700 hover:bg-green-800 focus:ring-4 mb-0.5 focus:ring-green-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-green-600 dark:hover:bg-green-700 dark:focus:ring-green-800">
                            Zobraziť
                        </button>
                    </section>
                </form>
            </div>
        </div>
    </div>
</section>
<section id="resultko" class="px-5"></section>
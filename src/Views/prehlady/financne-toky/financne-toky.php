<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
$dbdate = $_POST["dateTill"];
$fondid = explode(",", $_POST["portfoliovalues"]);
$includeAwait = $_POST['includeAwait'];
function getDBSums(string $eqIDs, string $uctovnykodList, bool $zavazky): string
{
    global $fond, $dbdate;
    if ($zavazky) {
        $query = "SELECT mt.sumadenom * CASE WHEN mt.md_d < 0.5 THEN -1 ELSE 1 END  AS sumadenom
                               FROM majetoktotal mt
                                        LEFT JOIN dbequity d ON d.isin = SUBSTRING(mt.kodaktiva FROM 1 FOR 12)
                                        LEFT JOIN navuctovanie n ON mt.uctovnykod = n.uctovnykod
                               WHERE mt.datum = '$dbdate'::DATE
                                 AND mt.subjektid in ($fond)
                                 AND ((n.uctovnykod::text) LIKE '315%' OR (n.uctovnykod::text) LIKE '325%' OR
                                      (n.uctovnykod::text) LIKE '261%')
                                 AND mt.uctovnykod NOT IN (315122, 325122)
                                 AND NOT EXISTS (SELECT 1
                                                 FROM majetoktotal mt2
                                                 WHERE mt2.datum = mt.datum
                                                   AND mt2.uctovnykod IN (221110, 221210)
                                                   AND mt.uctovnykod IN (315132, 315113)
                                                   AND mt.eqid = mt2.eqid
                                                   AND mt.subjektid = mt2.subjektid
                                                   AND mt.kodaktiva = mt2.kodaktiva
                                                   AND mt.ucetaktiva = mt2.ucetaktiva)";
    } else {
        $filter = "uctovnykod in ($uctovnykodList) AND";
        $filter .= match ($eqIDs) {
            "'Shares'" => " d.druheqid not in (8,15,17) and ",
            "'Fonds','Shares'" => " (d.eqid = 'Fonds' or (d.druheqid in (8,15,17) and d.eqid='Shares' )) and",
            default => " mt.eqid in ($eqIDs) and ",
        };

        $query = "SELECT SUM(sumadenom * sign(0.5-md_d)) as sumadenom
        from majetoktotal mt
        LEFT JOIN dbequity d ON d.isin = SUBSTRING(mt.kodaktiva FROM 1 FOR 12)
        where $filter datum = to_date('2023-12-31', 'YYYY-MM-DD')";
        $query .= " and subjektid in($fond)
        group by mt.menadenom";
    }
    $getSumsRes = Connection::getDataFromDatabase($query, defaultDB);
    $sums = $getSumsRes[1][0]['sumadenom'];
    $suma = round($sums, 2);
    return $suma;
}

if (!empty($fondid)) {
    foreach ($fondid as $fond) {
        if ($fond > 1) {
            $query = "select fondnameall as nazov,refmena from fonds where fondid = $fond";
        } else {
            $query = "select nazov, refmena from spravca where spravcaid = 1";
        }

        $resultConn = Connection::getDataFromDatabase($query, defaultDB);
        $result = $resultConn[1][0];
        $fondname = $result["nazov"];
        $refmena = $result["refmena"];

        if ($fond > 1) {
            $query = "SELECT
                f.*,
                p.podielnikid,
                p.cislozmluvy,
                to_char(t.datum, 'dd.mm.yyyy') as today_datum
            FROM
                portfolio p
                inner join fonds f on p.fondid = f.fondid
                inner join today t on t.fondid = p.fondid
                left join podielnik_sales ps on p.podielnikid = ps.id_podielnik
                and ps.datum_do = to_date('2099-12-31', 'YYYY-MM-DD')
                left join users u on ps.id_sales = u.userid
            WHERE
                p.fondid = $fond";
            $privatBankarRes = Connection::getDataFromDatabase($query, defaultDB);
            $privatBankar = $privatBankarRes[1][0];
            $podielnikid = $privatBankar["podielnikid"];
            $fondnameall = $privatBankar["fondnameall"];
            $cislozmluvy = $privatBankar['cislozmluvy'];
            $today_datum = $privatBankar['today_datum'];

            $query = "SELECT
                p.*,
                COALESCE(u.username, '--') as PrivBankar,
                COALESCE(u.telefon, '--') as telefon,
                COALESCE(u.email, '--') as email
            FROM
                podielnik p
                left join podielnik_sales ps on p.podielnikid = ps.id_podielnik
                and ps.datum_do = to_date('2099-12-31', 'YYYY-MM-DD')
                left join users u on ps.id_sales = u.userid
            WHERE
                p.podielnikid = $podielnikid";
            $udajeRes = Connection::getDataFromDatabase($query, defaultDB);
            $udaje = $udajeRes[1][0];
            $podielnik = trim($udaje["titulpred"] . " " . $udaje["prieznaz"] . " " . $udaje["meno"] . " " . $udaje["titulza"]);
            $podielnik = $cislozmluvy;
            $adresa = $udaje["address"] . ", " . $udaje["postalcode"] . " " . $udaje["city"];
            $rcico = $udaje["rcico"];
            $telefon = $udaje["kontaktphonenumber"];
            $PrivatBankar = $udaje['PrivBankar'];
            if ($PrivatBankar == "Habán Branislav")
                $PrivatBankar = "Habán Branislav, Chairman of the Board";
            $TeleftonPB = $udaje['telefon'];
            $Email = $udaje['email'];
        } else if ($fond != "") {
            $fondnameall = 'Vybrané portfóliá';
            $podielnik = '-';
            $adresa = '-';
            $rcico = '-';
            $PrivatBankar = 'Sympatia Financie o.c.p., a.s.';
            $TeleftonPB = '02/3263 0700';
            $Email = '<EMAIL>';
        } else {
            $podielnik = '-';
            $adresa = '-';
            $rcico = '-';
            $PrivatBankar = 'Sympatia Financie o.c.p., a.s.';
            $TeleftonPB = '02/3263 0700';
            $Email = '<EMAIL>';
            if ($fond) {
                if ($fond == -1) {
                    $fondnameall = 'Všetky';
                } else {
                    $query = "
                            SELECT
                                f.fondnameall
                            FROM
                                fonds f
                            WHERE
                                fondid = $fond
                        ";
                    $resultRes = Connection::getDataFromDatabase($query, defaultDB);
                    $result = $resultRes[1][0];
                    $fondnameall = $result["fondnameall"];
                }
            } else {
                $fondnameall = 'Iné';
            }
        }
        ?>
        <div class="relative mb-5 overflow-x-auto shadow-md rounded-lg">
            <section class="bg-white dark:bg-gray-700 shadow-md portfolioSection p-5 overflow-hidden rounded-lg">
                <?php
                $query = "SELECT menaref FROM majetoktotal WHERE datum=to_date('$dbdate','YYYY-MM-DD') ";
                if ($fond) {
                    $query .= " ";
                } else if ($fond != "") {
                    $query .= " and subjektid = $fond";
                }
                $queryRes = Connection::getDataFromDatabase($query, defaultDB);
                $result = $queryRes[1][0];

                $menaref = $result["menaref"];
                $p1 = getDBSums("'BU','TD'", '221110,221210,315132,315113', false);
                $suma_p1 = round($p1, 2);

                $d1 = getDBSums("'Bonds'", '251110,251120', false);
                $suma_d1 = round($d1, 2);

                $a1 = getDBSums("'Shares'", '251200', false);
                $suma_a1 = round($a1, 2);

                $f1 = getDBSums("'Fonds','Shares'", '251300,251200', false);
                $suma_f1 = round($f1, 2);

                $paz1 = getDBSums("'Fonds','Bonds','Shares','TD','BU'", "", true);
                $suma_paz1 = round($paz1, 2);

                $total = (float) $p1 + (float) $d1 + (float) $a1 + (float) $f1 + (float) $paz1;
                $total_suma = round($total, 2);

                if ($total == 0)
                    $total = 1;

                $p2 = ($p1 / $total) * 100;
                $podiel_p2 = round($p2, 3);
                $d2 = ($d1 / $total) * 100;
                $podiel_d2 = round($d2, 3);
                $a2 = ($a1 / $total) * 100;
                $podiel_a2 = round($a2, 3);
                $f2 = ($f1 / $total) * 100;
                $podiel_f2 = round($f2, 3);
                $paz2 = ($paz1 / $total) * 100;
                $podiel_paz2 = round($paz2, 3);
                ?>
                <div class="flex">
                    <div class="flex relative gap-12">
                        <div style="position: relative; display: flex; justify-content: center; align-items: center;">
                            <canvas id="chartik<?php echo $fond; ?>" style="max-width: 100%; max-height: 20vh;"></canvas>
                        </div>
                        <div class="mr-auto place-self-center lg:col-span-7">
                            <h1
                                class="max-w-2xl mb-4 text-3xl font-extrabold tracking-tight leading-none md:text-3xl xl:text-3xl dark:text-white">
                                Portfólio <span><?php echo $cislozmluvy; ?></span></h1>
                            <p class="max-w-2xl mb-1 font-light text-gray-500 dark:text-gray-300">
                                Celková hodnota
                                portfólia: <strong><?php echo $total_suma; ?>&nbsp;<?php echo $refmena; ?></strong></p>
                            <p class="max-w-2xl mb-1 font-light text-gray-500 dark:text-gray-300">
                                Typ portfólia: <strong><?php echo $fondnameall; ?></strong></p>
                        </div>
                    </div>
                </div>

                <table>
                    <tr>
                        <td class="text-2xl font-bold flex text-gray-500 dark:text-gray-100 items-center gap-4 mb-5 mt-4">
                            Štruktúra portfólia podľa tried aktív
                        </td>
                    </tr>
                </table>


                <div class="relative overflow-x-auto">
                    <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
                        <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-900 dark:text-gray-400">
                            <tr>
                                <th scope="col" class="px-6 py-3">
                                    Trieda aktív
                                </th>
                                <th scope="col" class="px-6 py-3">
                                    Objem v <?php echo $refmena ?>
                                </th>
                                <th scope="col" class="px-6 py-3">
                                    Podiel v %
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 border-gray-200">
                                <td class="py-2 px-6">Peňažné prostriedky</td>
                                <td class="py-2 px-6"><?php echo $suma_p1; ?>€</td>
                                <td class="py-2 px-6"><?php echo $podiel_p2; ?>%</td>
                            </tr>
                            <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 border-gray-200">
                                <td class="py-2 px-6">Dlhopisy</td>
                                <td class="py-2 px-6"><?php echo $suma_d1; ?>€</td>
                                <td class="py-2 px-6"><?php echo $podiel_d2; ?>%</td>
                            </tr>
                            <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 border-gray-200">
                                <td class="py-2 px-6">Akcie, ADR a GDR</td>
                                <td class="py-2 px-6"><?php echo $suma_a1; ?>€</td>
                                <td class="py-2 px-6"><?php echo $podiel_a2; ?>%</td>
                            </tr>
                            <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 border-gray-200">
                                <td class="py-2 px-6">Podielové listy a ETF</td>
                                <td class="py-2 px-6"><?php echo $suma_f1; ?>€</td>
                                <td class="py-2 px-6"><?php echo $podiel_f2; ?>%</td>
                            </tr>
                            <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 border-gray-200">
                                <td class="py-2 px-6">Záväzky a pohľadávky</td>
                                <td class="py-2 px-6"><?php echo $suma_paz1; ?>€</td>
                                <td class="py-2 px-6"><?php echo $podiel_paz2; ?>%</td>
                            </tr>
                            <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 border-gray-200 font-bold">
                                <td class="py-2 px-6">Trhová hodnota celkom</td>
                                <td class="py-2 px-6"><?php echo $total_suma; ?>€</td>
                                <td class="py-2 px-6">100%</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <table class="w-full mt-5">
                    <tr>
                        <td class="text-2xl font-bold flex text-gray-500 dark:text-gray-100 items-center gap-4 mb-5 mt-4">
                            Peňažné prostriedky
                        </td>
                    </tr>
                </table>
                <div class="relative overflow-x-auto">
                    <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
                        <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-900 dark:text-gray-400">
                            <tr>
                                <th scope="col" class="px-6 py-3">Typ účtu</th>
                                <th scope="col" class="px-6 py-3">Mena</th>
                                <th scope="col" class="px-6 py-3">Dátum zriadenia TV</th>
                                <th scope="col" class="px-6 py-3">Dátum splatnosti TV</th>
                                <th scope="col" class="px-6 py-3">Objem</th>
                                <th scope="col" class="px-6 py-3">AUV</th>
                                <th scope="col" class="px-6 py-3">Úroková sadzba</th>
                                <th scope="col" class="px-6 py-3">Kurz</th>
                                <th scope="col" class="px-6 py-3">Objem v <?php echo $refmena ?></th>
                                <th scope="col" class="px-6 py-3">Podiel v % na celom portfóliu</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            include "/home/<USER>/www/src/Controllers/prehlady/financne-toky/queries/penazne-prostriedky.php";
                            ?>
                            <tr
                                class="bg-white border-b dark:text-gray-50 dark:bg-gray-600 dark:border-gray-700 border-gray-200 font-bold">
                                <td class="py-2 px-6">Spolu</td>
                                <td class="py-2 px-6" colspan=7></td>
                                <td class="py-2 px-6"><?php echo $suma_p1; ?></td>
                                <td class="py-2 px-6"><?php echo $podiel_p2; ?>%</td>
                            </tr>
                        </tbody>
                    </table>
                    <table>
                        <tr>
                            <td class="text-2xl font-bold flex text-gray-500 dark:text-gray-100 items-center gap-4 mb-5 mt-4">
                                Dlhopisy
                            </td>
                        </tr>
                    </table>
                    <div class="relative overflow-x-auto">
                        <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
                            <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-900 dark:text-gray-400">
                                <tr>
                                    <th scope="col" class="px-6 py-3">Názov</th>
                                    <th scope="col" class="px-6 py-3">ISIN</th>
                                    <th scope="col" class="px-6 py-3">Mena</th>
                                    <th scope="col" class="px-6 py-3">Počet kusov</th>
                                    <th scope="col" class="px-6 py-3">Aktuálna cena</th>
                                    <th scope="col" class="px-6 py-3">Objem v trhových cenách</th>
                                    <th scope="col" class="px-6 py-3">AúV</th>
                                    <th scope="col" class="px-6 py-3">Kurz</th>
                                    <th scope="col" class="px-6 py-3">Objem v trhových cenách s AUV v <?php echo $refmena ?>
                                    </th>
                                    <th scope="col" class="px-6 py-3">Podiel v % na celom portfóliu</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                include "/home/<USER>/www/src/Controllers/prehlady/financne-toky/queries/dlhopisy.php";
                                ?>
                                <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 border-gray-200 font-bold">
                                    <td class="py-2 px-6">Spolu</td>
                                    <td class="py-2 px-6" colspan=7></td>
                                    <td class="py-2 px-6"><?php echo $suma_d1; ?>€</td>
                                    <td class="py-2 px-6"><?php echo $podiel_d2; ?>%</td>
                                </tr>
                            </tbody>
                        </table>
                        <?php if ($suma_a1 !== 0) { ?>
                            <table>
                                <tr>
                                    <td
                                        class="text-2xl font-bold flex text-gray-500 dark:text-gray-100 items-center gap-4 mb-5 mt-4">
                                        Akcie, ADR a GDR
                                    </td>
                                </tr>
                            </table>
                            <div class="relative overflow-x-auto">
                                <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
                                    <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-900 dark:text-gray-400">
                                        <tr>
                                            <th scope="col" class="px-6 py-3">Názov</th>
                                            <th scope="col" class="px-6 py-3">ISIN</th>
                                            <th scope="col" class="px-6 py-3">Mena</th>
                                            <th scope="col" class="px-6 py-3">Počet kusov</th>
                                            <th scope="col" class="px-6 py-3">Aktuálna cena</th>
                                            <th scope="col" class="px-6 py-3">Objem v trhových cenách</th>
                                            <th scope="col" class="px-6 py-3">Kurz</th>
                                            <th scope="col" class="px-6 py-3">Objem v trhových cenách v <?php echo $refmena ?></th>
                                            <th scope="col" class="px-6 py-3">Podiel v % na <BR> celom portfóliu</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        include "/home/<USER>/www/src/Controllers/prehlady/financne-toky/queries/akcie.php";
                                        ?>
                                        <tr
                                            class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 border-gray-200 font-bold">
                                            <td class="py-2 px-6">Spolu</td>
                                            <td class="py-2 px-6" colspan=6></td>
                                            <td class="py-2 px-6"><?php echo $suma_a1; ?></td>
                                            <td class="py-2 px-6"><?php echo $podiel_a2; ?></td>
                                        </tr>
                                    </tbody>
                                </table>
                            <?php } ?>
                            <table>
                                <tr>
                                    <td
                                        class="text-2xl font-bold flex text-gray-500 dark:text-gray-100 items-center gap-4 mb-5 mt-4">
                                        Podielové listy a ETF
                                    </td>
                                </tr>
                            </table>
                            <div class="relative overflow-x-auto">
                                <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
                                    <thead
                                        class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-900 dark:text-gray-400">
                                        <tr>
                                            <th scope="col" class="px-6 py-3">Názov</th>
                                            <th scope="col" class="px-6 py-3">ISIN</th>
                                            <th scope="col" class="px-6 py-3">Mena</th>
                                            <th scope="col" class="px-6 py-3">Počet kusov</th>
                                            <th scope="col" class="px-6 py-3">Aktuálna cena</th>
                                            <th scope="col" class="px-6 py-3">Objem v trhových cenách</th>
                                            <th scope="col" class="px-6 py-3">Kurz</th>
                                            <th scope="col" class="px-6 py-3">Objem v trhových cenách v <?php echo $refmena ?>
                                            </th>
                                            <th scope="col" class="px-6 py-3">Podiel v % na celom portfóliu</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        include "/home/<USER>/www/src/Controllers/prehlady/financne-toky/queries/podielove-listy.php";
                                        ?>
                                        <tr
                                            class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 border-gray-200 font-bold">
                                            <td class="text-center py-2">Spolu</td>
                                            <td class="text-center py-2" colspan=6></td>
                                            <td class="text-center py-2"><?php echo $suma_f1; ?> €</td>
                                            <td class="text-center py-2"><?php echo $podiel_f2; ?>%</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <table>
                                <tr>
                                    <td
                                        class="text-2xl font-bold flex text-gray-500 dark:text-gray-100 items-center gap-4 mb-5 mt-4">
                                        Záväzky a pohľadávky
                                    </td>
                                </tr>
                            </table>
                            <div class="relative overflow-x-auto">
                                <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
                                    <thead
                                        class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-900 dark:text-gray-400">
                                        <tr>
                                            <th scope="col" class="px-6 py-3">Názov</th>
                                            <th scope="col" class="px-6 py-3">Názov CP</th>
                                            <th scope="col" class="px-6 py-3">Mena</th>
                                            <th scope="col" class="px-6 py-3">Objem</th>
                                            <th scope="col" class="px-6 py-3">Počet</th>
                                            <th scope="col" class="px-6 py-3">Kurz</th>
                                            <th scope="col" class="px-6 py-3">Objem v <?php echo $refmena ?></th>
                                            <th scope="col" class="px-6 py-3">Podiel v % na celom portfóliu</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        include "/home/<USER>/www/src/Controllers/prehlady/financne-toky/queries/pohladavky.php";
                                        ?>
                                        <tr
                                            class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 border-gray-200 font-bold">
                                            <td class="text-center py-2">Spolu</td>
                                            <td class="text-center py-2" colspan=5></td>
                                            <td class="text-center py-2"><?php echo $suma_paz1; ?></td>
                                            <td class="text-center py-2"><?php echo $podiel_paz2; ?>%</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <?php if (isset($includeAwait)) { ?>
                                <table>
                                    <tr>
                                        <td
                                            class="text-2xl font-bold flex text-gray-500 dark:text-gray-100 items-center gap-4 mb-5 mt-4">
                                            Očakávané finančné toky ku dňu <?php echo $dbdate ?>
                                        </td>
                                    </tr>
                                </table>
                                <div class="relative overflow-x-auto">
                                    <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
                                        <thead
                                            class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-900 dark:text-gray-400">
                                            <tr>
                                                <th scope="col" class="px-6 py-3">Dátum</th>
                                                <th scope="col" class="px-6 py-3">Portfólio</th>
                                                <th scope="col" class="px-6 py-3">Investičný nástroj</th>
                                                <th scope="col" class="px-6 py-3">Mena</th>
                                                <th scope="col" class="px-6 py-3">Istina</th>
                                                <th scope="col" class="px-6 py-3">Kupón</th>
                                                <th scope="col" class="px-6 py-3">Daň</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php
                                            include "/home/<USER>/www/src/Controllers/prehlady/financne-toky/queries/ocakavane-toky.php";
                                            ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php } ?>
            </section>
        </div>
        <script>
            prostriedky = <?php echo $suma_p1; ?>;
            dlhopisy = <?php echo $suma_d1; ?>;
            akcie = <?php echo $suma_a1; ?>;
            podielove = <?php echo $suma_f1; ?>;
            zavazky = <?php echo $suma_paz1; ?>;
            if (prostriedky === 0 && dlhopisy === 0 && akcie === 0 && podielove === 0 && zavazky === 0) {
                document.getElementById('chartik<?php echo $fond; ?>').parentNode.style.minHeight = "10vw";
                document.getElementById('chartik<?php echo $fond; ?>').parentNode.style.minWidth = "10vw";
                document.getElementById('chartik<?php echo $fond; ?>').parentNode.innerHTML = "<p class='font-bold bg-gray-300 p-1 px-2 rounded-lg'>Žiadne dáta...</p>";
            }
            ctx = document.getElementById('chartik<?php echo $fond; ?>');

            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['Peňažné prostriedky', 'Dlhopisy', 'Akcie, ADR a GDR', 'Podielové listy a ETF', 'Záväzky a pohľadávky'],
                    datasets: [{
                        label: '<?php echo $refmena; ?>',
                        data: [prostriedky, dlhopisy, akcie, podielove, zavazky],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: true,
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });
        </script>
        <?php
    }
}

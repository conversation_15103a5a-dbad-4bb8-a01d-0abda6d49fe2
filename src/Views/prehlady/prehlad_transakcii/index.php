<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
require_once "/home/<USER>/www/src/Components/inputs/multiSelect/multiSelect.php";

$udaje = Connection::getDataFromDatabase("select 
		p.fondid as fondid,
		p.cislozmluvy as cislozmluvy 
		from  
			fonds f, 
			portfolio p 
		where 
			p.fondid = f.fondid and
			f.template = 0 and
			f.archiv = 'f' 
		order by p.cislozmluvy", defaultDB);
$portfolia = $udaje[1];
?>
<section class="p-5">
    <div class="flex gap-4 items-center">
        <h1 class="text-4xl dark:text-gray-100 font-bold my-4">Prehľady transackií</h1>
    </div>
    <hr />
    <div class="w-full mt-4 bg-white border border-gray-200 rounded-lg shadow dark:bg-gray-800 dark:border-gray-700">
        <div id="fullWidthTabContent" class="border-t border-gray-200 dark:border-gray-600">
            <div class="p-4 bg-white rounded-lg md:p-8 dark:bg-gray-800" id="hromadne" role="tabpanel"
                aria-labelledby="hromadne-tab">
                <form id="novyHromadnyVypis" hx-post="/src/Views/prehlady/prehlad_transakcii/prehlad_transakcii.php"
                    hx-target="#resultko" class="flex flex-col items-center">
                    <section class="flex items-end justify-center px-32 w-full gap-8" style="padding: 0 10vw;">
                        <div class="w-full">
                            <label for="dateFrom"
                                class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Dátum
                                od: </label>
                            <input type="date" id="dateFrom" name="dateFrom"
                                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                required />
                        </div>
                        <div class="w-full">
                            <label for="dateTill"
                                class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Dátum
                                do: </label>
                            <input type="date" id="dateTill" name="dateTill"
                                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                required />
                        </div>
                        <div class="w-full">
                            <?php echo MultiSelect::render("Vybrať portfólio", $portfolia, "portfolio", ["cislozmluvy"], "fondid"); ?>
                            <!-- <label for="portfolio"
                                   class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Výber
                                portfólia</label>
                            <select id="portfolio" name="portfolio"
                                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                                <?php foreach ($portfolia as $portfolio) { ?>
                                    <option value="<?php echo $portfolio["fondid"]; ?>"><?php echo $portfolio["cislozmluvy"]; ?></option>
                                <?php } ?>
                            </select> -->
                        </div>
                        <button type="submit"
                            class="focus:outline-none text-white bg-green-700 hover:bg-green-800 focus:ring-4 mb-0.5 focus:ring-green-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-green-600 dark:hover:bg-green-700 dark:focus:ring-green-800">
                            Zobraziť
                        </button>
                    </section>
                </form>
            </div>
        </div>
    </div>
</section>
<section class="p-5" id="resultko"></section>
<script src="/src/assets/js/prehlady/vypis-o-stave/index.js"></script>
<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/src/lib/functions/conversionEUR.php";
require_once "/home/<USER>/www/conf/settings.php";

error_reporting(E_ALL);
print_r($_POST);
$fromdate = $_POST["dateFrom"];
$todate = $_POST["dateTill"];
$fondid = $_POST["portfoliovalues"];

$efromdate = $fromdate;
$etodate = $todate;
$tms_todate = $todate;

if ($fondid) {
    //udaje o portfoliu
    $query = "select f.fondnameall,p.podielnikid,p.cislozmluvy
			from portfolio p,fonds f
			where p.fondid in ($fondid)
			and p.fondid=f.fondid";
    $portfolioUdajeRes = Connection::getDataFromDatabase($query, defaultDB);
    $portfolioUdaje = $portfolioUdajeRes[1];

    $fondnameall = array();
    $cislozmluvy = array();

    foreach ($portfolioUdaje as $item) {
        $podielnikid = $item["podielnikid"];
        $fondnameall[] = $item["fondnameall"];
        $cislozmluvy[] = $item["cislozmluvy"];
    }

    $fondnameall = implode(', ', $fondnameall);
    $cislozmluvy = implode(', ', $cislozmluvy);

    //udaje o podielnikovi
    $query = "select p.* ,COALESCE(u.username,'--') as PrivBankar, COALESCE(u.telefon,'--') as telefon, COALESCE(u.email,'--') as email
			from podielnik p
			left join podielnik_sales ps on p.podielnikid = ps.id_podielnik and ps.datum_do=to_date('2099-12-31','YYYY-MM-DD')
			left join users u on ps.id_sales = u.userid
			where p.podielnikid=$podielnikid";

    $podielnikUdajeRes = Connection::getDataFromDatabase($query, defaultDB);
    $podielnikUdaje = $podielnikUdajeRes[1][0];

    $podielnik = $item["prieznaz"];
    $adresa = $item["address"] . ", " . $item["postalcode"] . " " . $item["city"];
    $rcico = $item["rcico"];
    $telefon = $item["kontaktphonenumber"];
    $PrivatBankar = $item["PrivBankar"];
    if ($PrivatBankar == "Habán Branislav")
        $PrivatBankar = "Habán Branislav, Chairman of the Board";
    $telefonPB = $item["telefon"];
    $email = $item["email"];
}

$sumaAlertBottom = "";

$query = "select * from users
		where userid in 
		(
			select fm.userid
			from fondsmanagers fm
			where fm.fondid in ($fondid)
			and main=1
		)";

$queryRes = Connection::getDataFromDatabase($query, defaultDB);
$result = $queryRes[1];

foreach ($result as $item) {
    $managers .= $item["username"] . ", ";
}
$managers = substr($managers, 0, strlen($managers) - 2);

?>


<div class="relative overflow-x-auto shadow-md p-3 rounded-lg">
    <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
        <tr class="stred">
            <td class="text-2xl font-bold flex items-center gap-4 mb-5 mt-4">
                Prehľad transakcií od <?php echo "$efromdate do $etodate" ?>
            </td>
        </tr>
    </table>
    <table class="bg-white p-3 mb-4 shadow-lg w-full rounded-lg">
        <tr class="flex items-center">
            <td class="flex px-4 pt-2 font-bold"><?php echo "Portfólio" ?>:</td>
            <td class="flex px-4"><?php echo $cislozmluvy; ?></td>
        </tr>
        <tr class="normalne">
            <td class="flex px-4 font-bold">Kontaktná osoba:</td>
            <td class="flex px-4"><?php echo $PrivatBankar; ?></td>
        </tr>
        <tr class="normalne">
            <td class="flex px-4 font-bold"><?php echo "Telefónné číslo:" ?>:</td>
            <td class="flex px-4"><?php echo $telefonPB; ?></td>
        </tr>
        <tr class="normalne">
            <td class="flex px-4 font-bold">E-mail:</td>
            <td class="flex px-4"><?php echo $email; ?></td>
        </tr>
    </table>
    <?php
    flush();
    $fondid_add .= "subjektid in ($fondid) ";
    $query = "SELECT to_char(datum,'YYYY-MM-DD') as mydatum, t.* from (
				select 	-1 as poradie,
						'Počiatočný stav účtu' as popis, menadenom as mena1,
						CASE 
                            WHEN EXISTS (
                                SELECT datum 
                                FROM majetoktotal 
                                WHERE subjektid = mt.subjektid 
                                  AND eqid = 'BU' 
                                  AND uctovnykod = 221110 
                                  AND menadenom = mt.menadenom 
                                  AND datum < TO_DATE('$fromdate', 'YYYY-MM-DD')
                            ) THEN 
                                SUM(
                                    CASE 
                                        WHEN md_d = 0 THEN 1 
                                        WHEN md_d = 1 THEN -1 
                                        ELSE 0 
                                    END * pocet
                                )
                            ELSE 
                                0
                        END AS transsuma,
						to_date('$fromdate','YYYY-MM-DD') as datum,
						'' as poznamka,
						0 as md_d
				from	majetoktotal mt
				where	$fondid_add
						and datum = (select max(datum) from majetoktotal where datum < to_date('$fromdate','YYYY-MM-DD'))
						and eqid = 'BU'
						and uctovnykod = 221110
				group by menadenom, datum,subjektid
				union all
				select 	0 as poradie,
						'Automaticka konverzia SKK na EUR - konverzny kurz 1 EUR = 30,1260 SKK' as popis, mena as mena1,
						SUM(
                            CASE 
                                WHEN md_d = 0 THEN 1
                                WHEN md_d = 1 THEN -1
                                ELSE 0
                            END * pocet
                        ) AS transsuma,
						max(e.conversion_date) as datum,
						'' as poznamka,
						0 as md_d
				from	majetokarchiv mt, eurconversionsettings e
				where	$fondid_add
						and obratdatatimezauctovane = e.conversion_date
						and eqid = 'BU'
						and uctovnykod = 221110
						and destinacia = 'nauctovanieEUR'
						and e.conversion_date = to_date('$fromdate','YYYY-MM-DD')
				group by mena, subjektid

				union all
				
				select 	100000 as poradie,
						'Konečný stav účtu' as popis, menadenom as mena1, 
						CASE 
                            WHEN EXISTS (
                                SELECT 1
                                FROM majetoktotal 
                                WHERE subjektid = mt.subjektid 
                                  AND eqid = 'BU' 
                                  AND uctovnykod = 221110 
                                  AND menadenom = mt.menadenom 
                                  AND datum = mt.datum
                            ) 
                            THEN 
                                SUM(
                                    CASE 
                                        WHEN md_d = 0 THEN 1
                                        WHEN md_d = 1 THEN -1
                                        ELSE 0
                                    END * pocet
                                )
                            ELSE 
                                0
                        END AS transsuma,
						to_date('$todate','YYYY-MM-DD') as datum,
						'' as poznamka,
						0 as md_d
				from	majetoktotal mt
				where	$fondid_add
						and datum = (select max(datum) from majetoktotal where datum <= to_date('$todate','YYYY-MM-DD'))
						and eqid = 'BU'
						and uctovnykod = 221110
				group by menadenom, datum, subjektid
				union all
				select 	99999 as poradie,
						'Automaticka konverzia SKK na EUR - konverzny kurz 1 EUR = 30,1260 SKK' as popis, mena as mena1, 
						SUM(
                            CASE 
                                WHEN md_d = 0 THEN 1
                                WHEN md_d = 1 THEN -1
                                ELSE 0
                            END * pocet
                        ) AS transsuma,
						max(e.conversion_date) as datum,
						'' as poznamka,
						0 as md_d
				from	majetokarchiv mt, eurconversionsettings e
				where	$fondid_add
						and obratdatatimezauctovane = e.conversion_date
						and eqid = 'BU'
						and uctovnykod = 221110
						and destinacia = 'oductovanieSKK'
						and e.conversion_date = to_date('$fromdate','YYYY-MM-DD')
				group by mena, subjektid
				
				union all
				
 				select 	
					120 as poradie,
					'Vklad klienta' as popis, ob.mena as mena1, 
					ob.suma as transsuma,	ob.obratdatetime as datum, 
					'' as poznamka,
					ma.md_d
				from
					obratybu ob,
					obratybuobratid obo,
					majetokarchiv ma
				where
					ob.id = obo.id
					and ma.obratid = obo.obratid
					and ma.$fondid_add
					and ma.kodobratu = 201
					and ma.uctovnykod = 325300
					and ob.logactivityid = 15
					and ob.obratdatetime >= to_date('$fromdate','YYYY-MM-DD')
					and ob.obratdatetime <= to_date('$todate','YYYY-MM-DD')
					and ob.krdb = 1					
				union all
				select 
					420 as poradie,
					'Výber klienta' as popis, u.mena as mena1,
					u.suma as transsuma,	u.datesplatnost as datum, 
					'' as poznamka,
					ma.md_d
				from
					uhrada u, uhradaobratid ui, majetokarchiv ma
				where
					u.kodobratu = 303
					and u.id = ui.id
					and ma.obratid = ui.obratid
					and ma.$fondid_add
					and ma.kodobratu = 303
					and ma.uctovnykod = 261930
					and logactivityid = 15
					and datesplatnost >= to_date('$fromdate','YYYY-MM-DD')
					and datesplatnost <= to_date('$todate','YYYY-MM-DD')
				union all
                                (    WITH pool_data AS (SELECT p.dealid,
                                              pd.subjektid,
                                              pd.transsumareal
                                       FROM pool p
                                                JOIN pooldetailreal pd ON pd.poolid = p.poolid),
                         equity_data AS (SELECT k.dealid,
                                                d.cpnaz || '; ISIN ' || d.isinreal AS poznamka
                                         FROM konfirmaciacp k
                                                  JOIN dbequity d ON d.isin = k.isin)
                    SELECT 220                        AS poradie,
                           'Nákup akcie'              AS popis,
                           u.mena                     AS mena1,
                           COALESCE(
                                   pd.transsumareal,
                                   r.transsuma
                           )                          AS transsuma,
                           ma.obratdatatimezauctovane AS datum,
                           ed.poznamka,
                           ma.md_d
                    FROM uhrada u
                             JOIN
                         uhradaobratid ui ON u.id = ui.id
                             JOIN
                         majetokarchiv ma ON ma.obratid = ui.obratid
                             LEFT JOIN
                         rekonfirmaciacp r ON u.dealid = r.dealid AND u.tranza = r.tranza
                             LEFT JOIN
                         pool_data pd ON pd.dealid = r.dealid AND pd.subjektid = ma.subjektid
                             LEFT JOIN
                         equity_data ed ON ed.dealid = r.dealid
                    WHERE u.kodobratu = 331
                      AND ma.kodobratu = 331
                      AND ma.uctovnykod = 261911
                      AND u.logactivityid = 15
                      AND ma.obratdatatimezauctovane BETWEEN TO_DATE('$fromdate', 'YYYY-MM-DD')
                        AND TO_DATE('$todate', 'YYYY-MM-DD')
                      AND ma.subjektid IN (1752))
				union all 
				select 	
					70 as poradie,
					'Platba za predaj akcie' as popis,
					ma.jednotka as mena1, 
					COALESCE((select pd.transsumareal from pool p, pooldetailreal pd where p.dealid=r.dealid and pd.poolid=p.poolid and pd.subjektid=ma.subjektid),r.transsuma) as transsuma,
					-- ma.obratdatatimezauctovane as datum, 
					r.datvysporiadaniabureal as datum,
					(select d.cpnaz|| '; ISIN ' ||d.isinreal from konfirmaciacp k, dbequity d where k.dealid = r.dealid and d.isin = k.isin ) as poznamka,
					0
				from
					rekonfirmaciacpobratid ro,
					rekonfirmaciacp r,
					konfirmaciacp kcp,
					majetokarchiv ma
				where
					ma.$fondid_add
					and ma.kodobratu = 231
					and ma.uctovnykod = 261710
					and r.datvysporiadaniabureal >= to_date('$fromdate','YYYY-MM-DD')
					and r.datvysporiadaniabureal <= to_date('$todate','YYYY-MM-DD')
					and ro.obratid = ma.obratid
					and r.dealid = ro.dealid
					and r.tranza = ro.tranza
					and r.dealid = kcp.dealid
				union all 
				SELECT 210                         AS poradie,
                       'Nákup dlhopisu'            AS popis,
                       u.mena                      AS mena1,
                       COALESCE(
                               (SELECT transsumareal
                                FROM pooldetailreal pd
                                         JOIN pool p ON pd.poolid = p.poolid
                                WHERE p.dealid = r.dealid
                                  AND pd.$fondid_add),
                               r.transsuma
                       )                           AS transsuma,
                       ma.obratdatatimezauctovane  AS datum,
                       (SELECT d.cpnaz || '; ISIN ' || d.isinreal
                        FROM konfirmaciacp k
                                 JOIN dbequity d ON d.isin = k.isin
                        WHERE k.dealid = r.dealid) AS poznamka,
                       ma.md_d
                FROM uhrada u
                         JOIN
                     uhradaobratid ui ON u.id = ui.id
                         JOIN
                     majetokarchiv ma ON ma.obratid = ui.obratid
                         LEFT JOIN
                     rekonfirmaciacp r ON u.dealid = r.dealid AND u.tranza = r.tranza
                         LEFT JOIN
                     konfirmaciacp kcp ON r.dealid = kcp.dealid
                WHERE u.kodobratu = 302
                  AND ma.kodobratu = 302
                  AND ma.uctovnykod = 261920
                  AND u.logactivityid = 15
                  AND ma.obratdatatimezauctovane BETWEEN TO_DATE('$fromdate', 'YYYY-MM-DD')
                    AND TO_DATE('$todate', 'YYYY-MM-DD')
                  AND ma.$fondid_add				
				union all
				select 	
					60 as poradie,
					case when r.dealid in (46663904,47124904) then 'Pred�asn� splatenie dlhopisu na v�zvu emitenta'
						else 'Platba za predaj dlhopisu' end as popis,
					ma.jednotka as mena1, 
					COALESCE((select pd.transsumareal from pool p, pooldetailreal pd where p.dealid=r.dealid and pd.poolid=p.poolid and pd.subjektid=ma.subjektid),r.transsuma) as transsuma,
					-- ma.obratdatatimezauctovane as datum, 
					r.datvysporiadaniabureal as datum,
					(select d.cpnaz|| '; ISIN ' ||d.isinreal from konfirmaciacp k, dbequity d where k.dealid = r.dealid and d.isin = k.isin ) as poznamka,
					ma.md_d
				from
					rekonfirmaciacpobratid ro,
					rekonfirmaciacp r,
					konfirmaciacp kcp,
					majetokarchiv ma
				where
					ma.$fondid_add
					and ma.kodobratu = 134
					and ma.uctovnykod = 261210
					and r.datvysporiadaniabureal >= to_date('$fromdate','YYYY-MM-DD')
					and r.datvysporiadaniabureal <= to_date('$todate','YYYY-MM-DD')
					and ro.obratid = ma.obratid
					and r.dealid = ro.dealid
					and r.tranza = ro.tranza
					and r.dealid = kcp.dealid

				union all 
				select 
					230 as poradie,
					'Nákup fondu' as popis,
					u.mena as mena1, 
					COALESCE((select pd.transsumareal from pool p, pooldetailreal pd where p.dealid=r.dealid and pd.poolid=p.poolid and pd.subjektid=ma.subjektid),r.transsuma) as transsuma,
					r.dattransakcie as datum, 
					(select d.cpnaz|| '; ISIN ' ||d.isinreal from konfirmaciacp k, dbequity d where k.dealid = r.dealid and d.isin = k.isin ) as poznamka, 
					ma.md_d
				from
					uhrada u , rekonfirmaciacp r, uhradaobratid ui, majetokarchiv ma
				where 
					u.kodobratu = 332
					and u.id = ui.id
					and ma.obratid = ui.obratid
					and ma.$fondid_add
					and ma.kodobratu = 332
					and ma.uctovnykod = 261912
					and u.logactivityid = 15
					and r.dattransakcie >= to_date('$fromdate','YYYY-MM-DD')
					and r.dattransakcie <= to_date('$todate','YYYY-MM-DD')
					and u.dealid = r.dealid
					and u.tranza = r.tranza 
				
				union all
				select 	
					80 as poradie,
					'Platba za predaj fondu' as popis,
					ma.jednotka as mena1, 
					COALESCE((select pd.transsumareal from pool p, pooldetailreal pd where p.dealid=r.dealid and pd.poolid=p.poolid and pd.subjektid=ma.subjektid),r.transsuma) as transsuma,
					ma.obratdatatimezauctovane as datum, 
					(select d.cpnaz|| '; ISIN ' ||d.isinreal from konfirmaciacp k, dbequity d where k.dealid = r.dealid and d.isin = k.isin ) as poznamka,
					ma.md_d
				from
					rekonfirmaciacpobratid ro,
					rekonfirmaciacp r,
					majetokarchiv ma
				where
					ma.$fondid_add
					and ma.kodobratu = 145
					and ma.uctovnykod = 261810
					and ma.obratdatatimezauctovane >= to_date('$fromdate','YYYY-MM-DD')
					and ma.obratdatatimezauctovane <= to_date('$todate','YYYY-MM-DD')
					and ro.obratid = ma.obratid
					and r.dealid = ro.dealid
					and r.tranza = ro.tranza

				union all 
				select 
					430 as poradie,
					'Zriadenie termínovaného vkladu' as popis, 
					u.mena as mena1, 
					COALESCE(
					(select
					 --najpr sa citaju povodne hodnoty ak su (vyuzitie po konverzii ktv)
					 COALESCE(transsumareal_pov,transsumareal)
					 from pooldetailreal pd, pool p where p.dealid=r.dealid and pd.poolid=p.poolid and pd.subjektid=$fondid),r.sum_td) as transsuma,
					ma.obratdatatimezauctovane as datum, 
					'' as poznamka, 
					ma.md_d 
				from
					uhrada u , konfirmaciaktv r, uhradaobratid ui, majetokarchiv ma
				where 
					u.kodobratu = 301 
					and u.id = ui.id
					and ma.obratid = ui.obratid
					and ma.$fondid_add
					and ma.kodobratu = 301
					and ma.uctovnykod = 261910
					and u.logactivityid = 15
					and ma.obratdatatimezauctovane >= to_date('$fromdate','YYYY-MM-DD')
					and ma.obratdatatimezauctovane <= to_date('$todate','YYYY-MM-DD')
					and u.dealid = r.dealid
				union all
				
				select 	
					10 as poradie,
					'Splatenie termínovaného vkladu' as popis,
					ob.mena as mena1, 
					ma.pocet as transsuma, 
					ma.obratdatatimezauctovane as datum, 
					'' as poznamka, 
					ma.md_d 
				from
					obratybu ob,
					obratybuobratid obo,
					majetokarchiv ma
				where
					ob.id = obo.id
					and ma.obratid = obo.obratid
					and ma.$fondid_add
					and ma.kodobratu = 203
					and ma.uctovnykod = 325300
					and ob.logactivityid = 15
					and ob.obratdatetime >= to_date('$fromdate','YYYY-MM-DD')
					and ob.obratdatetime <= to_date('$todate','YYYY-MM-DD')
					and ob.krdb = 1
							
				union all

				
				select 	
					20 as poradie,
					'Splatenie úroku z termínovaného vkladu' as popis, ob.mena as mena1, 
					ma.pocet as transsuma, ma.obratdatatimezauctovane as datum, 
					'Úrok brutto = ' ||
					replace(replace(to_char(k.iv_b,'9,999,999,990.90'),',',' '),'.',',')				
					|| '; Daň = ' || 
					replace(replace(to_char(k.suma_dane,'9,999,999,990.90'),',',' '),'.',',') 
					as poznamka, 
					ma.md_d
				from
					obratybu ob,
					obratybuobratid obo,
					majetokarchiv ma,
					konfirmaciaktv k
				where
					ob.id = obo.id
					and ma.obratid = obo.obratid
					and ma.$fondid_add
					and ma.kodobratu = 204
					and ma.uctovnykod = 325300
					and ob.logactivityid = 15
					and ma.obratdatatimezauctovane >= to_date('$fromdate','YYYY-MM-DD')
					and ma.obratdatatimezauctovane <= to_date('$todate','YYYY-MM-DD')
					and ob.krdb = 1
					and k.$fondid_add
					and k.dealid::text = ob.vs
				
				union all

				select 	
					20 as poradie,
					'Splatenie úroku z termínovaného vkladu' as popis, ob.mena as mena1, 
					ma.pocet as transsuma, ma.obratdatatimezauctovane as datum, 
					'Úrok brutto = ' ||
					replace(replace(to_char(pdr.auvreal,'9,999,999,990.90'),',',' '),'.',',')				
					|| '; Daň = ' || 
					replace(replace(to_char(pdr.dan,'9,999,999,990.90'),',',' '),'.',',')
					as poznamka, 
					ma.md_d
				from
					obratybu ob,
					obratybuobratid obo,
					majetokarchiv ma,
					konfirmaciaktv k,
					pool po,
					pooldetailreal pdr
				where
					ob.id = obo.id
					and ma.obratid = obo.obratid
					and ma.$fondid_add
					and ma.kodobratu = 204
					and ma.uctovnykod = 325300
					and ob.logactivityid = 15
					and ma.obratdatatimezauctovane >= to_date('$fromdate','YYYY-MM-DD')
					and ma.obratdatatimezauctovane <= to_date('$todate','YYYY-MM-DD')
					and ob.krdb = 1
					and k.subjektid = 0
				 	and pdr.poolid = po.poolid 
				 	and pdr.subjektid = ma.SUBJEKTID
				 	and k.dealid = po.dealid
                                 	and k.dealid::text = ob.vs
				
				union all


				select 
					40 as poradie,
					'Splatenie kupónu' as popis, ma.mena as mena1, 
					s.suma as transsuma, ma.OBRATDATATIMEZAUCTOVANE as datum, 
					(select e.cpnaz|| '; ISIN ' ||e.isinreal from dbequity e, dbequitycurr c, dbequitycurrric r
					 where r.isincurrric = ma.kodaktiva 
						   and r.isincurr = c.isincurr
						   and c.isin = e.isin
						   ) as poznamka, 
					0 as md_d
				from
					splatenie s, 
					splatenieobratid so,
					majetokarchiv ma
				where
				   	 s.DEALID=so.DEALID
				   	 and so.OBRATID=ma.obratid
				   	 and so.tranza=s.tranza
					 and s.suma<>0
					 and ma.$fondid_add
					 and ma.kodobratu = 137
					 and ma.uctovnykod = 315124
					 and ma.destinacia = 'dbequity'
					 and ma.obratdatatimezauctovane >= to_date('$fromdate','YYYY-MM-DD')
					 and ma.obratdatatimezauctovane <= to_date('$todate','YYYY-MM-DD')


				union all

				/*
				select 
					30 as poradie,
					'Splatenie dlhopisu' as popis, 
					ma.mena as mena1, 
					s.suma as transsuma, 
					ma.OBRATDATATIMEZAUCTOVANE as datum, 
					(select e.cpnaz|| '; ISIN ' ||e.isinreal from dbequity e, dbequitycurr c, dbequitycurrric r
					 where r.isincurrric = ma.kodaktiva 
						   and r.isincurr = c.isincurr
						   and c.isin = e.isin
						   ) as poznamka, 
					0 as md_d
				from
					splatenie s, 
					splatenieobratid so,
					majetokarchiv ma
				where
				   	 s.DEALID=so.DEALID
				   	 and so.OBRATID=ma.obratid
				   	 and so.tranza=s.tranza
					 and ma.$fondid_add
					 and ma.kodobratu = 136
					 and ma.uctovnykod = 315123
					 and ma.destinacia = 'dbequity'
					and ma.obratdatatimezauctovane >= to_date('$fromdate','YYYY-MM-DD')
					and ma.obratdatatimezauctovane <= to_date('$todate','YYYY-MM-DD')
					
					 and not exists(
						select 
							1
						from
							splatenie sx, 
							splatenieobratid sox,
							majetokarchiv max
						where
				   	 		sx.DEALID=sox.DEALID
				   	 		and sox.OBRATID=max.obratid
				   	 		and sox.tranza=sx.tranza
					 		and max.$fondid_add
					 		and max.kodobratu = 136
					 		and max.uctovnykod = 561230
					 		and max.destinacia = 'dbequity'
							and sox.OBRATID = so.obratid
					 		and max.obratdatatimezauctovane >= to_date('$fromdate','YYYY-MM-DD')
							and max.obratdatatimezauctovane <= to_date('$todate','YYYY-MM-DD')
					 		and not exists (select 1 from majetokarchiv where obratid = ma.obratid and uctovnykod = 251110)
					 	)
							

	
				union all
				select 
					35 as poradie,
					'�iastkov� spl�tka istiny' as popis, ma.mena as mena1, 
					s.suma as transsuma, ma.OBRATDATATIMEZAUCTOVANE as datum, 
					(select e.cpnaz|| '; ISIN ' ||e.isinreal from dbequity e, dbequitycurr c, dbequitycurrric r
					 where r.isincurrric = s.kodaktiva 
						   and r.isincurr = c.isincurr
						   and c.isin = e.isin
						   ) as poznamka, 
					0 as md_d
				from
					splatenie s, 
					splatenieobratid so,
					majetokarchiv ma
				where
				   	 s.DEALID=so.DEALID
				   	 and so.OBRATID=ma.obratid
				   	 and so.tranza=s.tranza
					 and ma.$fondid_add
					 and ma.kodobratu = 136
					 and ma.uctovnykod = 561230
					 and ma.destinacia = 'dbequity'
					 and ma.obratdatatimezauctovane >= to_date('$fromdate','YYYY-MM-DD')
					 and ma.obratdatatimezauctovane <= to_date('$todate','YYYY-MM-DD')
					 and not exists (select 1 from majetokarchiv where obratid = ma.obratid and uctovnykod = 251110)
					 
			union all
			*/
					select
						35 as poradie,
						case when d.isin in ('SK4000016598') then 'Splatenie investičného certifikátu'
							else 'Splatenie dlhopisu' end as popis, 
						s.mena as mena1,
						s.suma as transsuma, 
						s.datumvyplaty as datum,
						d.cpnaz|| '; ISIN ' ||d.isinreal as poznamka,
						0 as md_d
 		
	from 
		splatenie s, 
		dbequity d, 
		dbequitycurr dc, 
		dbequitycurrric dcr, 
		portfolio p
	where		
		s.uctovnykod = 251110
		and s.datumvyplaty >= to_date('$fromdate','YYYY-MM-DD')
		and s.datumvyplaty <= to_date('$todate','YYYY-MM-DD') 
		and s.$fondid_add
        	and s.datum_naroku = (select max(datesplatnost) from floatkupon where s.kodaktiva = isincurrric and istina >= 100)
		and dcr.isincurrric=s.kodaktiva
		and dcr.isincurr=dc.isincurr
		and dc.isin=d.isin
		and p.fondid = s.subjektid
	union all
	select 
		35 as poradie,
						'Čiastočné splatenie istiny - konečné' as popis, 
						s.mena as mena1,
						s.suma as transsuma, 
						s.datumvyplaty as datum,
						d.cpnaz|| '; ISIN ' ||d.isinreal as poznamka,
						0 as md_d
	from 
		splatenie s, 
		dbequity d, 
		dbequitycurr dc, 
		dbequitycurrric dcr, 
		portfolio p
	where		
		s.uctovnykod = 251110
		and s.datumvyplaty >= to_date('$fromdate','YYYY-MM-DD')
		and s.datumvyplaty <= to_date('$todate','YYYY-MM-DD') 
		and s.$fondid_add
        	and s.datum_naroku = (select max(datesplatnost) from floatkupon where s.kodaktiva = isincurrric and istina < 100)
		and dcr.isincurrric=s.kodaktiva
		and dcr.isincurr=dc.isincurr
		and dc.isin=d.isin
		and p.fondid = s.subjektid
	union all
	select 
		35 as poradie,
						'Čiastočné splatenie istiny - priebežné' as popis, 
						s.mena as mena1,
						s.suma as transsuma, 
						s.datumvyplaty as datum,
						d.cpnaz|| '; ISIN ' ||d.isinreal as poznamka,
						0 as md_d
	from 
		splatenie s, 
		dbequity d, 
		dbequitycurr dc, 
		dbequitycurrric dcr, 
		portfolio p
	where		
		s.uctovnykod = 251110
		and s.datumvyplaty >= to_date('$fromdate','YYYY-MM-DD')
		and s.datumvyplaty <= to_date('$todate','YYYY-MM-DD')  
		and s.$fondid_add
        	and s.datum_naroku < (select max(datesplatnost) from floatkupon where s.kodaktiva = isincurrric and istina < 100)
		and dcr.isincurrric=s.kodaktiva
		and dcr.isincurr=dc.isincurr
		and dc.isin=d.isin
		and p.fondid = s.subjektid
			union all
					select 
					50 as poradie,
					d.nazov as popis , sa.mena as mena1,
					sa.suma as transsuma, sa.datumvyplaty as datum, 
					de.cpnaz || '; ISIN ' || de.isinreal as poznamka, 
					0
				from
					splatenieakcia sa,
					dbequity de,
					dbequitycurr dr,
					dbequitycurrric drr,
					dividendaakciatyp d
				where
					sa.$fondid_add
					and sa.datumvyplaty >= to_date('$fromdate','YYYY-MM-DD')
					and sa.datumvyplaty <= to_date('$todate','YYYY-MM-DD')
					and d.subkodobratu = sa.subkodobratu
					and sa.subkodobratu in (select subkodobratu from dividendaakciatyp where hotovost = 1)
					and sa.kodaktiva = drr.isincurrric
					and dr.isincurr = drr.isincurr
					and de.isin = dr.isin

					
					
				
				union all
				select 
					400 as poradie,
					'Ostatné platby' as popis, ob.mena as mena1, 
					ob.suma as transsuma, ob.obratdatetime as datum, 
					ob.nazpartnera  as poznamka,
					ma.md_d
				from
					obratybu ob,
					obratybuobratid obo,
					majetokarchiv ma
				where
					ob.id = obo.id
					and ma.obratid = obo.obratid
					and ma.$fondid_add
					and ma.uctovnykod in (221110, 325300)	
				--	and ma.kodobratu not in (201,203,204,205,206,207,208,209,210,213,215,214,274,279,231,232,233,234,235,236,237,238,260,261,262,263,285,401,402,403,601,602,603,613,614,631,632,633,634,644,645,646,647,648,650)
					and ma.kodobratu in (219,226,228,266,287,288,610,620,621,622,623,624,625,626)
					and ob.logactivityid = 15
					and ob.obratdatetime >= to_date('$fromdate','YYYY-MM-DD')
					and ob.obratdatetime <= to_date('$todate','YYYY-MM-DD')
				
				union all
				select 
					320 as poradie,
					'Poplatok za transakciu s CP'  as popis, pr.mena as mena1, 
					pr.suma as transsuma,	u.datesplatnost as datum, 
					'Dátum obchodu '||to_char(pr.datum,'DD.MM.YYYY')||'; ISIN '||kcp.ISIN as poznamka, 
					ma.md_d 
				from
					uhrada u, uhradaobratid ui, majetokarchiv ma, poplatok_register_links prl, poplatok_register pr, konfirmaciacp kcp
				where
					u.kodobratu = 350
					and u.id = ui.id
					and ma.obratid = ui.obratid
					and ma.$fondid_add
					and ma.uctovnykod = 221110
					and u.logactivityid = 15
					and datesplatnost >= to_date('$fromdate','YYYY-MM-DD')
					and datesplatnost <= to_date('$todate','YYYY-MM-DD')
					and prl.uhrada_id = u.id
					and pr.id = prl.poplatok_register_id
					and pr.typ = 'TRAN'
					and pr.fondid = ma.subjektid
					and pr.dealid = kcp.dealid

				union all 
				select 
					330 as poradie,
					'Poplatok za vysporiadanie transakcie s CP' as popis, pr.mena as mena1, 
					pr.suma as transsuma,	u.datesplatnost as datum, 
					'Dátum obchodu '||to_char(pr.datum,'DD.MM.YYYY')||'; ISIN '||kcp.ISIN as poznamka, 
					 ma.md_d
				from
					uhrada u, uhradaobratid ui, majetokarchiv ma, poplatok_register_links prl, poplatok_register pr, konfirmaciacp kcp
				where
					u.kodobratu = 350
					and u.id = ui.id
					and ma.obratid = ui.obratid
					and ma.$fondid_add
					and ma.uctovnykod = 221110
					and u.logactivityid = 15
					and datesplatnost >= to_date('$fromdate','YYYY-MM-DD')
					and datesplatnost <= to_date('$todate','YYYY-MM-DD')
					and prl.uhrada_id = u.id
					and pr.id = prl.poplatok_register_id
					and pr.typ = 'VYROV'
					and pr.fondid = ma.subjektid
					and pr.dealid = kcp.dealid

				union all 
				select 
					350 as poradie,
					'Poplatok za konverziu' as popis, ma.mena as mena1,
					ma.pocet as transsuma, ma.obratdatatimezauctovane as datum, 
					'' as poznamka,
					ma.md_d 
					
				from
					majetokarchiv ma
				where
					ma.$fondid_add
					and ma.kodobratu = 346
					and ma.uctovnykod = 261992
					and ma.obratdatatimezauctovane >= to_date('$fromdate','YYYY-MM-DD')
					and ma.obratdatatimezauctovane <= to_date('$todate','YYYY-MM-DD')

				union all
				select 
					340 as poradie,
					'Poplatok za obchod na peňažnom trhu' as popis, mena as mena1,
					ma.pocet as transsuma, ma.obratdatatimezauctovane as datum, 
					'' as poznamka,
					ma.md_d 
				from
					majetokarchiv ma
				where
					ma.$fondid_add
					and ma.kodobratu = 345
					and ma.uctovnykod = 261991
					and ma.obratdatatimezauctovane >= to_date('$fromdate','YYYY-MM-DD')
					and ma.obratdatatimezauctovane <= to_date('$todate','YYYY-MM-DD')
				
				union all
				select 
					310 as poradie,
					'Poplatok za správu portfólia' as popis, pr.mena as mena1,
					pr.suma as transsuma, u.datesplatnost as datum, 
					to_char(pr.datumod,'DD.MM.YYYY')||' - '||to_char(pr.datum,'DD.MM.YYYY') as poznamka,
					md_d
				from
					uhrada u, uhradaobratid ui, majetokarchiv ma, poplatok_register_links prl, poplatok_register pr
				where
					u.kodobratu = 350
					and u.id = ui.id
					and ma.obratid = ui.obratid
					and ma.$fondid_add
					and ma.uctovnykod = 221110
					and logactivityid = 15
					and datesplatnost >= to_date('$fromdate','YYYY-MM-DD')
					and datesplatnost <= to_date('$todate','YYYY-MM-DD')
					and prl.uhrada_id = u.id
					and pr.id = prl.poplatok_register_id
					and pr.typ = 'SPRAVA'
					and pr.fondid = ma.subjektid
				
				union all
				select 
					300 as poradie,
					'Poplatok za riadenie portfólia' as popis, ma.mena as mena1, 
					pr.suma as transsuma, u.datesplatnost as datum, 
					to_char(pr.datumod,'DD.MM.YYYY')||' - '||to_char(pr.datum,'DD.MM.YYYY') as poznamka,
					ma.md_d 
				from
					uhrada u, uhradaobratid ui, majetokarchiv ma, poplatok_register_links prl, poplatok_register pr
				where
					u.kodobratu = 350
					and u.id = ui.id
					and ma.obratid = ui.obratid
					and ma.$fondid_add
					and ma.uctovnykod = 221110
					and logactivityid = 15
					and datesplatnost >= to_date('$fromdate','YYYY-MM-DD')
					and datesplatnost <= to_date('$todate','YYYY-MM-DD')
					and prl.uhrada_id = u.id
					and pr.id = prl.poplatok_register_id
					and pr.typ = 'MANAZ'
					and pr.fondid = ma.subjektid
					
				union all
				select 
					360 as poradie,
					'Poplatok' as popis, ma.mena as mena1, 
					pr.suma as transsuma, u.datesplatnost as datum, 
					pr.dovod as poznamka,
					ma.md_d 
				from
					uhrada u, uhradaobratid ui, majetokarchiv ma, poplatok_register_links prl, poplatok_register pr
				where
					u.kodobratu = 350
					and u.id = ui.id
					and ma.obratid = ui.obratid
					and ma.$fondid_add
					and ma.uctovnykod = 221110
					and logactivityid = 15
					and datesplatnost >= to_date('$fromdate','YYYY-MM-DD')
					and datesplatnost <= to_date('$todate','YYYY-MM-DD')
					and prl.uhrada_id = u.id
					and pr.id = prl.poplatok_register_id
					and pr.typ = 'OSTATNE'
					and pr.fondid = ma.subjektid	

				union all
				select 
					370 as poradie,
					'Vstupný poplatok' as popis, mena as mena1,
					pocet as transsuma, obratdatatimezauctovane as datum, 
					'' as poznamka, 
					md_d
				from
					majetokarchiv
				where
					$fondid_add
					and kodobratu = 310
					and uctovnykod = 261961
					and obratdatatimezauctovane >= to_date('$fromdate','YYYY-MM-DD')
					and obratdatatimezauctovane <= to_date('$todate','YYYY-MM-DD')

				union all
				select 
					380 as poradie,
					'Poplatok za mimoriadny výpis' as popis, mena as mena1, 
					pocet as transsuma, obratdatatimezauctovane as datum, 
					'' as poznamka, 
					md_d
				from
					majetokarchiv
				where
					$fondid_add
					and kodobratu = 344
					and uctovnykod = 261995
					and obratdatatimezauctovane >= to_date('$fromdate','YYYY-MM-DD')
					and obratdatatimezauctovane <= to_date('$todate','YYYY-MM-DD')

				union all
				select 
					200 as poradie,
					'Konverzia - úhrada' as popis, 
					k.menadebet as mena1, 
					COALESCE((select pd.transsumareal from pool p, pooldetailreal pd where p.dealid=k.dealid and pd.poolid=p.poolid and pd.subjektid=ma.subjektid),u.suma)  as transsuma,
					u.datesplatnost as datum, 
					(
					 	case
					 		when menadebet||menakredit = menovypar then  menadebet || '/' || menakredit
					 		else menakredit || '/' || menadebet
					 	end
					 )  
					|| ' kurz = ' || 
					(case
						when k.kurz < 1 then replace(('0' || k.kurz::text),'.',',')
						else replace((k.kurz::text),'.',',')
					 end
					) 
					|| '' as poznamka, 
					ma.md_d
				from
					uhrada u, uhradaobratid ui, majetokarchiv ma, konverzia k
				where
					u.kodobratu = 334
					and u.id = ui.id
					and ma.obratid = ui.obratid
					and ma.$fondid_add
					and ma.kodobratu = 334
					and ma.uctovnykod = 261914
					and u.logactivityid = 15
					and datesplatnost >= to_date('$fromdate','YYYY-MM-DD')
					and datesplatnost <= to_date('$todate','YYYY-MM-DD')
					and u.dealid = k.dealid
				
					
				union all
				SELECT 90                         AS poradie,
                       'Konverzia - došlá platba' AS popis,
                       ob.mena                    AS mena1,
                       ma.pocet                   AS transsuma,
                       ma.obratdatatimezauctovane AS datum,
                       foo.popis                  AS poznamka,
                       ma.md_d
                FROM obratybu ob
                         JOIN
                     obratybuobratid obo ON ob.id = obo.id
                         JOIN
                     majetokarchiv ma ON ma.obratid = obo.obratid
                         LEFT JOIN
                     (SELECT CASE
                                 WHEN menadebet || menakredit = menovypar THEN menadebet || '/' || menakredit
                                 ELSE menakredit || '/' || menadebet
                                 END || ' kurz = ' ||
                             CASE
                                 WHEN k.kurz < 1 THEN REPLACE(LPAD(REPLACE(k.kurz::text, '999999999.999999', ''), 10, '0'), '.', ',')
                                 ELSE REPLACE(REPLACE(k.kurz::text, '999999999.999999', ''), '.', ',')
                                 END AS popis,
                             mak.OBRATDATATIMEZAUCTOVANE,
                             mak.pocet,
                             mak.mena,
                             mak.subjektid
                      FROM konverzia k
                               JOIN
                           konverziaobratid ko ON k.dealid = ko.dealid
                               JOIN
                           majetokarchiv mak ON ko.obratid = mak.obratid
                      WHERE mak.kodobratu = 237
                        AND mak.uctovnykod = 315160) foo ON ma.pocet = foo.pocet
                         AND ma.mena = foo.mena
                         AND ma.OBRATDATATIMEZAUCTOVANE = foo.OBRATDATATIMEZAUCTOVANE
                         AND ma.subjektid = foo.subjektid
                WHERE ma.subjektid in (1752)
                  AND ma.kodobratu = 237
                  AND ma.uctovnykod = 325300
                  AND ob.logactivityid = 15
                  AND ob.obratdatetime BETWEEN TO_DATE('2023-01-01', 'YYYY-MM-DD') AND TO_DATE('2023-07-05', 'YYYY-MM-DD')
                  AND ob.krdb = 1
			union all		
				select 	
					410 as poradie,
					(case
					 when k1.druhobchodu = 'prevod' then 'Prevod'
					 
					 end
					) ||
					' na portfólio č.' || (select cislozmluvy from portfolio where fondid = k2.subjektid)  as popis, k1.mena as mena1, 
					k1.suma as transsuma, k1.datum_zauctovania as datum, 
					'' as poznamka, 
					1
				from
					konfirmaciapp k1,
					konfirmaciapp k2
				where
					k1.dealid_related is not null
					and k1.$fondid_add
					and k1.logactivityid = 12
					and k1.druhobchodu in ('prevod')
					and k1.subjektid != 0
					and k2.dealid = k1.dealid_related
					and k1.datum_zauctovania >= to_date('$fromdate','YYYY-MM-DD')
					and k1.datum_zauctovania <= to_date('$todate','YYYY-MM-DD')
					
			union all
				select 	
					100 as poradie,
					(case
					 when k1.druhobchodu = 'prevod' then 'Prevod'
					 end
					) ||
					' z portfólia č.' || (select cislozmluvy from portfolio where fondid = k2.subjektid)  as popis, k1.mena as mena1, 
					k1.suma as transsuma, k1.datum_zauctovania as datum, 
					'' as poznamka, 
					0
				from
					konfirmaciapp k1,
					konfirmaciapp k2
				where
					k1.dealid_related is null
					and k1.$fondid_add 
					and k1.logactivityid = 12
					and k1.druhobchodu in ('prevod')
					and k1.subjektid != 0
					and k2.dealid_related = k1.dealid
					and k1.datum_zauctovania >= to_date('$fromdate','YYYY-MM-DD')
					and k1.datum_zauctovania <= to_date('$todate','YYYY-MM-DD')
			union all			
				select
					110 as poradie,
					'Ostatné platby' as popis,
					m.mena as mena1, 
					m.pocet as transsuma, 
					m.obratdatatimezauctovane as datum, 
					ob.nazpartnera as poznamka,
					0
				from
					majetokarchiv m,
					obratybu ob,
					obratybuobratid obo
				where
					m.obratdatatimezauctovane >= to_date('$fromdate','YYYY-MM-DD')
					and m.obratdatatimezauctovane <= to_date('$todate','YYYY-MM-DD')		 			
		 			and m.$fondid_add  
		 			and m.uctovnykod = 668000 
		 			and m.md_d = 1 
                 			and m.KODOBRATU = 214 
					and ob.id = obo.id
					and m.obratid = obo.obratid
             				--neberiem vkladypp ani opravy vkladov
		 			and not exists
		 				(
		  					select 1
		  					from
		  						konfirmaciapp k5,
		 						obratybu o5,
		  						obratybuobratid oo5
		  					where
		  						k5.subjektid = m.subjektid and
		  						k5.logactivityid = 12 and
		  						o5.subjektid = k5.subjektid and
		  						o5.ss = (k5.dealid::text) and
		  						o5.suma = k5.suma and
		  						o5.mena = k5.mena and
		  						o5.cub = k5.ucet and
		  						oo5.id = o5.id and
		  						oo5.obratid = m.obratid
                  					union all
                  					select 1
		  					from
                  						majetokarchiv ma5
							where
		 						ma5.OBRATDATATIMEZAUCTOVANE = m.OBRATDATATIMEZAUCTOVANE and
		 						ma5.$fondid_add and
		 						ma5.uctovnykod = 668000 and
		 						ma5.md_d = 0 and
                 						ma5.KODOBRATU = 214
		 				)
	
			union all

				select
					420 as poradie,
					'Výber klienta' as popis,
					k.mena as mena1,
					k.suma as transsuma,
					k.datum_zauctovania as datum,
					k.externy_ucet as poznamka,
					1
				from konfirmaciapp k
				where
					k.druhobchodu = 'vyber'
					and k.$fondid_add
					and k.logactivityid = 12
					and k.datum_zauctovania >= to_date('$fromdate','YYYY-MM-DD')
					and k.datum_zauctovania <= to_date('$todate','YYYY-MM-DD')
					
			union all
				select
					120 as poradie,
					'Vklad klienta' as popis,
					k.mena as mena1,
					k.suma as transsuma,
					k.datum_zauctovania as datum,
					k.externy_ucet as poznamka,
				--	(select cubpartnera from obratybu o where (to_char(o.ss) = k.dealid::text)) as poznamka,
					0
				from konfirmaciapp k
				where
					k.druhobchodu = 'vklad'
					and k.$fondid_add
					and k.logactivityid = 12
					and k.datum_zauctovania >= to_date('$fromdate','YYYY-MM-DD')
					and k.datum_zauctovania <= to_date('$todate','YYYY-MM-DD')
				
				) t
				order by mena1, mydatum, poradie";

    $transakcieRes = Connection::getDataFromDatabase($query, defaultDB);
    $transakcie = $transakcieRes[1];
    $cnt = 0;
    $mena_foo = "";
    $sekcia_ukoncena = true;
    $sumacheck = 0;
    $sumaAlert = "";

    foreach ($transakcie as $item) {
        if ($item['datum'] != '') {
            $datum = $item['datum'];
        } else {
            $datum = '';
        }
        if ($item['poznamka'] != '') {
            $poznamka = " (" . $item['poznamka'] . ")";
        } else {
            $poznamka = '';
        }
        $popis = $item['popis'];
        $suma = $item['transsuma'];
        $suma = $suma * 1;
        $cnt++;

        $mena = $item['mena1'];
        if ($mena_foo != $mena)
            $sumacheck = 0;
        $poradie = $item['poradie'];

        if (intval($poradie) == 100000) {
            $sekcia_ukoncena = true;
            if (round($sumacheck, 2) != round($suma, 2)) {
                $sumaAlert = "Neplatná zostava !";
                $sumaAlertBottom = "Neplatná zostava !";
            }
        } else {
            if ($item['md_d'])
                $sumacheck -= $suma;
            else
                $sumacheck += $suma;
        }

        if ($mena_foo != $mena) {
            if ($mena_foo != "") {
                if (!$sekcia_ukoncena) {
                    //pokial prebehli transakcie na ucte v jednom dni a v majetoktotal neexistuje zaznam, zostatok je nulovy
                    //teda v poslednom dni prebehli transakcie a zostatok na ucte je nulovy, do majetoktotal sa nezapise ...
                    ?>
                    <tr>
                        <td><?php echo (EURConversion::getConversionMode($tms_todate) == 2 and $mena_foo == EURConversion::GetSecondaryCurrency($tms_todate)) ? "00-00-0000" : $etodate; ?>
                        </td>
                        <td><?php echo 'Konečný stav účtu' ?></td>
                        <td class="euro"><?php echo EURConversion::PrintDualPriceAmount($tms_todate, 0, $mena_foo) ?></td>
                        <td class="vpravo"><?php echo round(0, 2) ?></td>
                    </tr>


                    <?php
                    if (round($predoslasumacheck, 2) != round(0, 2)) {
                        $sumaAlert = "Neplatná zostava !";
                        $sumaAlertBottom = "Neplatná zostava !";
                    }
                }
                //ukoncenie predchadzajucej casti vypisu
                ?>
                </tbody>
                </table>
                <?php
                if ($sumaAlert != "") {
                    ?>
                    <table>
                        <tr>
                            <td class="velkepismo">
                                <?php echo $sumaAlert ?>
                            </td>
                        </tr>
                    </table>
                    <?php
                    $sumaAlert = "";
                }
                ?>
                <?php
            }
            //zacina nova sekcia vypisu
            $sekcia_ukoncena = false;
            ?>
            <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
                <tr>
                    <td class="text-2xl font-bold flex items-center gap-4 mb-5 mt-4">
                        Výpis z bežného účtu (<?php echo $mena ?>)
                    </td>
                </tr>
            </table>
            <table class="w-full">
                <thead class="bg-gray-300 dark:bg-gray-700 dark:text-gray-100">
                    <tr>
                        <th scope="col" class="px-6 py-3">Dátum</th>
                        <th scope="col" class="px-6 py-3">Transakcia</th>
                        <th scope="col" class="px-6 py-3">
                            <?php echo (EURConversion::ShowDualPrice($tms_todate, $mena)) ? "Duálne zobrazenie (" . EURConversion::GetSecondaryCurrency($tms_todate, $mena) . ")" : "" ?>
                        </th>
                        <th scope="col" class="px-6 py-3">Čiastka</th>
                    </tr>
                </thead>
                <tbody class="dark:bg-gray-900 dark:text-gray-200">
                    <?php
                    if ($poradie != -1) { ?>
                        <tr class="dark:hover:bg-gray-600 transition-all mb-2 border-b border-gray-700">
                            <td class="text-center py-2"><?php echo $fromdate; ?></td>
                            <td class="px-3 py-2 font-bold"><?php echo 'Počiatočný stav účtu' ?></td>
                            <td class="text-center py-2"><?php echo EURConversion::PrintDualPriceAmount($tms_todate, 0, $mena) ?>
                            </td>
                            <td class="text-center py-2"><?php echo round(0, 2) ?></td>
                        </tr>
                        <?php
                    }
        }
        ?>
                <tr class="dark:hover:bg-gray-600 transition-all mb-2 border-b border-gray-700">
                    <td class="text-center py-2"><?php echo $datum; ?></td>
                    <td class="px-3 py-2"><?php echo $popis . $poznamka ?></td>
                    <td class="text-center py-2">
                        <?php echo EURConversion::PrintDualPriceAmount($tms_todate, ($item['md_d'] == 0 ? '' : '-') . $suma, $mena); ?>
                    </td>
                    <td class="text-center py-2"><?php echo ($item['md_d'] == 0 ? '' : '-') . round($suma, 2); ?></td>
                </tr>
                <?php
                $mena_foo = $mena;
                $predoslasuma = $suma;
                $predoslasumacheck = $sumacheck;
    }
    //koniec while --------------------------------------------------------------
    if ($cnt == 0) {
        ?>
                <table>
                    <tr>
                        <td class="text-2xl font-bold flex items-center gap-4 mb-5 mt-4">
                            Výpis z bežného účtu
                        </td>
                    </tr>
                </table>
                <table class="w-full">
                    <thead class="bg-gray-300 dark:bg-gray-700 dark:text-gray-100">
                        <tr>
                            <th scope="col" class="px-6 py-3">Dátum</th>
                            <th scope="col" class="px-6 py-3">Transakcia</th>
                            <th scope="col" class="px-6 py-3">
                                <?php echo (EURConversion::ShowDualPrice($tms_todate, $mena)) ? "Duálne zobrazenie (" . EURConversion::GetSecondaryCurrency($tms_todate, $mena) . ")" : "" ?>
                            </th>
                            <th scope="col" class="px-6 py-3">Čiastka</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td colspan="4" class="py-6 bg-red-300"><span class="px-6">Pre zvolené obdobie neprebehli žiadne
                                    transakcie</span>
                            </td>
                        </tr>
                        <?php
    } elseif (!$sekcia_ukoncena) {
        //pokial prebehli transakcie na ucte v jednom dni a v majetoktotal neexistuje zaznam, zostatok je nulovy
        //teda v poslednom dni prebehli transakcie a zostatok na ucte je nulovy, do majetoktotal sa nezapise ...
        ?>
                        <tr>
                            <td class="text-center py-2">
                                <?php echo (EURConversion::getConversionMode($tms_todate) == 2 and $mena_foo == EURConversion::GetSecondaryCurrency($tms_todate)) ? "00-00-0000" : $etodate; ?>
                            </td>
                            <td class="font-bold py-2"><?php echo 'Konečný stav účtu' ?></td>
                            <td class="text-center py-2">
                                <?php echo EURConversion::PrintDualPriceAmount($tms_todate, 0, $mena_foo) ?>
                            </td>
                            <td class="text-center py-2"><?php echo round(0, 2) ?></td>
                        </tr>

                        <?php
                        if (round($predoslasumacheck, 2) != round(0, 2)) {
                            $sumaAlert = "Neplatná zostava !";
                            $sumaAlertBottom = "Neplatná zostava !";
                        }
    }
    //ukoncenie predchadzajucej casti vypisu
    ?>
                </tbody>
            </table>
            <?php
            if ($sumaAlert != "") {
                ?>
                <table class="w-full">
                    <tr>
                        <td class="text-2xl font-bold flex items-center gap-4 mb-5 mt-4">
                            <?php echo $sumaAlert ?>
                        </td>
                    </tr>
                </table>
                <?php
                $sumaAlert = "";
            }
            $query = "
			select * from
			(
			SELECT 
				   (case when z_td < to_date('$fromdate','YYYY-MM-DD') then 1
				   		 when k_td > to_date('$todate','YYYY-MM-DD') then 3
				   		 else 2
				   	end) as trieda,
				   k.z_td as zaciatok,
				   k.k_td as koniec,
				   k.sum_td as suma,
				   k.ir_td as sadzba,
				   k.iv_b as brutto,
				   k.iv_n as netto_old,
				   k.iv_n as netto,
				   k.mena,
				   k.suma_dane as dan,
				   k.datvysporiadaniarealntd as vysporiadanie,
				   k.cutd,
				   k.subjektid,
				   z_td,
				   k_td,
				   p.nazovpartnera as miesto,
				   k.datum_cas_obchodu as datum_cas,
				   (select typ_pokynu from typy_pokynov tp where tp.typid = k.typ_pokynu) as typPokynu,
				   '' as detailKTV
			from 
				konfirmaciaktv k,
				partner p
			where 
				((z_td >= to_date('$fromdate','YYYY-MM-DD') and z_td <= to_date('$todate','YYYY-MM-DD'))
				 or
				 (k_td >= to_date('$fromdate','YYYY-MM-DD') and k_td <= to_date('$todate','YYYY-MM-DD')))
				and logactivityid in (17, 25)
			 	and k.$fondid_add 
				and k.partnerid = p.partnerid	
			
			union all
				
			select 	   
				   (case when z_td < to_date('$fromdate','YYYY-MM-DD') then 1
				   		 when k_td > to_date('$todate','YYYY-MM-DD') then 3
				   		 else 2
				   	end) as trieda,
				   k.z_td as zaciatok,
				   k.k_td as koniec,
				   pdr.transsumareal as suma,
				   k.ir_td as sadzba,
				   pdr.auvreal  as brutto,
	   	   		   k.iv_n * pdr.transsumareal / (select sum(x.transsumareal) from pooldetailreal x where x.poolid=po.poolid) as netto_old,
	   	   		   (pdr.auvreal - pdr.dan) as netto,
				   k.mena,
				   pdr.dan as dan,
				   k.datvysporiadaniarealntd as vysporiadanie,
				   k.cutd,
				   k.subjektid,
				   z_td,
				   k_td,
				   p.nazovpartnera as miesto,
				   k.datum_cas_obchodu as datum_cas,
				   (select typ_pokynu from typy_pokynov tp where tp.typid = k.typ_pokynu) as typPokynu,
				   (
					 case
					  when sum_td_pov is null then ''
					  else 'uzavreteSKK'
					 end 
					 ) as detailKTV
			from
				konfirmaciaktv k,
				pool po,
				pooldetailreal pdr,
				partner p
			where
				((z_td >= to_date('$fromdate','YYYY-MM-DD') and z_td <= to_date('$todate','YYYY-MM-DD'))
				 or
				 (k_td >= to_date('$fromdate','YYYY-MM-DD') and k_td <= to_date('$todate','YYYY-MM-DD')))
				 and k.subjektid = 0
				 and pdr.poolid = po.poolid 
				 and pdr.$fondid_add
				 and k.dealid = po.dealid
				 and k.logactivityid in (17, 25)
				 and k.partnerid = p.partnerid
				 
				 
			union all
			
			select 	   
				   (case when z_td < to_date('$fromdate','YYYY-MM-DD') then 1
				   		 when k_td > to_date('$todate','YYYY-MM-DD') then 3
				   		 else 2
				   	end) as trieda,
				   k.z_td as zaciatok,
				   k.k_td as koniec,
				   pdr.transsumareal_pov as suma,
				   k.ir_td as sadzba,
				   0  as brutto,
	   	   	 0 as netto_old,
	   	   	 0 as netto,
				   'SKK',
				   0 as dan,
				   k.datvysporiadaniarealntd as vysporiadanie,
				   k.cutd,
				   k.subjektid,
				   z_td,
				   k_td,
				   p.nazovpartnera as miesto,
				   k.datum_cas_obchodu as datum_cas,
				   (select typ_pokynu from typy_pokynov tp where tp.typid = k.typ_pokynu) as typPokynu,
				   'vyplateneEUR' as detailKTV
			from
				konfirmaciaktv k,
				pool po,
				pooldetailreal pdr,
				partner p
			where
				((z_td >= to_date('$fromdate','YYYY-MM-DD') and z_td <= to_date('$todate','YYYY-MM-DD'))
				 or
				 (k_td >= to_date('$fromdate','YYYY-MM-DD') and k_td <= to_date('$todate','YYYY-MM-DD')))
				 and k.subjektid = 0
				 and pdr.poolid = po.poolid 
				 and pdr.$fondid_add
				 and k.dealid = po.dealid
				 and k.logactivityid in (17, 25)
				 and k.partnerid = p.partnerid
				 and k.sum_td_pov is not null -- ktv po konverzii preratane do EUR	 
			) as kpkp
			order by mena, trieda, z_td, k_td";
            $secondResultRes = Connection::getDataFromDatabase($query, defaultDB);
            $secondResult = $secondResultRes[1];
            $cnt = 0;
            $mena_foo = "";
            $trieda_foo = '';

            foreach ($secondResult as $item) {
                $cnt++;
                $mena = $item["mena"];
                $suma = $item["suma"];
                $sadzba = $item["sadzba"];
                $brutto = $item["brutto"];
                $dan = $item["dan"];
                $netto = $item["netto"];
                $zaciatok = $item["zaciatok"];
                $koniec = $item["koniec"];
                $cisloKTV = $item["cutd"];
                $trieda = $item["trieda"];
                $miesto = $item["miesto"];
                $datum_cas = $item["datum_cas"];
                $typ_pokynu = $item["typPokynu"];
                $detailKTV = $item['detailKTV'];
                $style_hr = "";
                if ($mena_foo != $mena) {
                    if ($mena_foo != "") {
                        ?>
                    </tbody>
                </table>
                <?php
                    }
                    ?>

            <table class="w-full">
                <tr>
                    <td class="text-2xl font-bold flex items-center text-gray-500 gap-4 mb-5 mt-4">
                        Prehľad termínovaných vkladov (<?php echo $mena ?>)
                    </td>
                </tr>
            </table>
            <table class="w-full">
                <thead class="bg-gray-300 dark:bg-gray-700 dark:text-gray-100">
                    <tr>
                        <th scope="col" class="px-6 py-3">Uzavretie TV</th>
                        <th scope="col" class="px-6 py-3">Zriadenie TV</th>
                        <th scope="col" class="px-6 py-3">Splatnosť TV</th>
                        <th scope="col" class="px-6 py-3">Miesto výkonu</th>
                        <th scope="col" class="px-6 py-3">Typ pokynu</th>
                        <th scope="col" class="px-6 py-3">Suma</th>
                        <th scope="col" class="px-6 py-3">Sadzba</th>
                        <th scope="col" class="px-6 py-3">Úrok brutto</th>
                        <th scope="col" class="px-6 py-3">Daň</th>
                        <th scope="col" class="px-6 py-3">Úrok netto</th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                } elseif ($trieda_foo != $trieda) {
                    $style_hr = 'style="border-top: 0.25mm dashed #aaaaaa"';
                }
                ?>
                <tr>
                    <?php
                    if ($detailKTV == "uzavreteSKK") {
                        ?>
                        <td <?php echo $style_hr ?>>Uzavreté v SKK</td>
                        <?php
                    } else {
                        ?>
                        <td <?php echo $style_hr ?>><?php echo $datum_cas; ?></td>
                        <?php
                    }
                    ?>
                    <td class="text-center py-2" <?php echo $style_hr ?>><?php echo $zaciatok; ?></td>
                    <td class="text-center py-2" <?php echo $style_hr ?>><?php echo $koniec; ?></td>
                    <td class="text-center py-2" <?php echo $style_hr ?>><?php echo $miesto; ?></td>
                    <td class="text-center py-2" <?php echo $style_hr ?>><?php echo $typ_pokynu; ?></td>
                    <td class="text-center py-2" <?php echo $style_hr ?>><?php echo round($suma, 2); ?></td>
                    <td class="text-center py-2" <?php echo $style_hr ?>><?php echo round($sadzba, 4); ?></td>
                    <?php if ($detailKTV == "vyplateneEUR") {
                        ?>
                        <td colspan="3" class="text-center py-2" <?php echo $style_hr ?>>Vyplatené v EUR</td>
                        <?php
                    } else {
                        ?>
                        <td class="text-center py-2" <?php echo $style_hr ?>>
                            <?php echo $trieda == 3 ? "&nbsp;" : round($brutto, 2); ?>
                        </td>
                        <td class="text-center py-2" <?php echo $style_hr ?>>
                            <?php echo $trieda == 3 ? "&nbsp;" : round($dan, 2); ?>
                        </td>
                        <td class="text-center py-2" <?php echo $style_hr ?>>
                            <?php echo $trieda == 3 ? "&nbsp;" : round($netto, 2); ?>
                        </td>
                        <?php
                    }
                    ?>
                </tr>

                <?php
                $mena_foo = $mena;
                $trieda_foo = $trieda;
            }
            if ($cnt == 0) {
                ?>
                <table class="w-full">
                    <tr>
                        <td class="text-2xl font-bold flex text-gray-500 items-center gap-4 mb-5 mt-4">
                            Prehľad termínovaných vkladov (<?php echo $mena ?>)
                        </td>
                    </tr>
                </table>
                <table class="w-full">
                    <thead class="bg-gray-300 dark:bg-gray-700 dark:text-gray-100">
                        <tr>
                            <th scope="col" class="px-6 py-3">Uzavretie TV</th>
                            <th scope="col" class="px-6 py-3">Zriadenie TV</th>
                            <th scope="col" class="px-6 py-3">Splatnosť TV</th>
                            <th scope="col" class="px-6 py-3">Miesto <BR> výkonu</th>
                            <th scope="col" class="px-6 py-3">Typ <BR> pokynu</th>
                            <th scope="col" class="px-6 py-3">Suma</th>
                            <th scope="col" class="px-6 py-3">Sadzba</th>
                            <th scope="col" class="px-6 py-3">Úrok brutto</th>
                            <th scope="col" class="px-6 py-3">Daň</th>
                            <th scope="col" class="px-6 py-3">Úrok netto</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td colspan="10" class="py-6 px-10 font-bold bg-red-300"><span class="px-6">Pre zvolené obdobie
                                    neprebehli žiadne transakcie</span>
                            </td>
                        </tr>
                        <?php
            }
            ?>
                </tbody>
            </table>
            <table>
                <tr>
                    <td class="text-2xl font-bold flex text-gray-500 items-center gap-4 mb-5 mt-4">
                        Prehľad menových konverzíí
                    </td>
                </tr>
            </table>
            <table class="w-full">
                <thead class="bg-gray-300 dark:bg-gray-700 dark:text-gray-100">
                    <tr>
                        <th scope="col" class="px-6 py-3">Typ konverzie</th>
                        <th scope="col" class="px-6 py-3">Uzavretie konverzie</th>
                        <th scope="col" class="px-6 py-3">Vyrovnanie konverzie</th>
                        <th scope="col" class="px-6 py-3">Miesto <BR> výkonu</th>
                        <th scope="col" class="px-6 py-3">Typ <BR> pokynu</th>
                        <th scope="col" class="px-6 py-3">Menový pár</th>
                        <th scope="col" class="px-6 py-3">Kurz</th>
                        <th scope="col" class="px-6 py-3">Objem nakupovanej meny</th>
                        <th scope="col" class="px-6 py-3">Objem predávanej meny</th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    $query = "
	select
    *
from
    (
        (
            WITH maj_data AS (
                SELECT
                    obratid,
                    obratdatatimezauctovane,
                    kodaktiva
                FROM
                    majetoktoday
                WHERE
                    uctovnykod IN (251110, 251200, 251300)
                    AND obratdatatimezauctovane BETWEEN '$fromdate'
                    AND '$todate'
                    AND destinacia NOT LIKE 'muprevod'
                UNION
                ALL
                SELECT
                    obratid,
                    obratdatatimezauctovane,
                    kodaktiva
                FROM
                    majetokarchiv
                WHERE
                    uctovnykod IN (251110, 251200, 251300)
                    AND obratdatatimezauctovane BETWEEN '$fromdate'
                    AND '$todate'
                    AND destinacia NOT LIKE 'muprevod'
            ),
            cislozmluvy_data AS (
                SELECT
                    k.dealid,
                    f.cislozmluvy
                FROM
                    konfirmaciacp k
                    JOIN portfolio f ON f.fondid = k.subjektid
                WHERE
                    k.dealid IN (
                        SELECT
                            dealid - 1000
                        FROM
                            konfirmaciacp
                    )
                    OR k.dealid IN (
                        SELECT
                            dealid + 1000
                        FROM
                            konfirmaciacp
                    )
            ),
            typy_pokynu_data AS (
                SELECT
                    typid,
                    typ_pokynu
                FROM
                    typy_pokynov
            ),
            floatkupon_data AS (
                SELECT
                    isincurrric,
                    faktor,
                    datefrom,
                    datetill
                FROM
                    floatkupon
            )
            SELECT
                CASE
                    WHEN kcp.dealid IN (46663904, 47124904) THEN 'Predčasné splatenie dlhopisu na výzvu emitenta'
                    WHEN kcp.druhobchodu = 'nakup' THEN 'Nákup'
                    WHEN kcp.druhobchodu = 'predaj' THEN 'Predaj'
                    WHEN kcp.druhobchodu = 'vklad' THEN 'Vklad'
                    WHEN kcp.druhobchodu = 'vyber' THEN 'Výber'
                    WHEN kcp.druhobchodu = 'vklad-pr' THEN 'Prevod z port. c.' || cz.cislozmluvy
                    WHEN kcp.druhobchodu = 'vyber-pr' THEN 'Prevod na port. c.' || cz.cislozmluvy
                    WHEN kcp.druhobchodu = 'vklad-vy' THEN 'Výmena CP - pripísanie CP'
                    WHEN kcp.druhobchodu = 'vyber-vy' THEN 'Výmena CP - odpísanie CP'
                END AS popis,
                rcp.datvysporiadaniamureal,
                e.isin,
                e.isinreal,
                e.eqid,
                edruh.poddruheq AS poddruheq,
                e.cpnaz,
                e.cpnazskratka,
                rcp.kusovreal AS pocet,
                kcp.currencyidtrade,
                rcp.kurzreal AS kurz,
                rcp.auvreal AS auv,
                rcp.transsuma AS spolu,
                rcp.transsuma - COALESCE(rcp.auvreal, 0) as bezAUV,
                maj.obratdatatimezauctovane AS datum,
                kcp.currencyidtrade AS mena,
                CASE
                    WHEN kcp.dealid IN (46663904, 47124904) THEN '--'
                    ELSE p.nazovpartnera
                END AS miesto,
                kcp.datum_cas_obchodu AS datum_cas,
                CASE
                    WHEN kcp.dealid IN (46663904, 47124904) THEN '--'
                    ELSE tp.typ_pokynu
                END AS typPokynu,
                CASE
                    WHEN kcp.druhobchodu = 'nakup' THEN 0
                    WHEN kcp.druhobchodu = 'predaj' THEN 1
                    WHEN kcp.druhobchodu = 'vklad' THEN 0
                    WHEN kcp.druhobchodu = 'vyber' THEN 1
                    WHEN kcp.druhobchodu = 'vklad-pr' THEN 0
                    WHEN kcp.druhobchodu = 'vyber-pr' THEN 1
                END AS MD_D
            FROM
                konfirmaciacp kcp
                JOIN rekonfirmaciacp rcp ON kcp.dealid = rcp.dealid
                JOIN rekonfirmaciacpobratid rcpo ON rcpo.dealid = rcp.dealid
                AND rcpo.tranza = rcp.tranza
                JOIN dbequity e ON e.isin = kcp.isin
                JOIN equityid eid ON e.eqid = eid.eqid
                JOIN equitydruh edruh ON edruh.druheqid = e.druheqid
                JOIN maj_data maj ON maj.obratid = rcpo.obratid
                LEFT JOIN floatkupon_data fk ON fk.isincurrric = maj.kodaktiva
                AND (maj.obratdatatimezauctovane - e.exfrekist) BETWEEN fk.datefrom
                AND fk.datetill
                LEFT JOIN partner p ON kcp.partnerid = p.partnerid
                LEFT JOIN cislozmluvy_data cz ON cz.dealid = kcp.dealid
                LEFT JOIN typy_pokynu_data tp ON tp.typid = kcp.typ_pokynu
            WHERE
                kcp.$fondid_add
            ORDER BY
                datum,
                isin,
                MD_D
        )
        union
        all (
            WITH maj AS (
                SELECT
                    obratid,
                    obratdatatimezauctovane,
                    subjektid,
                    kodaktiva
                FROM
                    majetoktoday
                WHERE
                    obratid > 0
                    AND uctovnykod IN (251110, 251200, 251300)
                    AND obratdatatimezauctovane BETWEEN '$fromdate'
                    AND '$todate'
                    AND destinacia NOT LIKE 'muprevod'
                UNION
                ALL
                SELECT
                    obratid,
                    obratdatatimezauctovane,
                    subjektid,
                    kodaktiva
                FROM
                    majetokarchiv
                WHERE
                    obratid > 0
                    AND uctovnykod IN (251110, 251200, 251300)
                    AND obratdatatimezauctovane BETWEEN '$fromdate'
                    AND '$todate'
                    AND destinacia NOT LIKE 'muprevod'
            )
            SELECT
                CASE
                    WHEN kcp.dealid = 46663904 THEN 'Predčasné splatenie dlhopisu na výzvu emitenta'
                    ELSE CASE
                        kcp.druhobchodu
                        WHEN 'nakup' THEN 'Nákup'
                        WHEN 'predaj' THEN 'Predaj'
                        WHEN 'vklad' THEN 'Vklad'
                        WHEN 'vyber' THEN 'Výber'
                        WHEN 'vklad-vy' THEN 'Výmena CP - pripísanie CP'
                        WHEN 'vyber-vy' THEN 'Výmena CP - odpísanie CP'
                        ELSE NULL
                    END
                END AS popis,
                rcp.datvysporiadaniamureal,
                e.isin,
                e.isinreal,
                e.eqid,
                edruh.poddruheq,
                e.cpnaz,
                e.cpnazskratka,
                pdr.ksreal AS pocet,
                kcp.currencyidtrade,
                rcp.kurzreal AS kurz,
                pdr.auvreal AS auv,
                pdr.transsumareal AS spolu,
                rcp.transsuma - COALESCE(rcp.auvreal, 0) AS bezAUV,
                maj.obratdatatimezauctovane AS datum,
                kcp.currencyidtrade AS mena,
                CASE
                    WHEN kcp.dealid = 46663904 THEN '--'
                    ELSE p.nazovpartnera
                END AS miesto,
                kcp.datum_cas_obchodu AS datum_cas,
                CASE
                    WHEN kcp.dealid = 46663904 THEN '--'
                    ELSE tp.typ_pokynu
                END AS typPokynu,
                CASE
                    kcp.druhobchodu
                    WHEN 'nakup' THEN 0
                    WHEN 'predaj' THEN 1
                    WHEN 'vklad' THEN 0
                    WHEN 'vyber' THEN 1
                END AS MD_D
            FROM
                konfirmaciacp kcp
                JOIN rekonfirmaciacp rcp ON kcp.dealid = rcp.dealid
                JOIN rekonfirmaciacpobratid rcpo ON rcpo.dealid = rcp.dealid
                AND rcpo.tranza = rcp.tranza
                JOIN dbequity e ON e.isin = kcp.isin
                JOIN equityid eid ON e.eqid = eid.eqid
                JOIN equitydruh edruh ON edruh.druheqid = e.druheqid
                JOIN pool po ON kcp.dealid = po.dealid
                JOIN maj ON maj.obratid = rcpo.obratid
                JOIN pooldetailreal pdr ON pdr.poolid = po.poolid
                AND pdr.subjektid = maj.subjektid
                JOIN partner p ON kcp.partnerid = p.partnerid
                LEFT JOIN typy_pokynov tp ON tp.typid = kcp.typ_pokynu
            WHERE
                kcp.subjektid = 0
                AND pdr.$fondid_add
        )
        union
        all
        select
            'Splatenie' as popis,
            s.datumvyplaty as datvysporiadaniamureal,
            e.isin,
            e.isinreal,
            e.eqid,
            edruh.poddruheq as poddruheq,
            e.cpnaz,
            e.cpnazskratka,
            s.pocet,
            s.mena as currencyidtrade,
            /*100 as kurz,*/
            s.suma * 100 / (e.nominalemisie * s.pocet) as kurz,
            0 as auv,
            s.suma as spolu,
            s.suma as bezAUV,
            s.datum as datum,
            s.mena as mena,
            null as miesto,
            'Aktivita emitenta' as datum_cas,
            null as typPokynu,
            1 as MD_D
        from
            splatenie s,
            dbequity e,
            dbequitycurr c,
            dbequitycurrric r,
            equitydruh edruh
        where
            s.kodaktiva = r.isincurrric
            and r.isincurr = c.isincurr
            and c.isin = e.isin
            and e.druheqid = edruh.druheqid
            and s.uctovnykod = 251110
            and s.datum >= to_date('$fromdate', 'YYYY-MM-DD')
            and s.datum <= to_date('$todate', 'YYYY-MM-DD')
            and s.$fondid_add
            and s.datum_naroku = (
                select
                    max(datesplatnost)
                from
                    floatkupon
                where
                    s.kodaktiva = isincurrric
                    and istina >= 100
            )
        union
        all
        select
            'Čiastočné splatenie priebežné' as popis,
            s.datumvyplaty as datvysporiadaniamureal,
            e.isin,
            e.isinreal,
            e.eqid,
            edruh.poddruheq as poddruheq,
            e.cpnaz,
            e.cpnazskratka,
            s.pocet,
            s.mena as currencyidtrade,
            100 as kurz,
            0 as auv,
            s.suma as spolu,
            s.suma as bezAUV,
            s.datum as datum,
            s.mena as mena,
            null as miesto,
            'Aktivita emitenta' as datum_cas,
            null as typPokynu,
            1 as MD_D
        from
            splatenie s,
            dbequity e,
            dbequitycurr c,
            dbequitycurrric r,
            equitydruh edruh
        where
            s.kodaktiva = r.isincurrric
            and r.isincurr = c.isincurr
            and c.isin = e.isin
            and e.druheqid = edruh.druheqid
            and s.uctovnykod = 251110
            and s.datum >= to_date('$fromdate', 'YYYY-MM-DD')
            and s.datum <= to_date('$todate', 'YYYY-MM-DD')
            and s.$fondid_add
            and s.datum_naroku < (
                select
                    max(datesplatnost)
                from
                    floatkupon
                where
                    s.kodaktiva = isincurrric
                    and istina < 100
            )
        union
        all
        select
            'Čiastočné splatenie konečné' as popis,
            s.datumvyplaty as datvysporiadaniamureal,
            e.isin,
            e.isinreal,
            e.eqid,
            edruh.poddruheq as poddruheq,
            e.cpnaz,
            e.cpnazskratka,
            s.pocet,
            s.mena as currencyidtrade,
            100 as kurz,
            0 as auv,
            s.suma as spolu,
            s.suma as bezAUV,
            s.datum as datum,
            s.mena as mena,
            null as miesto,
            'Aktivita emitenta' as datum_cas,
            null as typPokynu,
            1 as MD_D
        from
            splatenie s,
            dbequity e,
            dbequitycurr c,
            dbequitycurrric r,
            equitydruh edruh
        where
            s.kodaktiva = r.isincurrric
            and r.isincurr = c.isincurr
            and c.isin = e.isin
            and e.druheqid = edruh.druheqid
            and s.uctovnykod = 251110
            and s.datum >= to_date('$fromdate', 'YYYY-MM-DD')
            and s.datum <= to_date('$todate', 'YYYY-MM-DD')
            and s.$fondid_add
            and s.datum_naroku = (
                select
                    max(datesplatnost)
                from
                    floatkupon
                where
                    s.kodaktiva = isincurrric
                    and istina < 100
            )
        union
        all
        select
            da.nazov as popis,
            s.datumvyplaty as datvysporiadaniamureal,
            e.isin,
            e.isinreal,
            e.eqid,
            edruh.poddruheq as poddruheq,
            e.cpnaz,
            e.cpnazskratka,
            s.pocet,
            s.mena as currencyidtrade,
            null as kurz,
            null as auv,
            null as spolu,
            s.pocet as bezAUV,
            s.datumvyplaty as datum,
            s.mena as mena,
            null as miesto,
            'Aktivita emitenta' as datum_cas,
            null as typPokynu,
            da.MD_D as MD_D
        from
            splatenieakcia s,
            dividendaakciatyp da,
            dbequity e,
            dbequitycurr c,
            dbequitycurrric r,
            equitydruh edruh
        where
            s.kodaktiva = r.isincurrric
            and r.isincurr = c.isincurr
            and c.isin = e.isin
            and e.druheqid = edruh.druheqid
            and s.datumvyplaty >= to_date('$fromdate', 'YYYY-MM-DD')
            and s.datumvyplaty <= to_date('$todate', 'YYYY-MM-DD')
            and s.$fondid_add
            and s.subkodobratu in (
                select
                    subkodobratu
                from
                    dividendaakciatyp
                where
                    hotovost = 0
            )
            and da.subkodobratu = s.subkodobratu
    ) as krkr
order by
    datum
	";
                    $thirdResultRes = Connection::getDataFromDatabase($query, defaultDB);
                    $thirdResult = $thirdResultRes[1];
                    $cnt = 0;

                    foreach ($thirdResult as $item) {
                        $datum = $item["datum"];
                        $datum_cas = $item["datum_cas"];
                        $miesto = $item["miesto"];
                        $typPokynu = $item["typPokynu"];
                        $kurz = round($item["kurz"], 5);
                        $sumakredit = round($item["sumakredit"], 2);
                        $sumadebet = round($item["sumadebet"], 2);
                        $menakredit = $item["menakredit"];
                        $menadebet = $item["menadebet"];
                        $druhObchodu = $item["popis"];

                        ?>
                        <tr>
                            <td class="text-center py-2"><?php echo $druhObchodu ?></td>
                            <td class="text-center py-2"><?php echo $datum_cas ?></td>
                            <td class="text-center py-2"><?php echo $datum ?></td>
                            <td class="text-center py-2"><?php echo $miesto ?></td>
                            <td class="text-center py-2"><?php echo $typPokynu ?></td>
                            <td class="text-center py-2"><?php echo $menakredit . " / " . $menadebet ?></td>
                            <td class="text-center py-2"><?php echo $kurz ?></td>
                            <td class="text-center py-2"><?php echo $sumakredit ?></td>
                            <td class="text-center py-2"><?php echo $sumadebet ?></td>
                        </tr>
                        <?php
                        $cnt++;
                    }
                    if ($cnt == 0) {
                        ?>
                        <tr>
                            <td colspan="10" class="py-6 px-10 font-bold bg-red-300"><span class="px-6">Pre zvolené obdobie
                                    neprebehli žiadne transakcie</span>
                            </td>
                        </tr>
                        <?php
                    }
                    ?>
                </tbody>
            </table>
            <table>
                <tr>
                    <td class="text-2xl font-bold flex text-gray-500 items-center gap-4 mb-5 mt-4">
                        Pohyby na majetkovom účte
                    </td>
                </tr>
            </table>
            <table class="w-full">
                <thead>
                    <tr class="bg-gray-300 dark:bg-gray-700 dark:text-gray-100">
                        <th scope="col" class="px-6 py-3">Uzavretie obchodu</th>
                        <th scope="col" class="px-6 py-3">Transakcia</th>
                        <th scope="col" class="px-6 py-3">Názov CP</th>
                        <th scope="col" class="px-6 py-3">ISIN</th>
                        <th scope="col" class="px-6 py-3">Počet CP</th>
                        <th scope="col" class="px-6 py-3">Mena</th>
                        <th scope="col" class="px-6 py-3">Cena CP</th>
                        <th scope="col" class="px-6 py-3">Cena bez AUV</th>
                        <th scope="col" class="px-6 py-3">AUV</th>
                        <th scope="col" class="px-6 py-3">Cena spolu</th>
                    </tr>
                    <tr class="bg-gray-500">
                        <th scope="col" class="px-6 py-3 text-white">Vysporiadanie</th>
                        <th scope="col" class="px-6 py-3 text-white">Typ pokynu</th>
                        <th scope="col" class="px-6 py-3 text-white">Miesto výkonu</th>
                        <th colspan="3" scope="col" class="px-6 py-3 text-white">Druh CP</th>
                        <th colspan="2" scope="col" class="px-6 py-3">
                            <?php echo (EURConversion::GetConversionMode($tms_todate)) ? "Duálne zobrazenie (" . EURConversion::GetSecondaryCurrency($tms_todate) . ")" : "&nbsp;" ?>
                        </th>
                        <th colspan="2" scope="col" class="px-6 py-3">
                            <?php echo (EURConversion::GetConversionMode($tms_todate)) ? "Duálne zobrazenie (" . EURConversion::GetSecondaryCurrency($tms_todate) . ")" : "&nbsp;" ?>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    $pomerNominalu = "(CASE WHEN kcp.eqid='Bonds' THEN (select faktor from floatkupon where isincurrric = maj.kodaktiva and (maj.obratdatatimezauctovane - e.exfrekist)  >= datefrom and (maj.obratdatatimezauctovane - e.exfrekist) <= datetill  ) ELSE 1 END)";
                    $query = "
			select
    *
from
    (
        (
            WITH majetok AS (
                SELECT
                    obratid,
                    obratdatatimezauctovane,
                    kodaktiva
                FROM
                    majetoktoday
                WHERE
                    obratid > 0
                    AND uctovnykod IN (251110, 251200, 251300)
                    AND obratdatatimezauctovane BETWEEN to_date('$fromdate', 'YYYY-MM-DD')
                    AND to_date('$todate', 'YYYY-MM-DD')
                    AND destinacia NOT LIKE 'muprevod'
                UNION
                ALL
                SELECT
                    obratid,
                    obratdatatimezauctovane,
                    kodaktiva
                FROM
                    majetokarchiv
                WHERE
                    obratid > 0
                    AND uctovnykod IN (251110, 251200, 251300)
                    AND obratdatatimezauctovane BETWEEN to_date('$fromdate', 'YYYY-MM-DD')
                    AND to_date('$todate', 'YYYY-MM-DD')
                    AND destinacia NOT LIKE 'muprevod'
            ),
            pre_calc AS (
                SELECT
                    kcp.dealid,
                    CASE
                        WHEN kcp.dealid IN (46663904, 47124904) THEN '--'
                        ELSE p.nazovpartnera
                    END AS miesto,
                    CASE
                        WHEN kcp.dealid IN (46663904, 47124904) THEN 'Predčasné splatenie dlhopisu na výzvu emitenta'
                        ELSE CASE
                            WHEN kcp.druhobchodu = 'nakup' THEN 'Nákup'
                            WHEN kcp.druhobchodu = 'predaj' THEN 'Predaj'
                            WHEN kcp.druhobchodu = 'vklad' THEN 'Vklad'
                            WHEN kcp.druhobchodu = 'vyber' THEN 'Výber'
                            WHEN kcp.druhobchodu = 'vklad-pr' THEN 'Prevod z port. c.' || f.cislozmluvy
                            WHEN kcp.druhobchodu = 'vyber-pr' THEN 'Prevod na port. c.' || f.cislozmluvy
                            WHEN kcp.druhobchodu = 'vklad-vy' THEN 'Výmena CP - pripísanie CP'
                            WHEN kcp.druhobchodu = 'vyber-vy' THEN 'Výmena CP - odpísanie CP'
                            ELSE NULL
                        END
                    END AS popis,
                    CASE
                        WHEN kcp.dealid IN (46663904, 47124904) THEN '--'
                        ELSE tp.typ_pokynu
                    END AS typPokynu,
                    CASE
                        WHEN kcp.druhobchodu IN ('nakup', 'vklad', 'vklad-pr', 'vklad-vy') THEN 0
                        WHEN kcp.druhobchodu IN ('predaj', 'vyber', 'vyber-pr', 'vyber-vy') THEN 1
                        ELSE NULL
                    END AS MD_D
                FROM
                    konfirmaciacp kcp
                    JOIN partner p ON kcp.partnerid = p.partnerid
                    LEFT JOIN portfolio f ON f.fondid = (
                        SELECT
                            k2.subjektid
                        FROM
                            konfirmaciacp k2
                        WHERE
                            k2.dealid = kcp.dealid + CASE
                                WHEN kcp.druhobchodu = 'vklad-pr' THEN -1000
                                ELSE 1000
                            END
                    )
                    LEFT JOIN typy_pokynov tp ON tp.typid = kcp.typ_pokynu
                WHERE
                    kcp.subjektid = 7925
            )
            SELECT
                pc.popis,
                rcp.datvysporiadaniamureal,
                e.isin,
                e.isinreal,
                e.eqid,
                edruh.poddruheq,
                e.cpnaz,
                e.cpnazskratka,
                rcp.kusovreal AS pocet,
                kcp.currencyidtrade,
                rcp.kurzreal AS kurz,
                rcp.auvreal AS auv,
                rcp.transsuma AS spolu,
                rcp.transsuma - COALESCE(rcp.auvreal, 0) as bezAUV,
                maj.obratdatatimezauctovane AS datum,
                currencyidtrade AS mena,
                pc.miesto,
				'' as datzns,
                pc.typPokynu,
                pc.MD_D
            FROM
                pre_calc pc
                JOIN konfirmaciacp kcp ON pc.dealid = kcp.dealid
                JOIN rekonfirmaciacp rcp ON pc.dealid = rcp.dealid
                JOIN rekonfirmaciacpobratid rcpo ON rcpo.dealid = rcp.dealid
                AND rcpo.tranza = rcp.tranza
                JOIN dbequity e ON e.isin = kcp.isin
                JOIN equityid eid ON e.eqid = eid.eqid
                JOIN equitydruh edruh ON edruh.druheqid = e.druheqid
                JOIN majetok maj ON maj.obratid = rcpo.obratid
                LEFT JOIN floatkupon f ON maj.kodaktiva = f.isincurrric
                AND maj.obratdatatimezauctovane <= f.datetill
                AND maj.obratdatatimezauctovane > f.datefrom
        )
        union
        all (
            WITH majetok_combined AS (
                SELECT
                    obratid,
                    obratdatatimezauctovane,
                    subjektid,
                    kodaktiva
                FROM
                    majetoktoday
                WHERE
                    obratid > 0
                    AND uctovnykod IN (251110, 251200, 251300)
                    AND obratdatatimezauctovane BETWEEN to_date('$fromdate', 'YYYY-MM-DD')
                    AND to_date('$todate', 'YYYY-MM-DD')
                    AND destinacia NOT LIKE 'muprevod'
                UNION
                ALL
                SELECT
                    obratid,
                    obratdatatimezauctovane,
                    subjektid,
                    kodaktiva
                FROM
                    majetokarchiv
                WHERE
                    obratid > 0
                    AND uctovnykod IN (251110, 251200, 251300)
                    AND obratdatatimezauctovane BETWEEN to_date('$fromdate', 'YYYY-MM-DD')
                    AND to_date('$todate', 'YYYY-MM-DD')
                    AND destinacia NOT LIKE 'muprevod'
            )
            SELECT
                CASE
                    WHEN kcp.dealid = 46663904 THEN 'Predčasné splatenie dlhopisu na výzvu emitenta'
                    ELSE CASE
                        kcp.druhobchodu
                        WHEN 'nakup' THEN 'Nákup'
                        WHEN 'predaj' THEN 'Predaj'
                        WHEN 'vklad' THEN 'Vklad'
                        WHEN 'vyber' THEN 'Výber'
                        WHEN 'vklad-vy' THEN 'Výmena CP - pripísanie CP'
                        WHEN 'vyber-vy' THEN 'Výmena CP - odpísanie CP'
                        ELSE NULL
                    END
                END AS popis,
                rcp.datvysporiadaniamureal,
                e.isin,
                e.isinreal,
                e.eqid,
                edruh.poddruheq AS poddruheq,
                e.cpnaz,
                e.cpnazskratka,
                pdr.ksreal AS pocet,
                kcp.currencyidtrade,
                rcp.kurzreal AS kurz,
                pdr.auvreal AS auv,
                pdr.transsumareal AS spolu,
                ROUND(
                    rcp.kurzreal * pdr.ksreal * COALESCE(e.nominalemisie * COALESCE(f.faktor, 1), 1) * CASE
                        WHEN kcp.eqid = 'Bonds' THEN COALESCE(f.faktor, 1) / 100
                        ELSE 1
                    END,
                    2
                ) AS bezAUV,
                maj.obratdatatimezauctovane AS datum,
                currencyidtrade AS mena,
                CASE
                    WHEN kcp.dealid = 46663904 THEN '--'
                    ELSE p.nazovpartnera
                END AS miesto,
                kcp.datum_cas_obchodu AS datum_cas,
                CASE
                    WHEN kcp.dealid = 46663904 THEN '--'
                    ELSE tp.typ_pokynu
                END AS typPokynu,
                CASE
                    WHEN kcp.druhobchodu IN ('nakup', 'vklad', 'vklad-vy') THEN 0
                    WHEN kcp.druhobchodu IN ('predaj', 'vyber', 'vyber-vy') THEN 1
                    ELSE NULL
                END AS MD_D
            FROM
                konfirmaciacp kcp
                JOIN rekonfirmaciacp rcp ON kcp.dealid = rcp.dealid
                JOIN rekonfirmaciacpobratid rcpo ON rcpo.dealid = rcp.dealid
                AND rcpo.tranza = rcp.tranza
                JOIN dbequity e ON e.isin = kcp.isin
                JOIN equityid eid ON e.eqid = eid.eqid
                JOIN equitydruh edruh ON edruh.druheqid = e.druheqid
                LEFT JOIN pool po ON kcp.dealid = po.dealid
                LEFT JOIN pooldetailreal pdr ON pdr.poolid = po.poolid
                AND rcp.tranza = pdr.tranza
                LEFT JOIN majetok_combined maj ON maj.obratid = rcpo.obratid
                AND maj.subjektid = pdr.subjektid
                LEFT JOIN floatkupon f ON maj.kodaktiva = f.isincurrric
                AND maj.obratdatatimezauctovane <= f.datetill
                AND maj.obratdatatimezauctovane > f.datefrom
                LEFT JOIN partner p ON kcp.partnerid = p.partnerid
                LEFT JOIN typy_pokynov tp ON tp.typid = kcp.typ_pokynu
            WHERE
                kcp.subjektid = 0
                AND pdr.subjektid = 7925
                AND maj.obratdatatimezauctovane IS NOT NULL
        )
        union
        all
        select
            'Splatenie' as popis,
            s.datumvyplaty as datvysporiadaniamureal,
            e.isin,
            e.isinreal,
            e.eqid,
            edruh.poddruheq as poddruheq,
            e.cpnaz,
            e.cpnazskratka,
            s.pocet,
            s.mena as currencyidtrade,
            s.suma * 100 /(e.nominalemisie * s.pocet) as kurz,
            0 as auv,
            s.suma as spolu,
            s.suma as bezAUV,
            s.datum as datum,
            s.mena as mena,
            null as miesto,
            'Aktivita emitenta' as datum_cas,
            null as typPokynu,
            1 as MD_D
        from
            splatenie s,
            dbequity e,
            dbequitycurr c,
            dbequitycurrric r,
            equitydruh edruh
        where
            s.kodaktiva = r.isincurrric
            and r.isincurr = c.isincurr
            and c.isin = e.isin
            and e.druheqid = edruh.druheqid
            and s.uctovnykod = 251110
            and s.datum >= to_date('$fromdate', 'YYYY-MM-DD')
            and s.datum <= to_date('$todate', 'YYYY-MM-DD')
            and s.$fondid_add
            and s.datum_naroku = (
                select
                    max(datesplatnost)
                from
                    floatkupon
                where
                    s.kodaktiva = isincurrric
                    and istina >= 100
            )
        union
        all
        select
            'Čiastočné splatenie priebežné' as popis,
            s.datumvyplaty as datvysporiadaniamureal,
            e.isin,
            e.isinreal,
            e.eqid,
            edruh.poddruheq as poddruheq,
            e.cpnaz,
            e.cpnazskratka,
            s.pocet,
            s.mena as currencyidtrade,
            100 as kurz,
            0 as auv,
            s.suma as spolu,
            s.suma as bezAUV,
            s.datum as datum,
            s.mena as mena,
            null as miesto,
            'Aktivita emitenta' as datum_cas,
            null as typPokynu,
            1 as MD_D
        from
            splatenie s,
            dbequity e,
            dbequitycurr c,
            dbequitycurrric r,
            equitydruh edruh
        where
            s.kodaktiva = r.isincurrric
            and r.isincurr = c.isincurr
            and c.isin = e.isin
            and e.druheqid = edruh.druheqid
            and s.uctovnykod = 251110
            and s.datum >= to_date('$fromdate', 'YYYY-MM-DD')
            and s.datum <= to_date('$todate', 'YYYY-MM-DD')
            and s.$fondid_add
            and s.datum_naroku < (
                select
                    max(datesplatnost)
                from
                    floatkupon
                where
                    s.kodaktiva = isincurrric
                    and istina < 100
            )
        union
        all
        select
            'Čiastočné splatenie konečné' as popis,
            s.datumvyplaty as datvysporiadaniamureal,
            e.isin,
            e.isinreal,
            e.eqid,
            edruh.poddruheq as poddruheq,
            e.cpnaz,
            e.cpnazskratka,
            s.pocet,
            s.mena as currencyidtrade,
            100 as kurz,
            0 as auv,
            s.suma as spolu,
            s.suma as bezAUV,
            s.datum as datum,
            s.mena as mena,
            null as miesto,
            'Aktivita emitenta' as datum_cas,
            null as typPokynu,
            1 as MD_D
        from
            splatenie s,
            dbequity e,
            dbequitycurr c,
            dbequitycurrric r,
            equitydruh edruh
        where
            s.kodaktiva = r.isincurrric
            and r.isincurr = c.isincurr
            and c.isin = e.isin
            and e.druheqid = edruh.druheqid
            and s.uctovnykod = 251110
            and s.datum >= to_date('$fromdate', 'YYYY-MM-DD')
            and s.datum <= to_date('$todate', 'YYYY-MM-DD')
            and s.$fondid_add
            and s.datum_naroku = (
                select
                    max(datesplatnost)
                from
                    floatkupon
                where
                    s.kodaktiva = isincurrric
                    and istina < 100
            )
        union
        all
        select
            da.nazov as popis,
            s.datumvyplaty as datvysporiadaniamureal,
            e.isin,
            e.isinreal,
            e.eqid,
            edruh.poddruheq as poddruheq,
            e.cpnaz,
            e.cpnazskratka,
            s.pocet,
            s.mena as currencyidtrade,
            null as kurz,
            null as auv,
            null as spolu,
            s.pocet as bezAUV,
            s.datumvyplaty as datum,
            s.mena as mena,
            null as miesto,
            'Aktivita emitenta' as datum_cas,
            null as typPokynu,
            da.MD_D as MD_D
        from
            splatenieakcia s,
            dividendaakciatyp da,
            dbequity e,
            dbequitycurr c,
            dbequitycurrric r,
            equitydruh edruh
        where
            s.kodaktiva = r.isincurrric
            and r.isincurr = c.isincurr
            and c.isin = e.isin
            and e.druheqid = edruh.druheqid
            and s.datumvyplaty >= to_date('$fromdate', 'YYYY-MM-DD')
            and s.datumvyplaty <= to_date('$todate', 'YYYY-MM-DD')
            and s.$fondid_add
            and s.subkodobratu in (
                select
                    subkodobratu
                from
                    dividendaakciatyp
                where
                    hotovost = 0
            )
            and da.subkodobratu = s.subkodobratu
    ) as krkr
order by
    datum";


                    $resultRes = Connection::getDataFromDatabase($query, defaultDB);
                    $result = $resultRes[1];
                    $cnt = 0;
                    $style_hr = 'style="border-bottom: 0.25mm dashed #aaaaaa"';

                    foreach ($result as $item) {
                        $cnt++;
                        ?>
                        <tr>
                            <td class="text-center py-2"><?php echo $item["datum"]; ?></td>
                            <td class="text-center py-2"><?php echo $item["popis"]; ?></td>
                            <td class="text-center py-2"><?php echo $item["cpnaz"]; ?></td>
                            <td class="text-center py-2"><?php echo $item["isinreal"]; ?></td>
                            <td class="text-center py-2">
                                <?php echo ($item['MD_D'] == 0 ? '' : '-') . round($item["pocet"], 0) ?>
                            </td>
                            <td class="text-center py-2"><?php echo $item["mena"]; ?></td>
                            <td class="text-center py-2"><?php echo ($item["kurz"] == null ? '' : round($item["kurz"], 4)) ?>
                            </td>
                            <td class="text-center py-2">
                                <?php echo $typ == 'Bonds' && $item["popis"] != 'Splatenie' ? round($item["bezAUV"], 2) : "" ?>
                            </td>
                            <td class="text-center py-2">
                                <?php echo $typ == 'Bonds' && $item["popis"] != 'Splatenie' ? round($item["auv"], 2) : "" ?>
                            </td>
                            <td class="text-center py-2"><?php echo ($item['spolu'] == null ? '' : round($suma, 2)) ?></td>
                        </tr>
                        <tr>
                            <td class="text-center py-2" <?php echo $style_hr ?>><?php echo $item["datum"] ?></td>
                            <td class="text-center py-2" <?php echo $style_hr ?>><?php echo $item["typPokynu"] ?></td>
                            <td class="text-center py-2" <?php echo $style_hr ?>><?php echo $item["miesto"] ?></td>
                            <td class="text-center py-2" colspan=3 <?php echo $style_hr ?>><?php echo $item["poddruheq"] ?>
                            </td>
                            <td <?php echo $style_hr ?> class="text-center py-2">
                                <?php echo ($item["eqid"] != 'Bonds' && $item['kurz'] != null && (EURConversion::ShowDualPrice($tms_todate, $item["mena"]))) ? EURConversion::PrintDualPriceAmount($tms_todate, $kurz, $item["mena"]) : "" ?>
                            </td>
                            <td <?php echo $style_hr ?> class="text-center py-2">
                                <?php echo ($item["eqid"] == 'Bonds' && $popis != 'Splatenie' && (EURConversion::ShowDualPrice($tms_todate, $item["mena"]))) ? EURConversion::PrintDualPriceAmount($tms_todate, $item["bezAUV"], $item["mena"]) : "" ?>
                            </td>
                            <td <?php echo $style_hr ?> class="text-center py-2">
                                <?php echo ($item["eqid"] == 'Bonds' && $popis != 'Splatenie' && (EURConversion::ShowDualPrice($tms_todate, $item["mena"]))) ? EURConversion::PrintDualPriceAmount($tms_todate, $item["auv"], $item["mena"]) : "" ?>
                            </td>
                            <td <?php echo $style_hr ?> class="text-center py-2">
                                <?php echo ($item['spolu'] != null && (EURConversion::ShowDualPrice($tms_todate, $item["mena"]))) ? EURConversion::PrintDualPriceAmount($tms_todate, $item["spolu"], $item["mena"]) : "" ?>
                            </td>
                        </tr>
                        <?php
                    }
                    if ($cnt == 0) {
                        ?>
                        <tr>
                            <td colspan="10" class="py-6 px-10 font-bold bg-red-300"><span class="px-6">Pre zvolené obdobie
                                    neprebehli žiadne transakcie</span>
                            </td>
                        </tr>
                        <?php
                    }
                    ?>
                </tbody>
            </table>
            <table>
                <tr>
                    <td class="text-2xl font-bold flex text-red-500 items-center gap-4 mb-5 mt-4">
                        <?php echo $sumaAlertBottom ?>
                    </td>
                </tr>
            </table>
</div>
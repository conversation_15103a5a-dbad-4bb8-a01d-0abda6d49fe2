select
    *
from
    (
        (
            WITH majetok AS (
                SELECT
                    obratid,
                    obratdatatimezauctovane,
                    kodaktiva
                FROM
                    majetoktoday
                WHERE
                    obratid > 0
                    AND uctovnykod IN (251110, 251200, 251300)
                    AND obratdatatimezauctovane BETWEEN to_date('2023-01-01', 'YYYY-MM-DD')
                    AND to_date('2023-07-05', 'YYYY-MM-DD')
                    AND destinacia NOT LIKE 'muprevod'
                UNION
                ALL
                SELECT
                    obratid,
                    obratdatatimezauctovane,
                    kodaktiva
                FROM
                    majetokarchiv
                WHERE
                    obratid > 0
                    AND uctovnykod IN (251110, 251200, 251300)
                    AND obratdatatimezauctovane BETWEEN to_date('2023-01-01', 'YYYY-MM-DD')
                    AND to_date('2023-07-05', 'YYYY-MM-DD')
                    AND destinacia NOT LIKE 'muprevod'
            ),
            pre_calc AS (
                SELECT
                    kcp.dealid,
                    CASE
                        WHEN kcp.dealid IN (46663904, 47124904) THEN '--'
                        ELSE p.nazovpartnera
                    END AS miesto,
                    CASE
                        WHEN kcp.dealid IN (46663904, 47124904) THEN 'Predčasné splatenie dlhopisu na výzvu emitenta'
                        ELSE CASE
                            WHEN kcp.druhobchodu = 'nakup' THEN 'Nákup'
                            WHEN kcp.druhobchodu = 'predaj' THEN 'Predaj'
                            WHEN kcp.druhobchodu = 'vklad' THEN 'Vklad'
                            WHEN kcp.druhobchodu = 'vyber' THEN 'Výber'
                            WHEN kcp.druhobchodu = 'vklad-pr' THEN 'Prevod z port. c.' || f.cislozmluvy
                            WHEN kcp.druhobchodu = 'vyber-pr' THEN 'Prevod na port. c.' || f.cislozmluvy
                            WHEN kcp.druhobchodu = 'vklad-vy' THEN 'Výmena CP - pripísanie CP'
                            WHEN kcp.druhobchodu = 'vyber-vy' THEN 'Výmena CP - odpísanie CP'
                            ELSE NULL
                        END
                    END AS popis,
                    CASE
                        WHEN kcp.dealid IN (46663904, 47124904) THEN '--'
                        ELSE tp.typ_pokynu
                    END AS typPokynu,
                    CASE
                        WHEN kcp.druhobchodu IN ('nakup', 'vklad', 'vklad-pr', 'vklad-vy') THEN 0
                        WHEN kcp.druhobchodu IN ('predaj', 'vyber', 'vyber-pr', 'vyber-vy') THEN 1
                        ELSE NULL
                    END AS MD_D
                FROM
                    konfirmaciacp kcp
                    JOIN partner p ON kcp.partnerid = p.partnerid
                    LEFT JOIN portfolio f ON f.fondid = (
                        SELECT
                            k2.subjektid
                        FROM
                            konfirmaciacp k2
                        WHERE
                            k2.dealid = kcp.dealid + CASE
                                WHEN kcp.druhobchodu = 'vklad-pr' THEN -1000
                                ELSE 1000
                            END
                    )
                    LEFT JOIN typy_pokynov tp ON tp.typid = kcp.typ_pokynu
                WHERE
                    kcp.subjektid = 7925
            )
            SELECT
                pc.popis,
                rcp.datvysporiadaniamureal,
                e.isin,
                e.isinreal,
                e.eqid,
                edruh.poddruheq,
                e.cpnaz,
                e.cpnazskratka,
                rcp.kusovreal AS pocet,
                kcp.currencyidtrade,
                rcp.kurzreal AS kurz,
                rcp.auvreal AS auv,
                rcp.transsuma AS spolu,
                rcp.transsuma - COALESCE(rcp.auvreal, 0) as bezAUV,
                maj.obratdatatimezauctovane AS datum,
                currencyidtrade AS mena,
                pc.miesto,
                '' as datzns,
                pc.typPokynu,
                pc.MD_D
            FROM
                pre_calc pc
                JOIN konfirmaciacp kcp ON pc.dealid = kcp.dealid
                JOIN rekonfirmaciacp rcp ON pc.dealid = rcp.dealid
                JOIN rekonfirmaciacpobratid rcpo ON rcpo.dealid = rcp.dealid
                AND rcpo.tranza = rcp.tranza
                JOIN dbequity e ON e.isin = kcp.isin
                JOIN equityid eid ON e.eqid = eid.eqid
                JOIN equitydruh edruh ON edruh.druheqid = e.druheqid
                JOIN majetok maj ON maj.obratid = rcpo.obratid
                LEFT JOIN floatkupon f ON maj.kodaktiva = f.isincurrric
                AND maj.obratdatatimezauctovane <= f.datetill
                AND maj.obratdatatimezauctovane > f.datefrom
        )
        union
        all (
            WITH majetok_combined AS (
                SELECT
                    obratid,
                    obratdatatimezauctovane,
                    subjektid,
                    kodaktiva
                FROM
                    majetoktoday
                WHERE
                    obratid > 0
                    AND uctovnykod IN (251110, 251200, 251300)
                    AND obratdatatimezauctovane BETWEEN to_date('2023-01-01', 'YYYY-MM-DD')
                    AND to_date('2023-07-05', 'YYYY-MM-DD')
                    AND destinacia NOT LIKE 'muprevod'
                UNION
                ALL
                SELECT
                    obratid,
                    obratdatatimezauctovane,
                    subjektid,
                    kodaktiva
                FROM
                    majetokarchiv
                WHERE
                    obratid > 0
                    AND uctovnykod IN (251110, 251200, 251300)
                    AND obratdatatimezauctovane BETWEEN to_date('2023-01-01', 'YYYY-MM-DD')
                    AND to_date('2023-07-05', 'YYYY-MM-DD')
                    AND destinacia NOT LIKE 'muprevod'
            )
            SELECT
                CASE
                    WHEN kcp.dealid = 46663904 THEN 'Predčasné splatenie dlhopisu na výzvu emitenta'
                    ELSE CASE
                        kcp.druhobchodu
                        WHEN 'nakup' THEN 'Nákup'
                        WHEN 'predaj' THEN 'Predaj'
                        WHEN 'vklad' THEN 'Vklad'
                        WHEN 'vyber' THEN 'Výber'
                        WHEN 'vklad-vy' THEN 'Výmena CP - pripísanie CP'
                        WHEN 'vyber-vy' THEN 'Výmena CP - odpísanie CP'
                        ELSE NULL
                    END
                END AS popis,
                rcp.datvysporiadaniamureal,
                e.isin,
                e.isinreal,
                e.eqid,
                edruh.poddruheq AS poddruheq,
                e.cpnaz,
                e.cpnazskratka,
                pdr.ksreal AS pocet,
                kcp.currencyidtrade,
                rcp.kurzreal AS kurz,
                pdr.auvreal AS auv,
                pdr.transsumareal AS spolu,
                ROUND(
                    rcp.kurzreal * pdr.ksreal * COALESCE(e.nominalemisie * COALESCE(f.faktor, 1), 1) * CASE
                        WHEN kcp.eqid = 'Bonds' THEN COALESCE(f.faktor, 1) / 100
                        ELSE 1
                    END,
                    2
                ) AS bezAUV,
                maj.obratdatatimezauctovane AS datum,
                currencyidtrade AS mena,
                CASE
                    WHEN kcp.dealid = 46663904 THEN '--'
                    ELSE p.nazovpartnera
                END AS miesto,
                kcp.datum_cas_obchodu AS datum_cas,
                CASE
                    WHEN kcp.dealid = 46663904 THEN '--'
                    ELSE tp.typ_pokynu
                END AS typPokynu,
                CASE
                    WHEN kcp.druhobchodu IN ('nakup', 'vklad', 'vklad-vy') THEN 0
                    WHEN kcp.druhobchodu IN ('predaj', 'vyber', 'vyber-vy') THEN 1
                    ELSE NULL
                END AS MD_D
            FROM
                konfirmaciacp kcp
                JOIN rekonfirmaciacp rcp ON kcp.dealid = rcp.dealid
                JOIN rekonfirmaciacpobratid rcpo ON rcpo.dealid = rcp.dealid
                AND rcpo.tranza = rcp.tranza
                JOIN dbequity e ON e.isin = kcp.isin
                JOIN equityid eid ON e.eqid = eid.eqid
                JOIN equitydruh edruh ON edruh.druheqid = e.druheqid
                LEFT JOIN pool po ON kcp.dealid = po.dealid
                LEFT JOIN pooldetailreal pdr ON pdr.poolid = po.poolid
                AND rcp.tranza = pdr.tranza
                LEFT JOIN majetok_combined maj ON maj.obratid = rcpo.obratid
                AND maj.subjektid = pdr.subjektid
                LEFT JOIN floatkupon f ON maj.kodaktiva = f.isincurrric
                AND maj.obratdatatimezauctovane <= f.datetill
                AND maj.obratdatatimezauctovane > f.datefrom
                LEFT JOIN partner p ON kcp.partnerid = p.partnerid
                LEFT JOIN typy_pokynov tp ON tp.typid = kcp.typ_pokynu
            WHERE
                kcp.subjektid = 0
                AND pdr.subjektid = 7925
                AND maj.obratdatatimezauctovane IS NOT NULL
        )
        union
        all
        select
            'Splatenie' as popis,
            s.datumvyplaty as datvysporiadaniamureal,
            e.isin,
            e.isinreal,
            e.eqid,
            edruh.poddruheq as poddruheq,
            e.cpnaz,
            e.cpnazskratka,
            s.pocet,
            s.mena as currencyidtrade,
            s.suma * 100 /(e.nominalemisie * s.pocet) as kurz,
            0 as auv,
            s.suma as spolu,
            s.suma as bezAUV,
            s.datum as datum,
            s.mena as mena,
            null as miesto,
            'Aktivita emitenta' as datum_cas,
            null as typPokynu,
            1 as MD_D
        from
            splatenie s,
            dbequity e,
            dbequitycurr c,
            dbequitycurrric r,
            equitydruh edruh
        where
            s.kodaktiva = r.isincurrric
            and r.isincurr = c.isincurr
            and c.isin = e.isin
            and e.druheqid = edruh.druheqid
            and s.uctovnykod = 251110
            and s.datum >= to_date('2023-01-01', 'YYYY-MM-DD')
            and s.datum <= to_date('2023-07-05', 'YYYY-MM-DD')
            and s.subjektid in (7925)
            and s.datum_naroku = (
                select
                    max(datesplatnost)
                from
                    floatkupon
                where
                    s.kodaktiva = isincurrric
                    and istina >= 100
            )
        union
        all
        select
            'Čiastočné splatenie priebežné' as popis,
            s.datumvyplaty as datvysporiadaniamureal,
            e.isin,
            e.isinreal,
            e.eqid,
            edruh.poddruheq as poddruheq,
            e.cpnaz,
            e.cpnazskratka,
            s.pocet,
            s.mena as currencyidtrade,
            100 as kurz,
            0 as auv,
            s.suma as spolu,
            s.suma as bezAUV,
            s.datum as datum,
            s.mena as mena,
            null as miesto,
            'Aktivita emitenta' as datum_cas,
            null as typPokynu,
            1 as MD_D
        from
            splatenie s,
            dbequity e,
            dbequitycurr c,
            dbequitycurrric r,
            equitydruh edruh
        where
            s.kodaktiva = r.isincurrric
            and r.isincurr = c.isincurr
            and c.isin = e.isin
            and e.druheqid = edruh.druheqid
            and s.uctovnykod = 251110
            and s.datum >= to_date('2023-01-01', 'YYYY-MM-DD')
            and s.datum <= to_date('2023-07-05', 'YYYY-MM-DD')
            and s.subjektid in (7925)
            and s.datum_naroku < (
                select
                    max(datesplatnost)
                from
                    floatkupon
                where
                    s.kodaktiva = isincurrric
                    and istina < 100
            )
        union
        all
        select
            'Čiastočné splatenie konečné' as popis,
            s.datumvyplaty as datvysporiadaniamureal,
            e.isin,
            e.isinreal,
            e.eqid,
            edruh.poddruheq as poddruheq,
            e.cpnaz,
            e.cpnazskratka,
            s.pocet,
            s.mena as currencyidtrade,
            100 as kurz,
            0 as auv,
            s.suma as spolu,
            s.suma as bezAUV,
            s.datum as datum,
            s.mena as mena,
            null as miesto,
            'Aktivita emitenta' as datum_cas,
            null as typPokynu,
            1 as MD_D
        from
            splatenie s,
            dbequity e,
            dbequitycurr c,
            dbequitycurrric r,
            equitydruh edruh
        where
            s.kodaktiva = r.isincurrric
            and r.isincurr = c.isincurr
            and c.isin = e.isin
            and e.druheqid = edruh.druheqid
            and s.uctovnykod = 251110
            and s.datum >= to_date('2023-01-01', 'YYYY-MM-DD')
            and s.datum <= to_date('2023-07-05', 'YYYY-MM-DD')
            and s.subjektid in (7925)
            and s.datum_naroku = (
                select
                    max(datesplatnost)
                from
                    floatkupon
                where
                    s.kodaktiva = isincurrric
                    and istina < 100
            )
        union
        all
        select
            da.nazov as popis,
            s.datumvyplaty as datvysporiadaniamureal,
            e.isin,
            e.isinreal,
            e.eqid,
            edruh.poddruheq as poddruheq,
            e.cpnaz,
            e.cpnazskratka,
            s.pocet,
            s.mena as currencyidtrade,
            null as kurz,
            null as auv,
            null as spolu,
            s.pocet as bezAUV,
            s.datumvyplaty as datum,
            s.mena as mena,
            null as miesto,
            'Aktivita emitenta' as datum_cas,
            null as typPokynu,
            da.MD_D as MD_D
        from
            splatenieakcia s,
            dividendaakciatyp da,
            dbequity e,
            dbequitycurr c,
            dbequitycurrric r,
            equitydruh edruh
        where
            s.kodaktiva = r.isincurrric
            and r.isincurr = c.isincurr
            and c.isin = e.isin
            and e.druheqid = edruh.druheqid
            and s.datumvyplaty >= to_date('2023-01-01', 'YYYY-MM-DD')
            and s.datumvyplaty <= to_date('2023-07-05', 'YYYY-MM-DD')
            and s.subjektid in (7925)
            and s.subkodobratu in (
                select
                    subkodobratu
                from
                    dividendaakciatyp
                where
                    hotovost = 0
            )
            and da.subkodobratu = s.subkodobratu
    ) as krkr
order by
    datum
WITH filtered_majetoktotal AS (
    SELECT
        mt.kodaktiva,
        mt.menadenom,
        SUM(
            CASE
                WHEN mt.md_d = 0 THEN mt.pocet
                ELSE (-1) * mt.pocet
            END
        ) AS pocet,
        SUM(
            CASE
                WHEN mt.md_d = 0 THEN mt.sumadenom
                ELSE (-1) * mt.sumadenom
            END
        ) AS objem,
        MAX(mt.kurzaktiva) AS kurz,
        f_menovy_kurz_lot(mt.menadenom, 'EUR', mt.datum) AS kurz_lot,
        MAX(f_menovy_kurz(mt.menadenom, 'EUR', mt.datum, 1)) AS kurz_mena
    FROM
        majetoktotal mt
    WHERE
        mt.datum = to_date('2023-01-01', 'YYYY-MM-DD')
        AND mt.uctovnykod IN (251200)
        AND mt.subjektid = 1752
    GROUP BY
        mt.kodaktiva,
        mt.menadenom,
        mt.datum
),
pocty AS (
    SELECT
        f.kodaktiva,
        f.menadenom,
        f.pocet,
        f.kurz,
        f.objem,
        (f.objem * f.kurz_lot) AS objemref,
        f.kurz_mena
    FROM
        filtered_majetoktotal f
)
SELECT
    d.cpnaz,
    pocty.menadenom AS mena,
    d.isinreal,
    pocty.pocet,
    pocty.kurz,
    pocty.objem,
    pocty.objemref,
    pocty.kurz_mena
FROM
    pocty
    JOIN dbequitycurrric r ON pocty.kodaktiva = r.isincurrric
    JOIN dbequitycurr c ON r.isincurr = c.isincurr
    JOIN dbequity d ON c.isin = d.isin
WHERE
    d.druheqid NOT IN (8, 15, 17)
ORDER BY
    d.isinreal;
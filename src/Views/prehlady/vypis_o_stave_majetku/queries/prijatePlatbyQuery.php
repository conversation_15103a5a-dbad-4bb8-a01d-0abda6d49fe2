<?php
$prijatePlatbyQuery = "
		select			
			1 as typ,
			COALESCE(sum(d.danzaklad*(SELECT kurz FROM f_menovy_kurz_lot(s.mena,'$refmena',s.datumvyplaty))),0) as suma
		FROM
			splatenie s,
			danvynosy d
		where
			s.subjektid = $fondid and 
		  	s.datumvyplaty >= to_date('$fromdate','YYYY-MM-DD') and
			s.datumvyplaty <= to_date('$todate','YYYY-MM-DD') and
			s.uctovnykod = (251120) and
			s.fiktivne = 0 and
			d.dealid = s.dealid and
			d.destinacia='splatenie'
		 		
		union all
		
		select 
		  	2 as typ,  
			COALESCE(sum(d.danzaklad*(SELECT kurz FROM f_menovy_kurz_lot(sa.mena,'$refmena',sa.datumvyplaty))),0)  as suma 
		FROM
			splatenieakcia sa,
			danvynosy d
		where
			sa.subjektid = $fondid and
			sa.datumvyplaty >= to_date('$fromdate','YYYY-MM-DD') and
			sa.datumvyplaty <= to_date('$todate','YYYY-MM-DD') and
			sa.subkodobratu in (select subkodobratu FROM dividendaakciatyp where MD_D = 0 and hotovost = 1) and
			sa.fiktivne = 0 and
			d.dealid = sa.dealid and
			d.destinacia = 'splatenieakcia'
		
		union all
			
		select
		  	3 as typ,
		  	COALESCE(sum(round(iv_b*(SELECT kurz FROM f_menovy_kurz_lot(k.mena,'$refmena',m.OBRATDATATIMEZAUCTOVANE)),2)),0) as suma
		FROM
			konfirmaciaktv k,
			ktvexportobratid ke,
			majetokarchiv m
		where
			k.subjektid = $fondid and
			k.logactivityid in (23,24,25) and
			ke.dealid = k.dealid and
			ke.status = 2 and
			m.obratid = ke.obratid and
			m.OBRATDATATIMEZAUCTOVANE between to_date('$fromdate','YYYY-MM-DD') and to_date('$todate','YYYY-MM-DD') and
			m.uctovnykod = 315113 and
			m.kodobratu = 115	
			
		union all
		
		select
			3 as typ,
			sum(round(COALESCE(pr.auvreal*(SELECT kurz FROM f_menovy_kurz_lot(k.mena,'$refmena',m.OBRATDATATIMEZAUCTOVANE)),0),2)) as suma
		FROM
			konfirmaciaktv k,
			ktvexportobratid ke,
			majetokarchiv m,
			pool p,
			pooldetailreal pr
		where
			k.subjektid = 0 and
			k.logactivityid in (23,24,25) and
			ke.dealid = k.dealid and
			ke.status = 2 and
			m.obratid = ke.obratid and
			m.OBRATDATATIMEZAUCTOVANE between to_date('$fromdate','YYYY-MM-DD') and to_date('$todate','YYYY-MM-DD') and
			m.uctovnykod = 315113 and
			m.kodobratu = 115	and
			p.dealid = k.dealid and
			pr.poolid = p.poolid and
			pr.subjektid = $fondid and
			m.subjektid = pr.subjektid

		union all		

		select
			4 as typ,
			COALESCE(sum(pocet*(SELECT kurz FROM f_menovy_kurz_lot(m.mena,'$refmena',m.OBRATDATATIMEZAUCTOVANE))),0) as suma 
		FROM
			majetokarchiv m
		where
		 	m.OBRATDATATIMEZAUCTOVANE between to_date('$fromdate','YYYY-MM-DD') and to_date('$todate','YYYY-MM-DD') and
		 	m.subjektid = $fondid and
		 	m.uctovnykod = 668000 and
		 	m.md_d = 1 and
		  	not exists
		  	(
		  		select 
					1
		  		FROM
		  			konfirmaciapp k,
		  			obratybu o,
		  			obratybuobratid oo
		  		where
		  			k.subjektid = $fondid and
		  			k.logactivityid = 12 and
		  			o.subjektid = k.subjektid and
		  			o.ss = to_char(k.dealid, '*') and
		  			o.suma = k.suma and
		  			o.mena = k.mena and
		  			o.cub = k.ucet and
		  			oo.id = o.id and
		  			oo.obratid = m.obratid
                  		
				union all
                  		
				select 
					1
		  		FROM
					majetokarchiv ma
				where
					ma.OBRATDATATIMEZAUCTOVANE = m.OBRATDATATIMEZAUCTOVANE and
					ma.subjektid = $fondid and
					ma.uctovnykod = 668000 and
					ma.md_d = 0 and
					ma.KODOBRATU = 214 
		 	)
	";
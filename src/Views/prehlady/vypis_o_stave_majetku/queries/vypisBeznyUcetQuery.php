<?php
$fondid_add = "subjektid in ($fondid)";
$queryTransakcie = "
select t.*
from (select -1                                             as poradie,
             'Počiatočn<PERSON> stav účtu'                         as popis,
             menadenom                                      as mena1,
             coalesce(sum(((0.5 - md_d) / 0.5) * pocet), 0) as transsuma,
             '$fromdate'::date                             as datum,
             ''                                             as poznamka,
             0                                              as md_d
      from majetoktotal_partition mt
      where $fondid_add
        and datum = '$fromdate'::date
        and eqid = 'BU'
        and uctovnykod = 221110
      group by menadenom, datum, subjektid
      union all
      /*SELECT 0                                                                       AS poradie,
             'Automatická konverzia SKK na EUR - konverzný kurz 1 EUR = 30,1260 SKK' AS popis,
             mena                                                                    AS mena1,
             SUM(CASE WHEN md_d = 0 THEN 1 ELSE -1 END * pocet)                      AS transsuma,
             MAX(e.conversion_date)                                                  AS datum,
             ''                                                                      AS poznamka,
             0                                                                       AS md_d
      FROM majetokarchiv mt
               JOIN eurconversionsettings e ON DATE_TRUNC('day', mt.obratdatatimezauctovane) = e.conversion_date
      WHERE $fondid_add
        AND eqid = 'BU'
        AND uctovnykod = 221110
        AND destinacia = 'nauctovanieEUR'
        AND e.conversion_date = '$fromdate'::date
      GROUP BY mena, subjektid
      union all*/
      -- HOTOVOOOO --
      SELECT 100000              AS poradie,
             'Konečný stav účtu' AS popis,
             menadenom           AS mena1,
             (CASE
                  WHEN datum_trunc_day = datum THEN SUM(CASE WHEN md_d = 0 THEN 1 ELSE -1 END * pocet)
                  ELSE 0 END)    AS transsuma,
             '$todate'::date  AS datum,
             ''                  AS poznamka,
             0                   AS md_d
      FROM majetoktotal_partition mt
      WHERE $fondid_add
        AND datum_trunc_day = (SELECT MAX(datum_trunc_day)
                               FROM majetoktotal_partition
                               WHERE datum_trunc_day = '$todate' ::date)
        AND eqid = 'BU'
        AND uctovnykod = 221110
      GROUP BY menadenom, subjektid, datum_trunc_day, mt.datum
      /*UNION ALL
      SELECT 99999                                                                   AS poradie,
             'Automatická konverzia SKK na EUR - konverzný kurz 1 EUR = 30,1260 SKK' AS popis,
             mena                                                                    AS mena1,
             SUM(CASE WHEN md_d = 0 THEN 1 ELSE -1 END * pocet)                      AS transsuma,
             MAX(e.conversion_date)                                                  AS datum,
             ''                                                                      AS poznamka,
             0                                                                       AS md_d
      FROM majetokarchiv mt
               JOIN eurconversionsettings e ON DATE_TRUNC('day', mt.obratdatatimezauctovane) = e.conversion_date
      WHERE $fondid_add
        AND eqid = 'BU'
        AND uctovnykod = 221110
        AND destinacia = 'oductovanieSKK'
        AND e.conversion_date = '$fromdate'::date
      GROUP BY mena, subjektid*/
      union all
      select 120              as poradie,
             'Vklad klienta'  as popis,
             ob.mena          as mena1,
             ob.suma          as transsuma,
             ob.obratdatetime as datum,
             ''               as poznamka,
             ma.md_d
      from obratybu ob,
           obratybuobratid obo,
           majetokarchiv ma
      where ob.id = obo.id
        and ma.obratid = obo.obratid
        and ma.$fondid_add
        and ma.kodobratu = 201
        and ma.uctovnykod = 325300
        and ob.logactivityid = 15
        and ob.obratdatetime >= to_date('$fromdate', 'YYYY-MM-DD')
        and ob.obratdatetime <= to_date('$todate', 'YYYY-MM-DD')
        and ob.krdb = 1
      union all
      select 420                                          as poradie,
             'Výber klienta'                              as popis,
             u.mena                                       as mena1,
             coalesce(sum(((0.5 - 1) / 0.5) * u.suma), 0) as transsuma,
             u.datesplatnost                              as datum,
             ''                                           as poznamka,
             ma.md_d
      from uhrada u,
           uhradaobratid ui,
           majetokarchiv ma
      where u.kodobratu = 303
        and u.id = ui.id
        and ma.obratid = ui.obratid
        and ma.$fondid_add
        and ma.kodobratu = 303
        and ma.uctovnykod = 261930
        and logactivityid = 15
        and datesplatnost >= to_date('$fromdate', 'YYYY-MM-DD')
        and datesplatnost <= to_date('$todate', 'YYYY-MM-DD')
      group by ma.md_d, u.datesplatnost, u.mena
      union all
      SELECT 220                                                        as poradie,
             'Nákup akcie'                                              as popis,
             u.mena                                                     as mena1,
             COALESCE((SELECT pd.transsumareal
                       FROM pool p,
                            pooldetailreal pd
                       WHERE p.dealid = r.dealid
                         AND pd.poolid = p.poolid
                         AND pd.subjektid = ma.subjektid), r.transsuma) as transsuma,
             ma.obratdatatimezauctovane                                 as datum,
             (SELECT d.cpnaz || '; ISIN ' || d.isinreal
              FROM konfirmaciacp k,
                   dbequity d
              WHERE k.dealid = r.dealid
                AND d.isin = k.isin)                                    as poznamka,
             ma.md_d
      FROM uhrada u
               JOIN rekonfirmaciacp r ON u.dealid = r.dealid AND u.tranza = r.tranza
               JOIN uhradaobratid ui ON u.id = ui.id
               JOIN majetokarchiv ma ON ma.obratid = ui.obratid
      WHERE u.kodobratu = 331
        AND ma.kodobratu = 331
        AND ma.uctovnykod = 261911
        AND ma.$fondid_add
        AND u.logactivityid = 15
        AND ma.obratdatatimezauctovane >= '$fromdate' ::date
        AND ma.obratdatatimezauctovane <= '$todate' ::date
      UNION ALL
      SELECT 70                                                         AS poradie,
             'Platba za predaj akcie'                                   AS popis,
             ma.jednotka                                                AS mena1,
             COALESCE((SELECT pd.transsumareal
                       FROM pool p
                                JOIN pooldetailreal pd ON p.poolid = pd.poolid
                       WHERE p.dealid = r.dealid
                         AND pd.subjektid = ma.subjektid), r.transsuma) AS transsuma,
             ma.obratdatatimezauctovane                                 AS datum, --r.datvysporiadaniabureal AS datum_vysporiadania,
             (SELECT d.cpnaz || '; ISIN ' || d.isinreal
              FROM konfirmaciacp k
                       JOIN dbequity d ON k.isin = d.isin
              WHERE k.dealid = r.dealid)                                AS poznamka,
             0
      FROM rekonfirmaciacpobratid ro
               JOIN rekonfirmaciacp r ON r.dealid = ro.dealid AND r.tranza = ro.tranza
               JOIN konfirmaciacp kcp ON r.dealid = kcp.dealid
               JOIN majetokarchiv ma ON ro.obratid = ma.obratid
      WHERE ma.$fondid_add
        AND ma.kodobratu = 231
        AND ma.uctovnykod = 261710
        AND r.datvysporiadaniabureal >= '$fromdate'::date
        AND r.datvysporiadaniabureal <= '$todate'::date
      UNION ALL
      SELECT 210                                                        AS poradie,
             'Nákup dlhopisu'                                           AS popis,
             u.mena                                                     AS mena1,
             COALESCE((SELECT transsumareal
                       FROM pooldetailreal pd
                                JOIN pool p ON pd.poolid = p.poolid
                       WHERE p.dealid = r.dealid
                         AND pd.subjektid = ma.subjektid), r.transsuma) AS transsuma,
             ma.obratdatatimezauctovane                                 AS datum,
             (SELECT d.cpnaz || '; ISIN ' || d.isinreal
              FROM konfirmaciacp k
                       JOIN dbequity d ON k.isin = d.isin
              WHERE k.dealid = r.dealid)                                AS poznamka,
             ma.md_d
      FROM uhrada u
               JOIN rekonfirmaciacp r ON u.dealid = r.dealid AND u.tranza = r.tranza
               JOIN uhradaobratid ui ON u.id = ui.id
               JOIN majetokarchiv ma ON ma.obratid = ui.obratid
               LEFT JOIN konfirmaciacp kcp ON r.dealid = kcp.dealid
      WHERE u.kodobratu = 302
        AND ma.$fondid_add
        AND ma.kodobratu = 302
        AND ma.uctovnykod = 261920
        AND u.logactivityid = 15
        AND ma.obratdatatimezauctovane >= '$fromdate' ::date
        AND ma.obratdatatimezauctovane <= '$todate' ::date
      union all
      SELECT 60                                                         AS poradie,
             CASE
                 WHEN r.dealid IN (46663904, 47124904) THEN 'Predčasné splatenie dlhopisu na výzvu emitenta'
                 ELSE 'Platba za predaj dlhopisu' END                   AS popis,
             ma.jednotka                                                AS mena1,
             COALESCE((SELECT pd.transsumareal
                       FROM pool p
                                JOIN pooldetailreal pd ON p.poolid = pd.poolid
                       WHERE p.dealid = r.dealid
                         AND pd.subjektid = ma.subjektid), r.transsuma) AS transsuma,
             ma.obratdatatimezauctovane                                 AS datum, --r.datvysporiadaniabureal AS datum_vysporiadania,
             (SELECT d.cpnaz || '; ISIN ' || d.isinreal
              FROM konfirmaciacp k
                       JOIN dbequity d ON k.isin = d.isin
              WHERE k.dealid = r.dealid)                                AS poznamka,
             ma.md_d
      FROM rekonfirmaciacpobratid ro
               JOIN rekonfirmaciacp r ON r.dealid = ro.dealid AND r.tranza = ro.tranza
               JOIN konfirmaciacp kcp ON r.dealid = kcp.dealid
               JOIN majetokarchiv ma ON ro.obratid = ma.obratid
      WHERE ma.$fondid_add
        AND ma.kodobratu = 134
        AND ma.uctovnykod = 261210
        AND r.datvysporiadaniabureal >= '$fromdate'::date
        AND r.datvysporiadaniabureal <= '$todate'::date
      UNION ALL
      SELECT 230                                                                   AS poradie,
             'Nákup fondu'                                                         AS popis,
             u.mena                                                                AS mena1,
             COALESCE((SELECT pd.transsumareal
                       FROM pool p
                                JOIN pooldetailreal pd ON p.poolid = pd.poolid
                       WHERE p.dealid = r.dealid
                         AND pd.subjektid = ma.subjektid), r.transsuma)            AS transsuma,
             r.dattransakcie                                                       AS datum,
             (SELECT d.cpnaz || '; ISIN ' || d.isinreal
              FROM konfirmaciacp k
                       JOIN dbequity d ON k.dealid = r.dealid AND d.isin = k.isin) AS poznamka,
             ma.md_d
      FROM uhrada u
               JOIN rekonfirmaciacp r ON u.dealid = r.dealid AND u.tranza = r.tranza
               JOIN uhradaobratid ui ON u.id = ui.id
               JOIN majetokarchiv ma ON ui.obratid = ma.obratid
      WHERE u.kodobratu = 332
        AND ma.$fondid_add
        AND ma.kodobratu = 332
        AND ma.uctovnykod = 261912
        AND u.logactivityid = 15
        AND r.dattransakcie >= '$fromdate'::date
        AND r.dattransakcie <= '$todate'::date
      union all
      SELECT 80                                                                    AS poradie,
             'Platba za predaj fondu'                                              AS popis,
             ma.jednotka                                                           AS mena1,
             COALESCE((SELECT pd.transsumareal
                       FROM pool p
                                JOIN pooldetailreal pd ON p.poolid = pd.poolid
                       WHERE p.dealid = r.dealid
                         AND pd.subjektid = ma.subjektid), r.transsuma)            AS transsuma,
             ma.obratdatatimezauctovane                                            AS datum,
             (SELECT d.cpnaz || '; ISIN ' || d.isinreal
              FROM konfirmaciacp k
                       JOIN dbequity d ON k.dealid = r.dealid AND d.isin = k.isin) AS poznamka,
             ma.md_d
      FROM rekonfirmaciacpobratid ro
               JOIN rekonfirmaciacp r ON r.dealid = ro.dealid AND r.tranza = ro.tranza
               JOIN majetokarchiv ma ON ro.obratid = ma.obratid
      WHERE ma.$fondid_add
        AND ma.kodobratu = 233
        AND ma.uctovnykod = 325300
        AND ma.obratdatatimezauctovane >= '$fromdate'::date
        AND ma.obratdatatimezauctovane <= '$todate'::date
      union all
      SELECT 430                                                     AS poradie,
             'Zriadenie termínovaného vkladu'                        AS popis,
             u.mena                                                  AS mena1,
             COALESCE((SELECT coalesce(transsumareal_pov, transsumareal)
                       FROM pooldetailreal pd
                                JOIN pool p ON pd.poolid = p.poolid
                       WHERE p.dealid = r.dealid
                         AND pd.subjektid = ma.subjektid), r.sum_td) AS transsuma,
             ma.obratdatatimezauctovane                              AS datum,
             ''                                                      AS poznamka,
             ma.md_d
      FROM uhrada u
               JOIN konfirmaciaktv r ON u.dealid = r.dealid
               JOIN uhradaobratid ui ON u.id = ui.id
               JOIN majetokarchiv ma ON ma.obratid = ui.obratid
      WHERE u.kodobratu = 301
        AND ma.$fondid_add
        AND ma.kodobratu = 301
        AND ma.uctovnykod = 2619
        AND u.logactivityid = 15
        AND ma.obratdatatimezauctovane >= '$fromdate' ::date
        AND ma.obratdatatimezauctovane <= '$todate' ::date
        AND u.dealid = r.dealid
      union all
      select 10                               as poradie,
             'Splatenie termínovaného vkladu' as popis,
             ob.mena                          as mena1,
             ma.pocet                         as transsuma,
             ma.obratdatatimezauctovane       as datum,
             ''                               as poznamka,
             ma.md_d
      from obratybu ob,
           obratybuobratid obo,
           majetokarchiv ma
      where ob.id = obo.id
        and ma.obratid = obo.obratid
        and ma.$fondid_add
        and ma.kodobratu = 203
        and ma.uctovnykod = 325300
        and ob.logactivityid = 15
        and ob.obratdatetime >= to_date('$fromdate', 'YYYY-MM-DD')
        and ob.obratdatetime <= to_date('$todate', 'YYYY-MM-DD')
        and ob.krdb = 1
      union all
      SELECT 20                                                                                           AS poradie,
             'Splatenie úroku z termínovaného vkladu'                                                     AS popis,
             ob.mena                                                                                      AS mena1,
             ma.pocet                                                                                     AS transsuma,
             ma.obratdatatimezauctovane                                                                   AS datum,
             'Úrok brutto = ' || replace(replace(to_char(k.iv_b, '9,999,999,990.90'), ',', ' '), '.', ',') ||
             '; Daň = ' || replace(replace(to_char(k.suma_dane, '9,999,999,990.90'), ',', ' '), '.', ',') AS poznamka,
             ma.md_d
      FROM obratybu ob
               JOIN obratybuobratid obo ON ob.id = obo.id
               JOIN majetokarchiv ma ON obo.obratid = ma.obratid
               JOIN konfirmaciaktv k ON k.dealid::text = ob.vs AND k.$fondid_add
      WHERE ma.$fondid_add
        AND ma.kodobratu = 204
        AND ma.uctovnykod = 325300
        AND ob.logactivityid = 15
        AND ma.obratdatatimezauctovane >= '$fromdate'::date
        AND ma.obratdatatimezauctovane <= '$todate'::date
        AND ob.krdb = 1
      union all
      select 20                                                                                       as poradie,
             'Splatenie úroku z termínovaného vkladu'                                                 as popis,
             ob.mena                                                                                  as mena1,
             ma.pocet                                                                                 as transsuma,
             ma.obratdatatimezauctovane                                                               as datum,
             'rok brutto = ' || replace(replace(to_char(pdr.auvreal, '9,999,999,990.90'), ',', ' '), '.', ',') ||
             '; Daň = ' || replace(replace(to_char(pdr.dan, '9,999,999,990.90'), ',', ' '), '.', ',') as poznamka,
             ma.md_d
      from obratybu ob,
           obratybuobratid obo,
           majetokarchiv ma,
           konfirmaciaktv k,
           pool po,
           pooldetailreal pdr
      where ob.id = obo.id
        and ma.obratid = obo.obratid
        and ma.$fondid_add
        and ma.kodobratu = 204
        and ma.uctovnykod = 325300
        and ob.logactivityid = 15
        and ma.obratdatatimezauctovane >= to_date('$fromdate', 'YYYY-MM-DD')
        and ma.obratdatatimezauctovane <= to_date('$todate', 'YYYY-MM-DD')
        and ob.krdb = 1
        and k.subjektid = 0
        and pdr.poolid = po.poolid
        and pdr.subjektid = ma.SUBJEKTID
        and k.dealid = po.dealid
        and k.dealid::text = ob.vs
      union all
      select 40                         as poradie,
             'Splatenie kupónu'         as popis,
             ma.mena                    as mena1,
             s.suma                     as transsuma,
             ma.OBRATDATATIMEZAUCTOVANE as datum,
             (select e.cpnaz || '; ISIN ' || e.isinreal
              from dbequity e,
                   dbequitycurr c,
                   dbequitycurrric r
              where r.isincurrric = ma.kodaktiva
                and r.isincurr = c.isincurr
                and c.isin = e.isin)    as poznamka,
             0                          as md_d
      from splatenie s,
           splatenieobratid so,
           majetokarchiv ma
      where s.DEALID = so.DEALID
        and so.OBRATID = ma.obratid
        and so.tranza = s.tranza
        and s.suma <> 0
        and ma.$fondid_add
        and ma.kodobratu = 137
        and ma.uctovnykod = 315124
        and ma.destinacia = 'dbequity'
        and ma.obratdatatimezauctovane >= to_date('$fromdate', 'YYYY-MM-DD')
        and ma.obratdatatimezauctovane <= to_date('$todate', 'YYYY-MM-DD')
      union all
      select 35                                 as poradie,
             case
                 when d.isin in ('SK4000016598') then 'Splatenie investičného certifikátu'
                 else 'Splatenie dlhopisu' end  as popis,
             s.mena                             as mena1,
             s.suma                             as transsuma,
             s.datumvyplaty                     as datum,
             d.cpnaz || '; ISIN ' || d.isinreal as poznamka,
             0                                  as md_d
      from splatenie s,
           dbequity d,
           dbequitycurr dc,
           dbequitycurrric dcr,
           portfolio p
      where s.uctovnykod = 251110
        and s.datumvyplaty >= to_date('$fromdate', 'YYYY-MM-DD')
        and s.datumvyplaty <= to_date('$todate', 'YYYY-MM-DD')
        and s.$fondid_add
        and
          s.datum_naroku = (select max(datesplatnost) from floatkupon where s.kodaktiva = isincurrric and istina >= 100)
        and dcr.isincurrric = s.kodaktiva
        and dcr.isincurr = dc.isincurr
        and dc.isin = d.isin
        and p.fondid = s.subjektid
      union all
      select 35                                     as poradie,
             'Čiastočné splatenie istiny - konečné' as popis,
             s.mena                                 as mena1,
             s.suma                                 as transsuma,
             s.datumvyplaty                         as datum,
             d.cpnaz || '; ISIN ' || d.isinreal     as poznamka,
             0                                      as md_d
      from splatenie s,
           dbequity d,
           dbequitycurr dc,
           dbequitycurrric dcr,
           portfolio p
      where s.uctovnykod = 251110
        and s.datumvyplaty >= to_date('$fromdate', 'YYYY-MM-DD')
        and s.datumvyplaty <= to_date('$todate', 'YYYY-MM-DD')
        and s.$fondid_add
        and
          s.datum_naroku = (select max(datesplatnost) from floatkupon where s.kodaktiva = isincurrric and istina < 100)
        and dcr.isincurrric = s.kodaktiva
        and dcr.isincurr = dc.isincurr
        and dc.isin = d.isin
        and p.fondid = s.subjektid
      union all
      select 35                                       as poradie,
             'Čiastočné splatenie istiny - priebežné' as popis,
             s.mena                                   as mena1,
             s.suma                                   as transsuma,
             s.datumvyplaty                           as datum,
             d.cpnaz || '; ISIN ' || d.isinreal       as poznamka,
             0                                        as md_d
      from splatenie s,
           dbequity d,
           dbequitycurr dc,
           dbequitycurrric dcr,
           portfolio p
      where s.uctovnykod = 251110
        and s.datumvyplaty >= to_date('$fromdate', 'YYYY-MM-DD')
        and s.datumvyplaty <= to_date('$todate', 'YYYY-MM-DD')
        and s.$fondid_add
        and
          s.datum_naroku < (select max(datesplatnost) from floatkupon where s.kodaktiva = isincurrric and istina < 100)
        and dcr.isincurrric = s.kodaktiva
        and dcr.isincurr = dc.isincurr
        and dc.isin = d.isin
        and p.fondid = s.subjektid
      union all
      select 50                                   as poradie,
             d.nazov                              as popis,
             sa.mena                              as mena1,
             sa.suma                              as transsuma,
             sa.datumvyplaty                      as datum,
             de.cpnaz || '; ISIN ' || de.isinreal as poznamka,
             0
      from splatenieakcia sa,
           dbequity de,
           dbequitycurr dr,
           dbequitycurrric drr,
           dividendaakciatyp d
      where sa.$fondid_add
        and sa.datumvyplaty >= to_date('$fromdate', 'YYYY-MM-DD')
        and sa.datumvyplaty <= to_date('$todate', 'YYYY-MM-DD')
        and d.subkodobratu = sa.subkodobratu
        and sa.subkodobratu in (select subkodobratu from dividendaakciatyp where hotovost = 1)
        and sa.kodaktiva = drr.isincurrric
        and dr.isincurr = drr.isincurr
        and de.isin = dr.isin
      union all
      select 400              as poradie,
             'Ostatné platby' as popis,
             ob.mena          as mena1,
             ob.suma          as transsuma,
             ob.obratdatetime as datum,
             ob.nazpartnera   as poznamka,
             ma.md_d
      from obratybu ob,
           obratybuobratid obo,
           majetokarchiv ma
      where ob.id = obo.id
        and ma.obratid = obo.obratid
        and ma.$fondid_add
        and ma.uctovnykod in (221110, 325300)
        and ma.kodobratu not in
            (201, 203, 204, 205, 206, 207, 208, 209, 210, 213, 215, 214, 274, 279, 231, 232, 233, 234, 235, 236, 237,
             238, 260, 261, 262, 263, 285, 401, 402, 403, 601, 602, 603, 613, 614, 631, 632, 633, 634, 644, 645, 646,
             647, 648, 650)
        and ma.kodobratu in (219, 226, 228, 266, 287, 288, 610, 620, 621, 622, 623, 624, 625, 626)
        and ob.logactivityid = 15
        and ob.obratdatetime >= to_date('$fromdate', 'YYYY-MM-DD')
        and ob.obratdatetime <= to_date('$todate', 'YYYY-MM-DD')
      union all
      select 320                                                                          as poradie,
             'Poplatok za transakciu s CP'                                                as popis,
             pr.mena                                                                      as mena1,
             pr.suma                                                                      as transsuma,
             u.datesplatnost                                                              as datum,
             'Dátum obchodu ' || to_char(pr.datum, 'DD.MM.YYYY') || '; ISIN ' || kcp.ISIN as poznamka,
             ma.md_d
      from uhrada u,
           uhradaobratid ui,
           majetokarchiv ma,
           poplatok_register_links prl,
           poplatok_register pr,
           konfirmaciacp kcp
      where u.kodobratu = 350
        and u.id = ui.id
        and ma.obratid = ui.obratid
        and ma.$fondid_add
        and ma.uctovnykod = 221110
        and u.logactivityid = 15
        and datesplatnost >= to_date('$fromdate', 'YYYY-MM-DD')
        and datesplatnost <= to_date('$todate', 'YYYY-MM-DD')
        and prl.uhrada_id = u.id
        and pr.id = prl.poplatok_register_id
        and pr.typ = 'TRAN'
        and pr.fondid = ma.subjektid
        and pr.dealid = kcp.dealid
      union all
      select 330                                                                          as poradie,
             'Poplatok za vysporiadanie transakcie s CP'                                  as popis,
             pr.mena                                                                      as mena1,
             pr.suma                                                                      as transsuma,
             u.datesplatnost                                                              as datum,
             'Dátum obchodu ' || to_char(pr.datum, 'DD.MM.YYYY') || '; ISIN ' || kcp.ISIN as poznamka,
             ma.md_d
      from uhrada u,
           uhradaobratid ui,
           majetokarchiv ma,
           poplatok_register_links prl,
           poplatok_register pr,
           konfirmaciacp kcp
      where u.kodobratu = 350
        and u.id = ui.id
        and ma.obratid = ui.obratid
        and ma.$fondid_add
        and ma.uctovnykod = 221110
        and u.logactivityid = 15
        and datesplatnost >= to_date('$fromdate', 'YYYY-MM-DD')
        and datesplatnost <= to_date('$todate', 'YYYY-MM-DD')
        and prl.uhrada_id = u.id
        and pr.id = prl.poplatok_register_id
        and pr.typ = 'VYROV'
        and pr.fondid = ma.subjektid
        and pr.dealid = kcp.dealid
      union all
      select 350                        as poradie,
             'Poplatok za konverziu'    as popis,
             ma.mena                    as mena1,
             ma.pocet                   as transsuma,
             ma.obratdatatimezauctovane as datum,
             ''                         as poznamka,
             ma.md_d
      from majetokarchiv ma
      where ma.$fondid_add
        and ma.kodobratu = 346
        and ma.uctovnykod = 261992
        and ma.obratdatatimezauctovane >= to_date('$fromdate', 'YYYY-MM-DD')
        and ma.obratdatatimezauctovane <= to_date('$todate', 'YYYY-MM-DD')
      union all
      select 340                                   as poradie,
             'Poplatok za obchod na peňažnom trhu' as popis,
             mena                                  as mena1,
             ma.pocet                              as transsuma,
             ma.obratdatatimezauctovane            as datum,
             ''                                    as poznamka,
             ma.md_d
      from majetokarchiv ma
      where ma.$fondid_add
        and ma.kodobratu = 345
        and ma.uctovnykod = 261991
        and ma.obratdatatimezauctovane >= to_date('$fromdate', 'YYYY-MM-DD')
        and ma.obratdatatimezauctovane <= to_date('$todate', 'YYYY-MM-DD')
      union all
      select 310                                                                           as poradie,
             'Poplatok za správu portfólia'                                                as popis,
             pr.mena                                                                       as mena1,
             pr.suma                                                                       as transsuma,
             u.datesplatnost                                                               as datum,
             to_char(pr.datumod, 'DD.MM.YYYY') || ' - ' || to_char(pr.datum, 'DD.MM.YYYY') as poznamka,
             md_d
      from uhrada u,
           uhradaobratid ui,
           majetokarchiv ma,
           poplatok_register_links prl,
           poplatok_register pr
      where u.kodobratu = 350
        and u.id = ui.id
        and ma.obratid = ui.obratid
        and ma.$fondid_add
        and ma.uctovnykod = 221110
        and logactivityid = 15
        and datesplatnost >= to_date('$fromdate', 'YYYY-MM-DD')
        and datesplatnost <= to_date('$todate', 'YYYY-MM-DD')
        and prl.uhrada_id = u.id
        and pr.id = prl.poplatok_register_id
        and pr.typ = 'SPRAVA'
        and pr.fondid = ma.subjektid
      union all
      select 300                                                                           as poradie,
             'Poplatok za riadenie portfólia'                                              as popis,
             ma.mena                                                                       as mena1,
             coalesce(sum(((0.5 - ma.md_d) / 0.5) * pr.suma), 0)                           as transsuma,
             u.datesplatnost                                                               as datum,
             to_char(pr.datumod, 'DD.MM.YYYY') || ' - ' || to_char(pr.datum, 'DD.MM.YYYY') as poznamka,
             ma.md_d
      from uhrada u,
           uhradaobratid ui,
           majetokarchiv ma,
           poplatok_register_links prl,
           poplatok_register pr
      where u.kodobratu = 350
        and u.id = ui.id
        and ma.obratid = ui.obratid
        and ma.$fondid_add
        and ma.uctovnykod = 221110
        and logactivityid = 15
        and datesplatnost >= to_date('$fromdate', 'YYYY-MM-DD')
        and datesplatnost <= to_date('$todate', 'YYYY-MM-DD')
        and prl.uhrada_id = u.id
        and pr.id = prl.poplatok_register_id
        and pr.typ = 'MANAZ'
        and pr.fondid = ma.subjektid
      group by ma.mena, u.datesplatnost, ma.md_d, pr.datumod, pr.datum
      union all
      select 360             as poradie,
             'Poplatok'      as popis,
             ma.mena         as mena1,
             pr.suma         as transsuma,
             u.datesplatnost as datum,
             pr.dovod        as poznamka,
             ma.md_d
      from uhrada u,
           uhradaobratid ui,
           majetokarchiv ma,
           poplatok_register_links prl,
           poplatok_register pr
      where u.kodobratu = 350
        and u.id = ui.id
        and ma.obratid = ui.obratid
        and ma.$fondid_add
        and ma.uctovnykod = 221110
        and logactivityid = 15
        and datesplatnost >= to_date('$fromdate', 'YYYY-MM-DD')
        and datesplatnost <= to_date('$todate', 'YYYY-MM-DD')
        and prl.uhrada_id = u.id
        and pr.id = prl.poplatok_register_id
        and pr.typ = 'OSTATNE'
        and pr.fondid = ma.subjektid
      union all
      select 370                     as poradie,
             'Vstupný poplatok'      as popis,
             mena                    as mena1,
             pocet                   as transsuma,
             obratdatatimezauctovane as datum,
             ''                      as poznamka,
             md_d
      from majetokarchiv
      where $fondid_add
        and kodobratu = 310
        and uctovnykod = 261961
        and obratdatatimezauctovane >= to_date('$fromdate', 'YYYY-MM-DD')
        and obratdatatimezauctovane <= to_date('$todate', 'YYYY-MM-DD')
      union all
      select 380                            as poradie,
             'Poplatok za mimoriadny výpis' as popis,
             mena                           as mena1,
             pocet                          as transsuma,
             obratdatatimezauctovane        as datum,
             ''                             as poznamka,
             md_d
      from majetokarchiv
      where $fondid_add
        and kodobratu = 344
        and uctovnykod = 261995
        and obratdatatimezauctovane >= to_date('$fromdate', 'YYYY-MM-DD')
        and obratdatatimezauctovane <= to_date('$todate', 'YYYY-MM-DD')
      union all
      SELECT 200                                                   AS poradie,
             'Konverzia - Šhrada'                                  AS popis,
             k.menadebet                                           AS mena1,
             COALESCE((SELECT pd.transsumareal
                       FROM pool p
                                JOIN pooldetailreal pd ON p.poolid = pd.poolid
                       WHERE p.dealid = k.dealid
                         AND pd.subjektid = ma.subjektid), u.suma) AS transsuma,
             u.datesplatnost                                       AS datum,
             (CASE
                  WHEN menadebet || menakredit = menovypar THEN menadebet || '/' || menakredit
                  ELSE menakredit || '/' || menadebet END) || ' kurz = ' || (CASE
                                                                                 WHEN k.kurz < 1
                                                                                     THEN replace(('0' || k.kurz::text), '.', ',')
                                                                                 ELSE replace(k.kurz::text, '.', ',') END) ||
             ''                                                    AS poznamka,
             ma.md_d
      FROM uhrada u
               JOIN uhradaobratid ui ON u.id = ui.id
               JOIN majetokarchiv ma ON ui.obratid = ma.obratid
               JOIN konverzia k ON u.dealid = k.dealid
      WHERE u.kodobratu = 334
        AND ma.$fondid_add
        AND ma.kodobratu = 334
        AND ma.uctovnykod = 261914
        AND u.logactivityid = 15
        AND u.datesplatnost >= '$fromdate'::date
        AND u.datesplatnost <= '$todate'::date
      union all
      SELECT 90                         AS poradie,
             'Konverzia - došlá platba' AS popis,
             ob.mena                    AS mena1,
             ma.pocet                   AS transsuma,
             ma.obratdatatimezauctovane AS datum,
             foo.popis                  AS poznamka,
             ma.md_d
      FROM obratybu ob
               JOIN obratybuobratid obo ON ob.id = obo.id
               JOIN majetokarchiv ma ON ma.obratid = obo.obratid
               LEFT JOIN (SELECT (CASE
                                      WHEN menadebet || menakredit = menovypar THEN menadebet || '/' || menakredit
                                      ELSE menakredit || '/' || menadebet END) || ' kurz = ' || (CASE
                                                                                                     WHEN k.kurz < 1
                                                                                                         THEN replace(('0' || k.kurz::text), '.', ',')
                                                                                                     ELSE replace(k.kurz::text, '.', ',') END) AS popis,
                                 mak.obratdatatimezauctovane,
                                 mak.pocet,
                                 mak.mena,
                                 mak.subjektid
                          FROM konverzia k
                                   JOIN konverziaobratid ko ON k.dealid = ko.dealid
                                   JOIN majetokarchiv mak ON ko.obratid = mak.obratid
                          WHERE mak.kodobratu = 237
                            AND mak.uctovnykod IN (315160, 315161)) foo
                         ON ma.pocet = foo.pocet AND ma.mena = foo.mena AND
                            ma.obratdatatimezauctovane = foo.obratdatatimezauctovane AND ma.subjektid = foo.subjektid
      WHERE ma.$fondid_add
        AND ma.kodobratu = 237
        AND ma.uctovnykod = 325300
        AND ob.logactivityid = 15
        AND ob.obratdatetime >= '$fromdate' ::date
        AND ob.obratdatetime <= '$todate' ::date
        AND ob.krdb = 1
      union all
      select 410                                                             as poradie,
             (case when k1.druhobchodu = 'prevod' then 'Prevod' end) || ' na portfólio č.' ||
             (select cislozmluvy from portfolio where fondid = k2.subjektid) as popis,
             k1.mena                                                         as mena1,
             k1.suma                                                         as transsuma,
             k1.datum_zauctovania                                            as datum,
             ''                                                              as poznamka,
             1
      from konfirmaciapp k1,
           konfirmaciapp k2
      where k1.dealid_related is not null
        and k1.$fondid_add
        and k1.logactivityid = 12
        and k1.druhobchodu in ('prevod')
        and k1.subjektid != 0
        and k2.dealid = k1.dealid_related
        and k1.datum_zauctovania >= to_date('$fromdate', 'YYYY-MM-DD')
        and k1.datum_zauctovania <= to_date('$todate', 'YYYY-MM-DD')
      union all
      select 100                                                             as poradie,
             (case when k1.druhobchodu = 'prevod' then 'Prevod' end) || ' z portfólia č.' ||
             (select cislozmluvy from portfolio where fondid = k2.subjektid) as popis,
             k1.mena                                                         as mena1,
             k1.suma                                                         as transsuma,
             k1.datum_zauctovania                                            as datum,
             ''                                                              as poznamka,
             0
      from konfirmaciapp k1,
           konfirmaciapp k2
      where k1.dealid_related is null
        and k1.$fondid_add
        and k1.logactivityid = 12
        and k1.druhobchodu in ('prevod')
        and k1.subjektid != 0
        and k2.dealid_related = k1.dealid
        and k1.datum_zauctovania >= to_date('$fromdate', 'YYYY-MM-DD')
        and k1.datum_zauctovania <= to_date('$todate', 'YYYY-MM-DD')
      union all
      select 101                                                                               as poradie,
             (case when k1.druhobchodu = 'presun' then 'Presun' end) || ' z ��tu: ' || k1.ucet as popis,
             k1.mena                                                                           as mena1,
             k1.suma                                                                           as transsuma,
             k1.datum_zauctovania                                                              as datum,
             ''                                                                                as poznamka,
             ma.md_d
      from konfirmaciapp k1,
           konfirmaciappobratid ko,
           majetokarchiv ma
      where k1.subjektid = 1
        and k1.dealid_related is not null
        and k1.$fondid_add
        and k1.dealid = ko.dealid
        and ko.obratid = ma.obratid
        and k1.ucet = ma.ucetaktiva
        and ma.uctovnykod = 221110
        and k1.logactivityid = 12
        and k1.druhobchodu in ('presun')
        and k1.datum_zauctovania >= to_date('$fromdate', 'YYYY-MM-DD')
        and k1.datum_zauctovania <= to_date('$todate', 'YYYY-MM-DD')
      union all
      select 102                                                                                as poradie,
             (case when k1.druhobchodu = 'presun' then 'Presun' end) || ' na ��et: ' || k1.ucet as popis,
             k1.mena                                                                            as mena1,
             k1.suma                                                                            as transsuma,
             k1.datum_zauctovania                                                               as datum,
             ''                                                                                 as poznamka,
             ma.md_d
      from konfirmaciapp k1,
           konfirmaciapp k2,
           konfirmaciappobratid ko,
           majetokarchiv ma
      where k1.subjektid = 1
        and k1.$fondid_add
        and k1.dealid_related is null
        and k1.dealid = k2.dealid_related
        and k2.dealid = ko.dealid
        and ko.obratid = ma.obratid
        and k1.ucet = ma.ucetaktiva
        and ma.uctovnykod = 221110
        and k1.logactivityid = 12
        and k1.druhobchodu in ('presun')
        and k1.datum_zauctovania >= to_date('$fromdate', 'YYYY-MM-DD')
        and k1.datum_zauctovania <= to_date('$todate', 'YYYY-MM-DD')
      union all
      select 110                       as poradie,
             'Ostatné platby'          as popis,
             m.mena                    as mena1,
             m.pocet                   as transsuma,
             m.obratdatatimezauctovane as datum,
             ob.nazpartnera            as poznamka,
             0
      from majetokarchiv m,
           obratybu ob,
           obratybuobratid obo
      where m.obratdatatimezauctovane >= to_date('$fromdate', 'YYYY-MM-DD')
        and m.obratdatatimezauctovane <= to_date('$todate', 'YYYY-MM-DD')
        and m.$fondid_add
        and m.uctovnykod = 668000
        and m.md_d = 1
        and m.KODOBRATU = 214
        and ob.id = obo.id
        and m.obratid = obo.obratid
--neberiem vkladypp ani opravy vkladov
        and not exists (select 1
                        from konfirmaciapp k5,
                             obratybu o5,
                             obratybuobratid oo5
                        where k5.subjektid = m.subjektid
                          and k5.logactivityid = 12
                          and o5.subjektid = k5.subjektid
                          and o5.ss = k5.dealid::text
                          and o5.suma = k5.suma
                          and o5.mena = k5.mena
                          and o5.cub = k5.ucet
                          and oo5.id = o5.id
                          and oo5.obratid = m.obratid
                        union all
                        select 1
                        from majetokarchiv ma5
                        where ma5.OBRATDATATIMEZAUCTOVANE = m.OBRATDATATIMEZAUCTOVANE
                          and ma5.$fondid_add
                          and ma5.uctovnykod = 668000
                          and ma5.md_d = 0
                          and ma5.KODOBRATU = 214)
      union all
      select 420                                          as poradie,
             'Výber klienta'                              as popis,
             k.mena                                       as mena1,
             coalesce(sum(((0.5 - 1) / 0.5) * k.suma), 0) as transsuma,
             k.datum_zauctovania                          as datum,
             k.externy_ucet                               as poznamka,
             1
      from konfirmaciapp k
      where k.druhobchodu = 'vyber'
        and k.$fondid_add
        and k.logactivityid = 12
        and k.datum_zauctovania >= to_date('$fromdate', 'YYYY-MM-DD')
        and k.datum_zauctovania <= to_date('$todate', 'YYYY-MM-DD')
      group by k.mena, k.datum_zauctovania, k.externy_ucet
      union all
      SELECT 120                                                                    AS poradie,
             'Vklad klienta'                                                        AS popis,
             k.mena                                                                 AS mena1,
             k.suma                                                                 AS transsuma,
             k.datum_zauctovania                                                    AS datum,
             (SELECT cubpartnera FROM obratybu o WHERE o.ss::text = k.dealid::text) AS poznamka,
             0
      FROM konfirmaciapp k
      WHERE k.druhobchodu = 'vklad'
        AND k.$fondid_add
        AND k.logactivityid = 12
        AND k.datum_zauctovania >= '$fromdate'::date
        AND k.datum_zauctovania <= '$todate'::date) t
order by mena1, poradie, datum
";
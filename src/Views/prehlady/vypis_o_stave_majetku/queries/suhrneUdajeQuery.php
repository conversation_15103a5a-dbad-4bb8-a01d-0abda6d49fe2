<?php
$suhrneUdajeQuery = "
		SELECT
			1 as typ,
			'<PERSON><PERSON> vkladov pp a cp' as popis,
			(f_getSumaVklady($fondid,to_date('$fromdate','YYYY-MM-DD'),to_date('$todate','YYYY-MM-DD'),1, '$refmena')) as suma		
		union all
		
		SELECT
			2 as typ,
			'<PERSON><PERSON> vyberov pp a cp' as popis,
			(f_getSumaVybery($fondid,to_date('$fromdate','YYYY-MM-DD'),to_date('$todate','YYYY-MM-DD'),1, '$refmena')) as suma		
		union all
		
		SELECT
            3 AS typ,
            'Prevod pp z ineho pf' AS popis, 
            SUM(k.suma * (SELECT kurz FROM f_menovy_kurz_lot(k.mena, '$refmena', k.datum_zauctovania))) AS suma
        FROM 
            konfirmaciapp k
		where
			k.druhobchodu = 'prevod' and
			k.dealid_related is null and
			k.subjektid = $fondid and
			k.logactivityid = 12 and
			k.datum_zauctovania >= to_date('$fromdate','YYYY-MM-DD') and
			k.datum_zauctovania <= to_date('$todate','YYYY-MM-DD')
			
		union all
		
		select
			4 as typ,
			'Prevod CP z ineho pf' as popis,
			sum((r.transsuma*(SELECT kurz FROM f_menovy_kurz_lot(k.currencyidtrade,'$refmena',r.dattransakcie)))) as suma
		FROM 
			konfirmaciacp k,
			rekonfirmaciacp r
		where
			k.dealid = r.dealid and
			k.druhobchodu = 'vklad-pr' and
			k.subjektid = $fondid and
			r.logactivityid >= 12 and
			r.logactivityid not in (14) and
			r.dattransakcie >= to_date('$fromdate','YYYY-MM-DD') and
			r.dattransakcie <= to_date('$todate','YYYY-MM-DD')
		
		union all
		
		select
			5 as typ,
			'Prevod pp na ine pf' as popis, 
			sum((k.suma * (SELECT kurz FROM f_menovy_kurz_lot(k.mena,'$refmena',k.datum_zauctovania)))) as suma
		FROM 
			konfirmaciapp k
		where
			k.druhobchodu = 'prevod' and
			k.dealid_related is not null and
			k.subjektid = $fondid and
			k.logactivityid = 12 and
			k.datum_zauctovania >= to_date('$fromdate','YYYY-MM-DD') and
			k.datum_zauctovania <= to_date('$todate','YYYY-MM-DD')
			
		union all
		
		select
			6 as typ,
			'Prevod CP na ine pf' as popis,
			sum((r.transsuma*(SELECT kurz FROM f_menovy_kurz_lot(k.currencyidtrade,'$refmena',r.dattransakcie)))) as suma
		FROM 
			konfirmaciacp k,
			rekonfirmaciacp r
		where
			k.dealid = r.dealid and
			k.druhobchodu = 'vyber-pr' and
			k.subjektid = $fondid and
			r.logactivityid >= 12 and
			r.logactivityid not in (14) and
			r.dattransakcie >= to_date('$fromdate','YYYY-MM-DD') and
			r.dattransakcie <= to_date('$todate','YYYY-MM-DD')
		
		union all		
		
		select 
			7 as typ,
			'Pociatocny stav' as popis,				
			SUM(COALESCE((CASE WHEN md_d = 0 THEN md_d0 ELSE md_d1 END) * sumadenom * (SELECT kurz FROM f_menovy_kurz_lot(m.menadenom,'$refmena',m.datum)),0)) as suma
		FROM
			majetoktotal m, navuctovanie nu
		WHERE
			m.uctovnykod = nu.uctovnykod and
			m.subjektid=$fondid and
			m.datum = (select max(datum) FROM pricestore where fondid=$fondid and datum < to_date('$fromdate','YYYY-MM-DD') )

		union all
		
		select 
			8 as poradie,
			'Konecny stav' as popis,
			sum(COALESCE((CASE WHEN md_d = 0 THEN md_d0 ELSE md_d1 END) * sumadenom * (SELECT kurz FROM f_menovy_kurz_lot(m.menadenom,'$refmena',m.datum)),0)) as suma
			FROM
			majetoktotal m, navuctovanie nu
			WHERE
				m.uctovnykod = nu.uctovnykod and
				m.subjektid=$fondid and
				m.datum = to_date('$todate','YYYY-MM-DD')
		
		union all

		select
			9 as typ,
			'Vyplatenie istiny na vlastny ucet' as popis,
			COALESCE(SUM(s.suma * (SELECT kurz FROM f_menovy_kurz_lot(s.mena,'$refmena',s.datumvyplaty))),0) as suma
		FROM
		    	splatenie s		    	
		where
			s.subjektid = $fondid and 
		  	s.datumvyplaty >= to_date('$fromdate','YYYY-MM-DD') and
			s.datumvyplaty <= to_date('$todate','YYYY-MM-DD') and
			s.uctovnykod = (251110) and
			s.fiktivne = 1			

		union all
		
		select
			10 as typ,
			'Vyplatenie kuponov na vlastny ucet' as popis,
			COALESCE(SUM(d.danzaklad*(SELECT kurz FROM f_menovy_kurz_lot(s.mena,'$refmena',s.datumvyplaty))),0) as suma
		FROM
		    	splatenie s,
		    	danvynosy d
		where
			s.subjektid = $fondid and 
		  	s.datumvyplaty >= to_date('$fromdate','YYYY-MM-DD') and
			s.datumvyplaty <= to_date('$todate','YYYY-MM-DD') and
			s.uctovnykod = (251120) and
			s.fiktivne = 1 and
			d.dealid = s.dealid and
			d.destinacia='splatenie'

		union all
		
		select
			11 as typ,
			'Vyplatenie dividend na vlastny ucet' as popis,
			COALESCE(sum(d.danzaklad*(SELECT kurz FROM f_menovy_kurz_lot(sa.mena,'$refmena',sa.datumvyplaty))),0) 
			as suma 
		FROM
			splatenieakcia sa,
			danvynosy d
		where
			sa.subjektid = $fondid and
			sa.datumvyplaty >= to_date('$fromdate','YYYY-MM-DD') and
			sa.datumvyplaty <= to_date('$todate','YYYY-MM-DD') and
			sa.subkodobratu <=9 and
			sa.fiktivne = 1 and
			d.dealid = sa.dealid and
			d.destinacia = 'splatenieakcia'		
	
		union all
		
		select
			12 as typ,
		 	'Dan z vynosov dividendy' as popis,
		  	COALESCE(sum(d.dan*(SELECT kurz FROM f_menovy_kurz_lot(d.mena,'$refmena',s.datumvyplaty))),0) 
		  	as suma
		FROM
			danvynosy d,
			splatenieakcia s
		where
			 d.destinacia = 'splatenieakcia' and
			 d.dealid = s.dealid and
			 s.subjektid = $fondid and
			 s.dealid = d.dealid and
			 s.subkodobratu <=9 and
			 s.datumvyplaty >= to_date('$fromdate','YYYY-MM-DD') and
			 s.datumvyplaty <= to_date('$todate','YYYY-MM-DD') and
			 s.fiktivne = 1		
		
		union all
		
		select
			13 as typ,
			'Dan z vynosov kuponov' as popis,
			COALESCE(sum(d.dan*(SELECT kurz FROM f_menovy_kurz_lot(s.mena,'$refmena',s.datumvyplaty))),0)
			as suma 
		FROM
		    splatenie s,
			danvynosy d
		where
			s.subjektid = $fondid and 
		    s.datumvyplaty >= to_date('$fromdate','YYYY-MM-DD') and
			s.datumvyplaty <= to_date('$todate','YYYY-MM-DD') and
			s.uctovnykod = (251120) and
			d.destinacia = 'splatenie' and
			d.dealid = s.dealid and
			s.fiktivne = 1
	";
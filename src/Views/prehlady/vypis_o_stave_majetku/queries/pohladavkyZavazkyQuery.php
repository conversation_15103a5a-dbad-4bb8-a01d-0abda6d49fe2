<?php
$pohladavkyZavazkyQuery = "
			SELECT 
				sum((sumadenom*sign(0.5-md_d) 
					   - COALESCE(
								(
									select sum(sumadenom*sign(0.5-md_d)) 
									from majetoktotal 
									where datum=mt.datum and uctovnykod in (315132,315113) and eqid = mt.eqid and subjektid = mt.subjektid
									and kodaktiva = mt.kodaktiva and ucetaktiva = mt.ucetaktiva
									and exists
									(
										select * 
										from majetoktotal 
										where datum=mt.datum and uctovnykod in (221110,221210) and eqid = mt.eqid and subjektid = mt.subjektid
											and kodaktiva = mt.kodaktiva and ucetaktiva = mt.ucetaktiva
									)
								),0
							)
					) * (SELECT kurz FROM f_menovy_kurz_lot(menadenom,'$refmena',datum)) 
				) as suma 
			from 
				majetoktotal mt
			left join dbequitycurrric dcr on dcr.isincurrric = mt.kodaktiva
			left join dbequitycurr dc on dc.isincurr = dcr.isincurr
			left join dbequity d on d.isin = dc.isin
			where datum = to_date('$dbdate', 'YYYY-MM-DD')
			  and uctovnykod in (221110, 221210) 
			  and mt.eqid in ('BU', 'TD') 
			  and subjektid = $fondid;
		";
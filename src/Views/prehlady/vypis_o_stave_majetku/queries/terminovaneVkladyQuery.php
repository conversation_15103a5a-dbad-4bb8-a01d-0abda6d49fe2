<?php
$fondid_add = "subjektid in ($fondid) ";
$queryTerminovaneVklady = "
			select * from
			(
			SELECT 
				   (case when z_td < to_date('$fromdate','YYYY-MM-DD') then 1
				   		 when k_td > to_date('$todate','YYYY-MM-DD') then 3
				   		 else 2
				   	end) as trieda,
				   k.z_td as zaciatok,
				   k.k_td as koniec,
				   k.sum_td as suma,
				   k.ir_td as sadz<PERSON>,
				   k.iv_b as brutto,
				   k.iv_n as netto_old,
				   k.iv_n as netto,
				   k.mena,
				   k.suma_dane as dan,
				   k.datvysporiadaniarealntd as vysporiadanie,
				   k.cutd,
				   k.subjektid,
				   z_td,
				   k_td,
				   p.nazovpart<PERSON>a as miesto,
				   k.datum_cas_obchodu as datum_cas,
				   (select typ_pokynu from typy_pokynov tp where tp.typid = k.typ_pokynu) as typPokynu,
				   '' as detailKTV
			from 
				konfirmaciaktv k,
				partner p
			where 
				((z_td >= to_date('$fromdate','YYYY-MM-DD') and z_td <= to_date('$todate','YYYY-MM-DD'))
				 or
				 (k_td >= to_date('$fromdate','YYYY-MM-DD') and k_td <= to_date('$todate','YYYY-MM-DD')))
				and logactivityid in (17, 25)
			 	and k.$fondid_add 
				and k.partnerid = p.partnerid	
			
			union all
				
			select 	   
				   (case when z_td < to_date('$fromdate','YYYY-MM-DD') then 1
				   		 when k_td > to_date('$todate','YYYY-MM-DD') then 3
				   		 else 2
				   	end) as trieda,
				   k.z_td as zaciatok,
				   k.k_td as koniec,
				   pdr.transsumareal as suma,
				   k.ir_td as sadzba,
				   pdr.auvreal  as brutto,
	   	   		   k.iv_n * pdr.transsumareal / (select sum(x.transsumareal) from pooldetailreal x where x.poolid=po.poolid) as netto_old,
	   	   		   (pdr.auvreal - pdr.dan) as netto,
				   k.mena,
				   pdr.dan as dan,
				   k.datvysporiadaniarealntd as vysporiadanie,
				   k.cutd,
				   k.subjektid,
				   z_td,
				   k_td,
				   p.nazovpartnera as miesto,
				   k.datum_cas_obchodu as datum_cas,
				   (select typ_pokynu from typy_pokynov tp where tp.typid = k.typ_pokynu) as typPokynu,
				   (
					 case
					  when sum_td_pov is null then ''
					  else 'uzavreteSKK'
					 end 
					 ) as detailKTV
			from
				konfirmaciaktv k,
				pool po,
				pooldetailreal pdr,
				partner p
			where
				((z_td >= to_date('$fromdate','YYYY-MM-DD') and z_td <= to_date('$todate','YYYY-MM-DD'))
				 or
				 (k_td >= to_date('$fromdate','YYYY-MM-DD') and k_td <= to_date('$todate','YYYY-MM-DD')))
				 and k.subjektid = 0
				 and pdr.poolid = po.poolid 
				 and pdr.$fondid_add
				 and k.dealid = po.dealid
				 and k.logactivityid in (17, 25)
				 and k.partnerid = p.partnerid
				 
				 
			union all
			
			select 	   
				   (case when z_td < to_date('$fromdate','YYYY-MM-DD') then 1
				   		 when k_td > to_date('$todate','YYYY-MM-DD') then 3
				   		 else 2
				   	end) as trieda,
				   k.z_td as zaciatok,
				   k.k_td as koniec,
				   pdr.transsumareal_pov as suma,
				   k.ir_td as sadzba,
				   0  as brutto,
	   	   	 0 as netto_old,
	   	   	 0 as netto,
				   'SKK',
				   0 as dan,
				   k.datvysporiadaniarealntd as vysporiadanie,
				   k.cutd,
				   k.subjektid,
				   z_td,
				   k_td,
				   p.nazovpartnera as miesto,
				   k.datum_cas_obchodu as datum_cas,
				   (select typ_pokynu from typy_pokynov tp where tp.typid = k.typ_pokynu) as typPokynu,
				   'vyplateneEUR' as detailKTV
			from
				konfirmaciaktv k,
				pool po,
				pooldetailreal pdr,
				partner p
			where
				((z_td >= to_date('$fromdate','YYYY-MM-DD') and z_td <= to_date('$todate','YYYY-MM-DD'))
				 or
				 (k_td >= to_date('$fromdate','YYYY-MM-DD') and k_td <= to_date('$todate','YYYY-MM-DD')))
				 and k.subjektid = 0
				 and pdr.poolid = po.poolid 
				 and pdr.$fondid_add
				 and k.dealid = po.dealid
				 and k.logactivityid in (17, 25)
				 and k.partnerid = p.partnerid
				 and k.sum_td_pov is not null
			) as kpkp
			order by mena, trieda, z_td, k_td
";
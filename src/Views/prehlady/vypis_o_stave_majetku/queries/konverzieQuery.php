<?php
$konverzieQuery = "SELECT *
FROM (
    SELECT
        k.datum_cas_obchodu AS datum_cas,
        p.nazovpartnera AS miesto,
        (SELECT typ_pokynu FROM typy_pokynov tp WHERE tp.typid = k.typ_pokynu) AS typPokynu,
        k.kurz,
        k.sumakredit,
        k.sumadebet,
        k.menakredit,
        k.menadebet,
        CASE WHEN k.typ_konverzie = 0 THEN 'Spot' WHEN k.typ_konverzie = 2 THEN 'Forward' END AS druhObchodu,
        TO_CHAR(k.dat_vysporiadaniecukredit, 'DD.MM.YYYY') AS datum
    FROM
        konverzia k,
        partner p
    WHERE
        k.logactivityid >= 12 AND
        k.logactivityid != 14 AND
        p.partnerid = k.partnerid AND
        k.$fondid_add AND
        k.dat_realizacia >= '$fromdate'::DATE AND
        k.dat_realizacia <= '$todate'::DATE

    UNION ALL

    SELECT
        k.datum_cas_obchodu AS datum_cas,
        p.nazovpartnera AS miesto,
        (SELECT typ_pokynu FROM typy_pokynov tp WHERE tp.typid = k.typ_pokynu) AS typPokynu,
        k.kurz,
        m.pocet AS sumakredit,
        pdr.transsumareal AS sumadebet,
        k.menakredit,
        k.menadebet,
        CASE WHEN k.typ_konverzie = 0 THEN 'Spot' WHEN k.typ_konverzie = 2 THEN 'Forward' END AS druhObchodu,
        TO_CHAR(k.dat_vysporiadaniecukredit, 'DD.MM.YYYY') AS datum
    FROM
        konverzia k,
        konverziaobratid ko,
        partner p,
        pool po,
        pooldetailreal pdr,
        majetokarchiv m
    WHERE
        k.logactivityid >= 12 AND
        k.logactivityid != 14 AND
        p.partnerid = k.partnerid AND
        k.subjektid = 0 AND
        pdr.$fondid_add AND
        po.poolid = pdr.poolid AND
        k.dealid = po.dealid AND
        ko.dealid = k.dealid AND
        m.subjektid = pdr.subjektid AND 
        m.obratid = ko.obratid AND 
        uctovnykod IN (315160, 315161) AND 
        m.mena = k.menakredit AND 
        m.destinacia = 'konverziaobratid' AND
        k.dat_realizacia >= '$fromdate'::DATE AND
        k.dat_realizacia <= '$todate'::DATE
) result
ORDER BY datum;	
";
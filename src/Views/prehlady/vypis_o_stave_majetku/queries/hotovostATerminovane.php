<?php
$hotovostATerminovaneQuery = "
		SELECT 
			sum( 
				(sumadenom*sign(0.5-md_d) 
				  + COALESCE(
							(
								select sum(sumadenom*sign(0.5-md_d)) 
								from majetoktotal 
								where datum=mt.datum  and uctovnykod in (315132,315113) and eqid = mt.eqid and subjektid = mt.subjektid
								and kodaktiva = mt.kodaktiva and ucetaktiva = mt.ucetaktiva
							),0
						)
				) * (SELECT kurz FROM f_menovy_kurz_lot(menadenom,'$refmena',datum)) 
			) as suma 
		FROM majetoktotal mt
            LEFT JOIN dbequitycurrric dcr ON dcr.isincurrric = mt.kodaktiva
            LEFT JOIN dbequitycurr dc ON dc.isincurr = dcr.isincurr
            LEFT JOIN dbequity d ON d.isin = dc.isin
        WHERE 
            datum = to_date('$dbdate', 'YYYY-MM-DD') 
            AND uctovnykod IN (221110, 221210) 
            AND mt.eqid IN ('BU', 'TD') 
            AND subjektid = $fondid;
	";
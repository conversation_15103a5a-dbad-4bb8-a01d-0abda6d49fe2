<?php
$pomerNominalu = "(CASE WHEN kcp.eqid='Bonds' THEN (select faktor from floatkupon where isincurrric = maj.kodaktiva and (maj.obratdatatimezauctovane - e.exfrekist)  >= datefrom and (maj.obratdatatimezauctovane - e.exfrekist) <= datetill  ) ELSE 1 END)";
$pohybyNaUcteQuery = "
		select * 
		from (
			select	CASE
    WHEN kcp.dealid IN (46663904, 47124904) THEN 'Predčasné splatenie dlhopisu na výzvu emitenta'
    ELSE
        CASE
            WHEN kcp.druhobchodu = 'nakup' THEN 'Nákup'
            WHEN kcp.druhobchodu = 'predaj' THEN 'Predaj'
            WHEN kcp.druhobchodu = 'vklad' THEN 'Vklad'
            WHEN kcp.druhobchodu = 'vyber' THEN 'Výber'
            WHEN kcp.druhobchodu = 'vklad-pr' THEN 'Prevod z port.<br>č.' || (
                SELECT cislozmluvy
                FROM portfolio f
                WHERE fondid = (
                    SELECT fondid
                    FROM konfirmaciacp k2
                    WHERE k2.dealid = (kcp.dealid) - 1000
                    AND f.fondid = k2.subjektid
                )
            )
            WHEN kcp.druhobchodu = 'vyber-pr' THEN 'Prevod na port.<br>č.' || (
                SELECT cislozmluvy
                FROM portfolio f
                WHERE fondid = (
                    SELECT fondid
                    FROM konfirmaciacp k2
                    WHERE k2.dealid = (kcp.dealid) + 1000
                    AND f.fondid = k2.subjektid
                )
            )
            WHEN kcp.druhobchodu = 'vklad-vy' THEN 'Výmena CP - pripísanie CP'
            WHEN kcp.druhobchodu = 'vyber-vy' THEN 'Výmena CP - odpísanie CP'
        END
END AS popis,
					rcp.datvysporiadaniamureal,
					e.isin,
					e.isinreal,
					e.eqid,
					edruh.poddruheq as poddruheq,
					e.cpnaz,e.cpnazskratka,
					rcp.kusovreal as pocet,
					kcp.currencyidtrade,
					rcp.kurzreal as kurz,
					rcp.auvreal as auv,
					rcp.transsuma as spolu,
					round((rcp.kurzreal * rcp.kusovreal * coalesce(e.nominalemisie, 1)  * $pomerNominalu / 
					        (CASE WHEN kcp.eqid='Bonds' THEN 100 ELSE 1 END)),2) as bezAUV,
					maj.obratdatatimezauctovane as datum,
					currencyidtrade as mena,					
					case when kcp.dealid in (46663904,47124904) then '--'
						else p.nazovpartnera end as miesto,
					kcp.datum_cas_obchodu as datum_cas,					
					case when kcp.dealid in (46663904,47124904) then '--'
						else (select typ_pokynu from typy_pokynov tp where tp.typid = kcp.typ_pokynu) end as typPokynu,
					CASE
                        WHEN kcp.druhobchodu = 'nakup' THEN 0
                        WHEN kcp.druhobchodu = 'predaj' THEN 1
                        WHEN kcp.druhobchodu = 'vklad' THEN 0
                        WHEN kcp.druhobchodu = 'vyber' THEN 1
                        WHEN kcp.druhobchodu = 'vklad-pr' THEN 0
                        WHEN kcp.druhobchodu = 'vyber-pr' THEN 1
                        ELSE NULL
                    END AS MD_D
			from	konfirmaciacp kcp,
					rekonfirmaciacp rcp,
					rekonfirmaciacpobratid rcpo,
					dbequity e,
					equityid eid,
					equitydruh edruh,
					(select obratid, obratdatatimezauctovane, kodaktiva
					 from 	majetoktoday
					 where	obratid > 0
							and uctovnykod in (251110,251200,251300)
							and obratdatatimezauctovane >= to_date('$fromdate','YYYY-MM-DD')
							and obratdatatimezauctovane <= to_date('$todate','YYYY-MM-DD')
							and destinacia not like 'muprevod'
					 union all
					 select obratid, obratdatatimezauctovane, kodaktiva
					 from majetokarchiv
					 where	obratid > 0
							and uctovnykod in (251110,251200,251300)
							and obratdatatimezauctovane >= to_date('$fromdate','YYYY-MM-DD')
							and obratdatatimezauctovane <= to_date('$todate','YYYY-MM-DD')
							and destinacia not like 'muprevod'
					) maj,
					partner p
			where	kcp.dealid = rcp.dealid
					and $fondid_add
					and e.isin = kcp.isin
					and e.eqid = eid.eqid
					and rcpo.dealid=rcp.dealid
					and rcpo.tranza=rcp.tranza
					and maj.obratid=rcpo.obratid
					and kcp.partnerid = p.partnerid
					and edruh.druheqid = e.druheqid
						
			union all
				
			select	CASE
    WHEN kcp.dealid = 46663904 THEN 'Predčasné splatenie dlhopisu na výzvu emitenta'
                    ELSE
                        CASE kcp.druhobchodu
                            WHEN 'nakup' THEN 'Nákup'
                            WHEN 'predaj' THEN 'Predaj'
                            WHEN 'vklad' THEN 'Vklad'
                            WHEN 'vyber' THEN 'Výber'
                            WHEN 'vklad-vy' THEN 'Výmena CP - pripísanie CP'
                            WHEN 'vyber-vy' THEN 'Výmena CP - odpísanie CP'
                        END
                END AS popis,
                rcp.datvysporiadaniamureal,
                e.isin,
                e.isinreal,
                e.eqid,
                edruh.poddruheq AS poddruheq,
                e.cpnaz,
                e.cpnazskratka,
                pdr.ksreal AS pocet,
                kcp.currencyidtrade,
                rcp.kurzreal AS kurz,
                pdr.auvreal AS auv,
                pdr.transsumareal AS spolu,
                ROUND((rcp.kurzreal * pdr.ksreal * coalesce(e.nominalemisie, 1) * $pomerNominalu /
                    (CASE WHEN kcp.eqid='Bonds' THEN 100 ELSE 1 END)), 2) AS bezAUV,
                maj.obratdatatimezauctovane AS datum,
                currencyidtrade AS mena,
                CASE
                    WHEN kcp.dealid = 46663904 THEN '--'
                    ELSE p.nazovpartnera
                END AS miesto,
                kcp.datum_cas_obchodu AS datum_cas,
                CASE
                    WHEN kcp.dealid = 46663904 THEN '--'
                    ELSE (SELECT typ_pokynu FROM typy_pokynov tp WHERE tp.typid = kcp.typ_pokynu)
                END AS typPokynu,
                CASE kcp.druhobchodu
                    WHEN 'nakup' THEN 0
                    WHEN 'predaj' THEN 1
                    WHEN 'vklad' THEN 0
                    WHEN 'vyber' THEN 1
                END AS MD_D
			from	konfirmaciacp kcp,
					rekonfirmaciacp rcp,
					rekonfirmaciacpobratid rcpo,
					dbequity e,
					equityid eid,
					equitydruh edruh,
					(select obratid, obratdatatimezauctovane, subjektid, kodaktiva
					 from 	majetoktoday
					 where	obratid > 0
							and uctovnykod in (251110,251200,251300)
							and obratdatatimezauctovane >= to_date('$fromdate','YYYY-MM-DD')
							and obratdatatimezauctovane <= to_date('$todate','YYYY-MM-DD')
							and destinacia not like 'muprevod'
					 union all
					 select obratid, obratdatatimezauctovane, subjektid, kodaktiva
					 from majetokarchiv
					 where	obratid > 0
							and uctovnykod in (251110,251200,251300)
							and obratdatatimezauctovane >= to_date('$fromdate','YYYY-MM-DD')
							and obratdatatimezauctovane <= to_date('$todate','YYYY-MM-DD')
							and destinacia not like 'muprevod'
					) maj,
					pool po,
					pooldetailreal pdr,
					partner p
			where	kcp.dealid = rcp.dealid
					and kcp.subjektid =0
					and e.eqid = eid.eqid
					and pdr.poolid = po.poolid 
					and pdr.subjektid = maj.subjektid
					and pdr.$fondid_add
					and kcp.dealid = po.dealid
					and rcp.tranza = pdr.tranza
					and e.isin = kcp.isin
					and rcpo.dealid=rcp.dealid
					and rcpo.tranza=rcp.tranza
					and maj.obratid=rcpo.obratid
					and kcp.partnerid = p.partnerid
					and edruh.druheqid = e.druheqid
													
			union all						
			
			select	'Splatenie' as popis,
					s.datumvyplaty as datvysporiadaniamureal,
					e.isin,
					e.isinreal,
					e.eqid,
					edruh.poddruheq as poddruheq,
					e.cpnaz,e.cpnazskratka,
					s.pocet,
					s.mena as currencyidtrade,
					/*100 as kurz,*/
					s.suma*100/(e.nominalemisie*s.pocet) as kurz, 
					0 as auv,
					s.suma as spolu,
					s.suma as bezAUV,
					s.datum as datum,
					s.mena as mena,
					null as miesto,
					'Aktivita emitenta' as datum_cas,
					null as typPokynu,
					1 as MD_D
			from
					splatenie s,
					dbequity e,
					dbequitycurr c,
					dbequitycurrric r,
					equitydruh edruh
			where
					s.kodaktiva = r.isincurrric
					and r.isincurr = c.isincurr
					and c.isin = e.isin
					and e.druheqid = edruh.druheqid
					and s.uctovnykod = 251110
					and s.datum >= to_date('$fromdate','YYYY-MM-DD')
					and s.datum <= to_date('$todate','YYYY-MM-DD')
					and s.$fondid_add
					and s.datum_naroku = (select max(datesplatnost) from floatkupon where s.kodaktiva = isincurrric and istina >= 100)
					
			union all

			select	'Čiastočné splatenie priebežné' as popis,
					s.datumvyplaty as datvysporiadaniamureal,
					e.isin,
					e.isinreal,
					e.eqid,
					edruh.poddruheq as poddruheq,
					e.cpnaz,e.cpnazskratka,
					s.pocet,
					s.mena as currencyidtrade,
					100 as kurz,
					0 as auv,
					s.suma as spolu,
					s.suma as bezAUV,
					s.datum as datum,
					s.mena as mena,
					null as miesto,
					'Aktivita emitenta' as datum_cas,
					null as typPokynu,
					1 as MD_D
			from
					splatenie s,
					dbequity e,
					dbequitycurr c,
					dbequitycurrric r,
					equitydruh edruh
			where
					s.kodaktiva = r.isincurrric
					and r.isincurr = c.isincurr
					and c.isin = e.isin
					and e.druheqid = edruh.druheqid
					and s.uctovnykod = 251110
					and s.datum >= to_date('$fromdate','YYYY-MM-DD')
					and s.datum <= to_date('$todate','YYYY-MM-DD')
					and s.$fondid_add
					and s.datum_naroku < (select max(datesplatnost) from floatkupon where s.kodaktiva = isincurrric and istina < 100)
					
			union all

			select	'Čiastočné splatenie konečné' as popis,
					s.datumvyplaty as datvysporiadaniamureal,
					e.isin,
					e.isinreal,
					e.eqid,
					edruh.poddruheq as poddruheq,
					e.cpnaz,e.cpnazskratka,
					s.pocet,
					s.mena as currencyidtrade,
					100 as kurz,
					0 as auv,
					s.suma as spolu,
					s.suma as bezAUV,
					s.datum as datum,
					s.mena as mena,
					null as miesto,
					'Aktivita emitenta' as datum_cas,
					null as typPokynu,
					1 as MD_D
			from
					splatenie s,
					dbequity e,
					dbequitycurr c,
					dbequitycurrric r,
					equitydruh edruh
			where
					s.kodaktiva = r.isincurrric
					and r.isincurr = c.isincurr
					and c.isin = e.isin
					and e.druheqid = edruh.druheqid
					and s.uctovnykod = 251110
					and s.datum >= to_date('$fromdate','YYYY-MM-DD')
					and s.datum <= to_date('$todate','YYYY-MM-DD')
					and s.$fondid_add
					and s.datum_naroku = (select max(datesplatnost) from floatkupon where s.kodaktiva = isincurrric and istina < 100)
					
			union all

			select	da.nazov  as popis,
					s.datumvyplaty as datvysporiadaniamureal,
					e.isin,
					e.isinreal,
					e.eqid,
					edruh.poddruheq as poddruheq,
					e.cpnaz,e.cpnazskratka,
					s.pocet,
					s.mena as currencyidtrade,
					null as kurz,
					null as auv,
					null as spolu,
					s.pocet as bezAUV,
					s.datumvyplaty as datum,
					s.mena as mena,
					null as miesto,
					'Aktivita emitenta' as datum_cas,
					null as typPokynu,
					da.MD_D  as MD_D
			from
					splatenieakcia s,
					dividendaakciatyp da,
					dbequity e,
					dbequitycurr c,
					dbequitycurrric r,
					equitydruh edruh
			where
					s.kodaktiva = r.isincurrric
					and r.isincurr = c.isincurr
					and c.isin = e.isin
					and e.druheqid = edruh.druheqid
					and s.datumvyplaty >= to_date('$fromdate','YYYY-MM-DD')
					and s.datumvyplaty <= to_date('$todate','YYYY-MM-DD')
					and s.$fondid_add
					and s.subkodobratu in (select subkodobratu from dividendaakciatyp where hotovost = 0)
					and da.subkodobratu = s.subkodobratu
		) as kr
		order by datum";
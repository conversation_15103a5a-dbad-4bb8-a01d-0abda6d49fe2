<?php
$nakladyNaDaneQuery = "
	select
			1 as typ,
		  	COALESCE(sum(d.dan*(SELECT kurz FROM f_menovy_kurz_lot(d.mena,'$refmena',s.datumvyplaty))),0)  as suma
		FROM
			danvynosy d,
			splatenieakcia s
		where
			 d.destinacia = 'splatenieakcia' and
			 d.dealid = s.dealid and
			 s.subjektid = $fondid and
			 s.dealid = d.dealid and
			 s.subkodobratu <=9 and
			 s.datumvyplaty >= to_date('$fromdate','YYYY-MM-DD') and
			 s.datumvyplaty <= to_date('$todate','YYYY-MM-DD') and
			 s.fiktivne = 0	
		
		union all
		
		select
			2 as typ,
			COALESCE(sum(d.dan*(SELECT kurz FROM f_menovy_kurz_lot(s.mena,'$refmena',s.datumvyplaty))),0) as suma 
		FROM
		    splatenie s,
			danvynosy d
		where
			s.subjektid = $fondid and 
		    s.datumvyplaty >= to_date('$fromdate','YYYY-MM-DD') and
			s.datumvyplaty <= to_date('$todate','YYYY-MM-DD') and
			s.uctovnykod = (251120) and
			d.destinacia = 'splatenie' and
			d.dealid = s.dealid and
			s.fiktivne = 0			 
		
		union all
		
		select
		  	3 as typ,
		  	COALESCE(sum(round(suma_dane*(SELECT kurz FROM f_menovy_kurz_lot(k.mena,'$refmena',m.OBRATDATATIMEZAUCTOVANE)),2)),0) as suma
		FROM
			konfirmaciaktv k,
			ktvexportobratid ke,
			majetokarchiv m
		where
			k.subjektid = $fondid and
			k.logactivityid in (23,24,25) and
			ke.dealid = k.dealid and
			ke.status = 2 and
			m.obratid = ke.obratid and
			m.OBRATDATATIMEZAUCTOVANE between to_date('$fromdate','YYYY-MM-DD') and to_date('$todate','YYYY-MM-DD') and
			m.uctovnykod = 315113 and
			m.kodobratu = 115		
			
		union all
		
		select
			4 as typ,
			sum(round(COALESCE(pr.dan*(SELECT kurz FROM f_menovy_kurz_lot(k.mena,'$refmena',m.OBRATDATATIMEZAUCTOVANE)),0),2)) as suma
		FROM
			konfirmaciaktv k,
			ktvexportobratid ke,
			majetokarchiv m,
			pool p,
			pooldetailreal pr
		where
			k.subjektid = 0 and
			k.logactivityid in (23,24,25) and
			ke.dealid = k.dealid and
			ke.status = 2 and
			m.obratid = ke.obratid and
			m.OBRATDATATIMEZAUCTOVANE between to_date('$fromdate','YYYY-MM-DD') and to_date('$todate','YYYY-MM-DD') and
			m.uctovnykod = 315113 and
			m.kodobratu = 115	and
			p.dealid = k.dealid and
			pr.poolid = p.poolid and
			pr.subjektid = $fondid and
			m.subjektid = pr.subjektid
			
		union all
		
		select
			5 as typ,
			sum((suma-(COALESCE(p.dan,0)))*(SELECT kurz FROM f_menovy_kurz_lot(p.mena,'$refmena',p.datum))) as suma
		FROM
			poplatok_register p
		where
			p.fondid = $fondid and
			p.datum >= to_date('$fromdate','YYYY-MM-DD') and
			p.datum <= to_date('$todate','YYYY-MM-DD') and
			p.stav in (0,1,2,3,4) and
			p.typ in ('MANAZ','SPRAVA')
			
		union all
		
		select
			6 as typ,
			sum((suma-(COALESCE(p.dan,0)))*(SELECT kurz FROM f_menovy_kurz_lot(p.mena,'$refmena',p.datum))) as suma
		FROM
			poplatok_register p
		where
			p.fondid = $fondid and
			p.datum >= to_date('$fromdate','YYYY-MM-DD') and
			p.datum <= to_date('$todate','YYYY-MM-DD') and
			p.stav in (0,2,3,4) and
			p.typ in ('VYROV','TRAN')
			
		union all
		
		select
			7 as typ,
			sum((suma-(COALESCE(p.dan,0)))*(SELECT kurz FROM f_menovy_kurz_lot(p.mena,'$refmena',p.datum))) as suma
		FROM
			poplatok_register p
		where
			p.fondid = $fondid and
			p.datum >= to_date('$fromdate','YYYY-MM-DD') and
			p.datum <= to_date('$todate','YYYY-MM-DD') and
			p.stav in (0,1,2,3,4) and
			p.typ in ('OSTATNE')
		
		union all
		
		select
			8 as typ,
			sum(p.dan*(SELECT kurz FROM f_menovy_kurz_lot(p.mena,'$refmena',p.datum))) as suma
		FROM
			poplatok_register p
		where
			p.fondid = $fondid and
			p.datum >= to_date('$fromdate','YYYY-MM-DD') and
			p.datum <= to_date('$todate','YYYY-MM-DD') and
			p.stav in (0,1,2,3,4) 
		
		union all
		
		select
			9 as typ, 
			COALESCE(sum(pocet*(SELECT kurz FROM f_menovy_kurz_lot(m.mena,'$refmena',m.OBRATDATATIMEZAUCTOVANE))),0) as suma 
		FROM
			majetokarchiv m
		where
		 	m.OBRATDATATIMEZAUCTOVANE between to_date('$fromdate','YYYY-MM-DD') and to_date('$todate','YYYY-MM-DD') and
		 	m.subjektid = $fondid and
		 	m.uctovnykod = 668000 and
		 	m.md_d = 1 and
		  	not exists
		  	(
		  		select 
					1
		  		FROM
		  			konfirmaciapp k,
		  			obratybu o,
		  			obratybuobratid oo
		  		where
		  			k.subjektid = $fondid and
		  			k.logactivityid = 12 and
		  			o.subjektid = k.subjektid and
		  			o.ss = to_char(k.dealid, '*') and
		  			o.suma = k.suma and
		  			o.mena = k.mena and
		  			o.cub = k.ucet and
		  			oo.id = o.id and
		  			oo.obratid = m.obratid
                  		
				union all
                  			
				select 
					1
		  		FROM
					majetokarchiv ma
				where
					ma.OBRATDATATIMEZAUCTOVANE = m.OBRATDATATIMEZAUCTOVANE and
					ma.subjektid = $fondid and
					ma.uctovnykod = 668000 and
					ma.md_d = 0 and
					ma.KODOBRATU = 214
		 	)		

		union all
		
		select
			10 as typ,
			COALESCE(sum(pocet*(SELECT kurz FROM f_menovy_kurz_lot(m.mena,'$refmena',m.OBRATDATATIMEZAUCTOVANE))),0) as suma 
		FROM
			majetokarchiv m
		where
		 	m.OBRATDATATIMEZAUCTOVANE between to_date('$fromdate','YYYY-MM-DD') and to_date('$todate','YYYY-MM-DD') and
		 	m.subjektid = $fondid and
		 	m.uctovnykod = 560000 and
		 	m.md_d = 0
		
		union all
		
		select
			11 as typ,
			COALESCE(sum(s.suma*(SELECT kurz FROM f_menovy_kurz_lot(s.mena,'$refmena',s.datumvyplaty))),0) as suma
		FROM
		    splatenie s		    	
		where
			s.subjektid = $fondid and 
		  	s.datumvyplaty >= to_date('$fromdate','YYYY-MM-DD') and
			s.datumvyplaty <= to_date('$todate','YYYY-MM-DD') and
			s.uctovnykod = (251110) and
			s.fiktivne = 1			

		union all
		
		select
			12 as typ,
			COALESCE(sum(d.danzaklad*(SELECT kurz FROM f_menovy_kurz_lot(s.mena,'$refmena',s.datumvyplaty))),0) as suma
		FROM
			splatenie s,
			danvynosy d
		where
			s.subjektid = $fondid and 
		  	s.datumvyplaty >= to_date('$fromdate','YYYY-MM-DD') and
			s.datumvyplaty <= to_date('$todate','YYYY-MM-DD') and
			s.uctovnykod = (251120) and
			s.fiktivne = 1 and
			d.dealid = s.dealid and
			d.destinacia='splatenie'

		union all
		
		select
			13 as typ,
			COALESCE(sum(d.danzaklad*(SELECT kurz FROM f_menovy_kurz_lot(sa.mena,'$refmena',sa.datumvyplaty))),0) as suma 
		FROM
			splatenieakcia sa,
			danvynosy d
		where
			sa.subjektid = $fondid and
			sa.datumvyplaty >= to_date('$fromdate','YYYY-MM-DD') and
			sa.datumvyplaty <= to_date('$todate','YYYY-MM-DD') and
			sa.subkodobratu <=9 and
			sa.fiktivne = 1 and
			d.dealid = sa.dealid and
			d.destinacia = 'splatenieakcia'			

		union all
		
		select
			14 as typ,
		  	COALESCE(sum(d.dan*(SELECT kurz FROM f_menovy_kurz_lot(d.mena,'$refmena',s.datumvyplaty))),0) as suma
		FROM
			danvynosy d,
			splatenieakcia s
		where
			 d.destinacia = 'splatenieakcia' and
			 d.dealid = s.dealid and
			 s.subjektid = $fondid and
			 s.dealid = d.dealid and
			 s.subkodobratu <=9 and
			 s.datumvyplaty >= to_date('$fromdate','YYYY-MM-DD') and
			 s.datumvyplaty <= to_date('$todate','YYYY-MM-DD') and
			 s.fiktivne = 1		
		
		union all
		
		select
			15 as typ,
			COALESCE(sum(d.dan*(SELECT kurz FROM f_menovy_kurz_lot(s.mena,'$refmena',s.datumvyplaty))),0) as suma 
		FROM
		    splatenie s,
			danvynosy d
		where
			s.subjektid = $fondid and 
		    s.datumvyplaty >= to_date('$fromdate','YYYY-MM-DD') and
			s.datumvyplaty <= to_date('$todate','YYYY-MM-DD') and
			s.uctovnykod = (251120) and
			d.destinacia = 'splatenie' and
			d.dealid = s.dealid and
			s.fiktivne = 1	

		union all

		select
		  	16 as typ,
		  	COALESCE(sum(round(rcp.poplatok*(SELECT kurz FROM f_menovy_kurz_lot(kcp.currencyidtrade,'$refmena',rcp.datvysporiadaniabureal)),2)),0) as suma
		FROM
			konfirmaciacp kcp,
			rekonfirmaciacp rcp
		where
			kcp.dealid = rcp.dealid and
			kcp.subjektid = $fondid and
			kcp.logactivityid = 12 and
			rcp.datvysporiadaniabureal between to_date('$fromdate','YYYY-MM-DD') and to_date('$todate','YYYY-MM-DD')
						
		union all
		
		select
			17 as typ,
			sum(round(COALESCE(pr.poplatok*(SELECT kurz FROM f_menovy_kurz_lot(kcp.currencyidtrade,'$refmena',rcp.datvysporiadaniabureal)),0),2)) as suma
		FROM
			konfirmaciacp kcp,
			rekonfirmaciacp rcp,
			pool p,
			pooldetailreal pr
		where
			kcp.dealid = rcp.dealid and
			kcp.logactivityid = 12 and
			rcp.datvysporiadaniabureal between to_date('$fromdate','YYYY-MM-DD') and to_date('$todate','YYYY-MM-DD') and
			kcp.subjektid = 0 and
			p.dealid = kcp.dealid and
			pr.poolid = p.poolid and
			pr.subjektid = $fondid			
	";
<?php
$danQuery = "
    WITH combined_data AS (SELECT to_char(s.datumvyplaty, 'dd.mm.yyyy') AS datum_vynosu,
                              s.datumvyplaty                        AS datum,
                              p.titul<PERSON>,
                              p.titulpred,
                              p.meno,
                              p.prie<PERSON>z,
                              p.address,
                              p.city,
                              p.rcico,
                              e.isinreal                            AS isin,
                              CASE
                                  WHEN e.eqid = 'Bonds' THEN 'Dlhopis'
                                  WHEN e.eqid = 'Shares' THEN 'Akcia'
                                  WHEN e.eqid = 'Fonds' THEN 'Fond'
                                  ELSE '' END                       AS asset_type,
                              ed.poddruheq                          AS druhcp,
                              s.pocet                               AS kusov,
                              dv.vynoskus                           AS vynos_kus,
                              to_char(dv.vynoskus, '999G999D99')    AS kuskus,
                              dv.danzaklad,
                              dv.dan,
                              dv.dansadzba,
                              dv.mena,
                              s.kodaktiva                           AS kodaktiva,
                              s.suma                                AS ssuma,
                              e.cpnaz,
                              ee.emitent<PERSON><PERSON>v,
                              st.stateall                           AS krajina_zdroj,
                              to_char(t.datum, 'dd.mm.yyyy')        AS today_datum,
                              po.cislozmluvy                        AS posa,
                              p.fpo
                       FROM splatenie s
                                INNER JOIN danvynosy dv ON s.dealid = dv.dealid
                                INNER JOIN dbequitycurrric drc ON s.kodaktiva = drc.isincurrric
                                INNER JOIN dbequitycurr dc ON drc.isincurr = dc.isincurr
                                INNER JOIN dbequity e ON dc.isin = e.isin
                                INNER JOIN equitydruh ed ON ed.druheqid = e.druheqid
                                INNER JOIN equityemitent ee ON ee.emitentid = e.emitentid
                                INNER JOIN portfolio po ON po.fondid = s.subjektid AND po.fondid = $fondid
                                INNER JOIN podielnik p ON p.podielnikid = po.podielnikid
                                INNER JOIN state st ON st.stateid = ee.emitentstateid
                                INNER JOIN today t ON t.fondid = po.fondid
                       WHERE s.datumvyplaty BETWEEN to_date('$fromdate', 'yyyy-mm-dd') AND to_date('$todate', 'yyyy-mm-dd')
                         AND s.fiktivne = 0
                       UNION ALL
                       SELECT to_char(s.datumvyplaty, 'dd.mm.yyyy') AS datum_vynosu,
                              s.datumvyplaty                        AS datum,
                              p.titulza,
                              p.titulpred,
                              p.meno,
                              p.prieznaz,
                              p.address,
                              p.city,
                              p.rcico,
                              e.isinreal                            AS isin,
                              CASE
                                  WHEN e.eqid = 'Bonds' THEN 'Dlhopis'
                                  WHEN e.eqid = 'Shares' THEN 'Akcia'
                                  WHEN e.eqid = 'Fonds' THEN 'Fond'
                                  ELSE '' END                       AS asset_type,
                              ed.poddruheq                          AS druhcp,
                              s.pocet                               AS kusov,
                              dv.vynoskus                           AS vynos_kus,
                              to_char(dv.vynoskus, '999G999D99')    AS kuskus,
                              dv.danzaklad,
                              dv.dan,
                              dv.dansadzba,
                              dv.mena,
                              s.kodaktiva                           AS kodaktiva,
                              s.suma                                AS ssuma,
                              e.cpnaz,
                              ee.emitentnazov,
                              st.stateall                           AS krajina_zdroj,
                              to_char(t.datum, 'dd.mm.yyyy')        AS today_datum,
                              po.cislozmluvy                        AS posa,
                              p.fpo
                       FROM splatenieakcia s
                                INNER JOIN danvynosy dv ON s.dealid = dv.dealid
                                INNER JOIN dbequitycurrric drc ON s.kodaktiva = drc.isincurrric
                                INNER JOIN dbequitycurr dc ON drc.isincurr = dc.isincurr
                                INNER JOIN dbequity e ON dc.isin = e.isin
                                INNER JOIN equitydruh ed ON ed.druheqid = e.druheqid
                                INNER JOIN equityemitent ee ON ee.emitentid = e.emitentid
                                INNER JOIN portfolio po ON po.fondid = s.subjektid AND po.fondid = $fondid
                                INNER JOIN podielnik p ON p.podielnikid = po.podielnikid
                                INNER JOIN state st ON st.stateid = ee.emitentstateid
                                INNER JOIN today t ON t.fondid = po.fondid
                       WHERE s.datumvyplaty BETWEEN to_date('$fromdate', 'yyyy-mm-dd') AND to_date('$todate', 'yyyy-mm-dd')
                         AND s.fiktivne = 0
                         AND s.subkodobratu < 3)
SELECT *
FROM combined_data
ORDER BY posa, datum;
";
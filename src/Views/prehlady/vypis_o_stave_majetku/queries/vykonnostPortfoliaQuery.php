<?php
$query = "select c.datum,c.mena,c.suma,1 as kumzhod,round((100*(power(exp(c.lnzhod),c.rok/c.obdobie)-1))::numeric,4) as kumz<PERSON><PERSON><PERSON>,c.priemobjem
				FROM (
					select b.datum,b.mena,b.suma,b.pohyby,round(b.suma/(b.lagsuma+b.pohyby),8) as zhod,
							-- sum(round(ln(b.suma/(b.lagsuma+b.pohyby)),8)) over (order by b.datum) as lnzhod,
					       1 as lnzhod,
							b.obdobie,
							to_date('31.12.'||extract (year FROM to_date('$todate','YYYY-MM-DD')),'dd.mm.yyyy') - to_date('31.12.'||(extract (year FROM to_date('$todate','YYYY-MM-DD'))-1),'dd.mm.yyyy') as rok,
							round(avg(b.suma) over (order by b.datum),2) as priemobjem
					FROM(
						select a.datum,a.suma*coalesce(k.kurz,1) as suma,a.menaref as mena,a.obdobie,COALESCE(v.suma*coalesce(k.kurz,1),0) as pohyby,
								lag(a.suma*coalesce(k.kurz,1),1) over (order by a.datum ) as lagsuma
						FROM (		
							select round(sum(mt.sumaref*((0.5-mt.md_d)/0.5)/COALESCE(ku.kurz,1)),2) as suma,mt.datum,'EUR' as mena,mt.menaref 
									,(max(mt.datum) over (order by mt.datum desc) - min(mt.datum) over (order by mt.datum) + 1) as obdobie
							FROM majetoktotal mt
							left join kurzyaktivarchiv ku on mt.datum=ku.datum and ku.ric='EUR'||mt.menaref
							where mt.subjektid in ($fondid) and mt.datum>=to_date('$fromdate','YYYY-MM-DD') and mt.datum<=to_date('$todate','YYYY-MM-DD')
							group by mt.datum,mt.menaref
						) a
						left join (
							select aa.mena,sum(aa.suma) as suma,aa.datum
							FROM (
								select 	ob.mena as mena,(ob.suma*((0.5-ma.md_d)/0.5)) as suma,ob.obratdatetime as datum
								FROM obratybu ob, obratybuobratid obo, majetokarchiv ma
								where ob.id = obo.id and ma.obratid = obo.obratid and ma.subjektid in ($fondid) and ma.kodobratu = 201
									and ma.uctovnykod = 325300 and ob.logactivityid = 15 and ob.obratdatetime >= to_date('$fromdate','YYYY-MM-DD')
									and ob.obratdatetime <= to_date('$todate','YYYY-MM-DD') and ob.krdb = 1					
								union all
								select  u.mena as mena,(u.suma*((0.5-ma.md_d)/0.5)) as suma,u.datesplatnost as datum
								FROM uhrada u, uhradaobratid ui, majetokarchiv ma
								where u.kodobratu = 303 and u.id = ui.id and ma.obratid = ui.obratid and ma.subjektid in ($fondid)
									and ma.kodobratu = 303 and ma.uctovnykod = 261930 and logactivityid = 15
									and datesplatnost >= to_date('$fromdate','YYYY-MM-DD')
									and datesplatnost <= to_date('$todate','YYYY-MM-DD')
								union all
								select'EUR' as mena, -round(k.suma/COALESCE(ku.kurz,1),2) as suma, k.datum_zauctovania as datum
								FROM konfirmaciapp k
								left join kurzyaktivarchiv ku on k.datum_zauctovania=ku.datum and ku.ric='EUR'||k.mena
								where k.druhobchodu = 'vyber' and k.subjektid in ($fondid) and k.logactivityid = 12
									and k.datum_zauctovania >= to_date('$fromdate','YYYY-MM-DD') and k.datum_zauctovania <= to_date('$todate','YYYY-MM-DD')
								union all	
								select 'EUR' as mena, round(k.suma/COALESCE(ku.kurz,1),2) as suma, k.datum_zauctovania as datum
								FROM konfirmaciapp k
								left join kurzyaktivarchiv ku on k.datum_zauctovania=ku.datum and ku.ric='EUR'||k.mena
								where k.druhobchodu = 'vklad' and k.subjektid in ($fondid) and k.logactivityid = 12
									and k.datum_zauctovania >= to_date('$fromdate','YYYY-MM-DD') and k.datum_zauctovania <= to_date('$todate','YYYY-MM-DD')
								union all	
								select 'EUR' as mena,round(a.suma/COALESCE(ku.kurz,1),2) as suma,a.datum
								FROM (
									select	kcp.currencyidtrade as mena,(rcp.transsuma*(CASE kcp.druhobchodu WHEN 'vklad' THEN 1 WHEN 'vyber' THEN -1 WHEN 'vklad-pr' THEN 1 WHEN 'vyber-pr' THEN -1 END)) as suma,rcp.datvysporiadaniamureal as datum		
									FROM	konfirmaciacp kcp, rekonfirmaciacp rcp, rekonfirmaciacpobratid rcpo,
											(select obratid, obratdatatimezauctovane, kodaktiva
											 FROM 	majetoktoday
											 where	obratid > 0 and uctovnykod in (251110,251200,251300) and obratdatatimezauctovane >= to_date('$fromdate','YYYY-MM-DD')
													and obratdatatimezauctovane <= to_date('$todate','YYYY-MM-DD') and destinacia not like 'muprevod'
											 union all
											 select obratid, obratdatatimezauctovane, kodaktiva
											 FROM majetokarchiv
											 where	obratid > 0 and uctovnykod in (251110,251200,251300) and obratdatatimezauctovane >= to_date('$fromdate','YYYY-MM-DD')
													and obratdatatimezauctovane <= to_date('$todate','YYYY-MM-DD') and destinacia not like 'muprevod'
											) maj		
									where	kcp.dealid = rcp.dealid and subjektid in ($fondid) and rcpo.dealid=rcp.dealid and rcpo.tranza=rcp.tranza
											and maj.obratid=rcpo.obratid and kcp.druhobchodu in('vklad','vyber', 'vklad-pr','vyber-pr')
								) a
								left join kurzyaktivarchiv ku on a.datum=ku.datum and ku.ric='EUR'||a.mena
								union all
								select 'EUR' as mena,round(a.suma/COALESCE(ku.kurz,1),2) as suma,a.datum
								FROM (
									select 	k1.mena as mena,-k1.suma as suma, k1.datum_zauctovania as datum
										FROM konfirmaciapp k1
										where k1.dealid_related is not null and k1.subjektid in ($fondid) and k1.logactivityid = 12 and k1.druhobchodu in ('prevod')
											and k1.datum_zauctovania >= to_date('$fromdate','YYYY-MM-DD') and k1.datum_zauctovania <= to_date('$todate','YYYY-MM-DD')
									union all
									select 	k1.mena as mena,k1.suma as suma, k1.datum_zauctovania as datum
									FROM konfirmaciapp k1
									where k1.dealid_related is null and k1.subjektid in ($fondid) and k1.logactivityid = 12 and k1.druhobchodu in ('prevod')
										and k1.datum_zauctovania >= to_date('$fromdate','YYYY-MM-DD') and k1.datum_zauctovania <= to_date('$todate','YYYY-MM-DD')
								) a
								left join kurzyaktivarchiv ku on a.datum=ku.datum and ku.ric='EUR'||a.mena 			
							) aa
							group by aa.mena,aa.datum
						) v on a.datum=v.datum
						left join kurzyaktivarchiv k on a.datum=k.datum and k.ric='EUR'||a.menaref
					) b
				) c	
				where c.datum=to_date('$todate','YYYY-MM-DD')
		";
<?php
if (isset($pdf)) unset($pdf);
$pdf = new MYPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);

// set document information
$pdf->SetCreator(PDF_CREATOR);
$pdf->Set<PERSON><PERSON><PERSON>("Sympatia Financie, o.c.p., a.s.");
$pdf->SetTitle('Výpis');

// set default header data
$pdf->SetHeaderData(PDF_HEADER_LOGO, PDF_HEADER_LOGO_WIDTH, PDF_HEADER_TITLE, PDF_HEADER_STRING);

// set header and footer fonts
$pdf->setHeaderFont(array('helvetica', '', 8));
$pdf->setFooterFont(array('helvetica', '', 8));

// set margins
$pdf->SetTopMargin(40);
$pdf->SetHeaderMargin(20);
$pdf->SetFooterMargin(20);

// set auto page breaks
//$pdf->SetAutoPageBreak(TRUE, PDF_MARGIN_BOTTOM);
$pdf->SetAutoPageBreak(TRUE, 40);
//$pdf->SetProtection(array('modify', 'copy', 'annot-forms', 'fill-forms', 'extract', 'assemble'), $vypisy_heslo, null, 3);

// set font
$pdf->SetFont('helvetica', '', 10);

// add a page
$pdf->AddPage();
<?php
require_once "/home/<USER>/www/lib/SimpleXLSXGen/SimpleXLSXGen.php";
$suhrneUdaje = [
    ['<PERSON>is', 'Čiastka'],
    ["Počiatočný stav", round(floatval($sumaPocStav), 2)],
    ["Suma vkladov", round(floatval($sumaVklady), 2)],
    ["Suma výberov", round(floatval($sumaVybery), 2)],
    ["Suma prevodov medzi portfóliami", round(floatval($sumaPrevody), 2)],
    ["Konečný stav", round(floatval($sumaKonStav), 2)]
];
$vykonnostPortfolia = [
    ['Popis', 'Výkonnosť'],
    ["Výkonnost portfólia", round(floatval($vykonnost), 4)],
    ["Výkonnost portfólia (p.a.)", round(floatval($vykonnostPA), 4)]
];

$prijatePlatby = [
    ['Popis', 'Referenčná mena ' . $refmena],
    ["Dividendy", floatval($sumaTyp2[2])],
    ["Kupóny", floatval($sumaTyp2[1])],
    ["Úroky", floatval($sumaTyp2[3])],
    ["Ostatné prijaté platby", floatval($sumaTyp2[4])]
];

$daneApoplatky = [
    ['Popis', 'Referenčná mena ' . $refmena],
    ["Poplatky za riadenie a správu", floatval($poplSpravaRiadenie)],
    ["Poplatky za transakcie a ich vysporiadanie", floatval($poplTranVyrov)],
    ["Poplatky tretích strán z obchodovania", floatval($poplTriStran)],
    ["Ostatné poplatky a náklady", floatval($poplOstatne)],
    ["DPH", floatval($DPH)],
    ["Celkové poplatky a náklady", floatval($poplSpravaRiadenie + $poplTranVyrov + $poplTriStran + $poplOstatne + $DPH)],
    ["Kumulatívny vplyv celkových poplatkov a nákladov na priemernú výšku investície", $priemobjem == 0 ? "NA" : floatval(-100 * ($poplSpravaRiadenie + $poplTranVyrov + $poplTriStran + $poplOstatne + $DPH) / $priemobjem) . ' %'],
    ["Zrazená daň z príjmov", floatval($danPrijem)]
];

$strukturaPortfolia = [
    ['Trieda aktív', 'Objem v EUR', 'Podiel v %'],
    ["Peňažné prostriedky", round($suma_p1, 2), round($podiel_p2, 4)],
    ["Dlhopisy", round($suma_d1, 2), round($podiel_d2, 4)],
    ["Akcie, ADR a GDR", round($suma_a1, 2), round($podiel_a2, 4)],
    ["Podielové listy a ETF", round($suma_f1, 2), round($podiel_f2, 4)],
    ["Záväzky a pohľadávky", round($suma_paz1, 2), round($podiel_paz2, 4)],
    ["Trhová hodnota celkom", round($total_suma, 2), 100]
];

$penazneProstriedky = array_merge(
    [
        ['Typ účtu', 'Mena', 'Dátum zriadenia TV', 'Dátum splatnosti TV', 'Objem', 'AUV', 'Úroková sadzba', 'Kurz', 'Objem v EUR', 'Podiel v %']
    ],
    $penazeArray,
    [
        ["Spolu", '', '', '', '', '', '', '', round($suma_p1, 2), round($podiel_p2, 4)]
    ]
);

$dlhopisy = array_merge(
    [
        ['Názov', 'ISIN', 'Počet kusov', 'Aktuálna cena', 'Objem', 'Mena', 'AUV', 'Kurz', 'Objem s AUV v ' . $refmena, 'Podiel v %']
    ],
    $dlhopisyArray,
    [
        ["Spolu", '', '', '', '', '', '', '', round($suma_p1, 2), round($podiel_p2, 4)]
    ]
);

$akcie = array_merge(
    [
        ['Názov', 'ISIN', 'Počet kusov', 'Aktuálna cena', 'Objem', 'Mena', 'Kurz', 'Objem s AUV v ' . $refmena, 'Podiel v %']
    ],
    $akcieArray,
    [
        ["Spolu", '', '', '', '', '', '', round($suma_a1, 2), round($podiel_a2, 4)]
    ]
);

$podielove = array_merge(
    [
        ['Názov', 'ISIN', 'Počet kusov', 'Aktuálna cena', 'Objem', 'Mena', 'Kurz', 'Objem s AUV v ' . $refmena, 'Podiel v %']
    ],
    $podieloveArray,
    [
        ["Spolu", '', '', '', '', '', '', round($suma_f1, 2), round($podiel_f2, 4)]
    ]
);

$pohladavky = array_merge(
    [
        ['Názov', 'Názov CP', 'Objem', 'Mena', 'Počet', 'Kurz', 'Objem v ' . $refmena, 'Podiel v %']
    ],
    $pohladavkyArray,
    [
        ["Spolu", '', '', '', '', '', round($suma_paz1, 2), round($podiel_paz2, 4)]
    ]
);

$ockavane = array_merge(
    [
        ['Dátum', 'Investičný nástroj', 'Mena', 'Istina', 'Kupón', 'Daň']
    ],
    $ocakavaneArray
);

$vypisBeznyUcet = array_merge(
    [[
        'Dátum', 'Transakcia', 'Čiastka'
    ]],
    $transakcieArray
);

$terminaky = array_merge(
    [[
        'Uzavretie TV', 'Zriadenie TV', 'Splatnosť TV', 'Miesto výkonu', 'Typ pokynu', 'Suma', 'Sadzba', 'Rok brutto', 'Daň', 'Rok netto'
    ]],
    $terminovaneArray
);

$konverzie = array_merge(
    [["Typ konverzie", "Uzavretie konverzie", "Vyrovnanie konverzie", "Menový pár", "Kurz", "Objem nakupovanej meny", "Objem predávanej meny"]],
    $konverzieArray
);

$xlsx = Shuchkin\SimpleXLSXGen::fromArray($suhrneUdaje, 'Súhrné údaje');
$xlsx->addSheet($vykonnostPortfolia, "Výkonnosť portfólia")->addSheet($prijatePlatby, "Prijaté platby")->addSheet($daneApoplatky, "Dane a poplatky");

// Výpis o stave majetku
$xlsx->addSheet($strukturaPortfolia, "Štruktúra portfólia")->addSheet($penazneProstriedky, "Peňažné prostriedky")->addSheet($dlhopisy, 'Dlhopisy')->addSheet($akcie, "Akcie, ADR a GDR");
$xlsx->addSheet($podielove, "Podielové listy a ETF ")->addSheet($pohladavky, "Záväzky a pohľadávky ")->addSheet($ockavane, "Očakávané finančné toky");
$xlsx->addSheet($vypisBeznyUcet, "Výpis z bežného účtu ($refmena)");
if (!empty($terminaky)) $xlsx->addSheet($terminaky, "Terminované vklady");


$xlsx->setTitle($klient . "_" . $fromdate . "_" . $todate)->download();


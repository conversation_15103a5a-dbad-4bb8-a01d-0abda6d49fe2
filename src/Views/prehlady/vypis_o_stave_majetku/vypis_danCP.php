<?php


$vypis_obsah = '
		<br><br><br>		
		<table style="width:100%;border-collapse:collapse;">
			<tr  style="width:100%;text-align:center;">
				<td style="height:26.25pt;">
					<div style="color: #6B819B;">
						<span style="font-size: 21pt;font-weight:400;">Prehľad potvrdení o&nbsp;zaplatenej dani z&nbsp;výnosov</span>
					</div>	
				</td>
			</tr>
			<tr style="width:100%;">
				<td style="height:20pt;min-width:100%;width:100%;"></td>
			</tr>	
			<tr  style="width:100%;text-align:center;">
				<td style="height:26.25pt;vertical-align:bottom;">
					<div  style="color: #000;vertical-align:bottom;">
						<span style="font-size: 9.25pt;font-weight:600;"><b>za obdobie od ' . $fromdate . ' do ' . $todate . '</b></span>
					</div>	
				</td>
			</tr>
		</table>
		<br><br>
		<table>
			<tr style="font-weight:normal;color: #0E0E0E;">
				<td style="min-width:4%;width:4%;"></td>
				<td style="vertical-align:bottom;width:71%;text-align:left;height:10pt;"><div style="vertical-align:bottom;"><span style="font-weight:400;font-size:7.5pt;">Klient:    </span><span style="font-weight:700;font-size:9.75pt;">' . $klient . '</span></div></td>
				<td style="vertical-align:bottom;width:20%;text-align:right;height:10pt;"><div style="vertical-align:bottom;"><span style="font-weight:400;font-size:7.5pt;">Portfólio:    </span><span style="font-weight:700;font-size:9.75pt;">' . $cislozmluvy . '</span></div></td>
				<td style="min-width:5%;width:5%;"></td>
			</tr>
		</table>
		';


//----------------------suhrnne udaje ----------------------
include "/home/<USER>/www/src/Views/prehlady/vypis_o_stave_majetku/queries/danZCPQuery.php";
$danRes = Connection::getDataFromDatabase($danQuery, defaultDB);
$dane = $danRes[1];

$vypis_obsah = $vypis_obsah . '
		<br><br><br><br>
		<table style="width:100%;border-collapse:collapse;">
			<tr>
				<td style="width:70%;text-align:left;" ><h3 style="color:' . $farbaciara2 . '">Zoznam potvrdení</h3></td>
			</tr>
			<tr style="width:100%;line-height:1pt;">
				<td colspan="2" style="height:1pt;min-width:100%;width:100%;"></td>
			</tr>
		</table>
		<br>
		<table style="width:100%;border-collapse:collapse;border-top:1pt solid ' . $farbaciara3 . ';border-bottom:1pt solid ' . $farbaciara3 . ';">
			<tr style="width:100%;border-collapse:collapse;border-top:1pt solid ' . $farbaciara3 . ';border-bottom:1pt solid ' . $farbaciara3 . ';">
					<th style="width:20%;text-align:left;font-size:8;" ><b>ISIN CP</b></th>
					<th style="width:10%;text-align:right;font-size:8;" ><b>Dátum pripísania výnosov</b></th>
					<th style="width:30%;text-align:right;font-size:8;" ><b>Druh dlhopisu</b></th>
					<th style="width:10%;text-align:left;font-size:8;" ><b>Počet kusov CP</b></th>
					<th style="width:10%;text-align:right;font-size:8;" ><b>Výnos na 1ks CP</b></th>
					<th style="width:10%;text-align:right;font-size:8;" ><b>Sadzba dane</b></th>
					<th style="width:10%;text-align:right;font-size:8;" ><b>Zrazená daň</b></th>
			</tr>
			<tr style="width:100%;line-height:1pt;">
					<td colspan="3" style="border-bottom:0.1mm solid ' . $farbaciara1 . ';height:1pt;min-width:100%;width:100%;"></td>
				</tr>
				<tr style="width:100%;line-height:1pt;">
					<td colspan="3" style="height:1pt;min-width:100%;width:100%;"></td>
				</tr>
	';

foreach ($dane as $item) {
    $vypis_obsah .= '<tr>
					<td style="width:20%;text-align:left;font-size:8;">' . $item["isin"] . '</td>
					<td style="width:10%;text-align:right;font-size:8;">' . $item["datum"] . '</td>
					<td style="width:30%;text-align:right;font-size:8;">' . $item["druhcp"] . '</td>
					<td style="width:10%;text-align:left;font-size:8;">' . round($item["kusov"], 0) . '</td>
					<td style="width:10%;text-align:right;font-size:8;">' . round($item["vynos_kus"], 2) . '</td>
					<td style="width:10%;text-align:right;font-size:8;">' . $item["dansadzba"] . '</td>
					<td style="width:10%;text-align:right;font-size:8;">' . round($item["dan"], 2) . '</td>
				</tr>';
}
$vypis_obsah .= '
    <tr style="width:100%;line-height:1pt;">
					<td colspan="3" style="border-bottom:0.1mm solid ' . $farbaciara1 . ';height:1pt;min-width:100%;width:100%;"></td>
				</tr>
				<tr style="width:100%;line-height:1pt;">
					<td colspan="3" style="height:1pt;min-width:100%;width:100%;"></td>
				</tr>
				<tr>
					<td colspan="3" style="height:10pt;min-width:100%;width:100%;"></td>
				</tr>
';
$vypis_obsah .= '<tr>
					<td style="width:85%;text-align:left;font-size:8;font-weight: bold;">Výnosy spolu - pred zdanením</td>
					<td style="width:15%;text-align:right;font-size:8;font-weight: bold;">' . $item["kuskus"] . '</td>
				</tr>';

$vypis_obsah .= '<tr>
					<td style="width:85%;text-align:left;font-size:8;font-weight: bold;">Výnosy spolu - po zdanení</td>
					<td style="width:15%;text-align:right;font-size:8;font-weight: bold;">' . $item["dan"] * $item["kurz"] . '</td>
				</tr>';

$vypis_obsah .= '</table>';

$vypis_danCP = $vypis_obsah;
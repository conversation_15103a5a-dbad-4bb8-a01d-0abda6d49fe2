<?php

class MYPDF extends TCPDF
{
    //Page header
    public function Header()
    {
        global $hlavicka1, $hlavicka2, $hlavicka3;
        $image_file = '/home/<USER>/www/src/assets/img/vypisyPdf/logo_SF.jpg';
        $this->Image($image_file, 10, 10, 45, '', 'JPG', '', 'T', false, 300, '', false, false, 0, false, false, false);
        $image_file = '/home/<USER>/www/src/assets/img/vypisyPdf/hlavicka4.jpg';
        $this->Image($image_file, 3, 3, 210, '', 'JPG', '', 'T', false, 300, '', false, false, 0, false, false, false);
        $image_file = '/home/<USER>/www/src/assets/img/vypisyPdf/hlavicka4_1.png';
        $this->Image($image_file, 3, 3, 70, '', 'PNG', '', 'T', false, 300, '', false, false, 0, false, false, false);
        $image_file = '/home/<USER>/www/src/assets/img/vypisyPdf/hlavicka4_2.jpg';
        $this->Image($image_file, 91, 3, 122, '', 'JPG', '', 'T', false, 300, '', false, false, 0, false, false, false);
    }

    // Page footer
    public function Footer()
    {
        global $zobrazPatickuPage1;
        $image_file = '/home/<USER>/www/src/assets/img/vypisyPdf/paticka5.png';
        $this->Image($image_file, 3, 264, 210, '', 'PNG', '', 'T', false, 300, '', false, false, 0, false, false, false);
        $this->SetY(-14.3);
        // Set font
        $this->SetFont('robotocondensed', 'I', 8);
        // Page number
        //$this->Cell(0, 10, ($this->PageNo()-$zobrazPatickuPage1), 0, false, 'C', 0, '', 0, false, 'T', 'M');
        $this->Cell(100, 10, '', 0, false, 'R', 0, '', 0, false, 'M', 'M');
        $this->Cell(30, 10, '- ' . $this->getAliasNumPage() . '/' . $this->getAliasNbPages() . ' -', 0, false, 'C', 0, '', 0, false, 'T', 'M');
    }
}

?>

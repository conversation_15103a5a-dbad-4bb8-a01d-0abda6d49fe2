<?php
$image_file = '/home/<USER>/www/src/assets/img/vypisyPdf/raster1.jpg';

if (!isset($client)) {

    $dir = "/home/<USER>/www/src/assets/pdf/vypisy/summary/" . $todate . "/";
    if (!file_exists($dir)) mkdir($dir, 0777, true);

    //generovanie heslovaneho pdf do spolocneho adresara aby sa to z neho dalo lahko naraz kopirovat na mailovu kampan
    $myfileSummary = $dir . "vypis_portfolia_" . $klientdb . "_" . $cislozmluvy . "_od_" . $fromdate . "_do_" . $todate . ".pdf";
    $objectPdf[$klient]['summaries'][] = $myfileSummary;
    $pdf = new MYPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);

// set document information
    $pdf->SetCreator(PDF_CREATOR);
    $pdf->SetAuthor("Sympatia Financie, o.c.p., a.s.");
    $pdf->SetTitle('Výpis');

// set default header data
    $pdf->SetHeaderData(PDF_HEADER_LOGO, PDF_HEADER_LOGO_WIDTH, PDF_HEADER_TITLE, PDF_HEADER_STRING);

// set header and footer fonts
    $pdf->setHeaderFont(array('helvetica', '', 8));
    $pdf->setFooterFont(array('helvetica', '', 8));

// set margins
    $pdf->SetTopMargin(40);
    $pdf->SetHeaderMargin(20);
    $pdf->SetFooterMargin(20);

    $pdf->SetAutoPageBreak(TRUE, 40);
    $pdf->SetProtection(array('modify', 'copy', 'annot-forms', 'fill-forms', 'extract', 'assemble'), $vypisy_heslo, null, 3);

    $pdf->SetFont('helvetica', '', 10);

    $pdf->AddPage();
    $pdf->Image($image_file, 0, 0, "10", "", 'JPG', '', 'T', false, 300, '', false, false, 0, false, false, false);
    $pdf->SetXY(0, 40);
    $pdf->writeHTML($vypis_zhodnotenie, true, false, true, false, '');
    $pdf->AddPage();
    $pdf->setPage($pdf->getPage());
    $pdf->Image($image_file, 10, 75.5, 190, '', 'JPG', '', 'T', false, 300, '', false, false, 0, false, false, false);
    $pdf->SetXY(0, 40);
    $pdf->writeHTML($vypis_portfolia, true, false, true, false, '');
    $pdf->AddPage();
    $pdf->setPage($pdf->getPage());
    $pdf->Image($image_file, 10, 75.5, 190, '', 'JPG', '', 'T', false, 300, '', false, false, 0, false, false, false);
    $pdf->SetXY(0, 40);
    $pdf->writeHTML($vypis_transakcii, true, false, true, false, '');
    $pdf->AddPage();
    $pdf->setPage($pdf->getPage());
    $pdf->Image($image_file, 10, 75.5, 190, '', 'JPG', '', 'T', false, 300, '', false, false, 0, false, false, false);
    $pdf->SetXY(0, 40);
    $pdf->writeHTML($vypis_danCP, true, false, true, false, '');
    ob_clean();
    $pdf->Output($myfileSummary, 'F');
}
include("pdfko_heslovane.php");
$dir = "/home/<USER>/www/src/assets/pdf/vypisy/" . $klientdb . "/Vypisy/";
if (!file_exists($dir)) mkdir($dir, 0777, true);

//generovanie heslovaneho pdf
$myfilePswd = $dir . "vypis_portfolia_" . $klientdb . "_" . $cislozmluvy . "_od_" . $fromdate . "_do_" . $todate . ".pdf";
$objectPdf[$klient]['pdfpswd'][] = $myfilePswd;
$pdf->Image($image_file, 10, 75.5, 190, '', 'JPG', '', 'T', false, 300, '', false, false, 0, false, false, false);
$pdf->SetXY(0, 40);
$pdf->writeHTML($vypis_zhodnotenie, true, false, true, false, '');
$pdf->AddPage();
$pdf->setPage($pdf->getPage());
$pdf->Image($image_file, 10, 75.5, 190, '', 'JPG', '', 'T', false, 300, '', false, false, 0, false, false, false);
$pdf->SetXY(0, 40);
$pdf->writeHTML($vypis_portfolia, true, false, true, false, '');
$pdf->AddPage();
$pdf->setPage($pdf->getPage());
$pdf->Image($image_file, 10, 75.5, 190, '', 'JPG', '', 'T', false, 300, '', false, false, 0, false, false, false);
$pdf->SetXY(0, 40);
$pdf->writeHTML($vypis_transakcii, true, false, true, false, '');
$pdf->AddPage();
$pdf->setPage($pdf->getPage());
$pdf->Image($image_file, 10, 75.5, 190, '', 'JPG', '', 'T', false, 300, '', false, false, 0, false, false, false);
$pdf->SetXY(0, 40);
$pdf->writeHTML($vypis_danCP, true, false, true, false, '');
ob_clean();
$pdf->Output($myfilePswd, 'F');

//generovanie neheslovaneho pdf
$dir = "/home/<USER>/www/temp/pdf/vypisy/" . $klientdb . "/Vypisy/";
if (!file_exists($dir)) mkdir($dir, 0777, true);
$myfile = $dir . "vypis_portfolia_" . $klientdb . "_" . $cislozmluvy . "_od_" . $fromdate . "_do_" . $todate . "_neheslovane.pdf";
$objectPdf[$klient]['pdfnp'][] = $myfile;
require("pdfko_neheslovane.php");


$pdf->SetXY(0, 40);
$pdf->Image($image_file, 10, 75.5, "190", "", 'JPG', '', 'T', false, 300, '', false, false, 0, false, false, false);
$pdf->SetXY(0, 40);
$pdf->writeHTML($vypis_zhodnotenie, true, false, true, false, '');
$pdf->AddPage();
$pdf->setPage($pdf->getPage());
$pdf->Image($image_file, 10, 75.5, 190, '', 'JPG', '', 'T', false, 300, '', false, false, 0, false, false, false);
$pdf->SetXY(0, 40);
$pdf->writeHTML($vypis_portfolia, true, false, true, false, '');
$pdf->AddPage();
$pdf->setPage($pdf->getPage());
$pdf->Image($image_file, 10, 75.5, 190, '', 'JPG', '', 'T', false, 300, '', false, false, 0, false, false, false);
$pdf->SetXY(0, 40);
$pdf->writeHTML($vypis_transakcii, true, false, true, false, '');
$pdf->AddPage();
$pdf->setPage($pdf->getPage());
$pdf->Image($image_file, 10, 75.5, 190, '', 'JPG', '', 'T', false, 300, '', false, false, 0, false, false, false);
$pdf->SetXY(0, 40);
$pdf->writeHTML($vypis_danCP, true, false, true, false, '');
ob_clean();
$pdf->Output($myfile, 'F');
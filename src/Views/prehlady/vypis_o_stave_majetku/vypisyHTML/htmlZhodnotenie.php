<?php
require_once "/home/<USER>/www/src/Components/tables/klientskyVypis/klientReportTable.php";
include_once "/home/<USER>/www/src/Views/prehlady/vypis_o_stave_majetku/queries/suhrneUdajeQuery.php";
include_once "/home/<USER>/www/src/Views/prehlady/vypis_o_stave_majetku/queries/vykonnostPortfoliaQuery.php";
include_once "/home/<USER>/www/src/Views/prehlady/vypis_o_stave_majetku/queries/prijatePlatbyQuery.php";
include_once "/home/<USER>/www/src/Views/prehlady/vypis_o_stave_majetku/queries/nakladyNaDaneQuery.php";
?>
    <section id="sumarizacia<?php echo $klientPodielnikid . $cislozmluvy ?>" class="flex w-full justify-between items-center">
        <div class="p-5 text-xl w-full font-semibold border rounded-lg shadow-sm text-left rtl:text-right bg-gray-700 text-white">
            <div>
                Sumárne informácie o portfóliu
                <p class="mt-1 text-sm font-normal dark:text-gray-400 text-gray-400"><span
                            class="flex items-center gap-2">Sumárne informácie o portfóliu za obdobie od <?php echo $fromdate . ' do ' . $todate ?>
                            </span>
                </p>
            </div>
        </div>
    </section>
<?php
//----------------------SUHRNE UDAJE----------------------
$suhrneUdajeRes = Connection::getDataFromDatabase($suhrneUdajeQuery, defaultDB);
$suhrneUdaje = $suhrneUdajeRes[1];

$sumaTyp = array();
foreach ($suhrneUdaje as $item) {
    $typ = $item["typ"];
    $sumaTyp[$typ] = $item["suma"];
}

$suhrneUdajeTableColumns = [["Popis", "Čiastka"]];
$suhrneUdajeTableData = [
    ["Počiatočný stav", round($sumaTyp[7], 2)],
    ["Suma vkladov", round($sumaTyp[1], 2)],
    ["Suma výberov", number_format(round($sumaTyp[2] + $sumaTyp[9] + $sumaTyp[10] + $sumaTyp[11] + $sumaTyp[12] + $sumaTyp[13], 2), 2, '.', ' ')],
    ["Suma prevodov medzi portfóliami", number_format(round(($sumaTyp[3] + $sumaTyp[4]) - ($sumaTyp[5] + $sumaTyp[6]), 2), 2, '.', ' ')]
];
klientReportTable::render($suhrneUdajeTableData, $suhrneUdajeTableColumns, "Súhrnné údaje", "", "Referenčná mena ($refmena)", ["url" => "/klienti/detail/" . $klientPodielnikid . "#" . $cislozmluvy, "cislozmluvy" => $cislozmluvy], ["klientPodielnikid" => "/klienti/detail/" . $klientPodielnikid, "klient" => $klient, "klientMail" => $klientMail], "");

//----------------------VYKONNOST PORTFOLIA----------------------
/*round(100*(exp(c.lnzhod)-1),4) namiesto as kumzhod*/
// TODO: logaritmus s michalom robiť

$vykonnostPortfoliaRes = Connection::getDataFromDatabase($query, defaultDB);
$vykonnostPortfolia = $vykonnostPortfoliaRes[1][0];
$vykonnostPortfoliaTableData = [$vykonnostPortfolia['kumzhod'], $vykonnostPortfolia['kumzhodpa']];
$vykonnostPortfoliaTableColumns = [["Popis", "Výkonosť"]];

klientReportTable::render($vykonnostPortfoliaTableData, $vykonnostPortfoliaTableColumns, "Výkonnosť portfólia", "", "Výkonnosť portfólia", ["url" => "/klienti/detail/" . $klientPodielnikid . "#" . $cislozmluvy, "cislozmluvy" => $cislozmluvy], ["klientPodielnikid" => "/klienti/detail/" . $klientPodielnikid, "klient" => $klient, "klientMail" => $klientMail], "");

//----------------------PRIJATE PLATBY----------------------

$prijatePlatbyRes = Connection::getDataFromDatabase($prijatePlatbyQuery, defaultDB);
$prijatePlatby = $prijatePlatbyRes[1];
$sumaTyp2 = array();

foreach ($resultQuery as $item) {
    $typ2 = $item["typ"];
    $suma2 = $item["suma"];
    $sumaTyp2[$typ2] += $suma2;
}

$prijatePlatbyTableColumns = [["Popis", "Čiastka"]];
$prijatePlatbyTableData = [
    ["Dividendy", floatval($sumaTyp2[2])],
    ["Kupóny", floatval($sumaTyp2[1])],
    ["Úroky", floatval($sumaTyp2[3])],
    ["Ostatné prijaté platby", floatval($sumaTyp2[4])]
];

klientReportTable::render($prijatePlatbyTableData, $prijatePlatbyTableColumns, "Prijaté platby", $refmena, "Zoznam prijatých platieb", ["url" => "/klienti/detail/" . $klientPodielnikid . "#" . $cislozmluvy, "cislozmluvy" => $cislozmluvy], ["klientPodielnikid" => "/klienti/detail/" . $klientPodielnikid, "klient" => $klient, "klientMail" => $klientMail], "");

//----------------------NAKLADY NA DANE A POPLATKY----------------------
$nakladyNaDaneRes = Connection::getDataFromDatabase($nakladyNaDaneQuery, defaultDB);
$nakladyNaDane = $nakladyNaDaneRes[1];
$sumaTyp = array();

foreach ($resultQuery as $item) {
    $typ = $item["typ"];
    $suma = $item["suma"];
    $sumaTyp[$typ] = $suma;
}

$danPrijem = $sumaTyp[1] + $sumaTyp[2] + $sumaTyp[3] + $sumaTyp[4];
$poplSpravaRiadenie = $sumaTyp[5];
$poplTranVyrov = $sumaTyp[6];
$poplOstatne = $sumaTyp[7] + $sumaTyp[10];
$DPH = $sumaTyp[8];
$poplTriStran = $sumaTyp[16] + $sumaTyp[17];
$priemobjem = $vykonnostPortfolia['priemobjem'];

$nakladyNaDaneColumns = [["Popis", "Čiastka"]];
$nakladyNaDaneTableData = [
    ["Poplatky za riadenie a správu", floatval($poplSpravaRiadenie)],
    ["Poplatky za transakcie a ich vysporiadanie", floatval($poplTranVyrov)],
    ["Poplatky tretích strán z obchodovania", floatval($poplTriStran)],
    ["Ostatné poplatky a náklady", floatval($poplOstatne)],
    ["DPH", floatval($DPH)],
    ["Celkové poplatky a náklady", floatval($poplSpravaRiadenie + $poplTranVyrov + $poplTriStran + $poplOstatne + $DPH)],
    ["Kumulatívny vplyv celkových poplatkov a nákladov na priemernú výšku investície", ($priemobjem == 0 ? "NA" : floatval(-100 * ($poplSpravaRiadenie + $poplTranVyrov + $poplTriStran + $poplOstatne + $DPH) / $priemobjem)) . ' %'],
    ["Zrazená daň z príjmov", floatval($danPrijem)]
];

klientReportTable::render($nakladyNaDaneTableData, $nakladyNaDaneColumns, "Dane a poplatky", "", "", ["url" => "/klienti/detail/" . $klientPodielnikid . "#" . $cislozmluvy, "cislozmluvy" => $cislozmluvy], ["klientPodielnikid" => "/klienti/detail/" . $klientPodielnikid, "klient" => $klient, "klientMail" => $klientMail], "");
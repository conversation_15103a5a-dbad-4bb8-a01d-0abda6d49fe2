<section id="portfolio<?php echo $klientPodielnikid . $cislozmluvy; ?>"
         class="flex w-full justify-between items-center">
    <div class="p-5 text-xl w-full font-semibold border rounded-lg shadow-sm text-left rtl:text-right bg-gray-700 text-white">
        <div>
            Výpis o stave majetku
            <p class="mt-1 text-sm font-normal dark:text-gray-400 text-gray-400"><span
                        class="flex items-center gap-2"><b>ku dňu <?php echo $todate ?></b>
                            </span>
            </p>
        </div>
    </div>
</section>
<?php
require_once "/home/<USER>/www/src/Components/tables/klientskyVypis/klientReportTable.php";
//---------------hotovost a terminovane vklady---------------
include "/home/<USER>/www/src/Views/prehlady/vypis_o_stave_majetku/queries/hotovostATerminovane.php";
$hotovostATerminovaneRes = Connection::getDataFromDatabase($hotovostATerminovaneQuery, defaultDB);
$hotovostATerminovane = $hotovostATerminovaneRes[1][0]["suma"];
$suma_p1 = floatval($hotovostATerminovane);

//---------------dlhopisy spolu---------------
include "/home/<USER>/www/src/Views/prehlady/vypis_o_stave_majetku/queries/dlhopisyQuery.php";
$dlhopisyRes = Connection::getDataFromDatabase($dlhopisyQuery, defaultDB);
$d1 = $dlhopisyRes[1][0]["suma"];
$suma_d1 = floatval($d1);

//---------------akcie------------------------
include "/home/<USER>/www/src/Views/prehlady/vypis_o_stave_majetku/queries/akcieQuery.php";
$akcieRes = Connection::getDataFromDatabase($akcieQuery, defaultDB);
$a1 = $akcieRes[1][0]["suma"];
$suma_a1 = floatval($a1);

//---------------podiely spolu----------------
include "/home/<USER>/www/src/Views/prehlady/vypis_o_stave_majetku/queries/podielyQuery.php";
$podieloveRes = Connection::getDataFromDatabase($podieloveQuery, defaultDB);
$f1 = $podieloveRes[1][0]["suma"];
$suma_f1 = floatval($f1);

//---------------pohladavky a zavazky spolu---------------
include "/home/<USER>/www/src/Views/prehlady/vypis_o_stave_majetku/queries/pohladavkyZavazkyQuery.php";
$connectionRes5 = Connection::getDataFromDatabase($pohladavkyZavazkyQuery, defaultDB);
$paz1 = $connectionRes5[1][0]["suma"];
$suma_paz1 = floatval($paz1);

$total = $p1 + $d1 + $a1 + $f1 + $paz1;
$total_suma = floatval($total);

if ($total == 0) $total = 1;        //tu je mozna chyba, ak bude mat klient rovnaku sumu plus aj minus tak mu to vypocita obravky percentualny podieln na portfoliu

$p2 = ($p1 / $total) * 100;
$podiel_p2 = floatval($p2);
$d2 = ($d1 / $total) * 100;
$podiel_d2 = floatval($d2);
$a2 = ($a1 / $total) * 100;
$podiel_a2 = floatval($a2);
$f2 = ($f1 / $total) * 100;
$podiel_f2 = floatval($f2);
$paz2 = ($paz1 / $total) * 100;
$podiel_paz2 = floatval($paz2);

$strukturaTableColumns = [["Trieda aktív", "Objem v " . $refmena, "Podiel v %"]];
$strukturaTableData = [
    $suma_p1 != 0 ? ["Peňažné prostriedky", round($suma_p1, 2), round($podiel_p2, 4) . "%"] : null,
    $suma_d1 != 0 ? ["Dlhopisy", round($suma_d1, 2), round($podiel_d2, 4) . "%"] : null,
    $suma_a1 != 0 ? ["Akcie, ADR a GDR", round($suma_a1, 2), round($podiel_a2, 4) . "%"] : null,
    $suma_f1 != 0 ? ["Podielové listy a ETF", round($suma_f1, 2), round($podiel_f2, 4) . "%"] : null,
    $suma_paz1 != 0 ? ["Záväzky a pohľadávky", round($suma_paz1, 2), round($podiel_paz2, 4) . "%"] : null,
    ["Trhová hodnota celkom", round($total_suma, 2), "100%"]
];

klientReportTable::render($strukturaTableData, $strukturaTableColumns, "Štruktúra portfólia podľa tried aktív", "", "", ["url" => "/klienti/detail/" . $klientPodielnikid . "#" . $cislozmluvy, "cislozmluvy" => $cislozmluvy], ["klientPodielnikid" => "/klienti/detail/" . $klientPodielnikid, "klient" => $klient, "klientMail" => $klientMail], "");

//---------------hotovost a terminovane vklady detail---------------
$query6 = "
		SELECT 		
			mt.ucetaktiva as ucetaktiva,  subjektid, eqid,  menadenom, sumadenom*sign(0.5-md_d) as sumadenom, 			
			(SELECT kurz FROM f_menovy_kurz_lot(menadenom,'$refmena',to_date('$dbdate','YYYY-MM-DD'))) as kurz, 
			sumadenom*sign(0.5-md_d) * (SELECT kurz FROM f_menovy_kurz_lot(menadenom,'$refmena',to_date('$dbdate','YYYY-MM-DD'))) as sumaref,
			COALESCE(
					(select sumadenom*sign(0.5-md_d) from majetoktotal where 
						subjektid=mt.subjektid
						and datum=mt.datum
						and uctovnykod in (315132,315113)
						and eqid = mt.eqid
						and kodaktiva = mt.kodaktiva
						and ucetaktiva = mt.ucetaktiva
					 ),0
				) as auv, 
			COALESCE(
					(select sumadenom*sign(0.5-md_d) * (SELECT kurz FROM f_menovy_kurz_lot(menadenom,'$refmena',to_date('$dbdate','YYYY-MM-DD'))) from majetoktotal where 
						subjektid=mt.subjektid
						and datum=mt.datum
						and uctovnykod in (315132,315113)
						and eqid = mt.eqid
						and kodaktiva = mt.kodaktiva
						and ucetaktiva = mt.ucetaktiva
					),0
				) as auvref 
		from 
			majetoktotal mt
		where 
			datum=to_date('$dbdate','YYYY-MM-DD') and eqid in ('BU','TD') and uctovnykod in (221110, 221210)
			and subjektid=$fondid
		order by eqid, menadenom
	";

$connectionRes6 = Connection::getDataFromDatabase($query6, defaultDB);
$result6 = $connectionRes6[1];

$penazeArray = [];
$cnt = 0;
foreach ($result6 as $item) {
    if ($cnt == 0) {
        $vypis_obsah = $vypis_obsah . '
<div class="relative mb-10 overflow-x-auto mt-3 shadow-md sm:rounded-lg">
    <table class="w-full text-sm text-left rtl:text-right text-gray-500">
        <caption
                class="p-3 text-xl font-semibold text-left rtl:text-right text-gray-900 bg-white">
            <section class="flex w-full justify-between items-center p-1">
                <div>
                    Peňažné prostriedky
                    <p class="mt-1 text-sm font-normal text-gray-500 dark:text-gray-400"><span
                                class="flex items-center gap-2">Portfólio: <a href="/klienti/detail/' . $klientPodielnikid . '#' . $cislozmluvy . '"
                                          class="bg-gray-500 flex items-center gap-1 hover:underline transition-all text-white py-0.5 px-2 rounded-lg"><span>' . $cislozmluvy . '</span>
                            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24"
                                 height="24" fill="none" viewBox="0 0 24 24">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                      stroke-width="2"
                                      d="M18 14v4.833A1.166 1.166 0 0 1 16.833 20H5.167A1.167 1.167 0 0 1 4 18.833V7.167A1.166 1.166 0 0 1 5.167 6h4.618m4.447-2H20v5.768m-7.889 2.121 7.778-7.778"/>
                            </svg>
                        </a>
                        </span>
                    </p>
                </div>
                <a href="/klienti/detail/' . $klientPodielnikid . '"
                   class="flex gap-3 hover:bg-gray-100 transition-all p-2 rounded-lg cursor-pointer">
                    <div class="relative w-10 h-10 overflow-hidden bg-gray-100 rounded-full dark:bg-gray-600">
                        <svg class="absolute w-12 h-12 text-gray-400 -left-1" fill="currentColor" viewBox="0 0 20 20"
                             xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                                  clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div>
                        <span class="text-sm">' . $klient . '</span>
                        <p class="text-xs text-gray-400">' . $klientMail . '</p>
                    </div>
                </a>
            </section>
        </caption>
        <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
        <tr>
            <th scope="col" class="px-6 py-3">Typ účtu</th>
            <th scope="col" class="px-6 py-3">Mena</th>
            <th scope="col" class="px-6 py-3">Dátum zriadenia TV</th>
            <th scope="col" class="px-6 py-3">Dátum splatnosti TV</th>
            <th scope="col" class="px-6 py-3">Objem</th>
            <th scope="col" class="px-6 py-3">AUV</th>
            <th scope="col" class="px-6 py-3">Úroková sadzba</th>
            <th scope="col" class="px-6 py-3">Kurz</th>
            <th scope="col" class="px-6 py-3">Objem v EUR</th>
            <th scope="col" class="px-6 py-3">Podiel v %</th>
        </tr>
        </thead>
        <tbody>
			';
    }
    $cnt++;

    if ($item["eqid"] == 'BU') {
        if (substr($item["ucetaktiva"], -9) == 'kolateral') {
            $typ = 'Bežný účet - kolaterál';
        } else {
            $typ = 'Bežný účet';
        }
    } else $typ = 'Termínovaný vklad';

    $obj = intval($item["sumaref"]) + intval($item["auvref"]);
    $auv = floatval($item["auv"]);
    $p = ($obj / $total) * 100;
    $podiel = floatval($p);
    $mena = $item["menadenom"];
    $objem = floatval($item["sumadenom"]);
    $objem_ref = floatval($obj);
    $menovykurz = floatval($item["kurz"]);
    $subjektid = $item["subjektid"];
    $ucetaktiva = $item["ucetaktiva"];

    $z_td = "";
    $k_td = "";
    $ir_td = "";
    if ($typ == 'Termínované vklad') {
        $query7 = " select z_td, k_td, ir_td 
						from konfirmaciaktv 
						where 
							subjektid = $subjektid  and Z_TD <= to_date('$dbdate','YYYY-MM-DD')						
							and K_TD >= to_date('$dbdate','YYYY-MM-DD')  and CUTD = '$ucetaktiva' and rownum = 1
			";

        $connectionRes7 = Connection::getDataFromDatabase($query7, defaultDB);
        $result7 = $connectionRes7[1];

        if (empty($result7))        // ak sme nic nenasli, pozreme este cez pool
        {
            $query8 = "	select z_td, k_td, ir_td 
							from konfirmaciaktv k, pool p, pooldetailreal pdr 
							where 
								k.subjektid = 0  and k.Z_TD <= to_date('$dbdate','YYYY-MM-DD') and k.K_TD >= to_date('$dbdate','YYYY-MM-DD') 
								and k.CUTD = '$ucetaktiva' and k.dealid = p.dealid and pdr.poolid = p.poolid and pdr.subjektid = $subjektid and rownum = 1
				";

            $connectionRes8 = Connection::getDataFromDatabase($query8, defaultDB);
            $result8 = $connectionRes8[1][0];
        }
        $z_td = gmdate(L_DATEFORMATZERO, fromDBDate($result8["z_td"]));
        $k_td = gmdate(L_DATEFORMATZERO, fromDBDate($result8["k_td"]));
        $ir_td = $result8["ir_td"];
        $ir_td = floatval($ir_td);
    }


    $vypis_obsah = $vypis_obsah . '
            <tr class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b dark:border-gray-700">
                <td class="px-6 py-4 font-medium">' . $typ . '</td>
                <td class="px-6 py-4 font-medium">' . $mena . '</td>
                <td class="px-6 py-4 font-medium">' . $z_td . '</td>
                <td class="px-6 py-4 font-medium">' . $k_td . '</td>
                <td class="px-6 py-4 font-medium">' . round($objem, 2) . '</td>
                <td class="px-6 py-4 font-medium">' . $auv . '</td>
                <td class="px-6 py-4 font-medium">' . $ir_td . '</td>
                <td class="px-6 py-4 font-medium">' . $menovykurz . '</td>
                <td class="px-6 py-4 font-medium">' . round($objem_ref, 2) . '</td>
                <td class="px-6 py-4 font-semibold text-right">' . round($podiel, 4) . '</td>
            </tr>
		';
}
if ($cnt != 0) {
    $vypis_obsah = $vypis_obsah . '
            <tr class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b dark:border-gray-700">
                <td colspan="8" class="px-6 py-4 font-bold">Spolu</td>
                <td class="px-6 py-4 font-bold">' . round($suma_p1, 2) . '</td>
                <td class="px-6 py-4 font-bold text-right">' . round($podiel_p2, 4) . '</td>
            </tr>
			</tbody>			
			</table>
			</div>
		';
}

//---------------dlhopisy detail---------------
$queryDlhopis = "
		select 
		        COALESCE(d.nominalemisie * COALESCE(f.faktor, 1), 1) as nominalemisie,
				d.cpnaz, menadenom as mena, d.isinreal as isinreal, d.isin, 
				d.maturitydate as datsplatnosti, 
				d.kupon, pocty.pocet, pocty.objemsauv, pocty.objemref, pocty.kurz, pocty.auv, pocty.kurz_mena
		from (
			select 	sum(case when md_d=0 then pocet else (-1)*pocet end) as pocet, 					
					max(kurzaktiva) as kurz, 
					sum((select sum(case when mt2.md_d=0 then mt2.sumadenom else (-1)*mt2.sumadenom end)
						from majetoktotal mt2 where mt.ucetaktiva=mt2.ucetaktiva and
						mt2.KODAKTIVA=mt.kodaktiva and mt.datum=mt2.datum and mt.subjektid=mt2.subjektid and
						mt2.uctovnykod in (251120) )
						) as auv,
					sum((select sum(case when mt2.md_d=0 then mt2.sumadenom else (-1)*mt2.sumadenom end)
						from majetoktotal mt2 where mt.ucetaktiva=mt2.ucetaktiva and
						mt2.KODAKTIVA=mt.kodaktiva and mt.datum=mt2.datum and mt.subjektid=mt2.subjektid and
						mt2.uctovnykod in (251110) )
						) as objemsauv,
					sum((select sum((case when mt2.md_d=0 then mt2.sumadenom else (-1)*mt2.sumadenom end) * 
						(SELECT kurz FROM f_menovy_kurz_lot(menadenom, '$refmena', mt2.datum)))
						from majetoktotal mt2 where mt.ucetaktiva=mt2.ucetaktiva and
						mt2.KODAKTIVA=mt.kodaktiva and mt.datum=mt2.datum and mt.subjektid=mt2.subjektid and
						mt2.uctovnykod in (251110, 251120) )
						) as objemref,
					kodaktiva,  menadenom, 
					max((SELECT kurz FROM f_menovy_kurz_lot(menadenom, '$refmena', mt.datum))) as kurz_mena
			from 
				majetoktotal mt
			where 
				mt.datum=to_date('$dbdate','YYYY-MM-DD') and mt.eqid in ('Bonds') and mt.uctovnykod in (251110) and	mt.subjektid in ($fondid)
			GROUP BY mt.kodaktiva,menadenom
		) pocty, dbequity d, dbequitycurr c, dbequitycurrric r, floatkupon f
		
		where pocty.kodaktiva=r.isincurrric and r.isincurr=c.isincurr and d.isin=c.isin AND pocty.kodaktiva = f.isincurrric AND f.datefrom <= '$todate' AND f.datetill >= '$fromdate'
		ORDER BY d.isinreal
	";
$connectionDlhopisy = Connection::getDataFromDatabase($queryDlhopis, defaultDB);
$dlhopisy = $connectionDlhopisy[1];

$cnt = 0;
foreach ($dlhopisy as $item) {
    if ($cnt == 0) {
        $vypis_obsah = $vypis_obsah . '
<div class="relative mb-10 overflow-x-auto mt-3 shadow-md sm:rounded-lg">
    <table class="w-full text-sm text-left rtl:text-right text-gray-500">
        <caption
                class="p-3 text-xl font-semibold text-left rtl:text-right text-gray-900 bg-white">
            <section class="flex w-full justify-between items-center p-1">
                <div>
                    Dlhopisy
                    <p class="mt-1 text-sm font-normal text-gray-500 dark:text-gray-400"><span
                                class="flex items-center gap-2">Portfólio: <a href="/klienti/detail/' . $klientPodielnikid . '#' . $cislozmluvy . '"
                                          class="bg-gray-500 flex items-center gap-1 hover:underline transition-all text-white py-0.5 px-2 rounded-lg"><span>' . $cislozmluvy . '</span>
                            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24"
                                 height="24" fill="none" viewBox="0 0 24 24">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                      stroke-width="2"
                                      d="M18 14v4.833A1.166 1.166 0 0 1 16.833 20H5.167A1.167 1.167 0 0 1 4 18.833V7.167A1.166 1.166 0 0 1 5.167 6h4.618m4.447-2H20v5.768m-7.889 2.121 7.778-7.778"/>
                            </svg>
                        </a>
                        </span>
                    </p>
                </div>
                <a href="/klienti/detail/' . $klientPodielnikid . '"
                   class="flex gap-3 hover:bg-gray-100 transition-all p-2 rounded-lg cursor-pointer">
                    <div class="relative w-10 h-10 overflow-hidden bg-gray-100 rounded-full dark:bg-gray-600">
                        <svg class="absolute w-12 h-12 text-gray-400 -left-1" fill="currentColor" viewBox="0 0 20 20"
                             xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                                  clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div>
                        <span class="text-sm">' . $klient . '</span>
                        <p class="text-xs text-gray-400">' . $klientMail . '</p>
                    </div>
                </a>
            </section>
        </caption>
        <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
        <tr>
            <th scope="col" class="px-6 py-3">Názov</th>
            <th scope="col" class="px-6 py-3">ISIN</th>
            <th scope="col" class="px-6 py-3">Počet kusov</th>
            <th scope="col" class="px-6 py-3">Aktuálna cena</th>
            <th scope="col" class="px-6 py-3">Objem</th>
            <th scope="col" class="px-6 py-3">Mena</th>
            <th scope="col" class="px-6 py-3">AUV</th>
            <th scope="col" class="px-6 py-3">Kurz</th>
            <th scope="col" class="px-6 py-3">Objem s AUV<br>v ' . $refmena . '</th>
            <th scope="col" class="px-6 py-3">Podiel v %</th>
        </tr>
        </thead>
        <tbody>
			';
    }
    $cnt++;

    $nazov = $item["cpnaz"];
    $mena = $item["mena"];
    $kurz = $item["kurz"];
    $kurz_mena = $item["kurz_mena"];
    $isinreal = $item["isinreal"];
    $pocet = $item["pocet"];
    $auv = floatval($item["auv"]);
    $objemsauv = floatval($item["objemsauv"]);//bez AUV
    $obj = $item["objemref"];
    $p = ($obj / $total) * 100;
    $podiel = floatval($p);
    $objemref = floatval($item["objemref"]);

    $vypis_obsah = $vypis_obsah . '
            <tr class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b dark:border-gray-700">
                <td class="px-6 py-4 font-medium">' . $nazov . '</td>
                <td class="px-6 py-4 font-medium">' . $isinreal . '</td>
                <td class="px-6 py-4 font-medium">' . $pocet . '</td>
                <td class="px-6 py-4 font-medium">' . round(floatval($kurz), 4) . '</td>
                <td class="px-6 py-4 font-medium">' . round($objemsauv, 2) . '</td>
                <td class="px-6 py-4 font-medium">' . $mena . '</td>
                <td class="px-6 py-4 font-medium">' . $auv . '</td>
                <td class="px-6 py-4 font-medium">' . round(floatval($kurz_mena), 4) . '</td>
                <td class="px-6 py-4 font-medium">' . round($objemref, 2) . '</td>
                <td class="px-6 py-4 font-semibold text-right">' . round($podiel, 4) . '</td>
            </tr>
		';
}

if ($cnt != 0) {
    $vypis_obsah = $vypis_obsah . '			
					<tr class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b dark:border-gray-700">
						<td colspan="8" class="px-6 py-4 font-medium"><b>Spolu</b></td>
						<td class="px-6 py-4 font-medium"<b>' . round($suma_d1, 2) . '</b></td>
						<td class="px-6 py-4 font-medium"><b>' . round($podiel_d2, 4) . '</b></td>
					</tr>
				</tbody>
			</table>
		</div>
		';
}

//---------------akcie detail---------------
$query9 = "select d.cpnaz, menadenom as mena, d.isinreal, pocty.pocet, pocty.kurz,
					pocty.objem, pocty.objemref, pocty.kurz_mena				
			from (
				SELECT sum(case when md_d=0 then pocet else (-1)*pocet end) as pocet, 
					sum(case when md_d=0 then sumadenom else (-1)*sumadenom end) as objem, 
					sum((case when md_d=0 then sumadenom else (-1)*sumadenom end) * 
						(SELECT kurz FROM f_menovy_kurz_lot(menadenom, '$refmena', datum))) as objemref,
					kodaktiva,menadenom,max(kurzaktiva) as kurz, max((SELECT * FROM f_menovy_kurz_lot(menadenom, '$refmena', mt.datum))) as kurz_mena
				from
					majetoktotal mt, dbequitycurrric dcr, dbequitycurr dc, dbequity d
				where
					mt.datum=to_date('$dbdate','YYYY-MM-DD') and mt.uctovnykod in (251200) and mt.subjektid=$fondid and
					dcr.isincurrric = mt.kodaktiva and dc.isincurr = dcr.isincurr and d.isin = dc.isin and
					d.druheqid not in (8,15,17) -- neberieme tu ETF a index certif
				group by mt.kodaktiva, menadenom
			) pocty, dbequity d, dbequitycurr c, dbequitycurrric r
			where pocty.kodaktiva=r.isincurrric and d.isin=c.isin and r.isincurr=c.isincurr 
			ORDER BY d.isinreal
	";

$connectionRes9 = Connection::getDataFromDatabase($query9, defaultDB);
$result9 = $connectionRes9[1];

$cnt = 0;
foreach ($result9 as $item) {
    if ($cnt == 0) {
        $vypis_obsah = $vypis_obsah . '
<div class="relative mb-10 overflow-x-auto mt-3 shadow-md sm:rounded-lg">
    <table class="w-full text-sm text-left rtl:text-right text-gray-500">
        <caption
                class="p-3 text-xl font-semibold text-left rtl:text-right text-gray-900 bg-white">
            <section class="flex w-full justify-between items-center p-1">
                <div>
                    Akcie, ADR a GDR
                    <p class="mt-1 text-sm font-normal text-gray-500 dark:text-gray-400"><span
                                class="flex items-center gap-2">Portfólio: <a href="/klienti/detail/' . $klientPodielnikid . '#' . $cislozmluvy . '"
                                          class="bg-gray-500 flex items-center gap-1 hover:underline transition-all text-white py-0.5 px-2 rounded-lg"><span>' . $cislozmluvy . '</span>
                            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24"
                                 height="24" fill="none" viewBox="0 0 24 24">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                      stroke-width="2"
                                      d="M18 14v4.833A1.166 1.166 0 0 1 16.833 20H5.167A1.167 1.167 0 0 1 4 18.833V7.167A1.166 1.166 0 0 1 5.167 6h4.618m4.447-2H20v5.768m-7.889 2.121 7.778-7.778"/>
                            </svg>
                        </a>
                        </span>
                    </p>
                </div>
                <a href="/klienti/detail/' . $klientPodielnikid . '"
                   class="flex gap-3 hover:bg-gray-100 transition-all p-2 rounded-lg cursor-pointer">
                    <div class="relative w-10 h-10 overflow-hidden bg-gray-100 rounded-full dark:bg-gray-600">
                        <svg class="absolute w-12 h-12 text-gray-400 -left-1" fill="currentColor" viewBox="0 0 20 20"
                             xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                                  clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div>
                        <span class="text-sm">' . $klient . '</span>
                        <p class="text-xs text-gray-400">' . $klientMail . '</p>
                    </div>
                </a>
            </section>
        </caption>
        <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
        <tr>
            <th scope="col" class="px-6 py-3">Názov</th>
            <th scope="col" class="px-6 py-3">ISIN</th>
            <th scope="col" class="px-6 py-3">Počet kusov</th>
            <th scope="col" class="px-6 py-3">Aktuálna cena</th>
            <th scope="col" class="px-6 py-3">Objem</th>
            <th scope="col" class="px-6 py-3">Mena</th>
            <th scope="col" class="px-6 py-3">Kurz</th>
            <th scope="col" class="px-6 py-3">Objem s AUV<br>v ' . $refmena . '</th>
            <th scope="col" class="px-6 py-3">Podiel v %</th>
        </tr>
        </thead>
        <tbody>
			';
    }
    $cnt++;

    $nazov = $item["cpnaz"];
    $mena = $item["mena"];
    $isinreal = $item["isinreal"];
    $pocet = $item["pocet"];
    $objem = $item["objem"];
    $kurz = $item["kurz"];
    $kurz_mena = $item["kurz_mena"];
    $obj = $item["objemref"];
    $p = ($obj / $total) * 100;
    $podiel = floatval($p);
    $objem = floatval($item["objem"]);
    $objemref = floatval($item["objemref"]);


    $vypis_obsah = $vypis_obsah . '
			<tr class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b">
				<td class="px-6 py-4 font-medium">' . $nazov . '</td>
				<td class="px-6 py-4 font-medium">' . $isinreal . '</td>
				<td class="px-6 py-4 font-medium">' . $pocet . '</td>
				<td class="px-6 py-4 font-medium">' . round(floatval($kurz), 4) . '</td>
				<td class="px-6 py-4 font-medium">' . round($objem, 2) . '</td>
				<td class="px-6 py-4 font-medium">' . $mena . '</td>
				<td class="px-6 py-4 font-medium">' . round(floatval($kurz_mena), 4) . '</td>
				<td class="px-6 py-4 font-medium">' . round($objemref, 2) . '</td>
				<td class="px-6 py-4 font-semibold text-right">' . round($podiel, 4) . '</td>
			</tr>
		';
}

if ($cnt != 0) {
    $vypis_obsah = $vypis_obsah . '
			<tr class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b">
					<td colspan="7" class="px-6 py-4 font-medium"><b>Spolu</b></td>
					<td class="px-6 py-4 font-bold"><b>' . round($suma_a1, 2) . '</b></td>
					<td class="px-6 py-4 font-semibold text-right"><b>' . round($podiel_a2, 4) . '</b></td>
				</tr>
               </tbody>
			</table>
		</div>
		';
}

//---------------podiely detail---------------
$queryPodiely = "select d.cpnaz, menadenom as mena, d.isinreal, pocty.pocet, pocty.kurz,
						pocty.objem, pocty.objemref, pocty.kurz_mena
				 from (
					SELECT sum(case when md_d=0 then pocet else (-1)*pocet end) as pocet,
							sum(case when md_d=0 then sumadenom else (-1)*sumadenom end) as objem, 
							sum((case when md_d=0 then sumadenom else (-1)*sumadenom end) * 
								(SELECT kurz FROM f_menovy_kurz_lot(menadenom, '$refmena', datum))) as objemref,
							kodaktiva,menadenom, max(kurzaktiva) as kurz, max((SELECT kurz FROM f_menovy_kurz_lot(menadenom, '$refmena', mt.datum))) as kurz_mena
					from
						majetoktotal mt, dbequitycurrric dcr, dbequitycurr dc, dbequity d
					where
						mt.datum=to_date('$dbdate','YYYY-MM-DD') and mt.uctovnykod in (251200,251300) and mt.subjektid=$fondid and
						dcr.isincurrric = mt.kodaktiva and dc.isincurr = dcr.isincurr and d.isin = dc.isin and
						(d.eqid = 'Fonds' or (d.druheqid in (8,15,17) and d.eqid='Shares' ))
					group by mt.kodaktiva,mt.menadenom
				) pocty, dbequity d, dbequitycurr c, dbequitycurrric r
				where pocty.kodaktiva=r.isincurrric and d.isin=c.isin and r.isincurr=c.isincurr 
	";

$connectionPodiely = Connection::getDataFromDatabase($queryPodiely, defaultDB);
$podiely = $connectionPodiely[1];

$cnt = 0;
foreach ($podiely as $item) {
    if ($cnt == 0) {
        $vypis_obsah = $vypis_obsah . '
<div class="relative mb-10 overflow-x-auto mt-3 shadow-md sm:rounded-lg">
    <table class="w-full text-sm text-left rtl:text-right text-gray-500">
        <caption
                class="p-3 text-xl font-semibold text-left rtl:text-right text-gray-900 bg-white">
            <section class="flex w-full justify-between items-center p-1">
                <div>
                    Podielové listy a ETF
                    <p class="mt-1 text-sm font-normal text-gray-500 dark:text-gray-400"><span
                                class="flex items-center gap-2">Portfólio: <a href="/klienti/detail/' . $klientPodielnikid . '#' . $cislozmluvy . '"
                                          class="bg-gray-500 flex items-center gap-1 hover:underline transition-all text-white py-0.5 px-2 rounded-lg"><span>' . $cislozmluvy . '</span>
                            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24"
                                 height="24" fill="none" viewBox="0 0 24 24">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                      stroke-width="2"
                                      d="M18 14v4.833A1.166 1.166 0 0 1 16.833 20H5.167A1.167 1.167 0 0 1 4 18.833V7.167A1.166 1.166 0 0 1 5.167 6h4.618m4.447-2H20v5.768m-7.889 2.121 7.778-7.778"/>
                            </svg>
                        </a>
                        </span>
                    </p>
                </div>
                <a href="/klienti/detail/' . $klientPodielnikid . '"
                   class="flex gap-3 hover:bg-gray-100 transition-all p-2 rounded-lg cursor-pointer">
                    <div class="relative w-10 h-10 overflow-hidden bg-gray-100 rounded-full dark:bg-gray-600">
                        <svg class="absolute w-12 h-12 text-gray-400 -left-1" fill="currentColor" viewBox="0 0 20 20"
                             xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                                  clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div>
                        <span class="text-sm">' . $klient . '</span>
                        <p class="text-xs text-gray-400">' . $klientMail . '</p>
                    </div>
                </a>
            </section>
        </caption>
        <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
        <tr>
            <th scope="col" class="px-6 py-3">Názov</th>
            <th scope="col" class="px-6 py-3">ISIN</th>
            <th scope="col" class="px-6 py-3">Počet kusov</th>
            <th scope="col" class="px-6 py-3">Aktuálna cena</th>
            <th scope="col" class="px-6 py-3">Objem</th>
            <th scope="col" class="px-6 py-3">Mena</th>
            <th scope="col" class="px-6 py-3">Kurz</th>
            <th scope="col" class="px-6 py-3">Objem s AUV<br>v ' . $refmena . '</th>
            <th scope="col" class="px-6 py-3">Podiel v %</th>
        </tr>
        </thead>
        <tbody>
			';
    }
    $cnt++;

    $nazov = $item["cpnaz"];
    $mena = $item["mena"];
    $isinreal = $item["isinreal"];
    $pocet = $item["pocet"];
    $kurz = $item["kurz"];
    $kurz_mena = $item["kurz_mena"];
    $objem = $item["objem"];
    $obj = $item["objemref"];
    $p = ($obj / $total) * 100;
    $podiel = floatval($p);
    $objem = floatval($item["objem"]);
    $objemref = floatval($item["objemref"]);

    $vypis_obsah = $vypis_obsah . '	
			<tr class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b">
				<td class="px-6 py-4 font-medium">' . $nazov . '</td>
				<td class="px-6 py-4 font-medium">' . $isinreal . '</td>
				<td class="px-6 py-4 font-medium">' . $pocet . '</td>
				<td class="px-6 py-4 font-medium">' . round(floatval($kurz), 4) . '</td>
				<td class="px-6 py-4 font-medium">' . round($objem, 2) . '</td>
				<td class="px-6 py-4 font-medium">' . $mena . '</td>
				<td class="px-6 py-4 font-medium">' . round(floatval($kurz_mena), 4) . '</td>
				<td class="px-6 py-4 font-medium">' . round($objemref, 2) . '</td>
				<td class="px-6 py-4 font-semibold text-right">' . round($podiel, 4) . '</td>
			</tr>
		';
}

if ($cnt != 0) {
    $vypis_obsah = $vypis_obsah . '
			<tr class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b">
					<td colspan="7" class="px-6 py-4 font-medium"><b>Spolu</b></td>
					<td class="px-6 py-4 font-bold"><b>' . round($suma_f1, 2) . '</b></td>
					<td class="px-6 py-4 font-bold text-right"><b>' . round($podiel_f2, 4) . '</b></td>
				</tr>
            </tbody>
			</table>
			</div>
		';
}

//---------------pohladavky a zavazky detail---------------
$queryPohladavky = "
		SELECT 
				n.popis, (CASE mt.eqid WHEN 'BU' THEN 0 WHEN 'TD' THEN mt.pocet END) as pocet,
				mt.ucetaktiva, mt.subjektid,mt.eqid, menadenom, sumadenom*sign(0.5-md_d) as sumadenom, menaref, 
				(SELECT * FROM f_menovy_kurz_lot(menadenom,'$refmena',to_date('$dbdate','YYYY-MM-DD'))) as kurz, 
				(SELECT kurz FROM f_menovy_kurz_lot(menadenom,'$refmena',to_date('$dbdate','YYYY-MM-DD'))) as kurz_lot,
				sumadenom*sign(0.5-md_d)*(SELECT kurz FROM f_menovy_kurz_lot(menadenom,'$refmena',to_date('$dbdate','YYYY-MM-DD'))) as sumaref,
				case  	when (mt.eqid in ('Bonds','Fonds','Shares','Depo') or mt.uctovnykod in(315601,315602,315603,315604,315605,315606,315607,315608,315609)) then d.cpnaz
						else mt.kodaktiva end as kodaktiva,
				COALESCE(
					( case when mt.uctovnykod in (315121,325121)
								then (	select sum(sign(0.5-md_d)*mt2.sumadenom)
										from majetoktotal mt2 
										where 
											mt.ucetaktiva = mt2.ucetaktiva and mt.kodaktiva =  mt2.kodaktiva  and  mt.datum = mt2.datum and 
											mt.subjektid = mt2.subjektid and mt.uctovnykod+1 = mt2.uctovnykod and mt.eqid = mt2.eqid
									)
							else 0 end 
					),0) as auvdenom
		FROM majetoktotal mt
        JOIN navuctovanie n ON mt.uctovnykod = n.uctovnykod
        LEFT JOIN dbequity d ON d.isin = SUBSTRING(mt.kodaktiva FROM 1 FOR 12)
        WHERE mt.datum = TO_DATE('$dbdate', 'YYYY-MM-DD')
          AND (CAST(n.uctovnykod AS TEXT) LIKE '315%' OR CAST(n.uctovnykod AS TEXT) LIKE '325%' OR CAST(n.uctovnykod AS TEXT) LIKE '261%')
          AND mt.uctovnykod NOT IN (315122, 325122) -- auv je zaratane k dlhopisu
          AND NOT EXISTS (
              SELECT 1
              FROM majetoktotal mt2
              JOIN navuctovanie n2 ON mt2.uctovnykod = n2.uctovnykod
              WHERE mt2.datum = mt.datum
                AND mt2.uctovnykod IN (221110, 221210)
                AND mt2.uctovnykod IN (SELECT uctovnykod 
                                       FROM navuctovanie 
                                       WHERE CAST(uctovnykod AS TEXT) LIKE '325%' OR CAST(uctovnykod AS TEXT) LIKE '315%' OR CAST(uctovnykod AS TEXT) LIKE '261%')
                AND mt2.eqid = mt.eqid
                AND mt2.subjektid = mt.subjektid
                AND mt2.kodaktiva = mt.kodaktiva
                AND mt2.ucetaktiva = mt.ucetaktiva
          )
          AND mt.subjektid = $fondid
        ORDER BY n.popis, mt.menadenom;
	";

$connectionPohladavky = Connection::getDataFromDatabase($queryPohladavky, defaultDB);
$pohladavky = $connectionPohladavky[1];

$cnt = 0;
foreach ($pohladavky as $item) {
    if ($cnt == 0) {
        $vypis_obsah = $vypis_obsah . '
<div class="relative mb-10 overflow-x-auto mt-3 shadow-md sm:rounded-lg">
    <table class="w-full text-sm text-left rtl:text-right text-gray-500">
        <caption
                class="p-3 text-xl font-semibold text-left rtl:text-right text-gray-900 bg-white">
            <section class="flex w-full justify-between items-center p-1">
                <div>
                    Záväzky a pohľadávky
                    <p class="mt-1 text-sm font-normal text-gray-500 dark:text-gray-400"><span
                                class="flex items-center gap-2">Portfólio: <a href="/klienti/detail/' . $klientPodielnikid . '#' . $cislozmluvy . '"
                                          class="bg-gray-500 flex items-center gap-1 hover:underline transition-all text-white py-0.5 px-2 rounded-lg"><span>' . $cislozmluvy . '</span>
                            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24"
                                 height="24" fill="none" viewBox="0 0 24 24">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                      stroke-width="2"
                                      d="M18 14v4.833A1.166 1.166 0 0 1 16.833 20H5.167A1.167 1.167 0 0 1 4 18.833V7.167A1.166 1.166 0 0 1 5.167 6h4.618m4.447-2H20v5.768m-7.889 2.121 7.778-7.778"/>
                            </svg>
                        </a>
                        </span>
                    </p>
                </div>
                <a href="/klienti/detail/' . $klientPodielnikid . '"
                   class="flex gap-3 hover:bg-gray-100 transition-all p-2 rounded-lg cursor-pointer">
                    <div class="relative w-10 h-10 overflow-hidden bg-gray-100 rounded-full dark:bg-gray-600">
                        <svg class="absolute w-12 h-12 text-gray-400 -left-1" fill="currentColor" viewBox="0 0 20 20"
                             xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                                  clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div>
                        <span class="text-sm">' . $klient . '</span>
                        <p class="text-xs text-gray-400">' . $klientMail . '</p>
                    </div>
                </a>
            </section>
        </caption>
        <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
        <tr>
            <th scope="col" class="px-6 py-3">Názov</th>
            <th scope="col" class="px-6 py-3">Názov CP</th>
            <th scope="col" class="px-6 py-3">Objem</th>
            <th scope="col" class="px-6 py-3">Mena</th>
            <th scope="col" class="px-6 py-3">Počet</th>
            <th scope="col" class="px-6 py-3">Kurz</th>
            <th scope="col" class="px-6 py-3">Objem v ' . $refmena . '</th>
            <th scope="col" class="px-6 py-3">Podiel v %</th>
        </tr>
        </thead>
        <tbody>
			';
    }
    $cnt++;

    $popis = $item["popis"];
    $obj = $item["sumaref"] + $item["auvdenom"] * $item["kurz_lot"];
    $paz = ($obj / $total) * 100;
    $podiel = floatval($paz);
    $mena = $item["menadenom"];
    $objem = floatval($item["sumadenom"] + $item["auvdenom"]);
    $objem_ref = floatval($obj);
    $menovykurz = floatval($item["kurz"]);
    $subjektid = $item["subjektid"];
    $ucetaktiva = $item["ucetaktiva"];
    $pocet = $item["pocet"];
    if ($pocet != '') $pocet = floatval($pocet);
    $kodaktiva = $item['kodaktiva'];

    $vypis_obsah = $vypis_obsah . '	
			<tr class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b">
				<td class="px-6 py-4 font-medium">' . $popis . '</td>
				<td class="px-6 py-4 font-medium">' . $kodaktiva . '</td>
				<td class="px-6 py-4 font-medium">' . round($objem, 2) . '</td>
				<td class="px-6 py-4 font-medium">' . $mena . '</td>
				<td class="px-6 py-4 font-medium">' . $pocet . '</td>
				<td class="px-6 py-4 font-medium">' . round($menovykurz, 4) . '</td>
				<td class="px-6 py-4 font-medium">' . round($objem_ref, 2) . '</td>
				<td class="px-6 py-4 font-semibold text-right">' . round($podiel, 4) . '</td>
			</tr>
		';
}

if ($cnt != 0) {
    $vypis_obsah = $vypis_obsah . '
					<tr class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b">
						<td colspan="6" class="px-6 py-4 font-bold"><b>Spolu</b></td>
						<td class="px-6 py-4 font-bold"><b>' . round($suma_paz1, 2) . '</b></td>
						<td class="px-6 py-4 text-right font-bold"><b>' . round($podiel_paz2, 4) . '</b></td>
					</tr>
                </tbody>
			</table>
		</div>
		';
}

//---------------Očakávané finančné toky---------------
$activity = "12,15,18";

$queryFinacneToky = "
		select  sum(round(
		                ((CASE md_d WHEN 0 THEN md_d0 WHEN 1 THEN md_d1 END) *
		                (COALESCE(d.nominalemisie * COALESCE(f.faktor, 1), 1)) * 
						m.pocet*		
						f.faktor*
						(f.kupon/100)*
		                f_koef_auv_isincurrric(dcr.isincurrric,d.zaklad,d.dateemisie,d.maturitydate,f.datesplatnost,d.prvy_kupon,d.posledny_kupon,d.kupfrek,d.exfrekkup,2))
						,COALESCE(d.rounding,25)
					)
				) as urok,
				0 as istina, to_char(f.datesplatnost,'dd.mm.yyyy') as mydatum, f.datesplatnost, d.cpnaz, m.menadenom,
				min(case when (po.typ_zdanenia = 'FO') then COALESCE(d.sadzbafo, 0)
						when (po.typ_zdanenia = 'PO') then COALESCE(d.sadzbapo, 0)
						when (po.typ_zdanenia = 'NO') then COALESCE(d.sadzbano, 0)			
						else 0 end
				) as dan,
				'kupon' as typ, m.subjektid, p.cislozmluvy
		from
			majetoktotal m, floatkupon f, dbequity d, dbequitycurr dc, dbequitycurrric dcr, navuctovanie n,
			portfolio p,  podielnik po
		where
			m.datum=to_date('$dbdate','YYYY-MM-DD') and m.uctovnykod in (315124) and m.subjektid in ($fondid) and
			f.isincurrric = m.kodaktiva and f.datesplatnost > m.datum and dcr.isincurrric = m.kodaktiva and
			dc.isincurr = dcr.isincurr and d.isin = dc.isin and m.uctovnykod = n.uctovnykod and	
			p.fondid = m.subjektid  and  (is_exdate(m.kodaktiva,m.datum,0) = 1) and ((f.datesplatnost-COALESCE(d.exfrekkup,0)) <= m.datum)  
			and (is_coupon_date(m.kodaktiva,m.datum) = 0) and p.podielnikid = po.podielnikid 
			and f.datesplatnost < TO_DATE(
              CASE
                WHEN SUBSTRING('2023-09-01', 6, 2) = '12' THEN
                  (CAST(SUBSTRING('2023-09-01', 1, 4) AS INTEGER) + 1)::TEXT || '-01-' || SUBSTRING('2023-09-01', 9, 2)
                ELSE
                  SUBSTRING('2023-09-01', 1, 4) || '-' || LPAD((CAST(SUBSTRING('2023-09-01', 6, 2) AS INTEGER) + 1)::TEXT, 2, '0') || '-' || SUBSTRING('2023-09-01', 9, 2)
              END,
              'YYYY-MM-DD'
            )
		group by
			m.kodaktiva, f.datesplatnost, d.cpnaz, m.menadenom, d.dan, m.subjektid, p.cislozmluvy, po.typ_zdanenia
		  
		union all
		 
		select
				sum(round(
				        ((CASE md_d WHEN 0 THEN md_d0 WHEN 1 THEN md_d1 END) *
						(COALESCE(d.nominalemisie * COALESCE(f.faktor, 1), 1)) *
						m.pocet*		
						f.faktor*
						(f.kupon/100)*
						f_koef_auv_isincurrric(dcr.isincurrric,d.zaklad,d.dateemisie,d.maturitydate,f.datesplatnost,d.prvy_kupon,d.posledny_kupon,d.istfrek,d.exfrekkup,2))
						,COALESCE(d.rounding,25)
					)
				) as urok,
				0 as istina, to_char(f.datesplatnost,'dd.mm.yyyy') as mydatum,
				f.datesplatnost, d.cpnaz, m.menadenom,
				min(case when (po.typ_zdanenia = 'FO') then COALESCE(d.sadzbafo, 0)
						when (po.typ_zdanenia = 'PO') then COALESCE(d.sadzbapo, 0)
						when (po.typ_zdanenia = 'NO') then COALESCE(d.sadzbano, 0)			
						else 0 end
				) as dan,
				'kupon' as typ, m.subjektid, p.cislozmluvy
		from
			majetoktotal m, floatkupon f, dbequity d, dbequitycurr dc, dbequitycurrric dcr, navuctovanie n, portfolio p, podielnik po
		where
			m.datum=to_date('$dbdate','YYYY-MM-DD') and m.uctovnykod in (251110,325121,315121) and m.subjektid in ($fondid) and
			f.isincurrric = m.kodaktiva and f.datesplatnost > m.datum and dcr.isincurrric = m.kodaktiva and
			dc.isincurr = dcr.isincurr and d.isin = dc.isin and m.uctovnykod = n.uctovnykod and	
			p.fondid = m.subjektid  and not ((is_exdate(m.kodaktiva,m.datum,0) = 1) and ((f.datesplatnost - COALESCE(d.exfrekkup,0)) <= m.datum) 
			and (is_exdate(m.kodaktiva,m.datum,0) = 0)) and p.PODIELNIKID = po.PODIELNIKID
			and f.datesplatnost < TO_DATE(
              CASE
                WHEN SUBSTRING('2023-09-01', 6, 2) = '12' THEN
                  (CAST(SUBSTRING('2023-09-01', 1, 4) AS INTEGER) + 1)::TEXT || '-01-' || SUBSTRING('2023-09-01', 9, 2)
                ELSE
                  SUBSTRING('2023-09-01', 1, 4) || '-' || LPAD((CAST(SUBSTRING('2023-09-01', 6, 2) AS INTEGER) + 1)::TEXT, 2, '0') || '-' || SUBSTRING('2023-09-01', 9, 2)
              END,
              'YYYY-MM-DD'
            )
		group by
			m.kodaktiva, f.datesplatnost, d.cpnaz, m.menadenom, d.dan, m.subjektid, p.cislozmluvy, po.typ_zdanenia

		union all

		select 0 as urok, 
		       round(
				                ((CASE md_d WHEN 0 THEN md_d0 WHEN 1 THEN md_d1 END) *
								-- decode(md_d,0,md_d0,1,md_d1)*
								-- pkg_conversion.getCPNominal(d.isin,m.datum)*
                                (COALESCE(d.nominalemisie * COALESCE(f.faktor, 1), 1)) *
								( case 	when is_exdate(m.kodaktiva,m.datum, 1) = 1
											then COALESCE((select pocet from majetoktotal m2 where m2.datum=m.datum and m2.subjektid=m.subjektid and m2.kodaktiva = m.kodaktiva and m2.uctovnykod=315123),0)
										else m.pocet end)*
				                (COALESCE(f.istina, 0)/100))
								,COALESCE(d.rounding,25)
							) as istina,
				to_char(f.datesplatnost,'dd.mm.yyyy') as mydatum, f.datesplatnost, d.cpnaz, m.menadenom,
				d.dan, 'istina' as typ, m.subjektid, p.cislozmluvy
		from
			majetoktotal m, floatkupon f, dbequity d, dbequitycurr dc, dbequitycurrric dcr, navuctovanie n, portfolio p
		where
			m.datum=to_date('$dbdate','YYYY-MM-DD') and m.uctovnykod in (251110,325121,315121) and m.subjektid in ($fondid) and
			f.isincurrric = m.kodaktiva and f.datesplatnost > m.datum and dcr.isincurrric = m.kodaktiva and
			dc.isincurr = dcr.isincurr and d.isin = dc.isin and f.istina != 0 and
			m.uctovnykod = n.uctovnykod and	 p.fondid = m.subjektid 
		    and f.datesplatnost < TO_DATE(
              CASE
                WHEN SUBSTR('2023-09-01', 6, 2) = '12' THEN
                  (CAST(SUBSTR('2023-09-01', 1, 4) AS INTEGER) + 1)::TEXT || '-01-' || SUBSTR('2023-09-01', 9, 2)
                ELSE
                  SUBSTR('2023-09-01', 1, 4) || '-' || LPAD((CAST(SUBSTR('2023-09-01', 6, 2) AS INTEGER) + 1)::TEXT, 2, '0') || '-' || SUBSTR('2023-09-01', 9, 2)
              END,
              'YYYY-MM-DD'
            )
		order by 
			datesplatnost, subjektid, cpnaz
	";

$connectionFinacneToky = Connection::getDataFromDatabase($queryFinacneToky, defaultDB);
$financneToky = $connectionFinacneToky[1];

$cnt = 0;

foreach ($financneToky as $item) {
    if ($cnt == 0) {
        $vypis_obsah = $vypis_obsah . '
<div class="relative mb-10 overflow-x-auto mt-3 shadow-md sm:rounded-lg">
    <table class="w-full text-sm text-left rtl:text-right text-gray-500">
        <caption
                class="p-3 text-xl font-semibold text-left rtl:text-right text-gray-900 bg-white">
            <section class="flex w-full justify-between items-center p-1">
                <div>
                    Očakávané finančné toky ku dňu ' . $showdate . '
                    <p class="mt-1 text-sm font-normal text-gray-500 dark:text-gray-400"><span
                                class="flex items-center gap-2">Portfólio: <a href="/klienti/detail/' . $klientPodielnikid . '#' . $cislozmluvy . '"
                                          class="bg-gray-500 flex items-center gap-1 hover:underline transition-all text-white py-0.5 px-2 rounded-lg"><span>' . $cislozmluvy . '</span>
                            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24"
                                 height="24" fill="none" viewBox="0 0 24 24">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                      stroke-width="2"
                                      d="M18 14v4.833A1.166 1.166 0 0 1 16.833 20H5.167A1.167 1.167 0 0 1 4 18.833V7.167A1.166 1.166 0 0 1 5.167 6h4.618m4.447-2H20v5.768m-7.889 2.121 7.778-7.778"/>
                            </svg>
                        </a>
                        </span>
                    </p>
                </div>
                <a href="/klienti/detail/' . $klientPodielnikid . '"
                   class="flex gap-3 hover:bg-gray-100 transition-all p-2 rounded-lg cursor-pointer">
                    <div class="relative w-10 h-10 overflow-hidden bg-gray-100 rounded-full dark:bg-gray-600">
                        <svg class="absolute w-12 h-12 text-gray-400 -left-1" fill="currentColor" viewBox="0 0 20 20"
                             xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                                  clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div>
                        <span class="text-sm">' . $klient . '</span>
                        <p class="text-xs text-gray-400">' . $klientMail . '</p>
                    </div>
                </a>
            </section>
        </caption>
        <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
        <tr>
            <th scope="col" class="px-6 py-3">Dátum</th>
            <th scope="col" class="px-6 py-3">Investičný nástroj</th>
            <th scope="col" class="px-6 py-3">Mena</th>
            <th scope="col" class="px-6 py-3">Istina</th>
            <th scope="col" class="px-6 py-3">Kupón</th>
            <th scope="col" class="px-6 py-3">Daň</th>
        </tr>
        </thead>
        <tbody>
			';
    }
    $cnt++;

    $datum = $item["mydatum"];
    $nastroj = $item["cpnaz"];
    $mena = $item["menadenom"];
    $dan = $item["dan"];
    $urok = $item["urok"];
    $istina = $item["istina"];
    $dan = $urok / 100 * $dan;
    $istina = floatval($istina);
    $urok = floatval($urok);
    $dan = floatval($dan);

    $vypis_obsah = $vypis_obsah . '
				<tr class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b">
					<td class="px-6 py-4 font-medium">' . $datum . '</td>
					<td class="px-6 py-4 font-medium">' . $nastroj . '</td>
					<td class="px-6 py-4 font-medium">' . $mena . '</td>
					<td class="px-6 py-4 font-medium">' . $istina . '</td>
					<td class="px-6 py-4 font-medium">' . $urok . '</td>
					<td class="px-6 py-4 font-semibold text-right">' . $dan . '</td>
				</tr>
		';
}

if ($cnt != 0) {
    $vypis_obsah = $vypis_obsah . '
</tbody>
			</table>
		</div>	
		';
}
<?php
require_once "/home/<USER>/www/src/Components/tables/klientskyVypis/klientReportTable.php";
include "/home/<USER>/www/src/Views/prehlady/vypis_o_stave_majetku/queries/danZCPQuery.php";
$danRes = Connection::getDataFromDatabase($danQuery, defaultDB);
$dane = $danRes[1];
print_r($dane);
if (sizeof($dane) > 0) { ?>
    <section id="danZCP<?php echo $klientPodielnikid . $cislozmluvy ?>"
             class="flex w-full justify-between mb-4 items-center">
        <div class="p-5 text-xl w-full font-semibold border rounded-lg shadow-sm text-left rtl:text-right bg-gray-700 text-white">
            <div>
                Prehľad potvrdení o&nbsp;zaplatenej dani z&nbsp;v<PERSON><PERSON><PERSON> z cenných papierov
                <p class="mt-1 text-sm font-normal dark:text-gray-400 text-gray-400">
                    <span class="flex items-center gap-2">Za obdobie od <?php echo $fromdate . ' do ' . $todate ?></span>
                </p>
            </div>
        </div>
    </section>

    <div class="relative mb-10 overflow-x-auto mt-3 shadow-md sm:rounded-lg">
    <section class="flex mb-4 justify-between p-8 bg-white rounded-lg">
        <div>
            <h3 class="text-lg"><b>Tuzemský obchodník s cennými papiermi</b></h3>
            <div class="flex gap-5">
                <b>Názov:</b>
                <p>Sympatia Financie, o.c.p., a.s.</p>
            </div>
            <div class="flex gap-5">
                <b>Sídlo:</b>
                <p>Vajnorská 100/B, 831 04 Bratislava</p>
            </div>
            <div class="flex gap-5">
                <b>IČO:</b>
                <p>35 842 369</p>
            </div>
        </div>
        <div>
            <h3 class="text-lg"><b>Skutočný vlastník príjmov</b></h3>
            <div class="flex gap-5">
                <b>Titul:</b>
                <p><?php echo $dane[0]["titulpred"]; ?></p>
            </div>
            <div class="flex gap-5">
                <b>Meno:</b>
                <p><?php echo $dane[0]["meno"] . " " . $dane[0]["prieznaz"]; ?></p>
            </div>
            <div class="flex gap-5">
                <b>Adresa:</b>
                <p><?php echo $dane[0]["address"] . ", " . $dane[0]["city"]; ?></p>
            </div>
            <div class="flex gap-5">
                <b>Rodné číslo:</b>
                <p><?php echo $dane[0]["rcico"]; ?></p>
            </div>
            <div class="flex gap-5">
                <b>Číslo klienta:</b>
                <p><?php echo $dane[0]["posa"]; ?></p>
            </div>
        </div>
    </section>
    <?php
    $daneTableData = [];
    $daneTableColums = [["ISIN CP", "Dátum pripísania výnosov", "Druh dlhopisu", "Počet kusov CP", "Výnos na 1ks CP", "Sadzba dane", "Zrazená daň"]];
    foreach ($dane

             as $item) {
        $rowData = [$item["isin"], $item["datum"], $item["druhcp"], round($item["kusov"], 0), round($item["vynos_kus"], 2), round($item["dansadzba"], 2), round($item["dan"], 2)];
        $daneTableData[] = $rowData;
    }
    ?>
    <section>
        <?php klientReportTable::render($daneTableData, $daneTableColums, "Zoznam potvrdení", "", "Portfólio: ", ["url" => "/klienti/detail/" . $klientPodielnikid . "#" . $cislozmluvy, "cislozmluvy" => $cislozmluvy], ["klientPodielnikid" => "/klienti/detail/" . $klientPodielnikid, "klient" => $klient, "klientMail" => $klientMail], ""); ?>
    </section>
<?php }
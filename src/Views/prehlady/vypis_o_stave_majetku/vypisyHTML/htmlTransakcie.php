<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
include "/home/<USER>/www/src/Views/prehlady/vypis_o_stave_majetku/queries/vypisBeznyUcetQuery.php";
include "/home/<USER>/www/src/Views/prehlady/vypis_o_stave_majetku/queries/terminovaneVkladyQuery.php";
include "/home/<USER>/www/src/Views/prehlady/vypis_o_stave_majetku/queries/konverzieQuery.php";
include "/home/<USER>/www/src/Views/prehlady/vypis_o_stave_majetku/queries/pohybyNaUcteQuery.php";
require_once "/home/<USER>/www/src/Components/tables/klientskyVypis/klientReportTable.php";

$transakcieRes = Connection::getDataFromDatabase($queryTransakcie, defaultDB);
$transakcie = $transakcieRes[1];

$terminovaneVklady = Connection::getDataFromDatabase($queryTerminovaneVklady, defaultDB);
$terminovaneVklady = $terminovaneVklady[1];
?>
    <section id="transakcie<?php echo $klientPodielnikid . $cislozmluvy ?>"
             class="flex w-full justify-between mb-4 items-center">
        <div class="p-5 text-xl w-full font-semibold border rounded-lg shadow-sm text-left rtl:text-right bg-gray-700 text-white">
            <div>
                Prehľad transakcií
                <p class="mt-1 text-sm font-normal dark:text-gray-400 text-gray-400"><span
                            class="flex items-center gap-2">Prehľad všetkých transakcií za obdobie od <?php echo $fromdate . ' do ' . $todate ?>
                            </span>
                </p>
            </div>
        </div>
    </section>
<?php
$getMaxDateForKlient = "SELECT max(datum) FROM majetoktotal WHERE subjektid = $fondid";
$clientToDate = Connection::getDataFromDatabase($getMaxDateForKlient, defaultDB)[1][0]['max'];

$cnt = 0;
$mena_foo = "";
$sekcia_ukoncena = true;
$sumacheck = 0;
$sumaAlert = "";
$transakcieTableData = [];
$transakcieArray = [];
$transakcieTableColumns = [["Dátum", "Transakcia", "Čiastka"]];

for ($i = 0; $i < sizeof($transakcie); $i++) {
    $datum = ($transakcie[$i]['datum'] !== '') ? $transakcie[$i]['datum'] : '';
    $poznamka = ($transakcie[$i]['poznamka'] !== '') ? " (" . $transakcie[$i]['poznamka'] . ")" : '';
    $popis = $transakcie[$i]['popis'];
    $suma = $transakcie[$i]['transsuma'];
    $mena = $transakcie[$i]['mena1'];
    $sumacheck = ($mena_foo != $mena) ? 0 : $sumacheck;
    $poradie = $transakcie[$i]['poradie'];
    if (intval($poradie) == 100000) {
        $sekcia_ukoncena = true;
        if (round($sumacheck, 2) != round($suma, 2)) {
            $sumaAlert = $sumaAlertBottom = "Neplatná zostava !";
        }
    } else {
        $sumacheck += $transakcie[$i]['md_d'] ? -$suma : $suma;
    }
    if ($mena_foo != $mena) {
        if ($mena_foo != "") {
            if (!$sekcia_ukoncena) {
                //pokial prebehli transakcie na ucte v jednom dni a v majetoktotal neexistuje zaznam, zostatok je nulovy
                //teda v poslednom dni prebehli transakcie a zostatok na ucte je nulovy, do majetoktotal sa nezapise ...
                $rowData = [$todate, "Konečný stav účtu", round(0, 2) . ' ' . $mena];
                $transakcieTableData[$cnt] = $rowData;
                if (isset($predoslasumacheck)) {
                    if (round($predoslasumacheck, 2) != round(0, 2)) {
                        $sumaAlert = $sumaAlertBottom = "Neplatná zostava !";
                    }
                }
            }
            if ($poradie != -1) {
                $rowData = [$fromdate, "Počiatočný stav účtu", round(0, 2) . ' ' . $mena];
                $transakcieTableData[$cnt] = $rowData;
            }
        }
        $sekcia_ukoncena = false;
        $vypisyspolu++;
    }
    $mena_foo = $mena;
    $predoslasuma = $suma;
    $predoslasumacheck = $sumacheck;
    $cnt = $i;
    $rowData = [$datum, $popis . $poznamka, number_format(round($suma, 2), 2, '.', ' ') . ' ' . $mena];
    $transakcieArray[] = [$datum, $popis . $poznamka, number_format(round($suma, 2), 2, '.', ' ') . ' ' . $mena];
    $transakcieTableData[$cnt] = $rowData;
}
if (!$sekcia_ukoncena) {
    $transakcieTableData[] = [$todate, "Konečný stav účtu", round(0, 2)];
}

usort($transakcieTableData, fn($a, $b) => $a[0] <=> $b[0]);
klientReportTable::render($transakcieTableData, $transakcieTableColumns, "Výpis z bežného účtu", "$mena", "Výpis všetkých transakcií na bežnom účte", ["url" => "/klienti/detail/" . $klientPodielnikid . "#" . $cislozmluvy, "cislozmluvy" => $cislozmluvy], ["klientPodielnikid" => "/klienti/detail/" . $klientPodielnikid, "klient" => $klient, "klientMail" => $klientMail], $sumaAlert);

$terminovaneArray = [];
$cnt = 0;
$mena_foo = "";
$trieda_foo = '';

foreach ($terminovaneVklady

         as $item) {
    $cnt++;
    $mena = $item["mena"];
    $suma = $item["suma"];
    $sadzba = $item["sadzba"];
    $brutto = $item["brutto"];
    $dan = $item["dan"];
    $netto = $item["netto"];
    $zaciatok = $item["zaciatok"];
    $koniec = $item["koniec"];
    $cisloKTV = $item["cutd"];
    $trieda = $item["trieda"];
    $miesto = $item["miesto"];
    $datum_cas = $item["datum_cas"];
    $typ_pokynu = $item["typPokynu"];
    $detailKTV = $item['detailKTV'];
    $rowData = [$detailKTV == "uzavreteSKK" ? "Uzavreté v SKK" : $datum_cas, $zaciatok, $koniec, $miesto, $typ_pokynu, round($suma, 2), number_format(round($sadzba, 4), 2, '.', ' ') . " " . $mena, $brutto, $dan];
    if ($detailKTV == "vyplateneEUR") {
        $detailKTVarr = "Vyplatené v EUR";
    } else {
        $detailKTVarr = $trieda == 3 ? "&nbsp;" : round($brutto, 2);
        $detailKTVarr = $trieda == 3 ? "&nbsp;" : round($dan, 2);
        $detailKTVarr = $trieda == 3 ? "&nbsp;" : round($netto, 2);
    }
    $rowData[] = $detailKTVarr;
    $terminovaneArray[] = $rowData;
    $tableData[$cnt] = $rowData;
}

$columns = ['Uzavretie TV', 'Zriadenie TV', 'Splatnosť TV', 'Miesto výkonu', 'Typ pokynu', 'Suma', 'Sadzba', 'Úrok brutto', 'Daň', 'Úrok netto'];
klientReportTable::render($tableData, $columns, "Prehľad termínovaných vkladov", "$mena", "", ["url" => "/klienti/detail/" . $klientPodielnikid . "#" . $cislozmluvy, "cislozmluvy" => $cislozmluvy], ["klientPodielnikid" => "/klienti/detail/" . $klientPodielnikid, "klient" => $klient, "klientMail" => $klientMail], $sumaAlert);

//-----------------------KONVERZIE--------------------------
$cnt = 0;
$konverzie = Connection::getDataFromDatabase($konverzieQuery, defaultDB);
$konverzieCount = $konverzie[0];
$konverzie = $konverzie[1];
$konverzieArray = [];
$konverzieTableData = [];
$konverzieTableColumns = [["Typ konverzie", "Uzavretie konverzie", "Vyrovnanie konverzie", "Menový pár", "Kurz", "Objem nakupovanej meny", "Objem predávanej meny"]];

for ($i = 0; $i < $konverzieCount; $i++) {
    $datum = $konverzie['datum'];
    $datum_cas = $konverzie['datum_cas'];
    $miesto = $konverzie['miesto'];
    $typPokynu = $konverzie['typPokynu'];
    $kurz = $konverzie['kurz'];
    $sumakredit = bcdiv($konverzie['sumakredit'], 1, 2);
    $sumadebet = bcdiv($konverzie['sumadebet'], 1, 2);
    $menakredit = $konverzie['menakredit'];
    $menadebet = $konverzie['menadebet'];
    $druhObchodu = $konverzie['druhObchodu'];

    $rowData = [$druhObchodu, $datum_cas, $datum, $menakredit . ' / ' . $menadebet, round($kurz, 4), $sumakredit, $sumadebet];
    $konverzieArray = $rowData;
    $konverzieTableData[$cnt] = $rowData;
    $cnt++;
}

if (sizeof($konverzieTableData) > 0) {
    klientReportTable::render($konverzieTableData, $konverzieTableColumns, "Prehľad menových konverzií", "", "", ["url" => "/klienti/detail/" . $klientPodielnikid . "#" . $cislozmluvy, "cislozmluvy" => $cislozmluvy], ["klientPodielnikid" => "/klienti/detail/" . $klientPodielnikid, "klient" => $klient, "klientMail" => $klientMail], "");
}


$pohyby = Connection::getDataFromDatabase($pohybyNaUcteQuery, defaultDB);
$pohybyCount = $pohyby[0];
$pohyby = $pohyby[1];
$pohybyTableColumns = [["Uzavretie obchodu", "Transakcia", "Názov CP", "ISIN", "Počet CP", "Mena", "Cena CP", "Cena bez AUV", "AUV", "Cena spolu"], ["Vysporiadanie", "Typ pokynu", "Miesto výkonu", "Druh CP", "", ""]];
$pohybyTableData = [];


for ($i = 0; $i < $pohybyCount; $i++) {
    $nazov = $pohyby['nazov'];
    $isin = $pohyby['isin'];
    $isinreal = $pohyby['isinreal'];
    $pocet = $pohyby['pocet'];
    $mena = $pohyby['mena'];
    $kurz = $pohyby['kurz'];
    $auv = $pohyby['auv'];
    $suma = $pohyby['spolu'];
    $bezAUV = $pohyby['bezAUV'];
    $popis = $pohyby['popis'];
    $typ = $pohyby['eqid'];
    $druh = $pohyby['poddruheq'];
    $datum = $pohyby['datum'];
    $datum = substr($datum, 8, 2) . '.' . substr($datum, 5, 2) . '.' . substr($datum, 0, 4);
    $datum_cas = $pohyby['datum_cas'];
    $miesto = $pohyby['miesto'];
    $typPokynu = $pohyby['typPokynu'];
    $MD_D = $pohyby['MD_D'];

    $rowData = [[$datum_cas, $popis, $nazov, $isinreal, ($pohyby['MD_D'] == 0 ? '' : '-') . round($pocet, 0), $mena, ($pohyby['kurz'] == null ? '' : round($kurz, 4)),
        ($typ == 'Bonds' && $popis != 'Splatenie' ? round($bezAUV, 2) : ""), ($typ == 'Bonds' && $popis != 'Splatenie' ? round($auv, 2) : ""),
        ($pohyby['spolu'] == null ? '' : round($suma, 2))],
        [$datum, $typPokynu, $miesto, $druh]];
}
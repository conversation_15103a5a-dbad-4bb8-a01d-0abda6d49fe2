<div class="bg-gray-100 dark:bg-gray-700 p-4 rounded-lg mb-4">
    <div class="bg-white dark:bg-gray-800 dark:text-gray-100 p-4 rounded-lg border">
        <div class="flex gap-3 mb-4 rounded-lg">
            <section class="flex gap-3 w-full">
                <div
                    class="relative w-14 h-14 overflow-hidden bg-gray-100 dark:bg-gray-800 rounded-full dark:bg-gray-600">
                    <svg class="absolute w-16 h-16 text-gray-400 -left-1" fill="currentColor" viewBox="0 0 20 20"
                        xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                            clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div>
                    <a href="/klienti/detail/<?php echo $klientPodielnikid . "#" . $cislozmluvy; ?>"
                        class="text-lg hover:underline cursor-pointer font-semibold"
                        target="_blank"><?php echo $klient; ?></a>
                    <p class="text-md text-gray-400"><?php echo $klientMail ?></p>
                </div>
            </section>

            <form id="pdfAfterGenerate" method="post" class="mb-0">
                <input type="hidden" name="dateFrom" id="dateFromAfter" value="<?php echo $fromdate; ?>" />
                <input type="hidden" name="dateTill" id="dateTillAfter" value="<?php echo $todate; ?>" />
                <input type="hidden" name="reportDesc" id="reportDescAfter" value="<?php echo $reportDesc; ?>" />
                <input type="hidden" name="fileName" id="fileNameAfter" value="<?php echo $fileName; ?>" />
                <input type="hidden" name="clientvalues" id="clientvaluesAfter" value="<?php echo $client; ?>" />
                <input type="hidden" name="afterPDFGeneration" id="afterPDFGeneration" value="<?php echo true; ?>" />
                <button id="generatePDFAfter" type="submit" class="p-4 bg-gray-100 dark:text-gray-400 dark:bg-gray-900 flex items-center justify-center gap-2 rounded-xl text-center font-semibold text-gray-600 hover:bg-blue-500 transition-all
                         cursor-pointer hover:text-white">
                    <svg class="w-6 h-6" id="afterPDFIcon" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                        width="24" height="24" fill="none" viewBox="0 0 24 24">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M5 17v-5h1.5a1.5 1.5 0 1 1 0 3H5m12 2v-5h2m-2 3h2M5 10V7.914a1 1 0 0 1 .293-.707l3.914-3.914A1 1 0 0 1 9.914 3H18a1 1 0 0 1 1 1v6M5 19v1a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-1M10 3v4a1 1 0 0 1-1 1H5m6 4v5h1.375A1.627 1.627 0 0 0 14 15.375v-1.75A1.627 1.627 0 0 0 12.375 12H11Z" />
                    </svg>
                    <svg aria-hidden="true" id="spinnerPDF"
                        class="w-6 h-6 hidden text-gray-200 animate-spin dark:text-gray-600 fill-blue-600"
                        viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                            fill="currentColor" />
                        <path
                            d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                            fill="currentFill" />
                    </svg>
                </button>
            </form>
            <form id="exportToExcel" method="post" class="mb-0">
                <input type="hidden" name="dateFrom" id="dateFrom" value="<?php echo $fromdate; ?>" />
                <input type="hidden" name="dateTill" id="dateTill" value="<?php echo $todate; ?>" />
                <input type="hidden" name="reportDesc" id="reportDesc" value="<?php echo $reportDesc; ?>" />
                <input type="hidden" name="fileName" id="fileName" value="<?php echo $fileName; ?>" />
                <input type="hidden" name="clientvalues" id="clientvalues" value="<?php echo $klientPodielnikid; ?>" />
                <input type="hidden" name="exportToExcelTrigger" id="exportToExcelTrigger"
                    value="<?php echo true; ?>" />
                <button id="exportToExcelTrigger" type="submit"
                    class="p-4 bg-gray-100 dark:bg-gray-900 flex items-center justify-center gap-2 rounded-xl text-center font-semibold text-gray-600 hover:bg-green-200 transition-all cursor-pointer hover:text-white">
                    <svg id="excelIcon" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="25" height="25"
                        viewBox="0 0 48 48">
                        <path fill="#169154" d="M29,6H15.744C14.781,6,14,6.781,14,7.744v7.259h15V6z"></path>
                        <path fill="#18482a" d="M14,33.054v7.202C14,41.219,14.781,42,15.743,42H29v-8.946H14z"></path>
                        <path fill="#0c8045" d="M14 15.003H29V24.005000000000003H14z"></path>
                        <path fill="#17472a" d="M14 24.005H29V33.055H14z"></path>
                        <g>
                            <path fill="#29c27f" d="M42.256,6H29v9.003h15V7.744C44,6.781,43.219,6,42.256,6z"></path>
                            <path fill="#27663f" d="M29,33.054V42h13.257C43.219,42,44,41.219,44,40.257v-7.202H29z">
                            </path>
                            <path fill="#19ac65" d="M29 15.003H44V24.005000000000003H29z"></path>
                            <path fill="#129652" d="M29 24.005H44V33.055H29z"></path>
                        </g>
                        <path fill="#0c7238"
                            d="M22.319,34H5.681C4.753,34,4,33.247,4,32.319V15.681C4,14.753,4.753,14,5.681,14h16.638 C23.247,14,24,14.753,24,15.681v16.638C24,33.247,23.247,34,22.319,34z">
                        </path>
                        <path fill="#fff"
                            d="M9.807 19L12.193 19 14.129 22.754 16.175 19 18.404 19 15.333 24 18.474 29 16.123 29 14.013 25.07 11.912 29 9.526 29 12.719 23.982z">
                        </path>
                    </svg>
                    <svg aria-hidden="true" id="spinnerExcel"
                        class="w-6 h-6 hidden text-gray-200 animate-spin dark:text-gray-600 fill-blue-600"
                        viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                            fill="currentColor" />
                        <path
                            d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                            fill="currentFill" />
                    </svg>
                </button>
            </form>
        </div>
        <section class="w-full">
            <div class="flex items-center gap-4">
                <a href="#sumarizacia<?php echo $klientPodielnikid . $cislozmluvy ?>"
                    class="p-4 w-full flex items-center justify-center gap-2 bg-gray-100 dark:bg-gray-600 dark:text-gray-100 rounded-xl text-center font-semibold text-gray-600 hover:bg-blue-500 transition-all cursor-pointer hover:text-white hover:shadow-md">
                    <svg class="w-5 h-5 mt-0.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24"
                        height="24" fill="none" viewBox="0 0 24 24">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M11 9h6m-6 3h6m-6 3h6M6.996 9h.01m-.01 3h.01m-.01 3h.01M4 5h16a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V6a1 1 0 0 1 1-1Z" />
                    </svg>
                    <span>Sumárne informácie</span>
                </a>
                <a href="#portfolio<?php echo $klientPodielnikid . $cislozmluvy ?>"
                    class="p-4 w-full flex items-center justify-center gap-2 bg-gray-100 dark:bg-gray-600 dark:text-gray-100 rounded-xl text-center font-semibold text-gray-600 hover:bg-blue-500 transition-all cursor-pointer hover:text-white">
                    <svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                        fill="none" viewBox="0 0 24 24">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M4 4.5V19a1 1 0 0 0 1 1h15M7 14l4-4 4 4 5-5m0 0h-3.207M20 9v3.207" />
                    </svg>
                    <span>Stav majetku</span>
                </a>
                <a href="#transakcie<?php echo $klientPodielnikid . $cislozmluvy ?>"
                    class="p-4 w-full bg-gray-100 flex items-center justify-center dark:bg-gray-600 dark:text-gray-100 gap-2 rounded-xl text-center font-semibold text-gray-600 hover:bg-blue-500 transition-all cursor-pointer hover:text-white">
                    <svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                        fill="none" viewBox="0 0 24 24">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 6.03v13m0-13c-2.819-.831-4.715-1.076-8.029-1.023A.99.99 0 0 0 3 6v11c0 .563.466 1.014 1.03 1.007 3.122-.043 5.018.212 7.97 1.023m0-13c2.819-.831 4.715-1.076 8.029-1.023A.99.99 0 0 1 21 6v11c0 .563-.466 1.014-1.03 1.007-3.122-.043-5.018.212-7.97 1.023" />
                    </svg>
                    <span>Prehľad transakcií</span>
                </a>
                <a href="#danZCP<?php echo $klientPodielnikid . $cislozmluvy ?>"
                    class="p-4 w-full bg-gray-100 dark:bg-gray-600 dark:text-gray-100 flex items-center justify-center gap-2 rounded-xl text-center font-semibold text-gray-600 hover:bg-blue-500 transition-all cursor-pointer hover:text-white">
                    <svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                        fill="none" viewBox="0 0 24 24">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 6.03v13m0-13c-2.819-.831-4.715-1.076-8.029-1.023A.99.99 0 0 0 3 6v11c0 .563.466 1.014 1.03 1.007 3.122-.043 5.018.212 7.97 1.023m0-13c2.819-.831 4.715-1.076 8.029-1.023A.99.99 0 0 1 21 6v11c0 .563-.466 1.014-1.03 1.007-3.122-.043-5.018.212-7.97 1.023" />
                    </svg>
                    <span>Daň z CP</span>
                </a>
            </div>
        </section>
    </div>

    <section class="px-3 border-r border-l pt-6 mr-2 ml-2">
        <?php include "htmlZhodnotenie.php";
        echo $vypis_obsah ?>
    </section>
    <section class="px-3 border-r border-l pt-6 mr-2 ml-2">
        <?php include "htmlPortfolio.php";
        echo $vypis_obsah ?>
    </section>
    <section class="px-3 border-r border-l pt-6 mr-2 ml-2">
        <?php include "htmlTransakcie.php"; ?>
    </section>
    <section class="px-3 border-r border-l pt-6 mr-2 ml-2">
        <?php include "htmlDanZCP.php"; ?>
    </section>
</div>
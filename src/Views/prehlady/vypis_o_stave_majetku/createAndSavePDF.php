<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
require_once('/home/<USER>/www/src/lib/tcpdf/tcpdf.php');
require_once('/home/<USER>/www/src/Components/inputs/multiSelect/multiSelect.php');
include("mypdf_class.php");


error_reporting(E_ALL);
$fromdate = $_POST["dateFrom"];
$todate = $_POST["dateTill"];
$client = $_POST["clientvalues"];
$useNew = isset($_POST["useNew"]);

if (isset($client)) {
    $query = "select *
                from (select m.subjektid                                                                       as fondid,
                             po.podielnikid                                                                    as podielnikid,
                             po.kontaktemail                                                                   as klientmail,
                             case when po.meno is null then po.prieznaz else po.meno || ' ' || po.prieznaz end as klient,
                             f.fondnameall,
                             p.cislozm<PERSON>vy,
                             po.vypisy_heslo,
                             f.refmena,
                             po.podielnikid_polaris,
                             f.fondnameshort
                      from majetoktotal m
                               left join portfolio p on m.subjektid = p.fondid
                               left join podielnik po on p.podielnikid = po.podielnikid
                               left join fonds f on p.fondid = f.fondid
                      where m.datum > '$fromdate'::date
                        and m.datum <= '$todate'::date
                        and m.subjektid > 1
                        AND p.podielnikid IN ($client)
                      group by m.subjektid, case when po.meno is null then po.prieznaz else po.meno || ' ' || po.prieznaz end,
                               f.fondnameall, p.cislozmluvy, po.vypisy_heslo, f.refmena, po.podielnikid_polaris, f.fondnameshort,
                               po.podielnikid) a
                order by a.fondid";
} else {
    //QUERY NA VSETKYCH KLIENTOV
    $query = "select * from (
            select 	m.subjektid as fondid,
            case when po.meno is null then po.prieznaz else po.meno||' '||po.prieznaz end as klient,
            f.fondnameall,p.cislozmluvy,po.vypisy_heslo,f.refmena,po.podielnikid_polaris,f.fondnameshort
            from majetoktotal m
            left join portfolio p on m.subjektid=p.fondid
            left join podielnik po on p.podielnikid=po.podielnikid
            left join fonds f on p.fondid=f.fondid
            where m.datum > '$fromdate'::date and m.datum <= '$todate'::date and m.subjektid>1
            group by m.subjektid,case when po.meno is null then po.prieznaz else po.meno||' '||po.prieznaz end,f.fondnameall,p.cislozmluvy,po.vypisy_heslo,f.refmena,
            po.podielnikid_polaris,f.fondnameshort
            ) a
            order by a.fondid";
}
$querTest = Connection::getDataFromDatabase($query, defaultDB);
$klienti = $querTest[1];

$objectPdf = [];
$klientNames = [];
$fileSummaries = [];
$pdfFilesNP = [];
$pdfFilesPswd = [];
if (!isset($_POST["afterPDFGeneration"])) {
    echo "<h2 class='font-bold dark:text-gray-100 text-3xl mb-4'>Zobrazenie klientského výpisu</h2>";
}

foreach ($klienti as $item) {
    $fondid = $item["fondid"];
    $cislozmluvy = $item["cislozmluvy"];
    $klient = $item["klient"];
    $klientMail = $item["klientmail"];
    $klientPodielnikid = $item["podielnikid"];
    $objectPdf[$klient]['name'] = [];
    if (!in_array($klient, $objectPdf[$klient]['name'])) {
        $objectPdf[$klient]['name'] = $klient;
        $objectPdf[$klient]['cislozmluvy'] = $cislozmluvy;
    }
    $klientdb = str_replace(" ", "_", $klient);
    $vypisy_heslo = $item["vypisy_heslo"];
    $refmena = $item["refmena"];
    $polarisid = $item["podielnikid_polaris"];
    $typ_portfolia = $item["fondnameall"];
    $fnshort = $item["fondnameshort"];
    $cub = "";
    $ucetaktiva = "";
    $k1ucet = "";
    $fileUrlS = [];

    $farbaciara1 = 'black';    //tenka ciara pod hlavickou tabulky
    $farbaciara2 = '#1782c5';    //nazov tabulky
    $farbaciara3 = '#DCE2EA';    //hruba ciara okolo tabulky
    if (isset($_POST["afterPDFGeneration"])) {
        include("vypis_transakcii.php");
        $vypis_obsah = "";
        include("vypis_portfolia.php");
        $vypis_obsah = "";
        include("vypis_zhodnotenie.php");
        $vypis_obsah = "";
        include("vypis_danCP.php");
        $vypis_obsah = "";
        include "renderAndSavePDF.php";
        header('Content-Type: application/json');
        echo json_encode(["myFileSummary" => $myfileSummary, "myFilePswd" => $myfilePswd, "myFile" => $myfile]);
        exit;
    }
    if (isset($_POST["exportToExcelTrigger"])) {
        include("vypis_portfolia.php");
        include("vypis_zhodnotenie.php");
        include("vypisyHTML/htmlTransakcie.php");
        include("exportToXLSX.php");
        exit;
    }
    if (isset($_POST["submitShow"])) {
        include "vypisyHTML/vypisyIndex.php";
    } else {
        include("vypis_transakcii.php");
        $vypis_obsah = "";
        include("vypis_portfolia.php");
        $vypis_obsah = "";
        include("vypis_zhodnotenie.php");
        $vypis_obsah = "";
        include("vypis_danCP.php");
        $vypis_obsah = "";
    }

    if (!isset($_POST["submitShow"])) {
        include "renderAndSavePDF.php";
    }

    if (!isset($_POST["submitShow"])) {
        $prefixToRemove = '/home/<USER>/www'; ?>
        <div class="w-full rounded-lg border p-3 shadow-xl">
            <div class="flex items-center justify-between  w-full pt-2 px-3">
                <strong class="text-xl flex items-center gap-2">
                    <svg class="w-6 h-6 text-green-600" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24"
                        height="24" fill="currentColor" viewBox="0 0 24 24">
                        <path fill-rule="evenodd"
                            d="M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10-4.477 10-10 10S2 17.523 2 12Zm13.707-1.293a1 1 0 0 0-1.414-1.414L11 12.586l-1.793-1.793a1 1 0 0 0-1.414 1.414l2.5 2.5a1 1 0 0 0 1.414 0l4-4Z"
                            clip-rule="evenodd" />
                    </svg>
                    Súbory boli úspešne vygenerované.</strong>
                <div class="hover:bg-gray-200 cursor-pointer p-1 rounded-lg">
                    <svg class="w-4 h-4 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                        width="24" height="24" fill="none" viewBox="0 0 24 24">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M6 18 17.94 6M18 18 6.06 6" />
                    </svg>
                </div>
            </div>
            <div class="p-3 gap-4 flex-wrap grid grid-cols-2">
                <?php
                foreach ($objectPdf as $object) { ?>
                    <div class="flex p-3 rounded-lg flex-col text-black bg-white dark:bg-gray-700 shadow-lg"><span
                            class="!text-black mb-2 font-bold"><?php echo $object['name']; ?></span>
                        <div class="flex gap-2 flex-wrap p-2 bg-gray-300 rounded-lg">
                            <span class="w-full flex gap-2">Súhrny <svg class="w-6 h-6 text-gray-800 dark:text-white"
                                    aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor"
                                    viewBox="0 0 24 24">
                                    <path fill-rule="evenodd"
                                        d="M8 10V7a4 4 0 1 1 8 0v3h1a2 2 0 0 1 2 2v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7a2 2 0 0 1 2-2h1Zm2-3a2 2 0 1 1 4 0v3h-4V7Zm2 6a1 1 0 0 1 1 1v3a1 1 0 1 1-2 0v-3a1 1 0 0 1 1-1Z"
                                        clip-rule="evenodd" />
                                </svg></span>
                            <?php foreach ($object['summaries'] as $item) { ?>
                                <a href="<?php echo str_replace($prefixToRemove, "", $item); ?>" target="_blank"
                                    class="shadow-md p-3 flex flex-col items-center hover:shadow-xl transition-all cursor-pointer bg-white rounded-md">
                                    <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true"
                                        xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor"
                                        viewBox="0 0 24 24">
                                        <path fill-rule="evenodd"
                                            d="M9 2.221V7H4.221a2 2 0 0 1 .365-.5L8.5 2.586A2 2 0 0 1 9 2.22ZM11 2v5a2 2 0 0 1-2 2H4a2 2 0 0 0-2 2v7a2 2 0 0 0 2 2 2 2 0 0 0 2 2h12a2 2 0 0 0 2-2 2 2 0 0 0 2-2v-7a2 2 0 0 0-2-2V4a2 2 0 0 0-2-2h-7Zm-6 9a1 1 0 0 0-1 1v5a1 1 0 1 0 2 0v-1h.5a2.5 2.5 0 0 0 0-5H5Zm1.5 3H6v-1h.5a.5.5 0 0 1 0 1Zm4.5-3a1 1 0 0 0-1 1v5a1 1 0 0 0 1 1h1.376A2.626 2.626 0 0 0 15 15.375v-1.75A2.626 2.626 0 0 0 12.375 11H11Zm1 5v-3h.375a.626.626 0 0 1 .625.626v1.748a.625.625 0 0 1-.626.626H12Zm5-5a1 1 0 0 0-1 1v5a1 1 0 1 0 2 0v-1h1a1 1 0 1 0 0-2h-1v-1h1a1 1 0 1 0 0-2h-2Z"
                                            clip-rule="evenodd" />
                                    </svg>
                                    <span>Súhrn <?php echo $object['cislozmluvy']; ?></span>
                                </a>
                            <?php } ?>
                        </div>
                        <div class="flex gap-2 flex-wrap p-2 mt-2 bg-gray-300 rounded-lg">
                            <span class="w-full">Neheslované súbory</span>
                            <?php foreach ($object['pdfnp'] as $item) { ?>
                                <a href="<?php echo str_replace($prefixToRemove, "", $item); ?>" target="_blank"
                                    class="shadow-md p-3 flex flex-col items-center hover:shadow-xl transition-all cursor-pointer bg-white rounded-md">
                                    <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true"
                                        xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor"
                                        viewBox="0 0 24 24">
                                        <path fill-rule="evenodd"
                                            d="M9 2.221V7H4.221a2 2 0 0 1 .365-.5L8.5 2.586A2 2 0 0 1 9 2.22ZM11 2v5a2 2 0 0 1-2 2H4a2 2 0 0 0-2 2v7a2 2 0 0 0 2 2 2 2 0 0 0 2 2h12a2 2 0 0 0 2-2 2 2 0 0 0 2-2v-7a2 2 0 0 0-2-2V4a2 2 0 0 0-2-2h-7Zm-6 9a1 1 0 0 0-1 1v5a1 1 0 1 0 2 0v-1h.5a2.5 2.5 0 0 0 0-5H5Zm1.5 3H6v-1h.5a.5.5 0 0 1 0 1Zm4.5-3a1 1 0 0 0-1 1v5a1 1 0 0 0 1 1h1.376A2.626 2.626 0 0 0 15 15.375v-1.75A2.626 2.626 0 0 0 12.375 11H11Zm1 5v-3h.375a.626.626 0 0 1 .625.626v1.748a.625.625 0 0 1-.626.626H12Zm5-5a1 1 0 0 0-1 1v5a1 1 0 1 0 2 0v-1h1a1 1 0 1 0 0-2h-1v-1h1a1 1 0 1 0 0-2h-2Z"
                                            clip-rule="evenodd" />
                                    </svg>
                                    <span><?php echo $object['cislozmluvy']; ?></span>
                                </a>
                            <?php } ?>
                        </div>
                        <div class="flex gap-2 flex-wrap p-2 bg-gray-300 mt-2 rounded-lg">
                            <span class="w-full flex gap-2">Heslované súbory <svg class="w-6 h-6 text-gray-800 dark:text-white"
                                    aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor"
                                    viewBox="0 0 24 24">
                                    <path fill-rule="evenodd"
                                        d="M8 10V7a4 4 0 1 1 8 0v3h1a2 2 0 0 1 2 2v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7a2 2 0 0 1 2-2h1Zm2-3a2 2 0 1 1 4 0v3h-4V7Zm2 6a1 1 0 0 1 1 1v3a1 1 0 1 1-2 0v-3a1 1 0 0 1 1-1Z"
                                        clip-rule="evenodd" />
                                </svg>
                            </span>
                            <?php foreach ($object['pdfpswd'] as $item) { ?>
                                <a href="<?php echo str_replace($prefixToRemove, "", $item); ?>" target="_blank"
                                    class="shadow-md p-3 flex flex-col items-center hover:shadow-xl transition-all cursor-pointer bg-white rounded-md">
                                    <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true"
                                        xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor"
                                        viewBox="0 0 24 24">
                                        <path fill-rule="evenodd"
                                            d="M9 2.221V7H4.221a2 2 0 0 1 .365-.5L8.5 2.586A2 2 0 0 1 9 2.22ZM11 2v5a2 2 0 0 1-2 2H4a2 2 0 0 0-2 2v7a2 2 0 0 0 2 2 2 2 0 0 0 2 2h12a2 2 0 0 0 2-2 2 2 0 0 0 2-2v-7a2 2 0 0 0-2-2V4a2 2 0 0 0-2-2h-7Zm-6 9a1 1 0 0 0-1 1v5a1 1 0 1 0 2 0v-1h.5a2.5 2.5 0 0 0 0-5H5Zm1.5 3H6v-1h.5a.5.5 0 0 1 0 1Zm4.5-3a1 1 0 0 0-1 1v5a1 1 0 0 0 1 1h1.376A2.626 2.626 0 0 0 15 15.375v-1.75A2.626 2.626 0 0 0 12.375 11H11Zm1 5v-3h.375a.626.626 0 0 1 .625.626v1.748a.625.625 0 0 1-.626.626H12Zm5-5a1 1 0 0 0-1 1v5a1 1 0 1 0 2 0v-1h1a1 1 0 1 0 0-2h-1v-1h1a1 1 0 1 0 0-2h-2Z"
                                            clip-rule="evenodd" />
                                    </svg>
                                    <span><?php echo $object['cislozmluvy']; ?></span>
                                </a>
                            <?php } ?>
                        </div>
                    </div>
                <?php } ?>
            </div>
        </div>
    <?php }
}
<?php
$id_fond = $fondid;
$dbdate = $todate;
$testhodnota = 0;

$vypis_obsah = '	
		<br><br><br>		
		<table style="width:100%;border-collapse:collapse;">
			<tr  style="width:100%;text-align:center;">
				<td style="height:26.25pt;">
					<div style="color: #6B819B;">
						<span style="font-size: 21pt;font-weight:400;">Výpis o stave majetku</span>
					</div>	
				</td>
			</tr>
			<tr style="width:100%;">
				<td style="height:20pt;min-width:100%;width:100%;"></td>
			</tr>	
			<tr  style="width:100%;text-align:center;">
				<td style="height:26.25pt;vertical-align:bottom;">
					<div  style="color: #000;vertical-align:bottom;">
						<span style="font-size: 9.25pt;font-weight:600;"><b>ku dňu ' . $todate . '</b></span>
					</div>	
				</td>
			</tr>
		</table>
		<br><br>
		<table>
			<tr style="font-weight:normal;color: #0E0E0E;">
				<td style="min-width:4%;width:4%;"></td>
				<td style="vertical-align:bottom;width:71%;text-align:left;height:10pt;"><div style="vertical-align:bottom;"><span style="font-weight:400;font-size:7.5pt;">Klient:    </span><span style="font-weight:700;font-size:9.75pt;">' . $klient . '</span></div></td>
				<td style="vertical-align:bottom;width:20%;text-align:right;height:10pt;"><div style="vertical-align:bottom;"><span style="font-weight:400;font-size:7.5pt;">Portfólio:    </span><span style="font-weight:700;font-size:9.75pt;">' . $cislozmluvy . '</span></div></td>
				<td style="min-width:5%;width:5%;"></td>
			</tr>
		</table>
	';

//---------------hotovost a terminovane vklady---------------
$query = "
		SELECT 
			sum( 
				(sumadenom*sign(0.5-md_d) 
				  + COALESCE(
							(
								select sum(sumadenom*sign(0.5-md_d)) 
								from majetoktotal 
								where datum=mt.datum  and uctovnykod in (315132,315113) and eqid = mt.eqid and subjektid = mt.subjektid
								and kodaktiva = mt.kodaktiva and ucetaktiva = mt.ucetaktiva
							),0
						)
				) * (SELECT kurz FROM f_menovy_kurz_lot(menadenom,'$refmena',datum)) 
			) as suma 
		FROM majetoktotal mt
            LEFT JOIN dbequitycurrric dcr ON dcr.isincurrric = mt.kodaktiva
            LEFT JOIN dbequitycurr dc ON dc.isincurr = dcr.isincurr
            LEFT JOIN dbequity d ON d.isin = dc.isin
        WHERE 
            datum = to_date('$dbdate', 'YYYY-MM-DD') 
            AND uctovnykod IN (221110, 221210) 
            AND mt.eqid IN ('BU', 'TD') 
            AND subjektid = $id_fond;
	";

$connectionRes = Connection::getDataFromDatabase($query, defaultDB);
$p1 = $connectionRes[1][0]["suma"];
$suma_p1 = floatval($p1);

//---------------dlhopisy spolu---------------
$query2 = "
		SELECT 
			sum( (sumadenom*sign(0.5-md_d)) * (SELECT kurz FROM f_menovy_kurz_lot(menadenom,'$refmena',datum))
			) as suma 
		FROM 
            majetoktotal mt
            LEFT JOIN dbequitycurrric dcr ON dcr.isincurrric = mt.kodaktiva
            LEFT JOIN dbequitycurr dc ON dc.isincurr = dcr.isincurr
            LEFT JOIN dbequity d ON d.isin = dc.isin
        WHERE 
            datum = to_date('$dbdate', 'YYYY-MM-DD')
            AND uctovnykod in (251110, 251120)
            AND mt.eqid = 'Bonds'
            AND subjektid = $id_fond;
	";
$connectionRes2 = Connection::getDataFromDatabase($query2, defaultDB);
$d1 = $connectionRes2[1][0]["suma"];
$suma_d1 = floatval($d1);

//---------------akcie---------------
$query3 = "
		SELECT 
				sum( (sumadenom*sign(0.5-md_d)) * (SELECT kurz FROM f_menovy_kurz_lot(menadenom,'$refmena',datum)) 
				) as suma 
			FROM 
                majetoktotal mt
                LEFT JOIN dbequitycurrric dcr ON dcr.isincurrric = mt.kodaktiva
                LEFT JOIN dbequitycurr dc ON dc.isincurr = dcr.isincurr
                LEFT JOIN dbequity d ON d.isin = dc.isin
            WHERE 
                datum = to_date('$dbdate', 'YYYY-MM-DD')
                AND uctovnykod in (251200)
                AND d.druheqid NOT IN (8, 15, 17)
                AND mt.eqid = 'Shares'
                AND subjektid = $id_fond;
	";

$connectionRes3 = Connection::getDataFromDatabase($query3, defaultDB);
$a1 = $connectionRes3[1][0]["suma"];
$suma_a1 = floatval($a1);

//---------------podiely spolu---------------
$query4 = "
			SELECT 
				sum( (sumadenom*sign(0.5-md_d)) * (SELECT kurz FROM f_menovy_kurz_lot(menadenom,'$refmena',datum)) 
				) as suma 
			FROM 
				majetoktotal mt
				LEFT JOIN dbequitycurrric dcr ON dcr.isincurrric = mt.kodaktiva
				LEFT JOIN dbequitycurr dc ON dc.isincurr = dcr.isincurr
				LEFT JOIN dbequity d ON d.isin = dc.isin
			WHERE 
				datum = to_date('$dbdate', 'YYYY-MM-DD')
				AND uctovnykod in (251300, 251200)
				AND (d.eqid = 'Fonds' OR (d.druheqid in (8, 15, 17) AND d.eqid = 'Shares'))
				AND mt.eqid in ('Fonds', 'Shares')
				AND subjektid = $id_fond;
		";

$connectionRes4 = Connection::getDataFromDatabase($query4, defaultDB);
$f1 = $connectionRes4[1][0]["suma"];
$suma_f1 = floatval($f1);

//---------------pohladavky a zavazky spolu---------------
$query5 = "
			SELECT 
				sum((sumadenom*sign(0.5-md_d) 
					   - COALESCE(
								(
									select sum(sumadenom*sign(0.5-md_d)) 
									from majetoktotal 
									where datum=mt.datum and uctovnykod in (315132,315113) and eqid = mt.eqid and subjektid = mt.subjektid
									and kodaktiva = mt.kodaktiva and ucetaktiva = mt.ucetaktiva
									and exists
									(
										select * 
										from majetoktotal 
										where datum=mt.datum and uctovnykod in (221110,221210) and eqid = mt.eqid and subjektid = mt.subjektid
											and kodaktiva = mt.kodaktiva and ucetaktiva = mt.ucetaktiva
									)
								),0
							)
					) * (SELECT kurz FROM f_menovy_kurz_lot(menadenom,'$refmena',datum)) 
				) as suma 
			from 
				majetoktotal mt
			left join dbequitycurrric dcr on dcr.isincurrric = mt.kodaktiva
			left join dbequitycurr dc on dc.isincurr = dcr.isincurr
			left join dbequity d on d.isin = dc.isin
			where datum = to_date('$dbdate', 'YYYY-MM-DD')
			  and uctovnykod in (221110, 221210) 
			  and mt.eqid in ('BU', 'TD') 
			  and subjektid = $fondid;
		";

$connectionRes5 = Connection::getDataFromDatabase($query5, defaultDB);
$paz1 = $connectionRes5[1][0]["suma"];
$suma_paz1 = floatval($paz1);

$total = $p1 + $d1 + $a1 + $f1 + $paz1;
$total_suma = floatval($total);

if ($total == 0)
	$total = 1;        //tu je mozna chyba, ak bude mat klient rovnaku sumu plus aj minus tak mu to vypocita obravky percentualny podieln na portfoliu

$p2 = ($p1 / $total) * 100;
$podiel_p2 = floatval($p2);
$d2 = ($d1 / $total) * 100;
$podiel_d2 = floatval($d2);
$a2 = ($a1 / $total) * 100;
$podiel_a2 = floatval($a2);
$f2 = ($f1 / $total) * 100;
$podiel_f2 = floatval($f2);
$paz2 = ($paz1 / $total) * 100;
$podiel_paz2 = floatval($paz2);

$vypis_obsah = $vypis_obsah . '
		<br><br><br><br>
		<table style="width:100%;border-collapse:collapse;">
			<tr>
				<td>
					<h3 style="color:' . $farbaciara2 . '">Štruktúra portfólia podľa tried aktív</h3>
				</td>
			</tr>
		</table>	
		<br><br>
		<table style="width:100%;border-collapse:collapse;border-top:1pt solid ' . $farbaciara3 . ';border-bottom:1pt solid ' . $farbaciara3 . ';">			
				<tr>
					<th style="width:60%;text-align:left;font-size:8;" ><b>Trieda aktív</b></th>
					<th style="width:20%;text-align:right;font-size:8;" ><b>Objem v ' . $refmena . '</b></th>
					<th style="width:20%;text-align:right;font-size:8;" ><b>Podiel v %</b></th>
				</tr>
				<tr style="width:100%;line-height:1pt;">
					<td colspan="3" style="border-bottom:0.1mm solid ' . $farbaciara1 . ';height:1pt;min-width:100%;width:100%;"></td>
				</tr>
				<tr style="width:100%;line-height:1pt;">
					<td colspan="3" style="height:1pt;min-width:100%;width:100%;"></td>
				</tr>
	';

if ($suma_p1 != 0) {
	$vypis_obsah = $vypis_obsah . '
				<tr>
					<td style="width:60%;text-align:left;font-size:8;">Peňažné prostriedky</td>
					<td style="width:20%;text-align:right;font-size:8;">' . $suma_p1 . '</td>
					<td style="width:20%;text-align:right;font-size:8;">' . $podiel_p2 . '</td>
				</tr>
		';
}
if ($suma_d1 != 0) {
	$vypis_obsah = $vypis_obsah . '
				<tr>
					<td style="width:60%;text-align:left;font-size:8;">Dlhopisy</td>
					<td style="width:20%;text-align:right;font-size:8;">' . $suma_d1 . '</td>
					<td style="width:20%;text-align:right;font-size:8;">' . $podiel_d2 . '</td>					
				</tr>
		';
}
if ($suma_a1 != 0) {
	$vypis_obsah = $vypis_obsah . '
				<tr>
					<td style="width:60%;text-align:left;font-size:8;">Akcie, ADR a GDR</td>
					<td style="width:20%;text-align:right;font-size:8;">' . $suma_a1 . '</td>
					<td style="width:20%;text-align:right;font-size:8;">' . $podiel_a2 . '</td>					
				</tr>
		';
}
if ($suma_f1 != 0) {
	$vypis_obsah = $vypis_obsah . '
				<tr>
					<td style="width:60%;text-align:left;font-size:8;">Podielové listy a ETF</td>
					<td style="width:20%;text-align:right;font-size:8;">' . $suma_f1 . '</td>
					<td style="width:20%;text-align:right;font-size:8;">' . $podiel_f2 . '</td>					
				</tr>
		';
}
if ($suma_paz1 != 0) {
	$vypis_obsah = $vypis_obsah . '
				<tr>
					<td style="width:60%;text-align:left;font-size:8;">Záväzky a pohľadávky</td>
					<td style="width:20%;text-align:right;font-size:8;">' . $suma_paz1 . '</td>
					<td style="width:20%;text-align:right;font-size:8;">' . $podiel_paz2 . '</td>					
				</tr>
		';
}

$vypis_obsah = $vypis_obsah . '
				<tr>
					<td style="width:60%;text-align:left;font-size:8;"><b>Trhová hodnota celkom</b></td>
					<td style="width:20%;text-align:right;font-size:8;"><b>' . $total_suma . '</b></td>
					<td style="width:20%;text-align:right;font-size:8;"><b>100,00</b></td>					
				</tr>
				<tr style="width:100%;line-height:1pt;">
					<td colspan="3" style="height:1pt;min-width:100%;width:100%;"></td>
				</tr>
		</table>
	';

//---------------hotovost a terminovane vklady detail---------------
$query6 = "
		SELECT 		
			mt.ucetaktiva as ucetaktiva,  subjektid, eqid,  menadenom, sumadenom*sign(0.5-md_d) as sumadenom, 			
			(SELECT kurz FROM f_menovy_kurz_lot(menadenom,'$refmena',to_date('$dbdate','YYYY-MM-DD'))) as kurz, 
			sumadenom*sign(0.5-md_d) * (SELECT kurz FROM f_menovy_kurz_lot(menadenom,'$refmena',to_date('$dbdate','YYYY-MM-DD'))) as sumaref,
			COALESCE(
					(select sumadenom*sign(0.5-md_d) from majetoktotal where 
						subjektid=mt.subjektid
						and datum=mt.datum
						and uctovnykod in (315132,315113)
						and eqid = mt.eqid
						and kodaktiva = mt.kodaktiva
						and ucetaktiva = mt.ucetaktiva
					 ),0
				) as auv, 
			COALESCE(
					(select sumadenom*sign(0.5-md_d) * (SELECT kurz FROM f_menovy_kurz_lot(menadenom,'$refmena',to_date('$dbdate','YYYY-MM-DD'))) from majetoktotal where 
						subjektid=mt.subjektid
						and datum=mt.datum
						and uctovnykod in (315132,315113)
						and eqid = mt.eqid
						and kodaktiva = mt.kodaktiva
						and ucetaktiva = mt.ucetaktiva
					),0
				) as auvref 
		from 
			majetoktotal mt
		where 
			datum=to_date('$dbdate','YYYY-MM-DD') and eqid in ('BU','TD') and uctovnykod in (221110, 221210)
			and subjektid=$id_fond
		order by eqid, menadenom
	";

$connectionRes6 = Connection::getDataFromDatabase($query6, defaultDB);
$result6 = $connectionRes6[1];

$penazeArray = [];
$cnt = 0;
foreach ($result6 as $item) {
	if ($cnt == 0) {
		$vypis_obsah = $vypis_obsah . '
				<br><br><br>
				<table style="width:100%;border-collapse:collapse;">					
					<tr>
						<td>
							<h3 style="color:' . $farbaciara2 . '">Peňažné prostriedky</h3>
						</td>
					</tr>
				</table>
				<br><br>
				<table style="width:100%;border-collapse:collapse;border-top:1pt solid ' . $farbaciara3 . ';border-bottom:1pt solid ' . $farbaciara3 . ';">
					<tr>
							<th style="width:14%;text-align:left;font-size:8;"><b>Typ účtu</b></th>
							<th style="width:6%;text-align:right;font-size:8;"><b>Mena</b></th>
							<th style="width:10%;text-align:right;font-size:8;"><b>Dátum<BR>zriadenia TV</b></th>
							<th style="width:10%;text-align:right;font-size:8;"><b>Dátum<BR>splatnosti TV</b></th>
							<th style="width:13%;text-align:right;font-size:8;"><b>Objem</b></th>
							<th style="width:10%;text-align:center;font-size:8;"><b>AUV</b></th>
							<th style="width:8%;text-align:right;font-size:8;"><b>Úroková<BR>sadzba</b></th>
							<th style="width:7%;text-align:right;font-size:8;"><b>Kurz</b></th>
							<th style="width:13%;text-align:right;font-size:8;"><b>Objem<br>v ' . $refmena . '</b></th>
							<th style="width:9%;text-align:right;font-size:8;"><b>Podiel<br>v %</b></th>
					</tr>
					<tr style="width:100%;line-height:1pt;">
						<td colspan="10" style="border-bottom:0.1mm solid ' . $farbaciara1 . ';height:1pt;min-width:100%;width:100%;"></td>
					</tr>
					<tr style="width:100%;line-height:1pt;">
						<td colspan="10" style="height:1pt;min-width:100%;width:100%;"></td>
					</tr>
			';
	}
	$cnt++;

	if ($item["eqid"] == 'BU') {
		if (substr($item["ucetaktiva"], -9) == 'kolateral') {
			$typ = 'Bežný účet - kolaterál';
		} else {
			$typ = 'Bežný účet';
		}
	} else
		$typ = 'Termínovaný vklad';

	$obj = intval($item["sumaref"]) + intval($item["auvref"]);
	$auv = floatval($item["auv"]);
	$p = ($obj / $total) * 100;
	$podiel = floatval($p);
	$mena = $item["menadenom"];
	$objem = floatval($item["sumadenom"]);
	$objem_ref = floatval($obj);
	$menovykurz = floatval($item["kurz"]);
	$subjektid = $item["subjektid"];
	$ucetaktiva = $item["ucetaktiva"];

	$z_td = "";
	$k_td = "";
	$ir_td = "";
	if ($typ == 'Termínované vklad') {
		$query7 = " select z_td, k_td, ir_td 
						from konfirmaciaktv 
						where 
							subjektid = $subjektid  and Z_TD <= to_date('$dbdate','YYYY-MM-DD')						
							and K_TD >= to_date('$dbdate','YYYY-MM-DD')  and CUTD = '$ucetaktiva' and rownum = 1
			";

		$connectionRes7 = Connection::getDataFromDatabase($query7, defaultDB);
		$result7 = $connectionRes7[1];

		if (empty($result7))        // ak sme nic nenasli, pozreme este cez pool
		{
			$query8 = "	select z_td, k_td, ir_td 
							from konfirmaciaktv k, pool p, pooldetailreal pdr 
							where 
								k.subjektid = 0  and k.Z_TD <= to_date('$dbdate','YYYY-MM-DD') and k.K_TD >= to_date('$dbdate','YYYY-MM-DD') 
								and k.CUTD = '$ucetaktiva' and k.dealid = p.dealid and pdr.poolid = p.poolid and pdr.subjektid = $subjektid and rownum = 1
				";

			$connectionRes8 = Connection::getDataFromDatabase($query8, defaultDB);
			$result8 = $connectionRes8[1][0];
		}
		$z_td = gmdate(L_DATEFORMATZERO, fromDBDate($result8["z_td"]));
		$k_td = gmdate(L_DATEFORMATZERO, fromDBDate($result8["k_td"]));
		$ir_td = $result8["ir_td"];
		$ir_td = floatval($ir_td);
	}
	$penazeArray[] = [$typ, $mena, $z_td, $k_td, round($objem, 2), $auv, $ir_td, $menovykurz, round($objem_ref, 2), round($podiel, 4)];
	$vypis_obsah = $vypis_obsah . '
			<tr >
				<td style="width:14%;text-align:left;font-size:8;">' . $typ . '</td>
				<td style="width:6%;text-align:right;font-size:8;">' . $mena . '</td>
				<td style="width:10%;text-align:right;font-size:8;">' . $z_td . '</td>
				<td style="width:10%;text-align:right;font-size:8;">' . $k_td . '</td>
				<td style="width:13%;text-align:right;font-size:8;">' . $objem . '</td>
				<td style="width:10%;text-align:center;font-size:8;">' . $auv . '</td>
				<td style="width:8%;text-align:right;font-size:8;">' . $ir_td . '</td>
				<td style="width:7%;text-align:right;font-size:8;">' . $menovykurz . '</td>
				<td style="width:13%;text-align:right;font-size:8;">' . $objem_ref . '</td>
				<td style="width:9%;text-align:right;font-size:8;">' . $podiel . '</td>
			</tr>
		';
}

if ($cnt != 0) {
	$vypis_obsah = $vypis_obsah . '
				<tr>
					<td style="width:14%;text-align:left;font-size:8;"><b>Spolu</b></td>
					<td style="width:6%;text-align:right;font-size:8;"></td>
					<td style="width:10%;text-align:right;font-size:8;"></td>
					<td style="width:10%;text-align:right;font-size:8;"></td>
					<td style="width:13%;text-align:right;font-size:8;"></td>
					<td style="width:10%;text-align:right;font-size:8;"></td>
					<td style="width:8%;text-align:right;font-size:8;"></td>
					<td style="width:7%;text-align:right;font-size:8;"></td>
					<td style="width:13%;text-align:right;font-size:8;"><b>' . $suma_p1 . '</b></td>
					<td style="width:9%;text-align:right;font-size:8;"><b>' . $podiel_p2 . '</b></td>
				</tr>
				<tr style="width:100%;line-height:1pt;">
					<td colspan="10" style="height:1pt;min-width:100%;width:100%;"></td>
				</tr>				
			</table>
		';
}

//---------------dlhopisy detail---------------
$queryDlhopis = "
		select 
		        COALESCE(d.nominalemisie * COALESCE(f.faktor, 1), 1) as nominalemisie,
				d.cpnaz, menadenom as mena, d.isinreal as isinreal, d.isin, 
				d.maturitydate as datsplatnosti, 
				d.kupon, pocty.pocet, pocty.objemsauv, pocty.objemref, pocty.kurz, pocty.auv, pocty.kurz_mena
		from (
			select 	sum(case when md_d=0 then pocet else (-1)*pocet end) as pocet, 					
					max(kurzaktiva) as kurz, 
					sum((select sum(case when mt2.md_d=0 then mt2.sumadenom else (-1)*mt2.sumadenom end)
						from majetoktotal mt2 where mt.ucetaktiva=mt2.ucetaktiva and
						mt2.KODAKTIVA=mt.kodaktiva and mt.datum=mt2.datum and mt.subjektid=mt2.subjektid and
						mt2.uctovnykod in (251120) )
						) as auv,
					sum((select sum(case when mt2.md_d=0 then mt2.sumadenom else (-1)*mt2.sumadenom end)
						from majetoktotal mt2 where mt.ucetaktiva=mt2.ucetaktiva and
						mt2.KODAKTIVA=mt.kodaktiva and mt.datum=mt2.datum and mt.subjektid=mt2.subjektid and
						mt2.uctovnykod in (251110) )
						) as objemsauv,
					sum((select sum((case when mt2.md_d=0 then mt2.sumadenom else (-1)*mt2.sumadenom end) * 
						(SELECT kurz FROM f_menovy_kurz_lot(menadenom, '$refmena', mt2.datum)))
						from majetoktotal mt2 where mt.ucetaktiva=mt2.ucetaktiva and
						mt2.KODAKTIVA=mt.kodaktiva and mt.datum=mt2.datum and mt.subjektid=mt2.subjektid and
						mt2.uctovnykod in (251110, 251120) )
						) as objemref,
					kodaktiva,  menadenom, 
					max((SELECT kurz FROM f_menovy_kurz_lot(menadenom, '$refmena', mt.datum))) as kurz_mena
			from 
				majetoktotal mt
			where 
				mt.datum=to_date('$dbdate','YYYY-MM-DD') and mt.eqid in ('Bonds') and mt.uctovnykod in (251110) and	mt.subjektid in ($id_fond)
			GROUP BY mt.kodaktiva,menadenom
		) pocty, dbequity d, dbequitycurr c, dbequitycurrric r, floatkupon f
		
		where pocty.kodaktiva=r.isincurrric and r.isincurr=c.isincurr and d.isin=c.isin AND pocty.kodaktiva = f.isincurrric AND f.datefrom <= '$dbdate' AND f.datetill >= '$dbdate'
		ORDER BY d.isinreal
	";
$connectionDlhopisy = Connection::getDataFromDatabase($queryDlhopis, defaultDB);
$dlhopisy = $connectionDlhopisy[1];

$dlhopisyArray = [];
$cnt = 0;
foreach ($dlhopisy as $item) {
	if ($cnt == 0) {
		$vypis_obsah = $vypis_obsah . '
				<br><br><br>
				<table style="width:100%;border-collapse:collapse;">					
					<tr>
						<td>
							<h3 style="color:' . $farbaciara2 . '">Dlhopisy</h3>
						</td>
					</tr>
				</table>
				<br><br>
				<table style="width:100%;border-collapse:collapse;border-top:1pt solid ' . $farbaciara3 . ';border-bottom:1pt solid ' . $farbaciara3 . ';">
					<tr>
							<th style="width:13%;text-align:left;font-size:8;"><b>Názov</b></th>
							<th style="width:13%;text-align:left;font-size:8;"><b>ISIN</b></th>
							<th style="width:7%;text-align:right;font-size:8;"><b>Počet <BR> kusov</b></th>
							<th style="width:9%;text-align:right;font-size:8;"><b>Aktuál-<BR>na cena</b></th>
							<th style="width:13%;text-align:right;font-size:8;"><b>Objem</b></th>
							<th style="width:6%;text-align:right;font-size:8;"><b>Mena</b></th>
							<th style="width:10%;text-align:right;font-size:8;"><b>AUV</b></th>
							<th style="width:7%;text-align:right;font-size:8;"><b>Kurz</b></th>
							<th style="width:13%;text-align:right;font-size:8;"><b>Objem s AUV<br>v ' . $refmena . '</b></th>
							<th style="width:9%;text-align:right;font-size:8;"><b>Podiel<br>v %</b></th>
					</tr>
					<tr style="width:100%;line-height:1pt;">
						<td colspan="10" style="border-bottom:0.1mm solid ' . $farbaciara1 . ';height:1pt;min-width:100%;width:100%;"></td>
					</tr>
					<tr style="width:100%;line-height:1pt;">
						<td colspan="10" style="height:1pt;min-width:100%;width:100%;"></td>
					</tr>
			';
	}
	$cnt++;

	$nazov = $item["cpnaz"];
	$mena = $item["mena"];
	$kurz = $item["kurz"];
	$kurz_mena = $item["kurz_mena"];
	$isinreal = $item["isinreal"];
	$pocet = $item["pocet"];
	$auv = floatval($item["auv"]);
	$objemsauv = floatval($item["objemsauv"]);//bez AUV
	$obj = $item["objemref"];
	$p = ($obj / $total) * 100;
	$podiel = floatval($p);
	$objemref = floatval($item["objemref"]);
	$dlhopisyArray[] = [$nazov, $isinreal, $pocet, floatval($kurz), $objemsauv, $mena, $auv, floatval($kurz_mena), $objemref, $podiel];
	$vypis_obsah = $vypis_obsah . '
			<tr>
				<td style="width:13%;text-align:left;font-size:8;font-weight:700;">' . $nazov . '</td>
				<td style="width:13%;text-align:left;font-size:8;">' . $isinreal . '</td>
				<td style="width:7%;text-align:right;font-size:8;">' . $pocet . '</td>
				<td style="width:9%;text-align:right;font-size:8;">' . floatval($kurz) . '</td>
				<td style="width:13%;text-align:right;font-size:8;">' . $objemsauv . '</td>
				<td style="width:6%;text-align:right;font-size:8;">' . $mena . '</td>
				<td style="width:10%;text-align:right;font-size:8;">' . $auv . '</td> 
				<td style="width:7%;text-align:right;font-size:8;">' . floatval($kurz_mena) . '</td>
				<td style="width:13%;text-align:right;font-size:8;">' . $objemref . '</td>
				<td style="width:9%;text-align:right;font-size:8;">' . $podiel . '</td>
			</tr>
		';
}

if ($cnt != 0) {
	$vypis_obsah = $vypis_obsah . '			
					<tr>
						<td style="width:13%;text-align:left;font-size:8;"><b>Spolu</b></td>
						<td style="width:13%;text-align:left;font-size:8;"></td>
						<td style="width:7%;text-align:right;font-size:8;"></td>
						<td style="width:9%;text-align:right;font-size:8;"></td>
						<td style="width:13%;text-align:rightfont-size:8;;"></td>
						<td style="width:6%;text-align:right;font-size:8;"></td>
						<td style="width:10%;text-align:right;font-size:8;"></td>
						<td style="width:7%;text-align:right;font-size:8;"></td>
						<td style="width:13%;text-align:right;font-size:8;"><b>' . $suma_d1 . '</b></td>
						<td style="width:9%;text-align:right;font-size:8;"><b>' . $podiel_d2 . '</b></td>
					</tr>
					<tr style="width:100%;line-height:1pt;">
						<td colspan="10" style="height:1pt;min-width:100%;width:100%;"></td>
					</tr>
			</table>
		';
}

//---------------akcie detail---------------
$query9 = "select d.cpnaz, menadenom as mena, d.isinreal, pocty.pocet, pocty.kurz,
					pocty.objem, pocty.objemref, pocty.kurz_mena				
			from (
				SELECT sum(case when md_d=0 then pocet else (-1)*pocet end) as pocet, 
					sum(case when md_d=0 then sumadenom else (-1)*sumadenom end) as objem, 
					sum((case when md_d=0 then sumadenom else (-1)*sumadenom end) * 
						(SELECT kurz FROM f_menovy_kurz_lot(menadenom, '$refmena', datum))) as objemref,
					kodaktiva,menadenom,max(kurzaktiva) as kurz, max((SELECT * FROM f_menovy_kurz_lot(menadenom, '$refmena', mt.datum))) as kurz_mena
				from
					majetoktotal mt, dbequitycurrric dcr, dbequitycurr dc, dbequity d
				where
					mt.datum=to_date('$dbdate','YYYY-MM-DD') and mt.uctovnykod in (251200) and mt.subjektid=$id_fond and
					dcr.isincurrric = mt.kodaktiva and dc.isincurr = dcr.isincurr and d.isin = dc.isin and
					d.druheqid not in (8,15,17) -- neberieme tu ETF a index certif
				group by mt.kodaktiva, menadenom
			) pocty, dbequity d, dbequitycurr c, dbequitycurrric r
			where pocty.kodaktiva=r.isincurrric and d.isin=c.isin and r.isincurr=c.isincurr 
			ORDER BY d.isinreal
	";

$connectionRes9 = Connection::getDataFromDatabase($query9, defaultDB);
$result9 = $connectionRes9[1];

$akcieArray = [];
$cnt = 0;
foreach ($result9 as $item) {
	if ($cnt == 0) {
		$vypis_obsah = $vypis_obsah . '
				<br><br><br>
				<table style="width:100%;border-collapse:collapse;">					
					<tr>
						<td>
							<h3 style="color:' . $farbaciara2 . '">Akcie, ADR a GDR</h3>
						</td>
					</tr>
				</table>
				<br><br>
				<table style="width:100%;border-collapse:collapse;border-top:1pt solid ' . $farbaciara3 . ';border-bottom:1pt solid ' . $farbaciara3 . ';">					
					<tr>
						<th style="width:21%;text-align:left;font-size:8;"><b>Názov</b></th>
						<th style="width:13%;text-align:left;font-size:8;"><b>ISIN</b></th>
						<th style="width:7%;text-align:right;font-size:8;"><b>Počet<BR>kusov</b></th>
						<th style="width:9%;text-align:right;font-size:8;"><b>Aktuálna<BR>cena</b></th>
						<th style="width:14%;text-align:right;font-size:8;"><b>Objem</b></th>
						<th style="width:6%;text-align:right;font-size:8;"><b>Mena</b></th>
						<th style="width:7%;text-align:right;font-size:8;"><b>Kurz</b></th>
						<th style="width:14%;text-align:right;font-size:8;"><b>Objem<BR>v ' . $refmena . '</b></th>
						<th style="width:9%;text-align:right;font-size:8;"><b>Podiel<br>v %</b></th>
					</tr>
					<tr style="width:100%;line-height:1pt;">
						<td colspan="9" style="border-bottom:0.1mm solid ' . $farbaciara1 . ';height:1pt;min-width:100%;width:100%;"></td>
					</tr>
					<tr style="width:100%;line-height:1pt;">
						<td colspan="9" style="height:1pt;min-width:100%;width:100%;"></td>
					</tr>
			';
	}
	$cnt++;

	$nazov = $item["cpnaz"];
	$mena = $item["mena"];
	$isinreal = $item["isinreal"];
	$pocet = $item["pocet"];
	$objem = $item["objem"];
	$kurz = $item["kurz"];
	$kurz_mena = $item["kurz_mena"];
	$obj = $item["objemref"];
	$p = ($obj / $total) * 100;
	$podiel = floatval($p);
	$objem = floatval($item["objem"]);
	$objemref = floatval($item["objemref"]);

	$akcieArray[] = [$nazov, $isinreal, $pocet, floatval($kurz), $objem, $mena, floatval($kurz_mena), $objemref, $podiel];
	$vypis_obsah = $vypis_obsah . '
			<tr>
				<td style="width:21%;text-align:left;font-size:8;font-weight:700;">' . $nazov . '</td>
				<td style="width:13%;text-align:left;font-size:8;">' . $isinreal . '</td>
				<td style="width:7%;text-align:right;font-size:8;">' . $pocet . '</td>
				<td style="width:9%;text-align:right;font-size:8;">' . floatval($kurz) . '</td>
				<td style="width:14%;text-align:right;font-size:8;">' . $objem . '</td>
				<td style="width:6%;text-align:right;font-size:8;">' . $mena . '</td>
				<td style="width:7%;text-align:right;font-size:8;">' . floatval($kurz_mena) . '</td>
				<td style="width:14%;text-align:right;font-size:8;">' . $objemref . '</td>
				<td style="width:9%;text-align:right;font-size:8;">' . $podiel . '</td>
			</tr>
		';
}

if ($cnt != 0) {
	$vypis_obsah = $vypis_obsah . '
			<tr>
					<td style="width:21%;text-align:left;font-size:8;"><b>Spolu</b></td>
					<td style="width:13%;text-align:left;font-size:8;"></td>
					<td style="width:6%;text-align:right;font-size:8;"></td>
					<td style="width:9%;text-align:right;font-size:8;"></td>
					<td style="width:8%;text-align:right;font-size:8;"></td>
					<td style="width:14%;text-align:right;font-size:8;"></td>
					<td style="width:7%;text-align:right;font-size:8;"></td>
					<td style="width:14%;text-align:right;font-size:8;"><b>' . $suma_a1 . '</b></td>
					<td style="width:9%;text-align:right;font-size:8;"><b>' . $podiel_a2 . '</b></td>
				</tr>
				<tr style="width:100%;line-height:1pt;">
					<td colspan="9" style="height:1pt;min-width:100%;width:100%;"></td>
				</tr>
			</table>
		';
}

//---------------podiely detail---------------
$queryPodiely = "select d.cpnaz, menadenom as mena, d.isinreal, pocty.pocet, pocty.kurz,
						pocty.objem, pocty.objemref, pocty.kurz_mena
				 from (
					SELECT sum(case when md_d=0 then pocet else (-1)*pocet end) as pocet,
							sum(case when md_d=0 then sumadenom else (-1)*sumadenom end) as objem, 
							sum((case when md_d=0 then sumadenom else (-1)*sumadenom end) * 
								(SELECT kurz FROM f_menovy_kurz_lot(menadenom, '$refmena', datum))) as objemref,
							kodaktiva,menadenom, max(kurzaktiva) as kurz, max((SELECT kurz FROM f_menovy_kurz_lot(menadenom, '$refmena', mt.datum))) as kurz_mena
					from
						majetoktotal mt, dbequitycurrric dcr, dbequitycurr dc, dbequity d
					where
						mt.datum=to_date('$dbdate','YYYY-MM-DD') and mt.uctovnykod in (251200,251300) and mt.subjektid=$id_fond and
						dcr.isincurrric = mt.kodaktiva and dc.isincurr = dcr.isincurr and d.isin = dc.isin and
						(d.eqid = 'Fonds' or (d.druheqid in (8,15,17) and d.eqid='Shares' ))
					group by mt.kodaktiva,mt.menadenom
				) pocty, dbequity d, dbequitycurr c, dbequitycurrric r
				where pocty.kodaktiva=r.isincurrric and d.isin=c.isin and r.isincurr=c.isincurr 
	";

$connectionPodiely = Connection::getDataFromDatabase($queryPodiely, defaultDB);
$podiely = $connectionPodiely[1];

$podieloveArray = [];
$cnt = 0;
foreach ($podiely as $item) {
	if ($cnt == 0) {
		$vypis_obsah = $vypis_obsah . '
				<br><br><br>
				<table style="width:100%;border-collapse:collapse;">
					<tr>
							<td>
								<h3 style="color:' . $farbaciara2 . '">Podielové listy a ETF</h3>
							</td>
					</tr>
				</table>
				<br><br>
				<table style="width:100%;border-collapse:collapse;border-top:1pt solid ' . $farbaciara3 . ';border-bottom:1pt solid ' . $farbaciara3 . ';">					
					<tr>
						<th style="width:21%;text-align:left;font-size:8;"><b>Názov</b></th>
						<th style="width:13%;text-align:left;font-size:8;"><b>ISIN</b></th>
						<th style="width:7%;text-align:right;font-size:8;"><b>Počet <BR> kusov</b></th>
						<th style="width:9%;text-align:right;font-size:8;"><b>Aktuálna<BR>cena</b></th>
						<th style="width:14%;text-align:right;font-size:8;"><b>Objem</b></th>
						<th style="width:6%;text-align:right;font-size:8;"><b>Mena</b></th>
						<th style="width:7%;text-align:right;font-size:8;"><b>Kurz</b></th>
						<th style="width:14%;text-align:right;font-size:8;"><b>Objem<BR>v ' . $refmena . '</b></th>
						<th style="width:9%;text-align:right;font-size:8;"><b>Podiel<br>v %</b></th>
					</tr>
					<tr style="width:100%;line-height:1pt;">
						<td colspan="9" style="border-bottom:0.1mm solid ' . $farbaciara1 . ';height:1pt;min-width:100%;width:100%;"></td>
					</tr>
					<tr style="width:100%;line-height:1pt;">
						<td colspan="9" style="height:1pt;min-width:100%;width:100%;"></td>
					</tr>
			';
	}
	$cnt++;

	$nazov = $item["cpnaz"];
	$mena = $item["mena"];
	$isinreal = $item["isinreal"];
	$pocet = $item["pocet"];
	$kurz = $item["kurz"];
	$kurz_mena = $item["kurz_mena"];
	$objem = $item["objem"];
	$obj = $item["objemref"];
	$p = ($obj / $total) * 100;
	$podiel = floatval($p);
	$objem = floatval($item["objem"]);
	$objemref = floatval($item["objemref"]);

	$podieloveArray[] = [$nazov, $isinreal, $pocet, floatval($kurz), $objem, $mena, floatval($kurz_mena), $objemref, $podiel];
	$vypis_obsah = $vypis_obsah . '	
			<tr>
				<td style="width:21%;text-align:left;font-size:8;font-weight:700;">' . $nazov . '</td>
				<td style="width:13%;text-align:left;font-size:8;">' . $isinreal . '</td>
				<td style="width:7%;text-align:right;font-size:8;">' . $pocet . '</td>
				<td style="width:9%;text-align:right;font-size:8;">' . floatval($kurz) . '</td>
				<td style="width:14%;text-align:right;font-size:8;">' . $objem . '</td>
				<td style="width:6%;text-align:right;font-size:8;">' . $mena . '</td>
				<td style="width:7%;text-align:right;font-size:8;">' . floatval($kurz_mena) . '</td>
				<td style="width:14%;text-align:right;font-size:8;">' . $objemref . '</td>
				<td style="width:9%;text-align:right;font-size:8;">' . $podiel . '</td>
			</tr>
		';
}

if ($cnt != 0) {
	$vypis_obsah = $vypis_obsah . '
			<tr>
					<td style="width:21%;text-align:left;font-size:8;"><b>Spolu</b></td>
					<td style="width:13%;text-align:left;font-size:8;"></td>
					<td style="width:7%;text-align:right;font-size:8;"></td>
					<td style="width:9%;text-align:right;font-size:8;"></td>
					<td style="width:14%;text-align:right;font-size:8;"></td>
					<td style="width:6%;text-align:right;font-size:8;"></td>
					<td style="width:7%;text-align:right;font-size:8;"></td>
					<td style="width:14%;text-align:right;font-size:8;"><b>' . $suma_f1 . '</b></td>
					<td style="width:9%;text-align:right;font-size:8;"><b>' . $podiel_f2 . '</b></td>
				</tr>
				<tr style="width:100%;line-height:1pt;">
					<td colspan="9" style="height:1pt;min-width:100%;width:100%;"></td>
				</tr>
			</table>
		';
}

//---------------pohladavky a zavazky detail---------------
$queryPohladavky = "
		SELECT 
				n.popis, (CASE mt.eqid WHEN 'BU' THEN 0 WHEN 'TD' THEN mt.pocet END) as pocet,
				mt.ucetaktiva, mt.subjektid,mt.eqid, menadenom, sumadenom*sign(0.5-md_d) as sumadenom, menaref, 
				(SELECT * FROM f_menovy_kurz_lot(menadenom,'$refmena',to_date('$dbdate','YYYY-MM-DD'))) as kurz, 
				(SELECT kurz FROM f_menovy_kurz_lot(menadenom,'$refmena',to_date('$dbdate','YYYY-MM-DD'))) as kurz_lot,
				sumadenom*sign(0.5-md_d)*(SELECT kurz FROM f_menovy_kurz_lot(menadenom,'$refmena',to_date('$dbdate','YYYY-MM-DD'))) as sumaref,
				case  	when (mt.eqid in ('Bonds','Fonds','Shares','Depo') or mt.uctovnykod in(315601,315602,315603,315604,315605,315606,315607,315608,315609)) then d.cpnaz
						else mt.kodaktiva end as kodaktiva,
				COALESCE(
					( case when mt.uctovnykod in (315121,325121)
								then (	select sum(sign(0.5-md_d)*mt2.sumadenom)
										from majetoktotal mt2 
										where 
											mt.ucetaktiva = mt2.ucetaktiva and mt.kodaktiva =  mt2.kodaktiva  and  mt.datum = mt2.datum and 
											mt.subjektid = mt2.subjektid and mt.uctovnykod+1 = mt2.uctovnykod and mt.eqid = mt2.eqid
									)
							else 0 end 
					),0) as auvdenom
		FROM majetoktotal mt
        JOIN navuctovanie n ON mt.uctovnykod = n.uctovnykod
        LEFT JOIN dbequity d ON d.isin = SUBSTRING(mt.kodaktiva FROM 1 FOR 12)
        WHERE mt.datum = TO_DATE('$dbdate', 'YYYY-MM-DD')
          AND (CAST(n.uctovnykod AS TEXT) LIKE '315%' OR CAST(n.uctovnykod AS TEXT) LIKE '325%' OR CAST(n.uctovnykod AS TEXT) LIKE '261%')
          AND mt.uctovnykod NOT IN (315122, 325122) -- auv je zaratane k dlhopisu
          AND NOT EXISTS (
              SELECT 1
              FROM majetoktotal mt2
              JOIN navuctovanie n2 ON mt2.uctovnykod = n2.uctovnykod
              WHERE mt2.datum = mt.datum
                AND mt2.uctovnykod IN (221110, 221210)
                AND mt2.uctovnykod IN (SELECT uctovnykod 
                                       FROM navuctovanie 
                                       WHERE CAST(uctovnykod AS TEXT) LIKE '325%' OR CAST(uctovnykod AS TEXT) LIKE '315%' OR CAST(uctovnykod AS TEXT) LIKE '261%')
                AND mt2.eqid = mt.eqid
                AND mt2.subjektid = mt.subjektid
                AND mt2.kodaktiva = mt.kodaktiva
                AND mt2.ucetaktiva = mt.ucetaktiva
          )
          AND mt.subjektid = $id_fond
        ORDER BY n.popis, mt.menadenom;
	";

$connectionPohladavky = Connection::getDataFromDatabase($queryPohladavky, defaultDB);
$pohladavky = $connectionPohladavky[1];

$pohladavkyArray = [];
$cnt = 0;
foreach ($pohladavky as $item) {
	if ($cnt == 0) {
		$vypis_obsah = $vypis_obsah . '
				<br><br><br>
				<table style="width:100%;border-collapse:collapse;">
					<tr>
						<td>
							<h3 style="color:' . $farbaciara2 . '">Záväzky a pohľadávky</h3>
						</td>
					</tr>
				</table>
				<br><br>
				<table style="width:100%;border-collapse:collapse;border-top:1pt solid ' . $farbaciara3 . ';border-bottom:1pt solid ' . $farbaciara3 . ';">
					
						<tr>
							<th style="width:15%;text-align:left;font-size:8;"><b>Názov</b></th>
							<th style="width:20%;text-align:left;font-size:8;"><b>Názov CP</b></th>
							<th style="width:14%;text-align:right;font-size:8;"><b>Objem</b></th>
							<th style="width:8%;text-align:right;font-size:8;"><b>Mena</b></th>
							<th style="width:10%;text-align:right;font-size:8;"><b>Počet</b></th>
							<th style="width:9%;text-align:right;font-size:8;"><b>Kurz</b></th>
							<th style="width:14%;text-align:right;font-size:8;"><b>Objem v ' . $refmena . '</b></th>
							<th style="width:10%;text-align:right;font-size:8;"><b>Podiel v %</b></th>
						</tr>
					<tr style="width:100%;line-height:1pt;">
						<td colspan="8" style="border-bottom:0.1mm solid ' . $farbaciara1 . ';height:1pt;min-width:100%;width:100%;"></td>
					</tr>
					<tr style="width:100%;line-height:1pt;">
						<td colspan="8" style="height:1pt;min-width:100%;width:100%;"></td>
					</tr>
			';
	}
	$cnt++;

	$popis = $item["popis"];
	$obj = $item["sumaref"] + $item["auvdenom"] * $item["kurz_lot"];
	$paz = ($obj / $total) * 100;
	$podiel = floatval($paz);
	$mena = $item["menadenom"];
	$objem = floatval($item["sumadenom"] + $item["auvdenom"]);
	$objem_ref = floatval($obj);
	$menovykurz = floatval($item["kurz"]);
	$subjektid = $item["subjektid"];
	$ucetaktiva = $item["ucetaktiva"];
	$pocet = $item["pocet"];
	if ($pocet != '')
		$pocet = floatval($pocet);
	$kodaktiva = $item['kodaktiva'];

	$pohladavkyArray[] = [$popis, $kodaktiva, $objem, $mena, $pocet, $menovykurz, $objem_ref, $podiel];
	$vypis_obsah = $vypis_obsah . '	
			<tr>
				<td style="width:15%;text-align:left;font-size:8;">' . $popis . '</td>
				<td style="width:20%;text-align:left;font-size:8;">' . $kodaktiva . '</td>
				<td style="width:14%;text-align:right;font-size:8;">' . $objem . '</td>
				<td style="width:8%;text-align:right;font-size:8;">' . $mena . '</td>
				<td style="width:10%;text-align:right;font-size:8;">' . $pocet . '</td>
				<td style="width:9%;text-align:right;font-size:8;">' . $menovykurz . '</td>
				<td style="width:14%;text-align:right;font-size:8;">' . $objem_ref . '</td>
				<td style="width:10%;text-align:right;font-size:8;">' . $podiel . '</td>
			</tr>
		';
}

if ($cnt != 0) {
	$vypis_obsah = $vypis_obsah . '
					<tr>
						<td style="width:15%;text-align:left;font-size:8;"><b>Spolu</b></td>
						<td style="width:20%;text-align:left;font-size:8;"></td>
						<td style="width:14%;text-align:right;font-size:8;"></td>
						<td style="width:9%;text-align:right;font-size:8;"></td>
						<td style="width:10%;text-align:right;font-size:8;"></td>
						<td style="width:9%;text-align:right;font-size:8;"></td>
						<td style="width:14%;text-align:right;font-size:8;"><b>' . $suma_paz1 . '</b></td>
						<td style="width:9%;text-align:right;font-size:8;"><b>' . $podiel_paz2 . '</b></td>
					</tr>
					<tr style="width:100%;line-height:1pt;">
						<td colspan="8" style="height:1pt;min-width:100%;width:100%;"></td>
					</tr>
			</table>
		';
}

//---------------Očakávané finančné toky---------------
$activity = "12,15,18";

$queryFinacneToky = "SELECT sum(round(
		                ((CASE md_d WHEN 0 THEN md_d0 WHEN 1 THEN md_d1 END) *
		                (COALESCE(d.nominalemisie * COALESCE(f.faktor, 1), 1)) * 
						m.pocet*		
						f.faktor*
						(f.kupon/100)*
		                f_koef_auv_isincurrric(dcr.isincurrric::varchar,d.zaklad::integer,d.dateemisie::varchar,d.maturitydate::date,f.datesplatnost::date,d.prvy_kupon::varchar,d.posledny_kupon::varchar,d.kupfrek::integer,d.exfrekkup::integer,2))
						,COALESCE(d.rounding,25)
					)
				) as urok,
				0 as istina, to_char(f.datesplatnost,'dd.mm.yyyy') as mydatum, f.datesplatnost, d.cpnaz, m.menadenom,
				min(case when (po.typ_zdanenia = 'FO') then COALESCE(d.sadzbafo, 0)
						when (po.typ_zdanenia = 'PO') then COALESCE(d.sadzbapo, 0)
						when (po.typ_zdanenia = 'NO') then COALESCE(d.sadzbano, 0)			
						else 0 end
				) as dan,
				'kupon' as typ, m.subjektid, p.cislozmluvy
		from
			majetoktotal m, floatkupon f, dbequity d, dbequitycurr dc, dbequitycurrric dcr, navuctovanie n,
			portfolio p,  podielnik po
		where
			m.datum=to_date('$dbdate','YYYY-MM-DD') and m.uctovnykod in (315124) and m.subjektid in ($id_fond) and
			f.isincurrric = m.kodaktiva and f.datesplatnost > m.datum and dcr.isincurrric = m.kodaktiva and
			dc.isincurr = dcr.isincurr and d.isin = dc.isin and m.uctovnykod = n.uctovnykod and	
			p.fondid = m.subjektid  and  (is_exdate(m.kodaktiva,m.datum,0) = 1) and ((f.datesplatnost-COALESCE(d.exfrekkup,0)) <= m.datum)  
			and (is_coupon_date(m.kodaktiva,m.datum) = 0) and p.podielnikid = po.podielnikid 
			and f.datesplatnost < TO_DATE(
              CASE
                WHEN SUBSTRING('2023-09-01', 6, 2) = '12' THEN
                  (CAST(SUBSTRING('2023-09-01', 1, 4) AS INTEGER) + 1)::TEXT || '-01-' || SUBSTRING('2023-09-01', 9, 2)
                ELSE
                  SUBSTRING('2023-09-01', 1, 4) || '-' || LPAD((CAST(SUBSTRING('2023-09-01', 6, 2) AS INTEGER) + 1)::TEXT, 2, '0') || '-' || SUBSTRING('2023-09-01', 9, 2)
              END,
              'YYYY-MM-DD'
            )
		group by
			m.kodaktiva, f.datesplatnost, d.cpnaz, m.menadenom, d.dan, m.subjektid, p.cislozmluvy, po.typ_zdanenia
		  
		union all
		 
		select
				sum(round(
				        ((CASE md_d WHEN 0 THEN md_d0 WHEN 1 THEN md_d1 END) *
						(COALESCE(d.nominalemisie * COALESCE(f.faktor, 1), 1)) *
						m.pocet*		
						f.faktor*
						(f.kupon/100)*
						f_koef_auv_isincurrric(dcr.isincurrric::varchar,d.zaklad::integer,d.dateemisie::varchar,d.maturitydate::date,f.datesplatnost::date,d.prvy_kupon::varchar,d.posledny_kupon::varchar,d.istfrek::integer,d.exfrekkup::integer,2))
						,COALESCE(d.rounding,25)
					)
				) as urok,
				0 as istina, to_char(f.datesplatnost,'dd.mm.yyyy') as mydatum,
				f.datesplatnost, d.cpnaz, m.menadenom,
				min(case when (po.typ_zdanenia = 'FO') then COALESCE(d.sadzbafo, 0)
						when (po.typ_zdanenia = 'PO') then COALESCE(d.sadzbapo, 0)
						when (po.typ_zdanenia = 'NO') then COALESCE(d.sadzbano, 0)			
						else 0 end
				) as dan,
				'kupon' as typ, m.subjektid, p.cislozmluvy
		from
			majetoktotal m, floatkupon f, dbequity d, dbequitycurr dc, dbequitycurrric dcr, navuctovanie n, portfolio p, podielnik po
		where
			m.datum=to_date('$dbdate','YYYY-MM-DD') and m.uctovnykod in (251110,325121,315121) and m.subjektid in ($id_fond) and
			f.isincurrric = m.kodaktiva and f.datesplatnost > m.datum and dcr.isincurrric = m.kodaktiva and
			dc.isincurr = dcr.isincurr and d.isin = dc.isin and m.uctovnykod = n.uctovnykod and	
			p.fondid = m.subjektid  and not ((is_exdate(m.kodaktiva,m.datum,0) = 1) and ((f.datesplatnost - COALESCE(d.exfrekkup,0)) <= m.datum) 
			and (is_exdate(m.kodaktiva,m.datum,0) = 0)) and p.PODIELNIKID = po.PODIELNIKID
			and f.datesplatnost < TO_DATE(
              CASE
                WHEN SUBSTRING('2023-09-01', 6, 2) = '12' THEN
                  (CAST(SUBSTRING('2023-09-01', 1, 4) AS INTEGER) + 1)::TEXT || '-01-' || SUBSTRING('2023-09-01', 9, 2)
                ELSE
                  SUBSTRING('2023-09-01', 1, 4) || '-' || LPAD((CAST(SUBSTRING('2023-09-01', 6, 2) AS INTEGER) + 1)::TEXT, 2, '0') || '-' || SUBSTRING('2023-09-01', 9, 2)
              END,
              'YYYY-MM-DD'
            )
		group by
			m.kodaktiva, f.datesplatnost, d.cpnaz, m.menadenom, d.dan, m.subjektid, p.cislozmluvy, po.typ_zdanenia

		union all

		select 0 as urok, 
		       round(
				                ((CASE md_d WHEN 0 THEN md_d0 WHEN 1 THEN md_d1 END) *
								-- decode(md_d,0,md_d0,1,md_d1)*
								-- pkg_conversion.getCPNominal(d.isin,m.datum)*
                                (COALESCE(d.nominalemisie * COALESCE(f.faktor, 1), 1)) *
								( case 	when is_exdate(m.kodaktiva,m.datum, 1) = 1
											then COALESCE((select pocet from majetoktotal m2 where m2.datum=m.datum and m2.subjektid=m.subjektid and m2.kodaktiva = m.kodaktiva and m2.uctovnykod=315123),0)
										else m.pocet end)*
				                (COALESCE(f.istina, 0)/100))
								,COALESCE(d.rounding,25)
							) as istina,
				to_char(f.datesplatnost,'dd.mm.yyyy') as mydatum, f.datesplatnost, d.cpnaz, m.menadenom,
				d.dan, 'istina' as typ, m.subjektid, p.cislozmluvy
		from
			majetoktotal m, floatkupon f, dbequity d, dbequitycurr dc, dbequitycurrric dcr, navuctovanie n, portfolio p
		where
			m.datum=to_date('$dbdate','YYYY-MM-DD') and m.uctovnykod in (251110,325121,315121) and m.subjektid in ($id_fond) and
			f.isincurrric = m.kodaktiva and f.datesplatnost > m.datum and dcr.isincurrric = m.kodaktiva and
			dc.isincurr = dcr.isincurr and d.isin = dc.isin and f.istina != 0 and
			m.uctovnykod = n.uctovnykod and	 p.fondid = m.subjektid 
		    and f.datesplatnost < TO_DATE(
              CASE
                WHEN SUBSTR('2023-09-01', 6, 2) = '12' THEN
                  (CAST(SUBSTR('2023-09-01', 1, 4) AS INTEGER) + 1)::TEXT || '-01-' || SUBSTR('2023-09-01', 9, 2)
                ELSE
                  SUBSTR('2023-09-01', 1, 4) || '-' || LPAD((CAST(SUBSTR('2023-09-01', 6, 2) AS INTEGER) + 1)::TEXT, 2, '0') || '-' || SUBSTR('2023-09-01', 9, 2)
              END,
              'YYYY-MM-DD'
            )
		order by 
			datesplatnost, subjektid, cpnaz
	";

$connectionFinacneToky = Connection::getDataFromDatabase($queryFinacneToky, defaultDB);
$financneToky = $connectionFinacneToky[1];

$ocakavaneArray = [];
$cnt = 0;
foreach ($financneToky as $item) {
	if ($cnt == 0) {
		$vypis_obsah = $vypis_obsah . '
				<br><br><br>
				<table style="width:100%;border-collapse:collapse;">
					<tr >
						<td>
							<h3 style="color:' . $farbaciara2 . '">Očakávané finančné toky ku dňu ' . $showdate . '</h3>
						</td>
					</tr>
				</table>
				<br><br>
				<table style="width:100%;border-collapse:collapse;border-top:1pt solid ' . $farbaciara3 . ';border-bottom:1pt solid ' . $farbaciara3 . ';">						
					<tr>
						<td style="width:15%;text-align:left;font-size:8;"><b>Dátum</b></td>
						<td style="width:35%;text-align:left;font-size:8;"><b>Investičný nástroj</b></td>
						<td style="width:9%;text-align:right;font-size:8;"><b>Mena</b></td> 
						<td style="width:14%;text-align:right;font-size:8;"><b>Istina</b></td>
						<td style="width:14%;text-align:right;font-size:8;"><b>Kupón</b></td>
						<td style="width:13%;text-align:right;font-size:8;"><b>Daň</b></td>
					</tr>
					<tr style="width:100%;line-height:1pt;">
						<td colspan="6" style="border-bottom:0.1mm solid ' . $farbaciara1 . ';height:1pt;min-width:100%;width:100%;"></td>
					</tr>
					<tr style="width:100%;line-height:1pt;">
						<td colspan="6" style="height:1pt;min-width:100%;width:100%;"></td>
					</tr>
			';
	}
	$cnt++;

	$datum = $item["mydatum"];
	$nastroj = $item["cpnaz"];
	$mena = $item["menadenom"];
	$dan = $item["dan"];
	$urok = $item["urok"];
	$istina = $item["istina"];
	$dan = $urok / 100 * $dan;
	$istina = floatval($istina);
	$urok = floatval($urok);
	$dan = floatval($dan);

	$ockavaneArray[] = [$datum, $nastroj, $mena, $istina, $urok, $dan];
	$vypis_obsah = $vypis_obsah . '
				<tr >
					<td style="width:15%;text-align:left;font-size:8;">' . $datum . '</td>
					<td style="width:35%;text-align:left;font-size:8;">' . $nastroj . '</td>
					<td style="width:9%;text-align:right;font-size:8;">' . $mena . '</td>
					<td style="width:14%;text-align:right;font-size:8;">' . $istina . '</td>
					<td style="width:14%;text-align:right;font-size:8;">' . $urok . '</td>
					<td style="width:13%;text-align:right;font-size:8;">' . $dan . '</td>
				</tr>
		';
}

if ($cnt != 0) {
	$vypis_obsah = $vypis_obsah . '
				<tr style="width:100%;line-height:1pt;">
					<td colspan="6" style="height:1pt;min-width:100%;width:100%;"></td>
				</tr>
			</table>
		';
}

$vypis_portfolia = $vypis_obsah;
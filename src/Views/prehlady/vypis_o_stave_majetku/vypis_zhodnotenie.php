<?php

$vypis_obsah = '
		<br><br><br>		
		<table style="width:100%;border-collapse:collapse;">
			<tr  style="width:100%;text-align:center;">
				<td style="height:26.25pt;">
					<div style="color: #6B819B;">
						<span style="font-size: 21pt;font-weight:400;">Sumárne informácie o portfóliu</span>
					</div>	
				</td>
			</tr>
			<tr style="width:100%;">
				<td style="height:20pt;min-width:100%;width:100%;"></td>
			</tr>	
			<tr  style="width:100%;text-align:center;">
				<td style="height:26.25pt;vertical-align:bottom;">
					<div  style="color: #000;vertical-align:bottom;">
						<span style="font-size: 9.25pt;font-weight:600;"><b>za obdobie od ' . $fromdate . ' do ' . $todate . '</b></span>
					</div>	
				</td>
			</tr>
		</table>
		<br><br>
		<table>
			<tr style="font-weight:normal;color: #0E0E0E;">
				<td style="min-width:4%;width:4%;"></td>
				<td style="vertical-align:bottom;width:71%;text-align:left;height:10pt;"><div style="vertical-align:bottom;"><span style="font-weight:400;font-size:7.5pt;">Klient:    </span><span style="font-weight:700;font-size:9.75pt;">' . $klient . '</span></div></td>
				<td style="vertical-align:bottom;width:20%;text-align:right;height:10pt;"><div style="vertical-align:bottom;"><span style="font-weight:400;font-size:7.5pt;">Portfólio:    </span><span style="font-weight:700;font-size:9.75pt;">' . $cislozmluvy . '</span></div></td>
				<td style="min-width:5%;width:5%;"></td>
			</tr>
		</table>
		';


//----------------------suhrnne udaje ----------------------
$query = "
		SELECT
			1 as typ,
			'Suma vkladov pp a cp' as popis,
			(f_getSumaVklady($fondid,to_date('$fromdate','YYYY-MM-DD'),to_date('$todate','YYYY-MM-DD'),1, '$refmena')) as suma		
		union all
		
		SELECT
			2 as typ,
			'Suma vyberov pp a cp' as popis,
			(f_getSumaVybery($fondid,to_date('$fromdate','YYYY-MM-DD'),to_date('$todate','YYYY-MM-DD'),1, '$refmena')) as suma		
		union all
		
		SELECT
            3 AS typ,
            'Prevod pp z ineho pf' AS popis, 
            SUM(k.suma * (SELECT kurz FROM f_menovy_kurz_lot(k.mena, '$refmena', k.datum_zauctovania))) AS suma
        FROM 
            konfirmaciapp k
		where
			k.druhobchodu = 'prevod' and
			k.dealid_related is null and
			k.subjektid = $fondid and
			k.logactivityid = 12 and
			k.datum_zauctovania >= to_date('$fromdate','YYYY-MM-DD') and
			k.datum_zauctovania <= to_date('$todate','YYYY-MM-DD')
			
		union all
		
		select
			4 as typ,
			'Prevod CP z ineho pf' as popis,
			sum((r.transsuma*(SELECT kurz FROM f_menovy_kurz_lot(k.currencyidtrade,'$refmena',r.dattransakcie)))) as suma
		FROM 
			konfirmaciacp k,
			rekonfirmaciacp r
		where
			k.dealid = r.dealid and
			k.druhobchodu = 'vklad-pr' and
			k.subjektid = $fondid and
			r.logactivityid >= 12 and
			r.logactivityid not in (14) and
			r.dattransakcie >= to_date('$fromdate','YYYY-MM-DD') and
			r.dattransakcie <= to_date('$todate','YYYY-MM-DD')
		
		union all
		
		select
			5 as typ,
			'Prevod pp na ine pf' as popis, 
			sum((k.suma * (SELECT kurz FROM f_menovy_kurz_lot(k.mena,'$refmena',k.datum_zauctovania)))) as suma
		FROM 
			konfirmaciapp k
		where
			k.druhobchodu = 'prevod' and
			k.dealid_related is not null and
			k.subjektid = $fondid and
			k.logactivityid = 12 and
			k.datum_zauctovania >= to_date('$fromdate','YYYY-MM-DD') and
			k.datum_zauctovania <= to_date('$todate','YYYY-MM-DD')
			
		union all
		
		select
			6 as typ,
			'Prevod CP na ine pf' as popis,
			sum((r.transsuma*(SELECT kurz FROM f_menovy_kurz_lot(k.currencyidtrade,'$refmena',r.dattransakcie)))) as suma
		FROM 
			konfirmaciacp k,
			rekonfirmaciacp r
		where
			k.dealid = r.dealid and
			k.druhobchodu = 'vyber-pr' and
			k.subjektid = $fondid and
			r.logactivityid >= 12 and
			r.logactivityid not in (14) and
			r.dattransakcie >= to_date('$fromdate','YYYY-MM-DD') and
			r.dattransakcie <= to_date('$todate','YYYY-MM-DD')
		
		union all		
		
		select 
			7 as typ,
			'Pociatocny stav' as popis,				
			SUM(COALESCE((CASE WHEN md_d = 0 THEN md_d0 ELSE md_d1 END) * sumadenom * (SELECT kurz FROM f_menovy_kurz_lot(m.menadenom,'$refmena',m.datum)),0)) as suma
		FROM
			majetoktotal m, navuctovanie nu
		WHERE
			m.uctovnykod = nu.uctovnykod and
			m.subjektid=$fondid and
			m.datum = (select max(datum) FROM pricestore where fondid=$fondid and datum < to_date('$fromdate','YYYY-MM-DD') )

		union all
		
		select 
			8 as poradie,
			'Konecny stav' as popis,
			sum(COALESCE((CASE WHEN md_d = 0 THEN md_d0 ELSE md_d1 END) * sumadenom * (SELECT kurz FROM f_menovy_kurz_lot(m.menadenom,'$refmena',m.datum)),0)) as suma
			FROM
			majetoktotal m, navuctovanie nu
			WHERE
				m.uctovnykod = nu.uctovnykod and
				m.subjektid=$fondid and
				m.datum = to_date('$todate','YYYY-MM-DD')
		
		union all

		select
			9 as typ,
			'Vyplatenie istiny na vlastny ucet' as popis,
			COALESCE(SUM(s.suma * (SELECT kurz FROM f_menovy_kurz_lot(s.mena,'$refmena',s.datumvyplaty))),0) as suma
		FROM
		    	splatenie s		    	
		where
			s.subjektid = $fondid and 
		  	s.datumvyplaty >= to_date('$fromdate','YYYY-MM-DD') and
			s.datumvyplaty <= to_date('$todate','YYYY-MM-DD') and
			s.uctovnykod = (251110) and
			s.fiktivne = 1			

		union all
		
		select
			10 as typ,
			'Vyplatenie kuponov na vlastny ucet' as popis,
			COALESCE(SUM(d.danzaklad*(SELECT kurz FROM f_menovy_kurz_lot(s.mena,'$refmena',s.datumvyplaty))),0) as suma
		FROM
		    	splatenie s,
		    	danvynosy d
		where
			s.subjektid = $fondid and 
		  	s.datumvyplaty >= to_date('$fromdate','YYYY-MM-DD') and
			s.datumvyplaty <= to_date('$todate','YYYY-MM-DD') and
			s.uctovnykod = (251120) and
			s.fiktivne = 1 and
			d.dealid = s.dealid and
			d.destinacia='splatenie'

		union all
		
		select
			11 as typ,
			'Vyplatenie dividend na vlastny ucet' as popis,
			COALESCE(sum(d.danzaklad*(SELECT kurz FROM f_menovy_kurz_lot(sa.mena,'$refmena',sa.datumvyplaty))),0) 
			as suma 
		FROM
			splatenieakcia sa,
			danvynosy d
		where
			sa.subjektid = $fondid and
			sa.datumvyplaty >= to_date('$fromdate','YYYY-MM-DD') and
			sa.datumvyplaty <= to_date('$todate','YYYY-MM-DD') and
			sa.subkodobratu <=9 and
			sa.fiktivne = 1 and
			d.dealid = sa.dealid and
			d.destinacia = 'splatenieakcia'		
	
		union all
		
		select
			12 as typ,
		 	'Dan z vynosov dividendy' as popis,
		  	COALESCE(sum(d.dan*(SELECT kurz FROM f_menovy_kurz_lot(d.mena,'$refmena',s.datumvyplaty))),0) 
		  	as suma
		FROM
			danvynosy d,
			splatenieakcia s
		where
			 d.destinacia = 'splatenieakcia' and
			 d.dealid = s.dealid and
			 s.subjektid = $fondid and
			 s.dealid = d.dealid and
			 s.subkodobratu <=9 and
			 s.datumvyplaty >= to_date('$fromdate','YYYY-MM-DD') and
			 s.datumvyplaty <= to_date('$todate','YYYY-MM-DD') and
			 s.fiktivne = 1		
		
		union all
		
		select
			13 as typ,
			'Dan z vynosov kuponov' as popis,
			COALESCE(sum(d.dan*(SELECT kurz FROM f_menovy_kurz_lot(s.mena,'$refmena',s.datumvyplaty))),0)
			as suma 
		FROM
		    splatenie s,
			danvynosy d
		where
			s.subjektid = $fondid and 
		    s.datumvyplaty >= to_date('$fromdate','YYYY-MM-DD') and
			s.datumvyplaty <= to_date('$todate','YYYY-MM-DD') and
			s.uctovnykod = (251120) and
			d.destinacia = 'splatenie' and
			d.dealid = s.dealid and
			s.fiktivne = 1
	";

$dbRes = Connection::getDataFromDatabase($query, defaultDB);
$queryResult = $dbRes[1];

$typ = '';
$suma = '';
$sumaTyp = array();
$sumaPocStav = 0;
$sumaKonStav = 0;
$sumaVklady = 0;
$sumaVybery = 0;
$sumaPrevody = 0;

foreach ($queryResult as $item) {
    $typ = $item["typ"];
    $suma = $item["suma"];
    $sumaTyp[$typ] = $suma;
}

$sumaPocStav = $sumaTyp[7];
$sumaKonStav = $sumaTyp[8];
$sumaVklady = $sumaTyp[1];
$sumaVybery = $sumaTyp[2] + $sumaTyp[9] + $sumaTyp[10] + $sumaTyp[11] + $sumaTyp[12] + $sumaTyp[13];
$sumaPrevody = ($sumaTyp[3] + $sumaTyp[4]) - ($sumaTyp[5] + $sumaTyp[6]);

$vypis_obsah = $vypis_obsah . '
		<br><br><br><br>
		<table style="width:100%;border-collapse:collapse;">
			<tr>
				<td style="width:70%;text-align:left;" ><h3 style="color:' . $farbaciara2 . '">Súhrnné údaje</h3></td>
				<td style="width:30%;text-align:right;" ><b>Referenčná mena ' . $refmena . '</b></td>
			</tr>
			<tr style="width:100%;line-height:1pt;">
				<td colspan="2" style="height:1pt;min-width:100%;width:100%;"></td>
			</tr>
		</table>
		<br>
		<table style="width:100%;border-collapse:collapse;border-top:1pt solid ' . $farbaciara3 . ';border-bottom:1pt solid ' . $farbaciara3 . ';">
			<tr style="width:100%;line-height:1pt;">
				<td colspan="2" style="height:1pt;min-width:100%;width:100%;"></td>
			</tr>
			<tr>
				<td style="width:70%;text-align:left;">Počiatočný stav:</td>
				<td style="width:30%;text-align:right;">' . floatval($sumaPocStav) . '</td>
			</tr>
			<tr>
				<td style="width:70%;text-align:left;">Suma vkladov:</td>
				<td style="width:30%;text-align:right;">' . floatval($sumaVklady) . '</td>
			</tr>
			<tr>
				<td style="width:70%;text-align:left;">Suma výberov:</td>
				<td style="width:30%;text-align:right;">' . floatval($sumaVybery) . '</td>
			</tr>
			<tr>
				<td style="width:70%;text-align:left;">Suma prevodov medzi portfóliami:</td>
				<td style="width:30%;text-align:right;">' . floatval($sumaPrevody) . '</td>
			</tr>
			<tr>
				<td style="width:70%;text-align:left;">Konečný stav:</td>
				<td style="width:30%;text-align:right;">' . floatval($sumaKonStav) . '</td>
			</tr>
			<tr style="width:100%;line-height:1pt;">
				<td colspan="2" style="height:1pt;min-width:100%;width:100%;"></td>
			</tr>
		</table>
	';

//----------------------vykonnost portfolia ----------------------
/*round(100*(exp(c.lnzhod)-1),4) namiesto as kumzhod*/
// TODO: logaritmus s michalom robiť
$query = "select c.datum,c.mena,c.suma,1 as kumzhod,round((100*(power(exp(c.lnzhod),c.rok/c.obdobie)-1))::numeric,4) as kumzhodpa,c.priemobjem
				FROM (
					select b.datum,b.mena,b.suma,b.pohyby,round(b.suma/(b.lagsuma+b.pohyby),8) as zhod,
							-- sum(round(ln(b.suma/(b.lagsuma+b.pohyby)),8)) over (order by b.datum) as lnzhod,
					       1 as lnzhod,
							b.obdobie,
							to_date('31.12.'||extract (year FROM to_date('$todate','YYYY-MM-DD')),'dd.mm.yyyy') - to_date('31.12.'||(extract (year FROM to_date('$todate','YYYY-MM-DD'))-1),'dd.mm.yyyy') as rok,
							round(avg(b.suma) over (order by b.datum),2) as priemobjem
					FROM(
						select a.datum,a.suma*coalesce(k.kurz,1) as suma,a.menaref as mena,a.obdobie,COALESCE(v.suma*coalesce(k.kurz,1),0) as pohyby,
								lag(a.suma*coalesce(k.kurz,1),1) over (order by a.datum ) as lagsuma
						FROM (		
							select round(sum(mt.sumaref*((0.5-mt.md_d)/0.5)/COALESCE(ku.kurz,1)),2) as suma,mt.datum,'EUR' as mena,mt.menaref 
									,(max(mt.datum) over (order by mt.datum desc) - min(mt.datum) over (order by mt.datum) + 1) as obdobie
							FROM majetoktotal mt
							left join kurzyaktivarchiv ku on mt.datum=ku.datum and ku.ric='EUR'||mt.menaref
							where mt.subjektid in ($fondid) and mt.datum>=to_date('$fromdate','YYYY-MM-DD') and mt.datum<=to_date('$todate','YYYY-MM-DD')
							group by mt.datum,mt.menaref
						) a
						left join (
							select aa.mena,sum(aa.suma) as suma,aa.datum
							FROM (
								select 	ob.mena as mena,(ob.suma*((0.5-ma.md_d)/0.5)) as suma,ob.obratdatetime as datum
								FROM obratybu ob, obratybuobratid obo, majetokarchiv ma
								where ob.id = obo.id and ma.obratid = obo.obratid and ma.subjektid in ($fondid) and ma.kodobratu = 201
									and ma.uctovnykod = 325300 and ob.logactivityid = 15 and ob.obratdatetime >= to_date('$fromdate','YYYY-MM-DD')
									and ob.obratdatetime <= to_date('$todate','YYYY-MM-DD') and ob.krdb = 1					
								union all
								select  u.mena as mena,(u.suma*((0.5-ma.md_d)/0.5)) as suma,u.datesplatnost as datum
								FROM uhrada u, uhradaobratid ui, majetokarchiv ma
								where u.kodobratu = 303 and u.id = ui.id and ma.obratid = ui.obratid and ma.subjektid in ($fondid)
									and ma.kodobratu = 303 and ma.uctovnykod = 261930 and logactivityid = 15
									and datesplatnost >= to_date('$fromdate','YYYY-MM-DD')
									and datesplatnost <= to_date('$todate','YYYY-MM-DD')
								union all
								select'EUR' as mena, -round(k.suma/COALESCE(ku.kurz,1),2) as suma, k.datum_zauctovania as datum
								FROM konfirmaciapp k
								left join kurzyaktivarchiv ku on k.datum_zauctovania=ku.datum and ku.ric='EUR'||k.mena
								where k.druhobchodu = 'vyber' and k.subjektid in ($fondid) and k.logactivityid = 12
									and k.datum_zauctovania >= to_date('$fromdate','YYYY-MM-DD') and k.datum_zauctovania <= to_date('$todate','YYYY-MM-DD')
								union all	
								select 'EUR' as mena, round(k.suma/COALESCE(ku.kurz,1),2) as suma, k.datum_zauctovania as datum
								FROM konfirmaciapp k
								left join kurzyaktivarchiv ku on k.datum_zauctovania=ku.datum and ku.ric='EUR'||k.mena
								where k.druhobchodu = 'vklad' and k.subjektid in ($fondid) and k.logactivityid = 12
									and k.datum_zauctovania >= to_date('$fromdate','YYYY-MM-DD') and k.datum_zauctovania <= to_date('$todate','YYYY-MM-DD')
								union all	
								select 'EUR' as mena,round(a.suma/COALESCE(ku.kurz,1),2) as suma,a.datum
								FROM (
									select	kcp.currencyidtrade as mena,(rcp.transsuma*(CASE kcp.druhobchodu WHEN 'vklad' THEN 1 WHEN 'vyber' THEN -1 WHEN 'vklad-pr' THEN 1 WHEN 'vyber-pr' THEN -1 END)) as suma,rcp.datvysporiadaniamureal as datum		
									FROM	konfirmaciacp kcp, rekonfirmaciacp rcp, rekonfirmaciacpobratid rcpo,
											(select obratid, obratdatatimezauctovane, kodaktiva
											 FROM 	majetoktoday
											 where	obratid > 0 and uctovnykod in (251110,251200,251300) and obratdatatimezauctovane >= to_date('$fromdate','YYYY-MM-DD')
													and obratdatatimezauctovane <= to_date('$todate','YYYY-MM-DD') and destinacia not like 'muprevod'
											 union all
											 select obratid, obratdatatimezauctovane, kodaktiva
											 FROM majetokarchiv
											 where	obratid > 0 and uctovnykod in (251110,251200,251300) and obratdatatimezauctovane >= to_date('$fromdate','YYYY-MM-DD')
													and obratdatatimezauctovane <= to_date('$todate','YYYY-MM-DD') and destinacia not like 'muprevod'
											) maj		
									where	kcp.dealid = rcp.dealid and subjektid in ($fondid) and rcpo.dealid=rcp.dealid and rcpo.tranza=rcp.tranza
											and maj.obratid=rcpo.obratid and kcp.druhobchodu in('vklad','vyber', 'vklad-pr','vyber-pr')
								) a
								left join kurzyaktivarchiv ku on a.datum=ku.datum and ku.ric='EUR'||a.mena
								union all
								select 'EUR' as mena,round(a.suma/COALESCE(ku.kurz,1),2) as suma,a.datum
								FROM (
									select 	k1.mena as mena,-k1.suma as suma, k1.datum_zauctovania as datum
										FROM konfirmaciapp k1
										where k1.dealid_related is not null and k1.subjektid in ($fondid) and k1.logactivityid = 12 and k1.druhobchodu in ('prevod')
											and k1.datum_zauctovania >= to_date('$fromdate','YYYY-MM-DD') and k1.datum_zauctovania <= to_date('$todate','YYYY-MM-DD')
									union all
									select 	k1.mena as mena,k1.suma as suma, k1.datum_zauctovania as datum
									FROM konfirmaciapp k1
									where k1.dealid_related is null and k1.subjektid in ($fondid) and k1.logactivityid = 12 and k1.druhobchodu in ('prevod')
										and k1.datum_zauctovania >= to_date('$fromdate','YYYY-MM-DD') and k1.datum_zauctovania <= to_date('$todate','YYYY-MM-DD')
								) a
								left join kurzyaktivarchiv ku on a.datum=ku.datum and ku.ric='EUR'||a.mena 			
							) aa
							group by aa.mena,aa.datum
						) v on a.datum=v.datum
						left join kurzyaktivarchiv k on a.datum=k.datum and k.ric='EUR'||a.menaref
					) b
				) c	
				where c.datum=to_date('$todate','YYYY-MM-DD')
		";
$queryRes = Connection::getDataFromDatabase($query, defaultDB);
$vykonnostResult = $queryRes[1][0];

$vykonnost = 0;
$vykonnostPA = 0;
$priemobjem = 0;

$vykonnost = $vykonnostResult['kumzhod'];
$vykonnostPA = $vykonnostResult['kumzhodpa'];
$priemobjem = $vykonnostResult['priemobjem'];

$vypis_obsah = $vypis_obsah . '
			<br><br><br>
			<table style="width:100%;border-collapse:collapse;">
				<tr>
					<td  style="width:100%;" ><h3 style="color:' . $farbaciara2 . '">Výkonnosť portfólia</h3></td>
				</tr>
				<tr style="width:100%;line-height:1pt;">
					<td colspan="2" style="height:1pt;min-width:100%;width:100%;"></td>
				</tr>				
			</table>	
			<br>
			<table style="width:100%;border-collapse:collapse;border-top:1pt solid ' . $farbaciara3 . ';border-bottom:1pt solid ' . $farbaciara3 . ';">
				<tr style="width:100%;line-height:1pt;">
					<td colspan="2" style="height:1pt;min-width:100%;width:100%;"></td>
				</tr>
				<tr>
					<td style="width:70%;text-align:left;">Výkonnost portfólia:</td>
					<td style="width:30%;text-align:right;">' . floatval($vykonnost) . ' %</td>
				</tr>
				<tr>
					<td style="width:70%;text-align:left;">Výkonnost portfólia (p.a.):</td>
					<td style="width:30%;text-align:right;">' . floatval($vykonnostPA) . ' %</td>
				</tr>
				<tr style="width:100%;line-height:1pt;">
					<td colspan="2" style="height:1pt;min-width:100%;width:100%;"></td>
				</tr>
			</table>		
		';
//}

//----------------------prijate platby ----------------------
$query = "
		select			
			1 as typ,
			COALESCE(sum(d.danzaklad*(SELECT kurz FROM f_menovy_kurz_lot(s.mena,'$refmena',s.datumvyplaty))),0) as suma
		FROM
			splatenie s,
			danvynosy d
		where
			s.subjektid = $fondid and 
		  	s.datumvyplaty >= to_date('$fromdate','YYYY-MM-DD') and
			s.datumvyplaty <= to_date('$todate','YYYY-MM-DD') and
			s.uctovnykod = (251120) and
			s.fiktivne = 0 and
			d.dealid = s.dealid and
			d.destinacia='splatenie'
		 		
		union all
		
		select 
		  	2 as typ,  
			COALESCE(sum(d.danzaklad*(SELECT kurz FROM f_menovy_kurz_lot(sa.mena,'$refmena',sa.datumvyplaty))),0)  as suma 
		FROM
			splatenieakcia sa,
			danvynosy d
		where
			sa.subjektid = $fondid and
			sa.datumvyplaty >= to_date('$fromdate','YYYY-MM-DD') and
			sa.datumvyplaty <= to_date('$todate','YYYY-MM-DD') and
			sa.subkodobratu in (select subkodobratu FROM dividendaakciatyp where MD_D = 0 and hotovost = 1) and
			sa.fiktivne = 0 and
			d.dealid = sa.dealid and
			d.destinacia = 'splatenieakcia'
		
		union all
			
		select
		  	3 as typ,
		  	COALESCE(sum(round(iv_b*(SELECT kurz FROM f_menovy_kurz_lot(k.mena,'$refmena',m.OBRATDATATIMEZAUCTOVANE)),2)),0) as suma
		FROM
			konfirmaciaktv k,
			ktvexportobratid ke,
			majetokarchiv m
		where
			k.subjektid = $fondid and
			k.logactivityid in (23,24,25) and
			ke.dealid = k.dealid and
			ke.status = 2 and
			m.obratid = ke.obratid and
			m.OBRATDATATIMEZAUCTOVANE between to_date('$fromdate','YYYY-MM-DD') and to_date('$todate','YYYY-MM-DD') and
			m.uctovnykod = 315113 and
			m.kodobratu = 115	
			
		union all
		
		select
			3 as typ,
			sum(round(COALESCE(pr.auvreal*(SELECT kurz FROM f_menovy_kurz_lot(k.mena,'$refmena',m.OBRATDATATIMEZAUCTOVANE)),0),2)) as suma
		FROM
			konfirmaciaktv k,
			ktvexportobratid ke,
			majetokarchiv m,
			pool p,
			pooldetailreal pr
		where
			k.subjektid = 0 and
			k.logactivityid in (23,24,25) and
			ke.dealid = k.dealid and
			ke.status = 2 and
			m.obratid = ke.obratid and
			m.OBRATDATATIMEZAUCTOVANE between to_date('$fromdate','YYYY-MM-DD') and to_date('$todate','YYYY-MM-DD') and
			m.uctovnykod = 315113 and
			m.kodobratu = 115	and
			p.dealid = k.dealid and
			pr.poolid = p.poolid and
			pr.subjektid = $fondid and
			m.subjektid = pr.subjektid

		union all		

		select
			4 as typ,
			COALESCE(sum(pocet*(SELECT kurz FROM f_menovy_kurz_lot(m.mena,'$refmena',m.OBRATDATATIMEZAUCTOVANE))),0) as suma 
		FROM
			majetokarchiv m
		where
		 	m.OBRATDATATIMEZAUCTOVANE between to_date('$fromdate','YYYY-MM-DD') and to_date('$todate','YYYY-MM-DD') and
		 	m.subjektid = $fondid and
		 	m.uctovnykod = 668000 and
		 	m.md_d = 1 and
		  	not exists
		  	(
		  		select 
					1
		  		FROM
		  			konfirmaciapp k,
		  			obratybu o,
		  			obratybuobratid oo
		  		where
		  			k.subjektid = $fondid and
		  			k.logactivityid = 12 and
		  			o.subjektid = k.subjektid and
		  			o.ss = to_char(k.dealid, '*') and
		  			o.suma = k.suma and
		  			o.mena = k.mena and
		  			o.cub = k.ucet and
		  			oo.id = o.id and
		  			oo.obratid = m.obratid
                  		
				union all
                  		
				select 
					1
		  		FROM
					majetokarchiv ma
				where
					ma.OBRATDATATIMEZAUCTOVANE = m.OBRATDATATIMEZAUCTOVANE and
					ma.subjektid = $fondid and
					ma.uctovnykod = 668000 and
					ma.md_d = 0 and
					ma.KODOBRATU = 214 
		 	)
	";
$queryRes = Connection::getDataFromDatabase($query, defaultDB);
$resultQuery = $queryRes[1];

$typ2 = '';
$suma2 = 0;
$sumaTyp2 = array();

foreach ($resultQuery as $item) {
    $typ2 = $item["typ"];
    $suma2 = $item["suma"];
    $sumaTyp2[$typ2] += $suma2;
}

$vypis_obsah = $vypis_obsah . '
		<br><br><br>
		<table style="width:100%;border-collapse:collapse;">
			<tr>
				<td style="width:70%;text-align:left;" ><h3 style="color:' . $farbaciara2 . '">Prijaté platby</h3></td>
				<td style="width:30%;text-align:right;"><b>Referenčná mena ' . $refmena . '</b></td>
			</tr>
			<tr style="width:100%;line-height:1pt;">
				<td colspan="2" style="height:1pt;min-width:100%;width:100%;"></td>
			</tr>	
		</table>
		<br>		
		<table style="width:100%;border-collapse:collapse;border-top:1pt solid ' . $farbaciara3 . ';border-bottom:1pt solid ' . $farbaciara3 . ';">
			<tr style="width:100%;line-height:1pt;">
				<td colspan="2" style="height:1pt;min-width:100%;width:100%;"></td>
			</tr>
			<tr>
				<td style="width:70%;text-align:left;">Dividendy:</td>
				<td style="width:30%;text-align:right;">' . floatval($sumaTyp2[2]) . '</td>
			</tr>
			<tr>
				<td style="width:70%;text-align:left;">Kupóny:</td>
				<td style="width:30%;text-align:right;">' . floatval($sumaTyp2[1]) . '</td>
			</tr>
			<tr>
				<td style="width:70%;text-align:left;">Úroky:</td>
				<td style="width:30%;text-align:right;">' . floatval($sumaTyp2[3]) . '</td>
			</tr>
			<tr>
				<td style="width:70%;text-align:left;">Ostatné prijaté platby:</td>
				<td style="width:30%;text-align:right;">' . floatval($sumaTyp2[4]) . '</td>
			</tr>
			<tr style="width:100%;line-height:1pt;">
				<td colspan="2" style="height:1pt;min-width:100%;width:100%;"></td>
			</tr>
		</table>
	';

//---------------------- Naklady na dane a poplatky ----------------------
$query = "
	select
			1 as typ,
		  	COALESCE(sum(d.dan*(SELECT kurz FROM f_menovy_kurz_lot(d.mena,'$refmena',s.datumvyplaty))),0)  as suma
		FROM
			danvynosy d,
			splatenieakcia s
		where
			 d.destinacia = 'splatenieakcia' and
			 d.dealid = s.dealid and
			 s.subjektid = $fondid and
			 s.dealid = d.dealid and
			 s.subkodobratu <=9 and
			 s.datumvyplaty >= to_date('$fromdate','YYYY-MM-DD') and
			 s.datumvyplaty <= to_date('$todate','YYYY-MM-DD') and
			 s.fiktivne = 0	
		
		union all
		
		select
			2 as typ,
			COALESCE(sum(d.dan*(SELECT kurz FROM f_menovy_kurz_lot(s.mena,'$refmena',s.datumvyplaty))),0) as suma 
		FROM
		    splatenie s,
			danvynosy d
		where
			s.subjektid = $fondid and 
		    s.datumvyplaty >= to_date('$fromdate','YYYY-MM-DD') and
			s.datumvyplaty <= to_date('$todate','YYYY-MM-DD') and
			s.uctovnykod = (251120) and
			d.destinacia = 'splatenie' and
			d.dealid = s.dealid and
			s.fiktivne = 0			 
		
		union all
		
		select
		  	3 as typ,
		  	COALESCE(sum(round(suma_dane*(SELECT kurz FROM f_menovy_kurz_lot(k.mena,'$refmena',m.OBRATDATATIMEZAUCTOVANE)),2)),0) as suma
		FROM
			konfirmaciaktv k,
			ktvexportobratid ke,
			majetokarchiv m
		where
			k.subjektid = $fondid and
			k.logactivityid in (23,24,25) and
			ke.dealid = k.dealid and
			ke.status = 2 and
			m.obratid = ke.obratid and
			m.OBRATDATATIMEZAUCTOVANE between to_date('$fromdate','YYYY-MM-DD') and to_date('$todate','YYYY-MM-DD') and
			m.uctovnykod = 315113 and
			m.kodobratu = 115		
			
		union all
		
		select
			4 as typ,
			sum(round(COALESCE(pr.dan*(SELECT kurz FROM f_menovy_kurz_lot(k.mena,'$refmena',m.OBRATDATATIMEZAUCTOVANE)),0),2)) as suma
		FROM
			konfirmaciaktv k,
			ktvexportobratid ke,
			majetokarchiv m,
			pool p,
			pooldetailreal pr
		where
			k.subjektid = 0 and
			k.logactivityid in (23,24,25) and
			ke.dealid = k.dealid and
			ke.status = 2 and
			m.obratid = ke.obratid and
			m.OBRATDATATIMEZAUCTOVANE between to_date('$fromdate','YYYY-MM-DD') and to_date('$todate','YYYY-MM-DD') and
			m.uctovnykod = 315113 and
			m.kodobratu = 115	and
			p.dealid = k.dealid and
			pr.poolid = p.poolid and
			pr.subjektid = $fondid and
			m.subjektid = pr.subjektid
			
		union all
		
		select
			5 as typ,
			sum((suma-(COALESCE(p.dan,0)))*(SELECT kurz FROM f_menovy_kurz_lot(p.mena,'$refmena',p.datum))) as suma
		FROM
			poplatok_register p
		where
			p.fondid = $fondid and
			p.datum >= to_date('$fromdate','YYYY-MM-DD') and
			p.datum <= to_date('$todate','YYYY-MM-DD') and
			p.stav in (0,1,2,3,4) and
			p.typ in ('MANAZ','SPRAVA')
			
		union all
		
		select
			6 as typ,
			sum((suma-(COALESCE(p.dan,0)))*(SELECT kurz FROM f_menovy_kurz_lot(p.mena,'$refmena',p.datum))) as suma
		FROM
			poplatok_register p
		where
			p.fondid = $fondid and
			p.datum >= to_date('$fromdate','YYYY-MM-DD') and
			p.datum <= to_date('$todate','YYYY-MM-DD') and
			p.stav in (0,2,3,4) and
			p.typ in ('VYROV','TRAN')
			
		union all
		
		select
			7 as typ,
			sum((suma-(COALESCE(p.dan,0)))*(SELECT kurz FROM f_menovy_kurz_lot(p.mena,'$refmena',p.datum))) as suma
		FROM
			poplatok_register p
		where
			p.fondid = $fondid and
			p.datum >= to_date('$fromdate','YYYY-MM-DD') and
			p.datum <= to_date('$todate','YYYY-MM-DD') and
			p.stav in (0,1,2,3,4) and
			p.typ in ('OSTATNE')
		
		union all
		
		select
			8 as typ,
			sum(p.dan*(SELECT kurz FROM f_menovy_kurz_lot(p.mena,'$refmena',p.datum))) as suma
		FROM
			poplatok_register p
		where
			p.fondid = $fondid and
			p.datum >= to_date('$fromdate','YYYY-MM-DD') and
			p.datum <= to_date('$todate','YYYY-MM-DD') and
			p.stav in (0,1,2,3,4) 
		
		union all
		
		select
			9 as typ, 
			COALESCE(sum(pocet*(SELECT kurz FROM f_menovy_kurz_lot(m.mena,'$refmena',m.OBRATDATATIMEZAUCTOVANE))),0) as suma 
		FROM
			majetokarchiv m
		where
		 	m.OBRATDATATIMEZAUCTOVANE between to_date('$fromdate','YYYY-MM-DD') and to_date('$todate','YYYY-MM-DD') and
		 	m.subjektid = $fondid and
		 	m.uctovnykod = 668000 and
		 	m.md_d = 1 and
		  	not exists
		  	(
		  		select 
					1
		  		FROM
		  			konfirmaciapp k,
		  			obratybu o,
		  			obratybuobratid oo
		  		where
		  			k.subjektid = $fondid and
		  			k.logactivityid = 12 and
		  			o.subjektid = k.subjektid and
		  			o.ss = to_char(k.dealid, '*') and
		  			o.suma = k.suma and
		  			o.mena = k.mena and
		  			o.cub = k.ucet and
		  			oo.id = o.id and
		  			oo.obratid = m.obratid
                  		
				union all
                  			
				select 
					1
		  		FROM
					majetokarchiv ma
				where
					ma.OBRATDATATIMEZAUCTOVANE = m.OBRATDATATIMEZAUCTOVANE and
					ma.subjektid = $fondid and
					ma.uctovnykod = 668000 and
					ma.md_d = 0 and
					ma.KODOBRATU = 214
		 	)		

		union all
		
		select
			10 as typ,
			COALESCE(sum(pocet*(SELECT kurz FROM f_menovy_kurz_lot(m.mena,'$refmena',m.OBRATDATATIMEZAUCTOVANE))),0) as suma 
		FROM
			majetokarchiv m
		where
		 	m.OBRATDATATIMEZAUCTOVANE between to_date('$fromdate','YYYY-MM-DD') and to_date('$todate','YYYY-MM-DD') and
		 	m.subjektid = $fondid and
		 	m.uctovnykod = 560000 and
		 	m.md_d = 0
		
		union all
		
		select
			11 as typ,
			COALESCE(sum(s.suma*(SELECT kurz FROM f_menovy_kurz_lot(s.mena,'$refmena',s.datumvyplaty))),0) as suma
		FROM
		    splatenie s		    	
		where
			s.subjektid = $fondid and 
		  	s.datumvyplaty >= to_date('$fromdate','YYYY-MM-DD') and
			s.datumvyplaty <= to_date('$todate','YYYY-MM-DD') and
			s.uctovnykod = (251110) and
			s.fiktivne = 1			

		union all
		
		select
			12 as typ,
			COALESCE(sum(d.danzaklad*(SELECT kurz FROM f_menovy_kurz_lot(s.mena,'$refmena',s.datumvyplaty))),0) as suma
		FROM
			splatenie s,
			danvynosy d
		where
			s.subjektid = $fondid and 
		  	s.datumvyplaty >= to_date('$fromdate','YYYY-MM-DD') and
			s.datumvyplaty <= to_date('$todate','YYYY-MM-DD') and
			s.uctovnykod = (251120) and
			s.fiktivne = 1 and
			d.dealid = s.dealid and
			d.destinacia='splatenie'

		union all
		
		select
			13 as typ,
			COALESCE(sum(d.danzaklad*(SELECT kurz FROM f_menovy_kurz_lot(sa.mena,'$refmena',sa.datumvyplaty))),0) as suma 
		FROM
			splatenieakcia sa,
			danvynosy d
		where
			sa.subjektid = $fondid and
			sa.datumvyplaty >= to_date('$fromdate','YYYY-MM-DD') and
			sa.datumvyplaty <= to_date('$todate','YYYY-MM-DD') and
			sa.subkodobratu <=9 and
			sa.fiktivne = 1 and
			d.dealid = sa.dealid and
			d.destinacia = 'splatenieakcia'			

		union all
		
		select
			14 as typ,
		  	COALESCE(sum(d.dan*(SELECT kurz FROM f_menovy_kurz_lot(d.mena,'$refmena',s.datumvyplaty))),0) as suma
		FROM
			danvynosy d,
			splatenieakcia s
		where
			 d.destinacia = 'splatenieakcia' and
			 d.dealid = s.dealid and
			 s.subjektid = $fondid and
			 s.dealid = d.dealid and
			 s.subkodobratu <=9 and
			 s.datumvyplaty >= to_date('$fromdate','YYYY-MM-DD') and
			 s.datumvyplaty <= to_date('$todate','YYYY-MM-DD') and
			 s.fiktivne = 1		
		
		union all
		
		select
			15 as typ,
			COALESCE(sum(d.dan*(SELECT kurz FROM f_menovy_kurz_lot(s.mena,'$refmena',s.datumvyplaty))),0) as suma 
		FROM
		    splatenie s,
			danvynosy d
		where
			s.subjektid = $fondid and 
		    s.datumvyplaty >= to_date('$fromdate','YYYY-MM-DD') and
			s.datumvyplaty <= to_date('$todate','YYYY-MM-DD') and
			s.uctovnykod = (251120) and
			d.destinacia = 'splatenie' and
			d.dealid = s.dealid and
			s.fiktivne = 1	

		union all

		select
		  	16 as typ,
		  	COALESCE(sum(round(rcp.poplatok*(SELECT kurz FROM f_menovy_kurz_lot(kcp.currencyidtrade,'$refmena',rcp.datvysporiadaniabureal)),2)),0) as suma
		FROM
			konfirmaciacp kcp,
			rekonfirmaciacp rcp
		where
			kcp.dealid = rcp.dealid and
			kcp.subjektid = $fondid and
			kcp.logactivityid = 12 and
			rcp.datvysporiadaniabureal between to_date('$fromdate','YYYY-MM-DD') and to_date('$todate','YYYY-MM-DD')
						
		union all
		
		select
			17 as typ,
			sum(round(COALESCE(pr.poplatok*(SELECT kurz FROM f_menovy_kurz_lot(kcp.currencyidtrade,'$refmena',rcp.datvysporiadaniabureal)),0),2)) as suma
		FROM
			konfirmaciacp kcp,
			rekonfirmaciacp rcp,
			pool p,
			pooldetailreal pr
		where
			kcp.dealid = rcp.dealid and
			kcp.logactivityid = 12 and
			rcp.datvysporiadaniabureal between to_date('$fromdate','YYYY-MM-DD') and to_date('$todate','YYYY-MM-DD') and
			kcp.subjektid = 0 and
			p.dealid = kcp.dealid and
			pr.poolid = p.poolid and
			pr.subjektid = $fondid			
	";
$queryRes = Connection::getDataFromDatabase($query, defaultDB);
$resultQuery = $queryRes[1];

$typ = '';
$suma = 0;
$sumaTyp = array();
$danPrijem = 0;
$poplSpravaRiadenie = 0;
$poplTranVyrov = 0;
$poplOstatne = 0;
$DPH = 0;
$financneVynosy = 0;
$financneNaklady = 0;
$fiktivnaIstina = 0;
$fiktivnyKupon = 0;
$fiktivnaDividenda = 0;
$fiktivneDane = 0;
$poplTriStran = 0;

foreach ($resultQuery as $item) {
    $typ = $item["typ"];
    $suma = $item["suma"];
    $sumaTyp[$typ] = $suma;
}

$danPrijem = $sumaTyp[1] + $sumaTyp[2] + $sumaTyp[3] + $sumaTyp[4];
$poplSpravaRiadenie = $sumaTyp[5];
$poplTranVyrov = $sumaTyp[6];
$poplOstatne = $sumaTyp[7] + $sumaTyp[10];
$DPH = $sumaTyp[8];
$financneVynosy = $sumaTyp[9];
$financneNaklady = $sumaTyp[10];
$fiktivnaIstina = $sumaTyp[11];
$fiktivnyKupon = $sumaTyp[12];
$fiktivnaDividenda = $sumaTyp[13];
$fiktivneDane = $sumaTyp[14] + $sumaTyp[15];
$poplTriStran = $sumaTyp[16] + $sumaTyp[17];

$vypis_obsah = $vypis_obsah . '
		<br><br><br>
		<table style="width:100%;border-collapse:collapse;">
			<tr>
				<td style="width:70%;text-align:left;"><h3 style="color:' . $farbaciara2 . '">Dane a poplatky</h3></td>
				<td style="width:30%;text-align:right;"><b>Referenčná mena ' . $refmena . '</b></td>
			</tr>
			<tr style="width:100%;line-height:1pt;">
				<td colspan="2" style="height:1pt;min-width:100%;width:100%;"></td>
			</tr>
		</table>
		<br>
		<table style="width:100%;border-collapse:collapse;border-top:1pt solid ' . $farbaciara3 . ';border-bottom:1pt solid ' . $farbaciara3 . ';">
			<tr style="width:100%;line-height:1pt;">
				<td colspan="2" style="height:1pt;min-width:100%;width:100%;"></td>
			</tr>
			<tr>
				<td style="width:80%;text-align:left;">Poplatky za riadenie a správu:</td>
				<td style="width:20%;text-align:right;">' . floatval($poplSpravaRiadenie) . '</td>
			</tr>
			<tr>
				<td style="width:80%;text-align:left;">Poplatky za transakcie a ich vysporiadanie:</td>
				<td style="width:20%;text-align:right;">' . floatval($poplTranVyrov) . '</td>
			</tr>
			<tr>
				<td style="width:80%;text-align:left;">Poplatky tretích strán z obchodovania:</td>
				<td style="width:20%;text-align:right;">' . floatval($poplTriStran) . '</td>
			</tr>
			<tr>
				<td style="width:80%;text-align:left;">Ostatné poplatky a náklady:</td>
				<td style="width:20%;text-align:right;">' . floatval($poplOstatne) . '</td>
			</tr>
			<tr>
				<td style="width:80%;text-align:left;">DPH:</td>
				<td style="width:20%;text-align:right;">' . floatval($DPH) . '</td>
			</tr> 
			<tr>
				<td style="width:80%;text-align:left;">Celkové poplatky a náklady:</td>
				<td style="width:20%;text-align:right;">' . floatval($poplSpravaRiadenie + $poplTranVyrov + $poplTriStran + $poplOstatne + $DPH) . '</td>
			</tr>
			<tr>
				<td style="width:80%;text-align:left;">Kumulatívny vplyv celkových poplatkov a nákladov na priemernú výšku investície:</td>
	';

if ($priemobjem == 0) {
    $vypis_obsah = $vypis_obsah . '
				<td style="width:20%;text-align:right;font-style:italic;">NA</td>
			';
} else {
    $vypis_obsah = $vypis_obsah . '
				<td style="width:20%;text-align:right;">' . floatval(-100 * ($poplSpravaRiadenie + $poplTranVyrov + $poplTriStran + $poplOstatne + $DPH) / $priemobjem) . ' %</td>
			';
}

$vypis_obsah = $vypis_obsah . '			
			</tr>
			<tr>
				<td style="width:80%;text-align:left;">Zrazená daň z príjmov:</td>
				<td style="width:20%;text-align:right;">' . floatval($danPrijem) . '</td>
			</tr>
			<tr style="width:100%;line-height:1pt;">
				<td colspan="2" style="height:1pt;min-width:100%;width:100%;"></td>
			</tr>
		</table>
		';
$vypis_zhodnotenie = $vypis_obsah;
﻿<?php
if (isset($pdf)) unset($pdf);
$pdf = new MYPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);
$pdf->SetCreator(PDF_CREATOR);
$pdf->Set<PERSON>uthor("Sympatia Financie, o.c.p., a.s.");
$pdf->SetTitle('Výpis');
$pdf->SetHeaderData(PDF_HEADER_LOGO, PDF_HEADER_LOGO_WIDTH, PDF_HEADER_TITLE, PDF_HEADER_STRING);
$pdf->setHeaderFont(array('helvetica', '', 8));
$pdf->setFooterFont(array('helvetica', '', 8));
$pdf->SetTopMargin(40);
$pdf->SetHeaderMargin(20);
$pdf->SetFooterMargin(20);
$pdf->SetAutoPageBreak(TRUE, 40);
$pdf->SetFont('helvetica', '', 10);
$pdf->AddPage();
<?php
$vypisyspolu = 0;

$vypis_obsah = '<br><br><br>		
		<table style="width:100%;border-collapse:collapse;">
			<tr  style="width:100%;text-align:center;">
				<td style="height:26.25pt;">
					<div style="color: #6B819B;">
						<span style="font-size: 21pt;font-weight:400;">Prehľad transakcií</span>
					</div>	
				</td>
			</tr>
			<tr style="width:100%;">
				<td style="height:20pt;min-width:100%;width:100%;"></td>
			</tr>	
			<tr  style="width:100%;text-align:center;">
				<td style="height:26.25pt;vertical-align:bottom;">
					<div  style="color: #000;vertical-align:bottom;">
						<span style="font-size: 9.25pt;font-weight:600;"><b>od ' . $fromdate . ' do ' . $todate . '</b></span>
					</div>	
				</td>
			</tr>
		</table>
		<br><br>
		<table>
			<tr style="font-weight:normal;color: #0E0E0E;">
				<td style="min-width:4%;width:4%;"></td>
				<td style="vertical-align:bottom;width:71%;text-align:left;height:10pt;"><div style="vertical-align:bottom;"><span style="font-weight:400;font-size:7.5pt;">Klient:    </span><span style="font-weight:700;font-size:9.75pt;">' . $klient . '</span></div></td>
				<td style="vertical-align:bottom;width:20%;text-align:right;height:10pt;"><div style="vertical-align:bottom;"><span style="font-weight:400;font-size:7.5pt;">Portfólio:    </span><span style="font-weight:700;font-size:9.75pt;">' . $cislozmluvy . '</span></div></td>
				<td style="min-width:5%;width:5%;"></td>
			</tr>
		</table>';

$getMaxDateForKlient = "SELECT max(datum) FROM majetoktotal WHERE subjektid = $fondid";
$clientToDate = Connection::getDataFromDatabase($getMaxDateForKlient, defaultDB)[1][0]['max'];

$fondid_add = "subjektid = $fondid";
include "queries/vypisBeznyUcetQuery.php";

$data = Connection::getDataFromDatabase($queryTransakcie, defaultDB);
$dataLen = $data[0];
$data = $data[1][0];
$cnt = 0;
$mena_foo = "";
$sekcia_ukoncena = true;
$sumacheck = 0;
$sumaAlert = "";

for ($i = 0; $i < $dataLen; $i++) {
    $datum = ($data['datum'] !== '') ? $data['datum'] : '';
    $poznamka = ($data['poznamka'] !== '') ? " (" . $data['poznamka'] . ")" : '';
    $popis = $data['popis'];
    $suma = $data['transsuma'];
    $mena = $data['mena1'];
    $sumacheck = ($mena_foo != $mena) ? 0 : $sumacheck;
    $poradie = $data['poradie'];
    if (intval($poradie) == 100000) {
        $sekcia_ukoncena = true;
        if (round($sumacheck, 2) != round($suma, 2)) {
            $sumaAlert = $sumaAlertBottom = "Neplatná zostava !";
        }
    } else {
        $sumacheck += $data['md_d'] ? -$suma : $suma;
    }
    if ($mena_foo != $mena) {
        if ($mena_foo != "") {
            if (!$sekcia_ukoncena) {
                //pokial prebehli transakcie na ucte v jednom dni a v majetoktotal neexistuje zaznam, zostatok je nulovy
                //teda v poslednom dni prebehli transakcie a zostatok na ucte je nulovy, do majetoktotal sa nezapise ...
                $vypis_obsah = $vypis_obsah . '
					<tr>
						<td style="width:20%;text-align:left;font-size:8;"><b>' . $todate . '</b></td>
						<td style="width:50%;text-align:left;font-size:8;"><b>Konečný stav účtu</b></td>
						<td style="width:30%;text-align:right;font-size:8;"><b>' . bcdiv(0, 1, 2) . '</b></td>
					</tr>
				';
                if (isset($predoslasumacheck)) {
                    if (round($predoslasumacheck, 2) != round(0, 2)) {
                        $sumaAlert = $sumaAlertBottom = "Neplatná zostava !";
                    }
                }
            }
            $vypis_obsah = $vypis_obsah . '	
						<tr style="width:100%;line-height:1pt;">
							<td colspan="3" style="height:1pt;min-width:100%;width:100%;"></td>
						</tr>	
					</tbody>
				</table>
			';
            if ($sumaAlert != "") {
                $vypis_obsah = $vypis_obsah . '
					<table>
						<tr>
							<td style="font-size:4.5mm;">' . $sumaAlert . '</td>
						</tr>
					</table>
				';
                $sumaAlert = "";
            }
        }
        $sekcia_ukoncena = false;
        $vypisyspolu++;
        $vypis_obsah = $vypis_obsah . '
            <table style="width:100%;border-collapse:collapse;">
                <tr>
                    <td>
                        <h3 style="color:' . $farbaciara2 . '">Výpis z bežného účtu (' . $mena . ')</h3>
                    </td>
                </tr>
            </table>	
            <br><br>
            <table style="width:100%;border-collapse:collapse;border-top:1pt solid ' . $farbaciara3 . ';border-bottom:1pt solid ' . $farbaciara3 . ';">			
				<tr>
					<th style="text-align:left;font-size:8;" ><b>Dátum</b></th>
					<th style="text-align:right;font-size:8;" ><b>Transakcia</b></th>
					<th style="text-align:right;font-size:8;" ><b>Čiastka</b></th>
				</tr>
				<tr style="width:100%;line-height:1pt;">
					<td colspan="3" style="border-bottom:0.1mm solid ' . $farbaciara1 . ';height:1pt;min-width:100%;width:100%;"></td>
				</tr>
			';
        if ($poradie != -1) {
            $vypis_obsah = $vypis_obsah . '
				<tr class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b dark:border-gray-700">
					<td style="text-align:left;font-size:8px;"><b>' . $fromdate . '</b></td>
					<td style="text-align:left;font-size:8px;"><b>Počiatočný stav účtu</b></td>
					<td style="text-align:left;font-size:8px;"><b>' . bcdiv(0, 1, 2) . '</b></td>
				</tr>
				';
        }
    }
    $vypis_obsah = $vypis_obsah . '
			<tr class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b dark:border-gray-700">
				<td style="text-align:left;font-size:8px;">' . $datum . '</td>
				<td style="text-align:left;font-size:8px;">' . $popis . $poznamka . '</td>
				<td style="text-align:left;font-size:8px;">' . ($data['md_d'] == 0 ? '' : '-') . bcdiv($suma, 1, 2) . '</td>
			</tr>
		';
    $mena_foo = $mena;
    $predoslasuma = $suma;
    $predoslasumacheck = $sumacheck;
    $cnt = $i;
}

/**
 * pokial prebehli transakcie na ucte v jednom dni a v majetoktotal neexistuje zaznam, zostatok je nulovy
 * teda v poslednom dni prebehli transakcie a zostatok na ucte je nulovy, do majetoktotal sa nezapise ...
 */
if (!$sekcia_ukoncena) {
    $vypis_obsah = $vypis_obsah . '
				<tr class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 border-b">
					<td style="text-align:left;font-size:8px;"><b>' . $todate . '</b></td>
					<td style="text-align:left;font-size:8px;"><b>Konečný stav účtu</b></td>
					<td style="text-align:left;font-size:8px;"><b>' . bcdiv(0, 1, 2) . '</b></td>
				</tr>
			</tbody>
		</table>';
}
if ($sekcia_ukoncena and $cnt > 0) {
    $vypis_obsah = $vypis_obsah . '				
				<tr style="width:100%;line-height:1pt;">
					<td colspan="3" style="height:1pt;min-width:100%;width:100%;"></td>
				</tr>		
			</tbody>
		</table>
	';
}
if ($sumaAlert != "") {
    $vypis_obsah = $vypis_obsah . '			
		<table>
			<tr>
				<td style="font-size:4.5mm;">' . $sumaAlert . '</td>
			</tr>
		</table>
	';
    $sumaAlert = "";
}

//------------------Terminovane vklady-----------------------------
$fondid_add = "subjektid in ($fondid) ";
$query = "SELECT *
FROM (SELECT CASE
                 WHEN z_td < to_date('$fromdate', 'YYYY-MM-DD') THEN 1
                 WHEN k_td > to_date('$todate', 'YYYY-MM-DD') THEN 3
                 ELSE 2 END                  AS trieda,
             k.z_td                          AS zaciatok,
             k.k_td                          AS koniec,
             k.sum_td                        AS suma,
             k.ir_td                         AS sadzba,
             k.iv_b                          AS brutto,
             k.iv_n                          AS netto_old,
             k.iv_n                          AS netto,
             k.mena,
             k.suma_dane                     AS dan,
             k.datvysporiadaniarealntd       AS vysporiadanie,
             k.cutd,
             k.subjektid,
             z_td,
             k_td,
             p.nazovpartnera                 AS miesto,
             k.datum_cas_obchodu             AS datum_cas,
             (SELECT typ_pokynu
              FROM typy_pokynov tp
              WHERE tp.typid = k.typ_pokynu) AS typPokynu,
             ''                              AS detailKTV
      FROM konfirmaciaktv k
               JOIN partner p ON k.partnerid = p.partnerid
      WHERE (
          (
              z_td >= to_date('2023-01-01', 'YYYY-MM-DD')
                  AND z_td <= to_date('2024-05-01', 'YYYY-MM-DD')
              )
              OR (
              k_td >= to_date('2023-01-01', 'YYYY-MM-DD')
                  AND k_td <= to_date('2024-05-01', 'YYYY-MM-DD')
              )
          )
        AND logactivityid IN (17, 25)
        AND k.$fondid_add
      UNION ALL
      SELECT CASE
                 WHEN z_td < to_date('2023-01-01', 'YYYY-MM-DD') THEN 1
                 WHEN k_td > to_date('2024-05-01', 'YYYY-MM-DD') THEN 3
                 ELSE 2 END                                              AS trieda,
             k.z_td                                                      AS zaciatok,
             k.k_td                                                      AS koniec,
             pdr.transsumareal                                           AS suma,
             k.ir_td                                                     AS sadzba,
             pdr.auvreal                                                 AS brutto,
             k.iv_n * pdr.transsumareal / (SELECT sum(x.transsumareal)
                                           FROM pooldetailreal x
                                           WHERE x.poolid = po.poolid)   AS netto_old,
             (pdr.auvreal - pdr.dan)                                     AS netto,
             k.mena,
             pdr.dan                                                     AS dan,
             k.datvysporiadaniarealntd                                   AS vysporiadanie,
             k.cutd,
             k.subjektid,
             z_td,
             k_td,
             p.nazovpartnera                                             AS miesto,
             k.datum_cas_obchodu                                         AS datum_cas,
             (SELECT typ_pokynu
              FROM typy_pokynov tp
              WHERE tp.typid = k.typ_pokynu)                             AS typPokynu,
             CASE WHEN sum_td_pov IS NULL THEN '' ELSE 'uzavreteSKK' END AS detailKTV
      FROM konfirmaciaktv k
               JOIN pool po ON k.dealid = po.dealid
               JOIN pooldetailreal pdr ON pdr.poolid = po.poolid
          AND pdr.subjektid in (1752)
               JOIN partner p ON k.partnerid = p.partnerid
      WHERE (
          (
              z_td >= to_date('$fromdate', 'YYYY-MM-DD')
                  AND z_td <= to_date('$todate', 'YYYY-MM-DD')
              )
              OR (
              k_td >= to_date('$fromdate', 'YYYY-MM-DD')
                  AND k_td <= to_date('$todate', 'YYYY-MM-DD')
              )
          )
        AND k.subjektid = 0
        AND k.logactivityid IN (17, 25)
      UNION ALL
      SELECT CASE
                 WHEN z_td < to_date('$fromdate', 'YYYY-MM-DD') THEN 1
                 WHEN k_td > to_date('$todate', 'YYYY-MM-DD') THEN 3
                 ELSE 2 END                  AS trieda,
             k.z_td                          AS zaciatok,
             k.k_td                          AS koniec,
             pdr.transsumareal_pov           AS suma,
             k.ir_td                         AS sadzba,
             0                               AS brutto,
             0                               AS netto_old,
             0                               AS netto,
             'SKK'                           AS mena,
             0                               AS dan,
             k.datvysporiadaniarealntd       AS vysporiadanie,
             k.cutd,
             k.subjektid,
             z_td,
             k_td,
             p.nazovpartnera                 AS miesto,
             k.datum_cas_obchodu             AS datum_cas,
             (SELECT typ_pokynu
              FROM typy_pokynov tp
              WHERE tp.typid = k.typ_pokynu) AS typPokynu,
             'vyplateneEUR'                  AS detailKTV
      FROM konfirmaciaktv k
               JOIN pool po ON k.dealid = po.dealid
               JOIN pooldetailreal pdr ON pdr.poolid = po.poolid
          AND pdr.$fondid_add
               JOIN partner p ON k.partnerid = p.partnerid
      WHERE (
          (
              z_td >= to_date('$fromdate', 'YYYY-MM-DD')
                  AND z_td <= to_date('$todate', 'YYYY-MM-DD')
              )
              OR (
              k_td >= to_date('$fromdate', 'YYYY-MM-DD')
                  AND k_td <= to_date('$todate', 'YYYY-MM-DD')
              )
          )
        AND k.subjektid = 0
        AND k.logactivityid IN (17, 25)
        AND k.sum_td_pov IS NOT NULL -- ktv po konverzii preratane do EUR )
     ) result
ORDER BY mena, trieda, zaciatok, koniec;
";
$terminovaneVklady = Connection::getDataFromDatabase($query, defaultDB);
$terminovaneVklady = $terminovaneVklady[1][0];

$cnt = 0;
$mena_foo = "";
$trieda_foo = '';

for ($i = 0; $i < $terminovaneVklady[0]; $i++) {
    $cnt++;
    echo $cnt;
    $mena = $terminovaneVklady['mena'];
    echo "Mena: " . $mena;
    $suma = $terminovaneVklady['suma'];
    $sadzba = $terminovaneVklady['sadzba'];
    $brutto = $terminovaneVklady['brutto'];
    $dan = $terminovaneVklady['dan'];
    $netto = $terminovaneVklady['netto'];
    $zaciatok = $terminovaneVklady['zaciatok'];
    $koniec = $terminovaneVklady['koniec'];
    $cisloKTV = $terminovaneVklady['cutd'];
    $trieda = $terminovaneVklady['trieda'];
    $miesto = $terminovaneVklady['miesto'];
    $datum_cas = $terminovaneVklady['datum_cas'];
    $typ_pokynu = $terminovaneVklady['typPokynu'];
    $detailKTV = $terminovaneVklady['detailKTV'];

    $style_hr = "";

    if ($mena_foo != $mena) {
        if ($mena_foo != "") {
            $vypis_obsah = $vypis_obsah . '						
				</tbody>
				</table>
			';
        }
        $vypisyspolu++;
        $vypis_obsah = $vypis_obsah . '	
			<br><br><br>
			<table style="width:100%;border-collapse:collapse;">				
				<tr>
					<td>
						<h3style="color:' . $farbaciara2 . '">Prehľad termínovaných vkladov</h3>
		';
        if (!isset($mena) or $mena == '') $vypis_obsah . ' č ';
        $vypis_obsah = $vypis_obsah . $mena . '
					</td>
				</tr>	
			</table>
			<br><br>
			<table style="width:100%;border-collapse:collapse;border-top:1pt solid ' . $farbaciara3 . ';border-bottom:1pt solid ' . $farbaciara3 . ';">
				<thead>
					<tr>
						<th style="text-align:left;font-size:8;"><b>Uzavretie TV</b></th>
						<th style="text-align:left;font-size:8;"<b>Zriadenie TV</b></th>
						<th style="text-align:left;font-size:8;"><b>Splatnosť TV</b></th>
						<th style="text-align:left;font-size:8;"><b>Miesto<BR>výkonu</b></th>
						<th style="text-align:left;font-size:8;"><b>Typ<BR>pokynu</b></th>		
						<th style="text-align:right;font-size:8;"><b>Suma</b></th>
						<th style="text-align:right;font-size:8;"><b>Sadzba</b></th>
						<th style="text-align:right;font-size:8;"><b>rok brutto</b></th>
						<th style="text-align:right;font-size:8;"><b>Daň</b></th>
						<th style="text-align:right;font-size:8;"><b>rok netto</b></th>
					</tr>
				</thead>
				<tbody>
					<tr style="width:100%;line-height:1pt;">
						<td colspan="10" style="border-bottom:0.1mm solid ' . $farbaciara1 . ';height:1pt;min-width:100%;width:100%;"></td>
					</tr>
					<tr style="width:100%;line-height:1pt;">
						<td colspan="10" style="height:1pt;min-width:100%;width:100%;"></td>
					</tr>
		';

    } elseif ($trieda_foo != $trieda) {
        $style_hr = 'style="border-top: 0.25mm dashed #aaaaaa"';
    }

    $vypis_obsah = $vypis_obsah . '		
		<tr>
	';
    $vypis_obsah = $vypis_obsah . '		
			<td ' . $style_hr . '>' . $datum_cas . '</td>
		';
    $vypis_obsah = $vypis_obsah . '		
			<td  style="font-size:8;" ' . $style_hr . '>' . $zaciatok . '</td>
			<td  style="font-size:8;" ' . $style_hr . '>' . $koniec . '</td>
			<td  style="font-size:8;" ' . $style_hr . '>' . $miesto . '</td>
			<td  style="font-size:8;" ' . $style_hr . '>' . $typ_pokynu . '</td>		
			<td  style="text-align:right;font-size:8;" ' . $style_hr . '>' . bcdiv($suma, 1, 2) . '</td>
			<td  style="text-align:right;font-size:8;" ' . $style_hr . '>' . bcdiv($sadzba, 1, 4) . '</td>
	';
    $vypis_obsah = $vypis_obsah . '		
			<td colspan="3" style="text-align:center;font-size:8;" ' . $style_hr . '>Vyplatené v EUR</td>
		';
    $vypis_obsah = $vypis_obsah . '			
			</tr>
			<tr style="width:100%;line-height:1pt;">
				<td colspan="10" style="height:1pt;min-width:100%;width:100%;"></td>
			</tr>
		</tbody>
	</table>
	';

    $mena_foo = $mena;
    $trieda_foo = $trieda;
}

//------------------konverzie-----------------------------
$query = "SELECT *
FROM (
    SELECT
        k.datum_cas_obchodu AS datum_cas,
        p.nazovpartnera AS miesto,
        (SELECT typ_pokynu FROM typy_pokynov tp WHERE tp.typid = k.typ_pokynu) AS typPokynu,
        k.kurz,
        k.sumakredit,
        k.sumadebet,
        k.menakredit,
        k.menadebet,
        CASE WHEN k.typ_konverzie = 0 THEN 'Spot' WHEN k.typ_konverzie = 2 THEN 'Forward' END AS druhObchodu,
        TO_CHAR(k.dat_vysporiadaniecukredit, 'DD.MM.YYYY') AS datum
    FROM
        konverzia k,
        partner p
    WHERE
        k.logactivityid >= 12 AND
        k.logactivityid != 14 AND
        p.partnerid = k.partnerid AND
        k.$fondid_add AND
        k.dat_realizacia >= '$fromdate'::DATE AND
        k.dat_realizacia <= '$todate'::DATE

    UNION ALL

    SELECT
        k.datum_cas_obchodu AS datum_cas,
        p.nazovpartnera AS miesto,
        (SELECT typ_pokynu FROM typy_pokynov tp WHERE tp.typid = k.typ_pokynu) AS typPokynu,
        k.kurz,
        m.pocet AS sumakredit,
        pdr.transsumareal AS sumadebet,
        k.menakredit,
        k.menadebet,
        CASE WHEN k.typ_konverzie = 0 THEN 'Spot' WHEN k.typ_konverzie = 2 THEN 'Forward' END AS druhObchodu,
        TO_CHAR(k.dat_vysporiadaniecukredit, 'DD.MM.YYYY') AS datum
    FROM
        konverzia k,
        konverziaobratid ko,
        partner p,
        pool po,
        pooldetailreal pdr,
        majetokarchiv m
    WHERE
        k.logactivityid >= 12 AND
        k.logactivityid != 14 AND
        p.partnerid = k.partnerid AND
        k.subjektid = 0 AND
        pdr.$fondid_add AND
        po.poolid = pdr.poolid AND
        k.dealid = po.dealid AND
        ko.dealid = k.dealid AND
        m.subjektid = pdr.subjektid AND 
        m.obratid = ko.obratid AND 
        uctovnykod IN (315160, 315161) AND 
        m.mena = k.menakredit AND 
        m.destinacia = 'konverziaobratid' AND
        k.dat_realizacia >= '$fromdate'::DATE AND
        k.dat_realizacia <= '$todate'::DATE
) result
ORDER BY datum;	
";

$cnt = 0;
$konverzie = Connection::getDataFromDatabase($query, defaultDB);
$konverzieCount = $konverzie[0];
$konverzie = $konverzie[1][0];

for ($i = 0; $i < $konverzieCount; $i++) {
    $datum = $konverzie['datum'];
    $datum_cas = $konverzie['datum_cas'];
    $miesto = $konverzie['miesto'];
    $typPokynu = $konverzie['typPokynu'];
    $kurz = $konverzie['kurz'];
    $sumakredit = bcdiv($konverzie['sumakredit'], 1, 2);
    $sumadebet = bcdiv($konverzie['sumadebet'], 1, 2);
    $menakredit = $konverzie['menakredit'];
    $menadebet = $konverzie['menadebet'];
    $druhObchodu = $konverzie['druhObchodu'];
    if ($cnt == 0) {
        $vypisyspolu++;
        $vypis_obsah = $vypis_obsah . '			
			<br>
			<br>
			<br>
			<table style="width:100%;border-collapse:collapse;">				
				<tr>
					<td>
						<h3 style="color:' . $farbaciara2 . '">Prehľad menových konverzií</h3>
					</td>
				</tr>				
			</table>
			<br><br>
			<table style="width:100%;border-collapse:collapse;border-top:1pt solid ' . $farbaciara3 . ';border-bottom:1pt solid ' . $farbaciara3 . ';">
				<thead>
					<tr >
						<th style="width:13%;text-align:left;font-size:8;"><b>Typ konverzie</b></th>
						<th style="width:15%;text-align:center;font-size:8;"><b>Uzavretie<br>konverzie</b></th>
						<th style="width:15%;text-align:center;font-size:8;"><b>Vyrovnanie<br>konverzie</b></th>
						<th style="width:14%;text-align:center;font-size:8;"><b>Menový pár</b></th>
						<th style="width:7%;text-align:center;font-size:8;"><b>Kurz</b></th>
						<th style="width:16%;text-align:right;font-size:8;"><b>Objem nakupo-<BR>vanej meny</b></th>
						<th style="width:16%;text-align:right;font-size:8;"><b>Objem predá-<BR>vanej meny</b></th>
					</tr>
				</thead>
				<tbody>
					<tr style="width:100%;line-height:1pt;">
						<td colspan="7" style="border-bottom:0.1mm solid ' . $farbaciara1 . ';height:1pt;min-width:100%;width:100%;"></td>
					</tr>
					<tr style="width:100%;line-height:1pt;">
						<td colspan="7" style="height:1pt;min-width:100%;width:100%;"></td>
					</tr>
		';
    }

    $vypis_obsah = $vypis_obsah . '		
			<tr>			
				<td style="width:13%;text-align:left;font-size:8;"><div>' . $druhObchodu . '</div></td>
				<td style="width:15%;text-align:center;font-size:8;"><div>' . $datum_cas . '</div></td>			
				<td style="width:15%;text-align:center;font-size:8;"><div>' . $datum . '</div></td>
				<td style="width:14%;text-align:center;font-size:8;"><div>' . $menakredit . ' / ' . $menadebet . '</div></td>
				<td style="width:7%;text-align:right;font-size:8;"><div>' . bcdiv($kurz_mena, 1, 4) . '</div></td>
				<td style="width:16%;text-align:right;font-size:8;"><div>' . $sumakredit . '</div></td> 
				<td style="width:16%;text-align:right;font-size:8;"><div>' . $sumadebet . '</div></td>
			</tr>
	';
    $cnt++;
}
$vypis_obsah = $vypis_obsah . '
				
				<tr style="width:100%;line-height:1pt;">
					<td colspan="7" style="height:1pt;min-width:100%;width:100%;"></td>
				</tr>
			</tbody>
		</table>
	';


//------------------------------------pohyby na majetkovom ucte----------------------------------		
$pomerNominalu = "(CASE WHEN kcp.eqid='Bonds' THEN (select faktor from floatkupon where isincurrric = maj.kodaktiva and (maj.obratdatatimezauctovane - e.exfrekist)  >= datefrom and (maj.obratdatatimezauctovane - e.exfrekist) <= datetill  ) ELSE 1 END)";
$query = "
		select * 
		from (
			select	CASE
    WHEN kcp.dealid IN (46663904, 47124904) THEN 'Predčasné splatenie dlhopisu na výzvu emitenta'
    ELSE
        CASE
            WHEN kcp.druhobchodu = 'nakup' THEN 'Nákup'
            WHEN kcp.druhobchodu = 'predaj' THEN 'Predaj'
            WHEN kcp.druhobchodu = 'vklad' THEN 'Vklad'
            WHEN kcp.druhobchodu = 'vyber' THEN 'Výber'
            WHEN kcp.druhobchodu = 'vklad-pr' THEN 'Prevod z port.<br>č.' || (
                SELECT cislozmluvy
                FROM portfolio f
                WHERE fondid = (
                    SELECT fondid
                    FROM konfirmaciacp k2
                    WHERE k2.dealid = (kcp.dealid) - 1000
                    AND f.fondid = k2.subjektid
                )
            )
            WHEN kcp.druhobchodu = 'vyber-pr' THEN 'Prevod na port.<br>č.' || (
                SELECT cislozmluvy
                FROM portfolio f
                WHERE fondid = (
                    SELECT fondid
                    FROM konfirmaciacp k2
                    WHERE k2.dealid = (kcp.dealid) + 1000
                    AND f.fondid = k2.subjektid
                )
            )
            WHEN kcp.druhobchodu = 'vklad-vy' THEN 'Výmena CP - pripísanie CP'
            WHEN kcp.druhobchodu = 'vyber-vy' THEN 'Výmena CP - odpísanie CP'
        END
END AS popis,
					rcp.datvysporiadaniamureal,
					e.isin,
					e.isinreal,
					e.eqid,
					edruh.poddruheq as poddruheq,
					e.cpnaz,e.cpnazskratka,
					rcp.kusovreal as pocet,
					kcp.currencyidtrade,
					rcp.kurzreal as kurz,
					rcp.auvreal as auv,
					rcp.transsuma as spolu,
					round((rcp.kurzreal * rcp.kusovreal * coalesce(e.nominalemisie, 1)  * $pomerNominalu / 
					        (CASE WHEN kcp.eqid='Bonds' THEN 100 ELSE 1 END)),2) as bezAUV,
					maj.obratdatatimezauctovane as datum,
					currencyidtrade as mena,					
					case when kcp.dealid in (46663904,47124904) then '--'
						else p.nazovpartnera end as miesto,
					kcp.datum_cas_obchodu as datum_cas,					
					case when kcp.dealid in (46663904,47124904) then '--'
						else (select typ_pokynu from typy_pokynov tp where tp.typid = kcp.typ_pokynu) end as typPokynu,
					CASE
                        WHEN kcp.druhobchodu = 'nakup' THEN 0
                        WHEN kcp.druhobchodu = 'predaj' THEN 1
                        WHEN kcp.druhobchodu = 'vklad' THEN 0
                        WHEN kcp.druhobchodu = 'vyber' THEN 1
                        WHEN kcp.druhobchodu = 'vklad-pr' THEN 0
                        WHEN kcp.druhobchodu = 'vyber-pr' THEN 1
                        ELSE NULL
                    END AS MD_D
			from	konfirmaciacp kcp,
					rekonfirmaciacp rcp,
					rekonfirmaciacpobratid rcpo,
					dbequity e,
					equityid eid,
					equitydruh edruh,
					(select obratid, obratdatatimezauctovane, kodaktiva
					 from 	majetoktoday
					 where	obratid > 0
							and uctovnykod in (251110,251200,251300)
							and obratdatatimezauctovane >= to_date('$fromdate','YYYY-MM-DD')
							and obratdatatimezauctovane <= to_date('$todate','YYYY-MM-DD')
							and destinacia not like 'muprevod'
					 union all
					 select obratid, obratdatatimezauctovane, kodaktiva
					 from majetokarchiv
					 where	obratid > 0
							and uctovnykod in (251110,251200,251300)
							and obratdatatimezauctovane >= to_date('$fromdate','YYYY-MM-DD')
							and obratdatatimezauctovane <= to_date('$todate','YYYY-MM-DD')
							and destinacia not like 'muprevod'
					) maj,
					partner p
			where	kcp.dealid = rcp.dealid
					and $fondid_add
					and e.isin = kcp.isin
					and e.eqid = eid.eqid
					and rcpo.dealid=rcp.dealid
					and rcpo.tranza=rcp.tranza
					and maj.obratid=rcpo.obratid
					and kcp.partnerid = p.partnerid
					and edruh.druheqid = e.druheqid
						
			union all
				
			select	CASE
    WHEN kcp.dealid = 46663904 THEN 'Predčasné splatenie dlhopisu na výzvu emitenta'
                    ELSE
                        CASE kcp.druhobchodu
                            WHEN 'nakup' THEN 'Nákup'
                            WHEN 'predaj' THEN 'Predaj'
                            WHEN 'vklad' THEN 'Vklad'
                            WHEN 'vyber' THEN 'Výber'
                            WHEN 'vklad-vy' THEN 'Výmena CP - pripísanie CP'
                            WHEN 'vyber-vy' THEN 'Výmena CP - odpísanie CP'
                        END
                END AS popis,
                rcp.datvysporiadaniamureal,
                e.isin,
                e.isinreal,
                e.eqid,
                edruh.poddruheq AS poddruheq,
                e.cpnaz,
                e.cpnazskratka,
                pdr.ksreal AS pocet,
                kcp.currencyidtrade,
                rcp.kurzreal AS kurz,
                pdr.auvreal AS auv,
                pdr.transsumareal AS spolu,
                ROUND((rcp.kurzreal * pdr.ksreal * coalesce(e.nominalemisie, 1) * $pomerNominalu /
                    (CASE WHEN kcp.eqid='Bonds' THEN 100 ELSE 1 END)), 2) AS bezAUV,
                maj.obratdatatimezauctovane AS datum,
                currencyidtrade AS mena,
                CASE
                    WHEN kcp.dealid = 46663904 THEN '--'
                    ELSE p.nazovpartnera
                END AS miesto,
                kcp.datum_cas_obchodu AS datum_cas,
                CASE
                    WHEN kcp.dealid = 46663904 THEN '--'
                    ELSE (SELECT typ_pokynu FROM typy_pokynov tp WHERE tp.typid = kcp.typ_pokynu)
                END AS typPokynu,
                CASE kcp.druhobchodu
                    WHEN 'nakup' THEN 0
                    WHEN 'predaj' THEN 1
                    WHEN 'vklad' THEN 0
                    WHEN 'vyber' THEN 1
                END AS MD_D
			from	konfirmaciacp kcp,
					rekonfirmaciacp rcp,
					rekonfirmaciacpobratid rcpo,
					dbequity e,
					equityid eid,
					equitydruh edruh,
					(select obratid, obratdatatimezauctovane, subjektid, kodaktiva
					 from 	majetoktoday
					 where	obratid > 0
							and uctovnykod in (251110,251200,251300)
							and obratdatatimezauctovane >= to_date('$fromdate','YYYY-MM-DD')
							and obratdatatimezauctovane <= to_date('$todate','YYYY-MM-DD')
							and destinacia not like 'muprevod'
					 union all
					 select obratid, obratdatatimezauctovane, subjektid, kodaktiva
					 from majetokarchiv
					 where	obratid > 0
							and uctovnykod in (251110,251200,251300)
							and obratdatatimezauctovane >= to_date('$fromdate','YYYY-MM-DD')
							and obratdatatimezauctovane <= to_date('$todate','YYYY-MM-DD')
							and destinacia not like 'muprevod'
					) maj,
					pool po,
					pooldetailreal pdr,
					partner p
			where	kcp.dealid = rcp.dealid
					and kcp.subjektid =0
					and e.eqid = eid.eqid
					and pdr.poolid = po.poolid 
					and pdr.subjektid = maj.subjektid
					and pdr.$fondid_add
					and kcp.dealid = po.dealid
					and rcp.tranza = pdr.tranza
					and e.isin = kcp.isin
					and rcpo.dealid=rcp.dealid
					and rcpo.tranza=rcp.tranza
					and maj.obratid=rcpo.obratid
					and kcp.partnerid = p.partnerid
					and edruh.druheqid = e.druheqid
													
			union all						
			
			select	'Splatenie' as popis,
					s.datumvyplaty as datvysporiadaniamureal,
					e.isin,
					e.isinreal,
					e.eqid,
					edruh.poddruheq as poddruheq,
					e.cpnaz,e.cpnazskratka,
					s.pocet,
					s.mena as currencyidtrade,
					/*100 as kurz,*/
					s.suma*100/(e.nominalemisie*s.pocet) as kurz, 
					0 as auv,
					s.suma as spolu,
					s.suma as bezAUV,
					s.datum as datum,
					s.mena as mena,
					null as miesto,
					null as datum_cas,
					null as typPokynu,
					1 as MD_D
			from
					splatenie s,
					dbequity e,
					dbequitycurr c,
					dbequitycurrric r,
					equitydruh edruh
			where
					s.kodaktiva = r.isincurrric
					and r.isincurr = c.isincurr
					and c.isin = e.isin
					and e.druheqid = edruh.druheqid
					and s.uctovnykod = 251110
					and s.datum >= to_date('$fromdate','YYYY-MM-DD')
					and s.datum <= to_date('$todate','YYYY-MM-DD')
					and s.$fondid_add
					and s.datum_naroku = (select max(datesplatnost) from floatkupon where s.kodaktiva = isincurrric and istina >= 100)
					
			union all

			select	'Čiastočné splatenie priebežné' as popis,
					s.datumvyplaty as datvysporiadaniamureal,
					e.isin,
					e.isinreal,
					e.eqid,
					edruh.poddruheq as poddruheq,
					e.cpnaz,e.cpnazskratka,
					s.pocet,
					s.mena as currencyidtrade,
					100 as kurz,
					0 as auv,
					s.suma as spolu,
					s.suma as bezAUV,
					s.datum as datum,
					s.mena as mena,
					null as miesto,
					null as datum_cas,
					null as typPokynu,
					1 as MD_D
			from
					splatenie s,
					dbequity e,
					dbequitycurr c,
					dbequitycurrric r,
					equitydruh edruh
			where
					s.kodaktiva = r.isincurrric
					and r.isincurr = c.isincurr
					and c.isin = e.isin
					and e.druheqid = edruh.druheqid
					and s.uctovnykod = 251110
					and s.datum >= to_date('$fromdate','YYYY-MM-DD')
					and s.datum <= to_date('$todate','YYYY-MM-DD')
					and s.$fondid_add
					and s.datum_naroku < (select max(datesplatnost) from floatkupon where s.kodaktiva = isincurrric and istina < 100)
					
			union all

			select	'Čiastočné splatenie konečné' as popis,
					s.datumvyplaty as datvysporiadaniamureal,
					e.isin,
					e.isinreal,
					e.eqid,
					edruh.poddruheq as poddruheq,
					e.cpnaz,e.cpnazskratka,
					s.pocet,
					s.mena as currencyidtrade,
					100 as kurz,
					0 as auv,
					s.suma as spolu,
					s.suma as bezAUV,
					s.datum as datum,
					s.mena as mena,
					null as miesto,
					null as datum_cas,
					null as typPokynu,
					1 as MD_D
			from
					splatenie s,
					dbequity e,
					dbequitycurr c,
					dbequitycurrric r,
					equitydruh edruh
			where
					s.kodaktiva = r.isincurrric
					and r.isincurr = c.isincurr
					and c.isin = e.isin
					and e.druheqid = edruh.druheqid
					and s.uctovnykod = 251110
					and s.datum >= to_date('$fromdate','YYYY-MM-DD')
					and s.datum <= to_date('$todate','YYYY-MM-DD')
					and s.$fondid_add
					and s.datum_naroku = (select max(datesplatnost) from floatkupon where s.kodaktiva = isincurrric and istina < 100)
					
			union all

			select	da.nazov  as popis,
					s.datumvyplaty as datvysporiadaniamureal,
					e.isin,
					e.isinreal,
					e.eqid,
					edruh.poddruheq as poddruheq,
					e.cpnaz,e.cpnazskratka,
					s.pocet,
					s.mena as currencyidtrade,
					null as kurz,
					null as auv,
					null as spolu,
					s.pocet as bezAUV,
					s.datumvyplaty as datum,
					s.mena as mena,
					null as miesto,
					null as datum_cas,
					null as typPokynu,
					da.MD_D  as MD_D
			from
					splatenieakcia s,
					dividendaakciatyp da,
					dbequity e,
					dbequitycurr c,
					dbequitycurrric r,
					equitydruh edruh
			where
					s.kodaktiva = r.isincurrric
					and r.isincurr = c.isincurr
					and c.isin = e.isin
					and e.druheqid = edruh.druheqid
					and s.datumvyplaty >= to_date('$fromdate','YYYY-MM-DD')
					and s.datumvyplaty <= to_date('$todate','YYYY-MM-DD')
					and s.$fondid_add
					and s.subkodobratu in (select subkodobratu from dividendaakciatyp where hotovost = 0)
					and da.subkodobratu = s.subkodobratu
		) as kr
		order by datum";

$pohyby = Connection::getDataFromDatabase($query, defaultDB);
$pohybyCount = $pohyby[0];
$pohyby = $pohyby[1][0];

for ($i = 0; $i < $pohybyCount; $i++) {
    $nazov = $pohyby['nazov'];
    $isin = $pohyby['isin'];
    $isinreal = $pohyby['isinreal'];
    $pocet = $pohyby['pocet'];
    $mena = $pohyby['mena'];
    $kurz = $pohyby['kurz'];
    $auv = $pohyby['auv'];
    $suma = $pohyby['spolu'];
    $bezAUV = $pohyby['bezAUV'];
    $popis = $pohyby['popis'];
    $typ = $pohyby['eqid'];
    $druh = $pohyby['poddruheq'];
    $datum = $pohyby['datum'];
    $datum = substr($datum, 8, 2) . '.' . substr($datum, 5, 2) . '.' . substr($datum, 0, 4);
    $datum_cas = $pohyby['datum_cas'];
    $miesto = $pohyby['miesto'];
    $typPokynu = $pohyby['typPokynu'];
    $MD_D = $pohyby['MD_D'];

    if ($i == 1) {
        $vypisyspolu++;
        $vypis_obsah = $vypis_obsah . '					
			<br><br><br>
			<table style="width:100%;border-collapse:collapse;">				
				<tr>
					<td>
						<h3 style="color:' . $farbaciara2 . '">Pohyby na majetkovom účte</h3>
					</td>
				</tr>
			</table>
			<br><br>
			<table style="width:100%;border-collapse:collapse;border-top:1pt solid ' . $farbaciara3 . ';border-bottom:1pt solid ' . $farbaciara3 . ';">
				<thead border-collapse:collapse;>
					<tr>				
						<th style="width:11%;text-align:left;font-size:8;"><b>Uzavretie obchodu</b></th>
						<th style="width:11%;text-align:left;font-size:8;"><b>Transakcia</b></th>
						<th style="width:10%;text-align:left;font-size:8;"><b>Názov CP</b></th>
						<th style="width:12%;text-align:left;font-size:8;"><b>ISIN</b></th>
						<th style="width:10%;text-align:right;font-size:8;"><b>Počet CP</b></th>
						<th style="width:6%;font-size:8;"><b>Mena</b></th>
						<th style="width:8%;text-align:right;font-size:8;"><b>Cena CP</b></th>
						<th style="width:11%;text-align:right;font-size:8;"><b>Cena bez AUV</b></th>
						<th style="width:10%;text-align:rightfont-size:8;"><b>AUV</b></th>
						<th style="width:11%;text-align:right;font-size:8;"><b>Cena spolu</b></th>
					</tr>
					<tr>
						<th style="width:11%;text-align:left;font-size:8;"><b>Vysporiadanie</b></th>
						<th style="width:11%;text-align:left;font-size:8;"><b>Typ pokynu</b></th>		
						<th style="width:10%;text-align:left;font-size:8;"><b>Miesto výkonu</b></th>
						<th colspan="3" style="width:12%;text-align:left;font-size:8;"><b>Druh CP</b></th>
						<th colspan="2" ></th>
						<th colspan="2" ></th>
					</tr>
				</thead>
				<tbody>
					<tr style="width:100%;line-height:1pt;">
						<td colspan="10" style="border-bottom:0.1mm solid ' . $farbaciara1 . ';height:1pt;min-width:100%;width:100%;"></td>
					</tr>
					<tr style="width:100%;line-height:1pt;">
						<td colspan="10" style="height:1pt;min-width:100%;width:100%;"></td>
					</tr>
		';
    }

    if ($i > 1) {
        $vypis_obsah = $vypis_obsah . '	
			<tr style="width:100%;line-height:1pt;">
				<td colspan="10" style="border-bottom:0.1mm dashed ' . $farbaciara1 . ';height:1pt;min-width:100%;width:100%;"></td>
			</tr>
			<tr style="width:100%;line-height:1pt;">
				<td colspan="10" style="height:1pt;min-width:100%;width:100%;"></td>
			</tr>
		';
    }

    $vypis_obsah = $vypis_obsah . '		
		<tr style="page-break-after:avoid;">
			<td style="width:11%;font-size:8;">' . $datum_cas . '</td>
			<td style="width:11%;font-size:8;">' . $popis . '</td>
			<td style="width:10%;font-size:8;">' . $nazov . '</td>
			<td style="width:12%;font-size:8;">' . $isinreal . '</td>
			<td style="width:10%;text-align:right;font-size:8;">' . ($pohyby['MD_D'] == 0 ? '' : '-') . bcdiv($pocet, 1, 0) . '</td>
			<td style="width:6%;text-align:center;font-size:8;">' . $mena . '</td>
			<td style="width:8%;text-align:right;font-size:8;">' . ($pohyby['kurz'] == null ? '' : bcdiv($kurz, 1, 4)) . '</td>
			<td style="width:11%;text-align:right;font-size:8;">' . ($typ == 'Bonds' && $popis != 'Splatenie' ? bcdiv($bezAUV, 1, 2) : "") . '</td>
			<td style="width:10%;text-align:right;font-size:8;">' . ($typ == 'Bonds' && $popis != 'Splatenie' ? bcdiv($auv, 1, 2) : "") . '</td>
			<td style="width:11%;text-align:right;font-size:8;">' . ($pohyby['spolu'] == null ? '' : bcdiv($suma, 1, 2)) . '</td>
		</tr>
		<tr>
			<td style="width:11%;font-size:8;">' . $datum . '</td>
			<td style="width:11%;font-size:8;">' . $typPokynu . '</td>		
			<td style="width:10%;font-size:8;">' . $miesto . '</td>
			<td colspan=3 style="width:11%;font-size:8;">' . $druh . '</td>
			<td style="font-size:8;"></td>
			<td style="font-size:8;"></td>
			<td style="font-size:8;"></td>
			<td style="font-size:8;"></td>
		</tr>
	';
}

$vypis_obsah = $vypis_obsah . '	
				<tr style="width:100%;line-height:1pt;">
					<td colspan="10" style="height:1pt;min-width:100%;width:100%;"></td>
				</tr>	
			</tbody>
		</table>		
	';


if ($vypisyspolu == 0) {
    $vypis_obsah = $vypis_obsah . '		
		<br><br>
		<table style="width:100%;border-collapse:collapse;">	
			<tr>
				<td colspan="4"><h4>Pre zvolené obdobie neprebehli žiadne transakcie</h4></tD>
			</tr>
		</table>
	';
}

$vypis_transakcii = $vypis_obsah;
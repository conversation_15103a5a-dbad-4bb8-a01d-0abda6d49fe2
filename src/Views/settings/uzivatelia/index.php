<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
require_once "/home/<USER>/www/src/Components/tables/settings/constantTable.php";
require_once "/home/<USER>/www/src/Components/createModals/generalCreateModal.php";

$forcedLogout = Connection::getDataFromDatabase("SELECT forcelogout FROM users WHERE forcelogout = TRUE", defaultDB);
print_r($forcedLogout[0]);
//CONFIG
$pageHeading = "Správa užívateľov";
$constantsTable = "users";
$modalHeading = "užívateľa";
$formID = "users";
$modalEditID = "userid";
$toSelect = "t.*";
$joinClause = "";
$rows = ["userid", "username", "logged", "supervisor", "sales", "usernick"];
$userGroupsRes = Connection::getDataFromDatabase("select *
		from  
			usergroup
		order by usergroupid", defaultDB);
$userGroups = $userGroupsRes[1];
$inputs = [
    ["name" => "username", "type" => "text", "label" => "Meno užívateľa"],
    ["name" => "agentid", "type" => "text", "label" => "Identifikačné číslo agenta"],
    ["name" => "supervisor", "type" => "select", "label" => "Supervisor", "options" => [["popis" => "Nie", "value" => "0"], ["popis" => "Áno", "value" => "1"]], "optionField" => "popis", "optionValue" => "value"],
    ["name" => "usernick", "type" => "text", "label" => "Prihlasovacie meno"],
    ["name" => "telefon", "type" => "text", "label" => "Telefónne číslo"],
    ["name" => "email", "type" => "text", "label" => "Emailová adresa"],
    ["name" => "sales_podielnikid", "type" => "select", "label" => "Je podielnikom", "options" => [["popis" => "Nie", "value" => "0"], ["popis" => "Áno", "value" => "1"]], "optionField" => "popis", "optionValue" => "value"],
    ["name" => "povolenia", "type" => "multiselect", "label" => "", "options" => $userGroups, "optionField" => "nazov", "optionValue" => "usergroupid"],
    ["name" => "userid", "type" => "hidden", "label" => "userid"],
];
$createSequence = "users_userid_seq";
$constantsOrderBy = "userid";
$columns = ["Číslo agenta", "Meno užívateľa", "Momentálny stav", "SuperVisor", "Username", "Sales", "Akcia"];

$query = "SELECT COUNT(*) FROM " . $constantsTable;
$dataRes = Connection::getDataFromDatabase($query, defaultDB);
$dataSize = $dataRes[1][0]["count"];


$total_pages = $dataSize;
$page = isset($_GET['page']) && is_numeric($_GET['page']) ? $_GET['page'] : 1;
$num_results_on_page = 60;

$calc_page = ($page - 1) * $num_results_on_page;
$query = "SELECT " . $toSelect . " FROM " . $constantsTable . " t " . $joinClause . " ORDER BY " . $constantsOrderBy . " OFFSET $calc_page LIMIT $num_results_on_page";
$dataRes = Connection::getDataFromDatabase($query, defaultDB);
$data = $dataRes[1];
?>
<section>
    <nav class="flex" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-2 rtl:space-x-reverse">
            <li class="inline-flex items-center">
                <a href="/nastavenia"
                    class="inline-flex items-center gap-2 text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                    <svg class="w-3 h-3 text-gray-500 dark:text-white" aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor"
                        viewBox="0 0 24 24">
                        <path fill-rule="evenodd"
                            d="M9.586 2.586A2 2 0 0 1 11 2h2a2 2 0 0 1 2 2v.089l.473.196.063-.063a2.002 2.002 0 0 1 2.828 0l1.414 1.414a2 2 0 0 1 0 2.827l-.063.064.196.473H20a2 2 0 0 1 2 2v2a2 2 0 0 1-2 2h-.089l-.196.473.063.063a2.002 2.002 0 0 1 0 2.828l-1.414 1.414a2 2 0 0 1-2.828 0l-.063-.063-.473.196V20a2 2 0 0 1-2 2h-2a2 2 0 0 1-2-2v-.089l-.473-.196-.063.063a2.002 2.002 0 0 1-2.828 0l-1.414-1.414a2 2 0 0 1 0-2.827l.063-.064L4.089 15H4a2 2 0 0 1-2-2v-2a2 2 0 0 1 2-2h.09l.195-.473-.063-.063a2 2 0 0 1 0-2.828l1.414-1.414a2 2 0 0 1 2.827 0l.064.063L9 4.089V4a2 2 0 0 1 .586-1.414ZM8 12a4 4 0 1 1 8 0 4 4 0 0 1-8 0Z"
                            clip-rule="evenodd" />
                    </svg>
                    Nastavenia
                </a>
            </li>
            <li>
                <div
                    class="inline-flex items-center gap-2 text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                    <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="m1 9 4-4-4-4" />
                    </svg>
                    <a href="/nastavenia/uzivatelia" class="hover:underline">Užívateľia</a>
                </div>
            </li>
        </ol>
    </nav>
    <div class="flex w-full justify-between items-center">
        <h2 class="text-xl mt-4 font-bold mb-8"><?php echo $pageHeading ?></h2>
        <div class="flex items-center gap-3">
            <?php if (in_array(1, $_SESSION["user"]["permissions"])) { ?>
                <button id="constantAddButton" type="button" data-modal-target="createModal" data-modal-toggle="createModal"
                    class="text-white gap-2 bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm py-2 px-4 text-center inline-flex items-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                    Pridať
                    <svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                        fill="none" viewBox="0 0 24 24">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 7.757v8.486M7.757 12h8.486M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                    </svg>
                </button>
                <button id="logoutAll" data-tooltip-target="tooltip-logout-all" type="button"
                    class="text-white bg-red-700 hover:bg-red-800 focus:ring-4 focus:outline-none focus:ring-red-300 font-medium rounded-lg text-sm px-3 py-2 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                    <svg id="logoutAllIcon" class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24"
                        height="24" fill="currentColor" viewBox="0 0 24 24">
                        <path fill-rule="evenodd"
                            d="M5 8a4 4 0 1 1 8 0 4 4 0 0 1-8 0Zm-2 9a4 4 0 0 1 4-4h4a4 4 0 0 1 4 4v1a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-1Zm13-6a1 1 0 1 0 0 2h4a1 1 0 1 0 0-2h-4Z"
                            clip-rule="evenodd" />
                    </svg>
                    <svg id="logoutAllSpinner" aria-hidden="true"
                        class="w-5 h-5 hidden text-gray-200 animate-spin dark:text-gray-600 fill-blue-600"
                        viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                            fill="currentColor" />
                        <path
                            d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                            fill="currentFill" />
                    </svg>
                </button>
                <div id="tooltip-logout-all" role="tooltip"
                    class="absolute z-10 invisible inline-block px-3 py-2 text-sm font-medium text-white transition-opacity duration-300 bg-gray-900 rounded-lg shadow-sm opacity-0 tooltip dark:bg-gray-700">
                    Odhlásiť všetkých používateľov
                    <div class="tooltip-arrow" data-popper-arrow></div>
                </div>
                <form class="killAllForm mb-0" method="POST" action="">
                    <input type="hidden" name="userid" value="<?php echo $_SESSION["user"]["data"]["userid"] ?>" />
                    <input id="isForcedLogout" type="hidden" name="isForcedLogout"
                        value="<?php echo ($forcedLogout[0] > 0 ? "true" : "false"); ?>" />
                    <button data-tooltip-target="tooltip-block-all" type="submit"
                        class="killAllButton text-white bg-gray-900 hover:bg-red-900 focus:ring-4 focus:outline-none focus:ring-red-300 font-medium rounded-lg text-sm px-3 py-2 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                        <svg id="killAllIcon" class="w-5 h-5" style="<?php echo ($forcedLogout[0] === 0 ? "" : "display: none;"); ?>"
                            aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor"
                            viewBox="0 0 24 24">
                            <path fill-rule="evenodd"
                                d="M8 10V7a4 4 0 1 1 8 0v3h1a2 2 0 0 1 2 2v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7a2 2 0 0 1 2-2h1Zm2-3a2 2 0 1 1 4 0v3h-4V7Zm2 6a1 1 0 0 1 1 1v3a1 1 0 1 1-2 0v-3a1 1 0 0 1 1-1Z"
                                clip-rule="evenodd" />
                        </svg>
                        <svg id="unlockAllIcon" class="w-5 h-5" style="<?php echo ($forcedLogout[0] > 0 ? "" : "display: none;"); ?>"
                            aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor"
                            viewBox="0 0 24 24">
                            <path fill-rule="evenodd"
                                d="M15 7a2 2 0 1 1 4 0v4a1 1 0 1 0 2 0V7a4 4 0 0 0-8 0v3H5a2 2 0 0 0-2 2v7a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2v-7a2 2 0 0 0-2-2V7Zm-5 6a1 1 0 0 1 1 1v3a1 1 0 1 1-2 0v-3a1 1 0 0 1 1-1Z"
                                clip-rule="evenodd" />
                        </svg>
                        <svg id="killAllSpinner" aria-hidden="true"
                            class="w-5 h-5 hidden text-gray-200 animate-spin dark:text-gray-600 fill-blue-600"
                            viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                                fill="currentColor" />
                            <path
                                d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                                fill="currentFill" />
                        </svg>
                    </button>
                    <div id="tooltip-block-all" role="tooltip"
                        class="absolute z-10 invisible inline-block px-3 py-2 text-sm font-medium text-white transition-opacity duration-300 bg-gray-900 rounded-lg shadow-sm opacity-0 tooltip dark:bg-gray-700">
                        <?php echo ($forcedLogout[0] > 0 ? "Odblokovať prihlasovanie" : "Zablokovať prihlasovanie"); ?>
                        <div class="tooltip-arrow" data-popper-arrow></div>
                    </div>
                </form>
            <?php } ?>
        </div>
    </div>
    <nav class="flex items-center shadow-md rounded-t-lg -mt-1 flex-column flex-wrap md:flex-row justify-between p-4"
        aria-label="Table navigation" style="margin-bottom: -1rem;">
        <span
            class="text-sm font-normal text-gray-500 dark:text-gray-400 mb-4 md:mb-0 block w-full md:inline md:w-auto">Počet
            výsledkov:
            <span class="font-semibold text-gray-900 dark:text-white"></span><span
                class="font-semibold text-gray-900 dark:text-white"><?php echo $dataSize; ?></span></span>
        <?php if (ceil($total_pages / $num_results_on_page) > 0): ?>
            <ul class="inline-flex -space-x-px rtl:space-x-reverse text-sm h-8">
                <?php if ($page > 1): ?>
                    <a href="?page=<?php echo $page - 1 ?>">
                        <li
                            class="flex items-center justify-center px-3 h-8 ms-0 leading-tight text-gray-500 bg-white border border-gray-300 rounded-s-lg hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                            Prev
                        </li>
                    </a>
                <?php endif; ?>

                <?php if ($page > 3): ?>
                    <a href="?page=1">
                        <li
                            class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                            1
                        </li>
                    </a>
                    <li
                        class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                        ...</li>
                <?php endif; ?>

                <?php if ($page - 2 > 0): ?>
                    <a href="?page=<?php echo $page - 2 ?>">
                        <li
                            class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                            <?php echo $page - 2 ?>
                        </li>
                    </a>
                <?php endif; ?>
                <?php if ($page - 1 > 0): ?>
                    <a href="?page=<?php echo $page - 1 ?>">
                        <li
                            class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                            <?php echo $page - 1 ?>
                        </li>
                    </a>
                <?php endif; ?>

                <a href="?page=<?php echo $page ?>">
                    <li
                        class="flex items-center justify-center px-3 h-8 text-blue-600 border border-gray-300 bg-blue-50 hover:bg-blue-100 hover:text-blue-700 dark:border-gray-700 dark:bg-gray-700 dark:text-white">
                        <?php echo $page ?>
                    </li>
                </a>

                <?php if ($page + 1 < ceil($total_pages / $num_results_on_page) + 1): ?>
                    <a href="?page=<?php echo $page + 1 ?>">
                        <li
                            class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                            <?php echo $page + 1 ?>
                        </li>
                    </a>
                <?php endif; ?>
                <?php if ($page + 2 < ceil($total_pages / $num_results_on_page) + 1): ?>
                    <a href="?page=<?php echo $page + 2 ?>">
                        <li
                            class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                            <?php echo $page + 2 ?>
                        </li>
                    </a>
                <?php endif; ?>

                <?php if ($page < ceil($total_pages / $num_results_on_page) - 2): ?>
                    <li
                        class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                        ...</li>
                    <a href="?page=<?php echo ceil($total_pages / $num_results_on_page) ?>">
                        <li
                            class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                            <?php echo ceil($total_pages / $num_results_on_page) ?>
                        </li>
                    </a>
                <?php endif; ?>

                <?php if ($page < ceil($total_pages / $num_results_on_page)): ?>
                    <a href="?page=<?php echo $page + 1 ?>">
                        <li
                            class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 rounded-e-lg hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                            Next
                        </li>
                    </a>
                <?php endif; ?>
            </ul>
        <?php endif; ?>
    </nav>
    <?php
    generalCreateModal::render($inputs, $constantsTable, $modalHeading, $createSequence);
    constantTable::render($data, $columns, $inputs, $constantsTable, $modalEditID, $modalHeading, $rows);
    ?>
    <nav class="flex items-center mb-10 shadow-lg rounded-b-lg -mt-1 flex-column flex-wrap md:flex-row justify-between p-4"
        aria-label="Table navigation">
        <span
            class="text-sm font-normal text-gray-500 dark:text-gray-400 mb-4 md:mb-0 block w-full md:inline md:w-auto">Počet
            výsledkov:
            <span class="font-semibold text-gray-900 dark:text-white"></span><span
                class="font-semibold text-gray-900 dark:text-white"><?php echo $dataSize; ?></span></span>
        <?php if (ceil($total_pages / $num_results_on_page) > 0): ?>
            <ul class="inline-flex -space-x-px rtl:space-x-reverse text-sm h-8">
                <?php if ($page > 1): ?>
                    <a href="?page=<?php echo $page - 1 ?>">
                        <li
                            class="flex items-center justify-center px-3 h-8 ms-0 leading-tight text-gray-500 bg-white border border-gray-300 rounded-s-lg hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                            Prev
                        </li>
                    </a>
                <?php endif; ?>

                <?php if ($page > 3): ?>
                    <a href="?page=1">
                        <li
                            class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                            1
                        </li>
                    </a>
                    <li
                        class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                        ...</li>
                <?php endif; ?>

                <?php if ($page - 2 > 0): ?>
                    <a href="?page=<?php echo $page - 2 ?>">
                        <li
                            class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                            <?php echo $page - 2 ?>
                        </li>
                    </a>
                <?php endif; ?>
                <?php if ($page - 1 > 0): ?>
                    <a href="?page=<?php echo $page - 1 ?>">
                        <li
                            class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                            <?php echo $page - 1 ?>
                        </li>
                    </a>
                <?php endif; ?>

                <a href="?page=<?php echo $page ?>">
                    <li
                        class="flex items-center justify-center px-3 h-8 text-blue-600 border border-gray-300 bg-blue-50 hover:bg-blue-100 hover:text-blue-700 dark:border-gray-700 dark:bg-gray-700 dark:text-white">
                        <?php echo $page ?>
                    </li>
                </a>

                <?php if ($page + 1 < ceil($total_pages / $num_results_on_page) + 1): ?>
                    <a href="?page=<?php echo $page + 1 ?>">
                        <li
                            class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                            <?php echo $page + 1 ?>
                        </li>
                    </a>
                <?php endif; ?>
                <?php if ($page + 2 < ceil($total_pages / $num_results_on_page) + 1): ?>
                    <a href="?page=<?php echo $page + 2 ?>">
                        <li
                            class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                            <?php echo $page + 2 ?>
                        </li>
                    </a>
                <?php endif; ?>

                <?php if ($page < ceil($total_pages / $num_results_on_page) - 2): ?>
                    <li
                        class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                        ...</li>
                    <a href="?page=<?php echo ceil($total_pages / $num_results_on_page) ?>">
                        <li
                            class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                            <?php echo ceil($total_pages / $num_results_on_page) ?>
                        </li>
                    </a>
                <?php endif; ?>

                <?php if ($page < ceil($total_pages / $num_results_on_page)): ?>
                    <a href="?page=<?php echo $page + 1 ?>">
                        <li
                            class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 rounded-e-lg hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                            Next
                        </li>
                    </a>
                <?php endif; ?>
            </ul>
        <?php endif; ?>
    </nav>
</section>
<script src="/src/assets/js/settings/updates.js"
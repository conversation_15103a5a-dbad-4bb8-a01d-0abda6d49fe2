<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
require_once "/home/<USER>/www/src/Components/tables/settings/constantTable.php";
require_once "/home/<USER>/www/src/Components/createModals/generalCreateModal.php";
include "config.php";

$query = "SELECT COUNT(*) FROM " . $constantsTable;
$dataRes = Connection::getDataFromDatabase($query, defaultDB);
$dataSize = $dataRes[1][0]["count"];


$total_pages = $dataSize;
$page = isset($_GET['page']) && is_numeric($_GET['page']) ? $_GET['page'] : 1;
$num_results_on_page = 50;

$calc_page = ($page - 1) * $num_results_on_page;
$query = "SELECT " . $toSelect . " FROM " . $constantsTable . " t " . $joinClause . " ORDER BY " . $constantsOrderBy . " OFFSET $calc_page LIMIT $num_results_on_page";
$dataRes = Connection::getDataFromDatabase($query, defaultDB);
$data = $dataRes[1];
?>
<section>
    <nav class="flex" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-2 rtl:space-x-reverse">
            <li class="inline-flex items-center">
                <a href="/nastavenia"
                    class="inline-flex items-center gap-2 text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                    <svg class="w-3 h-3 text-gray-500 dark:text-white" aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor"
                        viewBox="0 0 24 24">
                        <path fill-rule="evenodd"
                            d="M9.586 2.586A2 2 0 0 1 11 2h2a2 2 0 0 1 2 2v.089l.473.196.063-.063a2.002 2.002 0 0 1 2.828 0l1.414 1.414a2 2 0 0 1 0 2.827l-.063.064.196.473H20a2 2 0 0 1 2 2v2a2 2 0 0 1-2 2h-.089l-.196.473.063.063a2.002 2.002 0 0 1 0 2.828l-1.414 1.414a2 2 0 0 1-2.828 0l-.063-.063-.473.196V20a2 2 0 0 1-2 2h-2a2 2 0 0 1-2-2v-.089l-.473-.196-.063.063a2.002 2.002 0 0 1-2.828 0l-1.414-1.414a2 2 0 0 1 0-2.827l.063-.064L4.089 15H4a2 2 0 0 1-2-2v-2a2 2 0 0 1 2-2h.09l.195-.473-.063-.063a2 2 0 0 1 0-2.828l1.414-1.414a2 2 0 0 1 2.827 0l.064.063L9 4.089V4a2 2 0 0 1 .586-1.414ZM8 12a4 4 0 1 1 8 0 4 4 0 0 1-8 0Z"
                            clip-rule="evenodd" />
                    </svg>
                    Nastavenia
                </a>
            </li>
            <li>
                <div
                    class="inline-flex items-center gap-2 text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                    <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="m1 9 4-4-4-4" />
                    </svg>
                    <a href="/nastavenia/konstanty" class="hover:underline">Konštanty</a>
                </div>
            </li>
            <li>
                <div class="flex items-center">
                    <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="m1 9 4-4-4-4" />
                    </svg>
                    <span
                        class="ms-1 text-sm text-blue-500 font-bold md:ms-2 dark:text-gray-400"><?php echo $pageHeading ?></span>
                </div>
            </li>
        </ol>
    </nav>
    <div class="flex w-full justify-between items-center">
        <h2 class="text-xl mt-4 font-bold mb-8"><?php echo $pageHeading ?></h2>
        <button id="constantAddButton" type="button" data-modal-target="createModal" data-modal-toggle="createModal"
            class="text-white gap-2 bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm py-2 px-4 text-center inline-flex items-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
            Pridať
            <svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                fill="none" viewBox="0 0 24 24">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M12 7.757v8.486M7.757 12h8.486M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
            </svg>
        </button>
    </div>
    <nav class="flex items-center shadow-md rounded-t-lg -mt-1 flex-column flex-wrap md:flex-row justify-between p-4" aria-label="Table navigation" style="margin-bottom: -1rem;">
        <span
            class="text-sm font-normal text-gray-500 dark:text-gray-400 mb-4 md:mb-0 block w-full md:inline md:w-auto">Počet výsledkov:
            <span class="font-semibold text-gray-900 dark:text-white"></span><span
                class="font-semibold text-gray-900 dark:text-white"><?php echo $dataSize; ?></span></span>
        <?php if (ceil($total_pages / $num_results_on_page) > 0): ?>
            <ul class="inline-flex -space-x-px rtl:space-x-reverse text-sm h-8">
                <?php if ($page > 1): ?>
                    <a href="?page=<?php echo $page - 1 ?>"><li
                        class="flex items-center justify-center px-3 h-8 ms-0 leading-tight text-gray-500 bg-white border border-gray-300 rounded-s-lg hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                        Prev
                    </li></a>
                <?php endif; ?>

                <?php if ($page > 3): ?>
                    <a href="?page=1"><li
                        class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                        1
                    </li></a>
                    <li
                        class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                        ...</li>
                <?php endif; ?>

                <?php if ($page - 2 > 0): ?>
                    <a href="?page=<?php echo $page - 2 ?>"><li
                        class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                        <?php echo $page - 2 ?>
                    </li></a>
                <?php endif; ?>
                <?php if ($page - 1 > 0): ?>
                    <a href="?page=<?php echo $page - 1 ?>"><li
                        class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                        <?php echo $page - 1 ?>
                    </li></a>
                <?php endif; ?>

                <a href="?page=<?php echo $page ?>"><li
                    class="flex items-center justify-center px-3 h-8 text-blue-600 border border-gray-300 bg-blue-50 hover:bg-blue-100 hover:text-blue-700 dark:border-gray-700 dark:bg-gray-700 dark:text-white">
                    <?php echo $page ?>
                </li></a>

                <?php if ($page + 1 < ceil($total_pages / $num_results_on_page) + 1): ?>
                    <a href="?page=<?php echo $page + 1 ?>"><li
                        class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                        <?php echo $page + 1 ?>
                    </li></a>
                <?php endif; ?>
                <?php if ($page + 2 < ceil($total_pages / $num_results_on_page) + 1): ?>
                    <a href="?page=<?php echo $page + 2 ?>"><li
                        class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                        <?php echo $page + 2 ?>
                    </li></a>
                <?php endif; ?>

                <?php if ($page < ceil($total_pages / $num_results_on_page) - 2): ?>
                    <li
                        class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                        ...</li>
                        <a
                        href="?page=<?php echo ceil($total_pages / $num_results_on_page) ?>"><li
                        class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                        <?php echo ceil($total_pages / $num_results_on_page) ?>
                    </li></a>
                <?php endif; ?>

                <?php if ($page < ceil($total_pages / $num_results_on_page)): ?>
                    <a href="?page=<?php echo $page + 1 ?>"><li
                        class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 rounded-e-lg hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                        Next
                    </li></a>
                <?php endif; ?>
            </ul>
        <?php endif; ?>
    </nav>
    <?php
    constantTable::render($data, $columns, $inputs, $constantsTable, $modalEditID, $modalHeading, $rows);
    generalCreateModal::render($inputs, $constantsTable, $modalHeading, $createSequence);
    ?>
    <nav class="flex items-center mb-10 shadow-lg rounded-b-lg -mt-1 flex-column flex-wrap md:flex-row justify-between p-4" aria-label="Table navigation">
        <span
            class="text-sm font-normal text-gray-500 dark:text-gray-400 mb-4 md:mb-0 block w-full md:inline md:w-auto">Počet výsledkov:
            <span class="font-semibold text-gray-900 dark:text-white"></span><span
                class="font-semibold text-gray-900 dark:text-white"><?php echo $dataSize; ?></span></span>
        <?php if (ceil($total_pages / $num_results_on_page) > 0): ?>
            <ul class="inline-flex -space-x-px rtl:space-x-reverse text-sm h-8">
                <?php if ($page > 1): ?>
                    <a href="?page=<?php echo $page - 1 ?>"><li
                        class="flex items-center justify-center px-3 h-8 ms-0 leading-tight text-gray-500 bg-white border border-gray-300 rounded-s-lg hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                        Prev
                    </li></a>
                <?php endif; ?>

                <?php if ($page > 3): ?>
                    <a href="?page=1"><li
                        class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                        1
                    </li></a>
                    <li
                        class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                        ...</li>
                <?php endif; ?>

                <?php if ($page - 2 > 0): ?>
                    <a href="?page=<?php echo $page - 2 ?>"><li
                        class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                        <?php echo $page - 2 ?>
                    </li></a>
                <?php endif; ?>
                <?php if ($page - 1 > 0): ?>
                    <a href="?page=<?php echo $page - 1 ?>"><li
                        class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                        <?php echo $page - 1 ?>
                    </li></a>
                <?php endif; ?>

                <a href="?page=<?php echo $page ?>"><li
                    class="flex items-center justify-center px-3 h-8 text-blue-600 border border-gray-300 bg-blue-50 hover:bg-blue-100 hover:text-blue-700 dark:border-gray-700 dark:bg-gray-700 dark:text-white">
                    <?php echo $page ?>
                </li></a>

                <?php if ($page + 1 < ceil($total_pages / $num_results_on_page) + 1): ?>
                    <a href="?page=<?php echo $page + 1 ?>"><li
                        class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                        <?php echo $page + 1 ?>
                    </li></a>
                <?php endif; ?>
                <?php if ($page + 2 < ceil($total_pages / $num_results_on_page) + 1): ?>
                    <a href="?page=<?php echo $page + 2 ?>"><li
                        class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                        <?php echo $page + 2 ?>
                    </li></a>
                <?php endif; ?>

                <?php if ($page < ceil($total_pages / $num_results_on_page) - 2): ?>
                    <li
                        class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                        ...</li>
                        <a
                        href="?page=<?php echo ceil($total_pages / $num_results_on_page) ?>"><li
                        class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                        <?php echo ceil($total_pages / $num_results_on_page) ?>
                    </li></a>
                <?php endif; ?>

                <?php if ($page < ceil($total_pages / $num_results_on_page)): ?>
                    <a href="?page=<?php echo $page + 1 ?>"><li
                        class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 rounded-e-lg hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                        Next
                    </li></a>
                <?php endif; ?>
            </ul>
        <?php endif; ?>
    </nav>
</section>
<script src="/src/assets/js/settings/updates.js"
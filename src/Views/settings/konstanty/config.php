<?php
switch ($_SERVER["REQUEST_URI"]) {
    case str_contains($_SERVER["REQUEST_URI"], "nastavenia/konstanty/sektory-esa95"):
        $pageHeading = "Správa sektorov ESA 95";
        $constantsTable = "sektor_esa95";
        $modalHeading = "sektor";
        $formID = "sektor_esa95";
        $modalEditID = "esa95_sektorid";
        $toSelect = "t.*";
        $joinClause = "";
        $inputs = [
            ["name" => "esa95_sektorpopis", "type" => "text", "label" => "Názov sektoru"],
            ["name" => "esa95_sektoroznacenie", "type" => "text", "label" => "Označenie sektoru"],
            ["name" => "esa95_sektorid", "type" => "hidden", "label" => "esa95_sektorid"],
        ];
        $createSequence = "sektor_esa95_esa95_sektorid_seq";
        $constantsOrderBy = "esa95_sektorid";
        $columns = ["ID", "Názov sektoru", "Označnenie sektoru", "Akcia"];
        break;
    case str_contains($_SERVER["REQUEST_URI"], "konstanty/sektor"):
        $pageHeading = "Správa sektorov";
        $constantsTable = "equitysektor";
        $modalHeading = "sektor";
        $formID = "sektorid";
        $modalEditID = "sektorid";
        $toSelect = "t.*";
        $joinClause = "";
        $inputs = [
            ["name" => "sektorpopis", "type" => "text", "label" => "Názov sektoru"],
            ["name" => "sektorid", "type" => "hidden", "label" => "sektorid"],
        ];
        $createSequence = "equitysektor_sektorid_seq";
        $constantsOrderBy = "sektorid";
        $columns = ["ID", "Názov sektoru", "Akcia"];
        break;
    
    case str_contains($_SERVER["REQUEST_URI"], "konstanty/typy-pokynov"):
        $pageHeading = "Správa typov pokynov";
        $constantsTable = "typy_pokynov";
        $modalHeading = "typ pokynu";
        $formID = "typy_pokynov";
        $modalEditID = "typid";
        $toSelect = "*";
        $joinClause = "";
        $inputs = [
            ["name" => "typ_pokynu", "type" => "text", "label" => "Typ pokynu"],
            ["name" => "typid", "type" => "hidden", "label" => "typid"],
        ];
        $createSequence = "typy_pokynov_typid_seq";
        $constantsOrderBy = "typid";
        $columns = ["ID", "Typ pokynu", "Akcia"];
        break;
    case str_contains($_SERVER["REQUEST_URI"], "konstanty/atributy"):
        $pageHeading = "Správa atribútov";
        $constantsTable = "obmedzeniaassets";
        $modalHeading = "atribút";
        $formID = "obmedzeniaassets";
        $modalEditID = "assetid";
        $toSelect = "*";
        $joinClause = "";
        $rows = ["assetid", "asset", "note"];
        $inputs = [
            ["name" => "asset", "type" => "text", "label" => "Atribút"],
            ["name" => "note", "type" => "text", "label" => "Poznámka"],
            ["name" => "warningmessage", "type" => "select", "label" => "Varovná správa", "options" => [["popis" => "Nie", "value" => "0"], ["popis" => "Áno", "value" => "1"]], "optionField" => "popis", "optionValue" => "value"],
            ["name" => "attributes", "type" => "select", "label" => "Atribúty", "options" => [["popis" => "Nie", "value" => "0"], ["popis" => "Áno", "value" => "1"]], "optionField" => "popis", "optionValue" => "value"],
            ["name" => "attributespovin", "type" => "select", "label" => "Povinný", "options" => [["popis" => "Nie", "value" => "0"], ["popis" => "Áno", "value" => "1"]], "optionField" => "popis", "optionValue" => "value"],
            ["name" => "assetid", "type" => "hidden", "label" => "assetid"],
        ];
        $createSequence = "obmedzeniaassets_assetid_seq";
        $constantsOrderBy = "assetid";
        $columns = ["ID", "Atribút", "Poznámka", "Akcia"];
        break;
    case str_contains($_SERVER["REQUEST_URI"], "konstanty/doplnkove-sluzby"):
        $optionsRes = Connection::getDataFromDatabase("SELECT * FROM menadb", defaultDB);
        $options = $optionsRes[1];
        $pageHeading = "Správa doplnkových služieb";
        $constantsTable = "typds";
        $modalHeading = "doplnkovú službu";
        $formID = "typds";
        $modalEditID = "typdsid";
        $toSelect = "t.typdsid, t.nazovds, t.popisds, t.poplatokds, CASE WHEN t.kontrola = 1 THEN 'Áno' ELSE 'Nie' END as kontrola, CASE WHEN vybavuje = 1 THEN 'Centrála' ELSE 'Pobočka' END as vybavuje, t.poplatokmena";
        $joinClause = "";
        $inputs = [
            ["name" => "nazovds", "type" => "text", "label" => "Názov služby"],
            ["name" => "popisds", "type" => "text", "label" => "Popis"],
            ["name" => "poplatokds", "type" => "text", "label" => "Poplatok"],
            ["name" => "kontrola", "type" => "select", "label" => "Kontrola", "options" => [["popis" => "Nie", "value" => "0"], ["popis" => "Áno", "value" => "1"]], "optionField" => "popis", "optionValue" => "value"],
            ["name" => "vybavuje", "type" => "select", "label" => "Vybavuje", "options" => [["popis" => "Pobočka", "value" => "0"], ["popis" => "Centrála", "value" => "1"]], "optionField" => "popis", "optionValue" => "value"],
            ["name" => "poplatokmena", "type" => "select", "label" => "Mena poplatku", "options" => $options, "optionField" => "menanaz", "optionValue" => "mena"],
            ["name" => "typdsid", "type" => "hidden", "label" => "typdsid"],
        ];
        $createSequence = "typds_typdsid_seq";
        $constantsOrderBy = "typdsid";
        $columns = ["ID", "Názov služby", "Popis", "Poplatok", "Kontrola", "Vybavuje", "Mena poplatku", "Akcia"];
        break;
    case str_contains($_SERVER["REQUEST_URI"], "konstanty/druhy-aktiv"):
        $pageHeading = "Správa druhov aktív";
        $constantsTable = "equitydruh";
        $modalHeading = "druh aktíva";
        $formID = "equitydruh";
        $modalEditID = "druheqid";
        $toSelect = "t.*";
        $joinClause = "";
        $inputs = [
            ["name" => "eqid", "type" => "text", "label" => "Druh aktíva"],
            ["name" => "poddruheq", "type" => "text", "label" => "Poddruh aktíva"],
            ["name" => "skratkanbs", "type" => "text", "label" => "Skratka NBS"],
            ["name" => "druheqid", "type" => "hidden", "label" => "druheqid"],
        ];
        $createSequence = "equitydruh_druheqid_seq";
        $constantsOrderBy = "druheqid";
        $columns = ["ID", "Druh aktíva", "Poddruh aktíva", "Skratka NBS", "Akcia"];
        break;
    case str_contains($_SERVER["REQUEST_URI"], "konstanty/konstantny-symbol"):
        $pageHeading = "Správa konštantných symbolov";
        $constantsTable = "konstantnysymbol";
        $modalHeading = "konštantný symbol";
        $formID = "konstantnysymbol";
        $modalEditID = "konstatnyid";
        $toSelect = "t.*";
        $joinClause = "";
        $inputs = [
            ["name" => "cislo", "type" => "text", "label" => "Číslo"],
            ["name" => "popis", "type" => "text", "label" => "Popis konštantného symbolu"],
            ["name" => "konstatnyid", "type" => "hidden", "label" => "konstatnyid"],
        ];
        $createSequence = "konstantnysymbol_konstantnyid_seq";
        $constantsOrderBy = "konstatnyid";
        $columns = ["ID", "Číslo", "Popis konštantného symbolu", "Akcia"];
        break;
    case str_contains($_SERVER["REQUEST_URI"], "konstanty/ucet-partnera"):
        $optionsRes = Connection::getDataFromDatabase("SELECT * FROM typpartnera", defaultDB);
        $options = $optionsRes[1];
        $pageHeading = "Správa účtov partnera";
        $constantsTable = "partner";
        $modalHeading = "účet partnera";
        $formID = "partner";
        $modalEditID = "partnerid";
        $toSelect = "t.partnerid, p.popis, t.nazovpartnera, t.skratka, t.ico, t.address, t.city, t.postalcode";
        $rows = ["partnerid", "popis", "nazovpartnera", "skratka"];
        $joinClause = "INNER JOIN typpartnera p ON p.typpartnera = t.typpartnera";
        $inputs = [
            ["name" => "typpartnera", "type" => "select", "label" => "Typ partnera", "options" => $options, "optionField" => "popis", "optionValue" => "typpartnera"],
            ["name" => "nazovpartnera", "type" => "text", "label" => "Názov partnera"],
            ["name" => "skratka", "type" => "text", "label" => "Skratka"],
            ["name" => "ico", "type" => "text", "label" => "IČO"],
            ["name" => "address", "type" => "text", "label" => "Ulica"],
            ["name" => "city", "type" => "text", "label" => "Mesto"],
            ["name" => "postalcode", "type" => "text", "label" => "PSČ"],
            ["name" => "phonenumber", "type" => "text", "label" => "Telefónne číslo"],
            ["name" => "email", "type" => "text", "label" => "Email"],
            ["name" => "partnerid", "type" => "hidden", "label" => "partnerid"],
        ];
        $createSequence = "partner_partnerid_seq";
        $constantsOrderBy = "partnerid";
        $columns = ["ID", "Typ partnera", "Názov partnera", "Skratka", "Akcia"];
        break;
    case str_contains($_SERVER["REQUEST_URI"], "konstanty/odvetvia"):
        $optionsRes = Connection::getDataFromDatabase("SELECT * FROM sektor_esa95", defaultDB);
        $options = $optionsRes[1];
        $pageHeading = "Správa odvetví";
        $constantsTable = "equityodvetvie";
        $modalHeading = "nové odvetvie";
        $formID = "sektor_esa95";
        $modalEditID = "odvetvieid";
        $toSelect = "t.odvetvieid, t.odvetviepopis, s.esa95_sektorpopis";
        $joinClause = "INNER JOIN sektor_esa95 s ON s.esa95_sektorid = t.sektorid";
        $inputs = [
            ["name" => "odvetviepopis", "type" => "text", "label" => "Popis odvetvia"],
            ["name" => "sektorid", "type" => "select", "label" => "Sektor", "options" => $options, "optionField" => "esa95_sektorpopis", "optionValue" => "esa95_sektorid"],
            ["name" => "odvetvieid", "type" => "hidden", "label" => "emitentid"],
        ];
        $createSequence = "equityodvetvie_odvetvieid_seq";
        $constantsOrderBy = "odvetvieid";
        $columns = ["ID", "Odvetvie", "Sektor", "Akcia"];
        break;
    case str_contains($_SERVER["REQUEST_URI"], "konstanty/emitenti"):
        $optionsRes = Connection::getDataFromDatabase("SELECT * FROM sektor_esa95", defaultDB);
        $options = $optionsRes[1];
        $pageHeading = "Správa emitentov";
        $constantsTable = "equityemitent";
        $modalHeading = "nového emitenta";
        $formID = "sektor_esa95";
        $modalEditID = "emitentid";
        $toSelect = "*";
        $joinClause = "";
        $inputs = [
            ["name" => "emitentnazov", "type" => "text", "label" => "Názov emitenta"],
            ["name" => "emitentskratka", "type" => "text", "label" => "Skratka"],
            ["name" => "emitentstateid", "type" => "text", "label" => "Štát"],
            ["name" => "emitentico", "type" => "text", "label" => "IČO"],
            ["name" => "emitentsektor", "type" => "select", "label" => "Sektor", "options" => $options, "optionField" => "esa95_sektorpopis", "optionValue" => "esa95_sektorid"],
            ["name" => "emitentid", "type" => "hidden", "label" => "emitentid"],
        ];
        $createSequence = "equityemitent_emitentid_seq";
        $constantsOrderBy = "emitentid";
        $columns = ["ID", "Názov emitenta", "Skratka", "Štát", "IČO", "Sektor", "Akcie"];
        break;
    case str_contains($_SERVER["REQUEST_URI"], "konstanty/staty"):
        $pageHeading = "Správa štátov";
        $modalHeading = "nový štát";
        $formID = "Stat";
        $toSelect = "*";
        $joinClause = "";
        $modalEditID = "stateid";
        $inputs = [
            ["name" => "stateall", "type" => "text", "label" => "Štát"],
            ["name" => "stateshort", "type" => "text", "label" => "Kód štátu"],
            ["name" => "currency", "type" => "text", "label" => "Mena"],
            ["name" => "currencycode", "type" => "text", "label" => "Kód meny"],
            ["name" => "currencylot", "type" => "text", "label" => "Menový lot"],
            ["name" => "stateid", "type" => "hidden", "label" => "stateid"],
        ];
        $createSequence = "state_stateid_seq";
        $constantsTable = "state";
        $constantsOrderBy = "stateid";
        $columns = ["ID", "Štát", "Kód štátu", "Mena", "Kód meny", "Kurz lot", "Akcia"];
        break;
    default:
        $urlHeading = "";
        break;
}
<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
require_once "/home/<USER>/www/src/lib/functions/getDBSums.php";

if (isset($_SESSION["client"])) {
    $fondID = $_SESSION["client"]["fondid"];
} elseif ($_SESSION["mode"]["mode"] === "admin") {
    $fondID = 1;
} else {
    $fondID = 0;
}


$query = "SELECT 
        CASE 
            WHEN dlhopisy = 0 THEN 'Nie' 
            WHEN dlhopisy = 1 THEN 'Áno' 
        END as dlhopisy, 
        CASE 
            WHEN akcie = 0 THEN 'Nie' 
            WHEN akcie = 1 THEN 'Áno' 
        END as akcie, 
        refmena
	from 
		fonds 
	where 
		fondid=$fondID
 ";

$detailyFondu = Connection::getDataFromDatabase($query, defaultDB)[1][0];
$fond_dlhopisy = $detailyFondu['dlhopisy'];
$fond_akcie = $detailyFondu['akcie'];
$fond_refmena = $detailyFondu['refmena'];

$detailQuery = "SELECT nav * f_menovy_kurz_kriz(refmena, '$fond_refmena', datum,1) as nav, datum FROM pricestore WHERE fondid = $fondID and datum = (select max(datum) from pricestore where fondid = $fondID)";
$detailStav = Connection::getDataFromDatabase($detailQuery, defaultDB)[1][0];
$trhovaHodnota = $detailStav['nav'];
$trhovyDatum = $detailStav['datum'];

$query = "SELECT
		cislouctu,
       CASE
           WHEN ksks.eqid = 'BU' THEN 1
           WHEN ksks.eqid = 'TD' THEN 2
           WHEN ksks.eqid = 'Bonds' THEN 3
           WHEN ksks.eqid = 'Shares' THEN 4
           WHEN ksks.eqid = 'Fonds' THEN 5
           ELSE 6 END             AS orderList,
       ksks.eqid                  as druh,
       kodaktiva                  as aktivum,
       max(jednotka)              as jednotka,
       sum(stav)                  as stav,
       sum(stav - rezervacia - odchadzajuce - zavazokemisia - ktvviazane - poplatky - ostatne_zavazky +
           prevody)               as volne,
       sum(rezervacia + poplatky) as rezervacia,
       sum(odchadzajuce)          as odchadzajuce,
       sum(prichadzajuce)         as prichadzajuce,
       cpnaz                      as cpnaz,
       cpnazskratka               as cpnazskratka,
       isinreal                   as isinreal,
       dcr.isincurr               as isincurr
	from
	(
			select
			cutd as cislouctu,
			'' as destinacia,
			subjektid,
			mena as kodaktiva,
			'TD' as eqid,
			mena as jednotka,
			0 as stav,
			0 as rezervacia,
			0 as odchadzajuce,
			0 as prichadzajuce,
			0 as pocet,
			0 as zavazokemisia,
			0 as ktvviazane,
			0 as poplatky,
			0 as ostatne_zavazky,
			0 as prevody
			from konfirmaciaktv
			where
			subjektid = $fondID and
			logactivityid in (17,18,19,20,21,22)
		UNION ALL
			select
			ucetaktiva as cislouctu,
			'' as destinacia,
			mt.subjektid,
			mena||' ('||kodaktiva||')' as kodaktiva,
			'NDF' as eqid,
			mena as jednotka,
			pocet*(sign(md_d-0.5)*(-1)) as stav,
			0 as rezervacia,
			0 as odchadzajuce,
			0 as prichadzajuce,
			pocet*(sign(md_d-0.5)*(-1)) as pocet,
			0 as zavazokemisia,
			0 as ktvviazane,
			0 as poplatky,
			0 as ostatne_zavazky,
			0 as prevody
			from majetoktoday mt, konverzia k
			where
			mt.subjektid = $fondID and
			mt.subjektid = k.subjektid and
			'NDF-'||mt.kodaktiva = 'NDF-'||k.dealid and
			mena='SKK' and
			uctovnykod in (315181,325181) and
			logactivityid=12 and
			typ_konverzie = 1
		UNION ALL
			select
			ucetaktiva as cislouctu,
			'majetoktoday' as destinacia,
			subjektid,
			kodaktiva,
			eqid,
			jednotka,
			0 as stav,
			0 as rezervacia,
			0 as odchadzajuce,
			0 as prichadzajuce,
			0 as pocet,
			0 as zavazokemisia,
			0 as ktvviazane,
			(sign(md_d - 0.5) * pocet) as poplatky,
			0 as ostatne_zavazky,
			0 as prevody
			from majetoktoday
			where
			subjektid = $fondID and
			uctovnykod in (325413,325411,325412,325422,325421,325435,325437,325521,325522,325143,325443)
		UNION ALL
			select
			ucetaktiva as cislouctu,
			'majetoktoday' as destinacia,
			subjektid,
			kodaktiva,
			eqid,
			jednotka,
			0 as stav,
			0 as rezervacia,
			0 as odchadzajuce,
			0 as prichadzajuce,
			0 as pocet,
			(sign(md_d - 0.5) * pocet) as zavazokemisia,
			0 as ktvviazane,
			0 as poplatky,
			0 as ostatne_zavazky,
			0 as prevody
			from majetoktoday
			where
			subjektid = $fondID and
			uctovnykod in (325210)
		UNION ALL
			select
			ucetaktiva as cislouctu,
			'majetoktoday' as destinacia,
			subjektid,
			kodaktiva,
			eqid,
			jednotka,
			0 as stav,
			0 as rezervacia,
			0 as odchadzajuce,
			0 as prichadzajuce,
			0 as pocet,
			0 as zavazokemisia,
			0 as ktvviazane,
			0 as poplatky,
			(sign(md_d - 0.5) * pocet) as ostatne_zavazky,
			0 as prevody
			from majetoktoday
			where
			subjektid = $fondID and
			uctovnykod in (325300,325350)
		UNION ALL
			select
			ucetaktiva as cislouctu,
			'majetoktoday' as destinacia,
			subjektid,
			kodaktiva,
			eqid,
			jednotka,
			0 as stav,
			0 as rezervacia,
			(sign(md_d - 0.5) * pocet) as odchadzajuce,
			0 as prichadzajuce,
			0 as pocet,
			0 as zavazokemisia,
			0 as ktvviazane,
			0 as poplatky,
			0 as ostatne_zavazky,
			0 as prevody
			from majetoktoday
			where
			subjektid = $fondID and
			uctovnykod in (325700)
		UNION ALL
			select
			ucetaktiva as cislouctu,
			'majetoktoday' as destinacia,
			subjektid,
			kodaktiva,
			eqid,
			jednotka,
			(sign(md_d - 0.5) * (-1) * pocet) as stav,
			0 as rezervacia,
			0 as odchadzajuce,
			0 as prichadzajuce,
			(sign(md_d - 0.5) * (-1) * pocet) as pocet,
			0 as zavazokemisia,
			0 as ktvviazane,
			0 as poplatky,
			0 as ostatne_zavazky,
			0 as prevody
			from majetoktoday
			where
			subjektid = $fondID and
			uctovnykod in ( 221110,221210,251110,251200,251300,251400)
		UNION ALL
			select
			ucetaktiva as cislouctu,
			'majetokcesta' as destinacia,
			subjektid,
			kodaktiva,
			eqid,
			jednotka,
			0 as stav,
			0 as rezervacia,
			((1-in_out) * pocet) as odchadzajuce,
			(in_out * pocet) as prichadzajuce,
			(sign(in_out - 0.5) * pocet) as pocet,
			0 as zavazokemisia,
			0 as ktvviazane,
			0 as poplatky,
			0 as ostatne_zavazky,
			0 as prevody
			from majetokcesta ma
			where subjektid = $fondID and
			 in_out=1
		UNION ALL
			select
			ma.ucetaktiva as cislouctu,
			'majetokcesta' as destinacia,
			pdr.subjektid,
			ma.kodaktiva,
			ma.eqid,
			ma.jednotka,
			0 as stav,
			0 as rezervacia,
			((1-ma.in_out) * pdr.ksreal) as odchadzajuce,
			(ma.in_out * pdr.ksreal) as prichadzajuce,
			(sign(ma.in_out - 0.5) * pdr.ksreal) as pocet,
			0 as zavazokemisia,
			0 as ktvviazane,
			0 as poplatky,
			0 as ostatne_zavazky,
			0 as prevody
			from majetokcesta ma, today t, pool p, pooldetailreal pdr
			where ma.subjektid = 0 and 
      			pdr.subjektid = $fondID and
			t.fondid = ma.subjektid and
			ma.cestatill >=t.datum and
			ma.in_out=1 and 
			(ma.destinacia = p.destinacia or (ma.destinacia = 'rekonfirmaciacp' and p.destinacia = 'konfirmaciacp'))  
			and ma.dealid = p.dealid 
			and p.poolid = pdr.poolid
			and ma.eqid in ('Bonds', 'Shares', 'Fonds')
		UNION ALL
			select
			ma.ucetaktiva as cislouctu,
			'majetokcesta' as destinacia,
			pdr.subjektid,
			ma.kodaktiva,
			ma.eqid,
			ma.jednotka,
			0 as stav,
			0 as rezervacia,
			((1-ma.in_out) * pdr.transsumareal) as odchadzajuce,
			(ma.in_out * pdr.transsumareal) as prichadzajuce,
			(sign(ma.in_out - 0.5) * pdr.transsumareal) as pocet,
			0 as zavazokemisia,
			0 as ktvviazane,
			0 as poplatky,
			0 as ostatne_zavazky,
			0 as prevody
			from majetokcesta ma, today t, pool p, pooldetailreal pdr
			where ma.subjektid = 0 and 
      			pdr.subjektid = $fondID and
			t.fondid = ma.subjektid and
			ma.cestatill >=t.datum and
			ma.in_out=1 and 
			(ma.destinacia = p.destinacia or (ma.destinacia = 'rekonfirmaciacp' and p.destinacia = 'konfirmaciacp'))  
			and ma.dealid = p.dealid 
			and p.poolid = pdr.poolid
			and ma.eqid in ('BU')
			and ma.tranza = 1
		UNION ALL
			select
			ma.ucetaktiva as cislouctu,
			'majetokcesta' as destinacia,
			pdr.subjektid,
			ma.kodaktiva,
			ma.eqid,
			ma.jednotka,
			0 as stav,
			0 as rezervacia,
			 ((1 - ma.in_out) * (COALESCE(pdr.auvreal, 0) - COALESCE(pdr.dan, 0))) AS odchadzajuce,
             (ma.in_out * (COALESCE(pdr.auvreal, 0) - COALESCE(pdr.dan, 0)))       AS prichadzajuce,
             (SIGN(ma.in_out - 0.5) *
              (COALESCE(pdr.auvreal, 0) - COALESCE(pdr.dan, 0)))                   AS pocet,
			0 as zavazokemisia,
			0 as ktvviazane,
			0 as poplatky,
			0 as ostatne_zavazky,
			0 as prevody
			from majetokcesta ma, today t, pool p, pooldetailreal pdr
			where ma.subjektid = 0 and 
      			pdr.subjektid = $fondID and
			t.fondid = ma.subjektid and
			ma.cestatill >=t.datum and
			ma.in_out=1 and 
			(ma.destinacia = p.destinacia or (ma.destinacia = 'rekonfirmaciacp' and p.destinacia = 'konfirmaciacp'))  
			and ma.dealid = p.dealid 
			and p.poolid = pdr.poolid
			and ma.eqid in ('BU')
			and ma.tranza = 2
      		UNION ALL
			select
			ucetaktiva as cislouctu,
			'majetokcesta' as destinacia,
			subjektid,
			kodaktiva,
			eqid,
			jednotka,
			0 as stav,
			0 as rezervacia,
			((1-in_out) * pocet) as odchadzajuce,
			(in_out * pocet) as prichadzajuce,
			(sign(in_out - 0.5) * pocet) as pocet,
			0 as zavazokemisia,
			0 as ktvviazane,
			0 as poplatky,
			0 as ostatne_zavazky,
			0 as prevody
			from majetokcesta ma
			where subjektid = $fondID and
			 in_out=0
		UNION ALL
			select 
       			ma.ucetaktiva as cislouctu,
			'majetokcesta' as destinacia,
			pdr.subjektid,
			ma.kodaktiva,
			ma.eqid,
			ma.jednotka,
			0 as stav,
			0 as rezervacia,
			((1-ma.in_out) * pdr.ksreal) as odchadzajuce,
			(ma.in_out * pdr.ksreal) as prichadzajuce,
			(sign(ma.in_out - 0.5) * pdr.ksreal) as pocet,
			0 as zavazokemisia,
			0 as ktvviazane,
			0 as poplatky,
			0 as ostatne_zavazky,
			0 as prevody
			from majetokcesta ma, pool p, pooldetailreal pdr
			where 
			ma.subjektid = 0 and
			ma.in_out=0 and 
			(ma.destinacia = p.destinacia or (ma.destinacia = 'rekonfirmaciacp' and p.destinacia = 'konfirmaciacp'))  
			and ma.dealid = p.dealid and 
			p.poolid = pdr.poolid and
			pdr.subjektid = $fondID
			and ma.eqid in ('Bonds', 'Shares', 'Fonds')
		UNION ALL
			select 
       			ma.ucetaktiva as cislouctu,
			'majetokcesta' as destinacia,
			pdr.subjektid,
			ma.kodaktiva,
			ma.eqid,
			ma.jednotka,
			0 as stav,
			0 as rezervacia,
			((1-ma.in_out) * pdr.transsumareal) as odchadzajuce,
			(ma.in_out * pdr.transsumareal) as prichadzajuce,
			(sign(ma.in_out - 0.5) * pdr.transsumareal) as pocet,
			0 as zavazokemisia,
			0 as ktvviazane,
			0 as poplatky,
			0 as ostatne_zavazky,
			0 as prevody
			from majetokcesta ma, pool p, pooldetailreal pdr
			where 
			ma.subjektid = 0 and
			ma.in_out=0 and 
			(ma.destinacia = p.destinacia or (ma.destinacia = 'rekonfirmaciacp' and p.destinacia = 'konfirmaciacp'))  
			and ma.dealid = p.dealid and 
			p.poolid = pdr.poolid and
			pdr.subjektid = $fondID
			and ma.eqid in ('BU')
		UNION ALL
			select
			ucetaktiva as cislouctu,
			'rezervacia' as destinacia,
			subjektid,
			kodaktiva,
			eqid,
			jednotka,
			0 as stav,
			pocet as rezervacia,
			0 as odchadzajuce,
			0 as prichadzajuce,
			(pocet * -1) as pocet,
			0 as zavazokemisia,
			0 as ktvviazane,
			0 as poplatky,
			0 as ostatne_zavazky,
			0 as prevody
			from rezervacia
			where subjektid = $fondID
				and destinacia <> 'konfirmaciacp'
		UNION ALL
			select
			r.ucetaktiva as cislouctu,
			'rezervacia' as destinacia,
			r.subjektid,
			r.kodaktiva,
			r.eqid,
			r.jednotka,
			0 as stav,
			pd.transsuma as rezervacia,
			0 as odchadzajuce,
			0 as prichadzajuce,
			(pd.transsuma * -1) as pocet,
			0 as zavazokemisia,
			0 as ktvviazane,
			0 as poplatky,
			0 as ostatne_zavazky,
			0 as prevody
			from rezervacia r, pool p, pooldetail pd
			where r.subjektid = 0 and p.dealid = r.dealid and pd.poolid = p.poolid and r.destinacia = 'konfirmaciaktv' and pd.subjektid = $fondID
		UNION ALL
			select
			ucet as cislouctu,
			'prevodPP' as destinacia,
			subjektid,
			mena as kodaktiva,
			'BU' as eqid,
			mena as jednotka,
			0 as stav,
			0 as rezervacia,
			suma as odchadzajuce,
			0 as prichadzajuce,
			(-1 * suma) as pocet,
			0 as zavazokemisia,
			0 as ktvviazane,
			0 as poplatky,
			0 as ostatne_zavazky,
			0 as prevody
			from konfirmaciapp
			where subjektid = $fondID and
			druhobchodu = 'prevod' and
			logactivityid < 3 and
      			dealid_related is not NULL
		UNION ALL
			select
			ucet as cislouctu,
			'prevodPP' as destinacia,
			subjektid,
			mena as kodaktiva,
			'BU' as eqid,
			mena as jednotka,
			0 as stav,
			0 as rezervacia,
			0 as odchadzajuce,
			suma as prichadzajuce,
			suma as pocet,
			0 as zavazokemisia,
			0 as ktvviazane,
			0 as poplatky,
			0 as ostatne_zavazky,
			suma as prevody
			from konfirmaciapp
			where subjektid = $fondID and
			druhobchodu = 'prevod' and
			logactivityid < 3 and
      			dealid_related is NULL  
	) ksks
	LEFT JOIN dbequitycurrric dcr ON dcr.isincurrric = ksks.kodaktiva
    LEFT JOIN dbequitycurr dc ON dc.isincurr = dcr.isincurr
    LEFT JOIN dbequity d ON d.isin = dc.isin
    group by cislouctu, ksks.eqid, kodaktiva, cpnaz, cpnazskratka, isinreal,  dcr.isincurr
    order by orderList, aktivum";

$tableData = Connection::getDataFromDatabase($query, defaultDB)[1];


$dostupneSum = 0;
$dostupneData = [];
$rezervovaneSum = 0;
$rezervovaneData = [];
$odchadzajuceSum = 0;
$odchadzajuceData = [];
$prichadzajuceSum = 0;
$prichadzajuceSumData = [];

foreach ($tableData as $item) {
    if ($item["druh"] == "BU" && $item["stav"] > 0) {
        $dostupneSum += $item["volne"];
        if ($item["volne"] > 0) {
            foreach ($dostupneData as $key => $value) {
                if ($value["currency"] == $item["jednotka"]) {
                    $dostupneData[$key]["amount"] += $item["volne"];
                    $skip = true;
                }
            }
            if (!$skip) {
                $dostupneData[] = [
                    "amount" => $item["volne"],
                    "currency" => $item["jednotka"]
                ];
            }
        }
    }
    $rezervovaneSum += $item["rezervacia"];
    $odchadzajuceSum += $item["odchadzajuce"];
    $prichadzajuceSum += $item["prichadzajuce"];

    if ($item["prichadzajuce"] > 0) {
        $prichadzajuceData[] = [
            "amount" => $item["prichadzajuce"],
            "currency" => $item["jednotka"]
        ];
    }
    if ($item["odchadzajuce"] > 0) {
        $odchadzajuceData[] = [
            "amount" => $item["odchadzajuce"],
            "currency" => $item["jednotka"]
        ];
    }
    if ($item["rezervacia"] > 0) {
        $rezervovaneData[] = [
            "amount" => $item["rezervacia"],
            "currency" => $item["jednotka"]
        ];
    }
}

?>
<main id="mainMore" hx-trigger="load" hx-get="/api/global/data" hx-target="#mainMore" hx-swap="innerHTML">
    <div class="mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
            <div data-slot="card"
                class="bg-card text-card-foreground flex flex-col gap-6 border border-gray-200 py-6 shadow-sm rounded-md dark:bg-gray-700 dark:text-gray-200">
                <div data-slot="card-header"
                    class="grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6">
                    <div data-slot="card-description" class="text-muted-foreground text-sm">Celková trhová hodnota</div>
                    <div data-slot="card-title" class="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">
                        <?php echo number_format($trhovaHodnota, 2, '.', ' '); ?> <?php echo $fond_refmena; ?>
                    </div>
                    <!-- <div data-slot="card-action" class="col-start-2 row-span-2 row-start-1 self-start justify-self-end">
                    <span data-slot="badge"
                        class="inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&amp;>svg]:size-3 gap-1 [&amp;>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden text-foreground [a&amp;]:hover:bg-accent [a&amp;]:hover:text-accent-foreground"><svg
                            xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="tabler-icon tabler-icon-trending-up ">
                            <path d="M3 17l6 -6l4 4l8 -8"></path>
                            <path d="M14 7l7 0l0 7"></path>
                        </svg>+12.5%</span>
                </div> -->
                </div>
            </div>
            <div data-slot="card"
                class="bg-card text-card-foreground dark:bg-gray-700 dark:text-gray-200 flex flex-col gap-6 rounded-md border border-green-200 py-6 shadow-sm">
                <div data-slot="card-header"
                    class="grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6">
                    <div data-slot="card-description" class="text-muted-foreground text-sm">Voľné peňažné prostriedky
                    </div>
                    <div data-slot="card-title" class="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">
                        <?php echo number_format($dostupneSum, 2, '.', ' '); ?>
                    </div>
                    <div data-slot="card-footer"
                        class="flex [.border-t]:pt-6 flex-col dark:text-gray-300 text-gray-600 items-start gap-1.5 text-sm">
                        <div class="line-clamp-1 flex flex-col gap-2 font-medium">
                            <?php foreach ($dostupneData as $item) { ?>
                                <div class="flex items-center gap-2">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round"
                                        class="lucide lucide-circle-dollar-sign-icon dark:text-green-400 text-green-800 lucide-circle-dollar-sign">
                                        <circle cx="12" cy="12" r="10" />
                                        <path d="M16 8h-6a2 2 0 1 0 0 4h4a2 2 0 1 1 0 4H8" />
                                        <path d="M12 18V6" />
                                    </svg>
                                    <span><?php echo number_format($item["amount"], 2, '.', ' ') . " " . $item["currency"]; ?></span>
                                </div>
                            <?php } ?>
                        </div>
                    </div>
                </div>
            </div>
            <div data-slot="card"
                class="bg-card text-card-foreground flex flex-col gap-6 border border-gray-200 py-6 shadow-sm rounded-md dark:bg-gray-700 dark:text-gray-200">
                <div data-slot="card-header"
                    class="grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6">
                    <div data-slot="card-description" class="text-muted-foreground text-sm">Rezervované prostriedky
                    </div>
                    <div data-slot="card-title" class="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">
                        <?php echo number_format($rezervovaneSum, 2, '.', ' '); ?>
                    </div>
                    <div data-slot="card-footer"
                        class="flex [.border-t]:pt-6 flex-col dark:text-yellow-300 text-yellow-600 items-start gap-1.5 text-sm">
                        <div class="line-clamp-1 flex flex-col gap-2 font-medium">
                            <?php foreach ($rezervovaneData as $item) { ?>
                                <div class="flex items-center gap-2">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-clock-alert-icon lucide-clock-alert">
                                        <path d="M12 6v6l4 2" />
                                        <path d="M16 21.16a10 10 0 1 1 5-13.516" />
                                        <path d="M20 11.5v6" />
                                        <path d="M20 21.5h.01" />
                                    </svg>
                                    <span><?php echo number_format($item["amount"], 2, '.', ' ') . " " . $item["currency"]; ?></span>
                                </div>
                            <?php } ?>
                        </div>
                    </div>
                </div>
            </div>
            <div data-slot="card"
                class="bg-card text-card-foreground flex flex-col gap-6 border border-gray-200 py-6 shadow-sm dark:bg-gray-700 dark:text-gray-200 rounded-md">
                <div data-slot="card-header"
                    class="grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6">
                    <div data-slot="card-description" class="text-muted-foreground text-sm">Odchadzajúce prostriedky
                    </div>
                    <div data-slot="card-title" class="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">
                        <?php echo number_format($odchadzajuceSum, 2, '.', ' '); ?>
                    </div>
                    <div data-slot="card-footer"
                        class="flex [.border-t]:pt-6 flex-col dark:text-red-300 text-red-600 items-start gap-1.5 text-sm">
                        <div class="line-clamp-1 flex flex-col gap-2 font-medium">
                            <?php foreach ($odchadzajuceData as $item) { ?>
                                <div class="flex items-center gap-2">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round"
                                        class="lucide lucide-arrow-up-right-icon lucide-arrow-up-right">
                                        <path d="M7 7h10v10" />
                                        <path d="M7 17 17 7" />
                                    </svg>
                                    <span><?php echo number_format($item["amount"], 2, '.', ' ') . " " . $item["currency"]; ?></span>
                                </div>
                            <?php } ?>
                        </div>
                    </div>
                </div>
            </div>
            <div data-slot="card"
                class="bg-card text-card-foreground flex flex-col gap-6 border border-gray-200 py-6 shadow-sm dark:bg-gray-700 dark:text-gray-200 rounded-md">
                <div data-slot="card-header"
                    class="grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6">
                    <div data-slot="card-description" class="text-muted-foreground text-sm">Prichadzajúce prostriedky
                    </div>
                    <div data-slot="card-title" class="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">
                        <?php echo number_format($prichadzajuceSum, 2, '.', ' '); ?>
                    </div>
                    <div data-slot="card-footer"
                        class="flex [.border-t]:pt-6 flex-col dark:text-green-300 text-green-600 items-start gap-1.5 text-sm">
                        <div class="line-clamp-1 flex flex-col gap-2 font-medium">
                            <?php foreach ($prichadzajuceData as $item) { ?>
                                <div class="flex items-center gap-2">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round"
                                        class="lucide lucide-arrow-down-left-icon lucide-arrow-down-left">
                                        <path d="M17 7 7 17" />
                                        <path d="M17 17H7V7" />
                                    </svg>
                                    <span><?php echo number_format($item["amount"], 2, '.', ' ') . " " . $item["currency"]; ?></span>
                                </div>
                            <?php } ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Portfolio Overview with Filters -->
        <div id="customContextMenuWrapper"
            class="bg-white dark:bg-gray-600 rounded-lg shadow-lg relative overflow-hidden mb-8">
            <div
                class="px-6 py-5 border-b dark:border-gray-800 border-gray-200 bg-blue-50 dark:bg-blue-900 flex flex-col lg:flex-row justify-between items-start lg:items-center">
                <h2 class="text-lg font-medium dark:text-gray-100 text-gray-900 mb-4 lg:mb-0">Prehľad portfólia</h2>
                <div class="flex flex-col dark:text-gray-100 md:flex-row space-y-2 md:space-y-0 md:space-x-4 text-sm">
                    <div>
                        <span class="font-medium">Dlhopisy: </span>
                        <span class="font-bold">Áno</span>
                    </div>
                    <div>
                        <span class="font-medium">Akcie: </span>
                        <span class="font-bold">Áno</span>
                    </div>
                    <div>
                        <span class="font-medium">Refernčná mena: </span>
                        <span class="font-bold">EUR</span>
                    </div>
                </div>
            </div>
            <?php foreach ($tableData as $key => $item) {
                $menaItem = substr($item["isincurr"], -3);
                if ($menaItem === "") {
                    $menaItem = $item["aktivum"];
                }
                switch ($item["druh"]) {
                    case "BU":
                        $druh = "/investicne-zamery/pridat/konverzie";
                        break;
                    case "Bonds":
                        $druh = "/investicne-zamery/pridat/dlhopisy/new/" . $item["isinreal"];
                        break;
                    case "Shares":
                        $druh = "/investicne-zamery/pridat/akcie/new/" . $item["isinreal"];
                        break;
                    case "Fonds":
                        $druh = "/investicne-zamery/pridat/podielove-fondy/new/" . $item["isinreal"];
                        break;
                    default:
                        $druh = $item["druh"];
                        break;
                }
                ?>
                <div id="dropdown<?php echo $key; ?>"
                    class="z-10 bg-white dark:bg-gray-900 divide-y divide-gray-100 dropdownElement rounded-lg border border-gray-200 shadow-md w-44 dark:bg-gray-700"
                    style="display: none;">
                    <ul class="p-2 text-sm flex flex-col gap-1 dark:text-gray-200" aria-labelledby="dropdownDefaultButton">
                        <li>
                            <small class="text-gray-400 font-medium">Rýchle akcie</small>
                        </li>
                        <li>
                            <div hx-get="<?php echo $druh;
                            if ($item["druh"] === "BU") {
                                echo "?buyCurr=$menaItem";
                            } else {
                                echo "?action=buy&mena=$menaItem";
                            } ?>" hx-target="#pageContentMain" hx-replace-url="true" hx-push-url="true" class="block px-4 py-2 hover:bg-green-300 bg-green-200 transition-all 
                            cursor-pointer rounded-lg flex items-center
                             gap-2 text-green-800 dark:hover:bg-gray-600 dark:hover:text-white">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="lucide lucide-circle-plus-icon lucide-circle-plus">
                                    <circle cx="12" cy="12" r="10" />
                                    <path d="M8 12h8" />
                                    <path d="M12 8v8" />
                                </svg>
                                <span>Nákup</span>
                            </div>
                        </li>
                        <li>
                            <div hx-get="<?php echo $druh;
                            if ($item["druh"] === "BU") {
                                echo "?sellCurr=$menaItem";
                            } else {
                                echo "?action=sell&mena=$menaItem";
                            } ?>" hx-target="#pageContentMain" hx-replace-url="true" hx-push-url="true" class="block px-4 py-2 hover:bg-red-200 bg-red-100 transition-all 
                            cursor-pointer rounded-lg flex items-center
                             gap-2 text-green-500 dark:hover:bg-gray-600 dark:hover:text-white">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="lucide lucide-circle-minus-icon lucide-circle-minus">
                                    <circle cx="12" cy="12" r="10" />
                                    <path d="M8 12h8" />
                                </svg>
                                <span>Predaj</span>
                            </div>
                        </li>
                        <li>
                            <a href="#"
                                class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">Detail</a>
                        </li>
                    </ul>
                </div>
            <?php } ?>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y dark:divide-gray-700 divide-gray-200">
                    <thead class="bg-gray-50 dark:bg-gray-900 dark:text-gray-100">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium  uppercase tracking-wider">
                                <div class="flex items-center cursor-pointer">
                                    Číslo účtu
                                    <i class="fas fa-sort ml-1"></i>
                                </div>
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium  uppercase tracking-wider">
                                <div class="flex items-center cursor-pointer">
                                    Druh
                                    <i class="fas fa-sort ml-1"></i>
                                </div>
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium  uppercase tracking-wider">
                                <div class="flex items-center cursor-pointer">
                                    Aktívum
                                    <i class="fas fa-sort ml-1"></i>
                                </div>
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium  uppercase tracking-wider">
                                <div class="flex items-center cursor-pointer">
                                    Skratka
                                    <i class="fas fa-sort ml-1"></i>
                                </div>
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium  uppercase tracking-wider">
                                <div class="flex items-center cursor-pointer">
                                    Stav
                                    <i class="fas fa-sort ml-1"></i>
                                </div>
                            </th>
                            <th scope="col" class="px-6 py-3 text-center text-xs font-bold  uppercase tracking-wider">
                                Voľné
                            </th>
                            <th scope="col" class="px-6 py-3 text-center text-xs font-medium  uppercase tracking-wider">
                                Rezervované
                            </th>
                            <th scope="col" class="px-6 py-3 text-center text-xs font-medium  uppercase">
                                Odchádzajúce
                            </th>
                            <th scope="col" class="px-6 py-3 text-center text-xs font-medium  uppercase">
                                Prichadzajúce
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-gray-700 divide-y divide-gray-200">
                        <?php foreach ($tableData as $key => $item) { ?>
                            <tr id="<?php echo $key; ?>"
                                class="hover:bg-gray-300 dark:text-gray-200 rower cursor-pointer transition-all">
                                <td class="px-3 py-2 whitespace-nowrap text-sm font-bold">
                                    <?php echo $item["cislouctu"]; ?>
                                </td>
                                <td class="px-3 py-2 whitespace-nowrap text-sm "><?php echo $item["druh"]; ?></td>
                                <td class="px-3 py-2 whitespace-nowrap max-w-2xl text-sm ">
                                    <span data-tooltip-target="tooltip-default<?php echo $key; ?>"
                                        type="button"><?php echo $item["cpnaz"] ? $item["cpnaz"] : $item["aktivum"]; ?></span>
                                    <?php if ($item["cpnaz"]) { ?>
                                        <div id="tooltip-default<?php echo $key; ?>" role="tooltip"
                                            class="absolute z-10 invisible inline-block px-3 py-2 text-sm font-medium text-white transition-opacity duration-300 bg-gray-900 rounded-lg shadow-xs opacity-0 tooltip dark:bg-gray-700">
                                            <?php echo $item["aktivum"]; ?>
                                            <div class="tooltip-arrow" data-popper-arrow></div>
                                        </div>
                                    <?php } ?>
                                </td>
                                <td class="px-3 py-2 whitespace-nowrap text-sm ">
                                    <?php echo $item["cpnazskratka"] ? $item["cpnazskratka"] : "-"; ?>
                                </td>
                                <td class="px-3 py-2 whitespace-nowrap text-sm ">
                                    <?php echo $item["stav"]; ?>
                                </td>
                                <td class="px-3 py-2 text-center font-bold whitespace-nowrap text-sm text-gray-900">
                                    <?php echo number_format($item["volne"], 2, ".", " "); ?>
                                </td>
                                <td class="px-3 py-2 text-center whitespace-nowrap text-sm ">
                                    <?php echo number_format($item["rezervacia"], 2, ".", " "); ?>
                                </td>
                                <td class="px-3 py-2 text-center whitespace-nowrap rounded-lg text-sm font-medium">
                                    <span
                                        class="p-1 px-2 rounded-lg <?php echo $item["odchadzajuce"] == 0 ? "dark:text-gray-300 text-gray-600" : "text-red-700 bg-red-200"; ?>">
                                        <?php echo number_format($item["odchadzajuce"], 2, ".", " "); ?>
                                    </span>
                                </td>
                                <td class="px-3 py-2 whitespace-nowrap text-center text-sm  font-medium">
                                    <span
                                        class="p-1 px-2 rounded-lg <?php echo $item["prichadzajuce"] == 0 ? "dark:text-gray-300 text-gray-600" : "text-green-700 bg-green-200"; ?>">
                                        <?php echo number_format($item["prichadzajuce"], 2, ".", " "); ?>
                                    </span>
                                </td>
                            </tr>
                        <?php } ?>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Portfolio Analytics -->
        <?php
        $prostriedkySum = 0.00;
        $dlhopisySum = 0.00;
        $akcieSum = 0.00;
        $podieloveSum = 0.00;
        $zavazkySum = 0.00;
        $totalSum = 0.00;
        $podielProstriedky = 0.00;
        $podielDlhopisy = 0.00;
        $podielAkcie = 0.00;
        $podielPodielove = 0.00;
        $podielZavazky = 0.00;


        $datumQuery = Connection::getDataFromDatabase("SELECT max(datum) FROM pricestore WHERE fondid = $fondID", defaultDB);
        $dbdate = $datumQuery[1][0]["max"];
        $query = "SELECT menaref FROM majetoktotal WHERE datum=to_date('$dbdate','YYYY-MM-DD') ";
        if ($fondID) {
            $query .= " ";
        } else if ($fondID != "") {
            $query .= " and subjektid = $fondID";
        }
        $queryRes = Connection::getDataFromDatabase($query, defaultDB);
        $result = $queryRes[1][0];

        $menaref = $result["menaref"];

        $p1 = DBSums::get($fondID, $dbdate, "'BU','TD'", '221110,221210,315132,315113', false);
        $suma_p1 = round($p1, 2);

        $d1 = DBSums::get($fondID, $dbdate, "'Bonds'", '251110,251120', false);
        $suma_d1 = round($d1, 2);

        $a1 = DBSums::get($fondID, $dbdate, "'Shares'", '251200', false);
        $suma_a1 = round($a1, 2);

        $f1 = DBSums::get($fondID, $dbdate, "'Fonds','Shares'", '251300,251200', false);
        $suma_f1 = round($f1, 2);

        $paz1 = DBSums::get($fondID, $dbdate, "'Fonds','Bonds','Shares','TD','BU'", "", true);
        $suma_paz1 = round($paz1, 2);

        $total = (float) $p1 + (float) $d1 + (float) $a1 + (float) $f1 + (float) $paz1;
        $total_suma = round($total, 2);
        $totalSum += $total_suma;
        $prostriedkySum += $suma_p1;
        $dlhopisySum += $suma_d1;
        $akcieSum += $suma_a1;
        $podieloveSum += $suma_f1;
        $zavazkySum += $suma_paz1;

        if ($total == 0) {
            $total = 1;
        }

        $p2 = ($p1 / $total) * 100;
        $podiel_p2 = round($p2, 3);
        $podielProstriedky += $podiel_p2;
        $d2 = ($d1 / $total) * 100;
        $podiel_d2 = round($d2, 3);
        $podielDlhopisy += $podiel_d2;
        $a2 = ($a1 / $total) * 100;
        $podiel_a2 = round($a2, 3);
        $podielAkcie += $podiel_a2;
        $f2 = ($f1 / $total) * 100;
        $podiel_f2 = round($f2, 3);
        $podielPodielove += $podiel_f2;
        $paz2 = ($paz1 / $total) * 100;
        $podiel_paz2 = round($paz2, 3);
        $podielZavazky += $podiel_paz2;

        ?>
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
            <!-- Asset Allocation -->
            <div class="bg-white dark:bg-gray-900 dark:text-gray-200 rounded-lg shadow overflow-hidden">
                <div class="px-6 py-5 border-b border-gray-200">
                    <h2 class="text-lg font-medium flex items-center">
                        Asset Allocation
                    </h2>
                </div>
                <div class="p-6">
                    <div class="flex flex-col space-y-4">
                        <div>
                            <div class="flex justify-between mb-1">
                                <span class="text-sm font-medium">Peňažné prostriedky (BU)</span>
                                <span
                                    class="text-sm font-bold"><?php echo number_format($prostriedkySum, 2, '.', ' '); ?>
                                    &nbsp;<?php echo $menaref ?>
                                </span>
                            </div>
                            <div class="w-full bg-gray-200 relative text-center rounded-lg py-2"
                                style="background-color:rgba(54, 163, 235, 0.22);">
                                <div class="h-8 top-0 left-0 absolute rounded-full"
                                    style="width: <?php echo $podielProstriedky; ?>%; background-color: #36A2EB;"></div>
                                <p class="text-xs w-full"><?php echo $podielProstriedky; ?>%</p>
                            </div>
                        </div>

                        <div>
                            <div class="flex justify-between mb-1">
                                <span class="text-sm font-medium">Dlhopisy</span>
                                <span
                                    class="text-sm font-bold"><?php echo number_format($dlhopisySum, 2, '.', ' '); ?>&nbsp;<?php echo $menaref ?>
                                </span>
                            </div>
                            <div class="w-full bg-gray-200 relative text-center rounded-lg py-2"
                                style="background-color:rgba(255, 99, 133, 0.22);">
                                <div class="h-8 top-0 left-0 absolute rounded-full"
                                    style="width: <?php echo $podielDlhopisy; ?>%; background-color: #FF6384;"></div>
                                <p class="text-xs w-full"><?php echo $podielDlhopisy; ?>%</p>
                            </div>
                        </div>

                        <div>
                            <div class="flex justify-between mb-1">
                                <span class="text-sm font-medium">Akcie</span>
                                <span
                                    class="text-sm font-bold"><?php echo number_format($akcieSum, 2, '.', ' '); ?>&nbsp;<?php echo $menaref ?>
                                </span>
                            </div>
                            <div class="w-full bg-gray-200 relative text-center rounded-lg py-2"
                                style="background-color:rgba(255, 160, 64, 0.22);">
                                <div class="h-8 top-0 left-0 absolute rounded-full"
                                    style="width: <?php echo $podielAkcie; ?>%; background-color: #FF9F40;"></div>
                                <p class="text-xs w-full"><?php echo $podielAkcie; ?>%</p>
                            </div>
                        </div>

                        <div>
                            <div class="flex justify-between mb-1">
                                <span class="text-sm font-medium">Podielové fondy (ETF)</span>
                                <span
                                    class="text-sm font-bold"><?php echo number_format($podieloveSum, 2, '.', ' '); ?>&nbsp;<?php echo $menaref ?>
                                </span>
                            </div>
                            <div class="w-full bg-gray-200 relative text-center rounded-lg py-2"
                                style="background-color:rgba(255, 204, 86, 0.22);">
                                <div class="h-8 top-0 left-0 absolute z-0 rounded-full"
                                    style="width: <?php echo $podielPodielove; ?>%; background-color: #FFCD56;"></div>
                                <p class="text-xs relative z-10 w-full"><?php echo $podielPodielove; ?>%</p>
                            </div>
                        </div>

                        <div>
                            <div class="flex justify-between mb-1">
                                <span class="text-sm font-medium">Podielové fondy (ETF)</span>
                                <span
                                    class="text-sm font-bold"><?php echo number_format($zavazkySum, 2, '.', ' '); ?>&nbsp;<?php echo $menaref ?>
                                </span>
                            </div>
                            <div class="w-full bg-gray-200 relative text-center rounded-lg py-2"
                                style="background-color:rgba(75, 192, 192, 0.22);">
                                <div class="h-8 top-0 left-0 absolute z-0 rounded-full"
                                    style="width: <?php echo $podielZavazky; ?>%; background-color: #4BC0C0;"></div>
                                <p class="text-xs relative z-10 w-full"><?php echo $podielZavazky; ?>%</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Transactions -->
            <?php
            $transactions = Connection::getDataFromDatabase("SELECT * FROM majetoktoday
                WHERE subjektid = $fondID ORDER BY obratid DESC LIMIT 8", defaultDB)[1];
            if (sizeof($transactions) < 8) {
                $limit = 8 - sizeof($transactions);
                $transactionsArchiv = Connection::getDataFromDatabase("SELECT DISTINCT * FROM majetokarchiv
                WHERE subjektid = $fondID ORDER BY obratdatetimereal DESC LIMIT $limit", defaultDB)[1];
            }
            ?>
            <div class="bg-white dark:bg-gray-900 dark:text-gray-200 rounded-lg shadow overflow-hidden col-span-2">
                <div class="px-6 py-5 border-b border-gray-200">
                    <h2 class="text-lg font-medium dark:text-gray-200 text-gray-900 flex gap-4 items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-arrow-right-left-icon lucide-arrow-right-left">
                            <path d="m16 3 4 4-4 4" />
                            <path d="M20 7H4" />
                            <path d="m8 21-4-4 4-4" />
                            <path d="M4 17h16" />
                        </svg>
                        Nedávne transakcie
                    </h2>
                </div>
                <div class="p-6">
                    <div class="space-y-1">
                        <?php foreach ($transactions as $transaction) { ?>
                            <div
                                class="flex justify-between dark:hover:bg-gray-700 hover:bg-gray-100 p-2 rounded-lg cursor-pointer transition-all items-center">
                                <div>
                                    <p class="text-sm font-medium dark:text-gray-100 text-gray-900">[ID:
                                        <?php echo $transaction["obratid"]; ?>]
                                        <?php echo $transaction["ucetaktiva"]; ?>
                                    </p>
                                    <p class="text-xs "><?php echo $transaction["obratdatetimereal"]; ?>
                                        [<strong><?php echo $transaction["uctovnykod"]; ?></strong>]</p>
                                </div>
                                <span
                                    class="text-sm p-1 px-2 rounded-lg
                                <?php echo $transaction["md_d"] === 0 ? "text-green-900 bg-green-400 font-bold" : "text-red-600 bg-red-200 font-semibold"; ?>">
                                    <?php echo $transaction["md_d"] === 0 ? "+" : "-"; ?>
                                    <?php echo $transaction["pocet"]; ?>
                                    <?php echo $transaction["mena"]; ?></span>
                            </div>
                        <?php } ?>
                        <?php foreach ($transactionsArchiv as $transaction) { ?>
                            <div
                                class="flex justify-between hover:bg-gray-100 p-2 rounded-lg cursor-pointer transition-all items-center">
                                <div>
                                    <p class="text-sm font-medium dark:text-gray-300 text-gray-900">[ID:
                                        <?php echo $transaction["obratid"]; ?>]
                                        <?php echo $transaction["ucetaktiva"]; ?>
                                    </p>
                                    <p class="text-xs "><?php echo $transaction["obratdatetimereal"]; ?>
                                        [<strong><?php echo $transaction["uctovnykod"]; ?></strong>]</p>
                                </div>
                                <span
                                    class="text-sm p-1 px-2 rounded-lg
                                <?php echo $transaction["md_d"] === 0 ? "text-green-600 bg-green-300 font-bold" : "text-red-600 bg-red-200 font-semibold"; ?>"><?php echo $transaction["md_d"] === 0 ? "+" : "-"; ?>
                                    <?php echo $transaction["pocet"]; ?>
                                    <?php echo $transaction["mena"]; ?></span>
                            </div>
                        <?php } ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Export Options -->
        <div class="bg-white dark:bg-gray-900 dark:text-gray-200 rounded-lg shadow overflow-hidden mb-8">
            <div class="px-6 py-5 border-b border-gray-200">
                <h2 class="text-lg font-medium dark:text-gray-100 text-gray-900 flex items-center">
                    Export Options
                </h2>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <button
                        class="flex items-center justify-center px-4 py-3 border border-gray-300 rounded-md shadow-sm text-sm font-medium bg-white dark:bg-gray-700 dark:hover:bg-gray-600 transition-all hover:bg-gray-50">
                        <i class="fas fa-file-pdf text-red-500 mr-2"></i>
                        Export as PDF
                    </button>
                    <button
                        class="flex items-center justify-center px-4 py-3 border border-gray-300 rounded-md shadow-sm text-sm font-medium bg-white dark:bg-gray-700 dark:hover:bg-gray-600 transition-all hover:bg-gray-50">
                        <i class="fas fa-file-excel text-green-600 mr-2"></i>
                        Export as Excel
                    </button>
                    <button
                        class="flex items-center justify-center px-4 py-3 border border-gray-300 rounded-md shadow-sm text-sm font-medium bg-white dark:bg-gray-700 dark:hover:bg-gray-600 transition-all hover:bg-gray-50">
                        <i class="fas fa-print text-blue-600 mr-2"></i>
                        Print Report
                    </button>
                </div>
            </div>
        </div>
    </div>
</main>

<script>
    let = rowers = document.querySelectorAll(".rower");
    rowers.forEach(function (rower) {
        rower.addEventListener("contextmenu", function (event) {
            event.preventDefault();
            $(".rower").removeClass("bg-gray-300");
            $(".dropdownElement").css("display", "none");
            console.log(event.currentTarget.id);
            let x = event.clientX;
            let y = event.clientY;

            $(rower).addClass("bg-gray-300");

            let contextMenu = document.getElementById("dropdown" + event.currentTarget.id);
            contextMenu.style.display = "block";
            contextMenu.style.position = "fixed";
            contextMenu.style.left = x + "px";
            contextMenu.style.top = y + "px";
        }, false);
    });

    $(document).on("click", (e) => {
        if (!$(e.target).closest('.dropdownElement').length) {
            $(".dropdownElement").css("display", "none");
            $(".rower").removeClass("bg-gray-300");
        }
    });
</script>
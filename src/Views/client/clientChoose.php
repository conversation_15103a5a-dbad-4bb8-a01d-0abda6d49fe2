<?php require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
$klientiQuery = "SELECT p.podielnikid, p.meno, p.prie<PERSON><PERSON><PERSON>, p.kont<PERSON>, p.bu as ucet, po.cislozmluvy as cislozmluvy, po.fondid, pr.refmena, pr.nav * COALESCE(k.kurz, 1) as amount
FROM podielnik p
         INNER JOIN portfolio po ON po.podielnikid = p.podielnikid
        LEFT JOIN pricestore pr ON pr.fondid = po.fondid AND pr.datum = (SELECT max(datum) FROM pricestore WHERE fondid = po.fondid)
        LEFT JOIN kurzyaktivarchiv k ON pr.datum = k.datum AND k.ric = 'EUR'||pr.refmena
WHERE archiv = 'f' AND p.podielnikid > 0
ORDER BY prieznaz ASC
LIMIT 50";
$klienti = Connection::getDataFromDatabase($klientiQuery, defaultDB)[1];

$portfolia = [];
$clients = [];
$clientData = [];
foreach ($klienti as $key => $client) {
    if ($client["fondid"]) {
        $portfolia[$client["podielnikid"]][] = [
            "fondid" => $client["fondid"],
            "cislozmluvy" => $client["cislozmluvy"],
            "amount" => $client["amount"],
            "refmena" => $client["refmena"]
        ];
    }
    if (in_array($client["podielnikid"], $clients)) {
        continue;
    }
    $clients[] = $client["podielnikid"];
    $clientData[] = [
        "podielnikid" => $client["podielnikid"],
        "meno" => $client["meno"],
        "prieznaz" => $client["prieznaz"],
        "kontaktemail" => $client["kontaktemail"],
        "ucet" => $client["ucet"]
    ];
}

if (isset($_SESSION["client"])) {
    include "clientView.php";
} else {
    ?>
    <section class="py-4 px-5 sm:py-5" style="margin-bottom: 8rem">
        <div class="mx-auto max-w-screen-2xl">
            <h2
                class="scroll-m-20 mb-4 pb-2 text-4xl font-bold text-center text-gray-900 dark:text-gray-100 tracking-tight first:mt-0">
                Výber klienta
            </h2>
        </div>
        <div class="flex justify-center px-40 py-4">
            <form class="w-full mx-auto" hx-post="/api/get/clientModeClients" hx-target="#clientResult">
                <label for="default-search"
                    class="mb-2 text-sm font-medium text-gray-900 sr-only dark:text-white">Hľadať</label>
                <div class="relative">
                    <div class="absolute inset-y-0 start-0 flex items-center ps-3 pointer-events-none">
                        <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" aria-hidden="true"
                            xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z" />
                        </svg>
                    </div>
                    <input type="search" id="default-search" name="query"
                        class="block w-full p-4 ps-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                        placeholder="Vyhľadať klienta..." />
                    <button type="submit" id="searchForm"
                        class="text-white absolute end-2.5 bottom-2.5 bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-4 py-2 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">Hľadať</button>
                </div>
            </form>
        </div>
        <section id="clientResult" class="grid grid-cols-1 md:grid-cols-3 gap-6 lg:grid-cols-4 p-8">
            <?php foreach ($clientData as $key => $client) {
                ?>
                <div
                    class="w-full max-w-md p-3 transition-all dark:hover:bg-gray-700 hover:scale-105 cursor-pointer bg-white border border-gray-200 rounded-lg dark:text-gray-100 shadow-sm dark:bg-gray-800 dark:border-gray-700">
                    <div class="flow-root">
                        <ul role="list" class="divide-y divide-gray-200 dark:divide-gray-700">
                            <li class="p-1 flex flex-col justify-between h-full">
                                <section>
                                    <div
                                        class="flex items-center dark:bg-gray-700 bg-gray-200 dark:hover:bg-gray-900 transition-all text-gray-700 dark:text-white cursor-pointer p-2 px-3 rounded-lg">
                                        <div class="shrink-0">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                                stroke-linejoin="round" class="lucide lucide-circle-user">
                                                <circle cx="12" cy="12" r="10" />
                                                <circle cx="12" cy="10" r="3" />
                                                <path d="M7 20.662V19a2 2 0 0 1 2-2h6a2 2 0 0 1 2 2v1.662" />
                                            </svg>
                                        </div>
                                        <div class="flex-1 min-w-0 ms-4">
                                            <p class="text-sm font-bold text-gray-900 truncate dark:text-white">
                                                <?php echo $client["meno"] . " " . $client["prieznaz"]; ?>
                                            </p>
                                            <p class="text-sm text-gray-500 truncate dark:text-gray-400">
                                                <?php echo $client["kontaktemail"] ?>
                                            </p>
                                        </div>
                                    </div>
                                    <div class="flex flex-col gap-2 items-center py-4">
                                        <?php
                                        foreach ($portfolia[$client["podielnikid"]] as $portfolio) {
                                            $cislozmluvy = $portfolio["cislozmluvy"];
                                            $fondid = $portfolio["fondid"];
                                            $podielnikid = $client["podielnikid"];
                                            $meno = $client["meno"];
                                            $prieznaz = $client["prieznaz"];
                                            ?>
                                            <form
                                                onsubmit="runGoToPortfolio(event, '<?php echo $cislozmluvy; ?>', '<?php echo $fondid; ?>', '<?php echo $podielnikid; ?>', '<?php echo $meno; ?>', '<?php echo $prieznaz; ?>', false)"
                                                class="mb-0 w-full">
                                                <input type="hidden" name="fondid" id="fondid"
                                                    value="<?php echo $portfolio["fondid"]; ?>">
                                                <input type="hidden" name="podielnikid" id="podielnikid"
                                                    value="<?php echo $client["podielnikid"]; ?>">
                                                <input type="hidden" name="meno" id="meno" value="<?php echo $client["meno"]; ?>">
                                                <input type="hidden" name="prieznaz" id="prieznaz"
                                                    value="<?php echo $client["prieznaz"]; ?>">
                                                <button type="submit"
                                                    class="flex items-center p-3 w-full text-base font-bold text-gray-900 rounded-lg bg-gray-50 hover:bg-gray-100 group hover:shadow dark:bg-gray-600 dark:hover:bg-gray-500 dark:text-white">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                        stroke-linecap="round" stroke-linejoin="round"
                                                        class="lucide lucide-library-big">
                                                        <rect width="8" height="18" x="3" y="3" rx="1" />
                                                        <path d="M7 3v18" />
                                                        <path
                                                            d="M20.4 18.9c.2.5-.1 1.1-.6 1.3l-1.9.7c-.5.2-1.1-.1-1.3-.6L11.1 5.1c-.2-.5.1-1.1.6-1.3l1.9-.7c.5-.2 1.1.1 1.3.6Z" />
                                                    </svg>
                                                    <span
                                                        class="flex-1 ms-3 whitespace-nowrap"><?php echo $portfolio["cislozmluvy"]; ?></span>
                                                    <span
                                                        class="inline-flex items-center justify-center px-2 py-0.5 ms-3 text-xs text-gray-500 bg-gray-200 font-bold
                                                         rounded-sm dark:bg-gray-700 dark:text-gray-400"><?php echo number_format($portfolio["amount"], 2, '.', ' ') . " " . $portfolio["refmena"] ?></span>
                                                </button>
                                            </form>
                                        <?php } ?>
                                    </div>
                                </section>
                                <div class="w-full group">
                                    <div
                                        class="bg-green-100 w-full text-green-800 flex flex-col gap-2 text-sm rounded-lg font-medium me-2 px-3 items-start py-1 justify-start rounded-sm dark:bg-green-900 dark:text-green-300">
                                        <small>Účet: </small>
                                        <?php if ($client["ucet"] != "") { ?>
                                            <div class="flex justify-between w-full items-center">
                                                <b
                                                    class="text-xs underline hover:no-underline"><?php echo chunk_split(str_replace(" ", "", $client["ucet"]), 4, ' '); ?></b>
                                                <button type="button" id="<?php echo str_replace(" ", "", $client["ucet"]); ?>"
                                                    class="p-1 rounded-md active:scale-90 group-hover:opacity-100 copyBUtton opacity-0 transition-all dark:hover:bg-green-300 dark:hover:text-green-700">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14"
                                                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                        stroke-linecap="round" stroke-linejoin="round"
                                                        class="lucide lucide-clipboard-copy">
                                                        <rect width="8" height="4" x="8" y="2" rx="1" ry="1" />
                                                        <path d="M8 4H6a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-2" />
                                                        <path d="M16 4h2a2 2 0 0 1 2 2v4" />
                                                        <path d="M21 14H11" />
                                                        <path d="m15 10-4 4 4 4" />
                                                    </svg>
                                                </button>
                                            </div>
                                        <?php } else { ?>
                                            <b class="text-xs">Nedefinované</b>
                                        <?php } ?>
                                        </span>
                                    </div>
                            </li>
                        </ul>
                    </div>
                </div>

            <?php } ?>
        </section>
    </section>
    <script>
        $("#default-search").on("keyup", () => {
            let timeout;
            clearTimeout(timeout);
            timeout = setTimeout(() => {
                $("#searchForm").click();
            }, 300);
        });

        $("#default-search").on("change", () => {
            $("#searchForm").click();
        });

        $(".copyBUtton").on("click", function () {
            let id = $(this).attr("id");

            if (!window.isSecureContext) {
                unsecuredCopyToClipboard(id);
            } else {
                navigator.clipboard.writeText(id);
                $(this).addClass("opacity-100");
                setTimeout(() => {
                    $(this).removeClass("opacity-100");
                }, 1000);
            }

        });

        function unsecuredCopyToClipboard(text) {
            const textArea = document.createElement("textarea");
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            try {
                document.execCommand('copy');
            } catch (err) {
                console.error('Unable to copy to clipboard', err);
            }
            document.body.removeChild(textArea);
        }
    </script>
<?php } ?>
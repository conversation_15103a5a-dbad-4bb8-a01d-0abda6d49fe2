<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
require_once "/home/<USER>/www/src/Components/inputs/multiSelect/multiSelect.php";

if (isset($_SESSION["client"])) {
    $fondid = $_SESSION["client"]["fondid"];
} elseif ($_SESSION["mode"]["mode"] === "admin") {
    $fondid = 1;
} else {
    $fondid = 0;
}

$query = "SELECT
		  d.isin as isin,
		  s.stateall as state,
		  s.stateid as stateid 
		from 
			 dbequity d,
			 equityemitent e,
			 state s 
		where
			 e.emitentid = d.emitentid and
			 s.stateid = e.emitentstateid
             ";
$cpcka = Connection::getDataFromDatabase("SELECT 
		 	d.isin, d.isinreal, dc.currencytrade as mena, dc.isincurr, d.cp<PERSON><PERSON><PERSON><PERSON><PERSON>
		 from 
		 	dbequity d, dbequitycurr dc 
		 where
		 	dc.isin = d.isin and
		 	d.eqid IN ('Shares','Fonds')
		 order by d.isinreal, dc.currencytrade", defaultDB)[1];
$states = Connection::getDataFromDatabase($query, defaultDB)[1];
$typyDividend = Connection::getDataFromDatabase("SELECT * FROM dividendaakciatyp", defaultDB)[1];
?>
<div class="mx-auto px-40">
    <div class="bg-white dark:bg-gray-700 rounded-xl shadow-lg p-8">
        <h1 class="text-2xl font-bold text-gray-900 mb-8 dark:text-gray-100 text-center">Splatenie dividendy</h1>
        <form class="space-y-8" id="mainForm" hx-post="/api/akcie/dividendy/get" hx-target="#output">
            <input type="hidden" id="hotovostna" name="hotovostna" value="1" />
            <input type="hidden" name="action" value="splatenie" />
            <div class="flex w-full flex-col gap-2">
                <label for="dividendaTyp" class="text-gray-700 dark:text-gray-100 font-medium whitespace-nowrap">Typ
                    divdendy:</label>
                <select id="dividendaTyp" onchange="getCharakterDividendy(this)" name="dividendaTyp" class="px-3 py-2 border w-full border-gray-300 rounded-md bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-100
                     focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    <?php foreach ($typyDividend as $dividenda) { ?>
                        <option value="<?php echo $dividenda["subkodobratu"]; ?>"><?php echo $dividenda["nazov"]; ?>
                        </option>
                    <?php } ?>
                </select>
            </div>
            <div class="dark:text-gray-800 dark:text-gray-100 rounded-lg p-2">
                <h2 class="text-lg font-semibold mb-4 border-b dark:text-gray-100 border-blue-200 pb-2">Základné
                    informácie
                </h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="flex flex-col gap-2">
                        <label for="isin"
                            class="text-gray-700 dark:text-gray-100 font-medium whitespace-nowrap">CP:</label>
                        <?php echo MultiSelect::render("Vyber cenný papier", $cpcka, "isincurr", ["cpnazskratka", "mena"], "isinreal"); ?>
                        <p id="cpError" class="text-red-500 text-sm hidden">Vyberte aspoň jeden cenný papier.</p>
                    </div>
                    <div>
                        <label for="vs" class="block text-sm font-medium dark:text-gray-100 mb-2">Variabilný
                            symbol</label>
                        <input type="text" id="vs" name="vs"
                            class="w-full px-4 py-2 border border-gray-300 rounded-lg bg-white dark:bg-gray-900 dark:text-gray-100 text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    <div>
                        <label for="ucet" class="block text-sm font-medium dark:text-gray-100 mb-2">Účet</label>
                        <select id="ucet" name="cub" class="w-full px-4 py-3 border border-gray-300 dark:bg-gray-900 rounded-lg bg-white dark:text-gray-100 focus:outline-none
                             focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <?php
                            $ucty = Connection::getDataFromDatabase("SELECT distinct cub, mena from fondsbu order by cub", defaultDB)[1];
                            foreach ($ucty as $key => $item) { ?>
                                <option value="<?php echo $item["cub"]; ?>"><?php echo $item["cub"]; ?>
                                </option>
                            <?php } ?>
                        </select>
                    </div>
                    <div>
                        <label for="kraj-povodu" class="block text-sm font-medium text-gray-700 mb-2">Krajina
                            pôvodu</label>
                        <select id="kraj-povodu" name="kraj-povodu" class="w-full px-4 py-3 border border-gray-300 dark:bg-gray-900 rounded-lg bg-white dark:text-gray-100 focus:outline-none
                             focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <?php foreach ($states as $key => $item) { ?>
                                <option value="<?php echo $item["stateid"]; ?>"><?php echo $item["state"]; ?>
                                </option>
                            <?php } ?>
                        </select>
                    </div>
                </div>
            </div>
            <div class="dark:bg-gray-800 dark:text-gray-100 rounded-lg p-6">
                <h2 class="text-lg font-semibold mb-4 border-b border-gray-200 pb-2">Finančné detaily
                </h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="sadzba_dane" class="block text-sm font-medium mb-2">Sadzba dane</label>
                        <div class="relative">
                            <input type="number" id="sadzba_dane" name="sadzba-dane" onkeyup="danSadzbaChange()"
                                step="0.01" placeholder="0.00%"
                                class="w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg bg-white dark:bg-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <span class="absolute right-3 top-3 text-gray-500">%</span>
                        </div>
                    </div>

                    <div>
                        <label for="dan" class="block text-sm font-medium mb-2">Daň</label>
                        <div class="relative">
                            <input type="number" id="dan" step="0.01" placeholder="0.00" name="dan"
                                class="w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg bg-white dark:bg-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <span class="absolute right-3 top-3 text-gray-500">€</span>
                        </div>
                    </div>

                    <div>
                        <label for="hruby-vynos" class="block text-sm font-medium mb-2">Hurbý výnos</label>
                        <div class="relative">
                            <input type="number" id="hruby-vynos" onkeyup="hrubyVynosChange()" step="0.01"
                                name="hruby-vynos" placeholder="0.00" class="w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg bg-white dark:bg-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2
                                 focus:ring-blue-500 focus:border-transparent">
                            <span class="absolute right-3 top-3 text-gray-500">€</span>
                        </div>
                    </div>

                    <div>
                        <label for="cisty-vynos" class="block text-sm font-medium mb-2">Čistý výnos</label>
                        <div class="relative">
                            <input type="number" id="cisty-vynos" name="cisty-vynos" step="0.01" placeholder="0.00"
                                class="w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg bg-white dark:bg-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 
                                focus:ring-blue-500 focus:border-transparent">
                            <span class="absolute right-3 top-3 text-gray-500">€</span>
                        </div>
                    </div>
                    <div>
                        <label for="vynos-na-kus" class="block text-sm font-medium mb-2">Výnos na
                            kus</label>
                        <div class="relative">
                            <input type="text" id="vynos-na-kus" name="vynos-na-kus" placeholder="0.00" class="w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg bg-white dark:bg-gray-900 dark:text-gray-100 focus:outline-none 
                                focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <span class="absolute right-3 top-3 text-gray-500 font-medium">%</span>
                        </div>
                    </div>

                    <div>
                        <label for="dat-naroku" class="block text-sm font-medium mb-2">Dátum
                            nároku</label>
                        <input type="date" id="dat-naroku" name="datum-naroku" class="w-full px-4 py-3 border border-gray-300 rounded-lg bg-white dark:bg-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 
                            focus:ring-blue-500 focus:border-transparent">
                    </div>
                </div>
            </div>
            <div class="flex justify-center pt-6">
                <button type="submit" id="mainFormSubmitter" class="px-8 py-4 bg-blue-600 text-white font-semibold text-lg rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-4 focus:ring-blue-300 
                    transition-all duration-200 shadow-lg hover:shadow-xl">
                    Potvrdiť
                </button>
            </div>
        </form>
    </div>
</div>
<section id="output">

</section>
<script>
    function getCharakterDividendy(e) {
        var dividendaTyp = e.value;
        $.ajax({
            url: `/src/Controllers/akcie/dividendy/getCharakter.php?typ=${dividendaTyp}`,
            type: "GET",
            dataType: "json",
            success: function (data) {
                console.log(data);
                document.getElementById("hotovostna").value = data.hotovost;
            },
            error: function (xhr, status, error) {
                console.error("Error:", error);
                console.log("Response text:", xhr.responseText);
                alert("Nepodarilo sa aktualizovať!");
            },
        });
    }
    let cpcka = [];
    <?php
    $cnt = 0;
    foreach ($states as $key => $item) { ?>
        cpcka.push({ isin: '<?php echo $item["isin"] ?>', state: '<?php echo $item["state"] ?>', stateid: '<?php echo $item["stateid"] ?>', mena: '<?php echo $item["mena"] ?>' });
    <?php }
    ?>
    function isincurrSelectChange(e) {
        console.log(e.value);
        var isincurr = e.value;
        var isin = isincurr.substr(0, 12);

        if (document.getElementById('hotovostna').value == 1) {
            for (let i = 0; i < cpcka.length; i++) {
                if (isin == cpcka[i].isin) {
                    document.getElementById('kraj-povodu').value = cpcka[i].stateid;
                }
                if (isin === "") {
                    document.getElementById('kraj-povodu').value = "";
                }
                continue;
            }
        }
    }

    function danSadzbaChange() {
        var hrubyVynos = document.getElementById('hruby-vynos').value * 1;
        var danSadzba = (document.getElementById('sadzba_dane').value * 1) / 100;

        var form = document.getElementById('mainForm');
        var dan = hrubyVynos * danSadzba;
        dan = Math.round(dan * 100) / 100;
        var cistyVynos = hrubyVynos - dan;

        document.getElementById('cisty-vynos').value = cistyVynos;
        document.getElementById('dan').value = dan;
    }

    function hrubyVynosChange() {
        var danSadzba = (document.getElementById('sadzba_dane').value * 1) / 100;
        if (danSadzba != "") {
            danSadzbaChange();
        }
    }
</script>
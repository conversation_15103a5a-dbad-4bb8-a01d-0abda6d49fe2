<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
require_once "/home/<USER>/www/src/Components/inputs/multiSelect/multiSelect.php";

if (isset($_SESSION["client"])) {
    $fondid = $_SESSION["client"]["fondid"];
} elseif ($_SESSION["mode"]["mode"] === "admin") {
    $fondid = 1;
} else {
    $fondid = 0;
}

$cpcka = Connection::getDataFromDatabase("SELECT 
		 	d.isin, d.isinreal, dc.currencytrade as mena, dc.isincurr, d.cpnaz<PERSON><PERSON>ka
		 from 
		 	dbequity d, dbequitycurr dc 
		 where
		 	dc.isin = d.isin and
		 	d.eqid = 'Shares'
		 order by d.isinreal, dc.currencytrade", defaultDB)[1];

$typyDividend = Connection::getDataFromDatabase("SELECT * FROM dividendaakciatyp", defaultDB)[1];
$datum_naroku = Connection::getDataFromDatabase("SELECT max(datum) as today FROM today", defaultDB)[1][0]["today"];
$meny = Connection::getDataFromDatabase("SELECT mena FROM menadb ORDER BY poradie", defaultDB)[1];

if ($id_fond > 0) {
    $hotovostna = 1;
} else {
    $hotovostna = 0;
}

//zistenie ci sa jedna o datum z today alebo z majetoktotal
$datumek = Connection::getDataFromDatabase("SELECT (case when max(datum)=to_date('$datum','DD.MM.YYYY') then 1
					 else 0
				end) as today,
				MAX(datum) as datzauctovania
		from today where confirmed='1'", defaultDB)[1][0];

$today = $datumek["today"];
$datuazuctovania = $datumek["datzauctovania"];

//ak sa jedna o splatenie na fonde realne al. fiktivne tak citam z majetoktoday
if (isset($realneSplatenie) and $realneSplatenie != "") {
    $today = 1;
}

if (!$today) {
    //datum nie je z today, citam z majetoktotal stav akcii, jedna sa o natipovanie pohladavok
    $table_qry = " majetoktotal ";
    $where_qry = " 
	 m.datum = to_date('$datum','dd.mm.yyyy') and 
	 uctovnykod in (251200) and ";
} else {

}
?>
<div class="mx-20 mt-10">
    <form id="dividendaForm" class="dark:bg-gray-900 p-6 rounded-lg shadow-sm">
        <div class="flex flex-wrap items-start gap-6 justify-between">
            <div class="flex flex-col gap-2">
                <label for="dividendaTyp" class="text-gray-700 dark:text-gray-100 font-medium whitespace-nowrap">Typ
                    divdendy:</label>
                <select id="dividendaTyp" name="dividendaTyp" class="px-3 py-2 border border-gray-300 rounded-md bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-100
                     focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent max-w-52">
                    <?php foreach ($typyDividend as $dividenda) { ?>
                        <option value="<?php echo $dividenda["subkodobratu"]; ?>"><?php echo $dividenda["nazov"]; ?>
                        </option>
                    <?php } ?>
                </select>
            </div>
            <div class="flex flex-col gap-2">
                <label for="isin" class="text-gray-700 dark:text-gray-100 font-medium whitespace-nowrap">CP:</label>
                <?php echo MultiSelect::render("Vyber cenný papier", $cpcka, "isincurr", ["cpnazskratka", "mena"], "isincurr"); ?>
                <p id="cpError" class="text-red-500 text-sm hidden">Vyberte aspoň jeden cenný papier.</p>
            </div>
            <div class="flex flex-col gap-2">
                <label for="datum" class="text-gray-700 dark:text-gray-100 font-medium whitespace-nowrap">Dátum:</label>
                <input type="date" id="datum" name="datum_naroku" value="<?php echo $datum_naroku; ?>"
                    class="px-3 py-2 border border-gray-300 rounded-md bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                <p id="datumError" class="text-red-500 text-sm hidden">Dátum nesmie byť prázdny</p>
            </div>
            <div class="flex flex-col gap-2">
                <label for="suma" class="text-gray-700 dark:text-gray-100 font-medium whitespace-nowrap">Suma
                    kus</label>
                <input type="text" id="suma" name="suma"
                    class="px-3 py-2 border border-gray-300 rounded-md bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent w-20">
                <p id="sumaError" class="text-red-500 text-sm hidden">Suma nesmie byť prázdna</p>
            </div>
            <div class="flex flex-col gap-2">
                <label for="mena" class="text-gray-700 dark:text-gray-100 font-medium whitespace-nowrap">Mena:</label>
                <select id="mena" name="mena"
                    class="px-3 py-2 border border-gray-300 rounded-md bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    <?php foreach ($meny as $mena) { ?>
                        <option value="<?php echo $mena["mena"]; ?>"><?php echo $mena["mena"]; ?></option>
                    <?php } ?>
                </select>
            </div>
            <input type="hidden" name="hotovostna" value="<?php echo $hotovostna ?>">
            <input type="hidden" name="action" value="natip">
            <input type="hidden" name="id_fond" value="<?php echo $id_fond ?>">
            <input type="hidden" name="datum" value="<?php echo $datum_naroku ?>">
            <button type="submit"
                class="px-6 py-2 bg-blue-600 text-white font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200">
                Zobraziť
            </button>
        </div>
    </form>
</div>
<section id="placeForTable">

</section>
<script>
    $("#dividendaForm").on("submit", (e) => {
        e.preventDefault();
        const formData = new FormData(e.currentTarget);
        const id_fond = formData.get("id_fond");
        const datum = formData.get("datum");
        const dividendaTyp = formData.get("dividendaTyp");
        const isin = formData.get("isincurrvalues");
        const mena = formData.get("mena");
        const datum_naroku = formData.get("datum_naroku");
        const suma = formData.get("suma");
        const action = formData.get("action");
        const hotovostna = formData.get("hotovostna");
        let errors = false;

        if (isin === "") {
            $("#cpError").removeClass("hidden");
            errors = true;
        } else {
            $("#cpError").addClass("hidden");
            errors = false;
        }

        if (suma === "") {
            $("#sumaError").removeClass("hidden");
            errors = true;
        } else {
            $("#sumaError").addClass("hidden");
            errors = false;
        }

        if (datum_naroku === "") {
            $("#datumError").removeClass("hidden");
            errors = true;
        } else {
            $("#datumError").addClass("hidden");
            errors = false;
        }

        if (suma !== "" && datum_naroku !== "" && isin !== "") {
            htmx.ajax('POST', `/api/akcie/dividendy/get`, {
                target: "#placeForTable",
                values: {
                    "id_fond": id_fond,
                    "datum": datum,
                    "dividendaTyp": dividendaTyp,
                    "isin": isin,
                    "mena": mena,
                    "datum_naroku": datum_naroku,
                    "suma": suma,
                    "action": action,
                    "hotovostna": hotovostna
                }
            }).then((response) => {

            });
        }
    });

    document.getElementById("isincurrMultiSelectValues").onchange = function () {
        $("#datumError").addClass("hidden");
    };

    $("#suma").on("keyup", (e) => {
        if (e.target.value) {
            $("#sumaError").addClass("hidden");
        }
    });
</script>
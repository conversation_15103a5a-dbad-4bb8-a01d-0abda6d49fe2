<?php
$heading = "ClickUp Management"; 
?>
<style>
    tbody td {
        padding: 1rem;
        width: min-content;
    }

    .subAppHeading {
        background: rgba(111, 111, 111, 0.28);
        border-radius: 16px;
        box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(7.9px);
        -webkit-backdrop-filter: blur(7.9px);
        border: 1px solid rgba(111, 111, 111, 0.11);
    }

    #toast-status {
        display: none;
        width: 25rem;
        top: 5rem;
        z-index: 99;
        right: 2rem;
        justify-content: space-between;
    }

    #toast-status span {
        font-weight: bold;
        font-size: 18px;
    }
</style>
<body>
<div class="w-full mb-4 px-2 pb-4 border-b border-gray-200 bg-gray-50">
    <h1 class="mb-4 font-extrabold text-gray-900 dark:text-white text-4xl"><span
                class="text-transparent bg-clip-text bg-gradient-to-r to-emerald-600 from-sky-400">ClickUp</span>
        Management.</h1>
    <p class="text-md font-normal text-gray-500 dark:text-gray-400">Aplikácia ClickUp Management služí na
        hromadnú alebo aj jednotlivú aktualizáciu dát v Clickupe podľa aktuálnych dát, ktoré sa nachádzajú v PostgreSQL
        databáze.</p>
</div>
<div class="md:flex">
    <ul class="flex-column space-y space-y-4 text-sm font-medium text-gray-500 dark:text-gray-400 md:me-4 mb-4 md:mb-0">
        <li>
            <a href="" class=""></a>
            <a href="#" content="global"
               class="inline-flex items-center px-4 py-3 text-white bg-blue-700 rounded-lg active w-full tablink"
               aria-current="page">
                <svg class="w-4 h-4 me-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                     fill="none" viewBox="0 0 20 16">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2"/>
                </svg>
                Hromadný update
            </a>
        </li>
        <li>
            <a href="#" content="onetime"
               class="inline-flex items-center px-4 py-3 rounded-lg hover:text-gray-900 bg-gray-50 hover:bg-gray-100 w-full tablink dark:bg-gray-800 dark:hover:bg-gray-700 dark:hover:text-white">
                <svg class="w-4 h-4 me-2" aria-hidden="true"
                     xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 17 10">
                    <path stroke="currentColor" stroke-linecap="round" stroke-width="2"
                          d="M6 1h10M6 5h10M6 9h10M1.49 1h.01m-.01 4h.01m-.01 4h.01"/>
                </svg>
                Aktualizácia jednotlivo
            </a>
        </li>
        <li>
            <a href="#" content="settings"
               class="inline-flex items-center px-4 py-3 rounded-lg hover:text-gray-900 bg-gray-50 hover:bg-gray-100 w-full tablink dark:bg-gray-800 dark:hover:bg-gray-700 dark:hover:text-white">
                <svg class="w-4 h-4 me-2" aria-hidden="true"
                     xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M18 7.5h-.423l-.452-1.09.3-.3a1.5 1.5 0 0 0 0-2.121L16.01 2.575a1.5 1.5 0 0 0-2.121 0l-.3.3-1.089-.452V2A1.5 1.5 0 0 0 11 .5H9A1.5 1.5 0 0 0 7.5 2v.423l-1.09.452-.3-.3a1.5 1.5 0 0 0-2.121 0L2.576 3.99a1.5 1.5 0 0 0 0 2.121l.3.3L2.423 7.5H2A1.5 1.5 0 0 0 .5 9v2A1.5 1.5 0 0 0 2 12.5h.423l.452 1.09-.3.3a1.5 1.5 0 0 0 0 2.121l1.415 1.413a1.5 1.5 0 0 0 2.121 0l.3-.3 1.09.452V18A1.5 1.5 0 0 0 9 19.5h2a1.5 1.5 0 0 0 1.5-1.5v-.423l1.09-.452.3.3a1.5 1.5 0 0 0 2.121 0l1.415-1.414a1.5 1.5 0 0 0 0-2.121l-.3-.3.452-1.09H18a1.5 1.5 0 0 0 1.5-1.5V9A1.5 1.5 0 0 0 18 7.5Zm-8 6a3.5 3.5 0 1 1 0-7 3.5 3.5 0 0 1 0 7Z"/>
                </svg>
                Nastavenia
            </a>
        </li>
    </ul>
    <div id="global"
         class="px-6 bg-gray-50 text-medium text-gray-500 dark:text-gray-400 dark:bg-gray-800 rounded-lg w-full tabka">
        <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-2">Režim hromadnej aktualizácie</h3>
        <p class="mb-2">Tento režím slúži na automatickú aktualizáciu všetkých dát v Clickupe (pozície a klienti).</p>
        <small>Tento proces môže trvať aj hodinu...</small>
        <form id="getTasks" class="my-6 pt-6">
            <button type="submit"
                    class="text-white bg-gradient-to-br px-8 py-8 from-green-400 to-blue-600 hover:bg-gradient-to-bl text-xl focus:ring-4 focus:outline-none focus:ring-green-200 dark:focus:ring-green-800 font-medium rounded-lg text-sm px-5 py-2.5 text-center me-2 mb-2">
                Spustiť aktualizáciu
            </button>
        </form>
    </div>
    <div id="onetime"
         class="px-6 bg-gray-50 text-medium text-gray-500 dark:text-gray-400 dark:bg-gray-800 hidden rounded-lg w-full tabka">
        <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-2">Režim jednotlivej aktualizácie</h3>
        <p class="mb-2">Tento režím slúži na aktualizáciu konkrétných dát v Clickup databáze</p>
        <small>Tento proces trvá približne 3 minúty...</small>
        <form id="getFields" class="my-6">
            <div class="flex mb-4">
                <div class="flex items-center h-5">
                    <input id="helper-checkbox" aria-describedby="helper-checkbox-text" type="checkbox"
                           value="162517514"
                           class="list-check w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                </div>
                <div class="ms-2 text-sm">
                    <label for="helper-checkbox" class="font-medium text-gray-900 dark:text-gray-300">162517514</label>
                    <p id="helper-checkbox-text" class="text-xs font-normal text-gray-500 dark:text-gray-300">Stiahnuť
                        dáta z
                        listu pozícii</p>
                </div>
                <div class="flex ml-6 items-center h-5">
                    <input id="helper-checkbox2" aria-describedby="helper-checkbox-text" type="checkbox"
                           value="156440911"
                           class="list-check w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                </div>
                <div class="ms-2 text-sm">
                    <label for="helper-checkbox2" class="font-medium text-gray-900 dark:text-gray-300">156440911</label>
                    <p id="helper-checkbox-text" class="text-xs font-normal text-gray-500 dark:text-gray-300">Stiahnuť
                        dáta z
                        listu klientov</p>
                </div>
            </div>
            <p id="alert" class="text-red-500 mb-4"></p>
            <button type="submit"
                    class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800">
                Stiahnuť dáta (polia a tasky)
            </button>
        </form>
    </div>
    <div id="settings"
         class="px-6 bg-gray-50 text-medium text-gray-500 dark:text-gray-400 dark:bg-gray-800 hidden rounded-lg w-full tabka">
        <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-2">Nastavenia</h3>
      <p class="mb-2">Rôzne nastavenia aplikácie ClickUp Management</p>
      <button type="button" id="turboupload" class="focus:outline-none text-white bg-red-700 hover:bg-red-800 focus:ring-4 focus:ring-red-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 dark:bg-red-600 dark:hover:bg-red-700 dark:focus:ring-red-900">CLICKUP TURBO UPLOAD</button>
    </div>
</div>
<div id="loading-contianer">
    <div class="loader loader flex justify-center items-center h-80">
        <img src="/src/assets/img/loading-black.gif" alt="sympatia_loading" width="130"/>
    </div>
    <div id="status-load" class="status-load flex flex-col items-center">
        <h4 id="status-step" class="font-bold"></h4>
        <small id="status-current-item"></small>
    </div>
</div>
<div id="confirm-contianer">
    <div class="loader loader flex flex-col justify-center items-center h-[454px]">
        <div class="check-container">
            <div class="check-background">
                <svg viewBox="0 0 65 51" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M7 25L27.3077 44L58.5 7" stroke="white" stroke-width="13" stroke-linecap="round"
                          stroke-linejoin="round"/>
                </svg>
            </div>
            <div class="check-shadow"></div>
        </div>
        <div id="status-load" class="status-load mt-5 flex flex-col items-center">
            <h3 id="confirm-step" class="font-bold"><strong>Skript bol úspešne vykonaný!</strong></h3>
            <small id="confirm-current-item">Potrebný čas na vykonanie skriptu: sám si vypočítaj! ☺</small>
        </div>
    </div>
</div>
<form id="uploadToDB" class="relative">
    <div id="result" class="w-full mb-8" style="display: none" class="mt-8">
        <a href="#taskyHeading"
           class="inline-flex items-center justify-center p-5 text-base font-medium text-gray-500 rounded-lg bg-gray-50 hover:text-gray-900 hover:bg-gray-100 dark:text-gray-400 dark:bg-gray-800 dark:hover:bg-gray-700 dark:hover:text-white">
            <span class="w-full">Tasky</span>
            <svg class="w-4 h-4 ms-2 rtl:rotate-180" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                 viewBox="0 0 14 10">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M1 5h12m0 0L9 1m4 4L9 9"/>
            </svg>
        </a>
        <table class="w-full shadow text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
            <caption class="text-left py-4">Počet nájedných polí: <strong id="counter"></strong></caption>
            <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
            <tr>
                <th scope="col" class="px-6 py-3">
                    Názov
                </th>
                <th scope="col" class="px-6 py-3">
                    Poradie
                </th>
                <th scope="col" class="px-6 py-3">
                    List ID
                </th>
                <th scope="col" class="px-6 py-3">
                    Field ID
                </th>
                <th scope="col" class="px-6 py-3">
                    Options
                </th>
            </tr>
            </thead>
            <tbody id="databaseSelect">

            </tbody>
        </table>
        <table class="w-full shadow text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
            <caption class="text-left py-4">Počet nájedných polí: <strong id="counter"></strong></caption>
            <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
            <tr>
                <th scope="col" class="px-6 py-3">
                    Názov
                </th>
                <th scope="col" class="px-6 py-3">
                    Poradie
                </th>
                <th scope="col" class="px-6 py-3">
                    List ID
                </th>
                <th scope="col" class="px-6 py-3">
                    Field ID
                </th>
                <th scope="col" class="px-6 py-3">
                    Options
                </th>
            </tr>
            </thead>
            <tbody id="resultBody">

            </tbody>
        </table>
        <h2 id="taskyHeading" class="mb-4 text-3xl mt-4 font-bold">Tasky: </h2>
        <table class="w-full shadow text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
            <caption class="text-left py-4">Počet nájedných taskov: <strong id="counterTask"></strong></caption>
            <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
            <tr>
                <th scope="col" class="px-6 py-3">
                    NÁZOV
                </th>
                <th scope="col" class="px-6 py-3">
                    POPIS
                </th>
                <th scope="col" class="px-6 py-3">
                    LIST ID
                </th>
                <th scope="col" class="px-6 py-3">
                    TASK ID
                </th>
                <th scope="col" class="px-6 py-3">
                    CUSTOM FIELDS
                </th>
                <th scope="col" class="px-6 py-3">
                    Akcia
                </th>
            </tr>
            </thead>
            <tbody id="tasksBody">

            </tbody>
        </table>
        <div class="fixed bottom-[2rem]">
            <button type="submit"
                    class="focus:outline-none my-6 text-white bg-green-700 hover:bg-green-800 focus:ring-4 focus:ring-green-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 dark:bg-green-600 dark:hover:bg-green-700 dark:focus:ring-green-800">
                Upload custom fields to database
            </button>
        </div>
    </div>
</form>
<script src="/src/assets/js/clickup/clickupUpdate.ts" type="text/javascript"></script>
</body>

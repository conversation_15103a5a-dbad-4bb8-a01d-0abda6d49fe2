<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
$query = "SELECT od.suma as <PERSON><PERSON>, od.mena as <PERSON><PERSON>, odp.c<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as <PERSON>d, kp.c<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON>, od.datum_prevodu as <PERSON><PERSON>,
        (SELECT	SUM(SIGN(md_d - 0.5) * (-1) * mt.pocet) AS Pocet				
                    FROM majetoktoday mt
                    where
                    mt.uctovnykod = 221110
                    AND mt.subjektid = od.subjektid
                        and mt.mena = od.mena) 
        as AktualnaSuma
        from konfirmaciapp od,
        konfirmaciapp k,
        portfolio odp,
        portfolio kp
        where od.subjektid = odp.fondid
        and k.subjektid = kp.fondid
        and od.druhobchodu = 'prevod'
        and k.druhobchodu = 'prevod'
        and od.dealid_related = k.dealid
        and od.logactivityid in (1,2)";

$dataRes = Connection::getDataFromDatabase($query, defaultDB);
$data = $dataRes[1];
?>
<section style="margin-bottom: 8rem">
    <div class="mx-auto max-w-screen-2xl">
        <h2 class="font-bold my-4 text-2xl text-gray-800 dark:text-gray-100">Nevysporiadané prevody peňažných
            prostriedkov</h2>
        <?php if ($dataRes[0] === 0) { ?>
            <div class="p-4 mb-4 text-sm text-blue-800 rounded-lg bg-blue-50 dark:bg-gray-800 dark:text-blue-400"
                role="alert">
                <span class="font-bold">Informácia!</span> Nenašli sa žiadne údaje
            </div>
        <?php } ?>
        <div class="relative bg-white shadow-md dark:bg-gray-800 sm:rounded-lg">
            <div class="overflow-x-visible">
                <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                        <tr>
                            <th scope="col" class="px-4 py-3">Z portfólia</th>
                            <th scope="col" class="px-4 py-3">Na portfólio</th>
                            <th scope="col" class="px-4 py-3">Suma</th>
                            <th scope="col" class="px-4 py-3">Mena</th>
                            <th scope="col" class="px-4 py-3">Stav na účte</th>
                            <th scope="col" class="px-4 py-3">Zadaný dátum</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        foreach ($data as $item) { ?>
                            <tr
                                class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b dark:border-gray-700">
                                <td class="px-4 py-2">
                                    <span
                                        class="text-sm text-gray-900 font-bold px-2 py-0.5 rounded"><?php echo $item['od'] ?></span>
                                </td>
                                <td class="px-4 py-2">
                                    <span
                                        class="text-sm text-gray-900 font-bold px-2 py-0.5 rounded"><?php echo $item['komu']; ?></span>
                                </td>
                                <td class="px-4 py-2">
                                    <span
                                        class="text-sm text-gray-900 font-bold px-2 py-0.5 rounded"><?php echo $item['komu']; ?></span>
                                </td>
                                <td class="px-4 py-2">
                                    <span
                                        class="text-sm text-gray-900 font-bold px-2 py-0.5 rounded"><?php echo round($item['suma'], 2); ?></span>
                                </td>
                                <td class="px-4 py-2">
                                    <span
                                        class="text-sm text-gray-900 font-bold px-2 py-0.5 rounded"><?php echo $item['mena']; ?></span>
                                </td>
                            </tr>
                            <?php
                        }
                        ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</section>
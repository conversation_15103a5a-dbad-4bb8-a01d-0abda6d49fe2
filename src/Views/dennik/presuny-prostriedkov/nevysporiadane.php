<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
$query = "SELECT od.suma as <PERSON><PERSON>, od.mena as <PERSON><PERSON>, od.ucet as Od, k.ucet as <PERSON><PERSON>, odp.cislozmluvy as Klient
            from konfirmaciapp od,
            konfirmaciapp k,
            portfolio odp,
            portfolio kp
            where od.subjektid = odp.fondid
            and k.subjektid = kp.fondid
            and od.druhobchodu = 'presun'
            and k.druhobchodu = 'presun'
            and od.dealid_related = k.dealid
            and od.logactivityid in (1,2)
            union all
            select od.suma as <PERSON>ma, od.mena as <PERSON>a, od.ucet as Od, k.ucet as <PERSON><PERSON>, 'POOL' as Klient

            from konfirmaciapp od,
            konfirmaciapp k

            where od.druhobchodu = 'presun'
            and k.druhobchodu = 'presun'
            and od.dealid_related = k.dealid
            and od.logactivityid in (1,2)
            and od.subjektid = 0";

$dataRes = Connection::getDataFromDatabase($query, defaultDB);
$data = $dataRes[1];
?>
<section style="margin-bottom: 8rem">
    <div class="mx-auto p-3">
        <h2 class="font-bold my-4 text-2xl dark:text-gray-100 text-gray-800">Nevysporiadané presuny peňažných prostriedkov</h2>
        <?php if ($dataRes[0] === 0) { ?>
            <div class="p-4 mb-4 text-sm text-blue-800 rounded-lg bg-blue-50 dark:bg-gray-800 dark:text-blue-400"
                role="alert">
                <span class="font-bold">Informácia!</span> Nenašli sa žiadne údaje
            </div>
        <?php } ?>
        <div class="relative bg-white shadow-md dark:bg-gray-800 sm:rounded-lg">
            <div class="overflow-x-visible">
                <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                        <tr>
                            <th scope="col" class="px-4 py-3">Portfólio</th>
                            <th scope="col" class="px-4 py-3">Z účtu</th>
                            <th scope="col" class="px-4 py-3">Na účet</th>
                            <th scope="col" class="px-4 py-3">Suma</th>
                            <th scope="col" class="px-4 py-3">Mena</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        foreach ($data as $item) { ?>
                            <tr
                                class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b dark:border-gray-700">
                                <td class="px-4 py-2">
                                    <span
                                        class="text-sm text-gray-900 font-bold px-2 py-0.5 rounded"><?php echo $item['klient'] ?></span>
                                </td>
                                <td class="px-4 py-2">
                                    <span
                                        class="text-sm text-gray-900 font-bold px-2 py-0.5 rounded"><?php echo $item['od']; ?></span>
                                </td>
                                <td class="px-4 py-2">
                                    <span
                                        class="text-sm text-gray-900 font-bold px-2 py-0.5 rounded"><?php echo $item['komu']; ?></span>
                                </td>
                                <td class="px-4 py-2">
                                    <span
                                        class="text-sm text-gray-900 font-bold px-2 py-0.5 rounded"><?php echo round($item['suma'], 2); ?></span>
                                </td>
                                <td class="px-4 py-2">
                                    <span
                                        class="text-sm text-gray-900 font-bold px-2 py-0.5 rounded"><?php echo $item['mena']; ?></span>
                                </td>
                            </tr>
                            <?php
                        }
                        ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</section>
<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
require_once "/home/<USER>/www/src/Components/inputs/multiSelect/multiSelect.php";
$udaje = Connection::getDataFromDatabase("select 
		p.fondid as fondid,
		p.cislozmluvy as cislozmluvy 
		from  
			fonds f, 
			portfolio p 
		where 
			p.fondid = f.fondid and
			f.template = 0 and
			f.archiv = 'f' 
		order by p.cislozmluvy", defaultDB);
$portfolia = $udaje[1];
?>
<section class="p-4">
    <div
        class="w-full mt-4 mb-4 bg-white border border-gray-200 rounded-lg shadow dark:bg-gray-800 dark:border-gray-700">
        <div id="fullWidthTabContent" class="border-t border-gray-200 dark:border-gray-600">
            <div class="p-4 bg-white rounded-lg md:p-8 dark:bg-gray-800" id="hromadne" role="tabpanel"
                aria-labelledby="hromadne-tab">
                <form hx-post="/api/obchodny-dennik/vklady-penaznych-prostriedkov" hx-indicator="#poplatkyReportSpinner"
                    hx-target="#formResult" id="uhradenePoplatkyForm" class="flex flex-col">
                    <section class="flex items-center justify-center px-32 w-full gap-8" style="padding: 0 5vw;">
                        <div class="grid md:grid-cols-3 gap-4 w-full">
                            <div class="w-full">
                                <label for="usporiadanie"
                                    class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Mesiac</label>
                                <select name="usporiadanie"
                                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                                    <option value="kpp.datum_zauctovania DESC" selected="">Od najnovšieho</option>
                                    <option value="kpp.datum_zauctovania ASC">Od najstaršieho</option>
                                    <option value="p.cislozmluvy, kpp.datum_zauctovania ASC">Portfólio</option>
                                    <option value="kpp.mena, kpp.datum_zauctovania ASC">Podľa meny</option>
                                </select>
                            </div>
                            <div class="w-full">
                                <label for="month"
                                    class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Mesiac</label>
                                <select id="month" name="month"
                                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                                    <option selected value="0">Nezáleží</option>
                                    <option value="01">Január</option>
                                    <option value="02">Február</option>
                                    <option value="03">Marec</option>
                                    <option value="04">Apríl</option>
                                    <option value="05">Máj</option>
                                    <option value="06">Jún</option>
                                    <option value="07">Júl</option>
                                    <option value="08">August</option>
                                    <option value="09">September</option>
                                    <option value="10">Október</option>
                                    <option value="11">November</option>
                                    <option value="12">December</option>
                                </select>
                            </div>
                            <div class="w-full">
                                <label for="stvrtrok"
                                    class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Štvrťrok</label>
                                <select id="stvrtrok" name="stvrtrok"
                                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                                    <option selected value="0">Nezáleží</option>
                                    <option value="1">1.</option>
                                    <option value="2">2.</option>
                                    <option value="3">3.</option>
                                    <option value="4">4.</option>
                                </select>
                            </div>
                            <div class="w-full">
                                <label for="rok"
                                    class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Rok</label>
                                <select id="rok" name="rok"
                                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                                    <?php
                                    for ($i = 2003; $i < 2026; $i++) { ?>
                                        <option selected value="<?php echo $i; ?>"><?php echo $i; ?></option>
                                    <?php } ?>
                                </select>
                            </div>
                            <div class="w-full">
                                <label for="zmluva"
                                    class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Typ
                                    zmluvy</label>
                                <select name="zmluva" id="zmluva"
                                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                                    <option value="1::text,2::text,3::text,4::text,5::text" selected="">Všetky</option>
                                    <option value="1::text,2::text,3::text">Riadenie</option>
                                    <option value="4::text">Komisionárske</option>
                                    <option value="5::text">Správa</option>
                                </select>
                            </div>
                        </div>
                        <button type="submit"
                            class="focus:outline-none text-white flex items-center gap-1 bg-green-700 hover:bg-green-800 focus:ring-4 mb-0.5 focus:ring-green-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-green-600 dark:hover:bg-green-700 dark:focus:ring-green-800">
                            Potvrdiť
                            <svg id="akcieSpinner"
                                class="w-5 h-5 text-gray-200 htmx-indicator animate-spin dark:text-gray-600 fill-blue-600"
                                viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                                    fill="currentColor" />
                                <path
                                    d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                                    fill="currentFill" />
                            </svg>
                        </button>
                    </section>
                </form>
            </div>
        </div>
    </div>
    <section id="formResult"></section>
</section>
<div id="pooling-modal" tabindex="-1" aria-hidden="true"
    class="hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full h-[calc(100%-1rem)] max-h-full">
    <div id="modalWrapperInside" class="relative p-4 w-full max-h-full" style="max-width: 95vw;">
        <div
            class="relative rounded-lg shadow dark:bg-gray-700 max-h-full flex flex-col bg-white border-2 border-gray-300 overflow-x-hidden overflow-y-auto">
            <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
                <h3 class="text-xl font-semibold">
                    Pooling
                </h3>
                <section class="inline-flex items-center gap-4 relative z-50">
                    <button type="button" id="minimizeModal"
                        class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-minimize-2">
                            <polyline points="4 14 10 14 10 20" />
                            <polyline points="20 10 14 10 14 4" />
                            <line x1="14" x2="21" y1="10" y2="3" />
                            <line x1="3" x2="10" y1="21" y2="14" />
                        </svg>
                        <span class="sr-only">Minimize modal</span>
                    </button>
                    <button type="button" id="maximizeModal"
                        class="text-gray-400 bg-transparent hidden hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-maximize-2">
                            <polyline points="15 3 21 3 21 9" />
                            <polyline points="9 21 3 21 3 15" />
                            <line x1="21" x2="14" y1="3" y2="10" />
                            <line x1="3" x2="10" y1="21" y2="14" />
                        </svg>
                        <span class="sr-only">Maximize modal</span>
                    </button>
                    <button type="button"
                        class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
                        data-modal-hide="pooling-modal">
                        <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                            viewBox="0 0 14 14">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                        </svg>
                        <span class="sr-only">Close modal</span>
                    </button>
                </section>
            </div>
            <form id="poolDetailForm">
                <div id="modalWrapperko">
                    <div id="loading"
                        class="animate-pulse flex-col text-center gap-6 items-center py-24 justify-center">
                        <div class="flex items-center justify-center" role="status">
                            <svg aria-hidden="true"
                                class="inline w-16 h-16 text-gray-200 animate-spin dark:text-gray-600 fill-blue-600"
                                viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                                    fill="currentColor" />
                                <path
                                    d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                                    fill="currentFill" />
                            </svg>
                            <span class="sr-only">Loading...</span>
                        </div>
                        <h1
                            class="text-xl font-bold leading-tight mt-6 mb-6 tracking-tight text-gray-900 md:text-2xl dark:text-white">
                            Generujem...
                        </h1>
                    </div>
                </div>
                <section class="flex items-center p-4 md:p-5 border-t border-gray-200 rounded-b justify-end">
                    <div class="flex items-center gap-2">
                        <button data-modal-hide="pooling-modal" type="button" class="btn-secondary">Zavrieť</button>
                        <button id="confirmPoolButton" data-modal-hide="pooling-modal" type="submit"
                            class="btn-primary flex gap-3"><span>Potvrdiť pool</span>
                            <svg class="primaryBtnSpinner" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                style="display: none;" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                class="lucide animate-spin lucide-loader-circle">
                                <path d="M21 12a9 9 0 1 1-6.219-8.56" />
                            </svg>
                        </button>
                    </div>
                </section>
            </form>
        </div>
    </div>
</div>
<script src="/src/assets/js/poolingModal.js"></script>
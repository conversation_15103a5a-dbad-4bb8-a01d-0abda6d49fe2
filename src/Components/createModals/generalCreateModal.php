<?php

class generalCreateModal
{
    public static function render(array|null $inputs, string $id, string $heading, string $sequence): void
    {
        if ($inputs !== null) {
            echo self::buildWrapper($inputs, $id, $heading, $sequence);
        }
    }

    public static function buildWrapper(array $inputs, string $id, string $heading, string $sequence): string
    {
        $wrapper = '<div id="createModal" tabindex="-1" aria-hidden="true"
     class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
    <div class="relative p-4 w-full max-w-2xl max-h-full">
        <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
            <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
                <h3 class="text-xl font-semibold text-gray-900 dark:text-white">Pridať ' . $heading . '</h3>
                <button type="button"
                        class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
                        data-modal-hide="createModal">
                    <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                         viewBox="0 0 14 14">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                              stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                    </svg>
                    <span class="sr-only">Close modal</span>
                </button>
            </div>
            <div class="p-4 md:p-5 space-y-4">';
        $wrapper .= self::buildInputs($inputs, $id, $sequence);
        $wrapper .= '            </div>
        </div>
    </div>
</div>';
        return $wrapper;
    }

    public static function buildInputs($inputs, $id, string $sequence): string
    {
        $form = '<form id="' . $id . '" class="createForm" action="">
                    <div id="responseMsg"
                         class="p-4 mb-4 text-sm rounded-lg"
                         style="display: none;" role="alert">
                        <span class="font-semibold"></span>
                    </div>
                    <div class="grid gap-4 mb-4 sm:grid-cols-2">
                    <input type="hidden" class="hiddenOne" name="' . $sequence . '" value="' . $sequence . '">';
        foreach ($inputs as $input) {
            if ($input["type"] !== "hidden") {
                $form .= '<div>';
                $form .= '<label for="' . $input["name"] . '"
                                       class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">' . $input["label"] . '</label>';

                if ($input["type"] === "select") {
                    $form .= '<select class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" name="' . $input["name"] . '" id="">';
                    foreach ($input["options"] as $key=>$option) {
                        if($key === 0){
                            $form .= '<option value="' . $option[$input["optionValue"]] . '" selected="selected">' . $option[$input["optionField"]] . '</option>';
                        } else {
                            $form .= '<option value="' . $option[$input["optionValue"]] . '">' . $option[$input["optionField"]] . '</option>';
                        }   
                    }
                    $form .= '</select></div>';
                } else if ($input["type"] === "multiselect") {
                    require "/home/<USER>/www/src/Components/inputs/multiSelect/multiSelect.php";
                    $form .= '<div class="mt-6">' . MultiSelect::render("Priradiť povolenia pre užívateľa", $input["options"], "povolenia", [$input["optionField"]], $input["optionValue"]) . '</div>';
                } else {
                    $form .= '<input type="' . $input["type"] . '" name="' . $input["name"] . '" id="' . $input["name"] . '"
                                       class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-600 focus:border-blue-600 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                            </div>';
                }
            } else {
                $form .= '<input class="hiddenOne" type="hidden" name="' . $input["name"] . '"/>';
            }
        }
        $form .= '</div>
                    <div class="flex items-center space-x-4">
                        <button type="submit" id="updateButton"
                                class="text-white bg-blue-700 flex items-center gap-3 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                            <span>Vytvoriť</span>
                            <svg aria-hidden="true" id="spinnerUpdate"
                                 class="w-6 h-6 hidden text-gray-200 animate-spin dark:text-gray-600 fill-blue-600"
                                 viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                                      fill="currentColor"/>
                                <path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                                      fill="currentFill"/>
                            </svg>
                        </button>
                        <button type="button" data-modal-hide="createModal"
                                class="text-red-600 inline-flex items-center border border-red-600 hover:bg-red-700 focus:ring-4 focus:outline-none focus:ring-red-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:border-red-500 dark:text-red-500 dark:hover:text-white dark:hover:bg-red-600 dark:focus:ring-red-900">
                            Zrušiť
                        </button>
                    </div>
                </form>';
        return $form;
    }
}
<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

$server_path = $_SERVER["REQUEST_URI"];
if ($_SESSION["mode"]["mode"] === "client" && !isset($_SESSION["client"])) { ?>
    <div class="h-full my-2 rounded-lg dark:text-gray-200 text-gray-600 font-bold flex justify-center items-center px-6">
        <p class="-mt-20">Pre zobrazenie menu najprv zvoľte klienta!</p>
    </div>
<?php } else {
    if (isset($_SESSION["client"])) {
        $fondid = $_SESSION["client"]["fondid"];
    } elseif ($_SESSION["mode"]["mode"] === "admin") {
        $fondid = 1;
    } else {
        $fondid = 0;
    }

    $konfCount = Connection::getDataFromDatabase("SELECT COUNT(*) FROM (select kcp.loguserid                     as userid,
       to_char(kcp.datpok, 'DD.MM.YYYY') as d_datpok,
       e.cpnaz                           as cpnaz,
       e.cpnazskratka                    as cpnazskratka,
       c.currencytrade                   as currencytrade,
       d.cislo                           as cislo,
       kcp.limitkurz                     as limitkurz,
       kcp.limitprice                    as limitprice,
       kcp.kusov                         as kusov,
       kcp.druhobchodu                   as druhobchodu,
       kcp.dealid                        as dealid,
       kcp.eqid                          as eqid,
       CASE
           WHEN kcp.eqid = 'Bonds' THEN 'Dlhopis'
           WHEN kcp.eqid = 'Shares' THEN 'Akcia'
           WHEN kcp.eqid = 'Fonds' THEN 'Fond'
           ELSE 'N/A'
           END                           AS eqid_trans
from konfirmaciacp kcp,
     dbequity e,
     dbequitycurr c,
     dbequitycurrric r,
     equitydruh ed,
     dennikpm d
where ed.druheqid = e.druheqid
  and e.eqid in ('Bonds', 'Shares', 'Fonds')
  and c.isin = e.isin
  and r.isincurr = c.isincurr
  and kcp.dealid = d.dealid
  and kcp.isin = e.isin
  and kcp.currencyidtrade = c.currencytrade
  and kcp.ric = r.ric
  and kcp.subjektid = $fondid
  and kcp.logactivityid in (4, 6, 7, 8, 9)
union all
select k.loguserid                                                             as loguserid,
       to_char(k.dk_td, 'DD.MM.YYYY')                                          as d_datpok,
       k.CUB                                                                   as cpnaz,
       k.CUB                                                                   as cpnazskratka,
       k.MENA                                                                  as currencytrade,
       d.cislo                                                                 as cislo,
       null                                                                    as limitkurz,
       k.sum_td                                                                as limitprice,
       null                                                                    as kusov,
       to_char(k.z_td, 'dd.mm.yyyy') || ' - ' || to_char(k.k_td, 'dd.mm.yyyy') as druhobchodu,
       k.dealid                                                                as dealid,
       'KTV'                                                                   as eqid,
       'KTV'                                                                   as eqid_trans
from konfirmaciaktv k,
     dennikpm d
where k.logactivityid in (4, 6, 7, 8, 9)
  and k.dealid in (select dealid from rezervacia)
  and k.dealid = d.dealid
  and k.subjektid = $fondid
union all
select kk.loguserid                              as loguserid,
       to_char(kk.dat_konfirmacia, 'dd.mm.yyyy') as d_datpok,
       kk.menovypar                              as cpnaz,
       kk.menovypar                              as cpnazskratka,
       kk.menakredit                             as currencytrade,
       d.cislo                                   as cislo,
       null                                      as limitkurz,
       kk.sumakredit                             as limitprice,
       null                                      as kusov,
       'SPOT'                                    as druhobchodu,
       kk.dealid                                 as dealid,
       'Konv'                                    as eqid,
       'Konverzia'                               as eqid_trans
from konverzia kk,
     dennikpm d
where kk.subjektid = $fondid
  and kk.dealid = d.dealid
  and kk.logactivityid in (4, 6, 7, 8, 9)
order by dealid) a", defaultDB)[1][0]["count"];

    $rekonfCount = Connection::getDataFromDatabase("SELECT COUNT(*) AS count
FROM (SELECT DISTINCT kcp.loguserid                     as userid,
                u.username                        as username,
                to_char(kcp.datpok, 'DD.MM.YYYY') AS d_datpok,
                e.cpnaz                           AS cpnaz,
                e.cpnazskratka                    AS cpnazskratka,
                c.currencytrade                   AS currencytrade,
                d.cislo::text                     AS cislo,
                kcp.limitkurz                     AS limitkurz,
                kcp.limitprice                    AS limitprice,
                kcp.kusov                         AS kusov,
                kcp.druhobchodu                   AS druhobchodu,
                kcp.dealid                        AS dealid,
                kcp.eqid                          AS eqid,
                CASE
                    WHEN kcp.eqid = 'Bonds' THEN 'Dlhopis'
                    WHEN kcp.eqid = 'Shares' THEN 'Akcia'
                    WHEN kcp.eqid = 'Fonds' THEN 'Fond'
                    ELSE 'N/A' END                AS eqid_trans
FROM konfirmaciacp kcp
         JOIN dbequity e ON kcp.isin = e.isin
         JOIN dbequitycurr c ON kcp.currencyidtrade = c.currencytrade
         JOIN dbequitycurrric r ON kcp.ric = r.ric
         JOIN equitydruh ed ON ed.druheqid = e.druheqid
         JOIN dennikpm d ON kcp.dealid = d.dealid
         JOIN users u ON kcp.loguserid = u.userid
WHERE e.eqid IN ('Bonds', 'Shares', 'Fonds')
  AND kcp.subjektid = $fondid
  AND kcp.logactivityid = 10
UNION ALL
SELECT k.loguserid                                                             as loguserid,
       u.username                                                              as username,
       to_char(k.dk_td, 'DD.MM.YYYY')                                          AS d_datpok,
       k.CUB                                                                   AS cpnaz,
       k.CUB                                                                   AS cpnazskratka,
       k.MENA                                                                  AS currencytrade,
       d.cislo::text                                                           AS cislo,
       NULL                                                                    AS limitkurz,
       k.sum_td                                                                AS limitprice,
       NULL                                                                    AS kusov,
       to_char(k.z_td, 'DD.MM.YYYY') || ' - ' || to_char(k.k_td, 'DD.MM.YYYY') AS druhobchodu,
       k.dealid                                                                AS dealid,
       'KTV'                                                                   AS eqid,
       'KTV'                                                                   AS eqid_trans
FROM konfirmaciaktv k
         JOIN dennikpm d ON k.dealid = d.dealid
         JOIN users u ON k.loguserid = u.userid
WHERE k.logactivityid = 10
  AND k.dealid IN (SELECT dealid FROM rezervacia)
  AND k.subjektid = $fondid
UNION ALL
SELECT kk.loguserid                                                                                               as loguserid,
       u.username                                                                                                 as username,
       to_char(kk.dat_konfirmacia, 'DD.MM.YYYY')                                                                  AS d_datpok,
       kk.menovypar                                                                                               AS cpnaz,
       kk.menovypar                                                                                               AS cpnazskratka,
       kk.menakredit                                                                                              AS currencytrade,
       d.cislo::text                                                                                              AS cislo,
       NULL                                                                                                       AS limitkurz,
       kk.sumakredit                                                                                              AS limitprice,
       NULL                                                                                                       AS kusov,
       CASE
           WHEN kk.typ_konverzie = 0 THEN 'SPOT'
           WHEN kk.typ_konverzie = 2 THEN 'Forward Delivery'
           ELSE NULL END                                                                                          AS druhobchodu,
       kk.dealid                                                                                                  AS dealid,
       'Konv'                                                                                                     AS eqid,
       'Konverzia'                                                                                                AS eqid_trans
FROM konverzia kk
         JOIN dennikpm d ON kk.dealid = d.dealid
         JOIN users u ON kk.loguserid = u.userid
WHERE kk.subjektid = $fondid
  AND kk.logactivityid = 10
ORDER BY dealid
LIMIT 100) a", defaultDB)[1][0]["count"];
    ?>
    <div class="overflow-y-auto no-scrollbar py-2" style="max-height: 75%">
        <ul class="space-y-2 pt-2">
            <li>
                <button aria-controls="dropdown-sales"
                    class="flex gap-4 items-center topLevelNavigation text-md transition-all p-0.5 px-2 w-full text-base font-medium dark:text-gray-100 rounded-lg transition duration-75 group hover:bg-gray-300 dark:hover:bg-gray-600 dark:text-white dark:hover:bg-gray-700"
                    data-collapse-toggle="dropdown-sales" type="button">
                    <svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                        fill="currentColor" viewBox="0 0 24 24">
                        <path fill-rule="evenodd"
                            d="M8 3a1 1 0 0 1 1-1h6a1 1 0 0 1 1 1h2a2 2 0 0 1 2 2v15a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h2Zm6 1h-4v2H9a1 1 0 0 0 0 2h6a1 1 0 1 0 0-2h-1V4Zm-3 8a1 1 0 0 1 1-1h3a1 1 0 1 1 0 2h-3a1 1 0 0 1-1-1Zm-2-1a1 1 0 1 0 0 2h.01a1 1 0 1 0 0-2H9Zm2 5a1 1 0 0 1 1-1h3a1 1 0 1 1 0 2h-3a1 1 0 0 1-1-1Zm-2-1a1 1 0 1 0 0 2h.01a1 1 0 1 0 0-2H9Z"
                            clip-rule="evenodd" />
                    </svg>
                    <span class="flex-1 text-left whitespace-nowrap">Transakcie</span>
                    <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                        width="24" height="24" fill="none" viewBox="0 0 24 24">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="m8 10 4 4 4-4" />
                    </svg>
                </button>
                <ul class="py-2 flex flex-col gap-1 sub border-l" id="dropdown-sales">
                    <li class="flex flex-wrap">
                        <button aria-controls="dropdown-pages" hx-get="/investicne-zamery" hx-target="#pageContentMain"
                            hx-replace-url="true" hx-push-url="true"
                            onclick="refreshMenu(event); refreshTopBar(event); refreshBreadcrumb(event);"
                            class="flex items-center cursor-pointer text-sm text-base w-full font-medium
                             <?php echo $pathname === "/investicne-zamery" || $server_path === "/investicne-zamery" ? "dark:bg-gray-600 bg-gray-300 shadow-md hover:bg-gray-500 hover:text-white " : "" ?> 
                        dark:text-gray-100 p-1 px-2 rounded-lg transition-all duration-75 group hover:bg-gray-300 dark:hover:bg-gray-600 dark:text-white dark:hover:bg-gray-700" type="button">
                            <span class="flex-1 text-left whitespace-nowrap inline-flex justify-between">Investičné zámery
                                <?php if ($konfCount > 0) { ?>
                                    <span
                                        class="bg-red-100 text-blue-800 text-xs me-2 py-0.5 border border-red-400 rounded-full px-2 font-bold"><?php echo $konfCount; ?></span>
                                <?php } ?></span>
                        </button>
                    </li>
                    <li class="flex flex-wrap">
                        <button aria-controls="dropdown-pages" hx-get="/transakcie" hx-target="#pageContentMain"
                            hx-replace-url="true" hx-push-url="true"
                            onclick="refreshMenu(event); refreshTopBar(event); refreshBreadcrumb(event);"
                            class="flex items-center justify-between text-sm text-base w-full font-medium 
                            <?php echo $pathname === "/transakcie" || $server_path === "/transakcie" ? "dark:bg-gray-600 bg-gray-300 shadow-md hover:bg-gray-500 hover:text-white " : "" ?>
                             dark:text-gray-100 p-1 px-2 rounded-lg transition duration-75 group hover:bg-gray-300 dark:hover:bg-gray-600 dark:text-white dark:hover:bg-gray-700" type="button">
                            <span class="flex-1 text-left whitespace-nowrap inline-flex justify-between">Rekonfirmácia tran.
                                <?php if ($rekonfCount > 0) { ?>
                                    <span
                                        class="bg-blue-100 flex items-center px-1 justify-center text-blue-800 text-[.5rem] me-2 border border-blue-400 rounded-full font-bold"><?php echo $rekonfCount; ?></span>
                                <?php } ?>
                        </button>
                    </li>
                    <li class="flex flex-wrap">
                        <button aria-controls="dropdown-pages" hx-get="/zoznam-transakcii" hx-target="#pageContentMain"
                            hx-replace-url="true" hx-push-url="true"
                            onclick="refreshMenu(event); refreshTopBar(event); refreshBreadcrumb(event);"
                            class="flex items-center justify-between text-sm text-base w-full font-medium 
                            <?php echo $pathname === "/zoznam-transakcii" || $server_path === "/zoznam-transakcii" ? "dark:bg-gray-600 bg-gray-300 shadow-md hover:bg-gray-500 hover:text-white " : "" ?>
                             dark:text-gray-100 p-1 px-2 rounded-lg transition duration-75 group hover:bg-gray-300 dark:hover:bg-gray-600 dark:text-white dark:hover:bg-gray-700" type="button">
                            <span class="flex-1 text-left whitespace-nowrap inline-flex justify-between">Zoznam transakcií
                        </button>
                    </li>
                    <li class="flex flex-wrap">
                        <button aria-controls="dropdown-pages" hx-get="/presun-prostriedkov" hx-target="#pageContentMain"
                            hx-replace-url="true" hx-push-url="true"
                            onclick="refreshMenu(event); refreshTopBar(event); refreshBreadcrumb(event);"
                            class="flex items-center text-sm text-base w-full font-medium
                            <?php echo $pathname === "/presun-prostriedkov" || $server_path === "/presun-prostriedkov" ? "dark:bg-gray-600 bg-gray-300 shadow-md hover:bg-gray-500 hover:text-white " : "" ?> 
                            dark:text-gray-100 p-1 px-2 rounded-lg transition duration-75 group hover:bg-gray-300 dark:hover:bg-gray-600 dark:text-white dark:hover:bg-gray-700" type="button">
                            <span class="flex-1 text-left whitespace-nowrap">Presun prostriedkov</span>
                        </button>
                    </li>
                    <?php if ($_SESSION["mode"]["mode"] === "client") { ?>
                        <li class="flex flex-wrap">
                            <button aria-controls="dropdown-pages" hx-get="/vklad-prostriedkov" hx-target="#pageContentMain"
                                hx-replace-url="true" hx-push-url="true"
                                onclick="refreshMenu(event); refreshTopBar(event); refreshBreadcrumb(event);"
                                class="flex items-center text-sm text-base w-full font-medium
                            <?php echo $pathname === "/vklad-prostriedkov" || $server_path === "/vklad-prostriedkov" ? "dark:bg-gray-600 bg-gray-300 shadow-md hover:bg-gray-500 hover:text-white " : "" ?> 
                            dark:text-gray-100 p-1 px-2 rounded-lg transition duration-75 group hover:bg-gray-300 dark:hover:bg-gray-600 dark:text-white dark:hover:bg-gray-700" type="button">
                                <span class="flex-1 text-left whitespace-nowrap">Vklad prostriedkov</span>
                            </button>
                        </li>
                        <li class="flex flex-wrap">
                            <button aria-controls="dropdown-pages" hx-get="/vyber-prostriedkov" hx-target="#pageContentMain"
                                hx-replace-url="true" hx-push-url="true"
                                onclick="refreshMenu(event); refreshTopBar(event); refreshBreadcrumb(event);"
                                class="flex items-center text-sm text-base w-full font-medium
                            <?php echo $pathname === "/vyber-prostriedkov" || $server_path === "/vyber-prostriedkov" ? "dark:bg-gray-600 bg-gray-300 shadow-md hover:bg-gray-500 hover:text-white " : "" ?> 
                            dark:text-gray-100 p-1 px-2 rounded-lg transition duration-75 group hover:bg-gray-300 dark:hover:bg-gray-600 dark:text-white dark:hover:bg-gray-700" type="button">
                                <span class="flex-1 text-left whitespace-nowrap">Výber prostriedkov</span>
                            </button>
                        </li>
                        <li class="flex flex-wrap">
                            <button aria-controls="dropdown-pages" hx-get="/prevod-prostriedkov" hx-target="#pageContentMain"
                                hx-replace-url="true" hx-push-url="true"
                                onclick="refreshMenu(event); refreshTopBar(event); refreshBreadcrumb(event);"
                                class="flex items-center text-sm text-base w-full font-medium
                            <?php echo $pathname === "/prevod-prostriedkov" || $server_path === "/prevod-prostriedkov" ? "dark:bg-gray-600 bg-gray-300 shadow-md hover:bg-gray-500 hover:text-white " : "" ?> 
                            dark:text-gray-100 p-1 px-2 rounded-lg transition duration-75 group hover:bg-gray-300 dark:hover:bg-gray-600 dark:text-white dark:hover:bg-gray-700" type="button">
                                <span class="flex-1 text-left whitespace-nowrap">Prevod prostriedkov</span>
                            </button>
                        </li>
                    <?php } ?>
                </ul>
            </li>
            <?php if ($_SESSION["mode"]["mode"] != "admin") { ?>
                <li>
                    <button aria-controls="dlhopisky"
                        class="flex gap-4 items-center topLevelNavigation text-md transition-all p-0.5 px-2 w-full text-base font-medium dark:text-gray-100 rounded-lg transition duration-75 group hover:bg-gray-300 dark:hover:bg-gray-600 dark:text-white dark:hover:bg-gray-700"
                        data-collapse-toggle="dlhopisky" type="button">
                        <svg class="w-4 h-4 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                            width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                            <path fill-rule="evenodd"
                                d="M9 7V2.221a2 2 0 0 0-.5.365L4.586 6.5a2 2 0 0 0-.365.5H9Zm2 0V2h7a2 2 0 0 1 2 2v16a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V9h5a2 2 0 0 0 2-2Zm-1 9a1 1 0 1 0-2 0v2a1 1 0 1 0 2 0v-2Zm2-5a1 1 0 0 1 1 1v6a1 1 0 1 1-2 0v-6a1 1 0 0 1 1-1Zm4 4a1 1 0 1 0-2 0v3a1 1 0 1 0 2 0v-3Z"
                                clip-rule="evenodd" />
                        </svg>
                        <span class="flex-1 text-left whitespace-nowrap">Dlhopisy</span>
                        <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                            width="24" height="24" fill="none" viewBox="0 0 24 24">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="m8 10 4 4 4-4" />
                        </svg>
                    </button>
                    <ul class="py-2 sub border-l" id="dlhopisky">
                        <li class="flex flex-wrap">
                            <button aria-controls="kupon-pages"
                                class="flex items-center text-sm text-base w-full font-medium p-0.5 px-2 dark:text-gray-100 rounded-lg transition duration-75 group hover:bg-gray-300 dark:hover:bg-gray-600 dark:text-white dark:hover:bg-gray-700"
                                data-collapse-toggle="kupon-pages" type="button">
                                <span class="flex-1 text-left whitespace-nowrap">Kupón</span>
                                <svg aria-hidden="true" class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path clip-rule="evenodd"
                                        d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                                        fill-rule="evenodd"></path>
                                </svg>
                            </button>
                            <ul class="<?php echo str_contains($pathname, "dlhopisy/kupon") || str_contains($server_path, "dlhopisy/kupon") ? "" : "hidden" ?> py-1 w-full"
                                id="kupon-pages">
                                <li>
                                    <div hx-target="#pageContentMain" hx-get="/dlhopisy/kupon/potvrdenie" hx-replace-url="true"
                                        hx-push-url="true" onclick="refreshMenu(event)"
                                        class="sub2 mb-1 flex <?php echo $pathname === "/dlhopisy/kupon/potvrdenie" || $server_path === "/dlhopisy/kupon/potvrdenie" ? "dark:bg-gray-600 bg-gray-300 shadow-md hover:bg-gray-500 hover:text-white " : "" ?>
                                     items-center text-sm font-medium cursor-pointer dark:text-gray-100 rounded-lg transition duration-75 group hover:bg-gray-300 dark:hover:bg-gray-600">
                                        Potvrdenie
                                    </div>
                                </li>
                                <li>
                                    <div hx-target="#pageContentMain" hx-get="/dlhopisy/kupon/splatenie" hx-replace-url="true"
                                        hx-push-url="true" onclick="refreshMenu(event)"
                                        class="sub2 flex <?php echo $pathname === "/dlhopisy/kupon/splatenie" || $server_path === "/dlhopisy/kupon/splatenie" ? "dark:bg-gray-600 bg-gray-300 shadow-md hover:bg-gray-500 hover:text-white " : "" ?> 
                                    items-center text-sm font-medium cursor-pointer dark:text-gray-100 rounded-lg transition duration-75 group hover:bg-gray-300 dark:hover:bg-gray-600">
                                        Splatenie
                                    </div>
                                </li>
                            </ul>
                        </li>
                        <li class="flex flex-wrap">
                            <button aria-controls="istina-pages"
                                class="flex items-center text-sm text-base w-full font-medium p-0.5 px-2 dark:text-gray-100 rounded-lg transition duration-75 group hover:bg-gray-300 dark:hover:bg-gray-600 dark:text-white dark:hover:bg-gray-700"
                                data-collapse-toggle="istina-pages" type="button">
                                <span class="flex-1 text-left whitespace-nowrap">Istina</span>
                                <svg aria-hidden="true" class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path clip-rule="evenodd"
                                        d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                                        fill-rule="evenodd"></path>
                                </svg>
                            </button>
                            <ul class="<?php echo str_contains($pathname, "dlhopisy/istina") || str_contains($server_path, "dlhopisy/istina") ? "" : "hidden" ?> py-1 w-full"
                                id="istina-pages">
                                <li>
                                    <div hx-target="#pageContentMain" hx-get="/dlhopisy/istina/potvrdenie" hx-replace-url="true"
                                        onclick="refreshMenu(event)"
                                        class="sub2 mb-1 flex <?php echo $pathname === "/dlhopisy/istina/potvrdenie" || $server_path === "/dlhopisy/istina/potvrdenie" ? "dark:bg-gray-600 bg-gray-300 shadow-md hover:bg-gray-500 hover:text-white " : "" ?> items-center text-sm font-medium cursor-pointer dark:text-gray-100 rounded-lg transition duration-75 group hover:bg-gray-300 dark:hover:bg-gray-600">
                                        Potvrdenie
                                    </div>
                                </li>
                                <li>
                                    <div hx-target="#pageContentMain" hx-get="/dlhopisy/istina/splatenie" hx-replace-url="true"
                                        onclick="refreshMenu(event)"
                                        class="sub2 flex <?php echo $pathname === "/dlhopisy/istina/splatenie" || $server_path === "/dlhopisy/istina/splatenie" ? "dark:bg-gray-600 bg-gray-300 shadow-md hover:bg-gray-500 hover:text-white " : "" ?> items-center text-sm font-medium cursor-pointer dark:text-gray-100 rounded-lg transition duration-75 group hover:bg-gray-300 dark:hover:bg-gray-600">
                                        Splatenie
                                    </div>
                                </li>
                            </ul>
                        </li>
                    </ul>
                </li>
            <?php } ?>
            <li>
                <button aria-controls="akcies"
                    class="flex gap-4 items-center topLevelNavigation text-md transition-all p-0.5 px-2 w-full text-base font-medium dark:text-gray-100 rounded-lg transition duration-75 group hover:bg-gray-300 dark:hover:bg-gray-600 dark:text-white dark:hover:bg-gray-700"
                    data-collapse-toggle="akcies" type="button">
                    <svg class="w-4 h-4 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                        width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                        <path fill-rule="evenodd"
                            d="M9 7V2.221a2 2 0 0 0-.5.365L4.586 6.5a2 2 0 0 0-.365.5H9Zm2 0V2h7a2 2 0 0 1 2 2v16a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V9h5a2 2 0 0 0 2-2Zm-1 9a1 1 0 1 0-2 0v2a1 1 0 1 0 2 0v-2Zm2-5a1 1 0 0 1 1 1v6a1 1 0 1 1-2 0v-6a1 1 0 0 1 1-1Zm4 4a1 1 0 1 0-2 0v3a1 1 0 1 0 2 0v-3Z"
                            clip-rule="evenodd" />
                    </svg>
                    <span class="flex-1 text-left whitespace-nowrap">Akcie</span>
                    <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                        width="24" height="24" fill="none" viewBox="0 0 24 24">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="m8 10 4 4 4-4" />
                    </svg>
                </button>
                <ul class="py-2 sub border-l" id="akcies">
                    <li class="flex flex-wrap">
                        <button aria-controls="dropdown-pages" hx-get="/akcie/dividendy/natipovanie"
                            hx-target="#pageContentMain" hx-replace-url="true" hx-push-url="true"
                            onclick="refreshMenu(event)"
                            class="flex items-center cursor-pointer text-sm text-base w-full font-medium
                             <?php echo $pathname === "/akcie/dividendy/natipovanie" || $server_path === "/akcie/dividendy/natipovanie" ? "dark:bg-gray-600 bg-gray-300 shadow-md hover:bg-gray-500 hover:text-white " : "" ?> 
                        dark:text-gray-100 p-1 px-2 rounded-lg transition-all duration-75 group hover:bg-gray-300 dark:hover:bg-gray-600 dark:text-white dark:hover:bg-gray-700"
                            type="button">
                            <span class="flex-1 text-left whitespace-nowrap inline-flex justify-between">Natipovanie
                                dividendy</span>
                        </button>
                    </li>
                    <li class="flex flex-wrap">
                        <button aria-controls="dropdown-pages" hx-get="/akcie/dividendy/splatenie"
                            hx-target="#pageContentMain" hx-replace-url="true" hx-push-url="true"
                            onclick="refreshMenu(event)"
                            class="flex items-center cursor-pointer text-sm text-base w-full font-medium
                             <?php echo $pathname === "/akcie/dividendy/splatenie" || $server_path === "/akcie/dividendy/splatenie" ? "dark:bg-gray-600 bg-gray-300 shadow-md hover:bg-gray-500 hover:text-white " : "" ?> 
                        dark:text-gray-100 p-1 px-2 rounded-lg transition-all duration-75 group hover:bg-gray-300 dark:hover:bg-gray-600 dark:text-white dark:hover:bg-gray-700"
                            type="button">
                            <span class="flex-1 text-left whitespace-nowrap inline-flex justify-between">Splatenie
                                dividendy</span>
                        </button>
                    </li>
                </ul>
            </li>
            <li>
                <div hx-target="#pageContentMain" hx-get="/terminovany-vklad/potvrdenie" hx-replace-url="true"
                    hx-push-url="true" onclick="refreshMenu(event)"
                    class="flex gap-3 items-center cursor-pointer topLevelNavigation transition-all text-md p-0.5 px-2 text-base font-medium dark:text-gray-100 rounded-lg dark:text-white 
                    <?php echo $pathname === "/terminovany-vklad/potvrdenie" || $server_path === "/terminovany-vklad/potvrdenie" ? "dark:bg-gray-600 bg-gray-300 shadow-md hover:bg-gray-500 hover:text-white " : "" ?> group">
                    <svg class="w-4 h-4 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                        width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                        <path fill-rule="evenodd"
                            d="M9 7V2.221a2 2 0 0 0-.5.365L4.586 6.5a2 2 0 0 0-.365.5H9Zm2 0V2h7a2 2 0 0 1 2 2v16a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2v-5h7.586l-.293.293a1 1 0 0 0 1.414 1.414l2-2a1 1 0 0 0 0-1.414l-2-2a1 1 0 0 0-1.414 1.414l.293.293H4V9h5a2 2 0 0 0 2-2Z"
                            clip-rule="evenodd" />
                    </svg>
                    <span class="flex-1 whitespace-nowrap">Terminovaný vklad</span>
                </div>
            </li>
            <li>
                <button aria-controls="vysporiadanie"
                    class="flex gap-4 items-center topLevelNavigation text-md transition-all p-0.5 px-2 w-full text-base font-medium dark:text-gray-100 rounded-lg transition duration-75 group hover:bg-gray-300 dark:hover:bg-gray-600 dark:text-white dark:hover:bg-gray-700"
                    type="button">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="lucide lucide-landmark">
                        <line x1="3" x2="21" y1="22" y2="22" />
                        <line x1="6" x2="6" y1="18" y2="11" />
                        <line x1="10" x2="10" y1="18" y2="11" />
                        <line x1="14" x2="14" y1="18" y2="11" />
                        <line x1="18" x2="18" y1="18" y2="11" />
                        <polygon points="12 2 20 7 4 7" />
                    </svg>
                    <span class="flex-1 text-left font-bold whitespace-nowrap">Vysporiadanie</span>
                </button>
                <ul class="py-2 sub border-l" id="vysporiadanie">
                    <li class="flex ml-2 flex-wrap">
                        <small class="text-gray-500 font-medium">Peňažné</small>
                        <ul class="py-1 w-full" id="penaznee">
                            <li>
                                <div hx-target="#pageContentMain" hx-get="/vysporiadanie/penazne/odchazajuce/natipovanie"
                                    hx-replace-url="true" hx-push-url="true" onclick="refreshMenu(event)"
                                    class="flex <?php echo $pathname === "/vysporiadanie/penazne/odchazajuce/natipovanie" || $server_path === "/vysporiadanie/penazne/odchazajuce/natipovanie" ? "dark:bg-gray-600 bg-gray-300 shadow-md hover:bg-gray-500 hover:text-white " : "" ?>
                                     gap-2 p-1 px-2 items-center text-sm font-medium cursor-pointer dark:text-gray-100 rounded-lg transition duration-75 group hover:bg-gray-300 dark:hover:bg-gray-600">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-arrow-right-from-line">
                                        <path d="M3 5v14" />
                                        <path d="M21 12H7" />
                                        <path d="m15 18 6-6-6-6" />
                                    </svg>
                                    <span>Odchadzajúce</span>
                                </div>
                            </li>
                            <li>
                                <div data-collapse-toggle="sparovanie" type="button"
                                    class="flex pr-1 p-1 px-2 items-center text-sm justify-between font-medium cursor-pointer dark:text-gray-100 rounded-lg transition duration-75 group hover:bg-gray-300 dark:hover:bg-gray-600">
                                    <section class="flex items-center gap-2">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24"
                                            fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                            stroke-linejoin="round" class="lucide rotate-180 lucide-arrow-right-from-line">
                                            <path d="M3 5v14" />
                                            <path d="M21 12H7" />
                                            <path d="m15 18 6-6-6-6" />
                                        </svg>
                                        <span>Prichadzajúce</span>
                                    </section>
                                    <svg aria-hidden="true" class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path clip-rule="evenodd"
                                            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                                            fill-rule="evenodd"></path>
                                    </svg>
                                </div>
                                <ul class="<?php echo str_contains($pathname, "/vysporiadanie/penazne/prichadzajuce") || str_contains($server_path, "/vysporiadanie/penazne/prichadzajuce") ? "" : "hidden" ?>
                                 py-1 w-full" id="sparovanie">
                                    <li>
                                        <div hx-target="#pageContentMain"
                                            hx-get="/vysporiadanie/penazne/prichadzajuce/natipovanie" hx-replace-url="true"
                                            onclick="refreshMenu(event)"
                                            class="sub flex <?php echo $pathname === "/vysporiadanie/penazne/prichadzajuce/natipovanie" || $server_path === "/vysporiadanie/penazne/prichadzajuce/natipovanie" ? "dark:bg-gray-600 bg-gray-300 shadow-md hover:bg-gray-500 hover:text-white " : "" ?>
                                             items-center text-sm font-medium cursor-pointer dark:text-gray-100 rounded-lg transition duration-75 group hover:bg-gray-300 dark:hover:bg-gray-600">
                                            Natipovanie
                                        </div>
                                    </li>
                                    <li>
                                        <div hx-target="#pageContentMain"
                                            hx-get="/vysporiadanie/penazne/prichadzajuce/sparovanie" hx-replace-url="true"
                                            onclick="refreshMenu(event)"
                                            class="sub flex <?php echo $pathname === "/vysporiadanie/penazne/prichadzajuce/sparovanie" || $server_path === "/vysporiadanie/penazne/prichadzajuce/sparovanie" ? "dark:bg-gray-600 bg-gray-300 shadow-md hover:bg-gray-500 hover:text-white " : "" ?>
                                             items-center text-sm font-medium cursor-pointer dark:text-gray-100 rounded-lg transition duration-75 group hover:bg-gray-300 dark:hover:bg-gray-600">
                                            Spárovanie
                                        </div>
                                    </li>
                                </ul>
                            </li>
                        </ul>
                    </li>
                    <li class="flex ml-2 flex-wrap">
                        <small class="text-gray-500 font-medium">Majetkové</small>
                        <ul class="py-1 w-full" id="majetkovee">
                            <li>
                                <div hx-target="#pageContentMain" hx-get="/vysporiadanie/majetkove/odchadzajuce"
                                    hx-replace-url="true" onclick="refreshMenu(event)" hx-push-url="true"
                                    class="<?php echo $pathname === "/vysporiadanie/majetkove/odchadzajuce" || $server_path === "/vysporiadanie/majetkove/odchadzajuce" ? "dark:bg-gray-600 bg-gray-300 shadow-md hover:bg-gray-500 hover:text-white " : "" ?>
                                    flex mb-1 gap-2 p-1 px-2 items-center text-sm font-medium cursor-pointer dark:text-gray-100 rounded-lg transition duration-75 group hover:bg-gray-300 dark:hover:bg-gray-600">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-arrow-right-from-line">
                                        <path d="M3 5v14" />
                                        <path d="M21 12H7" />
                                        <path d="m15 18 6-6-6-6" />
                                    </svg>
                                    <span>Odchadzajúce</span>
                                </div>
                            </li>
                            <li>
                                <div hx-target="#pageContentMain" hx-get="/vysporiadanie/majetkove/prichadzajuce"
                                    hx-replace-url="true" onclick="refreshMenu(event)" hx-push-url="true"
                                    class="<?php echo $pathname === "/vysporiadanie/majetkove/prichadzajuce" || $server_path === "/vysporiadanie/majetkove/prichadzajuce" ? "dark:bg-gray-600 bg-gray-300 shadow-md hover:bg-gray-500 hover:text-white " : "" ?>
                                    flex gap-2 p-1 px-2 items-center text-sm font-medium cursor-pointer dark:text-gray-100 rounded-lg transition duration-75 group hover:bg-gray-300 dark:hover:bg-gray-600">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide rotate-180 lucide-arrow-right-from-line">
                                        <path d="M3 5v14" />
                                        <path d="M21 12H7" />
                                        <path d="m15 18 6-6-6-6" />
                                    </svg>
                                    <span>Prichadzajúce</span>
                                </div>
                            </li>
                        </ul>
                    </li>
                </ul>
            </li>
            <?php if ($_SESSION["mode"]["mode"] != "admin") { ?>
                <li>
                    <div hx-target="#pageContentMain" hx-get="/poplatky" hx-replace-url="true" hx-push-url="true"
                        onclick="refreshMenu(event)"
                        class="<?php echo $pathname === "/poplatky" || $server_path === "/poplatky" ? "dark:bg-gray-600 bg-gray-300 shadow-md hover:bg-gray-500 hover:text-white " : "" ?>
                    flex gap-3 items-center cursor-pointer topLevelNavigation transition-all text-md p-0.5 px-2 text-base font-medium dark:text-gray-100 rounded-lg dark:text-white hover:bg-gray-300 dark:hover:bg-gray-600 dark:hover:bg-gray-700 group">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-coins">
                            <circle cx="8" cy="8" r="6" />
                            <path d="M18.09 10.37A6 6 0 1 1 10.34 18" />
                            <path d="M7 6h1v4" />
                            <path d="m16.71 13.88.7.71-2.82 2.82" />
                        </svg>
                        <span class="flex-1 whitespace-nowrap">Poplatky</span>
                    </div>
                </li>
            <?php } ?>

        </ul>
    </div>
<?php } ?>
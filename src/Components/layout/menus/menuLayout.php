<?php
require_once("/home/<USER>/www/conf/settings.php");
require_once("/home/<USER>/www/src/lib/connection.php");
$pathname = $_POST["path"];
?>
<div aria-label="Sidenav" hx-get="/refreshMenu" hx-target="#drawer-navigation" hx-trigger="refresh-menu"
    class="w-60 top-0 h-screen fixed p-3 z-0 transition-transform md:translate-x-0 bg-white dark:bg-gray-800"
    id="drawer-navigation">
    <div hx-target="#pageContentMain" hx-get="/" hx-replace-url="true" hx-push-url="true"
        onclick="refreshAllUIComponents(event);"
        class="flex cursor-pointer relative bg-white p-6 p-1 rounded-lg hover:bg-gray-200 dark:bg-gray-500 dark:hover:bg-gray-600 transition-all justify-between">
        <img src="/src/assets/img/logo.svg" class="mr-3 h-7" alt="Sympatia Logo" />
        <small class="self-center absolute font-bold whitespace-nowrap dark:text-white"
            style="bottom: 10px; right: 30px">Asset Manager</small>
    </div>
    <div class="px-6">
        <hr class="border-black dark:border-gray-600" />
    </div>
    <?php include "/home/<USER>/www/src/Components/layout/menus/" . (isset($_SESSION["mode"]) ? $_SESSION["mode"]["mode"] : "default") . ".php"; ?>
    <div class="px-4 w-full h-40">
        <div class="w-full bg-gray-300 dark:bg-gray-600 rounded-md"></div>
    </div>
    <div class="absolute bottom-0 left-0 justify-center p-4 w-full lg:flex flex-col z-20">
        <div id="navigationBottomMenu" class="bottom-0 left-0 justify-between py-4 px-1 w-full lg:flex z-20">

            <a class="inline-flex justify-center p-2 text-gray-500 rounded cursor-pointer dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-300 dark:hover:bg-gray-600"
                data-tooltip-target="preference-settings" href="#">
                <svg aria-hidden="true" class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M5 4a1 1 0 00-2 0v7.268a2 2 0 000 3.464V16a1 1 0 102 0v-1.268a2 2 0 000-3.464V4zM11 4a1 1 0 10-2 0v1.268a2 2 0 000 3.464V16a1 1 0 102 0V8.732a2 2 0 000-3.464V4zM16 3a1 1 0 011 1v7.268a2 2 0 010 3.464V16a1 1 0 11-2 0v-1.268a2 2 0 010-3.464V4a1 1 0 011-1z">
                    </path>
                </svg>
            </a>
            <div class="inline-block absolute invisible z-10 py-2 px-3 text-sm font-medium text-white bg-gray-900 rounded-lg shadow-sm opacity-0 transition-opacity duration-300 tooltip"
                id="preference-settings" role="tooltip">
                Preferencie
                <div class="tooltip-arrow" data-popper-arrow></div>
            </div>
            <div hx-get="/nastavenia" hx-target="#pageContentMain" hx-replace-url="true"
                class="inline-flex justify-center p-2 text-gray-500 rounded cursor-pointer dark:text-gray-400 dark:hover:text-white hover:text-gray-900 hover:bg-gray-300 dark:hover:bg-gray-600"
                data-tooltip-target="tooltip-settings">
                <svg aria-hidden="true" class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg">
                    <path clip-rule="evenodd"
                        d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z"
                        fill-rule="evenodd"></path>
                </svg>
            </div>
            <div class="inline-block absolute invisible z-10 py-2 px-3 text-sm font-medium text-white bg-gray-900 rounded-lg shadow-sm opacity-0 transition-opacity duration-300 tooltip"
                id="tooltip-settings" role="tooltip">
                Systémové nastavenia
                <div class="tooltip-arrow" data-popper-arrow></div>
            </div>
            <div hx-get="/aktivita" hx-target="#pageContentMain" hx-replace-url="true"
                class="inline-flex justify-center p-2 text-gray-500 rounded cursor-pointer dark:text-gray-400 dark:hover:text-white hover:text-gray-900 hover:bg-gray-300 dark:hover:bg-gray-600"
                data-tooltip-target="tooltip-aktivita">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide lucide-square-chart-gantt">
                    <rect width="18" height="18" x="3" y="3" rx="2" />
                    <path d="M9 8h7" />
                    <path d="M8 12h6" />
                    <path d="M11 16h5" />
                </svg>
            </div>
            <div class="inline-block absolute invisible z-10 py-2 px-3 text-sm font-medium text-white bg-gray-900 rounded-lg shadow-sm opacity-0 transition-opacity duration-300 tooltip"
                id="tooltip-aktivita" role="tooltip">
                Aktivita
                <div class="tooltip-arrow" data-popper-arrow></div>
            </div>
        </div>
        <div class="dark:text-gray-200 dark:bg-gray-900 p-2 rounded-lg">
            <p class="text-sm">Evidenčný dátum:
                <?php echo Connection::getDataFromDatabase("SELECT TO_CHAR(max(datum), 'DD.MM.YYYY') as datum FROM today", defaultDB)[1][0]["datum"]; ?>
            </p>
        </div>
    </div>
</div>
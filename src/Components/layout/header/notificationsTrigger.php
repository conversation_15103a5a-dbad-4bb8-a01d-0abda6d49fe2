<?php
require_once("/home/<USER>/www/conf/settings.php");
require_once("/home/<USER>/www/src/lib/connection.php");

$userID = $_SESSION["user"]["data"]["userid"];

$notificationQuery = "SELECT count(n.id) as count
FROM notifications n
     JOIN users u ON u.last_readed <= n.datetimeactivity AND u.userid = $userID
     LEFT JOIN users us ON us.userid = n.mentioneduserid
     LEFT JOIN users ui ON ui.userid = n.userid
        WHERE n.type != 'helper'
     ";

$notificationCount = Connection::getDataFromDatabase($notificationQuery, defaultDB)[1][0]["count"];
?>

<button id="toggleNotifications" onclick="openNotifications(event)"
    class="p-2 mr-1 dark:text-gray-100 text-gray-500 relative rounded-lg cursor-pointer hover:text-gray-900 dark:hover:bg-gray-400 hover:bg-gray-200 transition-all focus:ring-4 focus:ring-gray-300"
    type="button">
    <span class="sr-only">View notifications</span>
    <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor"
        stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-bell">
        <path d="M10.268 21a2 2 0 0 0 3.464 0" />
        <path
            d="M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326" />
    </svg>
    <?php if ($notificationCount !== 0) { ?>
        <span
            id="notification-dot"><?php echo ($notificationCount + $mentions[0]) >= 50 ? "50+" : ($notificationCount + $mentions[0]); ?></span>
    <?php } ?>
</button>
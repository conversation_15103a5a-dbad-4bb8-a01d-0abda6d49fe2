<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
require_once "/home/<USER>/www/src/lib/notifications/renderNotification.class.php";
require_once "/home/<USER>/www/src/lib/notifications/renderMention.class.php";
$userid = $_SESSION["user"]["data"]["userid"];
$username = $_SESSION["user"]["data"]["username"];

$user = Connection::getDataFromDatabase("SELECT * FROM users WHERE userid = $userid", defaultDB)[1][0];
?>
<div class="overflow-hidden z-50 my-4 w-96 bg-clip-padding backdrop-filter backdrop-blur-md bg-opacity-10 dark:bg-gray-400/30 bg-white/60 text-base list-none rounded divide-y divide-gray-100 shadow-xl dark:divide-gray-600 rounded-xl"
    id="usersidebar-dropdown"
    style="position: absolute; margin: 0px; right: 1rem; top: 1rem; z-index: 50;max-height: 96vh;min-height: 96vh; height: 96vh;">
    <div class="block py-2 pb-1 px-4 font-bold text-xl text-left text-gray-900">
        <div class="flex justify-between dark:text-gray-50 text-gray-900">
            <span>Uživateľský profil</span>
            <section class="inline-flex gap-2 items-center">
                <div id="userSettingsButton" hx-get="/profil?edit=true" hx-target="#pageContentMain"
                    hx-replace-url="true"
                    class="p-1 rounded-md dark:hover:bg-gray-500 hover:bg-gray-200 transition-all cursor-pointer">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="lucide lucide-settings-2">
                        <path d="M20 7h-9" />
                        <path d="M14 17H5" />
                        <circle cx="17" cy="17" r="3" />
                        <circle cx="7" cy="7" r="3" />
                    </svg>
                </div>
                <div id="closeUserDropdown"
                    class="p-1 rounded-md dark:hover:bg-gray-500 hover:bg-gray-200 transition-all cursor-pointer">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="lucide lucide-x">
                        <path d="M18 6 6 18" />
                        <path d="m6 6 12 12" />
                    </svg>
                </div>
            </section>
        </div>
    </div>
    <section id="profileWrapper" style="height: 88.5vh;" class="overflow-y-auto p-4 pb-0">
        <div id="sidebar">
            <div class="flex flex-col h-full">
                <!-- Modern User Profile Header -->
                <div
                    class="relative p-3 bg-gradient-to-br from-primary-500 via-primary-600 to-primary-700 text-white overflow-hidden">
                    <div class="relative z-10">
                        <div hx-get="/pouzivatelia/detail/<?php echo $userid; ?>" hx-target="#pageContentMain"
                            hx-push-url="true" hx-replace-url="true"
                            class="flex items-center space-x-4 mb-8 dark:hover:bg-slate-400 transition-all cursor-pointer p-2 rounded-lg">
                            <div class="relative">
                                <div
                                    class="h-16 w-16 border-4 border-white/20 rounded-full text-white text-xl font-bold flex items-center justify-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round"
                                        class="lucide lucide-circle-user-round-icon lucide-circle-user-round">
                                        <path d="M18 20a6 6 0 0 0-12 0" />
                                        <circle cx="12" cy="10" r="4" />
                                        <circle cx="12" cy="12" r="10" />
                                    </svg>
                                </div>
                                <div
                                    class="absolute -bottom-1 -right-1 w-6 h-6 bg-green-400 rounded-full border-2 border-white flex items-center justify-center">
                                    <div class="w-2 h-2  rounded-full"></div>
                                </div>
                            </div>
                            <div>
                                <h3 class="text-xl font-bold"><?php echo $username; ?></h3>
                                <p class="text-gray-300 text-sm">@<?php echo $user["usernick"]; ?></p>
                            </div>
                        </div>
                        <!-- Status Info -->
                        <div class="mt-4 flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="h-4 w-4 text-primary-100">
                                    <circle cx="12" cy="12" r="10"></circle>
                                    <polyline points="12,6 12,12 16,14"></polyline>
                                </svg>
                                <span class="text-sm text-primary-100">Last login:
                                    <?php echo $user["lasttime"]; ?></span>
                            </div>
                            <span
                                class="bg-green-400 text-green-900 text-xs px-2.5 py-0.5 rounded-full font-medium">Admin</span>
                        </div>
                    </div>
                </div>
                <hr />
                <div class="flex-1 overflow-y-auto">
                    <div class="p-3 border-b border-slate-200">
                        <h4 class="font-semibold dark:text-slate-200 text-slate-800 mb-4 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="h-4 w-4 mr-2 text-primary-600">
                                <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10"></path>
                            </svg>
                            Recurring actions
                        </h4>
                        <div class="space-y-2">
                            <div class="p-4 text-sm text-gray-800 rounded-lg bg-gray-50 dark:bg-gray-800 dark:text-gray-300"
                                role="alert">
                                <span class="font-medium">Coming soon!</span> We need to gather more information.
                            </div>
                        </div>
                    </div>
                    <div class="p-2 flex flex-col justify-between">
                        <h4 class="font-normal text-sm dark:text-slate-300 text-slate-800 mb-4 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="h-4 w-4 mr-2 text-primary-600">
                                <path
                                    d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z">
                                </path>
                                <circle cx="12" cy="12" r="3"></circle>
                            </svg>
                            Nastavenia
                        </h4>
                        <div class="space-y-3">
                            <button hx-get="/profil" hx-target="#pageContentMain" hx-push-url="true"
                                hx-replace-url="true"
                                class="w-full justify-start p-3 hover:bg-slate-50 dark:hover:bg-slate-600 dark:text-slate-200 text-slate-800 transition-colors rounded-lg flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="h-4 w-4 mr-3">
                                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                                    <circle cx="12" cy="7" r="4"></circle>
                                </svg>
                                <span class="font-medium">Nastavenia profilu</span>
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="h-4 w-4 ml-auto">
                                    <path d="m9 18 6-6-6-6"></path>
                                </svg>
                            </button>

                            <button hx-get="/security" hx-target="#pageContentMain" hx-push-url="true"
                                hx-replace-url="true"
                                class="w-full justify-start p-3 hover:bg-slate-50 dark:hover:bg-slate-600 dark:text-slate-200 text-slate-800 transition-colors rounded-lg flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="h-4 w-4 mr-3">
                                    <rect width="18" height="11" x="3" y="11" rx="2" ry="2"></rect>
                                    <circle cx="12" cy="16" r="1"></circle>
                                    <path d="m7 11 0-5a5 5 0 0 1 10 0v5"></path>
                                </svg>
                                <span class="font-medium">Bezpečnosť</span>
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="h-4 w-4 ml-auto">
                                    <path d="m9 18 6-6-6-6"></path>
                                </svg>
                            </button>

                            <hr class="h-px bg-slate-200 border-0 my-4">

                            <a href="https://app.clickup.com/t/86995eez2" target="_blank"
                                class="w-full justify-start p-3 dark:hover:bg-slate-500 dark:text-gray-100 hover:bg-slate-50 transition-colors rounded-lg flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="h-4 w-4 mr-3">
                                    <circle cx="12" cy="12" r="10"></circle>
                                    <path d="m9 9 3 3 3-3"></path>
                                </svg>
                                <span class="font-medium">Podpora</span>
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="h-4 w-4 ml-auto">
                                    <path d="m9 18 6-6-6-6"></path>
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>
                <button hx-get="src/Controllers/login/logout.php" hx-target="body" hx-swap="outerHTML"
                    hx-push-url="true"
                    class="w-full justify-start p-3 dark:text-red-400 text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-500 dark:hover:text-white transition-colors rounded-lg flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="h-4 w-4 mr-3">
                        <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
                        <polyline points="16,17 21,12 16,7"></polyline>
                        <line x1="21" x2="9" y1="12" y2="12"></line>
                    </svg>
                    <span class="font-medium">Odhlásiť sa</span>
                </button>
            </div>
        </div>
    </section>
</div>
<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
require_once "/home/<USER>/www/src/lib/notifications/renderNotification.class.php";
require_once "/home/<USER>/www/src/lib/notifications/renderMention.class.php";
$userid = $_SESSION["user"]["data"]["userid"];
if (isset($_SESSION["client"])) {
    $fondid = $_SESSION["client"]["fondid"];
} elseif ($_SESSION["mode"]["mode"] === "admin") {
    $fondid = 1;
} else {
    $fondid = 0;
}

$notificationQuery = "SELECT 
n.datetimeactivity  as time,
   n.*,
   u.last_readed,
   ui.username                              as initiatorusername,
   ui.usernick                              as initiatorusernick,
   us.username,
   us.usernick
FROM notifications n
     JOIN users u ON u.last_readed <= n.datetimeactivity AND u.userid = $userid
     LEFT JOIN users us ON us.userid = n.mentioneduserid
     LEFT JOIN users ui ON ui.userid = n.userid
WHERE n.type != 'helper'
ORDER BY n.datetimeactivity DESC LIMIT 50";

$notifications = Connection::getDataFromDatabase($notificationQuery, defaultDB);
?>

<div class="overflow-hidden z-50 my-4 max-w-lg bg-clip-padding backdrop-filter backdrop-blur-md bg-opacity-10 dark:bg-gray-400/30 bg-white/60 text-base list-none rounded divide-y divide-gray-100 shadow-xl dark:divide-gray-600 rounded-xl"
    id="notification-dropdown"
    style="position: absolute; margin: 0px; right: 1rem; top: 1rem; z-index: 50;max-height: 96vh;min-height: 96vh; height: 96vh;">
    <div class="block py-2 pb-1 px-4 font-bold text-xl text-left text-gray-900">
        <div class="flex justify-between dark:text-gray-50 text-gray-900">
            <span>Notifikácie</span>
            <section class="inline-flex gap-2 items-center">
                <div class="p-1 rounded-md dark:hover:bg-gray-500 hover:bg-gray-200 transition-all cursor-pointer">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="lucide lucide-settings-2">
                        <path d="M20 7h-9" />
                        <path d="M14 17H5" />
                        <circle cx="17" cy="17" r="3" />
                        <circle cx="7" cy="7" r="3" />
                    </svg>
                </div>
                <div id="closeNotificationDropdown"
                    class="p-1 rounded-md dark:hover:bg-gray-500 hover:bg-gray-200 transition-all cursor-pointer">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="lucide lucide-x">
                        <path d="M18 6 6 18" />
                        <path d="m6 6 12 12" />
                    </svg>
                </div>
            </section>
        </div>
        <div class="flex justify-between items-center mt-2 overflow-y-auto">
            <ul class="flex flex-wrap text-xs font-medium text-center text-gray-500">
                <li class="me-2">
                    <a href="#" aria-current="page"
                        class="inline-block px-2 py-1 dark:text-blue-400 text-blue-600 bg-gray-100 rounded-lg active dark:bg-gray-800">Všetky
                        (<span id="notifCount"><?php echo $notifications[0] + $mentions[0]; ?></span>)</a>
                </li>
                <li class="me-2">
                    <a href="#"
                        class="inline-block px-2 transition-all dark:text-gray-400 py-1 rounded-lg hover:text-gray-600 hover:bg-gray-50 dark:hover:bg-gray-800 dark:hover:text-gray-300">Zmienky
                        (<span id="mentionCount"><?php echo $mentions[0]; ?></span>)</a>
                </li>
                <li class="me-2 flex items-center justify-center">
                    <a href="#"
                        class="inline-block px-2 py-1 rounded-lg dark:text-gray-400 hover:text-gray-600 hover:bg-gray-50 dark:hover:bg-gray-800 dark:hover:text-gray-300">
                        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-plus">
                            <path d="M5 12h14"></path>
                            <path d="M12 5v14"></path>
                        </svg>
                    </a>
                </li>
            </ul>
            <div id="markReadBtn" hx-post="/api/notifications/markAllRead" hx-target="#notificationsWrapper"
                class="p-1 rounded-md flex gap-2 px-2 dark:bg-green-800 dark:text-gray-100 text-gray-500 items-center text-xs dark:hover:bg-green-900 hover:bg-green-200 cursor-pointer transition-all">
                <span>Označiť ako prečítané</span>
                <svg id="markReadIcon" xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24"
                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide lucide-check-check">
                    <path d="M18 6 7 17l-5-5" />
                    <path d="m22 10-7.5 7.5L13 16" />
                </svg>
                <svg id="markReadSpinner" xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24"
                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    style="display: none;" class="lucide animate-spin lucide-loader-circle">
                    <path d="M21 12a9 9 0 1 1-6.219-8.56" />
                </svg>
            </div>
        </div>
    </div>
    <section id="notificationsWrapper" style="height: 88.5vh;" class="overflow-y-auto">
        <?php if ($notifications[0] !== 0) { ?>
            <span id="notifCountHidden" class="hidden"><?php echo $notifications[0]; ?></span>
            <div class="px-5 py-3 flex flex-col gap-4">
                <?php foreach ($mentions[1] as $key => $mention) {
                    $notif = new RenderMention($mention["notificationid"], $mention["destinacia"], $mention["objektid"], $mention["initiatorid"], $userid, $mention["notification"], $mention["objektdetail"], ""); ?>
                    <div class="w-full max-w-md <?php echo str_contains($mention["action"], "delete") ? "dark:bg-red-800/50 dark:text-red-100 dark:border-red-400 bg-red-500/50 border-red-400" : "dark:bg-blue-800/50 bg-blue-400/50 dark:text-gray-100 dark:border-gray-400 border-blue-400" ?> shadow-xl rounded-lg border relative"
                        style="border-width: 3px;">
                        <div class="p-3 flex space-x-4">
                            <div class="flex-shrink-0">
                                <img alt="user photo" class="w-8 h-8 rounded-full" src="/src/assets/img/user.png" />
                            </div>
                            <div class="flex-1 min-w-0">
                                <div class="flex items-start justify-between">
                                    <div>
                                        <p class="text-sm font-bold truncate"><?php echo $mention["initiatorusername"]; ?>
                                        </p>
                                        <p class="text-xs truncate">@<?php echo $mention["initiatorusernick"]; ?></p>
                                    </div>
                                    <div class="flex">
                                        <?php if ($mention["action"] === "delete") { ?>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                                stroke-linejoin="round" class="lucide text-red-500 lucide-message-circle-x">
                                                <path d="M7.9 20A9 9 0 1 0 4 16.1L2 22Z" />
                                                <path d="m15 9-6 6" />
                                                <path d="m9 9 6 6" />
                                            </svg>
                                        <?php } else { ?>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                                fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round"
                                                stroke-linejoin="round" class="lucide text-blue-400 lucide-at-sign">
                                                <circle cx="12" cy="12" r="4" />
                                                <path d="M16 8v5a3 3 0 0 0 6 0v-1a10 10 0 1 0-4 8" />
                                            </svg>
                                        <?php } ?>
                                    </div>
                                </div>
                                <p class="mt-1 text-sm"><?php $notif->render($mention["notificationid"]); ?></p>
                                <p class="mt-1 text-xs"><?php echo $mention["datetimeactivity"]; ?></p>
                            </div>
                        </div>
                    </div>
                <?php } ?>
                <?php foreach ($notifications[1] as $key => $item) {
                    $notif = new RenderNotification(
                        $item["activityid"],
                        $item["destinacia"],
                        $item["objektid"],
                        $item["action"],
                        $userid,
                        $item["initiatorusername"],
                        $item["notification"],
                        $item["objektdetail"],
                        $item["needsmentioning"],
                        $item["fondid"],
                        $item["type"],
                        $item["message"] ? $item["message"] : "Nič"
                    );

                    if (str_contains(strtolower($item["action"]), "delete")) {
                        $color = "dark:bg-red-800/50 dark:text-red-100 dark:border-red-400 bg-red-500/50 border-red-400";
                    } elseif (str_contains(strtolower($item["action"]), "create")) {
                        $color = "dark:bg-green-800/50 bg-green-400/50 dark:text-gray-100 dark:border-gray-400 border-green-400";
                    } else {
                        $color = "dark:bg-blue-100/20 bg-blue-400/50 dark:text-gray-100 dark:border-gray-400 border-blue-400";
                    }

                    if ($notif->render($item["id"]) != "") {
                        ?>
                        <div class="w-full max-w-md bg-white shadow-md rounded-lg border 
                            <?php echo $color ?> relative">
                            <div class="p-3 flex space-x-4">
                                <div class="flex-shrink-0">
                                    <img alt="user photo" class="w-8 h-8 rounded-full" src="/src/assets/img/user.png" />
                                </div>
                                <div class="flex-1 min-w-0">
                                    <div class="flex items-start justify-between">
                                        <div>
                                            <p class="text-sm font-bold truncate"><?php echo $item["initiatorusername"]; ?>
                                            </p>
                                            <p class="text-xs truncate">@<?php echo $item["initiatorusernick"]; ?></p>
                                        </div>
                                        <section class="flex gap-1 items-center">

                                            <?php if ($item["needsmentioning"] && $item["mentioneduserid"] == NULL) {
                                                ?>
                                                <div data-dropdown-toggle="dropdownUsers<?php echo $key; ?>"
                                                    data-dropdown-placement="bottom"
                                                    hx-get="/api/get/notificationUsers?notifID=<?php echo $item["id"]; ?>"
                                                    hx-target="#dropdownUsers<?php echo $key; ?>" id="<?php echo $key; ?>"
                                                    class="p-1 rounded-md items-center hover:bg-gray-200 dark:hover:text-gray-700 openUserAssignDropdown transition-all cursor-pointer text-xs inline-flex gap-1 px-2">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                                        stroke-linejoin="round" class="lucide lucide-user-round-plus">
                                                        <path d="M2 21a8 8 0 0 1 13.292-6"></path>
                                                        <circle cx="10" cy="8" r="5"></circle>
                                                        <path d="M19 16v6"></path>
                                                        <path d="M22 19h-6"></path>
                                                    </svg>
                                                    <small>Označiť používateľa</small>
                                                </div>
                                                <div id="dropdownUsers<?php echo $key; ?>"
                                                    class="z-10 hidden bg-white absolute right-2 dropdownUsersNotif rounded-lg shadow-xl border dark:bg-gray-700"
                                                    style="top: 2rem"></div>
                                            <?php } else if ($item["needsmentioning"] && $item["mentioneduserid"] != NULL) { ?>
                                                    <div data-dropdown-toggle="dropdownUsers<?php echo $key; ?>"
                                                        data-dropdown-placement="bottom"
                                                        hx-get="/api/get/notificationUsers?notifID=<?php echo $item["id"]; ?>"
                                                        hx-target="#dropdownUsers<?php echo $key; ?>" id="<?php echo $key; ?>"
                                                        class="p-1 rounded-md items-center dark:bg-green-700 dark:hover:bg-green-800 bg-green-200 openUserAssignDropdown transition-all cursor-pointer text-xs inline-flex gap-1 px-2">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                                            fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                                            stroke-linejoin="round" class="lucide lucide-circle-user-round">
                                                            <path d="M18 20a6 6 0 0 0-12 0" />
                                                            <circle cx="12" cy="10" r="4" />
                                                            <circle cx="12" cy="12" r="10" />
                                                        </svg>
                                                        <small><?php echo $item["username"]; ?></small>
                                                    </div>
                                            <?php }
                                            if ($item["fondid"] > 1) { ?>
                                                <div class="p-1 rounded-md items-center hover:bg-gray-200 dark:hover:text-gray-700 dark:text-gray-700 transition-all cursor-pointer text-xs inline-flex gap-1 px-2"
                                                    style="background-color: #05DF72;">
                                                    <small>Client</small>
                                                </div>
                                            <?php } else if ($item["fondid"] == 0) {
                                                ?>
                                                    <div class="p-1 rounded-md items-center hover:bg-gray-200 dark:hover:text-gray-700 transition-all cursor-pointer text-xs inline-flex gap-1 px-2"
                                                        style="background-color: #C27AFF;">
                                                        <small>Hromadný</small>
                                                    </div>
                                            <?php } else {
                                                ?>
                                                    <div class="p-1 rounded-md items-center hover:bg-gray-200 text-gray-700 transition-all cursor-pointer text-xs inline-flex gap-1 px-2"
                                                        style="background-color: #FDC700;">
                                                        <small>Správca</small>
                                                    </div>
                                            <?php } ?>
                                        </section>
                                    </div>
                                    <p class="mt-1 text-sm"><?php echo $notif->render($item["id"]); ?></p>
                                    <p class="mt-1 text-xs"><?php echo $item["time"]; ?></p>
                                </div>
                            </div>
                        </div>
                    <?php }
                } ?>
                <?php if ($notifications[0] > 49) { ?>
                    <div hx-get="/activity" hx-target="#pageContentMain" hx-replace-url="true"
                        class="p-4 text-sm text-gray-800 rounded-lg bg-gray-50 hover:dark:bg-gray-600 transition-all cursor-pointer dark:bg-gray-800 dark:text-gray-300"
                        role="alert">
                        <span class="font-bold">Viac notifikácii!</span> Pre zobrazenie všetkej aktivity klikni sem.
                    </div>
                <?php } ?>
            </div>

        <?php } else { ?>
            <span id="notifCountHiddenAfter" class="hidden"><?php echo $notifications[0]; ?></span>
            <div class="flex flex-col items-center justify-center p-8 rounded-lg h-full shadow-md">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="w-24 h-24 text-gray-300 mb-4">
                    <path
                        d="M21.2 8.4c.5.38.8.97.8 1.6v10a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V10a2 2 0 0 1 .8-1.6l8-6a2 2 0 0 1 2.4 0l8 6Z" />
                    <path d="m22 10-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 10" />
                </svg>
                <h3 class="text-xl font-semibold dark:text-gray-500 text-gray-700 mb-2">Momentálne nemáte žiadne notifikácie
                </h3>
                <p class="text-gray-400 text-center">
                    Všetko máte prečítané! Skontrolujte tento panel neskôr.
                </p>
            </div>
        <?php } ?>
    </section>
</div>
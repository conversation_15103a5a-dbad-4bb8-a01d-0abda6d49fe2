<?php

// Input URL
if (isset($_POST["path"])) {
    $url = $_POST["path"];
} else {
    $url = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
}

$trimmed_url = trim($url, '/');
$url_parts = explode('/', $trimmed_url);

$parsed_url = parse_url($_SERVER['REQUEST_URI']);
$query_params = [];
if (isset($parsed_url['query'])) {
    parse_str($parsed_url['query'], $query_params);
}
//print_r($url);
$action = $query_params["action"] !== "" ? $query_params["action"] : "";
$urlRealPaths = [
    "investicne-zamery" => "Investičné zámery",
    "akcie" => "Akcie",
    "aktiva" => "Aktíva",
    "cenne-papiere" => "Cenné papiere",
    "dlhopisy" => "Dlhopisy",
    "podielove-fondy" => "Podielové fondy",
    "new" => $action === "buy" ? "Nákup" : "Predaj",
    "konverzie" => "Konverzie",
    "kupon" => "Kupón",
    "istina" => "Istina",
    "potvrdenie" => "Potvrdenie",
    "splatenie" => "Splatenie",
    "vysporiadanie" => "Vysporiadanie",
    "penazne" => "Peňažné",
    "majetkove" => "Majetkové",
    "natipovanie" => "Natipovanie",
    "dosle-aktiva" => "Došlé aktíva",
    "presun-prostriedkov" => "Presun prostriedkov",
    "transakcie" => "Transakcie",
    "sankcny-zoznam" => "Sankčný zoznam",
    "uhrad" => "Úhrada",
    "obchodny-dennik" => "Obchodný denník",
    "poplatky" => "Poplatky",
    "odchadzajuce" => "Odchádzajúce",
    "prichadzajuce" => "Prichádzajúce",
    "terminovany-vklad" => "Terminovaný vklad",
    "edit" => "Úprava",
    "klienti" => "Klienti",
    "detail" => "Detail",
    "prehlady" => "Prehľady",
    "vypis-o-stave-majetku" => "Výpis o stave majetku",
    "klientsky-vypis" => "Klientský výpis",
    "vyplatene-vynosy" => "Vyplatené výnosy",
    "nbs-gfi" => "Reporty pre NBS a GFI",
    "menove-pary" => "Menové páry",
    "ucty" => "Účty",
    "majetkovy-ucet" => "Majetkový účet",
    "terminovany-ucet" => "Terminovaný účet",
    "dividendy" => "Dividendy",
    "vklad-prostriedkov" => "Vklad prostriedkov na účet",
    "vybery-penaznych-prostriedkov" => "Vybery peňažných prostriedkov",
    "presuny-penaznych-prostriedkov" => "Presuny peňažných prostriedkov",
    "prevody-penaznych-prostriedkov" => "Prevody peňažných prostriedkov",
    "vklady-cennych-papierov" => "Vklady cenných papierov",
    "vybery-cennych-papierov" => "Výbery cenných papierov",
];

$linkUrl = "/";
?>

<nav class="flex py-3 px-5 text-gray-700 border-b w-full dark:bg-gray-500 bg-white z-20"
    style="position: sticky; top: 4.5rem;">
    <ol class="inline-flex items-center space-x-1 md:space-x-2 rtl:space-x-reverse">
        <li class="inline-flex items-center">
            <div hx-get="/" hx-target="#pageContentMain" hx-replace-url="true" hx-push-url="true"
                class="inline-flex items-center text-sm gap-2 font-medium cursor-pointer text-gray-700 hover:text-blue-600 dark:text-gray-100 dark:hover:text-white">
                <svg xmlns="http://www.w3.org/2000/svg" width="13" height="13" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide lucide-house">
                    <path d="M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8" />
                    <path
                        d="M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z" />
                </svg>
                Domov
            </div>
        </li>
        <?php foreach ($url_parts as $key => $value) {
            $linkUrl .= $value . "/";
            if ($value === "pridat") {
                continue;
            }
            ?>
            <li>
                <div hx-get="<?php echo $linkUrl; ?>" class="flex items-center" hx-target="#pageContentMain"
                    hx-replace-url="true">
                    <svg class="rtl:rotate-180 block w-2 h-2 mx-1 dark:text-gray-100 text-gray-400 " aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                            d="m1 9 4-4-4-4" />
                    </svg>
                    <span
                        class="ms-1 cursor-pointer hover:underline text-sm <?php echo sizeof($url_parts) === $key + 1 ? "font-bold text-blue-500 dark:text-blue-100" : "font-medium dark:text-gray-100 text-gray-700" ?> md:ms-2">
                        <?php if (sizeof($url_parts) === $key + 1 && sizeof($url_parts) >= 3) {
                            echo $value;
                        } else {
                            echo $urlRealPaths[$value];
                        } ?>
                    </span>
                </div>
            </li>
        <?php } ?>
    </ol>
</nav>
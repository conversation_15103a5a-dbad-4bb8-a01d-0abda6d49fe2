<?php

class ExportToExcelButton extends ExcelController
{
    public function __construct($data, $columns, $filename, $property, $getExternalData = false)
    {
        parent::__construct($data, $columns, $filename, $property, $getExternalData);
    }

    public function render()
    { ?>
        <form id="exportToExcel" onsubmit="<?php echo $this->getExternalData ? "getExternalData(event)" : "exportToExcel(event)" ?>">
            <input type="hidden" name="data" id="dataInput" value='<?php echo json_encode($this->data); ?>' />
            <input type="hidden" name="columns" value='<?php echo json_encode($this->columns); ?>' />
            <input type="hidden" name="filename" value="<?php echo $this->filename; ?>" />
            <input type="hidden" name="property" value="<?php echo $this->property; ?>" />
            <button id="exportToExcelTrigger" type="submit"
                class="p-4 bg-gray-100 dark:bg-gray-900 dark:hover:bg-green-600 flex items-center justify-center gap-2 rounded-xl text-center font-semibold text-gray-600 hover:bg-green-200 transition-all cursor-pointer hover:text-white">
                <svg id="excelIcon" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="25" height="25"
                    viewBox="0 0 48 48">
                    <path fill="#169154" d="M29,6H15.744C14.781,6,14,6.781,14,7.744v7.259h15V6z"></path>
                    <path fill="#18482a" d="M14,33.054v7.202C14,41.219,14.781,42,15.743,42H29v-8.946H14z"></path>
                    <path fill="#0c8045" d="M14 15.003H29V24.005000000000003H14z"></path>
                    <path fill="#17472a" d="M14 24.005H29V33.055H14z"></path>
                    <g>
                        <path fill="#29c27f" d="M42.256,6H29v9.003h15V7.744C44,6.781,43.219,6,42.256,6z"></path>
                        <path fill="#27663f" d="M29,33.054V42h13.257C43.219,42,44,41.219,44,40.257v-7.202H29z"></path>
                        <path fill="#19ac65" d="M29 15.003H44V24.005000000000003H29z"></path>
                        <path fill="#129652" d="M29 24.005H44V33.055H29z"></path>
                    </g>
                    <path fill="#0c7238"
                        d="M22.319,34H5.681C4.753,34,4,33.247,4,32.319V15.681C4,14.753,4.753,14,5.681,14h16.638 C23.247,14,24,14.753,24,15.681v16.638C24,33.247,23.247,34,22.319,34z">
                    </path>
                    <path fill="#fff"
                        d="M9.807 19L12.193 19 14.129 22.754 16.175 19 18.404 19 15.333 24 18.474 29 16.123 29 14.013 25.07 11.912 29 9.526 29 12.719 23.982z">
                    </path>
                </svg>
                <svg aria-hidden="true" id="spinnerExcel"
                    class="w-6 h-6 hidden text-gray-200 animate-spin dark:text-gray-600 fill-blue-600" viewBox="0 0 100 101"
                    fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                        fill="currentColor" />
                    <path
                        d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                        fill="currentFill" />
                </svg>
            </button>
        </form>
        <script>
            function getExternalData(e){
                e.preventDefault();
                const formData = new FormData(e.currentTarget);
                const formDataObj = formData.get("data");
                const columns = formData.get("columns");
                const filename = formData.get("filename");
                const property = formData.get("property");
                document.getElementById("excelIcon").style.display = "none";
                document.getElementById("spinnerExcel").style.display = "block";
                console.log({filename, property});
                $.ajax({
                    url: `/src/Controllers/exports/${property}-export.php`,
                    method: "POST",
                    data: JSON.stringify({ "data": formDataObj, "columns": columns, "filename": filename, "property": property }),
                    dataType: "json",
                    contentType: "application/json",
                    success: function (data) {
                        document.getElementById("dataInput").value = JSON.stringify(data.data);
                        $("#exportToExcel").attr("onsubmit", "exportToExcel(event)");
                        $("#exportToExcelTrigger").click();
                    },
                    error: function (xhr, status, error) {
                        console.error("Error:", error);
                    },
                });
            }

            function exportToExcel(e) {
                e.preventDefault();
                const formData = new FormData(e.currentTarget);
                const formDataObj = formData.get("data");
                const columns = formData.get("columns");
                const filename = formData.get("filename");
                const property = formData.get("property");
                document.getElementById("excelIcon").style.display = "none";
                document.getElementById("spinnerExcel").style.display = "block";
                console.log({filename, property});
                $.ajax({
                    url: "/src/Controllers/global/exportToExcel.php",
                    method: "POST",
                    data: JSON.stringify({ "data": formDataObj, "columns": columns, "filename": filename, "property": property }),
                    dataType: "json",
                    contentType: "application/json",
                    success: function (data) {
                        console.log(data);
                        fetch(data.link.replace("/home/<USER>/www", ""))
                            .then(response => response.blob())
                            .then(blob => {
                                const url = URL.createObjectURL(blob);
                                const a = document.createElement('a');
                                a.href = url;
                                a.download = data.link.replace("/home/<USER>/www", "");
                                a.click();
                                URL.revokeObjectURL(url);
                                document.getElementById("excelIcon").style.display = "block";
                                document.getElementById("spinnerExcel").style.display = "none";
                            })
                            .catch(error => {
                                alert('File download failed:' + error);
                                console.error('File download failed:', error);
                            });
                    },
                    error: function (xhr, status, error) {
                        console.error("Error:", error);
                    },
                });
            }
        </script>
    <?php }
}
?>
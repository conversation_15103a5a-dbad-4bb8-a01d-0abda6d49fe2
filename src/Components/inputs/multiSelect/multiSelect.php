<?php

/**
 * MultiSelect component for selecting more options graphically and with additional functionalities
 **/

class MultiSelect
{
    public static function render(string $placeholder, $arrayOfOptions, string $id, array $optionText = [], string $value = ""): string
    {
        return self::buildMultiSelect($placeholder, $arrayOfOptions, $id, $optionText, $value);
    }

    public static function buildMultiSelect(string $placeholder, array $arrayOfOptions, string $id, array $optionText = [], string $value = ""): string
    {
        $select = "<div id='" . $id . "Picker' class='w-full MultiSelectWrappers relative'>
                            <button id='" . $id . "MultiSelectResetAllButton' type='button'
                                    class='absolute multiselectresetall hidden bg-gray-400 dark:bg-gray-800 hover:bg-red-500 cursor-pointer p-0.5 rounded-full text-white'
                                    style='left: -4px; top: -4px;'>
                                <svg class='w-3 h-3' aria-hidden='true' xmlns='http://www.w3.org/2000/svg' width='24'
                                     height='24' fill='none' viewBox='0 0 24 24'>
                                    <path stroke='currentColor' stroke-linecap='round' stroke-linejoin='round'
                                          stroke-width='2'
                                          d='M5 7h14m-9 3v8m4-8v8M10 3h4a1 1 0 0 1 1 1v3H9V4a1 1 0 0 1 1-1ZM6 7h12v13a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1V7Z'/>
                                </svg>
                            </button>
                            <div id='" . $id . "MultiSelectWrapper'
                                 class='text-md text-gray-800 bg-gray-100 dark:bg-gray-900 dark:text-gray-100 MultiSelectWrapper h-full rounded-lg p-2 px-4 cursor-pointer shadow-md hover:shadow-xs transition-all border border-gray-300 shadow-none font-semibold flex justify-between items-center'>
                                <div id='" . $id . "MultiSelectPickerInitial'>$placeholder</div>
                                <div id='" . $id . "MultiSelectPickerList'
                                     class='flex flex-wrap gap-1 items-center m-1.5'>

                                </div>
                                <svg id='" . $id . "MultiSelectSvg' class='w-4 h-4 text-gray-800 dark:text-white' aria-hidden='true'
                                     xmlns='http://www.w3.org/2000/svg' width='24' height='24' fill='none'
                                     viewBox='0 0 24 24'>
                                    <path id='MultiSelectPath' stroke='currentColor' stroke-linecap='round'
                                          stroke-linejoin='round'
                                          stroke-width='2' d='m19 9-7 7-7-7'/>
                                </svg>
                            </div>
                            <div id='" . $id . "multiselectDropdown' style='width: 100%; -ms-overflow-style: none;
          scrollbar-width: none;'
                                 class='absolute bg-white z-10 dark:bg-gray-800 hidden transition-all shadow-xl max-h-72 overflow-y-scroll rounded-lg'>
                                <div class='flex sticky gap-2 items-center top-0 bg-white dark:bg-gray-800 p-2 border-b'>
                                    <div class='w-full relative'>
                                        <div class='absolute inset-y-0 start-0 flex items-center ps-5 pointer-events-none'>
                                            <svg class='w-4 h-4 text-gray-500 dark:text-gray-400' aria-hidden='true'
                                                 xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'>
                                                <path stroke='currentColor' stroke-linecap='round'
                                                      stroke-linejoin='round' stroke-width='2'
                                                      d='m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z'/>
                                            </svg>
                                        </div>
                                        <svg id='" . $id . "clearSearch'
                                             class='w-4 h-4 hidden text-gray-800 absolute hover:bg-gray-500 cursor-pointer rounded-full hover:text-white top-3 right-2'
                                             aria-hidden='true' xmlns='http://www.w3.org/2000/svg' width='24'
                                             height='24'
                                             fill='none' viewBox='0 0 24 24'>
                                            <path stroke='currentColor' stroke-linecap='round' stroke-linejoin='round'
                                                  stroke-width='2'
                                                  d='m15 9-6 6m0-6 6 6m6-3a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z'/>
                                        </svg>
                                            <label for='search'></label>
                                            <input type='text' id='" . $id . "search' 
                                                   class='block no-spinner w-full ps-10 py-2 text-sm dark:bg-gray-700 dark:text-gray-100
                                                    border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 
                                                   focus:border-blue-500'
                                                   placeholder='Hľadať'/>
                                            <input type='hidden' id='" . $id . "MultiSelectValues' onchange='isincurrSelectChange(this)' name='" . $id . "values'>
                                            <input type='hidden' id='" . $id . "SecondParameter' onchange='' name='secondParam'/>
                                    </div>
                                    <button id='" . $id . "MultiSelectAll' type='button'
                                            class='bg-blue-500 text-white rounded-lg p-2 hover:bg-blue-800 hover:shadow-md'>
                                        <svg class='w-5 h-5' aria-hidden='true'
                                             xmlns='http://www.w3.org/2000/svg' width='24' height='24'
                                             fill='currentColor' viewBox='0 0 24 24'>
                                            <path fill-rule='evenodd'
                                                  d='M9 2a1 1 0 0 0-1 1H6a2 2 0 0 0-2 2v15a2 2 0 0 0 2  2h12a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2h-2a1 1 0 0 0-1-1H9Zm1 2h4v2h1a1 1 0 1 1 0 2H9a1 1 0 0 1 0-2h1V4Zm5.707 8.707a1 1 0 0 0-1.414-1.414L11 14.586l-1.293-1.293a1 1 0 0 0-1.414 1.414l2 2a1 1 0 0 0 1.414 0l4-4Z'
                                                  clip-rule='evenodd'/>
                                        </svg>
                                    </button>
                                </div>
                                <div id='" . $id . "MultiSelectSearchError' class='hidden bg-red-500 text-white p-2'></div>
                                <div class='flex flex-col mt-2 p-2 pt-0'>";

        foreach ($arrayOfOptions as $item) {
            $optionId = implode("", $optionText);
            $select .= "<span id='" . $item[$optionText[0]] . "_" . $item[$optionText[1]] . "' data-fondid='$item[$value]' data-second='" . $item[$optionText[1]] . "'
                                              class='cursor-pointer rounded-md p-2 " . $id . "MultiSelectSpan font-normal hover:bg-gray-300 dark:hover:bg-gray-900
                                               hover:font-bold dark:text-gray-100 transition-all'>[$item[$value]] " . $item[$optionText[0]] . " " . $item[$optionText[1]] . "</span>";
        }

        $select .= "</div>
            </div>
        </div>";

        $select .= "<script>
            // Function to initialize when element appears
            function initMultiSelectWhenVisible(id) {
                // Check if element already has initialization flag
                const multiSelectElement = document.getElementById(id + 'Picker');
                if (!multiSelectElement || multiSelectElement.dataset.initialized === 'true') {
                    return; // Already initialized or element doesn't exist
                }
                
                // Create an intersection observer to detect when the element becomes visible
                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            // Element is visible, initialize the multiSelect if not already initialized
                            if (!entry.target.dataset.initialized) {
                                initializeMultiSelect(id);
                                // Mark as initialized
                                entry.target.dataset.initialized = 'true';
                            }
                            // Stop observing once initialized
                            observer.unobserve(entry.target);
                        }
                    });
                });
                
                // Start observing the multiSelect element
                if (multiSelectElement) {
                    observer.observe(multiSelectElement);
                }
            }

            // For initial page load
            document.addEventListener('DOMContentLoaded', function() {
                initMultiSelectWhenVisible('" . $id . "');
            });
            
            // For HTMX content swaps
            document.body.addEventListener('htmx:afterSettle', function(event) {
                // Using afterSettle instead of afterSwap to ensure DOM is fully updated
                initMultiSelectWhenVisible('" . $id . "');
            });
        </script>";
        $select .= "<script src='/src/assets/js/prehlady/financne-toky/index.js'></script>";
        return $select;
    }
}
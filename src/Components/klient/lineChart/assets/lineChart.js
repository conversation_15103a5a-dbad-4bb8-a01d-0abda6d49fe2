document.getElementById("customRangeButton").addEventListener("click", (e) => {
    console.log(e.target.classList);
    if (e.target.classList.contains("zavri")) {
        document.getElementById("customRangeSpan").style.display = "block";
        document.getElementById("customRangeButton").classList.add("hover:bg-gray-100");
        document.getElementById("fromToDate").style.display = "none";
    } else if (e.target.classList.contains("intervalButton") || e.target.classList.contains("w-5")) {
        document.getElementById("customRangeSpan").style.display = "block";
        document.getElementById("customRangeButton").classList.add("hover:bg-gray-100");
        document.getElementById("fromToDate").style.display = "none";
        document.getElementById("intervalButtonCheck").classList.add("selected");
    } else {
        document.getElementById("customRangeSpan").style.display = "none";
        document.getElementById("customRangeButton").classList.remove("hover:bg-gray-100");
        document.getElementById("fromToDate").style.display = "flex";
    }
});

document.getElementById("resetDataPoints").addEventListener("click", () => {
    document.querySelectorAll(".dataPointButton").forEach((item) => {
        item.classList.remove("border-blue-700");
        item.classList.remove("selected");
    });
    let submitter;
    document.querySelectorAll(".intervalButton").forEach((item) => {
        console.log(item.classList);
        if (item.classList.contains("selected")) {
            submitter = item;
        }
    });
    document.getElementById("dataPointsErr").style.display = "none";
    document.getElementById("dataPointsErr").innerHTML = "";
    document.getElementById("graphControlsForm").requestSubmit(submitter);
});

document.getElementById("graphControlsForm").addEventListener("submit", (e) => {
    e.preventDefault();
    let fonds = "";
    document.querySelectorAll(".portfolioButton").forEach((item) => {
        console.log(item.classList);
        if (item.classList.contains("selected")) {
            fonds = item.attributes["data-fondid"].nodeValue;
        }
    });
    console.log(fonds);
    let client = e.submitter.attributes["data-client"].nodeValue;
    let selectedInterval;
    let datefrom = "";
    let dateto = "";
    if (document.getElementsByClassName("selected")[0].attributes["data-interval"] !== undefined) {
        selectedInterval = document.getElementsByClassName("selected")[0].attributes["data-interval"].nodeValue;
    } else {
        selectedInterval = "1";
        datefrom = e.target.datefrom.value;
        dateto = e.target.dateto.value;
    }
    let selectedDataPoint;
    document.querySelectorAll(".dataPointButton").forEach((e) => {
        if (e.classList.contains("selected")) {
            selectedDataPoint = e;
        }
    });

    let dates = [];
    let sums = [];
    let returnDates = [];
    let returnSums = [];
    let returnReturn = [];
    let points = [];
    let datePoints = [];
    let secondPoints = [];

    fetch('/src/Controllers/klienti/graph/getInterval.php?interval=' + selectedInterval + "&fondid=" + fonds + "&clientID=" + client + "&datefrom=" + datefrom + "&dateto=" + dateto, {
        method: 'GET'
    })
        .then(response => response.text())
        .then(data => {
            console.log(data)
            let responseData = JSON.parse(data);
            if (responseData.money.length === 0) {
                document.getElementById("noDataOverlay").style.opacity = "65%";
                document.getElementById("noDataOverlay").innerHTML = "Pre zvolené portfólio a čas neboli nájdené žiadne dáta";
            } else {
                document.getElementById("noDataOverlay").style.opacity = "0%";
                document.getElementById("noDataOverlay").innerHTML = "";
            }
            responseData.money.forEach(datum => {
                sums.push(datum.sum);
                dates.push(datum.datum);
            });
            let returnSum = 0;
            responseData.return.forEach(datum => {
                returnSum += parseFloat(datum.cumulative_return) / responseData.return.length;
                returnSums.push(datum.suma);
                returnDates.push(datum.datum);
                returnReturn.push(datum.cumulative_return);
            });
            document.getElementById("vykonnostSpan").innerHTML = returnSum.toFixed(2) + "%";
            updateGraph(dates, sums, returnDates, returnSums, returnReturn, datePoints, points, secondPoints, "");
        })
        .catch(error => {
            console.error(error);
        });

    if (selectedDataPoint !== undefined) {
        document.getElementById("resetDataPoints").style.display = "block";
        let dataObject = selectedDataPoint.attributes["data-object"].nodeValue;
        fetch('/src/Controllers/klienti/graph/getD.php?interval=' + selectedInterval + "&fondid=" + fonds + "&object=" + dataObject + "&datefrom=" + datefrom + "&dateto=" + dateto, {
            method: 'GET'
        })
            .then(response => response.text())
            .then(data => {
                let responseData = JSON.parse(data);
                let bubbleData = [];
                let sumy = [];

                if (!responseData.error) {
                    document.getElementById("dataPointsErr").style.display = "none";
                    document.getElementById("dataPointsErr").innerHTML = "";
                    responseData.points.forEach(function (value, i) {
                        datePoints.push(value.datumnaroku);
                        sumy.push(value.suma);
                        secondPoints.push(value.vynoskus);
                    });
                    dates.forEach(function (value, i) {
                        if (datePoints.includes(value)) {
                            bubbleData.push({x: value, y: 0, r: 12});
                        } else {
                            bubbleData.push({x: i, y: 0, r: 0});
                        }
                    });
                } else {
                    document.getElementById("dataPointsErr").style.display = "block";
                    document.getElementById("dataPointsErr").innerHTML = responseData.message;
                }
                updateGraph(dates, sums, returnDates, returnSums, returnReturn, dates, bubbleData, sumy, dataObject);
            })
            .catch(error => {
                console.error(error);
            });
    } else {
        document.getElementById("resetDataPoints").style.display = "none";
    }
})

document.querySelectorAll(".intervalButton").forEach((item) => {
    item.addEventListener('click', (e) => {
        document.querySelectorAll(".intervalButton").forEach((item) => {
            item.classList.remove("border-blue-700");
            e.target.classList.add("border-gray-200");
            item.classList.remove("selected");
        });
        e.target.classList.add("border-blue-700");
        e.target.classList.remove("border-gray-200");
        e.target.classList.add("selected");
    })
});

document.querySelectorAll(".dataPointButton").forEach((item) => {
    item.addEventListener('click', (e) => {
        document.querySelectorAll(".dataPointButton").forEach((item) => {
            item.classList.remove("border-blue-700");
            item.classList.add("border-gray-200");
            item.classList.remove("selected");
        });
        e.target.classList.add("border-blue-700");
        e.target.classList.remove("border-gray-200");
        e.target.classList.add("selected");
    })
});

document.querySelectorAll(".portfolioButton").forEach((item) => {
    item.addEventListener('click', (e) => {
        document.querySelectorAll(".portfolioButton").forEach((item) => {
            item.classList.remove("border-blue-700");
            item.classList.add("border-gray-200");
            item.classList.remove("selected");
        });
        e.target.classList.add("border-blue-700");
        item.classList.remove("border-gray-200");
        e.target.classList.add("selected");
    })
});

const getOrCreateTooltip = (chart) => {
    let tooltipEl = chart.canvas.parentNode.querySelector('div');

    if (!tooltipEl) {
        tooltipEl = document.createElement('div');
        tooltipEl.style.background = 'rgba(0, 0, 0, 1)';
        tooltipEl.style.borderRadius = '15px';
        tooltipEl.style.padding = "1rem";
        tooltipEl.style.opacity = 1;
        tooltipEl.style.width = "10rem";
        tooltipEl.style.pointerEvents = 'none';
        tooltipEl.style.position = 'absolute';
        tooltipEl.style.transform = 'translate(-50%, 0)';
        tooltipEl.style.transition = 'all .1s ease';

        const table = document.createElement('table');
        table.style.margin = '0px';

        tooltipEl.appendChild(table);
        chart.canvas.parentNode.appendChild(tooltipEl);
    }

    return tooltipEl;
};

const externalTooltipHandler = (context) => {
    const {chart, tooltip} = context;
    const tooltipEl = getOrCreateTooltip(chart);

    if (tooltip.opacity === 0) {
        tooltipEl.style.opacity = 0;
        return;
    }
    let filteredDataset = chart.data.datasets[0].data.filter((element) => element.r != 0);
    console.log(tooltip.title);
    let tooltipTitle = "";

    switch (chart.data.datasets[0].label) {
        case "D":
            tooltipTitle = "Dividenda";
            break;
        case "C":
            tooltipTitle = "Kupón";
            break;
        case "I":
            tooltipTitle = "Úrok";
            break;
        default:
            tooltipTitle = "Title"
    }

    if (tooltip.body) {
        const titleLines = tooltip.title || [];
        const bodyLines = tooltip.body.map(b => b.lines);

        const tableHead = document.createElement('thead');

        titleLines.forEach(title => {
            const tr = document.createElement('tr');
            tr.style.borderWidth = 0;

            const th = document.createElement('th');
            th.style.borderWidth = 0;
            th.style.color = "white";
            const text = document.createTextNode(tooltipTitle);

            th.appendChild(text);
            tr.appendChild(th);
            tableHead.appendChild(tr);
        });

        const tableBody = document.createElement('tbody');
        bodyLines.forEach((body, i) => {
            const colors = tooltip.labelColors[i];
            const span = document.createElement('span');
            span.style.background = colors.backgroundColor;
            span.style.borderColor = colors.borderColor;
            span.style.borderWidth = '2px';
            span.style.color = "white";
            span.style.marginRight = '12px';
            span.style.height = '12px';
            span.style.width = '12px';
            span.style.display = 'inline-block';

            const tr = document.createElement('tr');
            tr.style.backgroundColor = 'inherit';
            tr.style.borderWidth = "0";
            tr.style.color = "white";

            const td = document.createElement('td');
            td.style.borderWidth = "0";
            td.style.color = "white";
            console.log(chart.data.datasets[1].data);
            let suma = filteredDataset.findIndex((element) => element.x === tooltip.title[0]);
            console.log(suma);
            const text = document.createTextNode("Suma: " + chart.data.datasets[1].data[suma]);

            td.appendChild(span);
            td.appendChild(text);
            tr.appendChild(td);
            tableBody.appendChild(tr);
        });

        bodyLines.forEach((body, i) => {
            const tr = document.createElement('tr');
            tr.style.backgroundColor = 'inherit';
            tr.style.borderWidth = "0";
            tr.style.color = "white";

            const td = document.createElement('td');
            td.style.borderWidth = "0";
            td.style.color = "white";
            const text = document.createTextNode("Dátum: " + tooltip.title[0]);

            td.appendChild(text);
            tr.appendChild(td);
            tableBody.appendChild(tr);
        });

        const tableRoot = tooltipEl.querySelector('table');

        while (tableRoot.firstChild) {
            tableRoot.firstChild.remove();
        }

        tableRoot.appendChild(tableHead);
        tableRoot.appendChild(tableBody);
    }

    const {offsetLeft: positionX, offsetTop: positionY} = chart.canvas;

    tooltipEl.style.opacity = "1";
    tooltipEl.style.left = positionX + tooltip.caretX + 'px';
    tooltipEl.style.top = positionY + tooltip.caretY + 'px';
    tooltipEl.style.font = tooltip.options.bodyFont.string;
    tooltipEl.style.padding = tooltip.options.padding + 'px ' + tooltip.options.padding + 'px';
};

const down = (ctx, value) => ctx.p0.parsed.y > ctx.p1.parsed.y ? value : undefined;
const up = (ctx, value) => ctx.p0.parsed.y < ctx.p1.parsed.y ? value : undefined;
const plugin = {
    id: 'corsair', defaults: {
        width: 1, color: '#FF4949', dash: [3, 3], textBackgroundColor: '#333'
    }, afterInit: (chart, args, opts) => {
        chart.corsair = {
            x: 0, y: 0, draw: false, yValue: null
        }
    }, afterEvent: (chart, args) => {
        const {inChartArea} = args;
        const {x, y} = args.event;
        const yAxis = chart.scales.money;
        const yReturn = chart.scales.return;
        chart.corsair = {
            x,
            y,
            draw: inChartArea,
            yValue: inChartArea ? yAxis.getValueForPixel(y) : null,
            yReturn: inChartArea ? yReturn.getValueForPixel(y) : null
        };
        document.getElementById("chartValueY").style.display = "none";
        document.getElementById("chartValueReturn").style.display = "none";
        chart.draw();
    }, beforeDatasetsDraw: (chart, args, opts) => {
        const {ctx} = chart;
        const {top, bottom, left, right} = chart.chartArea;
        const {x, y, draw, yValue, yReturn} = chart.corsair;
        if (!draw) return;
        ctx.save();
        ctx.beginPath();
        ctx.lineWidth = opts.width;
        ctx.strokeStyle = opts.color;
        ctx.setLineDash(opts.dash);
        ctx.moveTo(x, bottom);
        ctx.lineTo(x, top);
        ctx.moveTo(left, y);
        ctx.lineTo(right, y);
        ctx.stroke();

        if (yValue !== null) {
            let text = yValue.toFixed(0).toString().replace(/\B(?=(\d{3})+(?!\d))/g, " ");
            document.getElementById("chartValueY").style.display = "block";
            document.getElementById("chartValueY").style.top = y - 13;
            document.getElementById("chartValueY").innerHTML = text;
        } else {
            document.getElementById("chartValueY").style.display = "none";
        }

        if (yReturn !== null) {
            let text = yReturn.toFixed(2);
            document.getElementById("chartValueReturn").style.display = "block";
            if (text >= 0) {
                document.getElementById("chartValueReturn").style.background = "rgb(14 129 112)";
            } else {
                document.getElementById("chartValueReturn").style.background = "rgb(204,1,1)";
            }
            document.getElementById("chartValueReturn").style.top = y - 13;
            document.getElementById("chartValueReturn").innerHTML = text + "%";
        } else {
            document.getElementById("chartValueReturn").style.display = "none";
        }

        ctx.restore()
    }
}
console.log(dataSums)
let timeChart = document.getElementById('timeChart');
const myChart = new Chart(timeChart, {
    type: 'line',
    data: {
        labels: dates,
        datasets: [{
            type: 'line',
            label: '€',
            data: dataSums,
            fill: false,
            borderColor: '#3F83F8',
            tension: 0.1,
            yAxisID: 'money'
        }, {
            type: 'line', label: '%', data: vykonostReturns, fill: false, tension: 0.1, yAxisID: 'return', segment: {
                borderColor: ctx => up(ctx, 'rgb(48,180,54)') || down(ctx, 'rgb(192,75,75)'),
            },
        }]
    }, options: {
        responsive: true, maintainAspectRatio: true, interaction: {
            mode: 'nearest', axis: 'x', intersect: false
        }, plugins: {
            legend: {
                display: false
            }, corsair: {
                color: 'black',
            }
        }, scales: {
            money: {
                type: 'linear', display: true, position: 'left',
            }, return: {
                type: 'linear', display: true, position: 'right', grid: {
                    drawOnChartArea: false,
                },
            },
        }
    }, plugins: [plugin]
});

const bubbleTextPlugin = {
    id: 'bubbleTextPlugin',
    afterDatasetsDraw(chart) {
        const ctx = chart.ctx;
        const datasets = chart.data.datasets;

        datasets.forEach((dataset, i) => {
            const meta = chart.getDatasetMeta(i);

            meta.data.forEach((bubble, index) => {
                if (bubble.options.radius > 2) {
                    const {x, y} = bubble.getCenterPoint();
                    const text = dataset.label;

                    ctx.save();
                    ctx.font = '12px Arial';
                    ctx.fontWeight = '800';
                    ctx.textAlign = 'center';
                    ctx.textBaseline = 'middle';
                    ctx.fillStyle = 'black';

                    ctx.fillText(text, x, y);
                    ctx.restore();
                }
            });
        });
    }
};

console.log(dates);
let bumChart = document.getElementById('bumChart');
const bumChartSSS = new Chart(bumChart, {
    type: 'bubble',
    data: {
        labels: dates,
        datasets: [{
            label: 'D',
            data: [],
            tension: 0.1,
        },
            {
                label: "sumy",
                data: [],
                hidden: true,
                color: "#FFF",
            },
            {
                label: "datumiky",
                data: [],
                hidden: true,
            }
        ]
    },
    options: {
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            },
            corsair: {
                color: 'black',
            },
            tooltip: {
                enabled: false,
                position: 'nearest',
                yAlign: 'top',
                external: externalTooltipHandler,
            }
        },
        scales: {
            x: {
                display: false,
                type: 'category',
                position: 'bottom',
                grid: {
                    display: false,
                }
            },
            y: {
                display: false,
                beginAtZero: true,
                ticks: {
                    callback: function (value) {
                        return value === 0 ? '0' : '';
                    }
                },
                grid: {
                    display: false,
                }
            }
        }
    },
    plugins: [bubbleTextPlugin]
});

function updateGraph(dates, dataSums, returnDates, returnSums, returnReturn, datePoints, points, secondPoints, label) {
    /*console.log(dates.length);
    console.log(dataSums.length);
    console.log(returnReturn.length);
    console.log(points.length);*/
    bumChartSSS.data.labels = dates;
    myChart.data.labels = dates;
    myChart.data.datasets[0].data = dataSums;
    myChart.data.datasets[1].data = returnReturn;
    bumChartSSS.data.datasets[0].data = points;
    bumChartSSS.data.datasets[1].data = secondPoints;
    console.log(label);
    let labelo = "";
    switch (label) {
        case "dividenda":
            labelo = "D";
            break;
        case "kupon":
            labelo = "C";
            break;
        case "urok":
            labelo = "I";
            break;
    }
    bumChartSSS.data.datasets[0].label = labelo;
    myChart.update();
    bumChartSSS.update();
}

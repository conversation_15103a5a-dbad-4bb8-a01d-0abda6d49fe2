<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

$portfoliaRes = Connection::getDataFromDatabase("SELECT po.* FROM podielnik p, portfolio po WHERE p.archiv = 'f' AND po.podielnikid = $clientID AND p.podielnikid = po.podielnikid 
ORDER BY po.fondid ASC", defaultDB);
$portfolia = $portfoliaRes[1];
?>
<section class="bg-white p-8 rounded-xl mb-10 shadow-md">
    <div class="flex justify-between items-center">
        <h2 class="text-3xl mb-4 text-gray-500 font-bold">Hodnota a výkonnosť majetku</h2>
        <div class="flex gap-6 mt-6 items-center mb-4">
            <h3 class="font-bold text-3xl">
                <?php echo number_format(end($dataObject["dataSums"]), 2, '.', ' ') . " " . $menaref ?></h3>
            <div
                class="flex items-center gap-2
             text-lg <?php echo round(array_sum($dataObject["vykonostPrecentualny"]), 2) > 0.00 ? 'bg-green-200 px-2 rounded-lg' : 'bg-red-200 px-2 rounded-lg' ?>">
                <?php if (round(array_sum($dataObject["vykonostPrecentualny"]), 2) > 0.00) { ?>
                    <svg class="w-5 h-5 text-green-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24"
                        height="24" fill="none" viewBox="0 0 24 24">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M4 4.5V19a1 1 0 0 0 1 1h15M7 14l4-4 4 4 5-5m0 0h-3.207M20 9v3.207" />
                    </svg>
                <?php } else { ?>
                    <svg class="w-5 h-5 text-red-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24"
                        height="24" fill="none" viewBox="0 0 24 24">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M4 4.5V19a1 1 0 0 0 1 1h15M7 10l4 4 4-4 5 5m0 0h-3.207M20 15v-3.207" />
                    </svg>
                <?php } ?>
                <span id="vykonnostSpan"
                    class="text-lg <?php echo round(array_sum($dataObject["vykonostPrecentualny"]), 2) > 0.00 ? 'text-green-500 bg-green-200 px-2 rounded-lg font-semibold' : 'text-red-500 bg-red-200 px-2 rounded-lg font-semibold' ?>"><?php echo round(array_sum($dataObject["vykonostPrecentualny"]), 2); ?>%</span>
            </div>
        </div>
    </div>
    <form id="graphControlsForm" method="get" action=""
        class="flex items-center justify-between bg-gray-50 p-2 px-3 rounded-xl border">
        <div class="inline-flex pr-4 border-r" role="group" style="border-right-width: 2px;">
            <button data-interval="1" type="submit" data-fondid="<?php print_r($id_fond); ?>"
                data-client="<?php echo $clientID; ?>"
                class="intervalButton py-1 px-2 text-sm selected font-medium bg-white text-gray-900 border border-blue-700 rounded-s-lg hover:bg-gray-100 hover:text-blue-700">
                1M
            </button>
            <button data-interval="3" type="submit" data-fondid="<?php print_r($id_fond); ?>"
                data-client="<?php echo $clientID; ?>"
                class="intervalButton py-1 px-2 text-sm bg-white font-medium text-gray-900 border border-gray-200 hover:bg-gray-100 hover:text-blue-700">
                3M
            </button>
            <button data-interval="6" type="submit" data-fondid="<?php print_r($id_fond); ?>"
                data-client="<?php echo $clientID; ?>"
                class="intervalButton py-1 px-2 text-sm bg-white font-medium text-gray-900 border border-gray-200 hover:bg-gray-100 hover:text-blue-700">
                6M
            </button>
            <button data-interval="12" type="submit" data-fondid="<?php print_r($id_fond); ?>"
                data-client="<?php echo $clientID; ?>"
                class="intervalButton py-1 px-2 text-sm bg-white font-medium text-gray-900 border border-gray-200 hover:bg-gray-100 hover:text-blue-700">
                1Y
            </button>
            <button data-interval="60" type="submit" data-fondid="<?php print_r($id_fond); ?>"
                data-client="<?php echo $clientID; ?>"
                class="intervalButton py-1 px-2 text-sm bg-white font-medium text-gray-900 border border-gray-200 hover:bg-gray-100 hover:text-blue-700">
                5Y
            </button>
            <div id="customRangeButton" type="submit"
                class="py-1 px-2 text-sm font-medium cursor-pointer text-gray-900 bg-white border border-gray-200 hover:bg-gray-100 rounded-e-lg ">
                <span id="customRangeSpan">Vlastný</span>
                <div id="fromToDate" class="px-1 gap-2 items-center rounded-lg" style="display: none">
                    <label for="datefrom">Od:</label>
                    <input type="date" class="bg-gray-200 selected text-xs bg-white border-gray-300 rounded-lg p-1 px-2"
                        name="datefrom" />
                    <label for="dateto">Do:</label>
                    <input type="date" class="bg-gray-200 selected text-xs bg-white border-gray-300 rounded-lg p-1 px-2"
                        name="dateto" />
                    <button type="submit" id="intervalButtonCheck"
                        class="intervalButton bg-green-200 rounded-md transition-all"
                        data-fondid="<?php print_r($id_fond); ?>" data-client="<?php echo $clientID; ?>">
                        <svg class="w-5 h-5 text-gray-800 dark:text-white" aria-hidden="true"
                            xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M5 11.917 9.724 16.5 19 7.5" />
                        </svg>
                    </button>
                    <button id="intervalButtonClose" type="button" class="bg-gray-300 p-0.5 transition-all rounded-md"
                        data-fondid="<?php print_r($id_fond); ?>" data-client="<?php echo $clientID; ?>">
                        <svg class="w-4 h-4 zavri" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24"
                            height="24" fill="none" viewBox="0 0 24 24">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M6 18 17.94 6M18 18 6.06 6" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>
        <section class="flex items-center gap-3">
            <button type="submit"
                class="bg-white portfolioButton border selected border-blue-700 rounded-lg p-1 px-2 text-xs font-semibold cursor-pointer hover:bg-gray-100 hover:text-blue-700"
                data-fondid="<?php print_r($id_fond); ?>" data-client="<?php echo $clientID; ?>">Všetky
            </button>
            <?php foreach ($portfolia as $porto) { ?>
                <button type="submit"
                    class="bg-white border portfolioButton rounded-lg p-1 px-2 text-xs font-semibold cursor-pointer hover:bg-gray-100 hover:text-blue-700"
                    data-fondid="<?php print_r($porto['fondid']); ?>"
                    data-client="<?php echo $clientID; ?>"><?php echo $porto["cislozmluvy"]; ?></button>
            <?php } ?>
        </section>
        <section class="flex gap-3 pl-3 border-l items-center" style="border-left-width: 2px;">
            <div id="dataPointsErr" class="bg-red-500 text-white font-semibold text-sm p-1 px-3 rounded-lg hidden">
            </div>
            <div class="inline-flex rounded-md shadow-sm" role="group">
                <button data-object="dividenda" type="submit" data-fondid="<?php print_r($id_fond); ?>"
                    data-client="<?php echo $clientID; ?>"
                    class="dataPointButton bg-white py-1 px-4 border rounded-l-lg text-sm font-medium text-gray-900 border-t border-r border-b border-gray-200 hover:bg-gray-100 hover:text-blue-700">
                    D
                </button>
                <button data-object="kupon" type="submit" data-fondid="<?php print_r($id_fond); ?>"
                    data-client="<?php echo $clientID; ?>"
                    class="dataPointButton bg-white py-1 px-4 text-sm font-medium text-gray-900 border border-gray-200 hover:bg-gray-100 hover:text-blue-700">
                    C
                </button>
                <button data-object="urok" type="submit" data-fondid="<?php print_r($id_fond); ?>"
                    data-client="<?php echo $clientID; ?>"
                    class="dataPointButton bg-white py-1 px-4 border-r rounded-r-lg text-sm font-medium text-gray-900 border border-gray-200 hover:bg-gray-100 hover:text-blue-700">
                    I
                </button>
            </div>
            <div id="resetDataPoints" class="cursor-pointer hidden hover:bg-gray-100 p-1 transition-all rounded-lg">
                <svg class="w-4 h-4 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                    width="24" height="24" fill="none" viewBox="0 0 24 24">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M6 18 17.94 6M18 18 6.06 6" />
                </svg>
            </div>
        </section>

    </form>
    <div class="grid grid-cols-8 relative gap-4" style="grid-template-columns: repeat(8, minmax(0, 1fr));">
        <div id="noDataOverlay"
            class="absolute bg-white z-20 text-2xl font-bold flex items-center justify-center w-full h-full"
            style="opacity: 0"></div>
        <div style="grid-column: span 1 / span 1;"></div>
        <div class="relative" style="grid-column: span 6 / span 6;">
            <div
                style="position: relative; display: flex; justify-content: center; align-items: center; max-width: 100%">
                <canvas id="timeChart" class=""></canvas>
                <p id="chartValueY"
                    class="hidden absolute bg-gray-900 text-xs font-bold text-white px-2 rounded-lg flex items-center mb-0 py-1"
                    style="left: 0rem;"></p>
                <p id="chartValueReturn"
                    class="hidden absolute text-xs font-bold text-white px-2 rounded-lg flex items-center mb-0 py-1"
                    style="right: 0;"></p>
            </div>
            <div style="height: 60px; width: 100%;padding: 0 1.4rem 0 1.9rem;position: absolute;bottom: 3.6rem;">
                <canvas id="bumChart" style="height: 30px"></canvas>
            </div>
        </div>
        <div style="grid-column: span 1 / span 1;"></div>
    </div>
</section>
<script>
    <?php
    $js_array = json_encode($dataObject["dates"]);
    echo "let dates = " . $js_array . ";\n";

    $dataSumsJS = json_encode($dataObject["dataSums"]);
    echo "let dataSums = " . $dataSumsJS . ";\n";

    $vykonostDates = json_encode($dataObject["vykonostDatums"]);
    echo "let vykonostDates = " . $vykonostDates . ";\n";

    $vykonostSums = json_encode($dataObject["vykonostSumy"]);
    echo "let vykonostSums = " . $vykonostSums . ";\n";

    $vykonostReturns = json_encode($dataObject["vykonostReturn"]);
    echo "let vykonostReturns = " . $vykonostReturns . ";\n";
    ?>
</script>
<script src="/src/Components/klient/lineChart/assets/lineChart.js"></script>
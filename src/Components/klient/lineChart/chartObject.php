<?php

/**
 * Line Chart for showing performance of a portfolio (or more) and is value throughout the time.
 **/

class LineChart
{
    public static function render(string $newdate, string $dbdate, $portfolia, $clientID): void
    {
        echo self::buildChart($newdate, $dbdate, $portfolia, $clientID);
    }

    public static function buildChart($newdate, $dbdate, $portfolia, $clientID): void
    {
        $id_fond = [];
        foreach ($portfolia as $portfolio) {
            $id_fond[] = $portfolio["fondid"];
        }
        $id_fond = implode(', ', $id_fond);
        $dataObject = self::getDataForChart($newdate, $dbdate, $portfolia, $clientID);
        include "chartFundation.php";
    }

    public static function getDataForChart($newdate, $dbdate, $portfolia, $clientID)
    {
        $dates = [];
        $dataSums = [];
        $timeData = [];
        $vykonost = [];
        $vykonnostPercent = [];
        foreach ($portfolia as $portfolio) {
            $fond = $portfolio["fondid"];
            $query = "SELECT datum, SUM(((0.5 - md_d) / 0.5 ) * sumadenom) as sum
                FROM majetoktotal
                WHERE datum BETWEEN '$newdate'::date AND '$dbdate' AND subjektid = $fond
                GROUP BY datum ORDER BY datum";
            $queryTime = Connection::getDataFromDatabase($query, defaultDB);
            if (!empty($queryTime[1])) {
                $timeData = array_merge($timeData, $queryTime[1]); // Appending data
            }

            $vykonostRes = Connection::getDataFromDatabase("select g.datum,
       trunc(g.suma_spolu, 3)::numeric                                            as suma,
       trunc(g.vklady_vybery, 3)::numeric                                         as vklady_vybery,
       g.mena::text,
       case
           when g.cumulative_return is null then 0
           else trunc(((g.cumulative_return - 1) * 100)::numeric, 4)::numeric end as cumulative_return
from (select f.datum,
             f.suma_spolu,
             f.vklad_vyber                                                                          as vklady_vybery,
             f.mena,
             trunc(((cumulative_mul(f.daily_return) over (order by f.datum)))::numeric, 4)::numeric as cumulative_return
      from (select a.datum,
                   sum(a.suma_spolu)                                                             as suma_spolu,
                   a.mena,
                   sum(coalesce(a.vklad_vyber, 0))                                               as vklad_vyber,
                   case
                       when sum(a.suma_spolu) = 0 then 1
                       else round(sum(a.daily_return * a.suma_spolu) / sum(a.suma_spolu), 6) end as daily_return
            from (select e.datum,
                         e.suma                     as suma_spolu,
                         e.mena,
                         coalesce(e.vklad_vyber, 0) as vklad_vyber,
                         case
                             when LAG(e.suma, 1) OVER (order by e.datum) = 0 then 1
                             when LAG(e.suma, 1) OVER (order by e.datum) is null and coalesce(e.vklad_vyber, 0) <> 0
                                 then
                                 coalesce(1 + ((e.suma + coalesce(e.vklad_vyber, 0)) /
                                               coalesce(LAG(e.suma, 1) OVER (order by e.datum), e.suma)), 1)
                             else coalesce(
                                     (e.suma + coalesce(e.vklad_vyber, 0)) / LAG(e.suma, 1) OVER (order by e.datum), 1)
                             end                    as daily_return
                  from (select ee.datum, sum(ee.suma) as suma, ee.mena, sum(vv) as vklad_vyber
                        from (select dd.datum, sum(dd.suma) as suma, dd.mena, 0 as vv
                              from (select a.datum                                      as datum,
                                           round(sum(a.suma/*/coalesce(k.kurz,1)*/), 2) as suma,
                                           a.mena
                                    from (select m.datum,
                                                 sum(m.sumaref * ((0.5 - md_d) / 0.5)) as suma,
                                                 m.menaref                             as mena
                                          from majetoktotal m
                                                   left join portfolio p on m.subjektid = p.fondid
                                          where p.podielnikid = $clientID
                                            and m.datum <= (case
                                                                when '$dbdate' is null
                                                                    then (select max(m.datum) from majetoktotal m)
                                                                else '$dbdate' end)
                                            and m.datum >= (case
                                                                when '$newdate' is null
                                                                    then (select max(m.datum) - interval '36 month' from majetoktotal m)
                                                                else '$newdate' end)
                                    
                                          group by m.menaref, m.datum) a
                                    group by a.datum, a.mena) dd
                              group by dd.datum, dd.mena

                              union all

                              select v.datumdat                                                       as datum,
                                     0                                                                as suma,
                                     rm.refmena                                                       as mena,
                                     round(v.suma / (coalesce(k1.kurz, 1) / coalesce(k2.kurz, 1)), 2) as vv
                              from vklady_vybery_goldmann($clientID, (case
                                                                         when '$dbdate' is null
                                                                             then (select max(m.datum) from majetoktotal m)
                                                                         else '$dbdate' end),
                                                          (case
                                                               when '$newdate' is null
                                                                   then (select max(m.datum) - interval '36 month' from majetoktotal m)
                                                               else '$newdate' end)::date, null) v
                                       left join (select f.refmena
                                                  from fonds f
                                                           left join portfolio p on f.fondid = p.fondid
                                                  where p.podielnikid = $clientID
                                                  group by f.refmena) rm on 1 = 1
                                       left join kurzyaktivarchiv k1
                                                 on 'EUR' || v.mena = k1.ric and v.datumdat = k1.datum
                                                     and k1.datum >= (case
                                                                          when '$newdate' is null
                                                                              then (select max(m.datum) - interval '36 month' from majetoktotal m)
                                                                          else '$newdate' end)
                                       left join kurzyaktivarchiv k2
                                                 on 'EUR' || rm.refmena = k2.ric and v.datumdat = k2.datum
                                                     and k2.datum >= (case
                                                                          when '$newdate' is null
                                                                              then (select max(m.datum) - interval '36 month' from majetoktotal m)
                                                                          else '$newdate' end)) ee
                        group by ee.datum, ee.mena) e) a
            group by a.datum, a.mena) f) g
order by g.datum", defaultDB);
            if (!empty($queryTime[1])) {
                $vykonost = array_merge($vykonost, $vykonostRes[1]);
            }
            $query = "
			select c.datum,
                c.mena,
                c.suma,
                round(100 * (exp(c.lnzhod) - 1), 4) AS kumzhod,
                round(100 * (power(exp(c.lnzhod), 
                    EXTRACT(YEAR FROM c.rok - c.obdobie) 
                    + EXTRACT(DAY FROM c.rok - c.obdobie) / 365.0
                ) - 1), 4) AS kumzhodpa,
                c.priemobjem
			from (
				select b.datum,b.mena,b.suma,b.pohyby,sum(
                            round(
                                ln(
                                    NULLIF(b.suma / NULLIF(b.lagsuma + b.pohyby, 0), 0)
                                ), 
                                8
                            )
                        ) OVER (ORDER BY b.datum) AS lnzhod,
						(to_date('$dbdate','yyyy-mm-dd') -to_date('$newdate','yyyy-mm-dd')) as obdobie,
						to_date(extract(year from to_date('2024-09-03','yyyy-mm-dd'))||'-12-31','yyyy-mm-dd') as rok,
						round(avg(b.suma) over (order by b.datum),2) as priemobjem
				from(
					select a.datum,a.suma*coalesce(k.kurz,1) as suma,a.menaref as mena,COALESCE(v.suma*coalesce(k.kurz,1),0) as pohyby,lag(a.suma*coalesce(k.kurz,1),1) over (order by a.datum ) as lagsuma
					from (		
						select round(sum(mt.sumaref*((0.5-mt.md_d)/0.5)/COALESCE(ku.kurz,1)),2) as suma,mt.datum,'EUR' as mena,mt.menaref 
						from majetoktotal mt
						left join kurzyaktivarchiv ku on mt.datum=ku.datum and ku.ric='EUR'||mt.menaref
						where mt.subjektid in ($fond) and mt.datum>=to_date('$newdate','yyyy-mm-dd') and mt.datum<=to_date('$dbdate','yyyy-mm-dd')
						group by mt.datum,mt.menaref
					) a
					left join (
						select aa.mena,sum(aa.suma) as suma,aa.datum
						from (
							select 	ob.mena as mena,(ob.suma*((0.5-ma.md_d)/0.5)) as suma,ob.obratdatetime as datum
							from obratybu ob, obratybuobratid obo, majetokarchiv ma
							where ob.id = obo.id and ma.obratid = obo.obratid and ma.subjektid in ($fond) and ma.kodobratu = 201
								and ma.uctovnykod = 325300 and ob.logactivityid = 15 and ob.obratdatetime >= to_date('$newdate','yyyy-mm-dd')
								and ob.obratdatetime <= to_date('$dbdate','yyyy-mm-dd') and ob.krdb = 1					
							union all
							select  u.mena as mena,(u.suma*((0.5-ma.md_d)/0.5)) as suma,u.datesplatnost as datum
							from uhrada u, uhradaobratid ui, majetokarchiv ma
							where u.kodobratu = 303 and u.id = ui.id and ma.obratid = ui.obratid and ma.subjektid in ($fond)
								and ma.kodobratu = 303 and ma.uctovnykod = 261930 and logactivityid = 15
								and datesplatnost >= to_date('$newdate','yyyy-mm-dd')
								and datesplatnost <= to_date('$dbdate','yyyy-mm-dd')
							union all
							select'EUR' as mena, -round(k.suma/COALESCE(ku.kurz,1),2) as suma, k.datum_zauctovania as datum
							from konfirmaciapp k
							left join kurzyaktivarchiv ku on k.datum_zauctovania=ku.datum and ku.ric='EUR'||k.mena
							where k.druhobchodu = 'vyber' and k.subjektid in ($fond) and k.logactivityid = 12
								and k.datum_zauctovania >= to_date('$newdate','yyyy-mm-dd') and k.datum_zauctovania <= to_date('$dbdate','yyyy-mm-dd')
							union all	
							select 'EUR' as mena, round(k.suma/COALESCE(ku.kurz,1),2) as suma, k.datum_zauctovania as datum
							from konfirmaciapp k
							left join kurzyaktivarchiv ku on k.datum_zauctovania=ku.datum and ku.ric='EUR'||k.mena
							where k.druhobchodu = 'vklad' and k.subjektid in ($fond) and k.logactivityid = 12
								and k.datum_zauctovania >= to_date('$newdate','yyyy-mm-dd') and k.datum_zauctovania <= to_date('$dbdate','yyyy-mm-dd')
							union all	
							select 'EUR' as mena,round(a.suma/COALESCE(ku.kurz,1),2) as suma,a.datum
							from (
								SELECT 
                                    kcp.currencyidtrade AS mena,
                                    (rcp.transsuma * 
                                        CASE 
                                            WHEN kcp.druhobchodu IN ('vklad', 'vklad-pr') THEN 1 
                                            WHEN kcp.druhobchodu IN ('vyber', 'vyber-pr') THEN -1 
                                            ELSE 0 
                                        END
                                    ) AS suma,
                                    rcp.datvysporiadaniamureal AS datum
                                FROM 
                                    konfirmaciacp kcp
                                JOIN 
                                    rekonfirmaciacp rcp ON kcp.dealid = rcp.dealid
                                JOIN 
                                    rekonfirmaciacpobratid rcpo ON rcpo.dealid = rcp.dealid AND rcpo.tranza = rcp.tranza
                                JOIN 
                                    (
                                        SELECT obratid, obratdatatimezauctovane, kodaktiva
                                        FROM majetoktoday
                                        WHERE obratid > 0 
                                          AND uctovnykod IN (251110, 251200, 251300)
                                          AND obratdatatimezauctovane >= to_date('$newdate', 'yyyy-mm-dd')
                                          AND obratdatatimezauctovane <= to_date('$dbdate', 'yyyy-mm-dd')
                                          AND destinacia NOT LIKE 'muprevod'
                                        UNION ALL
                                        SELECT obratid, obratdatatimezauctovane, kodaktiva
                                        FROM majetokarchiv
                                        WHERE obratid > 0 
                                          AND uctovnykod IN (251110, 251200, 251300)
                                          AND obratdatatimezauctovane >= to_date('$newdate', 'yyyy-mm-dd')
                                          AND obratdatatimezauctovane <= to_date('$dbdate', 'yyyy-mm-dd')
                                          AND destinacia NOT LIKE 'muprevod'
                                    ) maj ON maj.obratid = rcpo.obratid
                                WHERE 
                                    subjektid IN ($fond)
                                    AND kcp.druhobchodu IN ('vklad', 'vyber', 'vklad-pr', 'vyber-pr')
							) a
							left join kurzyaktivarchiv ku on a.datum=ku.datum and ku.ric='EUR'||a.mena
							union all
							select 'EUR' as mena,round(a.suma/COALESCE(ku.kurz,1),2) as suma,a.datum
							from (
								select 	k1.mena as mena,-k1.suma as suma, k1.datum_zauctovania as datum
									from konfirmaciapp k1
									where k1.dealid_related is not null and k1.subjektid in ($fond) and k1.logactivityid = 12 and k1.druhobchodu in ('prevod')
										and k1.datum_zauctovania >= to_date('$newdate','yyyy-mm-dd') and k1.datum_zauctovania <= to_date('$dbdate','yyyy-mm-dd')
								union all
								select 	k1.mena as mena,k1.suma as suma, k1.datum_zauctovania as datum
								from konfirmaciapp k1
								where k1.dealid_related is null and k1.subjektid in ($fond) and k1.logactivityid = 12 and k1.druhobchodu in ('prevod')
									and k1.datum_zauctovania >= to_date('$newdate','yyyy-mm-dd') and k1.datum_zauctovania <= to_date('$dbdate','yyyy-mm-dd')
							) a
							left join kurzyaktivarchiv ku on a.datum=ku.datum and ku.ric='EUR'||a.mena 			
						) aa
						group by aa.mena,aa.datum
					) v on a.datum=v.datum
					left join kurzyaktivarchiv k on a.datum=k.datum and k.ric='EUR'||a.menaref
				) b
			) c	
			where c.datum=to_date('$dbdate','yyyy-mm-dd')	
	        ";
            $getVykonnost = Connection::getDataFromDatabase($query, defaultDB);
            if (!empty($getVykonnost[1])) {
                $vykonnostPercent = array_merge($vykonnostPercent, $getVykonnost[1]);
            }
        }
        $vykonostDatums = [];
        $vykonostSumy = [];
        $vykonostReturn = [];
        $vykonnostPrecentualny = [];

        foreach ($vykonost as $datum) {
            $vykonostDatums[] = $datum["datum"];
            $vykonostSumy[] = $datum["suma"];
            $vykonostReturn[] = $datum["cumulative_return"];
        }

        foreach ($timeData as $datum) {
            $dates[] = $datum["datum"];
            $dataSums[] = $datum["sum"];
        }

        foreach ($vykonnostPercent as $datum) {
            $vykonnostPrecentualny[] = $datum["kumzhod"];
        }

        return ["vykonostDatums" => $vykonostDatums, "vykonostSumy" => $vykonostSumy, "vykonostReturn" => $vykonostReturn, "dates" => $dates, "dataSums" => $dataSums, "vykonostPrecentualny" => $vykonnostPrecentualny];
    }
}
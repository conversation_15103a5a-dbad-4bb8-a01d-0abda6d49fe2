<section class="bg-gray-50 dark:bg-gray-800 p-10 py-5" style="margin-bottom: 8rem">
    <h3 class="text-3xl mb-4 font-bold dark:text-white"><PERSON><PERSON><PERSON><PERSON></h3>
    <div class="mx-auto">
        <div class="relative bg-white shadow-md dark:bg-gray-800 sm:rounded-lg">
            <div class="relative bg-white dark:bg-gray-900">
                <div
                    class="flex flex-col items-center justify-between p-4 space-y-3 md:flex-row md:space-y-0 md:space-x-4">
                    <div class="w-full md:w-1/2">
                        <form id="searchFrom" class="flex mb-0 items-center">
                            <label for="simple-search" class="sr-only">Vyhľadávanie</label>
                            <div class="relative flex items-center w-full">
                                <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                    <svg aria-hidden="true" class="w-5 h-5 text-gray-500 dark:text-gray-400"
                                        fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                        <path fill-rule="evenodd"
                                            d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                                            clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <input type="text" id="searchBar" name="search"
                                    class="block w-full p-2 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                    placeholder="Vyhľadávanie...">
                                <div id="resetSearch"
                                    class="absolute hidden text-gray-500 hover:text-gray-900 transition-all cursor-pointer"
                                    style="right: .7rem">
                                    <svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                                        width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                                        <path fill-rule="evenodd"
                                            d="M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10-4.477 10-10 10S2 17.523 2 12Zm7.707-3.707a1 1 0 0 0-1.414 1.414L10.586 12l-2.293 2.293a1 1 0 1 0 1.414 1.414L12 13.414l2.293 2.293a1 1 0 0 0 1.414-1.414L13.414 12l2.293-2.293a1 1 0 0 0-1.414-1.414L12 10.586 9.707 8.293Z"
                                            clip-rule="evenodd" />
                                    </svg>

                                </div>
                            </div>
                        </form>
                    </div>
                    <div
                        class="flex flex-col items-stretch justify-end flex-shrink-0 w-full space-y-2 md:w-auto md:flex-row md:space-y-0 md:items-center md:space-x-3">
                        <a href="/klienti/create-new" type="button"
                            class="flex items-center justify-center px-4 py-2 text-sm font-medium text-white rounded-lg bg-blue-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 focus:outline-none dark:focus:ring-primary-800">
                            <svg class="h-3.5 w-3.5 mr-2" fill="currentColor" viewbox="0 0 20 20"
                                xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                                <path clip-rule="evenodd" fill-rule="evenodd"
                                    d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" />
                            </svg>
                            Pridať klienta
                        </a>
                        <div class="flex items-center w-full space-x-3 md:w-auto">
                            <button id="archivedDropdown" data-dropdown-toggle="actionsDropdown"
                                class="flex items-center justify-center w-full px-4 py-2 text-sm font-medium text-gray-900 bg-white border border-gray-200 rounded-lg md:w-auto focus:outline-none hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                                type="button">
                                <svg class="-ml-1 mr-1.5 w-5 h-5" fill="currentColor" viewbox="0 0 20 20"
                                    xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                                    <path clip-rule="evenodd" fill-rule="evenodd"
                                        d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" />
                                </svg>
                                <span id="archiveFilterText">Zobraziť archivovaných</span>
                            </button>
                            <div id="actionsDropdown"
                                class="z-10 hidden bg-white divide-y divide-gray-100 rounded shadow w-44 cursor-pointer">
                                <ul class="py-1 text-sm text-gray-700 dark:text-gray-200"
                                    aria-labelledby="actionsDropdownButton">
                                    <li id="isArchived"
                                        class="block px-4 cursor-pointer py-2 transition-all hover:bg-gray-100">
                                        Áno
                                    </li>
                                    <li id="noArchived"
                                        class="block px-4 cursor-pointer bg-green-200 font-bold py-2 transition-all">
                                        Nie
                                    </li>
                                </ul>
                            </div>
                            <button id="filterDropdownButton" data-dropdown-toggle="filterDropdown"
                                class="flex items-center justify-center w-full px-4 py-2 text-sm font-medium text-gray-900 bg-white border border-gray-200 rounded-lg md:w-auto focus:outline-none hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                                type="button">
                                <svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" id="filterIcon"
                                    class="w-4 h-4 mr-2 text-gray-400" viewbox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd"
                                        d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z"
                                        clip-rule="evenodd" />
                                </svg>
                                <span id="filterText">Filtrovať</span>
                                <svg class="-mr-1 ml-1.5 w-5 h-5" fill="currentColor" viewbox="0 0 20 20"
                                    xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                                    <path clip-rule="evenodd" fill-rule="evenodd"
                                        d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" />
                                </svg>
                            </button>
                            <div id="filterDropdown" style="min-width: 40vw;"
                                class="z-10 hidden p-3 bg-white rounded-lg shadow">
                                <form id="filterForm" class="flex w-full gap-2">
                                    <div class="w-full">
                                        <h6 class="mb-3 text-sm font-bold border-b pb-2 text-gray-900 dark:text-white">
                                            Podľa skupiny
                                        </h6>
                                        <ul class="space-y-2 max-h-72 overflow-y-scroll text-sm"
                                            aria-labelledby="dropdownDefault">
                                            <?php foreach ($skupiny[1] as $skupina) { ?>
                                                <li class="flex items-center">
                                                    <label for="group-<?php echo $skupina['skupina']; ?>"
                                                        class="ml-2 text-sm font-medium text-gray-900 flex items-center gap-2 dark:text-gray-100">
                                                        <input id="group-<?php echo $skupina['skupina']; ?>" name="group"
                                                            type="checkbox" value="<?php echo $skupina['skupina']; ?>"
                                                            class="filter-checkbox w-4 h-4 bg-gray-100 border-gray-300 rounded text-primary-600 focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-700 focus:ring-2 dark:bg-gray-600 dark:border-gray-500" />
                                                        <span><?php echo $skupina["skupina"]; ?></span>
                                                    </label>
                                                </li>
                                            <?php } ?>
                                        </ul>
                                    </div>
                                    <div class="w-full">
                                        <h6 class="mb-3 text-sm font-bold border-b pb-2 text-gray-900 dark:text-white">
                                            Podľa mesta
                                        </h6>
                                        <ul class="space-y-2 max-h-72 overflow-y-scroll text-sm"
                                            aria-labelledby="dropdownDefault">
                                            <?php foreach ($mesta[1] as $mesto) { ?>
                                                <li class="flex items-center">
                                                    <label for="city-<?php echo $mesto['city']; ?>"
                                                        class="ml-2 text-sm font-medium text-gray-900 flex items-center gap-2 dark:text-gray-100">
                                                        <input id="city-<?php echo $mesto['city']; ?>" name="city"
                                                            type="checkbox" value="<?php echo $mesto['city']; ?>"
                                                            class="filter-checkbox w-4 h-4 bg-gray-100 border-gray-300 rounded text-primary-600 focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-700 focus:ring-2 dark:bg-gray-600 dark:border-gray-500" />
                                                        <?php echo $mesto["city"]; ?>
                                                    </label>
                                                </li>
                                            <?php } ?>
                                        </ul>
                                    </div>
                                </form>
                                <div class="w-full flex items-center justify-end">
                                    <button id="resetFilter" type="button"
                                        class="px-3 hidden py-2 text-xs font-medium text-center text-white bg-blue-700 rounded-lg hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                                        Zrušiť filter <b>X</b></button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="">
                <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                        <tr>
                            <th scope="col" class="px-4 py-3">Meno</th>
                            <th scope="col" class="px-4 py-3">Portfólia</th>
                            <th scope="col" class="px-4 py-3">Mesto</th>
                            <th scope="col" class="px-4 py-3">Adresa</th>
                            <th scope="col" class="px-4 py-3 archivedCol hidden">Archivovaný</th>
                            <th scope="col" class="px-4 py-3">Možnosti</th>
                        </tr>
                    </thead>
                    <tbody id="klientiTableBody">
                        <?php
                        foreach ($podielnici as $podienik) {
                            ?>
                            <tr class="border-b dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-600">
                                <td class="px-4 py-2">
                                    <span
                                        class="text-sm text-gray-900 dark:text-gray-200 font-bold px-2 py-0.5 rounded"><?php echo $podienik['titulpred'] . " " . $podienik["meno"] . " " . $podienik["prieznaz"] . " " . $podienik["titulza"] ?></span>
                                </td>
                                <td class="px-4 py-2 flex flex-wrap items-center gap-2">
                                    <?php
                                    if (count($podienik['cislozmluvy']) > 0) {
                                        foreach ($podienik['cislozmluvy'] as $cislozmluvy) {
                                            if ($cislozmluvy == "")
                                                continue;
                                            ?>
                                            <span hx-get="/portfolia/detail/<?php echo $cislozmluvy ?>" hx-target="#pageContentMain"
                                                hx-replace-url="true" hx-push-url="true" preload="always mouseover"
                                                hx-trigger="click" hx-boost="true" class="bg-blue-100 text-blue-800 text-xs font-medium me-2 px-2.5 py-0.5 rounded-sm hover:dark:bg-blue-700 transition-all cursor-pointer
                                                 dark:bg-blue-900 dark:text-blue-300"><?php echo $cislozmluvy ?></span>
                                        <?php }
                                    } ?>
                                </td>
                                <td class="px-4 py-2 font-medium text-gray-900 whitespace-nowrap dark:text-white"><?php
                                if ($podienik["kontaktcity"] !== null) {
                                    echo $podienik["kontaktcity"];
                                } else {
                                    echo $podienik["city"];
                                }
                                ?></td>
                                <td class="px-4 py-2 font-medium text-gray-900 whitespace-nowrap dark:text-white"><?php
                                if ($podienik["kontaktaddress"] !== null) {
                                    echo $podienik["kontaktaddress"];
                                } else {
                                    echo $podienik["address"];
                                }
                                ?></td>
                                <td
                                    class="px-4 py-2 hidden archivedCol hidden font-medium text-success-900 gap-2 flex items-center justify-center whitespace-nowrap dark:text-white">
                                    <?php
                                    if ($podienik["archiv"] === "t") {
                                        ?>
                                        <svg class="w-5 h-5 text-green-600" aria-hidden="true"
                                            xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor"
                                            viewBox="0 0 24 24">
                                            <path fill-rule="evenodd"
                                                d="M20 10H4v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8ZM9 13v-1h6v1a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1Z"
                                                clip-rule="evenodd" />
                                            <path d="M2 6a2 2 0 0 1 2-2h16a2 2 0 1 1 0 4H4a2 2 0 0 1-2-2Z" />
                                        </svg> <span>Archivovaný</span>
                                        <?php
                                    }
                                    ?>
                                </td>
                                <td class="px-4 py-2 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                    <div class="flex items-center gap-4">
                                        <a href="/klienti/detail/<?php echo $podienik["podielnikid"]; ?>">
                                            <button data-tooltip-target="view-tooltip"
                                                class="p-2 cursor-pointer hover:bg-gray-300 transition-all rounded-lg">
                                                <svg class="w-5 h-5 text-gray-800 dark:text-white" aria-hidden="true"
                                                    xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none"
                                                    viewBox="0 0 24 24">
                                                    <path stroke="currentColor" stroke-width="2"
                                                        d="M21 12c0 1.2-4.03 6-9 6s-9-4.8-9-6c0-1.2 4.03-6 9-6s9 4.8 9 6Z" />
                                                    <path stroke="currentColor" stroke-width="2"
                                                        d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" />
                                                </svg>
                                            </button>
                                        </a>
                                        <div id="view-tooltip" role="tooltip"
                                            class="absolute z-10 invisible inline-block px-3 py-2 text-sm font-medium text-white transition-opacity duration-300 bg-gray-900 rounded-lg shadow-sm opacity-0 tooltip dark:bg-gray-700">
                                            Zobraziť
                                            <div class="tooltip-arrow" data-popper-arrow></div>
                                        </div>
                                        <a href="/klienti/edit/<?php echo $podienik["podielnikid"]; ?>">
                                            <button data-tooltip-target="edit-tooltip"
                                                class="p-2 cursor-pointer hover:bg-gray-300 transition-all rounded-lg">
                                                <svg class="w-5 h-5 text-gray-800 dark:text-white" aria-hidden="true"
                                                    xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                    fill="currentColor" viewBox="0 0 24 24">
                                                    <path fill-rule="evenodd"
                                                        d="M5 8a4 4 0 1 1 7.796 1.263l-2.533 2.534A4 4 0 0 1 5 8Zm4.06 5H7a4 4 0 0 0-4 4v1a2 2 0 0 0 2 2h2.172a2.999 2.999 0 0 1-.114-1.588l.674-3.372a3 3 0 0 1 .82-1.533L9.06 13Zm9.032-5a2.907 2.907 0 0 0-2.056.852L9.967 14.92a1 1 0 0 0-.273.51l-.675 3.373a1 1 0 0 0 1.177 1.177l3.372-.675a1 1 0 0 0 .511-.273l6.07-6.07a2.91 2.91 0 0 0-.944-4.742A2.907 2.907 0 0 0 18.092 8Z"
                                                        clip-rule="evenodd" />
                                                </svg>
                                            </button>
                                        </a>
                                        <div id="edit-tooltip" role="tooltip"
                                            class="absolute z-10 invisible inline-block px-3 py-2 text-sm font-medium text-white transition-opacity duration-300 bg-gray-900 rounded-lg shadow-sm opacity-0 tooltip dark:bg-gray-700">
                                            Upraviť
                                            <div class="tooltip-arrow" data-popper-arrow></div>
                                        </div>
                                        <button data-tooltip-target="archive-tooltip"
                                            class="p-2 cursor-pointer hover:bg-gray-300 transition-all rounded-lg">
                                            <svg class="w-5 h-5 text-gray-800 dark:text-white" aria-hidden="true"
                                                xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none"
                                                viewBox="0 0 24 24">
                                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                                    stroke-width="2"
                                                    d="M12 11v5m0 0 2-2m-2 2-2-2M3 6v1a1 1 0 0 0 1 1h16a1 1 0 0 0 1-1V6a1 1 0 0 0-1-1H4a1 1 0 0 0-1 1Zm2 2v10a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V8H5Z" />
                                            </svg>
                                        </button>
                                        <div id="archive-tooltip" role="tooltip"
                                            class="absolute z-40 invisible inline-block px-3 py-2 text-sm font-medium text-white transition-opacity duration-300 bg-gray-900 rounded-lg shadow-sm opacity-0 tooltip dark:bg-gray-700">
                                            Archivovať
                                            <div class="tooltip-arrow" data-popper-arrow></div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <?php
                        }
                        ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</section>
<script>
    (function () {
        let typingTimer;
        const doneTyping = 500;

        $('#searchBar').on('keyup', (e) => {
            if (e.currentTarget.value !== "") {
                $('#resetSearch').removeClass('hidden')
            } else {
                $('#resetSearch').addClass('hidden')
            }
            clearTimeout(typingTimer);
            typingTimer = setTimeout(() => {
                $('#searchFrom').submit();
            }, doneTyping);
        });

        $('#searchBar').on('keydown', () => {
            clearTimeout(typingTimer);
        });

        $('#searchFrom').on('submit', (e) => {
            e.preventDefault();
            const formData = new FormData(e.currentTarget)
            const searchQuery = formData.get("search");
            console.log(searchQuery);
            if (searchQuery !== "") {
                searchEntered = true;
            }
            htmx.ajax('POST', `/src/Controllers/klienti/searchFilter.php`, {
                target: '#klientiTableBody',
                values: {
                    "search": searchQuery
                }
            });
        });
    })();
</script>
<!--<script src="/src/assets/js/klienti/table.js"></script>-->
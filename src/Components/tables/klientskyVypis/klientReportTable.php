<?php

class klientReportTable
{
    public static function render(array|null $data, array $columns, string $tableHeading, string $additionalParameter, string $tableDesc, array $linkData, array $klientLinkData, string $sumaAlert): void
    {
        if ($data !== null) {
            echo self::buildTable($data, $columns, $tableHeading, $additionalParameter, $tableDesc, $linkData, $klientLinkData, $sumaAlert);
        }
    }

    public static function buildTable(array $data, array $columns, string $tableHeading, string $additionalParameter, string $tableDesc, array $linkData, array $klientLinkData, $sumaAlert): string
    {
        $table = '
            <div class="relative mb-10 overflow-x-auto shadow-md sm:rounded-lg">
                <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
        ';
        $table .= self::buildTableHeader($tableHeading, $additionalParameter, $tableDesc, $linkData, $klientLinkData, $columns);
        $table .= self::buildTableBody($data);
        $table .= '
           </table>
        ';
        if ($sumaAlert !== "") {
            $table .= '<table class="w-full">
                        <tr>
                            <td class="text-2xl font-bold flex items-center bg-red-300 gap-4 text-red-700 p-3">
                                ' . $sumaAlert . '
                            </td>
                        </tr>
                    </table>
                    </div>';
        } else {
            $table .= '</div>';
        }
        return $table;
    }

    public static function buildTableHeader(string $tableHeading, string $additionalParameter, string $tableDesc, array $linkData, array $klientLinkData, $columns): string
    {
        $tableHeader = '
            <caption
                class="p-5 text-xl font-semibold text-left rtl:text-right text-gray-900 bg-white dark:text-white dark:bg-gray-800">
            <section class="flex w-full justify-between items-center p-1">
                <div>
                    ' . $tableHeading . ' ' . ($additionalParameter !== "" ? "($additionalParameter)" : "") . '
                    <p class="mt-1 text-sm font-normal text-gray-500 dark:text-gray-400"><span
                                class="flex items-center gap-2">' . $tableDesc . '
                            <a href="' . $linkData["url"] . '"
                               class="bg-gray-500 flex items-center gap-1 hover:underline transition-all text-white py-0.5 px-2 rounded-lg"><span>' . $linkData["cislozmluvy"] . '</span>
                            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24"
                                 height="24" fill="none" viewBox="0 0 24 24">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                      stroke-width="2"
                                      d="M18 14v4.833A1.166 1.166 0 0 1 16.833 20H5.167A1.167 1.167 0 0 1 4 18.833V7.167A1.166 1.166 0 0 1 5.167 6h4.618m4.447-2H20v5.768m-7.889 2.121 7.778-7.778"/>
                            </svg>
                        </a>
                        </span>
                    </p>
                </div>
                <a href="' . $klientLinkData["klientPodielnikid"] . '"
                   class="flex gap-3 hover:bg-gray-100 transition-all p-2 rounded-lg cursor-pointer">
                    <div class="relative w-10 h-10 overflow-hidden bg-gray-100 rounded-full dark:bg-gray-600">
                        <svg class="absolute w-12 h-12 text-gray-400 -left-1" fill="currentColor" viewBox="0 0 20 20"
                             xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                                  clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div>
                        <span class="text-sm">' . $klientLinkData["klient"] . '</span>
                        <p class="text-xs text-gray-400">' . $klientLinkData["klientMail"] . '</p>
                    </div>
                </a>
            </section>
        </caption>
        <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
        ';
        foreach ($columns as $columnCategory) {
            $tableHeader .= '<tr class="border-b">';
            foreach ($columnCategory as $column) {
                $tableHeader .= '<th scope="col" class="px-6 py-3">' . $column . '</th>';
            }
            $tableHeader .= '</tr>';
        }
        $tableHeader .= '</thead>';
        return $tableHeader;
    }

    public static function buildTableBody(array $rows): string
    {
        //print_r($rows);
        $tableBody = '<tbody>';
        foreach ($rows as $row) {
            $tableBody .= '
                <tr class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b dark:border-gray-700">';
            foreach ($row as $item) {
                $tableBody .= '<td class="px-6 py-4 font-semibold text-md">' . $item . '</td>';
            }
            $tableBody .= '</tr>';
        }
        $tableBody .= '</tbody>';
        return $tableBody;
    }
}
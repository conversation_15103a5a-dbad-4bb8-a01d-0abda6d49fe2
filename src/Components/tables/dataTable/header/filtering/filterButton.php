<button id="<?php echo $togglerType; ?>Toggler" hx-get="/api/poplatky/get/typy" hx-target="#dropdownTypyPoplatkovTarget"
    data-dropdown-toggle="dropdownTypyPoplatkov"
    class="p-1 filterToggler rounded-md hover:bg-gray-200 transition-all hover:dark:bg-gray-900">
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor"
        stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-filter">
        <polygon points="22 3 2 3 10 12.46 10 19 16 21 16 12.46 22 3" />
    </svg>
</button>
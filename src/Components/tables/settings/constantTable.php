<?php

class constantTable
{
    public static function render(array|null $data, array $columns, array|null $inputs, string $formID, string $modalEditID, string $modalHeading, array|null $rows): void
    {
        if ($data !== null) {
            echo self::buildTable($data, $columns, $inputs, $formID, $modalEditID, $modalHeading, $rows);
        }
    }

    public static function buildTable(array $data, array $columns, array $inputs, string $formID, string $modalEditID, string $modalHeading, array|null $rows): string
    {
        $table = '
            <div class="relative overflow-x-auto shadow-md mb-1 sm:rounded-lg">
                <div class="p-4 bg-white dark:bg-gray-900">
                    <label for="table-search" class="sr-only">Search</label>
                    <div class="relative mt-1">
                        <div class="absolute inset-y-0 rtl:inset-r-0 start-0 flex items-center ps-3 pointer-events-none">
                            <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"/>
                            </svg>
                        </div>
                        <input type="text" id="constantSearchTable" class="block pt-2 ps-10 text-sm text-gray-900 border border-gray-300 rounded-lg w-80 bg-gray-50 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" placeholder="Vyhľadávať">
                    </div>
                </div>
                <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
        ';
        $table .= self::buildTableHeader($columns);
        $table .= self::buildTableBody($data, $modalEditID, $inputs, $rows);
        $table .= '
           </table>
        ';
        $table .= '
                    </table>
                    </div>';
        $table .= self::buildModals($data, $inputs, $formID, $modalEditID, $modalHeading);
        return $table;
    }

    public static function buildTableHeader($columns): string
    {
        $tableHeader = '
           <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
        ';
        $tableHeader .= '<tr class="border-b">';
        foreach ($columns as $column) {
            $tableHeader .= '<th scope="col" class="px-6 py-3">' . $column . '</th>';
        }
        $tableHeader .= '</tr>';
        $tableHeader .= '</thead>';
        return $tableHeader;
    }

    public static function buildTableBody(array $rows, $modalEditID, array|null $inputs, array|null $riadky): string
    {
        $tableBody = '<tbody>';
        foreach ($rows as $row) {
            $tableBody .= '
                <tr class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b dark:border-gray-700">';
            foreach ($row as $key => $item) {
                if ($riadky === null) {
                    $tableBody .= '<td class="px-6 py-4">' . $item . '</td>';
                } else {
                    if (in_array($key, $riadky)) {
                        switch ($key) {
                            case "logged":
                                if ($item === true) {
                                    $tableBody .= '<td class="px-6 py-4"> 
                                    <span class="bg-green-100 text-green-800 w-full text-xs font-medium px-2.5 py-0.5 border border-green-500 rounded-full dark:bg-green-900 dark:text-green-300 inline-flex items-center gap-2"><svg class="w-5 h-5 text-green-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                                        <path fill-rule="evenodd" d="M12 20a7.966 7.966 0 0 1-5.002-1.756l.002.001v-.683c0-1.794 1.492-3.25 3.333-3.25h3.334c1.84 0 3.333 1.456 3.333 3.25v.683A7.966 7.966 0 0 1 12 20ZM2 12C2 6.477 6.477 2 12 2s10 4.477 10 10c0 5.5-4.44 9.963-9.932 10h-.138C6.438 21.962 2 17.5 2 12Zm10-5c-1.84 0-3.333 1.455-3.333 3.25S10.159 13.5 12 13.5c1.84 0 3.333-1.455 3.333-3.25S13.841 7 12 7Z" clip-rule="evenodd"/>
                                        </svg>
                                        Momentálne aktívny</span></td>';
                                } else {
                                    $tableBody .= '<td class="px-6 py-4"><span class="bg-gray-100 w-full text-gray-800 text-xs font-medium inline-flex items-center px-2.5 py-0.5 rounded-full me-2 dark:bg-gray-700 dark:text-gray-400 border border-gray-500 ">
                                    <svg class="w-3 h-3 me-1.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M10 0a10 10 0 1 0 10 10A10.011 10.011 0 0 0 10 0Zm3.982 13.982a1 1 0 0 1-1.414 0l-3.274-3.274A1.012 1.012 0 0 1 9 10V6a1 1 0 0 1 2 0v3.586l2.982 2.982a1 1 0 0 1 0 1.414Z"/>
                                    </svg>
                                    Naposledy aktívny: ' . ($row["lasttime"] !== null ? $row["lasttime"] : "neuvedené") . '
                                    </span></td>';
                                }

                                break;
                            default:
                                $tableBody .= '<td class="px-6 py-4">' . $item . '</td>';
                                break;
                        }

                    }
                }
            }
            $tableBody .= '<td class="px-6 py-4">
                        <div class="flex items-center gap-2">
                            <button data-modal-target="modal' . $row[$modalEditID] . '"
                                    data-modal-toggle="modal' . $row[$modalEditID] . '"
                                    class="font-medium p-1 rounded-lg hover:bg-blue-200 cursor-pointer transiton-all text-blue-600 dark:text-blue-500 hover:underline">
                                <svg class="w-4 h-4" aria-hidden="true"
                                     xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor"
                                     viewBox="0 0 24 24">
                                    <path fill-rule="evenodd"
                                          d="M11.32 6.176H5c-1.105 0-2 .949-2 2.118v10.588C3 20.052 3.895 21 5 21h11c1.105 0 2-.948 2-2.118v-7.75l-3.914 4.144A2.46 2.46 0 0 1 12.81 16l-2.681.568c-1.75.37-3.292-1.263-2.942-3.115l.536-2.839c.097-.512.335-.983.684-1.352l2.914-3.086Z"
                                          clip-rule="evenodd"/>
                                    <path fill-rule="evenodd"
                                          d="M19.846 4.318a2.148 2.148 0 0 0-.437-.692 2.014 2.014 0 0 0-.654-.463 1.92 1.92 0 0 0-1.544 0 2.014 2.014 0 0 0-.654.463l-.546.578 2.852 3.02.546-.579a2.14 2.14 0 0 0 .437-.692 2.244 2.244 0 0 0 0-1.635ZM17.45 8.721 14.597 5.7 9.82 10.76a.54.54 0 0 0-.137.27l-.536 2.84c-.07.37.239.696.588.622l2.682-.567a.492.492 0 0 0 .255-.145l4.778-5.06Z"
                                          clip-rule="evenodd"/>
                                </svg>
                            </button>';
            if (in_array(1, $_SESSION["user"]["permissions"])) {
                $tableBody .= '<button data-modal-target="deleteModal' . $row[$modalEditID] . '"
                                    data-modal-toggle="deleteModal' . $row[$modalEditID] . '"
                                    class="font-medium p-1 rounded-lg hover:bg-red-200 cursor-pointer transiton-all text-red-600 dark:text-blue-500 hover:underline">
                                <svg class="w-4 h-4" aria-hidden="true"
                                     xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor"
                                     viewBox="0 0 24 24">
                                    <path fill-rule="evenodd"
                                          d="M8.586 2.586A2 2 0 0 1 10 2h4a2 2 0 0 1 2 2v2h3a1 1 0 1 1 0 2v12a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V8a1 1 0 0 1 0-2h3V4a2 2 0 0 1 .586-1.414ZM10 6h4V4h-4v2Zm1 4a1 1 0 1 0-2 0v8a1 1 0 1 0 2 0v-8Zm4 0a1 1 0 1 0-2 0v8a1 1 0 1 0 2 0v-8Z"
                                          clip-rule="evenodd"/>
                                </svg>
                            </button>';
            }
            $tableBody .= '</div>
                    </td></tr>';
        }
        $tableBody .= '</tbody>';
        return $tableBody;
    }

    public static function buildModals(array $rows, array $inputs, string $formID, string $modalEditID, string $modalHeading): string
    {
        $tableModals = "";
        foreach ($rows as $row) {
            $tableModals .= '<div id="modal' . $row[$modalEditID] . '" tabindex="-1" aria-hidden="true"
             class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
            <div class="relative p-4 w-full max-w-2xl max-h-full">
                <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
                    <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
                        <h3 class="text-xl font-semibold text-gray-900 dark:text-white">Upraviť ' . $modalHeading . '</h3>
                        <button type="button"
                                class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
                                data-modal-hide="modal' . $row[$modalEditID] . '">
                            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                                 viewBox="0 0 14 14">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                      stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                            </svg>
                            <span class="sr-only">Close modal</span>
                        </button>
                    </div>
                    <div class="p-4 md:p-5 space-y-4">
                        <form id="' . $formID . '" class="updateForm" action="">
                            <div id="responseMsg" class="p-2 mb-4 px-4 rounded-lg font-bold"
                                 style="display: none"></div>
                            <div class="grid gap-4 mb-4 sm:grid-cols-2">';
            foreach ($inputs as $input) {
                if ($input["type"] !== "hidden") {
                    $tableModals .= '<div>';
                    $tableModals .= '<label for="' . $input["name"] . '"
                                           class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">' . $input["label"] . '</label>';

                    if ($input["type"] === "select") {
                        $tableModals .= '<select class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" name="' . $input["name"] . '" id="">';
                        foreach ($input["options"] as $option) {
                            if ($row[$input["name"]] === $option[$input["optionValue"]] || $row[$input["optionField"]] === $option[$input["optionField"]]) {
                                $tableModals .= '<option value="' . $option[$input["optionValue"]] . '" selected>' . $option[$input["optionField"]] . '</option>';
                            } else {
                                $tableModals .= '<option value="' . $option[$input["optionValue"]] . '">' . $option[$input["optionField"]] . '</option>';
                            }
                        }
                        $tableModals .= '</select></div>';
                    } else {
                        $tableModals .= '<input type="' . $input["type"] . '" name="' . $input["name"] . '" id="' . $input["name"] . '"
                                           value="' . $row[$input["name"]] . '"
                                           class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-600 focus:border-blue-600 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                                </div>';
                    }
                } else {
                    $tableModals .= '<input class="hiddenOne" type="hidden" value="' . $row[$input["name"]] . '" name="' . $input["name"] . '"/>';
                }
            }
            $tableModals .= '
                            </div>
                            <div class="flex items-center space-x-4">
                                <button type="submit" id="updateButton"
                                        class="text-white bg-blue-700 flex items-center gap-3 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                                    <span>Aktualizovať</span>
                                    <svg aria-hidden="true" id="spinnerUpdate"
                                         class="w-6 h-6 hidden text-gray-200 animate-spin dark:text-gray-600 fill-blue-600"
                                         viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                                              fill="currentColor"/>
                                        <path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                                              fill="currentFill"/>
                                    </svg>
                                </button>
                                <button type="button" data-modal-hide="modal' . $row[$modalEditID] . '"
                                        class="text-red-600 inline-flex items-center border border-red-600 hover:bg-red-700 focus:ring-4 focus:outline-none focus:ring-red-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:border-red-500 dark:text-red-500 dark:hover:text-white dark:hover:bg-red-600 dark:focus:ring-red-900">
                                    Zrušiť
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <div id="deleteModal' . $row[$modalEditID] . '" tabindex="-1" aria-hidden="true"
             class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
            <div class="relative p-4 w-full max-w-2xl max-h-full">
                <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
                    <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
                        <h3 class="text-xl font-semibold text-gray-900 dark:text-white">Chcete tento objekt naozaj
                            odstrániť?</h3>
                        <button type="button"
                                class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
                                data-modal-hide="deleteModal' . $row[$modalEditID] . '">
                            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                                 viewBox="0 0 14 14">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                      stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                            </svg>
                            <span class="sr-only">Close modal</span>
                        </button>
                    </div>
                    <div class="p-4 md:p-5 space-y-4">
                        <form id="' . $formID . '" class="deleteForm" action="">
                            <div id="responseMsg" class="p-2 mb-4 px-4 rounded-lg font-bold"
                                 style="display: none"></div>';
            foreach ($inputs as $input) {
                if ($input["type"] === "hidden") {
                    $tableModals .= '<input class="hiddenOne" type="hidden" value="' . $row[$input["name"]] . '" name="' . $input["name"] . '"/>';
                }
            }
            $tableModals .= '
                            <div class="flex items-center space-x-4">
                                <button type="submit" id="deleteButton"
                                        class="text-red-600 inline-flex items-center gap-2 border border-red-600 hover:bg-red-800 hover:text-white transition-all focus:ring-4 focus:outline-none focus:ring-red-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:border-red-500 dark:text-red-500 dark:hover:text-white dark:hover:bg-red-600 dark:focus:ring-red-900">
                                    <svg class="w-4 h-4" aria-hidden="true"
                                         xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor"
                                         viewBox="0 0 24 24">
                                        <path fill-rule="evenodd"
                                              d="M8.586 2.586A2 2 0 0 1 10 2h4a2 2 0 0 1 2 2v2h3a1 1 0 1 1 0 2v12a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V8a1 1 0 0 1 0-2h3V4a2 2 0 0 1 .586-1.414ZM10 6h4V4h-4v2Zm1 4a1 1 0 1 0-2 0v8a1 1 0 1 0 2 0v-8Zm4 0a1 1 0 1 0-2 0v8a1 1 0 1 0 2 0v-8Z"
                                              clip-rule="evenodd"/>
                                    </svg>
                                    <span>Odstrániť</span>
                                    <svg aria-hidden="true" id="spinnerUpdate"
                                         class="w-6 h-6 hidden text-gray-200 animate-spin dark:text-gray-600 fill-blue-600"
                                         viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                                              fill="currentColor"/>
                                        <path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                                              fill="currentFill"/>
                                    </svg>
                                </button>
                                <button type="button" data-modal-hide="deleteModal' . $row[$modalEditID] . '"
                                        class="text-white bg-blue-700 flex items-center gap-3 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                                    <span>Zrušiť</span>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>';
        }
        return $tableModals;
    }
}
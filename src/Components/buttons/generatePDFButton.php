<?php
if ($item["datum_pokynu"] != "") {
    $dealerName = Connection::getDataFromDatabase("SELECT username FROM users WHERE userid = " . $item["assigneduserid"], defaultDB)[1][0]["username"];
    $zadavatel = Connection::getDataFromDatabase("SELECT u.username FROM notifications n INNER JOIN users u ON u.userid = n.userid WHERE objektid = " . $item["dealid"], defaultDB)[1][0]["username"];
    ?>
    <form class="generatePDFForm" hx-post="/api/obchodnny-dennik/generatePDF" hx-target="#toast">
        <input type="hidden" name="datum_prijatia" value="<?php echo strtok($item["datum_pokynu"], " "); ?>" />
        <input type="hidden" name="cas_prijatia"
            value="<?php echo substr($item["datum_pokynu"], strpos($item["datum_pokynu"], " ") + 1); ?>" />
        <input type="hidden" name="datum_realizacie" value="<?php echo strtok($item["datum_cas_obchodu"], " "); ?>" />
        <input type="hidden" name="cas_alokacie"
            value="<?php echo substr($item["datum_cas_obchodu"], strpos($item["datum_cas_obchodu"], " ") + 1); ?>" />
        <?php
        if ($item["druhobchodu"] === "predaj") {
            $datumVysporiadania = $item["FV"];
        } else {
            $datumVysporiadania = $item["MV"];
        } ?>
        <input type="hidden" name="datum_vysporiadania" value="<?php echo $datumVysporiadania; ?>" />
        <input type="hidden" name="druh" value="<?php echo $eqid; ?>" />
        <input type="hidden" name="nazov" value="<?php echo $item["nazov"]; ?>" />
        <input type="hidden" name="kusov" value="<?php echo $item["pocet"]; ?>" />
        <input type="hidden" name="isin" value="<?php echo $item["isin"]; ?>" />
        <input type="hidden" name="urok" value="<?php echo $item["kupon"]; ?>" />
        <input type="hidden" name="mena" value="<?php echo $item["mena"]; ?>" />
        <input type="hidden" name="splatnost" value="<?php echo $item["maturitydate"]; ?>" />
        <input type="hidden" name="trh" value="<?php echo $item["skratka"]; ?>" />
        <input type="hidden" name="klient" value="<?php echo $klientiText; ?>" />
        <input type="hidden" name="dealer" value="<?php echo $dealerName; ?>" />
        <input type="hidden" name="zadany_kurz" value="<?php echo $item["kurz"]; ?>" />
        <input type="hidden" name="dealid" value="<?php echo $item["dealid"]; ?>" />
        <?php if ($item["pokyn_ako"] == 0) {
            $platnyDoDate = strtotime($item["datum_pokynu"]);
            $platnyDoDate = date("d.m.Y", strtotime("+7 days", $platnyDoDate));
        } else {
            $platnyDoDate = "odvolania";
        }
        ?>
        <input type="hidden" name="platny_do" value="<?php echo $platnyDoDate; ?>" />
        <input type="hidden" name="zadany_zadal" value="<?php echo $zadavatel; ?>" />
        <input type="hidden" name="cena_spolu" value="<?php echo $item["sumaspolu"]; ?>" />
        <input type="hidden" name="objem_nominalny"
            value="<?php echo $item["pocet"] * ($item["nominalemisie"] ? $item["nominalemisie"] : 1 * $item["kurz"]); ?>" />
        <input type="hidden" name="auvk_1ks" value="<?php echo $item["auv"] / $item["pocet"]; ?>" />
        <input type="hidden" name="auvk_celkom" value="<?php echo $item["auv"]; ?>" />
        <input type="hidden" name="nh_auvk"
            value="<?php echo $item["pocet"] * $item["nominalemisie"] * $item["kurz"] * $item["faktor"] + $item["auv"]; ?>" />
        <button type="submit"
            class="font-medium p-1 rounded-lg hover:bg-gray-400 hover:text-gray-900 transition-all cursor-pointer">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="lucide lucide-file-text-icon lucide-file-text">
                <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z" />
                <path d="M14 2v4a2 2 0 0 0 2 2h4" />
                <path d="M10 9H8" />
                <path d="M16 13H8" />
                <path d="M16 17H8" />
            </svg>
        </button>
    </form>
<?php } ?>
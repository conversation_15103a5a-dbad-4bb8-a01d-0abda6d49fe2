s<?php
class Button
{
    public string $text;
    public string $name;
    public string $type;
    public string $dataModalTarget;
    public string $link;
    public string $variant;
    public string $icon;
    public string $iconPosition;
    public string $target;
    public string $action;
    public function __construct($text, $name, $type = "button", $dataModalTarget, $variant, $link = "", $icon = "", $iconPosition = "left", $action = "get", $target = "#pageContentMain")
    {
        $this->text = $text;
        $this->name = $name;
        $this->type = $type;
        $this->dataModalTarget = $dataModalTarget;
        $this->variant = $variant;
        $this->link = $link;
        $this->icon = $icon;
        $this->iconPosition = $iconPosition;
    }

    public function buttonStyle(): string
    {
        switch ($this->variant) {
            case "decline":
                $padding =  $this->text === "" ? "px-2 py-1" : "px-5 py-2.5";
                return "ms-3 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 $padding hover:text-blue-700
                     focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-100
                      dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700";
            case "primary":
                $padding =  $this->text === "" ? "px-2 py-1" : "px-5 py-2.5";
                return "text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm $padding text-center 
                    dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800";
            case "delete":
                $padding =  $this->text === "" ? "px-2 py-1" : "px-5 py-2.5";
                return "text-white cursor-pointer bg-red-600 hover:bg-red-800 focus:ring-4 focus:outline-none focus:ring-red-300 dark:focus:ring-red-800 font-medium rounded-lg
                 text-sm inline-flex gap-4 items-center $padding text-center";
            case "alternative":
                $padding =  $this->text === "" ? "px-2 py-1" : "px-5 py-2.5";
                return "me-2 text-sm $padding font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700
                 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700";
            default:
                return "";
        }
    }

    public function modalLogic(): string
    {
        if ($this->dataModalTarget != "" && $this->variant === "decline") {
            return "data-modal-hide='$this->dataModalTarget'";
        } else if ($this->dataModalTarget != "" && $this->variant !== "decline") {
            return "data-modal-target='$this->dataModalTarget' data-modal-show='$this->dataModalTarget'";
        } else {
            return "";
        }
    }

    public function htmx(): string {
        if($this->link != "" && $this->action === "get"){
            return "hx-get='$this->link' hx-target='#pageContentMain' hx-replace-url='true' hx-push-url='true'";
        } else if ($this->link != "" && $this->action === "post") {
            return "hx-post='$this->link' hx-target='$this->target' hx-replace-url='true' hx-push-url='true'";
        } else {
            return "";
        }
    }

    public function render()
    {
        $style = $this->buttonStyle();
        $modalLogic = $this->modalLogic();
        $htmx = $this->htmx();
        ?>
        <button <?php echo $modalLogic; ?> type="<?php echo $this->type; ?>" name="<?php echo $this->name; ?>" <?php echo $htmx; ?>
            class="<?php echo $style; ?>">
            <?php 
                if($this->icon){ ?>
                    <span class="w-4 inline-flex">
                     <?php include "/home/<USER>/www/src/Components/icons/$this->icon.svg"; ?>
                    </span>
               <?php } ?>
           <?php echo $this->text; ?>
        </button>
        <?php
    }
}